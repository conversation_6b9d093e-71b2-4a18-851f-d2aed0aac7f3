package org.addx.iot.domain.video.service.impl;

import org.addx.iot.common.condition.LocalBeanCondition;
import org.addx.iot.domain.common.service.IS3Service;
import org.addx.iot.domain.extension.ai.constant.AiFeatures;
import org.addx.iot.domain.extension.ai.model.SelectInfo;
import org.addx.iot.domain.extension.ai.param.AiVideoQueryByObjectRequest;
import org.addx.iot.domain.extension.ai.service.IAiVideoObjectService;
import org.addx.iot.domain.extension.ai.vo.AiObjectPlayListVideoResult;
import org.addx.iot.domain.extension.ai.vo.AiVideoObjectInfoResult;
import org.addx.iot.domain.extension.video.entity.UserLibraryViewDO;
import org.addx.iot.domain.extension.video.param.LibraryRequest;
import org.addx.iot.domain.video.entity.VideoSliceDO;
import org.addx.iot.domain.video.service.ICrossCameraVideoService;
import org.addx.iot.domain.video.service.ILibraryService;
import org.addx.iot.domain.video.service.IVideoFileService;
import org.addx.iot.domain.video.service.IVideoStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Conditional(LocalBeanCondition.class)
public class CrossCameraVideoService implements ICrossCameraVideoService {

    @Autowired
    private IS3Service s3Service;
    @Autowired
    IVideoStoreService videoStoreService;
    @Autowired
    ILibraryService libraryService;
    @Autowired
    IVideoFileService videoFileService;
    @Autowired
    private IAiVideoObjectService aiVideoObjectService;

    @Override
    public AiObjectPlayListVideoResult queryVideoByObjectId(AiVideoQueryByObjectRequest videoQueryByObjectRequest) {
        AiObjectPlayListVideoResult result = new AiObjectPlayListVideoResult();

        // 1. 通过 objectId 和objectType 获取对应的traceID 和切片信息
        final long currentVideoTime = videoQueryByObjectRequest.getCurrentVideoTimestamp() != null ? videoQueryByObjectRequest.getCurrentVideoTimestamp() : System.currentTimeMillis() / 1000;
        final long startTime = videoQueryByObjectRequest.getStartTimestamp() != null ? videoQueryByObjectRequest.getStartTimestamp() : currentVideoTime - 60 * 60 * 12;  // 默认查询区间  前后 12小时
        final long endTime = videoQueryByObjectRequest.getEndTimestamp() != null ? videoQueryByObjectRequest.getEndTimestamp() : currentVideoTime + 60 * 60 * 12;
        List<AiVideoObjectInfoResult> videoObjectList = aiVideoObjectService.queryObjectDetailByCreateTimeRange(startTime * 1000, endTime * 1000, videoQueryByObjectRequest.getObjectId(), videoQueryByObjectRequest.getObjectType(), AiFeatures.CROSS_CAMERA_EVENT);
        if (videoObjectList.isEmpty()) {
            return result;
        }
        Map<String, List<AiVideoObjectInfoResult>> traceId2VideoObjects = videoObjectList.stream().collect(Collectors.groupingBy(AiVideoObjectInfoResult::getTraceId));
        List<String> traceIds = new ArrayList<>(traceId2VideoObjects.keySet());
        // 2. 获取LibraryVideo 原始信息
        LibraryRequest libraryQuery = new LibraryRequest().setTraceIds(traceIds);
        libraryQuery.setUserId(videoQueryByObjectRequest.getUserId());
        List<UserLibraryViewDO> userLibraryViewDOList = libraryService.selectLibrary(libraryQuery);
        Map<String, String> traceId2DeviceName = new HashMap<>();
        Map<String, String> traceId2SerialNumberMap = new HashMap<>();
        for (UserLibraryViewDO userLibraryViewDO : userLibraryViewDOList) {
            //userLibraryViewDO.setObjects(traceId2VideoObjects.get(userLibraryViewDO.getTraceId()));
            userLibraryViewDO.setImageUrl(s3Service.preSignUrl(userLibraryViewDO.getImageUrl()));
            userLibraryViewDO.setVideoUrl(s3Service.preSignUrl(userLibraryViewDO.getVideoUrl()));
            traceId2DeviceName.put(userLibraryViewDO.getTraceId(), userLibraryViewDO.getDeviceName());
            traceId2SerialNumberMap.put(userLibraryViewDO.getTraceId(), userLibraryViewDO.getSerialNumber());
        }

        List<SelectInfo> selectInfoList = videoObjectList.stream()
                .map(it -> new SelectInfo().setDeviceName(traceId2DeviceName.get(it.getTraceId()))
                        .setStartPts(it.getAppearStartTime())
                        .setSerialNumber(traceId2SerialNumberMap.get(it.getTraceId()))
                        .setEndPts(it.getAppearEndTime())
                        .setTraceId(it.getTraceId())
                        .setImageUrl(s3Service.preSignUrl(it.getKeyFrameImage()))
                ).filter(it -> it.getDeviceName() != null).collect(Collectors.toList());

        result.setCameraVideoList(userLibraryViewDOList);

        // 3. 获取符合切片信息 拼接被选中的部分切片列表
        final List<String> videoUrls = videoStoreService.queryVideoUrlByTraceIds(traceIds);
        List<VideoSliceDO> sliceList = new ArrayList<>();
        Map<String, Long> trace2ValidStartTime = new HashMap<>();
        Map<String, Long> trace2ValidEndTime = new HashMap<>();
        for (String videoUrl : videoUrls) {
            List<VideoSliceDO> slices = videoFileService.querySliceByVideoDirUrl(videoUrl);
            if (slices.isEmpty()) {
                continue;
            }
            slices = slices.stream().sorted(Comparator.comparingInt(VideoSliceDO::getOrder)).collect(Collectors.toList());
            VideoSliceDO lastSlice = slices.stream().max(Comparator.comparingInt(VideoSliceDO::getOrder)).get();
            if (lastSlice.getStartPtsTimestamp() <= 0 && slices.size() > 1) {
                VideoSliceDO beforeLastSlice = slices.get(slices.size() - 2);
                lastSlice.setStartPtsTimestamp(beforeLastSlice.getStartPtsTimestamp() + beforeLastSlice.getPeriod().multiply(new BigDecimal(1000.0)).longValue());
            }
            trace2ValidEndTime.put(lastSlice.getTraceId(), lastSlice.getStartUtcTimestamp() + lastSlice.getPeriod().multiply(new BigDecimal(1000.0)).longValue());
            Long utcTimeGap = lastSlice.getStartPtsTimestamp() - lastSlice.getStartUtcTimestamp();
            // 修正每个切片的utc 时间
            slices = slices.stream().map(it -> it.setStartUtcTimestamp(it.getStartPtsTimestamp() - utcTimeGap)).collect(Collectors.toList());
            VideoSliceDO firstSlice = slices.stream().min(Comparator.comparingInt(VideoSliceDO::getOrder)).get();
            trace2ValidStartTime.put(firstSlice.getTraceId(), firstSlice.getStartUtcTimestamp());
            sliceList.addAll(slices);
        }

        //
        selectInfoList = selectInfoList.stream().map((it) -> {
            it.setOriginalStartTime(trace2ValidStartTime.get(it.getTraceId()));
            it.setOriginalEndTime(trace2ValidEndTime.get(it.getTraceId()));
            return it;
        }).collect(Collectors.toList());

        long totalVideoStartTime = Long.MAX_VALUE;
        long totalVideoEndTime = 0;

        for (VideoSliceDO videoSliceDO : sliceList) {
            if (totalVideoEndTime < videoSliceDO.getStartUtcTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue()) {
                totalVideoEndTime = videoSliceDO.getStartUtcTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue();
            }
            if (totalVideoStartTime > videoSliceDO.getStartUtcTimestamp()) {
                totalVideoStartTime = videoSliceDO.getStartUtcTimestamp();
            }

            // 3.1 修正蓝色部分开始结束时间为完整切片
            for (SelectInfo selectInfo : selectInfoList) {
                if (selectInfo.getTraceId().equals(videoSliceDO.getTraceId())) {
                    if (selectInfo.getStartPts() >= videoSliceDO.getStartPtsTimestamp()
                            && selectInfo.getStartPts() <= videoSliceDO.getStartPtsTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).longValue()) {
                        selectInfo.setStartTime(videoSliceDO.getStartUtcTimestamp());
                        selectInfo.setOffsetStartTime(videoSliceDO.getStartUtcTimestamp() - selectInfo.getStartTime());
                        selectInfo.setStartTime(videoSliceDO.getStartUtcTimestamp());
                    }
                    if (selectInfo.getEndPts() >= videoSliceDO.getStartPtsTimestamp()
                            && selectInfo.getEndPts() < videoSliceDO.getStartPtsTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).longValue()) {
                        selectInfo.setEndTime(videoSliceDO.getStartUtcTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue());
                    }
                }
            }
        }
        for (SelectInfo selectInfo : selectInfoList) {
            if (selectInfo.getStartTime() == null) {
                selectInfo.setStartTime(trace2ValidStartTime.get(selectInfo.getTraceId()));
            }
            if (selectInfo.getEndTime() == null) {
                selectInfo.setEndTime(trace2ValidEndTime.get(selectInfo.getTraceId()));
            }
            for (VideoSliceDO videoSliceDO : sliceList) {
                if (selectInfo.getTraceId().equals(videoSliceDO.getTraceId())) {
                    if (selectInfo.getStartTime() <= videoSliceDO.getStartUtcTimestamp()
                            && selectInfo.getEndTime() >= videoSliceDO.getStartUtcTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue()) {
                        selectInfo.addTsInfo(new SelectInfo.TsInfo().setNum(videoSliceDO.getOrder())
                                .setStartTime(videoSliceDO.getStartUtcTimestamp())
                                .setDuration(videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue())
                                .setEndTime(videoSliceDO.getStartUtcTimestamp() + videoSliceDO.getPeriod().multiply(new BigDecimal(1000.0)).intValue())
                                .setTsUrl(s3Service.preSignUrl(videoSliceDO.getVideoUrl())));
                    }
                }
            }
        }

        result.setEndTime(totalVideoEndTime);
        result.setStartTime(totalVideoStartTime);
        result.setSelectInfoList(selectInfoList.stream().sorted(Comparator.comparingLong(SelectInfo::getStartTime)).collect(Collectors.toList()));
        result.setObjectInfo(videoObjectList.get(0).setKeyFrameImage(null).setTraceId(null).setObjectImage(s3Service.preSignUrl(videoObjectList.get(0).getObjectImage())));
        return result;
    }

}
