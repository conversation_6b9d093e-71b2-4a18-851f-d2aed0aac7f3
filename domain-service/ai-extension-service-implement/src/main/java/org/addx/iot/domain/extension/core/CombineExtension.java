package org.addx.iot.domain.extension.core;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class CombineExtension extends Extension {

    private final List<IExtension> extensions;

    public CombineExtension(ExtensionManager extensionManager, Extension... combineExtensions) {
        super(extensionManager);
        this.extensions = combineExtensions != null ?
                Arrays.asList(combineExtensions) : new ArrayList<>();
    }

    @Override
    public List<IExtension> getExtensions() {
        return extensions;
    }

    @Override
    public boolean isCombine() {
        return true;
    }

}
