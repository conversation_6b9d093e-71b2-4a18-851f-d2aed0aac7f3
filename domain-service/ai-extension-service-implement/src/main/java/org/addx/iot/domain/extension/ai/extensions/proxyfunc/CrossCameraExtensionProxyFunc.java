package org.addx.iot.domain.extension.ai.extensions.proxyfunc;

import org.addx.iot.domain.extension.ai.extensions.CrossCameraExtension;
import org.addx.iot.domain.extension.ai.service.IAiDeviceSettingService;
import org.addx.iot.domain.extension.core.ExtensionManager;

public class CrossCameraExtensionProxyFunc extends CrossCameraExtension {

    public CrossCameraExtensionProxyFunc(ExtensionManager extensionManager, IAiDeviceSettingService aiDeviceSettingService) {
        super(extensionManager, aiDeviceSettingService);
    }

}
