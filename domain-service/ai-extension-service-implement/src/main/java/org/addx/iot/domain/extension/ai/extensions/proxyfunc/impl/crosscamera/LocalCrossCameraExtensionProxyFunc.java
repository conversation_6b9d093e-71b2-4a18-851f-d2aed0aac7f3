package org.addx.iot.domain.extension.ai.extensions.proxyfunc.impl.crosscamera;

import org.addx.iot.domain.common.service.IS3Service;
import org.addx.iot.domain.extension.ai.constant.AiFeatures;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.extensions.proxyfunc.CrossCameraExtensionProxyFunc;
import org.addx.iot.domain.extension.ai.service.IAiDeviceSettingService;
import org.addx.iot.domain.extension.ai.service.IAiVideoObjectService;
import org.addx.iot.domain.extension.ai.vo.AiVideoObjectInfoResult;
import org.addx.iot.domain.extension.core.ExtensionManager;
import org.addx.iot.domain.extension.video.entity.UserLibraryViewDO;
import org.addx.iot.domain.extension.video.param.LibraryRequest;
import org.addx.iot.domain.extension.video.param.UserLibraryViewRequest;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class LocalCrossCameraExtensionProxyFunc extends CrossCameraExtensionProxyFunc {

    private final IS3Service s3Service;
    private final IAiVideoObjectService aiVideoObjectService;

    public LocalCrossCameraExtensionProxyFunc(ExtensionManager extensionManager,
                                              IAiDeviceSettingService aiDeviceSettingService,
                                              IS3Service s3Service,
                                              IAiVideoObjectService aiVideoObjectService) {
        super(extensionManager, aiDeviceSettingService);
        this.s3Service = s3Service;
        this.aiVideoObjectService = aiVideoObjectService;
    }

    @Override
    public List<UserLibraryViewDO> queryVideoList(LibraryRequest request, Function<Object[], Object> primitiveFunc) {
        // 填充videoObject
        List<UserLibraryViewDO> videoList = (List<UserLibraryViewDO>) primitiveFunc.apply(new Object[]{request});
        if (CollectionUtils.isEmpty(videoList)) return videoList;
        videoList.forEach((video) -> {
            List<AiVideoObjectInfoResult> objects = aiVideoObjectService
                    .queryObjectsByTraceIdsAndObjectTypeGroupByObjectIdsAndFeatureNames(new ArrayList<>() {{
                                                                                            add(video.getTraceId());
                                                                                        }},
                            new ArrayList<>() {{
                                add(AiObjectEnum.FACE.getObjectName());
                                add(AiObjectEnum.PERSON.getObjectName());
                            }},
                            new ArrayList<>() {{
                                add(AiFeatures.CROSS_CAMERA_EVENT);
                            }});
            objects = objects.stream().peek((it) -> {
                it.setKeyFrameImage(null);
                it.setObjectName(null); // 跨境事件不能显示对象名称
            }).collect(Collectors.toList());
            objects.forEach((e) -> {
                s3Service.preSignUrl(e.getObjectImage());
                s3Service.preSignUrl(e.getKeyFrameImage());
            });
            video.setObjects(objects);
        });
        return videoList;
    }

    @Override
    public UserLibraryViewDO querySingleLibrary(UserLibraryViewRequest request, Function<Object[], Object> primitiveFunc) {
        UserLibraryViewDO videoDetail = (UserLibraryViewDO) primitiveFunc.apply(new Object[]{request});
        if (videoDetail == null) return null;
        List<AiVideoObjectInfoResult> objects = aiVideoObjectService
                .queryObjectsByTraceIdsAndObjectTypeGroupByObjectIdsAndFeatureNames(new ArrayList<>() {{
                                                                                        add(videoDetail.getTraceId());
                                                                                    }},
                        new ArrayList<>() {{
                            add(AiObjectEnum.FACE.getObjectName());
                            add(AiObjectEnum.PERSON.getObjectName());
                        }},
                        new ArrayList<>() {{
                            add(AiFeatures.CROSS_CAMERA_EVENT);
                        }});
        objects.forEach((e) -> {
            s3Service.preSignUrl(e.getObjectImage());
            s3Service.preSignUrl(e.getKeyFrameImage());
        });
        videoDetail.setObjects(objects);
        return videoDetail;
    }

}
