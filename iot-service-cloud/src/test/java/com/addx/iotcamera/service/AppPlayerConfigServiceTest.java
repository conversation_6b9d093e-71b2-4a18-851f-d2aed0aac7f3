package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.config.AppPlayerConfigModel;
import com.addx.iotcamera.bean.config.AppPlayerConfigRequest;
import com.addx.iotcamera.bean.config.AppPlayerConfigResponse;
import com.addx.iotcamera.config.apollo.AppPlayerConfigApollo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * AppPlayerConfigService 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class AppPlayerConfigServiceTest {

    @InjectMocks
    private AppPlayerConfigService appPlayerConfigService;

    @Mock
    private AppPlayerConfigApollo appPlayerConfigApollo;

    private AppPlayerConfigModel mockConfigModel;

    @Before
    public void setUp() {
        mockConfigModel = createMockConfigModel();
        when(appPlayerConfigApollo.toConfigModel()).thenReturn(mockConfigModel);
    }

    /**
     * 创建测试用的配置模型
     */
    private AppPlayerConfigModel createMockConfigModel() {
        AppPlayerConfigModel configModel = new AppPlayerConfigModel();
        
        // 用户ID白名单
        AppPlayerConfigModel.WhitelistRule userWhitelistRule = new AppPlayerConfigModel.WhitelistRule();
        userWhitelistRule.setUserIds(Arrays.asList("1001", "1002"));
        Map<String, Object> userWhitelistConfig = new HashMap<>();
        userWhitelistConfig.put("feature_enable", true);
        userWhitelistConfig.put("max_concurrent_streams", 10);
        userWhitelistConfig.put("quality_preference", "high");
        userWhitelistRule.setConfig(userWhitelistConfig);
        configModel.setUserWhitelist(Arrays.asList(userWhitelistRule));
        
        // 设备序列号白名单
        AppPlayerConfigModel.WhitelistRule deviceWhitelistRule = new AppPlayerConfigModel.WhitelistRule();
        deviceWhitelistRule.setSerialNumbers(Arrays.asList("device_123", "device_456"));
        Map<String, Object> deviceWhitelistConfig = new HashMap<>();
        deviceWhitelistConfig.put("feature_enable", true);
        deviceWhitelistConfig.put("max_concurrent_streams", 8);
        deviceWhitelistConfig.put("quality_preference", "medium");
        deviceWhitelistRule.setConfig(deviceWhitelistConfig);
        configModel.setDeviceWhitelist(Arrays.asList(deviceWhitelistRule));
        
        // 一般规则
        List<AppPlayerConfigModel.GeneralRule> generalRules = new ArrayList<>();
        
        // iOS高版本规则
        AppPlayerConfigModel.GeneralRule iOSHighVersionRule = new AppPlayerConfigModel.GeneralRule();
        iOSHighVersionRule.setName("iOS高版本");
        iOSHighVersionRule.setAppType("iOS");
        iOSHighVersionRule.setMinVersion("2.0.0");
        Map<String, Object> iOSHighVersionConfig = new HashMap<>();
        iOSHighVersionConfig.put("feature_enable", true);
        iOSHighVersionConfig.put("max_concurrent_streams", 6);
        iOSHighVersionConfig.put("quality_preference", "high");
        iOSHighVersionRule.setConfig(iOSHighVersionConfig);
        generalRules.add(iOSHighVersionRule);
        
        // Android高版本规则
        AppPlayerConfigModel.GeneralRule androidHighVersionRule = new AppPlayerConfigModel.GeneralRule();
        androidHighVersionRule.setName("Android高版本");
        androidHighVersionRule.setAppType("Android");
        androidHighVersionRule.setMinVersion("2.0.0");
        Map<String, Object> androidHighVersionConfig = new HashMap<>();
        androidHighVersionConfig.put("feature_enable", true);
        androidHighVersionConfig.put("max_concurrent_streams", 6);
        androidHighVersionConfig.put("quality_preference", "medium");
        androidHighVersionRule.setConfig(androidHighVersionConfig);
        generalRules.add(androidHighVersionRule);
        
        // iOS低版本规则
        AppPlayerConfigModel.GeneralRule iOSLowVersionRule = new AppPlayerConfigModel.GeneralRule();
        iOSLowVersionRule.setName("iOS低版本");
        iOSLowVersionRule.setAppType("iOS");
        iOSLowVersionRule.setMaxVersion("1.9.9");
        Map<String, Object> iOSLowVersionConfig = new HashMap<>();
        iOSLowVersionConfig.put("feature_enable", false);
        iOSLowVersionConfig.put("max_concurrent_streams", 2);
        iOSLowVersionConfig.put("quality_preference", "low");
        iOSLowVersionRule.setConfig(iOSLowVersionConfig);
        generalRules.add(iOSLowVersionRule);
        
        configModel.setGeneralRules(generalRules);
        
        // 默认配置
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("feature_enable", false);
        defaultConfig.put("max_concurrent_streams", 4);
        defaultConfig.put("quality_preference", "medium");
        configModel.setDefaultConfig(defaultConfig);
        
        return configModel;
    }

    /**
     * 创建测试请求对象
     */
    private AppPlayerConfigRequest createRequest(String appType, String versionName, String serialNumber) {
        AppPlayerConfigRequest request = new AppPlayerConfigRequest();
        AppInfo appInfo = new AppInfo();
        appInfo.setAppType(appType);
        appInfo.setVersionName(versionName);
        request.setApp(appInfo);
        request.setSerialNumber(serialNumber);
        return request;
    }

    @Test
    public void testGetAppPlayerConfig_UserWhitelistMatch() {
        // 测试用户ID白名单匹配
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", null);
        Integer userId = 1001;
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("USER_WHITELIST", response.getMatchType());
        assertEquals("userId:1001", response.getMatchValue());
        assertEquals(true, response.getConfig().get("feature_enable"));
        assertEquals(10, response.getConfig().get("max_concurrent_streams"));
        assertEquals("high", response.getConfig().get("quality_preference"));
        assertNotNull(response.getTimestamp());
    }

    @Test
    public void testGetAppPlayerConfig_DeviceWhitelistMatch() {
        // 测试设备序列号白名单匹配（用户ID不在白名单中）
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", "device_123");
        Integer userId = 9999; // 不在用户白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("DEVICE_WHITELIST", response.getMatchType());
        assertEquals("serialNumber:device_123", response.getMatchValue());
        assertEquals(true, response.getConfig().get("feature_enable"));
        assertEquals(8, response.getConfig().get("max_concurrent_streams"));
        assertEquals("medium", response.getConfig().get("quality_preference"));
    }

    @Test
    public void testGetAppPlayerConfig_GeneralRuleMatch_IOSHighVersion() {
        // 测试iOS高版本一般规则匹配
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", "unknown_device");
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("GENERAL_RULE", response.getMatchType());
        assertEquals("rule:iOS高版本", response.getMatchValue());
        assertEquals(true, response.getConfig().get("feature_enable"));
        assertEquals(6, response.getConfig().get("max_concurrent_streams"));
        assertEquals("high", response.getConfig().get("quality_preference"));
    }

    @Test
    public void testGetAppPlayerConfig_GeneralRuleMatch_AndroidHighVersion() {
        // 测试Android高版本一般规则匹配
        AppPlayerConfigRequest request = createRequest("Android", "2.0.1", "unknown_device");
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("GENERAL_RULE", response.getMatchType());
        assertEquals("rule:Android高版本", response.getMatchValue());
        assertEquals(true, response.getConfig().get("feature_enable"));
        assertEquals(6, response.getConfig().get("max_concurrent_streams"));
        assertEquals("medium", response.getConfig().get("quality_preference"));
    }

    @Test
    public void testGetAppPlayerConfig_GeneralRuleMatch_IOSLowVersion() {
        // 测试iOS低版本一般规则匹配
        AppPlayerConfigRequest request = createRequest("iOS", "1.8.0", "unknown_device");
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("GENERAL_RULE", response.getMatchType());
        assertEquals("rule:iOS低版本", response.getMatchValue());
        assertEquals(false, response.getConfig().get("feature_enable"));
        assertEquals(2, response.getConfig().get("max_concurrent_streams"));
        assertEquals("low", response.getConfig().get("quality_preference"));
    }

    @Test
    public void testGetAppPlayerConfig_DefaultConfig() {
        // 测试默认配置（没有匹配任何规则）
        AppPlayerConfigRequest request = createRequest("UnknownOS", "1.0.0", "unknown_device");
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("DEFAULT", response.getMatchType());
        assertEquals("default", response.getMatchValue());
        assertEquals(false, response.getConfig().get("feature_enable"));
        assertEquals(4, response.getConfig().get("max_concurrent_streams"));
        assertEquals("medium", response.getConfig().get("quality_preference"));
    }

    @Test
    public void testGetAppPlayerConfig_UserIdNull() {
        // 测试用户ID为null的情况
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", "device_123");
        Integer userId = null;
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        // 应该跳过用户白名单，匹配设备白名单
        assertEquals("DEVICE_WHITELIST", response.getMatchType());
        assertEquals("serialNumber:device_123", response.getMatchValue());
    }

    @Test
    public void testGetAppPlayerConfig_SerialNumberNull() {
        // 测试设备序列号为null的情况
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", null);
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        // 应该跳过设备白名单，匹配一般规则
        assertEquals("GENERAL_RULE", response.getMatchType());
        assertEquals("rule:iOS高版本", response.getMatchValue());
    }

    @Test
    public void testGetAppPlayerConfig_VersionComparison() {
        // 测试版本比较逻辑
        Integer userId = 9999; // 不在白名单中
        
        // 测试边界版本 2.0.0（刚好满足最小版本要求）
        AppPlayerConfigRequest request1 = createRequest("iOS", "2.0.0", "unknown_device");
        AppPlayerConfigResponse response1 = appPlayerConfigService.getAppPlayerConfig(request1, userId);
        assertEquals("GENERAL_RULE", response1.getMatchType());
        assertEquals("rule:iOS高版本", response1.getMatchValue());
        
        // 测试边界版本 1.9.9（刚好满足最大版本要求）
        AppPlayerConfigRequest request2 = createRequest("iOS", "1.9.9", "unknown_device");
        AppPlayerConfigResponse response2 = appPlayerConfigService.getAppPlayerConfig(request2, userId);
        assertEquals("GENERAL_RULE", response2.getMatchType());
        assertEquals("rule:iOS低版本", response2.getMatchValue());
        
        // 测试中间版本 1.9.0（应该匹配低版本规则）
        AppPlayerConfigRequest request3 = createRequest("iOS", "1.9.0", "unknown_device");
        AppPlayerConfigResponse response3 = appPlayerConfigService.getAppPlayerConfig(request3, userId);
        assertEquals("GENERAL_RULE", response3.getMatchType());
        assertEquals("rule:iOS低版本", response3.getMatchValue());
    }

    @Test
    public void testGetAppPlayerConfig_CaseInsensitiveAppType() {
        // 测试App类型大小写不敏感
        AppPlayerConfigRequest request = createRequest("ios", "2.1.0", "unknown_device");
        Integer userId = 9999; // 不在白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertNotNull(response);
        assertEquals("GENERAL_RULE", response.getMatchType());
        assertEquals("rule:iOS高版本", response.getMatchValue());
    }

    @Test
    public void testGetAppPlayerConfig_ComplexVersionNumber() {
        // 测试复杂版本号
        Integer userId = 9999; // 不在白名单中
        
        // 测试四位版本号
        AppPlayerConfigRequest request1 = createRequest("Android", "2.0.1.5", "unknown_device");
        AppPlayerConfigResponse response1 = appPlayerConfigService.getAppPlayerConfig(request1, userId);
        assertEquals("GENERAL_RULE", response1.getMatchType());
        assertEquals("rule:Android高版本", response1.getMatchValue());
        
        // 测试两位版本号
        AppPlayerConfigRequest request2 = createRequest("Android", "2.1", "unknown_device");
        AppPlayerConfigResponse response2 = appPlayerConfigService.getAppPlayerConfig(request2, userId);
        assertEquals("GENERAL_RULE", response2.getMatchType());
        assertEquals("rule:Android高版本", response2.getMatchValue());
    }

    @Test
    public void testGetAppPlayerConfig_PriorityOrder() {
        // 测试优先级顺序：用户白名单 > 设备白名单 > 一般规则
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", "device_123");
        Integer userId = 1001; // 在用户白名单中
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        // 即使设备也在白名单中，也应该优先使用用户白名单
        assertEquals("USER_WHITELIST", response.getMatchType());
        assertEquals("userId:1001", response.getMatchValue());
        assertEquals(10, response.getConfig().get("max_concurrent_streams")); // 用户白名单的配置
    }

    @Test
    public void testGetAppPlayerConfig_EmptyConfig() {
        // 测试空配置的情况
        AppPlayerConfigModel emptyConfigModel = new AppPlayerConfigModel();
        emptyConfigModel.setUserWhitelist(null);
        emptyConfigModel.setDeviceWhitelist(null);
        emptyConfigModel.setGeneralRules(null);
        
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("feature_enable", false);
        defaultConfig.put("max_concurrent_streams", 1);
        emptyConfigModel.setDefaultConfig(defaultConfig);
        
        when(appPlayerConfigApollo.toConfigModel()).thenReturn(emptyConfigModel);
        
        AppPlayerConfigRequest request = createRequest("iOS", "2.1.0", "device_123");
        Integer userId = 1001;
        
        AppPlayerConfigResponse response = appPlayerConfigService.getAppPlayerConfig(request, userId);
        
        assertEquals("DEFAULT", response.getMatchType());
        assertEquals("default", response.getMatchValue());
        assertEquals(false, response.getConfig().get("feature_enable"));
        assertEquals(1, response.getConfig().get("max_concurrent_streams"));
    }
} 