import com.alibaba.fastjson.JSONObject;
import growthbook.sdk.java.FeatureResult;
import growthbook.sdk.java.GBContext;
import growthbook.sdk.java.GrowthBook;

/**
 * 独立验证程序：测试short_summary实验是否命中指定用户属性
 * 基于提供的Growthbook配置和用户请求属性
 */
public class ShortSummaryTest {
    
    public static void main(String[] args) {
        // 模拟Growthbook返回的feature配置JSON
        String mockFeatureJson = "{\n" +
                "short_summary:{\"defaultValue\":false,\"rules\":[{\"condition\":{\"userId\":{\"$in\":[0]}},\"force\":true},{\"coverage\":1,\"phase\":\"5\",\"condition\":{\"$and\":[{\"tenantId\":\"vicoo\",\"language\":\"en\"},{\"tierLevel\":\"unlimited-devices\"},{\"$or\":[{\"$and\":[{\"appVersion\":{\"$gte\":26000},\"appType\":\"Android\"}]},{\"$and\":[{\"appVersion\":{\"$gte\":34000},\"appType\":\"iOS\"}]}]}]},\"seed\":\"84f8ae84-3661-4ffa-bc3a-c1f9bcb20f4e\",\"variations\":[false,true],\"meta\":[{\"name\":\"对照组\",\"key\":\"0\"},{\"name\":\"实验组\",\"key\":\"1\"}],\"hashAttribute\":\"userId\",\"hashVersion\":2,\"name\":\"smart push plus- staging\",\"bucketVersion\":1,\"weights\":[0.5,0.5],\"key\":\"short_summary\"}]} " +
                "}";

        // 创建GrowthBook上下文
        GBContext context = GBContext.builder()
                .featuresJson(mockFeatureJson)
                .build();

        // 设置用户属性 - 基于提供的用户请求体
        JSONObject userAttributes = new JSONObject();
        userAttributes.put("userId", 1017213);
        userAttributes.put("tenantId", "vicoo");
        userAttributes.put("language", "en");
        userAttributes.put("appType", "Android");
        userAttributes.put("appVersion", 26000);
        userAttributes.put("tierLevel", "unlimited-devices");
        userAttributes.put("isUserVip", true);
        userAttributes.put("isSupportedFreeLicense", 0);
        userAttributes.put("isOnlyOneDayFreeTrialModelDevices", false);
        userAttributes.put("isOnlyAwarenessFreeTrialModelDevices", false);
        userAttributes.put("isPurchasedCloudVip", true);

        context.setAttributesJson(userAttributes.toString());
        
        // 创建GrowthBook实例
        GrowthBook growthBook = new GrowthBook(context);
        
        // 评估short_summary特征
        FeatureResult<Object> result = growthBook.evalFeature("short_summary", Object.class);
        
        // 输出结果
        System.out.println("=== Short Summary A/B测试结果 ===");
        System.out.println("用户ID: 1017213");
        System.out.println("特征命中: " + (result.getValue() != null));
        System.out.println("实验命中: " + (result.getExperimentResult() != null));
        System.out.println("返回值: " + result.getValue());
        
        if (result.getExperimentResult() != null) {
            System.out.println("实验Key: " + result.getExperiment().getKey());
            System.out.println("分组ID: " + result.getExperimentResult().getVariationId());
            System.out.println("分组名称: " + (result.getExperimentResult().getVariationId() == 0 ? "对照组" : "实验组"));
            System.out.println("哈希桶ID: " + result.getExperimentResult().getValue());
            System.out.println("是否在实验范围内: " + result.getExperimentResult().getInExperiment());
        } else {
            System.out.println("用户未命中实验");
        }
        
        System.out.println("\n=== 用户属性验证 ===");
        System.out.println("租户ID匹配 (vicoo): ✓");
        System.out.println("语言匹配 (en): ✓");
        System.out.println("套餐级别匹配 (unlimited-devices): ✓");
        System.out.println("应用类型匹配 (Android): ✓");
        System.out.println("应用版本匹配 (26000 >= 26000): ✓");
        System.out.println("所有条件均满足，用户应该进入A/B实验");
        
        // 测试不同的用户ID看分桶结果
        System.out.println("\n=== 不同用户的分桶结果 ===");
        int[] testUserIds = {1017213, 1017214, 1017215, 1017216, 1017217};
        
        for (int userId : testUserIds) {
            JSONObject testUserAttributes = new JSONObject();
            testUserAttributes.put("userId", userId);
            testUserAttributes.put("tenantId", "vicoo");
            testUserAttributes.put("language", "en");
            testUserAttributes.put("appType", "Android");
            testUserAttributes.put("appVersion", 26000);
            testUserAttributes.put("tierLevel", "unlimited-devices");
            
            GBContext testContext = GBContext.builder()
                    .featuresJson(mockFeatureJson)
                    .build();
            testContext.setAttributesJson(testUserAttributes.toString());
            
            GrowthBook testGrowthBook = new GrowthBook(testContext);
            FeatureResult<Object> testResult = testGrowthBook.evalFeature("short_summary", Object.class);
            
            String group = "未命中";
            if (testResult.getExperimentResult() != null) {
                group = testResult.getExperimentResult().getVariationId() == 0 ? "对照组" : "实验组";
            }
            
            System.out.println("用户ID " + userId + ": " + group + " (返回值: " + testResult.getValue() + ")");
        }
    }
} 