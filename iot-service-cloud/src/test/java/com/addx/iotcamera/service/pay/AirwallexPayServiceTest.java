package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.db.SubscriptionPaymentTask;
import com.addx.iotcamera.bean.response.AirwallexPaymentResponse;
import com.addx.iotcamera.dao.SubscriptionPaymentTaskDAO;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.Call;
import org.addx.iot.common.vo.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.lang.reflect.Field;
import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Airwallex支付服务单元测试
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class AirwallexPayServiceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(AirwallexPayServiceTest.class);
    
    private static final String API_KEY = "YOUR_API_KEY"; // Replace with actual API key
    private static final String WEBHOOK_URL = "https://api.airwallex.com/api/v1/webhooks/create";
    private static final String NOTIFY_URL = "https://api.addx.live/pay/airwallex/notify";
    
    // 使用默认值，如果反射获取失败则使用该值
    private static int MAX_RETRY_COUNT = 5;
    
    // 静态块中尝试通过反射获取实际值
    static {
        try {
            Field field = AirwallexPayService.class.getDeclaredField("MAX_RETRY_COUNT");
            field.setAccessible(true);
            MAX_RETRY_COUNT = (int) field.get(null);
        } catch (Exception e) {
            // 获取失败时继续使用默认值，不需要处理
            System.out.println("无法通过反射获取MAX_RETRY_COUNT，使用默认值: " + MAX_RETRY_COUNT);
        }
    }
    
    @Mock
    private OkHttpClient okHttpClient;
    
    @Mock
    private Call call;
    
    @Mock
    private Response response;
    
    @Mock
    private ResponseBody responseBody;
    
    @InjectMocks
    private AirwallexPayService airwallexPayService;
    
    @Mock
    private SubscriptionPaymentTaskDAO subscriptionPaymentTaskDAO;
    
    // 测试数据
    private SubscriptionPaymentTask task;
    private SubscriptionPaymentTask fullTask;
    private AirwallexPaymentResponse paymentResponse;
    
    @Before
    public void setUp() throws Exception {
        when(okHttpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{\"success\":true}");
        
        // 初始化测试数据
        task = SubscriptionPaymentTask.builder()
                .id(1L)
                .orderId(1001L)
                .userId(2001)
                .productId(3001)
                .currency("USD")
                .paymentConsentId("pc_123456789")
                .amount(new BigDecimal("99.99"))
                .executionTime((int)(System.currentTimeMillis() / 1000))
                .taskType("AUTO_RENEWAL")
                .retryCount(0)
                .status("PENDING")
                .build();
        
        fullTask = SubscriptionPaymentTask.builder()
                .id(1L)
                .orderId(1001L)
                .userId(2001)
                .productId(3001)
                .currency("USD")
                .paymentConsentId("pc_123456789")
                .amount(new BigDecimal("99.99"))
                .executionTime((int)(System.currentTimeMillis() / 1000))
                .taskType("AUTO_RENEWAL")
                .retryCount(0)
                .status("PENDING")
                .firstFailureTime(null)
                .gracePeriodEnd(null)
                .firstErrorMessage(null)
                .build();
        
        paymentResponse = AirwallexPaymentResponse.builder()
                .paymentIntentId("pi_123456789")
                .status("FAILURE")
                .amount(99.99)
                .currency("USD")
                .merchantOrderId("1001")
                .paymentConsentId("pc_123456789")
                .build();
        
        // 设置mock行为
        when(subscriptionPaymentTaskDAO.getById(anyLong())).thenReturn(fullTask);
    }
    
    /**
     * 测试首次失败时的处理
     * - 应该设置宽限期
     * - 应该记录首次失败时间和错误信息
     * - 应该安排下次重试
     */
    @Test
    public void testHandlePaymentFailure_FirstFailure() {
        // Arrange
        task.setRetryCount(0);
        
        // 捕获方法调用参数
        ArgumentCaptor<Long> idCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<Integer> firstFailureTimeCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> gracePeriodEndCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<String> errorMessageCaptor = ArgumentCaptor.forClass(String.class);
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证调用了updateFirstFailureInfo方法
        verify(subscriptionPaymentTaskDAO).updateFirstFailureInfo(
                idCaptor.capture(),
                firstFailureTimeCaptor.capture(),
                gracePeriodEndCaptor.capture(),
                errorMessageCaptor.capture()
        );
        
        // 验证任务ID
        assertEquals(task.getId(), idCaptor.getValue());
        
        // 验证首次失败时间设置为当前时间
        assertNotNull(firstFailureTimeCaptor.getValue());
        
        // 验证宽限期设置为7天后
        assertNotNull(gracePeriodEndCaptor.getValue());
        int expectedGracePeriod = 7 * 24 * 60 * 60; // 7天的秒数
        // 允许1秒误差
        long diff = gracePeriodEndCaptor.getValue() - firstFailureTimeCaptor.getValue();
        assertEquals(expectedGracePeriod, diff, 1);
        
        // 验证错误信息
        assertEquals(paymentResponse.getStatus(), errorMessageCaptor.getValue());
        
        // 验证更新了任务状态和重试信息
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("PENDING"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(1), // 重试次数+1
                eq(paymentResponse.getStatus())
        );
        
        // 验证更新了下次重试时间
        verify(subscriptionPaymentTaskDAO).updateTaskExecutionTime(eq(task.getId()), anyInt());
    }
    
    /**
     * 测试非首次失败时的处理
     * - 应该使用已有的宽限期
     * - 不应该更新首次失败时间
     * - 应该安排下次重试
     */
    @Test
    public void testHandlePaymentFailure_RetryFailure() {
        // Arrange
        task.setRetryCount(1); // 已经失败过一次
        
        // 设置已有的首次失败时间和宽限期
        int currentTime = (int)(System.currentTimeMillis() / 1000);
        int gracePeriodEnd = currentTime + 5 * 24 * 60 * 60; // 还有5天宽限期
        
        fullTask.setFirstFailureTime(currentTime - 2 * 24 * 60 * 60); // 2天前首次失败
        fullTask.setGracePeriodEnd(gracePeriodEnd);
        fullTask.setFirstErrorMessage("Initial payment failed");
        fullTask.setRetryCount(1);
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证没有再次调用updateFirstFailureInfo方法
        verify(subscriptionPaymentTaskDAO, never()).updateFirstFailureInfo(
                anyLong(), anyInt(), anyInt(), anyString()
        );
        
        // 验证更新了任务状态和重试信息
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("PENDING"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(2), // 重试次数+1
                eq(paymentResponse.getStatus())
        );
        
        // 验证更新了下次重试时间
        verify(subscriptionPaymentTaskDAO).updateTaskExecutionTime(eq(task.getId()), anyInt());
    }
    
    /**
     * 测试宽限期缺失的异常情况
     * - 应该将任务标记为失败
     * - 不应该安排下次重试
     */
    @Test
    public void testHandlePaymentFailure_MissingGracePeriod() {
        // Arrange
        task.setRetryCount(1); // 已经失败过一次
        
        // 设置首次失败时间但缺少宽限期
        fullTask.setFirstFailureTime((int)(System.currentTimeMillis() / 1000) - 2 * 24 * 60 * 60); // 2天前首次失败
        fullTask.setGracePeriodEnd(null); // 宽限期缺失
        fullTask.setRetryCount(1);
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证将任务标记为失败
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("FAILED"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(2), // 重试次数+1
                anyString() // 使用anyString因为这个测试是错误信息不是来自paymentResponse
        );
        
        // 验证没有安排下次重试
        verify(subscriptionPaymentTaskDAO, never()).updateTaskExecutionTime(anyLong(), anyInt());
    }
    
    /**
     * 测试超过最大重试次数
     * - 应该将任务标记为失败
     * - 不应该安排下次重试
     */
    @Test
    public void testHandlePaymentFailure_ExceedMaxRetries() {
        // Arrange
        task.setRetryCount(MAX_RETRY_COUNT); // 已达到最大重试次数
        
        // 设置首次失败时间和宽限期
        int currentTime = (int)(System.currentTimeMillis() / 1000);
        fullTask.setFirstFailureTime(currentTime - 2 * 24 * 60 * 60); // 2天前首次失败
        fullTask.setGracePeriodEnd(currentTime + 5 * 24 * 60 * 60); // 还有5天宽限期
        fullTask.setRetryCount(MAX_RETRY_COUNT);
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证将任务标记为失败
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("FAILED"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(MAX_RETRY_COUNT + 1), // 重试次数+1
                eq(paymentResponse.getStatus())
        );
        
        // 验证没有安排下次重试
        verify(subscriptionPaymentTaskDAO, never()).updateTaskExecutionTime(anyLong(), anyInt());
    }
    
    /**
     * 测试超过宽限期
     * - 应该将任务标记为失败
     * - 不应该安排下次重试
     */
    @Test
    public void testHandlePaymentFailure_ExceedGracePeriod() {
        // Arrange
        task.setRetryCount(2); // 已经重试2次，未达到最大重试次数
        
        // 设置首次失败时间和已过期的宽限期
        int currentTime = (int)(System.currentTimeMillis() / 1000);
        fullTask.setFirstFailureTime(currentTime - 8 * 24 * 60 * 60); // 8天前首次失败
        fullTask.setGracePeriodEnd(currentTime - 1 * 24 * 60 * 60); // 宽限期已过期1天
        fullTask.setRetryCount(2);
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证将任务标记为失败
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("FAILED"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(3), // 重试次数+1
                eq(paymentResponse.getStatus())
        );
        
        // 验证没有安排下次重试
        verify(subscriptionPaymentTaskDAO, never()).updateTaskExecutionTime(anyLong(), anyInt());
    }
    
    /**
     * 测试数据库异常情况
     * - 应该优雅处理异常
     * - 应该将任务标记为失败
     */
    @Test
    public void testHandlePaymentFailure_DatabaseException() {
        // Arrange
        task.setRetryCount(1);
        
        // 模拟数据库查询异常
        when(subscriptionPaymentTaskDAO.getById(anyLong())).thenThrow(new RuntimeException("Database error"));
        
        // Act
        airwallexPayService.handlePaymentFailure(task, Result.ListResult(paymentResponse));
        
        // Assert
        // 验证将任务标记为失败
        verify(subscriptionPaymentTaskDAO).updateStatus(eq(task.getId()), eq("FAILED"));
        verify(subscriptionPaymentTaskDAO).updateRetryInfo(
                eq(task.getId()), 
                eq(2), // 重试次数+1
                contains("Database error")
        );
        
        // 验证没有安排下次重试
        verify(subscriptionPaymentTaskDAO, never()).updateTaskExecutionTime(anyLong(), anyInt());
    }
} 