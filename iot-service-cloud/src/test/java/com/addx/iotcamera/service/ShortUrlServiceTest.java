package com.addx.iotcamera.service;

import org.addx.iot.domain.common.service.ShortUrlService;
import org.addx.iot.common.vo.Result;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 短链接服务测试 - 简化版本
 */
@SpringBootTest
@ActiveProfiles("test")
public class ShortUrlServiceTest {

    @Autowired
    private ShortUrlService shortUrlService;

    @Test
    public void testCreateShortUrl() {
        String originalUrl = "https://www.example.com/test";
        
        Result<String> result = shortUrlService.createShortUrl(originalUrl);
        
        assertTrue(Result.isSuccess(result));
        assertNotNull(result.getData());
        assertFalse(result.getData().isEmpty());
        assertTrue(result.getData().length() >= 6, "短码长度应该至少6位（1亿的Base62编码）");
        
        System.out.println("生成的短链接码: " + result.getData());
    }

    @Test
    public void testGetOriginalUrl() {
        String originalUrl = "https://www.example.com/test2";
        
        // 先创建短链接
        Result<String> createResult = shortUrlService.createShortUrl(originalUrl);
        assertTrue(Result.isSuccess(createResult));
        String shortCode = createResult.getData();
        
        // 再获取原始URL
        Result<String> getResult = shortUrlService.getOriginalUrl(shortCode);
        assertTrue(Result.isSuccess(getResult));
        assertEquals(originalUrl, getResult.getData());
    }

    @Test
    public void testDuplicateUrl() {
        String originalUrl = "https://www.example.com/duplicate";
        
        // 创建第一次
        Result<String> result1 = shortUrlService.createShortUrl(originalUrl);
        assertTrue(Result.isSuccess(result1));
        
        // 创建第二次，应该返回相同的短码
        Result<String> result2 = shortUrlService.createShortUrl(originalUrl);
        assertTrue(Result.isSuccess(result2));
        
        assertEquals(result1.getData(), result2.getData(), "相同URL应该返回相同的短码");
    }

    @Test
    public void testInvalidShortCode() {
        Result<String> result = shortUrlService.getOriginalUrl("invalid@#$");
        assertFalse(Result.isSuccess(result));
        assertTrue(result.getMsg().contains("无效") || result.getMsg().contains("不存在"));
    }

    @Test
    public void testEmptyUrl() {
        Result<String> result = shortUrlService.createShortUrl("");
        assertFalse(Result.isSuccess(result));
        assertEquals("原始URL不能为空", result.getMsg());
    }

    @Test
    public void testShortCodeFormat() {
        String originalUrl = "https://www.example.com/format-test";
        
        Result<String> result = shortUrlService.createShortUrl(originalUrl);
        assertTrue(Result.isSuccess(result));
        
        String shortCode = result.getData();
        assertTrue(shortCode.length() >= 6, "短码长度应该至少6位");
        
        // 验证字符集：只包含Base62字符
        assertTrue(shortCode.matches("[0-9a-zA-Z]+"), 
                   "短码应该只包含Base62字符集");
        
        System.out.println("格式测试生成的短码: " + shortCode);
    }

    @Test
    public void testMultipleUrlsUniqueness() {
        Set<String> shortCodes = new HashSet<>();
        int testCount = 50; // 减少测试数量，因为现在每个都要插入数据库
        
        for (int i = 0; i < testCount; i++) {
            String url = "https://www.example.com/test-" + i;
            Result<String> result = shortUrlService.createShortUrl(url);
            
            assertTrue(Result.isSuccess(result), "URL: " + url + " 创建失败");
            String shortCode = result.getData();
            
            assertFalse(shortCodes.contains(shortCode), 
                       "发现重复的短码: " + shortCode + " for URL: " + url);
            shortCodes.add(shortCode);
        }
        
        assertEquals(testCount, shortCodes.size(), "所有短码应该是唯一的");
        System.out.println("成功创建 " + testCount + " 个唯一的短码");
    }

    @Test
    public void testBase62Encoding() {
        String originalUrl = "https://www.example.com/base62-test";
        
        Result<String> result = shortUrlService.createShortUrl(originalUrl);
        assertTrue(Result.isSuccess(result));
        
        String shortCode = result.getData();
        
        // Base62编码应该只包含0-9, a-z, A-Z
        for (char c : shortCode.toCharArray()) {
            assertTrue((c >= '0' && c <= '9') || 
                      (c >= 'a' && c <= 'z') || 
                      (c >= 'A' && c <= 'Z'), 
                      "发现非Base62字符: " + c);
        }
        
        System.out.println("Base62测试生成的短码: " + shortCode);
    }
} 