package com.addx.iotcamera.testutil;

import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.junit.Test;
import org.junit.jupiter.api.TestTemplate;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.StringUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.URI;
import java.net.URL;
import java.util.Base64;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NoonLightServiceTest {
    private String token = "Or8Pk8FEMBhcG6vlsWqgN8bS3jG2rwks";
    private String urlAlarmCreate = "https://api-sandbox.noonlight.com/dispatch/v1/alarms";
    private String urlAlarmStatus = "https://api-sandbox.noonlight.com/dispatch/v1/alarms/{alarm_id}/status";

//    @Test
//    public void test_alarm_create() throws IOException {
//        JSONObject userAlarmMessage = new JSONObject();
//        userAlarmMessage.put("name","xzhang");
//        userAlarmMessage.put("phone","16692238140");
//        userAlarmMessage.put("pin","1234");
//        JSONObject location = new JSONObject();
//        JSONObject address = new JSONObject();
//        address.put("line1","1111 S Figueroa St");
//        address.put("city","Los Angeles");
//        address.put("state","CA");
//        address.put("zip","90015");
//        location.put("address",address);
//        userAlarmMessage.put("location",location);
//        log.info("body param {}",userAlarmMessage.toJSONString());
//
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("accept", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("content-type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//        String response = HttpUtils.httpsPostRequest(heads,userAlarmMessage.toJSONString(),urlAlarmCreate);
//        log.info("response {}",response);
//    }
//
//
//    @Test
//    public void test_alarmStatus_get() throws IOException {
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("accept", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//        urlAlarmStatus = urlAlarmStatus.replace("{alarm_id}","67bc154192a185e2c4b9b225");
//        String response = HttpUtils.httpGet(urlAlarmStatus,Maps.newHashMap(),heads);
//        log.info("response {}",response);
//    }
//
//    @Test
//    public void test_alarm_updatePerson() throws IOException {
//        String urlCreatePerson = "https://api-sandbox.noonlight.com/dispatch/v1/alarms/{alarm_id}/people/{person_id}";
//
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("content-type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//
//        JSONArray userAlarmMessage = new JSONArray();
//        JSONObject person1 = new JSONObject();
//        person1.put("name","xzhang1");
//        person1.put("phone","16692238140");
//        person1.put("pin","1234");
//        userAlarmMessage.add(person1);
//
//        JSONObject person2 = new JSONObject();
//        person2.put("name","xzhang2");
//        person2.put("phone","16692238140");
//        person2.put("pin","1234");
//
//        userAlarmMessage.add(person2);
//
//        urlCreatePerson = urlCreatePerson.replace("{alarm_id}","67bc154192a185e2c4b9b225");
//        String response = HttpUtils.httpsPostRequest(heads,userAlarmMessage.toJSONString(),urlCreatePerson);
//        log.info("response {}",response);
//    }
//
//    @Test
//    public void test_alarm_createPerson() throws IOException {
//        String urlCreatePerson = "https://api-sandbox.noonlight.com/dispatch/v1/alarms/{alarm_id}/people";
//
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("content-type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//
//        JSONArray userAlarmMessage = new JSONArray();
//        JSONObject person1 = new JSONObject();
//        person1.put("name","xzhang1");
//        person1.put("phone","16692238140");
//        person1.put("pin","1234");
//        userAlarmMessage.add(person1);
//
//        JSONObject person2 = new JSONObject();
//        person2.put("name","xzhang2");
//        person2.put("phone","16692238140");
//        person2.put("pin","1234");
//
//        userAlarmMessage.add(person2);
//
//        urlCreatePerson = urlCreatePerson.replace("{alarm_id}","67bc154192a185e2c4b9b225");
//        String response = HttpUtils.httpsPostRequest(heads,userAlarmMessage.toJSONString(),urlCreatePerson);
//        log.info("response {}",response);
//    }
//
//    @Test
//    public void test_export_file(){
//        String urlCertificates = "https://api-sandbox.noonlight.com/dispatch/v1/certificates";
//
//        JSONObject userAlarmMessage = new JSONObject();
//
//        JSONObject address = new JSONObject();
//        address.put("line1","1111 S Figueroa St");
//        address.put("city","Los Angeles");
//        address.put("state","CA");
//        address.put("zip","90015");
//        userAlarmMessage.put("address",address);
//
//        log.info("body param {}",userAlarmMessage.toJSONString());
//
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("content-type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//        String response = HttpUtils.httpsPostRequest(heads,userAlarmMessage.toJSONString(),urlCertificates);
//        log.info("response {}",response);
//        if(!StringUtils.hasLength(response)){
//            log.error("response null");
//            return;
//        }
//        JSONObject object = JSONObject.parseObject(response);
//        // 将 Base64 字符串解码为字节数组
//        byte[] decodedBytes = Base64.getDecoder().decode(object.getString("payload"));
//
//        // 创建 PDF 文件并将解码的字节数据写入文件
//        try (FileOutputStream fos = new FileOutputStream("/Users/<USER>/Desktop/output.pdf")) {
//            fos.write(decodedBytes);
//            log.info("PDF 文件已成功保存为 output.pdf");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void test_alarm_cancelAlarm() throws IOException {
//        String urlCancelAlarm = "https://api-sandbox.noonlight.com/dispatch/v1/alarms/{alarm_id}/status";
//
//        Map<String,String> heads = Maps.newHashMap();
//        heads.put("content-type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
//        heads.put("authorization", "Bearer " + token);
//
//
//        JSONObject body = new JSONObject();
//        body.put("status","CANCELED");
//
//        urlCancelAlarm = urlCancelAlarm.replace("{alarm_id}","67bc154192a185e2c4b9b225");
//        String response = HttpUtils.httpsPostRequest(heads,body.toJSONString(),urlCancelAlarm);
//        log.info("response {}",response);
//    }
}
