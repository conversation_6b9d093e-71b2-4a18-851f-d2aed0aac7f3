package com.addx.iotcamera.config.app;

import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;


@Data
@Component
@PropertySource(value = {"classpath:/app/email_48h_guide.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "email")
public class EmailGuideConfig {
    private Map<String, Map<String, String>> guide;

    /**
     * 根据 tenantId 获取对应的 Map
     */
    public Map<String, String> getGuideByTenantId(String tenantId) {
        if (guide == null) {
            return new HashMap<>();
        }
        return guide.containsKey(tenantId)? guide.get(tenantId) : guide.get(TENANTID_VICOO);
    }
}