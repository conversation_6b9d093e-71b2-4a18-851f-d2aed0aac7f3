package com.addx.iotcamera.config.device;

import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.thingmodel.ThingModel;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;

@Data
@Component
@PropertySource(value = {"classpath:/models/device_setting.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "device.setting")
@Slf4j
public class DeviceSettingConfig {
    /**
     * 运动检测灵敏度
     */
    private Map<String,Integer> motionSensitivity;
    @Schema(title = "抗频闪开关")
    private Set<String> antiflickerSwitch;

    private Set<String> chargeAutoPowerOnSwitch;


    @Schema(title = "指示灯开关")
    private Map<String,Integer> recLamp;

    @Schema(title = "其他运动检测开关")
    private Map<String,Integer> enableOtherMotionAi;

    // 物模型里的属性配置，可以按型号覆盖。modelNo -> identifier -> 属性配置
    @Schema(title = "按型号配置的物模型里的属性配置")
    private Map<String, Map<String, ThingModel.ThingEntity>> thingModelProperty;

    /**
     * 查询型号对应运动检测灵敏度 （thing_model）
     * @param modelNo
     * @return
     */
    public Integer queryMotionSensitivity(String modelNo, DeviceModel deviceModel){
        if(deviceModel != null && !deviceModel.isCanStandby()){
            return MOTION_SENSITIVITY_LOW_VALUE;
        }
        return motionSensitivity.containsKey(modelNo) ? motionSensitivity.get(modelNo) : MOTION_SENSITIVITY_VALUE;
    }

    /**
     * 配置开关关闭的型号
     * @param modelNo
     * @return
     */
    public Integer queryModelAntiflickerSwitch(String modelNo){
        return antiflickerSwitch.contains(modelNo) ? 0 : 1;
    }

    public Integer queryModelChargeAutoPowerOnSwitch(String modelNo){
        return chargeAutoPowerOnSwitch.contains(modelNo) ? 1 :   DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_SWITCH;
    }

    public Integer queryMotionTrackSwitch(String modelNo) {
        return 0;
    }
    /**
     * 查询型号对应指示灯开关配置
     * @param modelNo 设备型号
     * @return 指示灯开关值
     */
    public Integer queryRecLamp(String modelNo){
        return recLamp != null ? recLamp.getOrDefault(modelNo, REC_LAMP_VALUE): REC_LAMP_VALUE;
    }

    /**
     * 查询型号对应其他运动检测开关配置
     * @param modelNo 设备型号
     * @return 是否启用其他运动检测
     */
    public Boolean queryEnableOtherMotionAi(String modelNo){
        return enableOtherMotionAi != null && enableOtherMotionAi.getOrDefault(modelNo, 0).equals(1);
    }

}
