package com.addx.iotcamera.config.pay;

import com.addx.iotcamera.bean.domain.pay.TierCopywriteDiff;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.api.client.util.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;

@Component
@Data
@ConfigurationProperties(prefix = "tier.copywrite.diff")
@PropertySource(value = {"classpath:/app/tier_copywrite_diff.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
public class TierCopywriteDiffConfig {
    // tenant -> vipType -> 权益List
    private Map<String,Map<String, List<TierCopywriteDiff>>> config;

    /**
     * 根据vip类型返回对应的对比
     * @param vipType
     * @return
     */
    public List<TierCopywriteDiff> queryTierCopywriteDiffList(String vipType,String tenantId){
        String queryTenant = config.containsKey(tenantId) ? tenantId : TENANTID_VICOO;
        return config.get(queryTenant).getOrDefault(vipType,Lists.newArrayList());
    }
}
