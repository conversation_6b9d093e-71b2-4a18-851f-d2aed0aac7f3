package com.addx.iotcamera.config.app;

import com.addx.iotcamera.bean.response.ZendeskItem;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.zendesk.client.v2.Zendesk;

import javax.annotation.PostConstruct;
import java.util.Map;

import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;

@Data
@Component
@ConfigurationProperties(prefix = "zendesk")
public class ZendeskConfig {
    // tenantId app 初始化 zendesk 参数
    Map<String,Map<String,ZendeskItem>> sdkconfig;

    //后端调用zendesk api 配置
    Map<String,ZendeskUser> userconfig;



    public Map<String,Zendesk> zendeskMap = Maps.newHashMap();


    /**
     * 查询app对应zendesk 配置
     * @param tenantId
     * @return
     */
    public ZendeskItem queryZendeskItem(String tenantId,String serveNode){
        return sdkconfig.get(sdkconfig.containsKey(tenantId) ? tenantId : TENANTID_VICOO).get(serveNode);
    }

    @PostConstruct
    public void init() {
        for(String tenantId : userconfig.keySet()){
            ZendeskUser zendeskUser = this.queryZendeskUser(tenantId);
            Zendesk zendesk = new Zendesk.Builder(zendeskUser.getUrl()).setUsername(zendeskUser.getUsername()).setToken(zendeskUser.getToken()).setRetry(true).build();
            this.zendeskMap.put(tenantId,zendesk);
        }
    }

    public Zendesk queryZendesk(String tenantId){
        return zendeskMap.containsKey(tenantId) ? zendeskMap.get(tenantId): zendeskMap.get(TENANTID_VICOO);
    }


    /**
     * 获取zendesk api 配置
     * @param tenantId
     * @return
     */
    public ZendeskUser queryZendeskUser(String tenantId){
        return userconfig.get(userconfig.containsKey(tenantId) ? tenantId : TENANTID_VICOO);
    }

    @Data
    public static class ZendeskUser{
        @Schema(title = "节点")
        private String zone;
        @Schema(title = "zendesk jwt 秘钥")
        private String jwtsecret;
        @Schema(title = "zendesk api 地址")
        private String url;
        @Schema(title = "zendesk 用户名")
        private String username;
        @Schema(title = "zendesk token")
        private String token;
    }
}
