package com.addx.iotcamera.config.device;

import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.AppFormOptionsDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.app.TenantFreeTierConfig;
import com.addx.iotcamera.enums.pay.TierTypeEnums;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.UserTierDeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.DeviceModelNoUtil;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.api.client.util.Lists;
import lombok.Data;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_2_YEAR;

@Data
@Component
@PropertySource(value = {"classpath:/models/free_user_vip_tier2.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "free-user-vip-tier2")
public class FreeUserVipTier2Config implements InitializingBean {

    private static FreeUserVipTier2Config INSTANCE = null;

    @Autowired
    private TenantFreeTierConfig tenantFreeTierConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    /**
     * 根据用户注册的免费套餐2配置
     */
    private List<RegisterConfig> registerConfigList;


    @Autowired
    private UserSettingService userSettingService;

    @Resource
    private FreeLicenseService freeLicenseService;

    @Resource
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private DeviceModelConfigService deviceModelConfigService;

    @Resource
    private TierService tierService;


    @Override
    public void afterPropertiesSet() throws Exception {
        INSTANCE = this;
    }

    public static long getTierCapacityByUserRegisterTime(Integer tierId, int registerTime) {
        Integer matchTierId = (tierId == null || tierId <= 0) ? 100 : tierId;
        RegisterConfig registerConfig = INSTANCE.registerConfigList.stream().filter(registerConfigObj -> ObjectUtils.equals(matchTierId, registerConfigObj.getTierId()) && registerTime >= registerConfigObj.getRegisterStart() && registerTime < registerConfigObj.getRegisterEnd()).findFirst().orElse(null);
        return Double.valueOf(registerConfig.capacityGB * 1024 * 1024 * 1024).longValue();
    }

    public static double getTierCapacityGbByUserRegisterTime(Integer tierId, int registerTime) {
        Integer matchTierId = (tierId == null || tierId <= 0) ? 100 : tierId;
        RegisterConfig registerConfig = INSTANCE.registerConfigList.stream().filter(registerConfigObj -> ObjectUtils.equals(matchTierId, registerConfigObj.getTierId()) && registerTime >= registerConfigObj.getRegisterStart() && registerTime < registerConfigObj.getRegisterEnd()).findFirst().orElse(null);
        return registerConfig.capacityGB;
    }

    public static int getTierLookBackDayByUserRegisterTime(Integer tierId, int registerTime) {
        Integer matchTierId = (tierId == null || tierId <= 0) ? 100 : tierId;
        RegisterConfig registerConfig = INSTANCE.registerConfigList.stream().filter(registerConfigObj -> ObjectUtils.equals(matchTierId, registerConfigObj.getTierId()) && registerTime >= registerConfigObj.getRegisterStart() && registerTime < registerConfigObj.getRegisterEnd()).findFirst().orElse(null);
        return registerConfig.getLookBackDay();
    }

    public static List<AppFormOptionsDO.CooldownOptionValue> getCooldownOptionValueList(Integer tierId, int registerTime, String modelNo, String sn) {
        RegisterConfig registerConfig = INSTANCE.registerConfigList.stream()
                .filter(registerConfigObj -> ObjectUtils.equals(tierId, registerConfigObj.getTierId()) && registerTime >= registerConfigObj.getRegisterStart() && registerTime < registerConfigObj.getRegisterEnd())
                .findFirst()
                .orElse(null);

        Comparator<AppFormOptionsDO.CooldownOptionValue> cooldownOptionValueComparator = (cooldownOptionValue1, cooldownOptionValue2) -> ObjectUtils.compare(cooldownOptionValue1.getValue(), cooldownOptionValue2.getValue());

        if(INSTANCE == null || StringUtils.isEmpty(modelNo)) {
            return Arrays.asList(AppFormOptionsDO.CooldownOptionValue.values())
                    .stream()
                    .filter(cooldownOption -> registerConfig.getTotalCooldownValueList().contains(cooldownOption.getValue()))
                    .sorted(cooldownOptionValueComparator)
                    .collect(Collectors.toList());
        }

        DeviceModel deviceModel = INSTANCE.deviceModelConfigService.queryDeviceModelConfig(sn);
        boolean standBy = deviceModel != null && deviceModel.isCanStandby();

        List<AppFormOptionsDO.CooldownOptionValue> cooldownValueOptionList = null;
        if (MapUtils.isEmpty(registerConfig.getCooldownValue()) || !registerConfig.getCooldownValue().containsKey(modelNo.toUpperCase())) {
            // 未配置型号可选项,常电设备不使用默认值
            cooldownValueOptionList = !standBy ? Lists.newArrayList() : Arrays.asList(AppFormOptionsDO.CooldownOptionValue.values())
                    .stream()
                    .filter(cooldownOption -> registerConfig.getTotalCooldownValueList().contains(cooldownOption.getValue()))
                    .sorted(cooldownOptionValueComparator)
                    .collect(Collectors.toList());

        } else {
            // 配置了型号支持可选项
            cooldownValueOptionList = Arrays.asList(registerConfig.getCooldownValue().get(modelNo.toUpperCase()).split(",")).stream()
                    .map(str -> Arrays.asList(
                            AppFormOptionsDO.CooldownOptionValue.values())
                            .stream()
                            .filter(cooldownOptionValue -> ObjectUtils.equals(cooldownOptionValue.getValue(), Integer.valueOf(str)))
                            .findAny()
                            .orElse(null))
                    .filter(obj -> obj!=null)
                    .sorted(cooldownOptionValueComparator)
                    .collect(Collectors.toList());
        }

        // 合并按canStandby配置的免费选项
        List<AppFormOptionsDO.CooldownOptionValue> canStandbyFreeOptions = AppFormOptionsDO.CooldownOptionValue
                .getFreeOptionsByCanStandby(standBy);
        cooldownValueOptionList = Stream.concat(cooldownValueOptionList.stream(), canStandbyFreeOptions.stream()).distinct().sorted(cooldownOptionValueComparator).collect(Collectors.toList());

        return cooldownValueOptionList;
    }

    public static List<Integer> getCooldownOptionValueListBySerialNumber(String serialNumber) {
        if(INSTANCE == null || INSTANCE.tenantFreeTierConfig == null) {
            return Arrays.asList(AppFormOptionsDO.CooldownOptionValue.values()).stream().map(AppFormOptionsDO.CooldownOptionValue::getValue).collect(Collectors.toList());
        }

        int adminId = INSTANCE.userRoleService.getDeviceAdminUser(serialNumber);
        User user = INSTANCE.userService.queryUserById(adminId);

        UserSettingsDO userSettingsDO = INSTANCE.userSettingService.queryUserSetting(adminId);
        boolean supportFreeLicenseTierId = userSettingsDO != null && userSettingsDO.getSupportFreeLicense().equals(1);
        Integer currentTierId = INSTANCE.userTierDeviceService.getDeviceCurrentTier(adminId,serialNumber);

        Integer freeTierId = supportFreeLicenseTierId ? Optional.ofNullable(currentTierId).orElse(FREE_LICENSE_2_YEAR) :
                INSTANCE.tenantFreeTierConfig.queryFreeTier(user.getTenantId());

        int registTime = user.getRegistTime();
        String modelNo = DeviceModelNoUtil.getDeviceModelNo(serialNumber);
        List<AppFormOptionsDO.CooldownOptionValue> cooldownOptionValueList = getCooldownOptionValueList(freeTierId, registTime, modelNo, serialNumber);
        return cooldownOptionValueList.stream().map(AppFormOptionsDO.CooldownOptionValue::getValue).collect(Collectors.toList());
    }

    public static List<Integer> getVideoSecondsValueListBySerialNumber(String serialNumber,Boolean isPromontion) {
        List<Integer> defaultVideoSecondsList = Arrays.asList(
                AppFormOptionsDO.VideoSecondOptionValue.values())
                .stream()
                .filter(videoSecondOptionValue -> isPromontion? videoSecondOptionValue.isPromotion() : videoSecondOptionValue.isFreeOption())
                .map(AppFormOptionsDO.VideoSecondOptionValue::getValue)
                .collect(Collectors.toList());
        Comparator<AppFormOptionsDO.VideoSecondOptionValue> videoSecondOptionValueComparator = (videoSecondOptionValue1, videoSecondOptionValue2) -> ObjectUtils.compare(videoSecondOptionValue1.getValue(), videoSecondOptionValue2.getValue());

        if(INSTANCE == null || INSTANCE.tenantFreeTierConfig == null) {
            return defaultVideoSecondsList;
        }

        int adminId = INSTANCE.userRoleService.getDeviceAdminUser(serialNumber);
        User user = INSTANCE.userService.queryUserById(adminId);
        Integer freeTierId = INSTANCE.tenantFreeTierConfig.queryFreeTier(user.getTenantId());
        int registTime = user.getRegistTime();
        String modelNo = DeviceModelNoUtil.getDeviceModelNo(serialNumber);

        RegisterConfig registerConfig = INSTANCE.registerConfigList.stream().filter(registerConfigObj -> ObjectUtils.equals(freeTierId, registerConfigObj.getTierId()) && registTime >= registerConfigObj.getRegisterStart() && registTime < registerConfigObj.getRegisterEnd()).findFirst().orElse(null);
        if(registerConfig == null) {
            return defaultVideoSecondsList;
        }

        List<Integer> videoSecondsOptionList = null;
        if (MapUtils.isEmpty(registerConfig.getVideoSecondsValue()) || StringUtils.isEmpty(modelNo) || !registerConfig.getVideoSecondsValue().containsKey(modelNo.toUpperCase())) {
            videoSecondsOptionList = defaultVideoSecondsList;
        } else {
            videoSecondsOptionList = Arrays.asList(registerConfig.getVideoSecondsValue().get(modelNo.toUpperCase()).split(",")).stream()
                    .map(str -> Arrays.asList(AppFormOptionsDO.VideoSecondOptionValue.values()).stream().filter(videoSecondValue -> ObjectUtils.equals(videoSecondValue.getValue(), Integer.valueOf(str))).findAny().orElse(null))
                    .filter(obj -> obj!=null)
                    .sorted(videoSecondOptionValueComparator)
                    .map(AppFormOptionsDO.VideoSecondOptionValue::getValue)
                    .collect(Collectors.toList());
        }
        return videoSecondsOptionList;
    }


    @Data
    public static class RegisterConfig {
        private Integer tierId;
        private int registerStart;
        private int registerEnd;
        private double capacityGB;
        private int lookBackDay;
        private List<Integer> totalCooldownValueList = Arrays.asList(AppFormOptionsDO.CooldownOptionValue.values()).stream().map(AppFormOptionsDO.CooldownOptionValue::getValue).collect(Collectors.toList());
        private List<Integer> totalVideoSecondsValueList = Arrays.asList(AppFormOptionsDO.VideoSecondOptionValue.values()).stream().map(AppFormOptionsDO.VideoSecondOptionValue::getValue).collect(Collectors.toList());
        private Map<String, String> cooldownValue;
        private Map<String, String> videoSecondsValue;
    }
}
