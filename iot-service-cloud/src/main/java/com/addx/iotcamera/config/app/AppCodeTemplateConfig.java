package com.addx.iotcamera.config.app;

import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@PropertySource(value = {"classpath:/app/app_code_template.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "code.template")
public class AppCodeTemplateConfig {
    // 指定短信模板,language-template
    Map<String,String> template;

    // 短信签名
    Map<String, String> signName;

    //阿里云短信服务秘钥
    private String accessKeyId;
    private String accessKeySecret;
}
