package com.addx.iotcamera.config.app;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.FreeLicenseABTestResult;
import com.addx.iotcamera.bean.response.user.RecommendProductDO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.abtest.model.AbFeatureSimpleResult;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.util.MixPropertySourceFactory;
import com.google.api.client.util.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.addx.iotcamera.constants.AbTestConstants.YEAR_TIER_RECOMMEND_BUTTON;
import static com.addx.iotcamera.constants.AbTestConstants.YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT;
import static com.addx.iotcamera.constants.PayConstants.DEFAULT_FREE_TRAIL_DAY;
import static org.addx.iot.common.constant.AppConstants.*;

@Data
@Component
@Slf4j
@PropertySource(value = {"classpath:/app/app_recommend_product.yml"}, encoding = "utf-8", factory = MixPropertySourceFactory.class)
@ConfigurationProperties(prefix = "app.recommend.product")
public class AppRecommendProductConfig {
    // tenantId 免费领取、推荐商品
    Map<String, TierRecommendProduct> config;

    @Resource
    private AbTestService abTestService;

    @Resource
    private IUserVipDAO userVipDAO;

    @Resource
    @Lazy
    private UserSettingService userSettingService;

    public static final String BUTTON_FEATURE_SUBSCRIBE = "subscribe_btn";

    public static final String BUTTON_FEATURE_FREE = "try_it_free";

    @Data
    public static class TierRecommendProduct{
        @Schema(title = "免费领取商品Id")
        private Integer freeProductId;
        @Schema(title = "推荐商品id")
        private Integer recommendProductId;

        @Schema(title = "推荐商品默认选中商品id-月订阅")
        private Integer defaultMonthSelectProductId;
        @Schema(title = "推荐商品默认选中商品id-年订阅")
        private Integer defaultYearSelectProductId;

        @Schema(title = "推荐商品默认选中商品id-月订阅-无freetrial")
        private Integer defaultMonthSelectProductIdWithoutFreeTrail;
        @Schema(title = "推荐商品默认选中商品id-年订阅-无freetrial")
        private Integer defaultYearSelectProductIdWithoutFreeTrail;

        @Schema(title = "推荐商品id-月订阅")
        private List<UserVipTier.RecommendProduct> recommendProductIdV1;

        @Schema(title = "推荐商品id-年订阅")
        private List<UserVipTier.RecommendProduct> recommendProductIdYear;

        @Schema(title = "推荐商品id-月订阅-无freetrial")
        private List<UserVipTier.RecommendProduct> recommendProductIdV1WithoutFreeTrail;

        @Schema(title = "推荐商品id-年订阅-无freetrial")
        private List<UserVipTier.RecommendProduct> recommendProductIdYearWithoutFreeTrail;

        @Schema(title = "推荐商品id-月订阅-7天freetrial")
        private List<UserVipTier.RecommendProduct> recommendProductIdV1With7DayFreeTrial;

        @Schema(title = "推荐商品id-年订阅-7天freetrial")
        private List<UserVipTier.RecommendProduct> recommendProductIdYearWith7DayFreeTrial;

        @Schema(title = "推荐商品-4g套餐")
        private List<UserVipTier.RecommendProduct> recommendProduct4g;
        @Schema(title = "推荐商品-4g套餐-年订阅")
        private List<UserVipTier.RecommendProduct> recommendProductYear4g;
        @Schema(title = "推荐商品-4g套餐-半年订阅")
        private List<UserVipTier.RecommendProduct> recommendProductHalfYear4g;

        @Schema(title = "推荐商品默认选中商品id-月订阅")
        private Integer defaultSelectProduct4GId;
        @Schema(title = "推荐商品默认选中商品id-半年订阅")
        private Integer defaultSelectProductHalfYear4GId;
        @Schema(title = "推荐商品默认选中商品id-年订阅")
        private Integer defaultSelectProductYear4GId;
    }



    /**
     * 获取推荐商品Id
     * @param tenantId
     * @return
     */
    public Integer queryRecommendProductId(String tenantId){
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductId() : null;
    }


    /**
     * 获取推荐商品Id-月
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdV1(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdV1() : Lists.newArrayList();
    }

    /**
     * 获取推荐商品Id-月
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdV1WithoutFreeTrail(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdV1WithoutFreeTrail() : Lists.newArrayList();
    }

    /**
     * 获取推荐商品Id-月 7天freetrial
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdV1With7DayFreeTrial(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdV1With7DayFreeTrial() : Lists.newArrayList();
    }

    /**
     * 获取4G推荐商品
     * @param tenantId
     * @param appType
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProduct4G(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProduct4g() : Lists.newArrayList();
    }

    /**
     * 获取4G推荐商品-年订阅
     * @param tenantId
     * @param appType
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductYear4G(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductYear4g() : Lists.newArrayList();
    }

    /**
     * 获取4G推荐商品-半年订阅
     * @param tenantId
     * @param appType
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductHalfYear4G(String tenantId, String appType) {
        if (TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)) {
            return Lists.newArrayList();
        }
        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductHalfYear4g() : Lists.newArrayList();
    }

    /**
     * 获取推荐商品Id-年订阅
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdYear(String tenantId,String appType){
        if(TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)){
            return Lists.newArrayList();
        }

        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdYear() : Lists.newArrayList();
    }

    /**
     * 获取推荐商品Id-年订阅-7天freetrial
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdYearWith7DayFreeTrial(String tenantId,String appType){
        if(TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)){
            return Lists.newArrayList();
        }

        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdYearWith7DayFreeTrial() : Lists.newArrayList();
    }

    /**
     * 获取推荐商品Id-年订阅-WithOutFreeTrail
     * @param tenantId
     * @return
     */
    public List<UserVipTier.RecommendProduct> queryRecommendProductIdYearWithOutFreeTrail(String tenantId,String appType){
        if(TENANTID_GUARD.equals(tenantId) && APP_TYPE_ANDROID.equals(appType)){
            return Lists.newArrayList();
        }

        return config.containsKey(tenantId) ? config.get(tenantId).getRecommendProductIdYearWithoutFreeTrail() : Lists.newArrayList();
    }

    /**
     * 推荐商品-默认选中商品id
     * @param tenantId
     * @return
     */
    public Integer queryDefaultSelectProductId(String tenantId, boolean yearProduct){
        if(!config.containsKey(tenantId)){
            return 0;
        }
        return yearProduct ?  config.get(tenantId).getDefaultYearSelectProductId() : config.get(tenantId).getDefaultMonthSelectProductId();
    }

    /**
     * 推荐商品-默认选中商品id-WithoutFreeTrail
     * @param tenantId
     * @return
     */
    public Integer queryDefaultSelectProductIdWithoutFreeTrail(String tenantId, boolean yearProduct){
        if(!config.containsKey(tenantId)){
            return 0;
        }
        return yearProduct ?  config.get(tenantId).getDefaultYearSelectProductIdWithoutFreeTrail() : config.get(tenantId).getDefaultMonthSelectProductIdWithoutFreeTrail();
    }

    /**
     * 获取免费领取商品
     * @param tenantId
     * @return
     */
    public Integer queryFreeProductId(String tenantId){
        return config.containsKey(tenantId) ? config.get(tenantId).getFreeProductId() : null;
    }

    /**
     * 推荐商品4G-默认选中商品id
     * @param tenantId
     * @return
     */
    public Integer queryDefaultSelectProduct4GId(String tenantId){
        return config.containsKey(tenantId) ? config.get(tenantId).getDefaultSelectProduct4GId() : null;
    }

    /**
     * 推荐商品4G-半年付-默认选中商品id
     * @param tenantId
     * @return
     */
    public Integer queryDefaultSelectProductHalfYear4GId(String tenantId){
        return config.containsKey(tenantId) ? config.get(tenantId).getDefaultSelectProductHalfYear4GId() : null;
    }

    /**
     * 推荐商品4G-年付-默认选中商品id
     * @param tenantId
     * @return
     */
    public Integer queryDefaultSelectProductYear4GId(String tenantId){
        return config.containsKey(tenantId) ? config.get(tenantId).getDefaultSelectProductYear4GId() : null;
    }

    /**
     * 组装推荐商品
     * @param userId
     * @param appType
     * @param language
     * @param tenantId
     * @return
     */
    public RecommendProductDO initRecommendProductDO(Integer userId,String appType,String language,String tenantId, AppRequestBase requestBase){
        if (tenantId.equals(tenantIdKiwibit)) {
            RecommendProductDO recommendProductDO = new RecommendProductDO();
            recommendProductDO.setRecommendProductList(this.queryRecommendProductIdV1(tenantId,appType));
            recommendProductDO.setRecommendProductYearList(this.queryRecommendProductIdYear(tenantId,appType));
            recommendProductDO.setDefaultSelectedProductId(
                            this.queryDefaultSelectProductId(tenantId,true)
            ) ;
            recommendProductDO.setYearFeatureGroup("1");
            List<UserVipDO> userVipDOList = userVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            boolean usedToBeVip = userVipDOList.stream().anyMatch(userVipDO -> userVipDO.getTierId() % 10 > 0);
            recommendProductDO.setButtonFeatureGroup(usedToBeVip ? BUTTON_FEATURE_SUBSCRIBE: BUTTON_FEATURE_FREE);
            return recommendProductDO;
        }
        //判断是否在实验组内
        Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
        RecommendProductDO recommendProductDO = new RecommendProductDO();
        FreeLicenseABTestResult abResult = abTestService.getFreeLicenseAbResult(userId, requestBase);
        ABTestResult awarenessFreeTrailDayAbResult = abTestService.getAwarenessFreeTrailDayAbResult(userId, requestBase);
        if (awarenessFreeTrailDayAbResult != null && awarenessFreeTrailDayAbResult.isExperimentSuccessful()) {
            abFeatureSimpleResultList.putAll(awarenessFreeTrailDayAbResult.getAbFeatureSimpleResultList());
        }
        //配置推荐商品，如果命中实验组，配置无freetrial商品，命中对照组或未命中，配置原本商品

        if (abResult.isNotFreeTrial() || Objects.requireNonNull(awarenessFreeTrailDayAbResult).isHit0DayExperimentGroup()) {
            recommendProductDO.setRecommendProductList(this.queryRecommendProductIdV1WithoutFreeTrail(tenantId, appType));
        } else if (awarenessFreeTrailDayAbResult.isHit7DayExperimentGroup()) {
            recommendProductDO.setRecommendProductList(this.queryRecommendProductIdV1With7DayFreeTrial(tenantId, appType));
        } else {
            recommendProductDO.setRecommendProductList(this.queryRecommendProductIdV1(tenantId, appType));
        }
        if(TENANTID_VICOO.equals(tenantId)){
            //配置年推荐商品，如果命中实验组，配置无freetrial商品，命中对照组或未命中，配置原本商品
            if (abResult.isNotFreeTrial() || Objects.requireNonNull(awarenessFreeTrailDayAbResult).isHit0DayExperimentGroup()) {
                recommendProductDO.setRecommendProductYearList(this.queryRecommendProductIdYearWithOutFreeTrail(tenantId, appType));
            } else if (awarenessFreeTrailDayAbResult.isHit7DayExperimentGroup()) {
                recommendProductDO.setRecommendProductYearList(this.queryRecommendProductIdYearWith7DayFreeTrial(tenantId, appType));
            } else {
                recommendProductDO.setRecommendProductYearList(this.queryRecommendProductIdYear(tenantId, appType));
            }

            // 默认选中商品abTest
            AbFeatureSimpleResult defaultProductResult = abTestService.singleFeatureCheck(String.valueOf(userId),YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT,appType,language,tenantId, requestBase.getApp().getVersionName());
            recommendProductDO.setDefaultSelectedProductId(
                    defaultProductResult.experimentSuccessful() ?
                    Integer.valueOf(defaultProductResult.getValue()) :
                    this.queryDefaultSelectProductId(tenantId,true)
            ) ;
            recommendProductDO.setDefaultSelectedProductId(abResult.isNotFreeTrial()? this.queryDefaultSelectProductIdWithoutFreeTrail(tenantId,true) :
                    recommendProductDO.getDefaultSelectedProductId());
            if (defaultProductResult.experimentSuccessful()) {
                abFeatureSimpleResultList.put(defaultProductResult.getFeatureId(), defaultProductResult.getVariationId());
            }
            recommendProductDO.setYearFeatureGroup("1");
        }else{
            recommendProductDO.setYearFeatureGroup("-1");
            recommendProductDO.setDefaultSelectedProductId(abResult.isNotFreeTrial()? this.queryDefaultSelectProductIdWithoutFreeTrail(tenantId,false):
                    this.queryDefaultSelectProductId(tenantId,false));
        }

        UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(userId);
        List<UserVipDO> userVipDOList = userVipDAO.queryUserVipInfo(userId, 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
        boolean usedToBeVip = userVipDOList.stream().anyMatch(userVipDO -> userVipDO.getTierId() % 10 > 0);

        if (Optional.ofNullable(userSettingsDO.getSupportFreeLicense()).orElse(0) == 0 || !usedToBeVip) {
            AbFeatureSimpleResult abFeatureSimpleResultForButton = abTestService.singleFeatureCheck(String.valueOf(userId), YEAR_TIER_RECOMMEND_BUTTON, appType, language, tenantId, requestBase.getApp().getVersionName());
            if (abFeatureSimpleResultForButton.experimentSuccessful()) {
                recommendProductDO.setButtonFeatureGroup(abFeatureSimpleResultForButton.getVariationId() >= 0 ? abFeatureSimpleResultForButton.getValue() : "");
                abFeatureSimpleResultList.put(abFeatureSimpleResultForButton.getFeatureId(), abFeatureSimpleResultForButton.getVariationId());
            } else {
                recommendProductDO.setButtonFeatureGroup("");
            }
        } else {
            recommendProductDO.setButtonFeatureGroup(BUTTON_FEATURE_SUBSCRIBE);
        }

        if (abResult.isNotFreeTrial() || Objects.requireNonNull(awarenessFreeTrailDayAbResult).isHit0DayExperimentGroup()) {
            //命中实验组
            recommendProductDO.setButtonFeatureGroup(BUTTON_FEATURE_SUBSCRIBE);
            if (abResult.getAbFeatureSimpleResultList() != null) {
                abFeatureSimpleResultList.putAll(abResult.getAbFeatureSimpleResultList());
            }
        } else if (abResult.isFreeTrial()) {
            //命中对照组
            recommendProductDO.setButtonFeatureGroup(recommendProductDO.getButtonFeatureGroup());
            if (abResult.getAbFeatureSimpleResultList() != null) {
                abFeatureSimpleResultList.putAll(abResult.getAbFeatureSimpleResultList());
            }
        }
        recommendProductDO.setFreeTrialDay(Objects.requireNonNull(awarenessFreeTrailDayAbResult).isExperimentSuccessful() ? awarenessFreeTrailDayAbResult.getValue() : DEFAULT_FREE_TRAIL_DAY);
        recommendProductDO.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
        return recommendProductDO;
    }
}
