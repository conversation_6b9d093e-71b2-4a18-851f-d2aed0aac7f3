package com.addx.iotcamera.constants;

import com.addx.iotcamera.enums.ProductTypeEnums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class PayConstants {
    public final static String MOMO_PAY_TOKEN = "momo:pay:token";
    //momo pay 获取token url
    public final static String MOMO_PAY_QUERY_TOKEN_URL = "/collection/token/";
    //momo pay 请求支付 url
    public final static String MOMO_PAY_REQUEST_PAY_URL = "/collection/v1_0/requesttopay";
    //momo pay 查询支付结果
    public final static String MOMO_PAY_QUERY_PAY_RESULT_URL = "/collection/v1_0/requesttopay/";
    //  momo pay订单支付状态
    public final static String MOMO_PAY_STATUS_SUCCESS = "SUCCESSFUL";
    public final static String MOMO_PAY_STATUS_PENDING = "PENDING";
    public final static String MOMO_PAY_STATUS_FAILED = "FAILED";

    //查询谷歌退款订单list
    public final static String GOOGLE_CANCEL_ORDER_LIST = "https://www.googleapis.com/androidpublisher/v3/applications/com.smartaddx.vicohome/purchases/voidedpurchases?";


    public final static String TIER_COPYWRITE_DIFF_LITE = "lite";
    public final static String TIER_COPYWRITE_DIFF_NOPLAN = "noPlan";
    public final static String TIER_COPYWRITE_DIFF_VIP = "vip";

    public final static String TIER_COPYWRITE_DIFF_BIRD_AND_OTHERS = "birdAndOthers";
    public final static String TIER_COPYWRITE_DEVICE_ONLY_BIRD = "onlyBird";
    public final static String TIER_COPYWRITE_DEVICE_NO_BIRD = "noBird";

    //订阅类型商品
    public final static Set<Integer> SUB_TIER_PRODUCT_SET = new HashSet<>(
            Arrays.asList(
                    ProductTypeEnums.SUBSCRIBE.getCode(),
                    ProductTypeEnums.PRODUCT_DEVICE_NUM.getCode(),
                    ProductTypeEnums.PRODUCT_LEVEL_DEVICE_NUM.getCode(),
                    ProductTypeEnums.PRODUCT_DEVICE_4G.getCode()
            )
    );

    //公钥类型
    public final static String PUBLICKEY_TYPE = "RSA";
    //苹果公钥地址
    public static String APPLE_AUTH_URL = "https://appleid.apple.com/auth/keys";
    public static final String ISSUER = "https://appleid.apple.com";

    public static final int DEFAULT_ROOLING_DAY = 30;
    public static final int DEFAULT_KIWIBIT_ROOLING_DAY = 60;

    // 设备数量限制套餐名称Key
    public static final String TIER_NAME_KEY_ONE_DEVICE = "gen2_single_plan_title";
    public static final String TIER_NAME_KEY_TWO_DEVICE = "two_device_plan";
    public static final String TIER_NAME_KEY_NO_LIMIT_DEVICE = "gen2_unlimited_plan_title";
    public static final String TIER_NAME_KEY_BASIC_ONE_DEVICE = "tier_message_name_basic_one_device";
    public static final String TIER_NAME_KEY_PLUS_ONE_DEVICE = "tier_message_name_plus_one_device";
    public static final String TIER_NAME_KEY_BASIC_TWO_DEVICE = "tier_message_name_basic_two_device";
    public static final String TIER_NAME_KEY_PLUS_TWO_DEVICE = "tier_message_name_plus_two_device";
    public static final Integer MONTH_TIME_SECEND = 30 * 24 * 60 * 60;
    public static final Integer TWO_HOUR_SECEND = 48 * 3600;
    public static final String TIER_NAME_KEY_4G = "tier_message_name_4g_{deviceLimit}";

    public static final String QUERY_PRODUCT_ID_BY_SUBSCRIPTION_URL = "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{packageName}/purchases/subscriptionsv2/tokens/{token}?";
    public static final String OUT_OF_PLAN_KEY = "out_of_plan";


    public static final String FREE_TIER_EXPIRE_30_DAY_START_USER_ID = "freeTier30DayStartUserId:{date}";
    public static final String FREE_TIER_EXPIRE_180_DAY_START_USER_ID = "freeTier180DayStartUserId:{date}";
    public static final String FREE_TIER_EXPIRE_START_USER_ID = "freeTierExpireStartUserId:{date}";

    public static final String TEMPLATE_EMAIL_SEND_NUM = "{template}:ESend:{date}";


    public static final Integer FREE_TIER_EXPIRE_30_DAY_REGISTER_TIME = (2 * 365 - 30) * 24 * 60 * 60;
    public static final Integer FREE_TIER_EXPIRE_180_DAY_REGISTER_TIME = (2 * 365 - 180) * 24 * 60 * 60;
    public static final Integer FREE_TIER_EXPIRE_EXPIRE_REGISTER_TIME = (2 * 365) * 24 * 60 * 60;

    public static final int FREE_TIER_EXPIRE_SOON = 30 * 24 * 60 * 60;
    public static final int PURCHASE_TIER_EXPIRE_SOON = 7 * 24 * 60 * 60;
    public static final int SUB_VIP_EXPIRE = 8 * 24 * 60 * 60;

    public static final int PURCHASE_TIER_EXPIRE_NO_SUB = 24 * 60 * 60;



    public static final Integer FREE_LICENSE_7_DAY  = 1001;
    public static final Integer FREE_LICENSE_1_DAY  = 1002;
    public static final Integer FREE_LICENSE_2_YEAR  = 1003;
    public static final Integer FREE_TIER_2  = 100;
    public static final Set<Integer> FREE_LICENSE_TIER_NOT_VIP  = new HashSet<>(Arrays.asList(1002,1003));
    public static final String FREE_LICENSE_TIER_NAME_KEY  = "deviceFreeLicense_{tierId}";


    public static final int TIER_EXPIRE_START_USER_EXPIRE = 3 * 24 * 60 * 60;

    public static final Set<Integer> TIER_ID_SETS_4G  = new HashSet<>(Arrays.asList(231,232,233,234,235,236,237,238));
    public static final Integer DEFAULT_FREE_TRAIL_DAY = 30;
    public static final Integer SEVEN_FREE_TRAIL_DAY = 7;
    public static final Integer ZERO_FREE_TRAIL_DAY = 0;

    public static final String TIER_RECEIVE_KEY = "tierReceive-{userId}";
    public static final int TIER_RECEIVE_EXPIRE_START = 2 * 24 * 60 * 60;

    public static final String TIER_NAME_KEY_AI_BIRD_DEVICE = "tier_redeem_product_name";
    public static final Integer AI_BIRD_DEVICE_TIER  = 1004;
    public static final Set<Integer> FREE_LICENSE_TIER  = new HashSet<>(Arrays.asList(1001,1002,1003));
    public static final Set<String> AI_BIRD_MODEL_NO_SET = new HashSet<>(Arrays.asList("BC11110B"));
    public static final Integer AI_BIRD_PRODUCT_ID = 1014014;

}
