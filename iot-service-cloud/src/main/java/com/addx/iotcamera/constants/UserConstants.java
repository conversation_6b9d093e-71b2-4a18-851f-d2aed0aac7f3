package com.addx.iotcamera.constants;

public class UserConstants {
    //用户设置消息（pir）免打扰标记
    public static final String USER_MESSAGE_SHIELD_KEY = "user:message:shield:{userId}";

    //用户设置消息（pir）免打扰标记时间
    public static final String USER_MESSAGE_SHIELD_TIME_KEY = "user:message:shield:time:{userId}";

    // 到期时间
    public final static String PAY_EMAIL_IMAGE = "<img src=\"{emailPayImageUrl}\" />";
    public final static String PAY_EMAIL_BR = "</br>";
    public final static String PAY_EMAIL_A_HREF = "<a href = \"{url}\">{content}</a>";
    /**
     * 监听用户事件
     */
    public static final String USER_REMINDER_EVENT_KEY = "user:reminder:email:{userId}:{type}:{firstTime}";
    public static final String USER_REMINDER_EVENT_PRE_KEY = "user:reminder:email";
    // 用户套餐结束前3天
    public static final int VIP_EXPIRE_TIME_ADVANCE3 = 3 * 24 * 60 * 60;
    // 用户套餐结束前1天
    public static final int VIP_EXPIRE_TIME_ADVANCE1 = 1 * 24 * 60 * 60;
    // 用户第一次设备绑定1天后
    public static final long FIRST_DEVICE_BIND = 7 * 24 * 60 * 60;
    // 用户注册后60天
    public static final long USER_CONTINUEUSE = 2 * 30 * 24 * 60 * 60;

    //shading消息类型
    public static final String SHARE_MSG_TYPE_KEY = "msgType";

    //用户套餐active刷新key
    public static final String USER_VIP_ACTIVE_KEY = "userVipActive:{date}";
    public static final String USER_IOS_SUBORDER_START_KEY = "userSubOrderStart:{date}";
    public static final int USER_VIP_ACTIVE_EXPIRE_TIME = 2 * 24 * 60 * 60;

    public static final String USER_FREE_TIER_REFRASH_KEY = "userFreeTierRefrash:{date}";

    public static final String USER_ABTEST_REPORT_TYPE = "userAbTestReportType";

    public static final String USER_VIP_EXPIRE_NOTIFY = "userVipExpireNotify:{userId}";

    public static final String USER_TIER_EXPIRE_KEY = "expirationReminderPopupMessage";
    public static final String USER_TIER_EXPIRE_TODAY_KEY = "expirationReminderPopupMessageToday";


    public static final String USER_TIER_EXPIRE_FREE_KEY = "expirationReminderPopupMessageFree";
    public static final String USER_TIER_EXPIRE_TODAY_FREE_KEY = "expirationReminderPopupMessageTodayFree";


    public static final int USER_EARLY_REGISTERE_USER_VICOO = 1669219200;
    public static final int USER_EARLY_REGISTERE_USER_SHENMOU = 1672531200;

}
