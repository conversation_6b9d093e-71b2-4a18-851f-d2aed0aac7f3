package com.addx.iotcamera.constants;

import com.addx.iotcamera.enums.EReportEvent;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class DeviceInfoConstants {
    // 设备固件构建号
    public final static String DEVICE_FIRMWARE_BUILDER = "device:firmware:builder:{serialNumber}";

    /**
     * 查询mqtt返回重试次数
     */
    public final static int DEVICE_MQTT_RETRY_COUNT = 30;
    // 设备status info
    public final static String DEVICE_STATUS_INFO = "device:status:info:{serialNumber}";
    public final static int DEVICE_STATUS_EXPIRE = 60 * 10;

    public final static int QUERY_DEVICE_LIMIT_NUM = 1000;

    public final static String CMD_DEVICE_OPERATION_PREFIX  = "cmd:";
    public final static String CMD_DEVICE_SHARE_PREFIX  = "share:";

    public final static String DEVICE_KEEP_ALIVE_PREFIX = "device:keep:alive:{serialNumber}";
    // 用户与设备关系
    public final static String DEVICE_USER_ROLE_PREFIX = "userDeviceRole::{userId}-{serialNumber}";
    public final static String DEVICE_ADMIN_ROLE_PREFIX = "deviceAdminRole::{serialNumber}";
    public final static String DEVICE_ADMIN_PREFIX = "deviceAdminV1::{serialNumber}";
    public final static String DEVICE_ROLE_PREFIX = "deviceRole::{serialNumber}";
    public final static String DEVICE_ADMIN_COUNTRY_NO_PREFIX = "deviceAdminCountryNo::{serialNumber}";
    public final static String DEVICE_SN = "iUserRoleDAO-getDeviceAdminUser";
    public final static String DEVICE_SN_PREFIX = "iUserRoleDAO-getDeviceAdminUser::{serialNumber}";
    public final static String[] DEVICE_USER_ROLE_PREFIXES = {DEVICE_ADMIN_ROLE_PREFIX, DEVICE_ADMIN_PREFIX
            , DEVICE_ROLE_PREFIX, DEVICE_ADMIN_COUNTRY_NO_PREFIX, DEVICE_SN_PREFIX};

    // 默认mtu
    public final static int DEVICE_DEFAUlT_MTU = 1480;
    public final static int DEVICE_DEBUG_REPORT_HASH_CODE = 0;

    // 设备当前固件是否是使用wowza上传
    public final static String DEVICE_IS_WOWZA = "device:isWowza:{serialNumber}";
    //有线wifi前缀
    public static final String DEVICE_WIRED_MAC_ADDRESS_PRE = "50:F2:61:";

    public static final String DEVICE_HOME_MODE_SETTING_KEY = "d_h_m_s_{homeId}_{modeId}_{sn}";
    public static final String DEVICE_HOME_MODE_DELAY_KEY = "d_h_m_s_{homeId}_{modeId}";
    public static final String DEVICE_SUPPORT_ALARM_DELAY = "supportAlarmDelay";

    public static final String DEVICE_HOME_ACTIVE_MODE_KEY = "d_h_active_{homeId}";

    public static final String REDIS_KEY_SNAPSHOT_IMAGE_URL = "snapshot:cover:{sn}:{traceId}";
    public static final int REDIS_TIMEOUT_SNAPSHOT_IMAGE_URL = 86400; // 缓存1天<

    public static final Set<Integer> DOORBELL_EVENT = new HashSet<>( Arrays.asList(EReportEvent.DEVICE_CALL.getEventId(),EReportEvent.DOORBELL_PRESS.getEventId(),EReportEvent.DOORBELL_REMOVE.getEventId()));
}
