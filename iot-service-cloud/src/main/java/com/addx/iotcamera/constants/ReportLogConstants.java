package com.addx.iotcamera.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class ReportLogConstants {

    // 定义Report组
    public static String REPORT_GROUP_LIVE = "live";
    public static String REPORT_GROUP_UDP = "udp";
    public static String REPORT_GROUP_BIND = "bind";
    //统计
    public static String REPORT_GROUP_CENSUS = "census";
    public static String REPORT_GROUP_DEVICE_STATUS = "deviceStatus";
    public static String REPORT_GROUP_WAKEUP = "wakeup";
    public static String REPORT_GROUP_REPORT = "report";
    public static String REPORT_GROUP_APP_REPORT = "appReport";
    public static String REPORT_GROUP_MSG = "msg";
    public static String REPORT_GROUP_USERCONFIG = "userconfig";
    public static String REPORT_GROUP_ORDER_PAY = "orderPay";
    public static String REPORT_GROUP_MESSAGE_PUSH = "appMessagePush";
    // 定义reporter
    public static String REPORTER_APP = "app";
    public static String REPORTER_SYS = "sys";
    public static String REPORTER_WOWZA = "wowza";
    public static String REPORTER_DEVICE = "device";

    // 开始直播
    public static String REPORT_TYPE_BEGIN_START_LIVE = "beginStartLive";
    // 获取到wowza直播地址
    public static String REPORT_TYPE_GET_WOWZA_SERVER = "getLiveWowzaServer";
    // 发送消息，等待开启直播
    public static String REPORT_TYPE_START_LIVING_LOOP_SEND = "startLiveLoopSend";
    // 开始直播发生异常
    public static String REPORT_TYPE_START_LIVE_LOOP_EXCEPTION = "startLiveLoopException";
    // 开始直播收到回复
    public static String REPORT_TYPE_START_LIVE_LOOP_GET_ANSWER = "startLiveLoopGetAnswer";
    // 开始直播超时，没有收到回复
    public static String REPORT_TYPE_START_LIVE_LOOP_NO_ANSWER = "startLiveLoopNoAnswer";
    // 开始直播超成功
    public static String REPORT_TYPE_START_LIVE_RETURN = "startLiveReturn";
    // 开始直播失败
    public static String REPORT_TYPE_START_LIVE_FAIL = "startLiveFail";

    // 结束直播
    public static String REPORT_TYPE_STOP_LIVE = "stopLive";

    //收到设备wake request
    public static String REPORT_TYPE_DEVICE_WAKE_REQUEST = "deviceWakeupRequest";

    //收到设备report event
    public static String REPORT_TYPE_DEVICE_REPORT_EVENT = "deviceReportEvent";
    //收到s3通知设备端切片
    public static String REPORT_TYPE_S3_NOTIFY_VIDEO_SLICE = "s3NotifyVideoSlice";
    //设备端视频上传完成
    public static String REPORT_TYPE_VIDEO_UPLOAD_COMPLETE = "videoUploadComplete";
    // 1，从收到设备上报的PIR（运动检测事件）到第一个完整切片完成的时间；
    public static String REPORT_TYPE_PIR_START_TO_FIRST_S3_EVENT = "pirStart2FirstS3Event";
    // 2，从收到设备上报PIR（运动检测事件）到发送第一条给AI分析的消息时间；
    public static String REPORT_TYPE_PIR_START_TO_FIRST_NOTIFY_AI = "pirStart2FirstNotifyAi";
    // 3，从收到设备上报PIR（运动检测事件）到第一条AI分析调用推送消息接口的时间；
    public static String REPORT_TYPE_PIR_START_TO_FIRST_AI_RESULT = "pirStart2FirstAiResult";
    // 4，从收到设备上报PIR（运动检测事件）到第一条消息推送出去的时间；
    public static String REPORT_TYPE_PIR_START_TO_FIRST_PUSH_AI_MSG = "pirStart2FirstPushAIMsg";
    // 5, 每一个收到ai分析结果，都记录通知ai到收到ai结果的耗时
    public static String REPORT_TYPE_NOTIFY_AI_TO_RESULT = "notifyAi2AiResult";
    // 处理设备端切片全过程详细耗时
    public static String REPORT_TYPE_HANDLE_SLICE_COST_TIME = "handleSliceCostTime";

    //收到的切片视频
    public static String REPORT_TYPE_SEGMENT_VIDEO = "segmentVideo";

    //收到wowza推送的完整视频
    public static String REPORT_TYPE_WOWZA_COMPLETE_VIDEO = "wowzaCompleteVideo";

    // 准备发送awake request 唤醒设备
    public static String REPORT_TYPE_PREPARE_SEND_AWAKE_REQUEST = "prepareSendAwakeRequest";

    // 收到awake request 准备之后发送udp包
    public static String REPORT_TYPE_RECEIVED_AWAKE_REQUEST_SEND_LATER = "receivedAwakeRequest-sendLater";

    // 保活设备-准备阶段
    public static String REPORT_TYPE_PREPARE_KEEPALIVE = "prepareKeepalive";
    // 发送消息 保活
    public static String REPORT_TYPE_KEEPALIVE_LOOP_SEND = "keepaliveLoopSend";
    // 收到保活的callback消息
    public static String REPORT_TYPE_KEEPALIVE_LOOP_ANSWER = "keepaliveLoopAnswer";
    // 没有收到保活的回执
    public static String REPORT_TYPE_KEEPALIVE_LOOP_NO_ANSWER = "keepaliveLoopNoAnswer";


    public static String REPORT_TYPE_UDP_RECEIVE_EFFECTIVE_HEARTBEAT = "udpReceiveEffectiveHeartbeat";
    public static String REPORT_TYPE_UDP_REPLY_HEARTBEAT = "udpReplyHeartbeat";

    public static String REPORT_TYPE_GENERATE_BIND_OPERATION = "generateBindOperation";
    public static String REPORT_TYPE_GENERATE_BIND_CODE = "generateBindCode";
    public static String REPORT_TYPE_CHECK_BIND_OPERATION = "checkBindOperation";
    public static String REPORT_TYPE_CHECK_BIND_OPERATION_STEP = "checkBindOperationStep";
    public static String REPORT_TYPE_RECEIVE_BIND_REQUEST_FROM_DEVICE = "receiveBindRequestFromDevice";
    public static String REPORT_TYPE_DEVICE_OFFLINE = "checkDeviceOffline";

    // 设备上报wowza连接问题
    public static String REPORT_TYPE_DEVICE_WOWZA = "wowza";

    // 设备上报连接问题
    public static String REPORT_TYPE_DEVICE_HTTP_CONNECT = "httpConnect";
    public static String REPORT_TYPE_DEVICE_BATTERT = "deviceBatteryCensus";
    public static String REPORT_TYPE_DEVICE_BATTERY = "deviceBatteryEvent";
    // APP收到消息后Push
    public static String REPORT_TYPE_APP_RECEIVE_MSG = "reportAppReceiveMsg";

    // 系统发出推送
    public static String REPORT_TYPE_SYS_PUSH_MSG = "reportPushMsg";

    //更新用户配置
    public static String REPORT_TYPE_UPDATE_USER_CONFIG_START = "reportUpdateUserConfigStart";
    public static String REPORT_TYPE_UPDATE_USER_CONFIG_END = "reportUpdateUserConfigEnd";
    public static String REPORT_TYPE_PAY_PARAM_EROOR = "reportPayParamError";

    // 设备上报异常断线
    public static String DEVICE_REPORT_DISCONNECTED = "device:report:disconnect:{serialNumber}";

    //设备status battery
    public static String REPORT_TYPE_DEVICE_STATUS_BATTERY = "deviceStatusBattery";
    public static String DEVICE_STATUS_BATTERY_KEY = "deviceStatusBattery:{serialNumber}";
    public static int DEVICE_STATUS_BATTERY_EXPIRE = 600;

    public static String REPORT_TYPE_APP_PUSH_END = "appPushMessageLogEnd";
    // 设备ota 步骤上报
    public static String REPORT_TYPE_DEVICE_OTA_STEP = "deviceOtaStepReport";
    // 设备ota start上报
    public static String REPORT_TYPE_DEVICE_OTA_START = "deviceOtaStartReport";
    // 设备ota finish上报
    public static String REPORT_TYPE_DEVICE_OTA_FINISH = "deviceOtaFinishReport";

    public static String REPORT_TYPE_DEVICE_OTA_SUCCESS = "deviceOtaSuccessReport";

    // mqtt请求处理耗时
    public static String REPORT_TYPE_MQTT_REQUEST_COST = "mqttRequestCost";
    // 异步任务监控
    public static String REPORT_TYPE_ASYNC_TASK_MONITOR = "asyncTaskMonitor";
    // 线程池监控
    public static String REPORT_TYPE_THREAD_POOL_MONITOR = "threadPoolMonitor";
    // 线程池关闭监控
    public static String REPORT_TYPE_POOL_SHUTDOWN_MONITOR = "poolShutdownMonitor";

    //设备Pir 结果上报
    public static  String REPORT_TYPE_DEVICE_PIR_RESULT_EVENT_REPORT = "devicePirResultReport";

    // 视频生成-执行任务
    public static  String REPORT_TYPE_EXECUTE_DEVICE_TASKS = "executeDeviceTasks";

    public static  String REPORT_TYPE_CHANGE_TO_BACK_UP = "cloudServiceChanged";
    //标记日志是否需要收集
    public static String REPORT_LOG_NEED_COLLECT = "needCollect";
    public static Set<String> REPORT_LOG_NEED_COLLECT_KEY = new HashSet<>(Arrays.asList(
            "subscription_guide_page_show",
            "vipserver_subscription_topay_click",
            "page_plan_compare_view",
            "search_result_show",
            "dialog_search_result_click",
            "dev_get_iot_device_info",
            "page_connection_type_click",
            "page_name_location_view",
            "dev_add_device_connecting_hidden",
            "search_result_click",
            "dev_connect_device_ap",
            "userAbTestReportType",
            "device_status_report",
            "device_support_report",
            "device_wakeup_report"
    ));
}
