package com.addx.iotcamera.constants;
public class AbTestConstants {
    // 年订阅AB实验组
    public static final String YEAR_TIER_RECOMMEND_PRODUCT = "annual_products";
    public static final String YEAR_TIER_RECOMMEND_BUTTON = "pay_button_copy";
    // 默认选中商品Id
    public static final String YEAR_TIER_RECOMMEND_DEFAULT_PRODUCT = "defaut_selected_product";

    public static final String AWARENESS_FREE_TRAIL = "awareness_7_days_free_trial";

    public static final String ONE_DAY_FREE_TRAIL = "one_day_cloud_storage_free_trial";

    //freelicense abtest实验组，对照组取值
    public static final int EXPERIMENT_GROUP = 0;
    public static final int CONTROL_GROUP = 1;

    public static final String AWARENESS_FREE_TRAIL_MODEL_NO = "CQ425A1-YH";
    public static final String ONE_DAY_FREE_TRAIL_MODEL_NO = "CG625-YH";

    public static final Integer AWARENESS_FREE_LICENSE_ID = 1001;
    public static final Integer ONE_DAY_FREE_LICENSE_ID = 1002;

    // 7天 Freetrial实验
    public static final String AWARENESS_FREE_TRIAL_DAY = "awareness_free_trial_day";
    public static final Integer SEVEN_DAYS_EXPERIMENT_GROUP = 7;
    public static final Integer ZERO_DAYS_EXPERIMENT_GROUP = 0;
    public static final Integer AWARENESS_CONTROL_GROUP = 30;

    //4G实验
    public static final String DATA_PLAN_SKU_OFFER_TEST = "data_plan_sku_offer_test";
    public static final Integer DURATION_ONE_MONTH = 1;
    public static final Integer DURATION_HALF_YEAR = 6;
    public static final Integer DURATION_ONE_YEAR = 12;

}
