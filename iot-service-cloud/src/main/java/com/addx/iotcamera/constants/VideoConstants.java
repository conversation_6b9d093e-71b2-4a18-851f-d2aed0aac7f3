package com.addx.iotcamera.constants;

import com.google.common.collect.ImmutableList;

import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

public class VideoConstants {
    public static final String PATH_VIDEO = "/video";
    public static final String PATH_DOWNLOAD_M3U8 = "/download/m3u8";
    public static final String PATH_VIDEO_DOWNLOAD_M3U8 = PATH_VIDEO + PATH_DOWNLOAD_M3U8;
    public static final String POSTFIX_M3U8 = ".m3u8";

    public static final String CAMERA_LOCAL_ROOT_URL = "https://api-camera.safemo.com";
    public static final String HEADER_SAFE_RTC_ROOT_URL = "safe-rtc-root-url";
    public static final String PARAM_TOKEN = "token";
    public static final String PARAM_SUPPORT_MASTER_PLAYLIST = "supportMasterPlaylist";
    public static final int CAMERA_LOCAL_ROLLING_DAYS = 60; // 摄像头本地视频回看60天
    public static final int CAMERA_LOCAL_EXPIRE_DAYS = (10 * 365) * 24 * 60 * 60;
    public static final int CAMERA_LOCAL_HEAD_SLICE_PERIOD = 2000;
    public static final int CAMERA_LOCAL_TAIL_SLICE_PERIOD = 2000;
    public static final String STORE_SWITCH_IDENTIFIER = "storeSwitch";
    public static final Integer STORE_SWITCH_BIT_INDEX_CAMERA_LOCAL = 0; // 第0位bit表示是否存摄像头本地

    //    public static final String M3U8_CONTENT_TYPE = "audio/mpegurl";
    public static final String M3U8_CONTENT_TYPE = "application/vnd.apple.mpegurl";
    public static final String M3U8_CHARSET = StandardCharsets.UTF_8.displayName();
    public static final String UPLOAD_KEY_HEAD = "device_video_slice";
    public static final String UPLOAD_KEY_PREFIX = UPLOAD_KEY_HEAD + "/${sn}/${traceId}/";
    public static final String UPLOAD_AI_EDGE_HEAD = "ai_edge";
    public static final String UPLOAD_AI_EDGE_PREFIX = UPLOAD_AI_EDGE_HEAD + "/${sn}/${traceId}/";
    public static final String THIRD_UPLOAD_KEY_PREFIX = "${thirdPrefix}/${traceId}/";
    public static final String UPLOAD_KEY_POSTFIX_SLICE = "slice_${period}_${order}_${isLast}.ts";
    public static final String UPLOAD_KEY_POSTFIX_IMAGE = "image.${imgType}";
    public static final String UPLOAD_KEY_POSTFIX_M3U8 = "list.m3u8";
    public static final String VIDEO_BACKUP_PREFIX = "video_backup";
    public static final Pattern PATTERN_VIDEO_SLICE_LAST_NAME = Pattern.compile("^slice_([0-9]+)_([0-9]+)_([01])\\.ts$");
    public static final Pattern PATTERN_IMAGE_LAST_NAME = Pattern.compile("^image\\.\\w+$");
    public static final long MAX_TOTAL_PERIOD = 60 * 3;
    public static final int LIBRARY_TRACE_TIMEOUT = 5 * 60;

    public static final int S3_VIDEO_SLICE_INFO_TIMEOUT = 15 * 60;
    /*** 处理s3切片通知过程中缓存在redis中的数据的键，都在同一个Hash中: s3VideoSliceInfo:${traceId} HASH中 **/
    public static final String S3_VIDEO_SLICE_INFO_PREFIX = "s3VideoSliceInfo:";
    public static final String S3_VIDEO_SLICE_INFO_NUM = "num";
    public static final String S3_VIDEO_SLICE_INFO_IMAGE_URL = "imageUrl";
    public static final String S3_VIDEO_SLICE_INFO_ORDER_PREFIX = "order:";
    public static final String S3_VIDEO_SLICE_INFO_NOTIFY_TIME = "notifyTime:";
    public static final String S3_VIDEO_SLICE_INFO_REPORT_EVENT = "reportEvent:";
    public static final String S3_VIDEO_SLICE_INFO_SERVICE_NAME = "serviceName:"; // s3|gcs
    // 与视频关联的reportEvent集合
    public static final String S3_VIDEO_SLICE_INFO_REPORT_EVENTS = "videoReportEvents:";

    // 以下字段用来做ai分析耗时统计
    public static final String S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME = "mqttEventTime";
    public static final String S3_VIDEO_SLICE_INFO_PIR_START_TIME = "pirStartTime";
    public static final String S3_VIDEO_SLICE_INFO_PIR_END_TIME = "pirEndTime";

    public static final String S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA = "notifyAlexa";
    // pir触发时保存的字段
    public static final ImmutableList<String> S3_VIDEO_SLICE_INFO_PIR_TRIGGER_FIELDS = ImmutableList.of(
            S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME, S3_VIDEO_SLICE_INFO_PIR_START_TIME, S3_VIDEO_SLICE_INFO_PIR_END_TIME);
    // 视频时长更新任务
    public static final String VIDEO_PERIOD_UPDATE_TASK = "videoPeriodUpdateTask";
    // 视频时长更新任务延时。视频录制时长最长为3分钟
    public static final long[] VIDEO_PERIOD_UPDATE_TASK_DELAYS = new long[]{30, 60 * 2, 60 * 4};

    public static final String LIBRARY_ACTIVITY_TRACE_ID = "library_activityZone:{traceId}";
    public static final Integer LIBRARY_ACTIVITY_TRACE_EXPIRE = 15 * 60;

    public final static Integer SECONDS_PER_DAY = 86400;
    public final static Integer THREAD_POOL_SIZE = 5;
    // 设备视频事件信息
    public final static String DEVICE_VIDEO_EVENT = "video:event:{serialNumber}";
    public final static String DEVICE_VIDEO_EVENT_LAST_TIME = "lastTime";
    public final static long DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT = 15 * 1000;
    public final static String DEVICE_VIDEO_EVENT_START_TIME = "startTime";
    public final static long DEVICE_VIDEO_EVENT_START_TIME_LIMIT = 30 * 60 * 1000;

    public final static String LIBRARY_TRACE_TO_VIDEO_EVENT = "traceId:videoEvent:{traceId}";

    public final static String LIBRARY_VIDEO_EVENT_TO_TAG = "videoEvent:tag:{videoEvent}";

    //视频事件信息
    public final static long LIBRARY_VIDEO_EVENT_EXPIRE = 30 * 60;
    //视频事件tag信息
    public final static int LIBRARY_VIDEO_EVENT_TAG_EXPIRE = 40 * 60;


    public final static String LIBRARY_EVENT_OBJECT_DESCRIBE = "ai";

    //通知渠道
    public final static String PUSH_CHANNEL_DEVICE_REMINDER = "{tenantId}_pir_id";
    public final static String PUSH_CHANNEL_VOIP = "{tenantId}_doorbell_ring_id";

    public final static String VIDEO_HOME_ALARM = "d:alarm:{sn}";

    public final static int ALARM_DELAY_MARK = 24 * 60 * 60;
}
