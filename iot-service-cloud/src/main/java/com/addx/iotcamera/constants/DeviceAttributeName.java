package com.addx.iotcamera.constants;

import com.addx.iotcamera.bean.device.attributes.DeviceAttributeType;
import com.addx.iotcamera.util.FuncUtil;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Set;

import static com.addx.iotcamera.bean.device.attributes.DeviceAttributeType.*;

@RequiredArgsConstructor
public enum DeviceAttributeName {

    pirSwitch(SWITCH), /* 1.运动检测-总开关*/
    pirSensitivity(ENUM), /* 2.运动检测-灵敏度*/
    pirRecordTime(ENUM), /* 3.运动检测-录制时长*/
    pirCooldownSwitch(SWITCH), /* 4.运动检测-触发间隔开关*/
    pirCooldownTime(ENUM), /* 5.运动检测-触发间隔时间*/
    timedDormancySwitch(SWITCH), /* 6.定时休眠-总开关*/
    motionTrackingSwitch(SWITCH), /* 7.运动追踪-总开关*/
    videoResolution(ENUM), /* 8.视频-分辨率*/
    videoFlipSwitch(SWITCH), /* 9.视频-翻转开关*/
    videoAntiFlickerSwitch(SWITCH), /* 10.视频-抗频闪开关*/
    videoAntiFlickerFrequency(ENUM), /* 11.视频-抗频闪频率*/
    liveStreamCodec(ENUM), /* 12.音视频流联动(直播流编码)*/
    location(OBJECT_ENUM), /* 13.设备所在位置*/
    home(OBJECT_ENUM), /* .设备所在home*/
    deviceName(TEXT), /* 14.设备名称*/
    timeZone(TEXT), /* 15.时区*/
    doorbellPressNotifySwitch(SWITCH), /* 16.按铃通知开关*/
    doorbellPressNotifyType(ENUM), /* 17.按铃通知方式*/

    /*** 设备设置通用化改造二期 开始 */

    deviceCallNotifySwitch(SWITCH), /* 18.一键呼叫-一键呼叫开关*/
    mechanicalDingDongSwitch(SWITCH), /* 19.响铃设置-机械响铃开关*/
    mechanicalDingDongDuration(INT_RANGE), /* 20.响铃设置-响铃时长*/
    motionAlertSwitch(SWITCH), /* 21.报警设置-运动触发报警*/
    antiDisassemblyAlarmSwitch(SWITCH), /* 22.报警设置-防拆报警开关*/
    alarmFlashLightSwitch(SWITCH), /* 23.报警设置-报警闪光灯开关*/
    recLampSwitch(SWITCH), /* 24.灯光设置-指示灯开关*/
    nightVisionSwitch(SWITCH), /* 25.灯光设置-夜视开关控制*/
    nightVisionSensitivity(ENUM), /* 26.灯光设置-夜视灵敏度*/
    nightVisionMode(ENUM), /* 27.灯光设置-夜视模式调节*/
    alarmDuration(ENUM), /* 28.声音设置-报警时长*/
    alarmVolume(INT_RANGE), /* 29.声音设置-警铃音量*/
    voiceLanguage(ENUM), /* 30.声音设置-设备语音语种*/
    doorBellRing(ENUM), /* 31.声音设置-门铃铃音选择*/
    voiceVolume(INT_RANGE), /* 32.声音设置-提示音音量*/
    liveAudioSwitch(SWITCH), /* 33.声音设置-直播收音开关*/
    recordingAudioSwitch(SWITCH), /* 34.声音设置-录像收音开关*/

    /*** 设备设置通用化改造二期 结束 */
    powerSource(ENUM), /* 35.供电方式。选项在后端*/
    chargeAutoPowerOnSwitch(SWITCH), /* 36.充电自动开机开关，默认关*/
    chargeAutoPowerOnCapacity(ENUM), /* 37.充电自动开机电量，默认25*/
    liveSpeakerVolume(INT_RANGE), /* 38.声音设置-直播对讲音量*/

    pirAi(CHECKBOX), /** 38. pir偏好AI检测  */

    sdCardPirRecordTime(ENUM), /* sd卡 运动检测-录制时长*/

    sdCardCooldownSwitch(SWITCH),
    sdCardVideoModes(ENUM),
    sdCardCooldownSeconds(ENUM),

    /** 泛光灯相关start */
    motionTriggeredFloodlightSwitch(SWITCH), // 泛光灯运动检测开关
    motionFloodlightTimer(ENUM),  // 泛光灯运动检测事件
    floodlightScheduleSwitch(SWITCH), // 泛光灯定时计划开关
    floodlightSchedulePlan(TEXT),  // 泛光灯定时计划内容

    floodlightMode(ENUM),
    otaAutoUpgrade(SWITCH),

    /** 泛光灯相关end */
    video12HourSwitch(SWITCH),
    propertyJson(SWITCH),  /*  只是占个summary位置，新增的设置都在这里 */
    alarmDelay(ENUM),
    leavingDelay(ENUM),
    pushIgnoredSwitch(SWITCH) //pir通知
    ;

    @Getter
    private final DeviceAttributeType type;

    private static final ImmutableMap<String, DeviceAttributeName> name2Enum = FuncUtil.createImmutableMap(DeviceAttributeName.values(), it -> it.name());

    public static DeviceAttributeName nameOf(String name) {
        return name2Enum.get(name);
    }

    public static boolean containsName(String name) {
        return name2Enum.containsKey(name);
    }

    public static Set<String> names() {
        return name2Enum.keySet();
    }
}
