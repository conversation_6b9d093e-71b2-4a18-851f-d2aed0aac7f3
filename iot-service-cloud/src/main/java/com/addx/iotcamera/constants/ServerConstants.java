package com.addx.iotcamera.constants;

import com.addx.iotcamera.util.OpenApiUtil;

public class ServerConstants {
    public static final String SERVER_NODE_CN = "CN";
    public static final String SERVER_NODE_US = "US";
    public static final String SERVER_NODE_EU = "EU";

    // redis cache 默认过期时间
    public static final int REDIS_CACHE_DEFAULT_EXPIRE_TIME = 60 * 60;

    public static final String SERVER_ID = OpenApiUtil.shortUUID(); // 每个实例生成一个唯一ID
}
