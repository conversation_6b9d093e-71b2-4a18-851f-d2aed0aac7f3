package com.addx.iotcamera.dao;


import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.MsgResponseDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface IDeviceDAO {
    @Insert("REPLACE INTO `camera`.`device` " +
            "(`serial_number`, " +
            "`location_id`, " +
            "`home_id`, " +
            "`device_name`, " +
            "`dormancy_plan_switch`," +
            "`activated_time`, " +
            "`activated`, " +
            "`ota_ignored`, " +
            "`push_ignored`, " +
            "`firmware_id`) " +
            "VALUES " +
            "( #{serialNumber}, " +
            " #{locationId}, " +
            " #{homeId}, " +
            " #{deviceName}, " +
            " #{dormancyPlanSwitch}, " +
            " UNIX_TIMESTAMP(), " +
            " 1, " +
            " 0, " +
            " 0, " +
            " #{firmwareId}); ")
    Integer activateDevice(DeviceDO device);

    @Update({"<script>UPDATE `camera`.`device` " +
            "SET " +
            "<if test = 'locationId != null'>`location_id` = #{locationId}, </if>" +
            "<if test = 'homeId != null'>`home_id` = #{homeId}, </if>" +
            "<if test = 'deviceName != null'>`device_name` = #{deviceName}, </if>" +
            "<if test = 'activatedTime != null'>`activated_time` = #{activatedTime}, </if>" +
            "<if test = 'activated != null'>`activated` = #{activated}, </if>" +
            "<if test = 'firmwareId != null'>`firmware_id` = #{firmwareId}, </if>" +
            "<if test = 'otaIgnored != null'>`ota_ignored` = #{otaIgnored}, </if>" +
            "<if test = 'pushIgnored != null'>`push_ignored` = #{pushIgnored}, </if>" +
            "<if test = 'personDetect != null'>`person_detect` = #{personDetect}, </if>" +
            "<if test = 'recResolution != null'>`rec_resolution` = #{recResolution}, </if>" +
            "<if test = 'otaOnAwake != null'>`ota_on_awake` = #{otaOnAwake}, </if>" +
            "<if test = 'displayModelNo != null'>`display_model_no` = #{displayModelNo}, </if>" +
            "<if test = 'packagePush != null'>`package_push` = #{packagePush}, </if>" +
            "<if test = 'dormancyPlanSwitch != null'>`dormancy_plan_switch` = #{dormancyPlanSwitch}, </if>" +
            "`serial_number` = #{serialNumber}  " +
            " WHERE `serial_number` = #{serialNumber} </script>"})
    Integer updateDeviceInfo(DeviceDO deviceDO);


    @Select("SELECT  * FROM  `camera`.`device` WHERE `serial_number` = #{serialNumber};")
    DeviceDO getAllDeviceInfo(String serialNumber);

    @Update("UPDATE `camera`.`device` " +
            "SET " +
            "`firmware_id` = #{firmwareId} " +
            "WHERE `serial_number` = #{serialNumber};")
    Integer updateFirmwareVersionBySerialNumber(@Param("serialNumber") String serialNumber, @Param("firmwareId") String firmwareId);

    @Select("select firmware_id from `camera`.`device` WHERE `serial_number` = #{serialNumber};")
    @DS("camera-readonly")
    String queryFirmwareVersionBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 查出来的每一项是用户维度的数据
     * todo 连了4个表，应该修改
     */
    @Select("SELECT  " +
            "    u.`id` AS `userId`, " +
            "    p.`msg_type`, " +
            "    p.`msg_token`, " +
            "    p.`app_type`, " +
            "    p.`bundle_name`, " +
            "    d.`serial_number`, " +
            "    d.`device_name`, " +
            "    l.`id` AS `locationId`, " +
            "    l.`location_name` " +
            "FROM " +
            "    `camera`.`user` u " +
            "        INNER JOIN " +
            "    `camera`.`user_role` r ON r.`user_id` = u.`id` " +
            "        INNER JOIN " +
            "    `camera`.`device` d ON r.`serial_number` = d.`serial_number` " +
            "        INNER JOIN " +
            "   `camera`.`push_info` p ON p.`user_id` = u.`id`" +
            "        INNER JOIN " +
            "    `camera`.`location` l ON l.`id` = d.`location_id`" +
            "WHERE " +
            "    d.`serial_number` = #{serialNumber}")
    List<MsgResponseDO> getPushDetailBySerialNumber(String serialNumber);

    @Insert("INSERT INTO `camera`.`bind_operation` " +
            "(`operation_id`, " +
            "`user_id`, " +
            "`location_id`, " +
            "`home_id`, " +
            "`device_language`, " +
            "`time_zone`, " +
            "`request_time`, " +
            "`answered`, " +
            "`ip`, " +
            "`bind_code`, " +
            "`tenant_id`, " +
            "`app_type`, " +
            "`version`, " +
            "`bind_content_src`, " +
            "`device_net_type`, " +
            "`status_code`,`bind_code_timestamp`,bind_type) " +
            "VALUES " +
            "(#{operationId} , " +
            "#{userId} , " +
            "#{locationId} , " +
            "#{homeId} , " +
            "#{deviceLanguage} , " +
            "#{timeZone} , " +
            "UNIX_TIMESTAMP() , " +
            "0 , " +
            "#{ip}, " +
            "#{bindCode}, " +
            "#{tenantId}, " +
            "#{appType}, " +
            "#{version}, " +
            "#{bindContentSrc}, " +
            "#{deviceNetType}, " +
            "0 ,#{bindCodeTimestamp},#{bindType}); ")
    Integer initBindOperation(BindOperationTb operationDO);

    @Select("SELECT `bind_operation`.`operation_id`, " +
            "    `bind_operation`.`user_id`, " +
            "    `bind_operation`.`bind_code`, " +
            "    `bind_operation`.`location_id`, " +
            "    `bind_operation`.`home_id`, " +
            "    `bind_operation`.`device_language`, " +
            "    `bind_operation`.`time_zone`, " +
            "    `bind_operation`.`request_time`, " +
            "    `bind_operation`.`answered`, " +
            "    `bind_operation`.`status_code`, " +
            "    `bind_operation`.`ip`, " +
            "    `bind_operation`.`tenant_id`, " +
            "    `bind_operation`.`serial_number`, " +
            "    `bind_operation`.`bind_content_src`, " +
            "    `bind_operation`.`device_net_type` " +
            "FROM `camera`.`bind_operation` " +
            "WHERE `operation_id` = #{operationId}; ")
    BindOperationTb selectOperation(String operationId);

    @Update({"<script>UPDATE `camera`.`bind_operation` " +
            "SET " +
            "`operation_id` = #{operationId} , " +
            "`answered` = #{answered} , " +
            "`status_code` = #{statusCode}, " +
            "`serial_number` = #{serialNumber} " +
            "<if test = 'bindRequestApproved != null'>,`bind_request_approved` = #{bindRequestApproved} </if>" +
            "<if test = 'bindRequestApprovedTimestamp != null'>,`bind_request_approved_timestamp` = #{bindRequestApprovedTimestamp} </if>" +
            "WHERE `operation_id` = #{operationId}; </script>"})
    Integer updateBindOperation(BindOperationTb operationTb);

    @Update({"<script>UPDATE `camera`.`bind_operation` " +
            "SET " +
            "`operation_id` = #{operationId} " +
            "<if test = 'bindCodeTimestamp != null'>,`bind_code_timestamp` = #{bindCodeTimestamp}</if>" +
            "<if test = 'deviceHttpRequest != null'>,`device_http_request` = #{deviceHttpRequest}</if> " +
            "<if test = 'deviceHttpRequestTimestamp != null'>,`device_http_request_timestamp` = #{deviceHttpRequestTimestamp}</if> " +
            "<if test = 'deviceMqttRequest != null'>,`device_mqtt_request` = #{deviceMqttRequest}</if>" +
            "<if test = 'deviceMqttRequestTimestamp != null'>,`device_mqtt_request_timestamp` = #{deviceMqttRequestTimestamp}</if> " +

            "<if test = 'bindRequestComplete != null'>,`bind_request_complete` = #{bindRequestComplete}</if>" +
            "<if test = 'bindRequestCompleteTimestamp != null'>,`bind_request_complete_timestamp` = #{bindRequestCompleteTimestamp}</if> " +

            "<if test = 'appRequestBindComplete != null'>,`app_request_bind_complete` = #{appRequestBindComplete}</if> " +
            "<if test = 'appRequestBindCompleteTimestamp != null'>,`app_request_bind_complete_timestamp` = #{appRequestBindCompleteTimestamp}</if>" +

            "<if test = 'appBindStepWifi != null'>,`app_bind_step_wifi` = #{appBindStepWifi}</if> " +
            "<if test = 'appBindStepWifiTimestamp != null'>,`app_bind_step_wifi_timestamp` = #{appBindStepWifiTimestamp}</if>" +
            "<if test = 'appBindStepHttp != null'>,`app_bind_step_http` = #{appBindStepHttp}</if> " +
            "<if test = 'appBindStepHttpTimestamp != null'>,`app_bind_step_http_timestamp` = #{appBindStepHttpTimestamp}</if>" +
            "<if test = 'appBindStepApproved != null'>,`app_bind_step_approved` = #{appBindStepApproved}</if> " +
            "<if test = 'appBindStepApprovedTimestamp != null'>,`app_bind_step_approved_timestamp` = #{appBindStepApprovedTimestamp}</if>" +
            "<if test = 'appBindStepComplete != null'>,`app_bind_step_complete` = #{appBindStepComplete}</if> " +
            "<if test = 'appBindStepCompleteTimestamp != null'>,`app_bind_step_complete_timestamp` = #{appBindStepCompleteTimestamp}</if>" +

            "WHERE `operation_id` = #{operationId}; </script>"})
    Integer updateBindOperationBindInfo(BindOperationTb operationTb);


    @Update("UPDATE `camera`.`bind_operation` " +
            "SET " +
            "`is_scan_user_sn` = #{isScanUserSn}, " +
            "`user_sn` = #{userSn}, " +
            "`model_no` = #{modelNo} " +
            "WHERE `bind_code` = #{bindCode};")
    Integer updateBindOperationScanUserSn(BindOperationTb operationTb);

    @Select({"<script>select serial_number,location_id,device_name,activated_time,person_detect,activated,firmware_id,ota_ignored,push_ignored,rec_resolution,home_id from `camera`.`device` where serial_number in " +
            "<foreach collection='serialNumbers' item='serialNumber' open='(' separator=',' close=')'>" +
            "#{serialNumber}" +
            "</foreach></script>"})
    List<DeviceDO> queryDeviceBynSerialNumbers(@Param("serialNumbers") List<String> serialNumbers);


    @Select("select serial_number,location_id,device_name,activated_time,person_detect,activated,firmware_id,ota_ignored,push_ignored,rec_resolution,home_id from `camera`.`device` where location_id = #{locationId}")
    List<DeviceDO> queryDeviceByLocation(@Param("locationId") Integer locationId);

    @Select("select * from `camera`.`bind_operation` where user_id = #{userId} order by request_time desc")
    List<BindOperationTb> queryBindHistory(@Param("userId") int userId);

    @Select("select * from `camera`.`bind_operation` where user_id = #{userId} and bind_request_complete=1 order by request_time desc limit 1")
    @DS("camera-readonly")
    BindOperationTb queryBindHistoryCompleted(@Param("userId") int userId);

    @Select("select * from `camera`.`bind_operation` where serial_number = #{serialNumber} order by request_time desc")
    @DS("camera-readonly")
    List<BindOperationTb> queryBindHistoryBySn(@Param("serialNumber") String serialNumber);

}

