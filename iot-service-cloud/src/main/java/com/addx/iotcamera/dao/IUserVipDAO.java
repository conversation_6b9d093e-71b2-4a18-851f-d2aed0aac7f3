package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public interface IUserVipDAO {

    @Select({"<script>select vip.id,vip.user_id,vip.tier_id,vip.effective_time,vip.end_time,vip.cdate," +
            "vip.order_id,vip.trade_no,vip.active,vip.rolling_day,vip.tier_service_type " +
            " from camera.user_vip vip " +
            "left join camera.tier t on vip.tier_id = t.tier_id " +
            " where vip.user_id = #{userId} and vip.end_time > #{currentTime}" +
            "<if test='tierServiceType != null'> and t.tier_service_type = #{tierServiceType} </if>" +
            " order by t.tier_type desc,t.level desc,t.max_device_num desc,vip.id asc " +
            "</script>"})
    List<UserVipDO> queryUserVipInfo(@Param("userId") Integer userId,
                                     @Param("currentTime") Integer currentTime,
                                     @Param("tierServiceType") Integer tierServiceType);


    @Select({"<script>select vip.id,vip.user_id,vip.tier_id,vip.effective_time,vip.end_time,vip.cdate," +
            "vip.order_id,vip.trade_no,vip.active,vip.rolling_day,vip.tier_service_type " +
            " from camera.user_vip vip " +
            "left join camera.tier t on vip.tier_id = t.tier_id " +
            " where vip.user_id in  " +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' > #{userId} </foreach>" +
            "and vip.end_time > #{currentTime}" +
            " order by t.tier_type desc,t.level desc,t.max_device_num desc,vip.id asc " +
            "</script>"})
    @DS("camera-readonly")
    List<UserVipDO> queryUserVipInfoBatch(@Param("userIds") List<Integer> userIds,
                                     @Param("currentTime") Integer currentTime);

    @Select({"<script>select count(*) from camera.user_vip " +
            " where user_id in  <foreach collection='userIds' item='id'  open='(' separator=',' close=')' >" +
            "  #{id}" +
            " </foreach> AND order_id !=0 and tier_service_type = #{tierServiceType} " +
            "</script>"})
    @DS("camera-readonly")
    int queryUserVipInfoByUserIds(@Param("userIds") Set<Integer> userIds,@Param("tierServiceType") Integer tierServiceType);

    @Select({"<script>select count(*) from camera.user_vip " +
            " where user_id =#{userId} AND order_id !=0 and tier_service_type = #{tierServiceType} ",
            "</script>"})
    @DS("camera-readonly")
    int queryUserVipInfoByUserId(@Param("userId") Integer userId, @Param("tierServiceType") Integer tierServiceType);

    // queryCurrentTierTime tier protection
    @Select("select id,user_id,tier_id,effective_time,end_time from camera.user_vip " +
            "where user_id = #{userId} and end_time < #{currentTime} and order_id !=0 and tier_service_type = 0 " +
            "order by end_time DESC,tier_id desc limit 1")
    UserVipDO queryUserVipInfoBefor(@Param("userId") Integer userId,
                                    @Param("currentTime") Integer currentTime);


    @Select("<script>select id,user_id,tier_id,effective_time,end_time,order_id,rolling_day " +
            "from camera.user_vip where user_id = #{userId} and order_id !=0 " +
            "<if test='tierServiceType!=null'> and tier_service_type = #{tierServiceType} </if>" +
            "order by end_time desc limit 1" +
            "</script>")
    UserVipDO queryLastUserVipInfo(@Param("userId") Integer userId,
                                   @Param("tierServiceType") Integer tierServiceType);


    @Insert("insert into camera.user_vip (user_id,tier_id,effective_time,end_time,cdate,order_id,trade_no,rolling_day,tier_service_type) values (" +
            "#{userId},#{tierId},#{effectiveTime},#{endTime},#{cdate},#{orderId},#{tradeNo},#{rollingDay},#{tierServiceType})")
    int insertUserVip(UserVipDO userVipDO);


    @Update("update camera.user_vip set effective_time=#{effectiveTime},end_time=#{endTime} where id = #{id}")
    int updateUserVipTime(UserVipDO userVipDO);

    @Update("update camera.user_vip set active=#{active} where id = #{id}")
    int updateUserVipActive(UserVipDO userVipDO);

    // payment is/is not renewal order
    @Select("select order_id from camera.user_vip where user_id = #{userId} and end_time > UNIX_TIMESTAMP() and order_id != 0")
    List<Long> queryUserOrderId(@Param("userId") Integer userId);

    @Select("<script>" +
            " select ifnull(max(end_time),0)  from camera.user_vip where user_id=#{userId} and tier_service_type = #{tierServiceType} " +
            " <if test='tierId != null'> AND tier_id = #{tierId} </if> " +
            "</script>")
    @DS("camera-readonly")
    int userVipEndTime(@Param("userId") Integer userId, @Param("tierId") Integer tierId,
                       @Param("tierServiceType") Integer tierServiceType);

    @Update("update camera.user_vip set active = 0 where user_id=#{userId} " +
            "and (end_time < UNIX_TIMESTAMP() or effective_time>UNIX_TIMESTAMP()) and active=1")
    int updateActiveExpire(@Param("userId") Integer userId);

    @Select("select * from camera.user_vip  where id = #{id}")
    @DS("camera-readonly")
    UserVipDO queryUserVipById(@Param("id") Long id);

    @Update("update camera.user_vip set rolling_day = #{rollingDay} where id = #{id}")
    int updateUserVipRollingDay(@Param("id") Long id,@Param("userId") Integer userId, @Param("rollingDay") Integer rollingDay);


    @Select("select * from camera.user_vip  where user_id = #{userId} and tier_id = #{tierId}" +
            " and end_time > UNIX_TIMESTAMP() and effective_time <= UNIX_TIMESTAMP() " +
            "order by id asc limit 1")
    UserVipDO queryUserVipByActiveTierId(@Param("userId") Integer userId,@Param("tierId") Integer tierId);
}
