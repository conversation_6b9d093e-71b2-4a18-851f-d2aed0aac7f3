package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.domain.Tier;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
@DS("camera-readonly")
public interface TierDao {

    @Select("SELECT `tier`.`tier_id`, " +
            "    `tier`.`rolling_days`, " +
            "    `tier`.`size`, " +
            "    `tier`.`level`, " +
            "    `tier`.`storage`, " +
            "    `tier`.`max_device_num`, " +
            "    `tier`.`tier_type`, " +
            "    `tier`.`tier_service_type`, " +
            "    `tier`.`tier_group_id` " +
            "FROM `camera`.`tier` " +
            "WHERE `tier_id`=#{tierId}; ")
    Tier getTierByTierId(int tierId);

    @Select({"<script>SELECT `tier`.`tier_id`, " +
            "    `tier`.`rolling_days`, " +
            "    `tier`.`size`, " +
            "    `tier`.`level`, " +
            "    `tier`.`storage`, " +
            "    `tier`.`max_device_num`, " +
            "    `tier`.`tier_type`, " +
            "    `tier`.`tier_service_type`, " +
            "    `tier`.`tier_group_id` " +
            "FROM `camera`.`tier` " +
            "WHERE `tier_id` in <foreach collection='tierIds' item='id'  open='(' separator=',' close=')' >" +
            "  #{id}" +
            " </foreach> </script>"})
    List<Tier> getTierByTierIds(@Param("tierIds") Collection<Integer> tierIds);

    @Select({"SELECT `tier`.`tier_id`, " +
            "    `tier`.`rolling_days`, " +
            "    `tier`.`size`, " +
            "    `tier`.`level`, " +
            "    `tier`.`storage`, " +
            "    `tier`.`max_device_num`, " +
            "    `tier`.`tier_type`, " +
            "    `tier`.`tier_group_id`,tier_type,tier_service_type,tenant_id " +
            "FROM `camera`.`tier` " +
            "WHERE tier_service_type = #{tierServiceType} and tenant_id = #{tenantId}"})
    List<Tier> getTierByTierServiceType(@Param("tierServiceType") Integer tierServiceType,
                                @Param("tenantId") String tenantId);
}
