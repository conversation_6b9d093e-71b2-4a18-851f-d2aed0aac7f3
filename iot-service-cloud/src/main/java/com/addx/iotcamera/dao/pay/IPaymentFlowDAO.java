package com.addx.iotcamera.dao.pay;

import com.addx.iotcamera.bean.db.PaymentFlow;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface IPaymentFlowDAO {
    @Insert("INSERT INTO camera.payment_flow (out_trade_no,trade_no, type, user_id,product_id, product_subject, product_body, amount," +
            "currency,time_start,time_end,status,trade_type,extend,tenantId,cdate,mdate,app_version_name,serve_version,free_trial," +
            "purchase_time,purchase_date_pst,purchase_date,expire_time,order_info,order_info_v2) " +
            "VALUES (#{outTradeNo}, #{tradeNo},#{type}, #{userId},#{productId}, #{productSubject}, #{productBody}, #{amount}," +
            " #{currency},#{timeStart},#{timeEnd},#{status},#{tradeType},#{extend},#{tenantId},UNIX_TIMESTAMP(),UNIX_TIMESTAMP()," +
            "#{appVersionName},#{serveVersion},#{freeTrial},#{purchaseTime},#{purchaseDatePst},#{purchaseDate},#{expireTime},#{orderInfo},#{orderInfoV2})")
    @SelectKey(before = false, keyProperty = "id", resultType = Long.class, statementType = StatementType.STATEMENT, statement = "SELECT LAST_INSERT_ID() AS id;")
    Long insertPayment(PaymentFlow paymentFlow);

    /**
     * 未支付完成的流水
     *
     * @return
     */
    @Select("select id,out_trade_no,product_id,type,product_subject,product_body,amount,currency,status,extend" +
            " from camera.payment_flow where cdate between #{timeStart} and #{timeEnd} and  status = 1 and type = #{type}")
    List<PaymentFlow> queryPayListOrderList(@Param("timeStart") Integer timeStart,
                                            @Param("timeEnd") Integer timeEnd,
                                            @Param("type") Integer type);

    /**
     * 支付完成的流水
     *
     * @return
     */
    @Select("select id,out_trade_no,trade_no,product_id,type,product_subject,product_body,amount,currency,status,extend," +
            "free_trial,refund,refund_time,refund_reason,time_start,time_end,user_id,expire_time " +
            " from camera.payment_flow where  trade_no =#{tradeNo} limit 1")
    PaymentFlow queryPaymentFlow(@Param("tradeNo") String tradeNo);

    /**
     * 支付完成的流水
     *
     * @return
     */
    @Select("select id,out_trade_no,trade_no,product_id,type,product_subject,product_body,amount,currency,status,extend," +
            "free_trial,refund,refund_time,refund_reason,time_start,time_end,user_id,expire_time " +
            " from camera.payment_flow where  trade_no like '${tradeNo}%' order by id desc limit 1")
    PaymentFlow queryPaymentFlowLast(@Param("tradeNo") String tradeNo);

    /**
     * 支付完成的流水数量
     *
     * @return
     */
    @Select("select count(*) from camera.payment_flow where out_trade_no=#{outTradeNo} ")
    int queryPaymentFlowCount(@Param("outTradeNo") String outTradeNo);

    /**
     * 支付完成的流水数量
     *
     * @return
     */
    @Select("select count(*) from camera.payment_flow where user_id =#{userId} and  trade_no!=#{tradeNo} ")
    int queryPaymentFlowCountExclusion(@Param("userId") Integer userId,@Param("tradeNo") String tradeNo);

    /**
     * 支付完成的流水列表
     *
     * @return
     */
    @Select("select * from camera.payment_flow where cdate between #{timeStart} and #{timeEnd} ")
    List<PaymentFlow> queryPaymentFlowList(@Param("timeStart") int timeStart, @Param("timeEnd") int timeEnd);

    @Select("<script>select trade_no from camera.payment_flow where trade_no in " +
            " <foreach collection='tradeNos' item='tradeNo'  open='(' separator=',' close=')' > #{tradeNo} </foreach>" +
            "</script>")
    @DS("camera-readonly")
    Set<String> queryPaymentFlowExistBatch(@Param("tradeNos") Set<String> tradeNos);

    @Update("<script>update camera.payment_flow set " +
            "id = id " +
            "<if test='refund!=null'>,refund = #{refund} </if>" +
            "<if test='refundTime!=null'>,refund_time=#{refundTime} </if>" +
            "<if test='refundReason!=null'>,refund_reason=#{refundReason} </if>" +
            "<if test='freeTrial!=null'>,free_trial=#{freeTrial} </if>" +
            "<if test='refundAppVersionName!=null'>,refund_app_version_name=#{refundAppVersionName} </if>" +
            "<if test='refundServeVersion!=null'>,refund_serve_version=#{refundServeVersion} </if>" +
            " where id = #{id}</script>")
    int updateRefundOrderInfo(PaymentFlow paymentFlow);


    @Select("<script>select id,trade_no,refund,user_id,product_id from camera.payment_flow where trade_no in " +
            " <foreach collection='tradeNos' item='tradeNo'  open='(' separator=',' close=')' > #{tradeNo} </foreach>" +
            " and refund = 0</script>")
    @DS("camera-readonly")
    List<PaymentFlow> queryPaymentFlowNoRefundBatch(@Param("tradeNos") Set<String> tradeNos);


    @Select("select ifnull(max(cdate),0) from camera.payment_flow where out_trade_no= #{outTradeNo}")
    @DS("camera-readonly")
    Integer queryLastPaymentTime(@Param("outTradeNo") String outTradeNo);


    @Select("<script>select id,out_trade_no,trade_no,product_id,type,product_subject,product_body,amount,currency,status," +
            "free_trial,refund,refund_time,refund_reason,time_start,time_end,user_id,expire_time,extend " +
            " from camera.payment_flow where  trade_no in " +
            "<foreach collection='tradeNos' item='tradeNo'  open='(' separator=',' close=')' > #{tradeNo} </foreach>" +
            " </script>")
    List<PaymentFlow> queryPaymentFlowBatch(@Param("tradeNos") List<String> tradeNos);


    @Update("update camera.payment_flow set expire_time =#{expireTime} where trade_no = #{tradeNo}")
    int updatePaymentFlow(@Param("tradeNo") String tradeNo,@Param("expireTime")Integer expireTime);
}
