package com.addx.iotcamera.dao.device;

import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.response.device.ModelFirmwareUpgradeCount;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IDeviceManualDAO {

    /**
     * 添加或者更新设备信息
     *
     * @param deviceManual
     */
    @Insert("INSERT INTO camera.device_manual (" +
            "serial_number, user_sn, model_no, original_model_no, " +
            "display_model_no, mac_address, mcu_number, status) " +
            "VALUES (" +
            "#{serialNumber}, #{userSn}, #{modelNo}, #{originalModelNo}, " +
            "#{displayModelNo}, #{macAddress}, #{mcuNumber}, 0) " +
            "ON DUPLICATE KEY UPDATE " +
            "serial_number = #{serialNumber}, " +
            "user_sn = #{userSn}, " +
            "model_no = #{modelNo}, " +
            "original_model_no = #{originalModelNo}, " +
            "display_model_no = #{displayModelNo}, " +
            "mac_address = #{macAddress}, " +
            "mcu_number = #{mcuNumber}")
    int addDeviceManual(DeviceManualDO deviceManual);


    /**
     * 通过serialNumber获取modelNo
     *
     * @param serialNumber
     * @return
     */
    @Select("SELECT model_no from camera.device_manual WHERE serial_number = #{serialNumber}")
    @DS("camera-readonly")
    String getModelNoBySerialNumber(@Param("serialNumber") String serialNumber);

    @Select("SELECT original_model_no from camera.device_manual WHERE serial_number = #{serialNumber}")
    @DS("camera-readonly")
    String getOriginalModelNoBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 通过userSn获取modelNo
     *
     * @param userSn
     * @return
     */
    @Select("SELECT serial_number,model_no,mac_address from camera.device_manual WHERE user_sn = #{userSn}")
    DeviceManualDO getModelNoByUserSn(@Param("userSn") String userSn);

    /**
     * 获取设备型号信息
     *
     * @param serialNumber
     * @return
     */
    @Select("SELECT * from camera.device_manual WHERE serial_number = #{serialNumber}")
    @DS("camera-readonly")
    DeviceManualDO getDeviceManualBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 更新mcu版本
     *
     * @param serialNumber
     * @param mcuNumber
     * @return
     */
    @Update("UPDATE camera.device_manual SET mcu_number = #{mcuNumber} WHERE serial_number = #{serialNumber}")
    void updateMcuVersionBySerialNumber(@Param("serialNumber") String serialNumber,
                                        @Param("mcuNumber") String mcuNumber);

    /**
     * 查询mcu版本
     *
     * @param serialNumber
     * @return
     */
    @Select("select mcu_number from camera.device_manual WHERE serial_number = #{serialNumber}")
    @DS("camera-readonly")
    String queryMcuVersionBySerialNumber(@Param("serialNumber") String serialNumber);



    /**
     * 通过userSN获取serialNumber
     *
     * @param userSn
     * @return
     */
    @Select("SELECT serial_number FROM camera.device_manual WHERE user_sn = #{userSn}")
    @DS("camera-readonly")
    String getSerialNumberByUserSn(@Param("userSn") String userSn);


    @Select({"<script>select dm.model_no,sum(case firmware_id when #{firmwareId} THEN 1 else 0 END) as count ,count(*) as total  from camera.device_manual dm " +
            "LEFT JOIN camera.device d on dm.serial_number = d.serial_number " +
            "where dm.model_no in " +
            "<foreach collection='modelNos' item='modelNo' open='(' separator=',' close=')'>" +
            "#{modelNo}" +
            "</foreach>" +
            "and d.activated =1 " +
            "GROUP BY dm.model_no </script>"})
    @DS("camera-readonly")
    List<ModelFirmwareUpgradeCount> queryModelFirmwareUpgradeCount(@Param("modelNos") List<String> modelNos,
                                                                   @Param("firmwareId") String firmwareId);

    @Select("select serial_number,model_no,original_model_no from camera.device_manual limit ${offset},${maxNum};")
    List<DeviceManualDO> batchQueryDeviceManualDO(@Param("offset") Integer offset, @Param("maxNum") Integer maxNum);

    @Select("select id,serial_number,model_no,original_model_no from camera.device_manual where id >  ${maxId} order by id asc limit ${maxNum};")
    List<DeviceManualDO> batchQueryDeviceManualDOByMaxId(@Param("maxId") Long maxId, @Param("maxNum") Integer maxNum);
}
