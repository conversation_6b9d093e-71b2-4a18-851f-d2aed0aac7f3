package com.addx.iotcamera.dao.user;

import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.db.user.UserShowFreeLicenseDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface UserShowFreeLicenseDAO {
    @Insert("INSERT INTO camera.user_show_free_license (user_id, free_license_id,auto_reem,promotion_period, cdate) VALUES (#{userId}, #{freeLicenseId},#{autoReem},#{promotionPeriod}, UNIX_TIMESTAMP())")
    void insertUserShowFreeLicense(UserShowFreeLicenseDO userShowFreeLicense);

    @Select("SELECT * FROM camera.user_show_free_license WHERE user_id = #{userId}")
    @DS("camera-readonly")
    UserShowFreeLicenseDO findByUserId(@Param("userId") Integer userId);
}
