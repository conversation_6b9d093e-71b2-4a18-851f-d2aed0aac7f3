package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.db.ProductDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IProductDAO {

    @Select("select id,type,price,subject,body,currency,tier_id,additional_tier_uid,month,tenant_id,subscription_group_id,subscription_period" +
            " from camera.product where id = #{id} and status = 0")
    @DS("camera-readonly")
    ProductDO selectById(@Param("id") Integer id);

    @Select("select id,type,price,subject,body,currency,tier_id,additional_tier_uid,month,tenant_id,subscription_group_id from camera.product where tier_id >= #{tierId}")
    @DS("camera-readonly")
    List<ProductDO> selectByTierId(@Param("tierId") Integer tierId);

    @Select("<script>" +
            "select id, type, price, subject, body, currency, tier_id, additional_tier_uid, month, tenant_id, " +
            "subscription_period, show_in_tier, subscription_group_id " +
            "from camera.product " +
            "where tenant_id = #{tenantId} " +
            "<if test='freeTrialDuration != null'>" +
            "and (free_trial_duration = #{freeTrialDuration} or free_trial_duration is null) " +
            "</if> " +
            "and type in " +
            "   <foreach collection='typeList' item='type' open='(' separator=',' close=')'>#{type}</foreach>" +
            "</script>")
    @DS("camera-readonly")
    List<ProductDO> queryProductByType(@Param("typeList") List<Integer> typeList,
                                       @Param("tenantId") String tenantId,
                                       @Param("freeTrial") Integer freeTrial,
                                       @Param("freeTrialDuration") Integer freeTrialDuration);


    @Select("select max(type) from camera.product where tier_id = #{tierId}")
    @DS("camera-readonly")
    Integer queryProductTypeByTier(@Param("tierId") Integer tierId);

    @Select("select * from camera.product where tenant_id = #{tenantId} and tier_id =-1 and price = 0 limit 1")
    @DS("camera-readonly")
    ProductDO queryAdditionalProductFree(@Param("tenantId") String tenantId);

    @Select("select * from camera.product where tier_id =-1 and price = 0")
    @DS("camera-readonly")
    List<ProductDO> queryAdditionalProductFreeList();

    @Cacheable(value = "iProductDAO-selectOldPackCount", key = "#userId", unless = "#result==null")
    @Select("select count(*) from camera.product where tier_id in (select tier_id from camera.user_vip  where user_id = #{userId}) " +
            "and `type`  in (0,1,2)")
    // @DS("camera-readonly") // 用了缓存注解就不要查从库
    Integer selectOldPackCount(@Param("userId") Integer userId);

    @Cacheable(value = "iProductDAO-selectNewPackCount", key = "#userId", unless = "#result==null")
    @Select("select count(*) from camera.product where tier_id in (select tier_id from camera.user_vip  where user_id = #{userId}) " +
            "and `type`  in (3,4)")
    // @DS("camera-readonly") // 用了缓存注解就不要查从库
    Integer selectNewPackCount(@Param("userId") Integer userId);
}
