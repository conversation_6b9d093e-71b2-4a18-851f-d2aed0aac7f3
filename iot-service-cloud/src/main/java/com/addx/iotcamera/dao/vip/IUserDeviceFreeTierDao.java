package com.addx.iotcamera.dao.vip;

import com.addx.iotcamera.bean.domain.user.UserLiveReportDO;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IUserDeviceFreeTierDao {
    @Select("select * from camera.user_device_free_tier where end_time between  #{endTime} and unix_timestamp()" +
            " and id > #{startId} order by id asc limit 1000 ")
    @DS("camera-readonly")
    List<UserDeviceFreeTierDO> queryUserDeviceFreeTierList(@Param("endTime") Integer endTime,@Param("startId") Long startId);

    @Insert("INSERT INTO `camera`.user_device_free_tier (user_id, serial_number, tier_id,auto_reem, cdate, start_time, end_time) " +
            "VALUES (#{userId}, #{serialNumber}, #{tierId},#{autoReem}, #{cdate}, #{startTime}, #{endTime})")
    void insertUserDeviceFreeTier(UserDeviceFreeTierDO userDeviceFreeTier);

    @Select("SELECT * FROM `camera`.user_device_free_tier WHERE user_id = #{userId} " +
            "ORDER BY tier_id DESC,id desc LIMIT 1")
    @DS("camera-readonly")
    UserDeviceFreeTierDO selectHighestTierByUserId(@Param("userId") Integer userId);

    @Select("SELECT * FROM `camera`.user_device_free_tier WHERE user_id = #{userId} AND serial_number = #{serialNumber} " +
            "LIMIT 1")
    @DS("camera-readonly")
    UserDeviceFreeTierDO selectDeviceFreeTierByUserIdAndSerialNumber(@Param("userId") Integer userId,
                                                   @Param("serialNumber") String serialNumber);

    @Select("<script>SELECT * FROM `camera`.user_device_free_tier WHERE user_id = #{userId}" +
            "<if test='tierId!=null'> AND tier_id = #{tierId} </if> " +
            "</script>")
    @DS("camera-readonly")
    List<UserDeviceFreeTierDO> selectDeviceFreeTierByUserIdAndTierId(@Param("userId") Integer userId,
                                                   @Param("tierId") Integer tierId);

    @Select("SELECT * FROM `camera`.user_device_free_tier WHERE serial_number = #{serialNumber} " +
            "LIMIT 1")
    @DS("camera-readonly")
    UserDeviceFreeTierDO selectDeviceFreeTierBySerialNumber(@Param("serialNumber") String serialNumber);
}
