package com.addx.iotcamera.dao.model;

import com.addx.iotcamera.bean.device.model.DeviceModelBatteryDO;
import com.addx.iotcamera.bean.device.model.DeviceModelVolumeDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface IDeviceModelBatteryDAO {

    @DS("camera-readonly")
    @Select("SELECT  battery_code,voltameter_type,battery,url from camera.device_model_battery WHERE `battery_code`=#{batteryCode} and voltameter_type = #{voltameterType}")
    DeviceModelBatteryDO queryDeviceModelBatteryDO(@Param("batteryCode") String batteryCode,
                                                 @Param("voltameterType") Integer voltameterType);

    @Insert("insert into camera.device_model_battery (battery_code,voltameter_type,battery,url)  values (#{batteryCode},#{voltameterType},#{battery},#{url}) " +
            "ON DUPLICATE KEY UPDATE battery=#{battery} , url=#{url}")
    int saveDeviceModelBatteryDO(DeviceModelBatteryDO deviceModel);
}
