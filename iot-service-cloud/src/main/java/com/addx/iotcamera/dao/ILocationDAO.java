package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.app.LocationRequest;
import com.addx.iotcamera.bean.domain.LocationDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ILocationDAO {
    @Select("SELECT `location`.`id`, " +
            "    `location`.`admin_id`, " +
            "    `location`.`home_id`, " +
            "    `location`.`location_name`, " +
            "    `location`.`country`, " +
            "    `location`.`street_address1`, " +
            "    `location`.`street_address2`, " +
            "    `location`.`city`, " +
            "    `location`.`state`, " +
            "    `location`.`postal_code`, " +
            "    `location`.`insert_time`, " +
            "    `location`.`district` " +
            "FROM `camera`.`location` " +
            "where `admin_id`=#{adminId} ORDER BY `insert_time` DESC;")
    List<LocationDO> listUserLocations(@Param("adminId") Integer adminId);

    @Select("SELECT `location`.`id`, " +
            "    `location`.`admin_id`, " +
            "    `location`.`home_id`, " +
            "    `location`.`location_name`, " +
            "    `location`.`country`, " +
            "    `location`.`street_address1`, " +
            "    `location`.`street_address2`, " +
            "    `location`.`city`, " +
            "    `location`.`state`, " +
            "    `location`.`postal_code`, " +
            "    `location`.`insert_time`, " +
            "    `location`.`district` " +
            "FROM `camera`.`location` " +
            "where `id`=#{locationId};")
    LocationDO selectSingleLocation(Integer locationId);

    @Update("UPDATE camera.location  " +
            "SET  " +
            "    location_name = #{locationName}, " +
            "    country = #{country}, " +
            "    home_id = #{homeId}, " +
            "    street_address1 = #{streetAddress1}, " +
            "    street_address2 = #{streetAddress2}, " +
            "    city = #{city}, " +
            "    state = #{state}, " +
            "    postal_code = #{postalCode}, " +
            "    district = #{district} " +
            "WHERE " +
            "    id = #{id};")
    Integer updateLocation(LocationRequest locationRequest);

    @Insert("INSERT INTO camera.location (admin_id, location_name, country, street_address1, street_address2, city, state, postal_code, insert_time, district,home_id)" +
            "VALUES (#{adminId}, #{locationName}, #{country}, #{streetAddress1}, #{streetAddress2}, #{city}, #{state}, #{postalCode}, UNIX_TIMESTAMP(), #{district},#{homeId});")
    @SelectKey(before = false, keyProperty = "id", resultType = Integer.class, statementType = StatementType.STATEMENT, statement = "SELECT LAST_INSERT_ID() AS id;")
    Integer insertLocation(LocationDO location);

    @Delete("DELETE FROM camera.location WHERE id = #{id};")
    Integer deleteLocation(int id);

    @Delete("DELETE FROM camera.location WHERE admin_id = #{adminId} and home_id = #{homeId};")
    Integer deleteLocationByHome(@Param("adminId") int adminId,@Param("homeId") Long homeId);

    @Select("SELECT COUNT(*) FROM `camera`.`location` WHERE admin_id=#{adminId} AND location_name=#{locationName} and home_id = #{homeId};")
    Integer countOfLocationName(int adminId, String locationName,Long homeId);

    @Select("SELECT id,location_name,admin_id FROM `camera`.`location` WHERE admin_id=#{adminId} and home_id=#{homeId} AND location_name=#{locationName};")
    LocationDO queryLocationByAdminIdAndName(LocationDO location);

    @Select({"<script>SELECT id,location_name,admin_id FROM `camera`.`location` WHERE id in " +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach></script>"})
    List<LocationDO> queryLocationByIds(@Param("ids") Set<Integer> ids);


    @Update({"<script>update `camera`.`location` set home_id = #{homeId} WHERE id in " +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach></script>"})
    int updateLocationHomeByIds(@Param("ids") Set<Integer> ids,@Param("homeId") Long homeId);

    @Select({"<script>SELECT id,location_name,admin_id,home_id FROM `camera`.`location` WHERE admin_id=#{adminId} and home_id in " +
            " <foreach collection='homeIds' item='homeId' open='(' separator=',' close=')'>" +
            "#{homeId}" +
            "</foreach></script>"})
    List<LocationDO> queryLocationByHomeIds(@Param("adminId") Integer adminId,@Param("homeIds") List<Long> homeIds);
}
