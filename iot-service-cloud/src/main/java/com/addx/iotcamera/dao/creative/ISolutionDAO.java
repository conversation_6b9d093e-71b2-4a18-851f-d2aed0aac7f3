package com.addx.iotcamera.dao.creative;

import com.addx.iotcamera.bean.db.creative.SolutionDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/4/23 11:29
 */
@Repository
public interface ISolutionDAO {


    @DS("camera-readonly")
    @Select("<script>" +
            " select * from `camera`.`solution` where `id` in " +
            " <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            " #{id}" +
            " </foreach>" +
            "</script>")
    List<SolutionDO> getSolutionsByIds(@Param("ids")List<Long> ids);

    @DS("camera-readonly")
    @Select("<script> select * from `camera`.`solution`" +
            "<if test='slotName!=null'> where slot_name =#{slotName} </if>  order by id desc limit #{offset},#{limit} </script>")
    List<SolutionDO> getSolutions(@Param("offset") Integer offset, @Param("limit") Integer limit, @Param("slotName") String slotName);

    @Insert("<script>" +
            "INSERT INTO `camera`.`solution` " +
            "(slot_name, layout, video, single_image, images, name, create_time, update_time " +
            "<if test='themeColor != null'>, theme_color</if>" +
            "<if test='componentColorType != null'>, component_color_type</if>" +
            "<if test='componentColorStart != null'>, component_color_start</if>" +
            "<if test='componentColorEnd != null'>, component_color_end</if>" +
            "<if test='labelColorType != null'>, label_color_type</if>" +
            "<if test='labelColorStart != null'>, label_color_start</if>" +
            "<if test='labelColorEnd != null'>, label_color_end</if>" +
            ") VALUES (" +
            "#{slotName}, #{layout}, #{video}, #{singleImage}, #{images}, #{name}, #{createTime}, #{updateTime} " +
            "<if test='themeColor != null'>, #{themeColor}</if>" +
            "<if test='componentColorType != null'>, #{componentColorType}</if>" +
            "<if test='componentColorStart != null'>, #{componentColorStart}</if>" +
            "<if test='componentColorEnd != null'>, #{componentColorEnd}</if>" +
            "<if test='labelColorType != null'>, #{labelColorType}</if>" +
            "<if test='labelColorStart != null'>, #{labelColorStart}</if>" +
            "<if test='labelColorEnd != null'>, #{labelColorEnd}</if>" +
            ")" +
            "</script>")
    Long insert(SolutionDO solutionDO);
}
