package com.addx.iotcamera.dao.device;

import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.ReflectionUtils;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DeviceSupportDAOHelper {

    public static String collectionToString(Collection<?> coll) {
        if (coll == null || coll.isEmpty()) return "";
        return coll.stream().map(it -> it + "").collect(Collectors.joining(","));
    }

    public static Integer booleanToInteger(Boolean value) {
        if (value == null) return null;
        return value ? 1 : 0;
    }

    public static List<String> stringListFromString(String value) {
        return TextUtil.splitToNotBlankList(value, ',');
    }

    public static List<Integer> integerListFromString(String value) {
        return TextUtil.splitToNotBlankStream(value, ',').map(Integer::valueOf).collect(Collectors.toList());
    }

    @SneakyThrows
    public static Map<String, Object> deviceSupportToMap(CloudDeviceSupport cloudDeviceSupport, boolean filterNullValue) {
        final Map<String, Object> map = new LinkedHashMap<>();
        for (final Field field : ReflectionUtils.getAllFields(CloudDeviceSupport.class)) {
            if (field.isSynthetic()) continue; // 忽略sonar自动生成的字段
            field.setAccessible(true);
            final Object value = field.get(cloudDeviceSupport);
            if (filterNullValue && value == null) continue;
            final Object sqlValue;
            if (field.getType() == String.class) { // 字符串
                sqlValue = value;
            } else if (field.getType() == Boolean.class || field.getType() == boolean.class) { // 包装类或原生布尔值
                sqlValue = booleanToInteger((Boolean) value);
            } else if (Number.class.isAssignableFrom(field.getType()) || field.getType().isPrimitive()) { // 包装类或原生数字
                sqlValue = value;
            } else if (Collection.class.isAssignableFrom(field.getType())) {
                sqlValue = collectionToString((Collection<?>) value);
            } else if (field.getType() == DeviceAttributeIntRange.class) {
                sqlValue = JSON.toJSONString(value);
            }  else if (field.getType() == JSONObject.class) {
                sqlValue = JSON.toJSONString(value);
            }else {
                throw new IllegalArgumentException("deviceSupportToMap 不支持的类型! name=" + field.getName() + ",type=" + field.getType());
            }
            map.put(TextUtil.toUnderline(field.getName()), sqlValue);
        }
        log.info("deviceSupportToMap bean={},map={}", JSON.toJSONString(cloudDeviceSupport), JSON.toJSONString(map));
        return map;
    }

    @SneakyThrows
    public static CloudDeviceSupport deviceSupportFromMap(Map<String, Object> map, boolean filterNullValue) {
        if (map == null) return null;
        final CloudDeviceSupport cloudDeviceSupport = new CloudDeviceSupport();
        for (final Field field : ReflectionUtils.getAllFields(CloudDeviceSupport.class)) {
            if (field.isSynthetic()) continue; // 忽略sonar自动生成的字段
            field.setAccessible(true);
            final Object sqlValue = map.get(TextUtil.toUnderline(field.getName()));
            if (filterNullValue && sqlValue == null) continue;
            final Object value;
            if (field.getType() == String.class) { // 字符串
                value = sqlValue;
            } else if (field.getType() == Boolean.class || field.getType() == boolean.class) { // 包装类或原生布尔值
                value = ((Number) sqlValue).intValue() > 0;
            } else if (field.getType() == Integer.class || field.getType() == int.class) { // 包装类或原生数字
                value = ((Number) sqlValue).intValue();
            } else if (isParameterizedType(field.getGenericType(), List.class, String.class)) {
                value = stringListFromString((String) sqlValue);
            } else if (isParameterizedType(field.getGenericType(), List.class, Integer.class)) {
                value = integerListFromString((String) sqlValue);
            } else if (field.getType() == DeviceAttributeIntRange.class) {
                value = JSON.parseObject((String) sqlValue, DeviceAttributeIntRange.class);
            } else if (field.getType() == JSONObject.class) {
                value = JSON.parseObject((String) sqlValue, JSONObject.class);
            } else {
                throw new IllegalArgumentException("deviceSupportToMap 不支持的类型! name=" + field.getName() + ",type=" + field.getType());
            }
            field.set(cloudDeviceSupport, value);
        }
        log.info("deviceSupportFromMap map={},bean={}", JSON.toJSONString(map), JSON.toJSONString(cloudDeviceSupport));
        return cloudDeviceSupport;
    }

    public static boolean isParameterizedType(Type type, Class rawType, Class... typeArgs) {
        if (!(type instanceof ParameterizedType)) return false;
        final ParameterizedType pType = (ParameterizedType) type;
        if (!rawType.isAssignableFrom((Class) pType.getRawType())) return false;
        if (typeArgs.length != pType.getActualTypeArguments().length) return false;
        for (int i = 0; i < typeArgs.length; i++) {
            if (!typeArgs[i].isAssignableFrom((Class) pType.getActualTypeArguments()[i])) return false;
        }
        return true;
    }
}
