package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.domain.User;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface IUserDAO {

    @Insert("INSERT INTO camera.user (name, email,phone, hashed_password, salt," +
            " regist_time, country_no, tenant_id,language, " +
            " `type`,`third_user_id`,`account_id`,app_type,app_version_name,app_version) " +
            "VALUES (#{name}, #{email},#{phone}, #{hashedPassword}, #{salt}," +
            " UNIX_TIMESTAMP(), #{countryNo}, #{tenantId},#{language}," +
            " #{type},#{thirdUserId},#{accountId},#{appType},#{appVersionName},#{appVersion});")
    @SelectKey(before = false, keyProperty = "id", resultType = Integer.class, statementType = StatementType.STATEMENT, statement = "SELECT LAST_INSERT_ID() AS id;")
    Integer insertUser(User user);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#user.id")
    @Update("UPDATE camera.user " +
            "SET name = #{name},last_name = #{lastName} " +
            "WHERE id = #{id}")
    Integer updateUserName(User user);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#user.id")
    @Update("UPDATE `camera`.`user` " +
            "SET `hashed_password` = #{hashedPassword}, " +
            " `salt` = #{salt}" +
            "WHERE id = #{id}")
    Integer updateUserPassword(User user);

    @Insert("insert into `camera`.`user_pwd_history` (`user_id`,`salt`,`hashed_password`,`reason`) values (#{user.id},#{user.salt},#{user.hashedPassword},#{reason})")
    int insertUserPwdHistory(@Param("user") User user, @Param("reason") String reason);

    @Select({"<script> SELECT * FROM camera.user WHERE 1=1 " +
            "<if test=\"email!=null and email!=''\">and `email` = #{email}</if>" +
            " <if test=\"phone!=null and phone!='' \"> and phone=#{phone} </if>   " +
            " AND `tenant_id` = #{tenantId}" +
            " and status = 0; </script>"})
    User getUserByEmailAndTenantId(@Param("email") String email, @Param("phone") String phone, @Param("tenantId") String tenantId);

    @Select(" SELECT * FROM camera.user WHERE id = #{id} and status = 0")
    @DS("camera-readonly")
    User getUserById(User user);

    @Select(" SELECT name FROM camera.user WHERE id = #{id}")
    @DS("camera-readonly")
    String getUserName(Integer id);

    @Select(" SELECT id,name,tenant_id,email,phone,language,`type`,`account_id` FROM camera.user WHERE id = #{id}")
    @DS("camera-readonly")
    User getUserUserInfo(Integer id);

    @Select("<script>" +
            " SELECT * FROM camera.user where id > #{id}" +
            " <if test='statuses!=null and statuses.size()>0'> and `status` in " +
            "   <foreach collection='statuses' item='status' open='(' separator=',' close=')'>#{status}</foreach>" +
            " </if>" +
            " <if test='excludeTenantIds!=null and excludeTenantIds.size()>0'> and `tenant_id` not in " +
            "   <foreach collection='excludeTenantIds' item='tenantId' open='(' separator=',' close=')'>#{tenantId}</foreach>" +
            " </if> " +
            " <if test='registerTime!=null and registerTime>0'> and `regist_time` &lt; #{registerTime}</if> " +
            " order by id asc limit ${limitNum}" +
            "</script>")
    @DS("camera-readonly")
    List<User> selectAllBatch(@Param("id") Integer id, @Param("statuses") Collection<Integer> statuses
            , @Param("excludeTenantIds") Collection<String> excludeTenantIds,
                              @Param("limitNum") Integer limitNum,
                              @Param("registerTime") Integer registerTime);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#user.id")
    @Delete("update `camera`.`user` set status = 1,cancellation_reason=#{cancellationReason}," +
            "cancellation_remark=#{cancellationRemark} WHERE id = #{id}")
    Integer deleteUser(User user);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#user.id")
    @Update("<script>UPDATE `camera`.`user` " +
            "SET `language` = #{language} " +
            " <if test='appType!=null'>,`app_type`=#{appType}</if>" +
            " <if test='appVersion!=null'>,`app_version`=#{appVersion}</if>" +
            " <if test='appVersionName!=null'>,`app_version_name`=#{appVersionName}</if>" +
            "WHERE `id` = #{id}</script>")
    Integer updateUserLanguage(User user);

    @Select("SELECT id,name,email,phone,`type`,`account_id`,`third_user_id` FROM camera.user where id in (${userIds})")
    @DS("camera-readonly")
    List<User> selectUserByUserIds(@Param("userIds") String userIds);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#userId")
    @Update({"UPDATE `camera`.`user` " +
            "SET " +
            "`email` = #{email}, " +
            "`phone` = #{phone} " +
            "WHERE `id` = #{userId} ; "})
    Integer updateUserContact(@Param("userId") Integer userId, @Param("email") String email, @Param("phone") String phone);

    @Select("SELECT internal FROM `camera`.`user` WHERE id = #{userId}")
    Integer getUserInternal(@Param("userId") Integer userId);

    @Cacheable(value = "iUserVipDAO-selectUserByEmail", key = "#user.id", unless = "#result==null")
    @Select({"<script>SELECT id,name,email,phone FROM camera.user where email = #{email} " +
            "<choose> <when test=\"tenantId == 'guard'\"> AND (`tenant_id` = #{tenantId} or `tenant_id` = 'vicoo') </when><otherwise> AND `tenant_id` = #{tenantId}</otherwise></choose></script>"})
    // @DS("camera-readonly") // 用了缓存注解就不要查从库
    List<User> selectUserByEmail(User user);

    @Cacheable(value = "iUserVipDAO-selectUserByPhone", key = "#user.id", unless = "#result==null")
    @Select({"<script>SELECT id,name,email,phone FROM camera.user where phone = #{phone} " +
            "<choose> <when test=\"tenantId == 'guard'\"> AND (`tenant_id` = #{tenantId} or `tenant_id` = 'vicoo') </when><otherwise> AND `tenant_id` = #{tenantId}</otherwise></choose></script>"})
    // @DS("camera-readonly") // 用了缓存注解就不要查从库
    List<User> selectUserByPhone(User user);

    @Select("select `id`,`tenant_id`,`third_user_id`,`type` from `camera`.`user`" +
            " where `tenant_id`=#{tenantId} and `third_user_id`=#{thirdUserId} and `status`=0")
    User queryByTenantIdAndThirdUserId(@Param("tenantId") String tenantId, @Param("thirdUserId") String thirdUserId);

    @Select("select `id`,`third_user_id`,`type` from `camera`.`user` where `tenant_id`=#{tenantId} and `status`=0")
    List<User> queryByTenantId(@Param("tenantId") String tenantId);

    @CacheEvict(value = {"iUserVipDAO-selectUserByEmail", "iUserVipDAO-selectUserByPhone"}, key = "#userModify.id")
    @Update("<script>" +
            " update `camera`.`user` set `id`=`id`" +
            " <if test=\"language!=null\">,`language`=#{language}</if>" +
            " <if test=\"countryNo!=null\">,`country_no`=#{countryNo}</if>" +
            " <if test=\"email!=null\">,`email`=#{email}</if>" +
            " <if test=\"phone!=null\">,`phone`=#{phone}</if>" +

            " <if test='appType!=null'>,`app_type`=#{appType}</if>" +
            " <if test='appVersion!=null'>,`app_version`=#{appVersion}</if>" +
            " <if test='appVersionName!=null'>,`app_version_name`=#{appVersionName}</if>" +
            " where `id`=#{id}" +
            "</script>")
    int updateUserById(User userModify);

    @Select({"<script> SELECT * FROM camera.user where tenant_id = 'safemo' and status=0 and app_type = 'Android' and app_version_name like '1.1.2%' and regist_time > #{registerTime};  </script>"})
    @DS("camera-readonly")
    List<User> selectByTimeStamp(@Param("registerTime") int registerTime);

    @Update("update camera.user set status = 0 where `internal` = 1 and UNIX_TIMESTAMP(update_time) < UNIX_TIMESTAMP(NOW() - INTERVAL 24 HOUR)")
    void updateUserStatusForReviewAccount();

    @Select("select * from camera.user where `internal` = 1 and status = 1 and UNIX_TIMESTAMP(update_time) < UNIX_TIMESTAMP(NOW() - INTERVAL 24 HOUR)")
    List<User> selectUserForReviewAccount();

}
