package com.addx.iotcamera.dao.pay;

import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IOrderDAO {

    @Insert("insert into camera.order (order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type,product_month,sandbox,guidance_source) values (" +
            "#{orderSn},#{orderType},#{userId},#{status},#{tenantId},#{timeStart},#{timeEnd},#{extend},#{cdate},#{mdate},#{tradeNo},#{subType},#{productMonth},#{sandbox},#{guidanceSource})")
    @SelectKey(before = false, keyProperty = "id", resultType = Long.class, statementType = StatementType.STATEMENT, statement = "SELECT LAST_INSERT_ID() AS id;")
    int insertOrder(OrderDO orderDO);


    @Update("update camera.order set status = #{status},mdate = #{mdate},trade_no=#{tradeNo},sub_type=#{subType},time_end=#{timeEnd} where id = #{id}")
    int updateOrderStatus(OrderDO orderDO);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type," +
            "order_cancel,order_cancel_time,order_cancel_reason  " +
            "from camera.order where order_sn = #{orderSn} order by id asc limit 1")
    OrderDO queryByOrderSn(@Param("orderSn") String orderSn);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type,order_cancel " +
            "from camera.order where id = #{id}")
    OrderDO queryByOrderId(@Param("id") Long id);
    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type,order_cancel " +
            "from camera.order where id = #{id}")
    @DS("camera-readonly")
    OrderDO queryByOrderIdReadOnly(@Param("id") Long id);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,sub_type,trade_no,order_cancel " +
            "from camera.order where trade_no = #{tradeNo} order by time_end desc limit 1")
    OrderDO queryBytradeNo(@Param("tradeNo") String tradeNo);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,sub_type,trade_no,order_cancel " +
            "from camera.order where trade_no = #{tradeNo} order by id desc")
    @DS("camera-readonly")
    List<OrderDO> queryBytradeNoBatch(@Param("tradeNo") String tradeNo);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type from  camera.order " +
            " where cdate > #{cdate} and status = 0 and order_type in (0,1)")
    List<OrderDO> queryOrderUnCompleteList(@Param("cdate") Integer cdate);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type,order_cancel from  camera.order " +
            " where id > #{startOrderId} and status = 1 and order_type = 2 and sub_type = 1 " +
            "and (order_cancel = 0 or (order_cancel = 1 and order_cancel_time > 0 and order_cancel_reason = 0)) " +
            " order by id asc limit 100")
    @DS("camera-readonly")
    List<OrderDO> queryIosSubOrderList(@Param("startOrderId") Long startOrderId);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type from  camera.order " +
            " where id > #{startOrderId} and order_type = 2 and sandbox=0 order by id asc limit 200")
    @DS("camera-readonly")
    List<OrderDO> queryIosOrderBatch(@Param("startOrderId") Long startOrderId);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type from  camera.order " +
            " where  status = 1  and sub_type = 1 and sandbox = 0")
    List<OrderDO> querySubOrderList();

    @Select("select * from  camera.order " +
            " where user_id=#{userId} and order_type in (0,1,2,3) order by cdate desc")
    List<OrderDO> queryUserOrderList(@Param("userId") Integer userId);

    @Select("select * from  camera.order " +
            " where user_id=#{userId} and order_type = 4 and status = 1  order by cdate desc")
    List<OrderDO> queryUserTestOrderList(@Param("userId") Integer userId);

    @Update("update camera.order set mdate = UNIX_TIMESTAMP(),time_end=#{timeEnd} where id = #{id}")
    int updateOrderEndDate(OrderDO orderDO);

    @Update("update camera.order set mdate = UNIX_TIMESTAMP(),is_close = 1 where id = #{id}")
    int closeMomoOrder(@Param("id") Long id);

    @Select({"<script>select id from camera.order where id in " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " and status = 1 and sub_type=1 and order_type = 2 order by time_end desc limit 1</script>"})
    Long queryUserNewestSubOrder(@Param("ids") List<Long> ids);

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type " +
            "from camera.order where order_type = 5 and status = 0")
    List<OrderDO> queryMomoOrderPending();

    @Select("select id,order_sn,order_type,user_id,status,tenant_id,time_start,time_end,extend,cdate,mdate,trade_no,sub_type " +
            "from camera.order where   user_id = #{userId} and  order_type = 5 and status in (0,1,3) and is_close = 0")
    List<OrderDO> queryUserMomoOrderPending(@Param("userId") Integer userId);

    @Select("select order_type from camera.order where id = #{id}")
    Integer queryOrderTypeByOrderId(@Param("id") Long id);


    @Select({"<script>select count(*) from camera.order where id in " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " and status = 1 and order_type in (0,1,2,3) </script>"})
    int queryUserPaymentOrderCount(@Param("ids") List<Long> ids);


    @Update("update camera.order set order_cancel=#{orderCancel},order_cancel_time=#{orderCancelTime}," +
            "order_cancel_reason=#{orderCancelReason},cancel_event_key=#{cancelEventKey} where id = #{id}")
    int updateOrderCancelTime(OrderDO orderDO);


    @Select({"<script>select id,order_sn,order_type,order_cancel,sub_type from camera.order where id in " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " and status = 1 and sub_type=1 order by id desc </script>"})
    @DS("camera-readonly")
    List<OrderDO> queryOrderBatch(@Param("ids") List<Long> ids);
}
