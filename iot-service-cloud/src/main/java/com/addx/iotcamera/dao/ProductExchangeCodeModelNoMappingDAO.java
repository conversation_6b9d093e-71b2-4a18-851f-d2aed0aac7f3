package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.db.ProductExchangeCodeDisplay;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductExchangeCodeModelNoMappingDAO {
    @DS("camera-readonly")
    @Select("SELECT display_id FROM `camera`.`product_exchange_code_model_no_mapping` WHERE model_no = #{modelNo};")
    List<Integer> queryProductExchangeCodeDisplayIdByModelNo(@Param("modelNo") String modelNo);
}
