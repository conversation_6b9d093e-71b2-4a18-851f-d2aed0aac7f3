package com.addx.iotcamera.dao.factory;

import com.addx.iotcamera.bean.app.device.DeviceBindInfoDO;
import com.addx.iotcamera.bean.warranty.WarrantyOrderDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS("factory")
@Repository
public interface DeviceBindInfoDao {

    @Select("SELECT user_sn FROM `factory`.`device_manufacture` WHERE serial_number = #{serialNumber}")
    String findUserSnBySerialNumber(@Param("serialNumber") String serialNumber);

    @Select("SELECT register_model_no FROM `factory`.`device_manufacture` WHERE user_sn = #{userSn}")
    String findModelNoByUserSn(@Param("userSn") String userSn);

    @Select("SELECT parent_uniq_code FROM `factory`.`device_component_bind_info` WHERE uniq_code = #{uniqCode} AND bind_status = 1 limit 1")
    String findParentUniqCodeByUniqCode(@Param("uniqCode") String uniqCode);

    @Select("SELECT parent_uniq_code FROM `factory`.`device_component_bind_info` WHERE uniq_code = #{uniqCode} AND bind_status = 1")
    List<String> findAllParentUniqCodesByUniqCode(@Param("uniqCode") String uniqCode);

    @Select("SELECT uniq_code, model_no FROM `factory`.`device_component_bind_info` WHERE uniq_code = #{parentUniqCode} AND parent_uniq_code = #{parentUniqCode} AND bind_status = 1")
    List<DeviceBindInfoDO> findDeviceBindInfoByParentUniqCode(@Param("parentUniqCode") String parentUniqCode);

    @Select("SELECT icon_url FROM `factory`.`device_model` WHERE model_no = #{modelNo}")
    String findIconUrlByModelNo(@Param("modelNo") String modelNo);

    @Select("SELECT sale_name FROM `factory`.`device_model_display_model` WHERE model_no = #{modelNo} AND sale_name IS NOT NULL LIMIT 1")
    String findSaleNameByModelNo(@Param("modelNo") String modelNo);
}
