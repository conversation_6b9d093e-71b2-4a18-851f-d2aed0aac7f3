package com.addx.iotcamera.dao.log;


import com.addx.iotcamera.bean.db.LogAnalysisConfig;
import com.addx.iotcamera.bean.db.UserAgreeDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ILogAnalysisConfigDao {

    /**
     * 批量插入或更新日志分析配置
     *
     * @param logAnalysisConfigList 日志分析配置列表
     * @return 受影响的行数
     */
    @Insert({"<script>",
            "REPLACE INTO `log_analysis_config_v2` (",
            "`rule_ower`, `bx_serial_number`, `cx_serial_number`, `role_id`, ",
            "`device_type`, `analysis_key`, `application`, `log_info`, ",
            "`start_time`, `end_time`, `type`",
            ") VALUES ",
            "<foreach collection='list' item='config' separator=','>",
            "(",
            "#{config.ruleOwer}, #{config.bxSerialNumber}, #{config.cxSerialNumber}, #{config.roleId}, ",
            "#{config.deviceType}, #{config.analysisKey}, #{config.application}, #{config.logInfo}, ",
            "#{config.startTime}, #{config.endTime}, #{config.type}",
            ")",
            "</foreach>",
            "</script>"})
    int insert(@Param("list") List<LogAnalysisConfig> logAnalysisConfigList);

    /**
     * 根据规则属主和CX设备序列号列表查询日志规则
     *
     * @param ruleOwer 规则属主(用户ID或租户ID)
     * @param cxSerialNumbers CX设备序列号列表
     * @return 符合条件的日志规则列表
     */
    @DS("camera-readonly")
    @Select("<script>SELECT * FROM `log_analysis_config_v2` WHERE rule_ower = #{ruleOwer} " +
            "AND cx_serial_number IN " +
            "<foreach collection='cxSerialNumbers' item='cxSn' open='(' separator=',' close=')'>" +
            "#{cxSn}" +
            "</foreach>" +
            "</script>")
    List<LogAnalysisConfig> findByRuleOwerAndCxSerialNumbers(@Param("ruleOwer") String ruleOwer,
                                                               @Param("cxSerialNumbers") List<String> cxSerialNumbers);

    /**
     * 根据规则属主和BX设备序列号查询日志规则
     *
     * @param ruleOwer 规则属主(用户ID或租户ID)
     * @param bxSerialNumber BX设备序列号
     * @return 符合条件的日志规则列表
     */
    @DS("camera-readonly")
    @Select("SELECT * FROM `log_analysis_config_v2` WHERE rule_ower = #{ruleOwer} " +
            "AND bx_serial_number = #{bxSerialNumber}")
    List<LogAnalysisConfig> findByRuleOwerAndBxSerialNumber(@Param("ruleOwer") String ruleOwer,
                                                            @Param("bxSerialNumber") String bxSerialNumber);

    /**
     * 根据规则属主列表查询日志分析配置
     *
     * @param ruleOwerList 规则属主列表
     * @return 符合条件的日志分析配置列表
     */
    @Select("<script>SELECT * FROM `log_analysis_config_v2` WHERE rule_ower IN " +
            "<foreach collection='ruleOwerList' item='ruleOwer' open='(' separator=',' close=')'>" +
            "#{ruleOwer}" +
            "</foreach>" +
            "</script>")
    List<LogAnalysisConfig> getConfigByRuleOwers(@Param("ruleOwerList") List<String> ruleOwerList);

//    @Select("<script>SELECT * FROM `camera`.`log_analysis_config` WHERE user_id = #{userId}")
//    List<LogAnalysisConfig> getConfig(@Param("userId")Integer userId);
//

//    @Update("UPDATE `camera`.`log_analysis_config` " +
//            "SET " +
//            "`roles` = #{roles}, " +
//            "`update_timestamp` = #{updateTimestamp} " +
//            "WHERE `id` = #{id} ;")
//    void update(LogAnalysisConfig userAgreeDO);
}
