package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.domain.DeviceOperationDO;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.StateMachineService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import lombok.Data;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Data
public class DeviceOperationHelper {

    private RedisService redisService;
    private StateMachineService stateMachineService;

    @Autowired
    @Lazy
    private DeviceStatusService deviceStatusService;

    private static Logger LOGGER = LoggerFactory.getLogger(DeviceOperationHelper.class);

    @Autowired
    public DeviceOperationHelper(RedisService redisService,StateMachineService stateMachineService) {
        this.redisService = redisService;
        this.stateMachineService = stateMachineService;
    }

    public Result waitOperation(String operationId) {

        LOGGER.info("Waiting for: " + operationId);

        Retryer<DeviceOperationDO> retryer = RetryerBuilder.<DeviceOperationDO>newBuilder()
                .retryIfResult(Predicates.isNull())
                .withWaitStrategy(WaitStrategies.fixedWait(100, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(100))
                .build();

        try {
            DeviceOperationDO storedOperation = retryer.call(() -> redisService.getDeviceOperationDo(operationId));

            LOGGER.info("Operation: {} succeeded", operationId);

            return new Result(storedOperation);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "Operation  failed " + operationId, e);
            return ResultCollection.DEVICE_NO_RESPONSE.getResult();
        }
    }

    public Result waitOperation(String operationId, Integer retryCount) {

        LOGGER.info("Waiting for: " + operationId);

        Retryer<DeviceOperationDO> retryer = RetryerBuilder.<DeviceOperationDO>newBuilder()
                .retryIfResult(Predicates.isNull())
                .withWaitStrategy(WaitStrategies.fixedWait(100, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryCount))
                .build();

        try {
            DeviceOperationDO storedOperation = retryer.call(() -> redisService.getDeviceOperationDo(operationId));

            LOGGER.info("Operation: {} succeeded", operationId);

            return new Result(storedOperation);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "Operation: {} failed", operationId);
            return ResultCollection.DEVICE_NO_RESPONSE.getResult();
        }
    }


    public boolean waitDeviceWake(String serialNumber) {
        LOGGER.info("Waiting for device {} wakeup" ,serialNumber);

        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                .retryIfResult(Predicates.equalTo(false))
                .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(15))
                .build();

        try {
            Boolean deviceStatus = retryer.call(() -> deviceStatusService.deviceConnectionStatus(serialNumber));
            LOGGER.info("Waiting for device {} wakeup {}", serialNumber,deviceStatus);
            return deviceStatus;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "Waiting for device {} wakeup failed", serialNumber);
            return false;
        }
    }
}
