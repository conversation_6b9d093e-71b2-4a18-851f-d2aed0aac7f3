package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.domain.HttpTokenDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.service.TokenService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.google.common.collect.ImmutableMap;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtHelper {

    private final static String TOKEN_PREFIX = "Bearer";
    public final static String HEADER_STRING = "Authorization";
    //oauth server 会用到，要改得一起改
    private final static String SECRET = "ZqdQpCTbf7V3Lle52YPhPAIhdwZQ4RFTsdl0KBOGYUA0qfBViAeuCN82uWY2josPTHeCjBGEr9g4spSEXwxjPJCecrSmNwCQDIZALSVcpXzceHqiPQSDG45LPqFTc3QoTCyyl6ShiuTGS32hFtpZ8FPm4jxpdNUT3mnbXiYVjK3mUhVeNemW3dLQGCiPwFoOulDZjGM0tagSxi0QHeNe7XVaNpBx1P2XRVdGF5o";
    private final static long EXPIRATION_TIME = 999999999; // Immortal Token

    //TODO 出厂时为每个设备分配一个
    private final static String DEVICE_SHARE_KEY = "_u72js2u4s75b13hz";

    private final static Logger LOGGER = LoggerFactory.getLogger(JwtHelper.class);

    @Autowired(required = false)
    private TokenService tokenService;

    public HttpTokenDO generateToken(Map<String, Object> claims) {
        return getHttpTokenWithExpiration(claims, EXPIRATION_TIME);
    }

    public HttpTokenDO generateToken(Map<String, Object> claims, long expiration) {
        return getHttpTokenWithExpiration(claims, expiration);
    }

    public Map<String, Object> validateTokenAndGetClaims(HttpServletRequest request) {
        String token = request.getHeader(HEADER_STRING);
        if (token == null) {
            return null;
        }

        if (isValidDeviceRequest(token)) {
            return ImmutableMap.of("serialNumber", token.split("_")[0]);
        }

        token = getLoginTokenIfOauthAccessToken(token);

        try {
            return Jwts.parser()
                    .setSigningKey(SECRET.getBytes())
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                    .getBody();

        } catch (Exception ex) {
            try{
                return Jwts.parser()
                    .setSigningKey(SECRET)
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                    .getBody();
            }catch(Exception e) {
                return new HashMap<>();
            }
        }
    }

    public Integer getUserId(String token) {
        return this.getTokenInteger(token.split(" ")[1], "userId");
    }

    /**
     * 将token解析成原始数据
     *
     * @param token
     * @return
     */
    public Map<String, Object> getUserToken(String token) {
        return parseToken(token.replace(TOKEN_PREFIX, ""));
    }

    // 解析token
    public Map<String, Object> parseToken(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(SECRET.getBytes())
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            try{
                // 短期兼容，防止设备、app的老token 没有过期
                return Jwts.parser()
                            .setSigningKey(SECRET)
                            .parseClaimsJws(token)
                            .getBody();
            }catch (Exception ex) {
                return Collections.emptyMap();
            }
        }
    }

    // 创建token
    public String createToken(Map<String, Object> claims, long expiration) {
        Date date = Date.from(Instant.now().plusSeconds(expiration));
        String jwt = Jwts.builder()
                .setClaims(claims)
                .setExpiration(date)
                .signWith(SignatureAlgorithm.HS512, SECRET.getBytes())
                .compact();
        return jwt;
    }

    private HttpTokenDO getHttpTokenWithExpiration(Map<String, Object> claims, long expiration) {
        String jwt = createToken(claims, expiration);
        HttpTokenDO token = new HttpTokenDO();
        token.setToken(String.format("%s %s", TOKEN_PREFIX, jwt));
        token.setTokenType(TOKEN_PREFIX);
        return token;
    }

    private Integer getTokenInteger(String token, String key) {
        Map<String, Object> map = Jwts.parser()
                .setSigningKey(SECRET.getBytes())
                .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                .getBody();
        if(map.size() <= 0){
            map = Jwts.parser()
                    .setSigningKey(SECRET)
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                    .getBody();
        }
        return (Integer) map.get(key);
    }

    /*
    app与设备都共用Authorization头，但是参数结构不同
    设备token结构:"${serialNumber}_${receivedTime}_${hash}"
    hash=md5("${serialNumber}_${receivedTime}${DEVICE_SHARE_KEY}")
    eg:aad7f5e6c64b88d419b5b79153402d2e_1615170494_c2cf868e74ff0e1d4bdbfaddde2a9055
    */
    private boolean isValidDeviceRequest(String token) {
        try {
            String[] tokenParamArray = token.split("_");
            if (tokenParamArray.length == 1) {
                //不是设备发出的请求
                return false;
            }
            String tokenTime = token.split("_")[1];
            if (!isNumeric(tokenTime)) {
                return false;
            }
            Instant receivedTime = Instant.ofEpochSecond(Long.parseLong(tokenTime));
            LOGGER.debug("received time: {}", receivedTime);

            Instant currentTime = Instant.now();

            Duration timeElapsed = Duration.between(receivedTime, currentTime);

            if (timeElapsed.toMinutes() > 5) {
//                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "timeElapsed: {}, expired request", timeElapsed);
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceTokenTimeElapsedCounterOptional().ifPresent(it -> {
                    final int minutesLog10 = 1 + (int) Math.log10(timeElapsed.toMinutes()); // 超时分钟数的数量级
                    it.labels(PrometheusMetricsUtil.getHostName(), "notQueryOnJwtFilter", minutesLog10 + "").inc();
                }));
                return false;
            }

            String sourceString = token.substring(0, token.lastIndexOf('_')) + DEVICE_SHARE_KEY;

            String receivedHash = token.substring(token.lastIndexOf('_') + 1);

            LOGGER.debug("received hash: {}", receivedHash);


            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(sourceString.getBytes());
            byte[] digest = messageDigest.digest();

            LOGGER.debug("expected hash: {}", DatatypeConverter.printHexBinary(digest));

            return receivedHash.equalsIgnoreCase(DatatypeConverter.printHexBinary(digest));
        } catch (Exception ex) {
            LOGGER.info("Not valid device direct token", ex);
            return false;
        }
    }

    /**
     * 三方平台通过oauth生成access_token发起调用解析iot service loginToken
     * 1. 判断是否合法access_token
     * 2. 如果access_token合法，但是携带loginToken已失效(用户其他地方登录踢出), 需使用新的loginToken让请求正常进入iot service
     *
     * @param token
     * @return
     */
    public String getLoginTokenIfOauthAccessToken(String token) {
        String loginToken = token;

        Map<String, Object> tokenInfoMap = new HashMap<>();
        try {
            tokenInfoMap = Jwts.parser()
                    .setAllowedClockSkewSeconds(10 * 365 * 24 * 3600)
                    .setSigningKey(SECRET.getBytes())
                    .parseClaimsJws(token.replace(TOKEN_PREFIX, "").replace("Basic", ""))
                    .getBody();
        } catch (Exception e) {
            try {
                tokenInfoMap = Jwts.parser()
                        .setAllowedClockSkewSeconds(10 * 365 * 24 * 3600)
                        .setSigningKey(SECRET)
                        .parseClaimsJws(token.replace(TOKEN_PREFIX, "").replace("Basic", ""))
                        .getBody();

            }catch(Exception ex){
                LOGGER.debug("not our oauth access_token, do nothing {}",loginToken);
            }
        }

        if (tokenInfoMap.containsKey("scope")) {
            String jtiBase64 = String.valueOf(tokenInfoMap.get("jti"));
            String jti = new String(Base64.getDecoder().decode(jtiBase64));
            loginToken = jti.substring(jti.indexOf("_") + 1).trim();
            Map<String, Object> iotServiceTokenMap = parseToken(loginToken);

            Integer userId = Integer.valueOf(tokenInfoMap.get("user_name").toString());
            String seed = tokenService != null ? tokenService.queryUserSeed(userId.toString()) : iotServiceTokenMap.get("seed").toString();

            //如果seed已变(用户重新登录), 需要使用新的loginToken 否则会被JwtFilter过滤掉请求
            if (CollectionUtils.isEmpty(iotServiceTokenMap) || !StringUtils.equalsIgnoreCase(iotServiceTokenMap.get("seed").toString(), seed)) {
                loginToken = tokenService.generateMsgToken(seed, new User() {{
                    setId(userId);
                }}).getToken();
            }

            loginToken = TOKEN_PREFIX + " " + loginToken;
        }

        return loginToken;
    }

    private static boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
}
