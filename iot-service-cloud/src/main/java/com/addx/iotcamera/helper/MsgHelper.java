package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.msg.fcm.FcmMsgEntity;
import com.addx.iotcamera.bean.msg.ios.IosMsgEntity;
import com.addx.iotcamera.bean.msg.umeng.UmengMsgEntity;
import com.addx.iotcamera.config.apollo.PushConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.publishers.notification.Firebase.FirebaseMessage;
import com.addx.iotcamera.publishers.notification.Firebase.FirebasePublisher;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.publishers.notification.UMeng.UMengPublisher;
import com.addx.iotcamera.publishers.notification.UMeng.UmengPayload;
import com.addx.iotcamera.publishers.notification.iOS.BaseIosNotification;
import com.addx.iotcamera.publishers.notification.iOS.IosMessage;
import com.addx.iotcamera.publishers.notification.iOS.IosPublisher;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.service.message.FcmV1Service;
import com.addx.iotcamera.util.MapUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.addx.tracking.config.snowplow.SnowPlowManager;
import com.turo.pushy.apns.PushType;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_SYS_PUSH_MSG;
import static org.addx.iot.common.constant.AppConstants.DEFAULT_PACKAGE_VICOO;

@Component
@Slf4j
public class MsgHelper {
    @Autowired
    private PushConfig config;

    @Autowired
    private ReportLogService reportLogService;

    @Autowired
    @Lazy
    private IosPublisher iosPublisher;

    @Autowired
    @Lazy
    private FirebasePublisher firebasePublisher;
    @Autowired
    @Lazy
    private FcmV1Service fcmV1Service;

    @Autowired
    @Lazy
    private UMengPublisher uMengPublisher;

    @Autowired(required = false)
    private RedisService redisService;

    public static final String STATUS_OK = "OK";

    @Async(value = "pushMessage")
    public void Send(PushArgs pushArgs, MsgEntityBase msg) {
        String traceId = msg.getTraceId();
        int userId = pushArgs.getUserId();
        MDC.put(MDCKeys.SERIAL_NUMBER, pushArgs.getSerialNumber());
        reportLogService.sysReportMsgPush(REPORT_TYPE_SYS_PUSH_MSG,
                MapUtil.builder()
                        .put("traceId", traceId)
                        .put("userId", userId)
                        .put("msgId", msg.getMsgId())
                        .put("messageId", msg.getMessageId())
                        .put("msgType", msg.getType())
                        .put("pushType", pushArgs.getMsgType())
                        .put("pushTime", msg.getTime())
                        .put("serialNumber",pushArgs.getSerialNumber())
                        .put("thumbnailUrl", msg.getThumbnailUrl())
                        .build()
        );
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getReportPushMsgCountOptional()
                .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), pushArgs.getMsgType() + "").inc()));

        String status = STATUS_OK;
        switch (pushArgs.getMsgType()) {
            case 101:
                status = SendFCM(msg, pushArgs);
                break;
            case 102:
                status = SendUMeng(msg, pushArgs);
                break;
            case 103:
                status = SendFcmV1(msg, pushArgs);
                break;
            case 201:
                status = SendiOSDebug(msg, pushArgs);
                break;
            case 202:
                status = SendiOSRelease(msg, pushArgs);
                break;
            default:
                status = "Unknown message type: " + pushArgs.getMsgType();
                break;
        }

        analytic(pushArgs, msg, status);
    }

    @Timed
    private String SendFcmV1(MsgEntityBase msg, PushArgs pushArgs) {
        try {
            boolean result = fcmV1Service.pushMessage(msg, pushArgs);
            return result ? STATUS_OK : "FCMV1_ERROR: Push failed";
        } catch (Exception e) {
            log.error("SendFcmV1 failed: {}", e.getMessage(), e);
            return "FCMV1_ERROR: " + e.getMessage();
        }
    }

    @Timed
    private String SendFCM(MsgEntityBase msg, PushArgs pushArgs) {
        try {
            //有证书，则使用新api推送
            String bundleId = getFcmBundId(pushArgs.getBundleName());
            if(fcmV1Service.checkIfConfigExist(bundleId)){
                return SendFcmV1(msg, pushArgs);
            }

            FirebaseMessage fbm = new FirebaseMessage();
            fbm.setTo(pushArgs.getMsgToken());

            FcmMsgEntity msgEntity = msg.defaultFcmEntity();
            msgEntity.setTitle(msg.getTitle());
            msgEntity.setBody(msg.getBody());
            msgEntity.ParseFrom(msg);
            msgEntity.setTimestamp(msg.getTime());
            msgEntity.setSerialNumber(pushArgs.getSerialNumber());
            msgEntity.setVideoEvent(msg.getVideoEvent());
            fbm.setData(msgEntity);
            String bundId = getFcmBundId(pushArgs.getBundleName());

            fbm.setCollapse_key(msg.getVideoEvent());
            String androidKey = config.getAndroidConfig().get(bundId);
            
            return firebasePublisher.publish(fbm, androidKey, pushArgs);
        } catch (Exception e) {
            log.error("SendFCM failed: {}", e.getMessage(), e);
            return "FCM_ERROR: " + e.getMessage();
        }
    }

    public static String getFcmBundId(String bind) {
        return bind.replace(".", "");
    }

    @Timed
    private String SendUMeng(MsgEntityBase msg, PushArgs pushArgs) {
        try {
            UmengMsgEntity msgEntity = msg.defaultUmengEntity();
            msgEntity.ParseFrom(msg);

            UmengPayload payload = new UmengPayload();
            payload.getBody().setExpand_image(msg.getThumbnailUrl());
            payload.getBody().setTitle(msg.getTitle());
            payload.getBody().setText(msg.getBody());
            payload.getExtra().setData(msgEntity);
            payload.getExtra().setTitle(msg.getTitle());
            payload.getExtra().setBody(msg.getBody());
            payload.getExtra().setTimestamp(PhosUtils.getUTCStamp());
            
            return uMengPublisher.publish(pushArgs, payload);
        } catch (Exception e) {
            log.error("SendUMeng failed: {}", e.getMessage(), e);
            return "UMENG_ERROR: " + e.getMessage();
        }
    }

    @Timed
    private String SendiOSDebug(MsgEntityBase msg, PushArgs pushArgs) {
        try {
            IosMsgEntity msgDO = msg.defaultIosEntity();
            msgDO.ParseFrom(msg);
            msgDO.setVideoEvent(msg.getVideoEvent());
            BaseIosNotification bin = new BaseIosNotification();
            bin.setTitle(msg.getTitle());
            bin.setBody(msg.getBody());
            bin.setMutableContent(1);

            IosMessage im = new IosMessage();
            im.setTo(pushArgs.getMsgToken());
            im.setNotification(bin);
            im.setData(msgDO);
            im.setVideoEvent(msg.getVideoEvent());
            pushArgs.setBundleName(StringUtils.isEmpty(pushArgs.getBundleName()) ? DEFAULT_PACKAGE_VICOO : pushArgs.getBundleName());

            String voipResult = STATUS_OK;
            if (ObjectUtils.equals(msg.getType(), MsgType.DEVICE_CALL_MSG) && !StringUtils.isEmpty(pushArgs.getIosVoipToken())) {
                String voipPushKey = String.join("", "voip_push","#", msg.getTraceId(), ":", pushArgs.getIosVoipToken());
                boolean needPushVoip = redisService == null ? true : redisService.setIfAbsent(voipPushKey, "", 15, TimeUnit.SECONDS);
                if(needPushVoip) {
                    PushArgs pushArgs1 = new PushArgs();
                    BeanUtils.copyProperties(pushArgs, pushArgs1);
                    IosMessage im1 = new IosMessage();
                    BeanUtils.copyProperties(im, im1);
                    pushArgs1.setPushType(PushType.VOIP.getHeaderValue());
                    pushArgs1.setBundleName(String.join(".", pushArgs.getBundleName(), "voip"));
                    im1.setTo(pushArgs.getIosVoipToken());
                    im1.getData().setVoipUUID(UUID.randomUUID());
                    voipResult = iosPublisher.publish(im1, false, pushArgs1);
                }
            }

            String regularResult = iosPublisher.publish(im, false, pushArgs);
            return voipResult.equals(STATUS_OK) ? regularResult : voipResult;
        } catch (Exception e) {
            log.error("SendiOSDebug failed: {}", e.getMessage(), e);
            return "IOS_DEBUG_ERROR: " + e.getMessage();
        }
    }

    @Timed
    private String SendiOSRelease(MsgEntityBase msg, PushArgs pushArgs) {
        try {
            IosMsgEntity msgDO = msg.defaultIosEntity();
            msgDO.ParseFrom(msg);
            msgDO.setSerialNumber(pushArgs.getSerialNumber());
            msgDO.setVideoEvent(msg.getVideoEvent());
            BaseIosNotification bin = new BaseIosNotification();
            bin.setBody(msg.getBody());
            bin.setTitle(msg.getTitle());
            bin.setMutableContent(1);

            IosMessage im = new IosMessage();
            im.setTo(pushArgs.getMsgToken());
            im.setNotification(bin);
            im.setData(msgDO);
            im.setVideoEvent(msg.getVideoEvent());
            pushArgs.setBundleName(StringUtils.isEmpty(pushArgs.getBundleName()) ? DEFAULT_PACKAGE_VICOO : pushArgs.getBundleName());

            String voipResult = STATUS_OK;
            if (!StringUtils.isEmpty(pushArgs.getIosVoipToken()) && ObjectUtils.equals(msg.getType(), MsgType.DEVICE_CALL_MSG)) {
                String voipPushKey = String.join("", "voip_push","#", msg.getTraceId(), ":", pushArgs.getIosVoipToken());
                boolean needPushVoip = redisService == null ? true : redisService.setIfAbsent(voipPushKey, "", 15, TimeUnit.SECONDS);
                if(needPushVoip) {
                    IosMessage im1 = new IosMessage();
                    BeanUtils.copyProperties(im, im1);
                    im1.setTo(pushArgs.getIosVoipToken());
                    im1.getData().setVoipUUID(UUID.randomUUID());
                    PushArgs pushArgs1 = new PushArgs();
                    BeanUtils.copyProperties(pushArgs, pushArgs1);
                    pushArgs1.setPushType(PushType.VOIP.getHeaderValue());
                    pushArgs1.setBundleName(String.join(".", pushArgs.getBundleName(), "voip"));
                    voipResult = iosPublisher.publish(im1, true, pushArgs1);
                }
            }

            String regularResult = iosPublisher.publish(im, true, pushArgs);
            return voipResult.equals(STATUS_OK) ? regularResult : voipResult;
        } catch (Exception e) {
            log.error("SendiOSRelease failed: {}", e.getMessage(), e);
            return "IOS_RELEASE_ERROR: " + e.getMessage();
        }
    }

    public void analytic(PushArgs pushArgs, MsgEntityBase msg, String status) {
        try {
            Map<String, java.lang.Object> map = new HashMap<>();
            map.put("message_id", msg.getMessageId());
            map.put("cx_sn", pushArgs.getSerialNumber());
            map.put("trace_id", msg.getTraceId());
            map.put("tenant_id", pushArgs.getTenantId());
            map.put("user_id", pushArgs.getUserId().toString());
            map.put("message_type", msg.getType().toString());
            map.put("thirdpart_name", pushArgs.getMsgType().toString());
            map.put("push_status", status);
            SnowPlowManager.getInstance().analyticSelfEvent("cloud_send_notification_message", map);

        } catch (Exception e) {
            log.warn("analytics error, msg:{}, pushArgs:{}, e:{}", msg, pushArgs, e);
        }
    }
}

