package com.addx.iotcamera.mq;

import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.bean.domain.user.UserPayEmailDO;
import com.addx.iotcamera.bean.openapi.PaasTenantInfo;
import com.addx.iotcamera.bean.reid.ReIdData;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.enums.user.ShareMessageTypeEnum;
import com.addx.iotcamera.kiss.service.IKissAllocationService;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

import static com.addx.iotcamera.constants.UserConstants.SHARE_MSG_TYPE_KEY;
import static com.addx.iotcamera.enums.SendEmailTypeEnums.FREE_TRIAL_EMAIL;

@Component
@Slf4j
public class MqListener {

    @Resource
    private IKissAllocationService kissAllocationService;

    @Resource
    private PaasTenantConfig paasTenantConfig;

    @Autowired
    private ReIdService reIdService;
    @Resource
    private MailConfirmService mailConfirmService;

    @Resource
    private UserService userService;

    @Resource
    private FreeStorageService freeStorageService;

    @Resource
    private UserVipService userVipService;

    @Resource
    private UserVipActivateService userVipActivateService;

    @Autowired
    @Lazy
    private ProductExchangeCodeService productExchangeCodeService;

    @Autowired
    @Qualifier("heartbeatConnected")
    @Lazy
    private Executor heartbeatConnectedExecutor;

    @Autowired
    private DeviceSettingService deviceSettingService;

    @Resource
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private Device4GService device4GService;

    @Resource
    @Lazy
    private OrderService orderService;

    @Resource
    @Lazy
    private ApplePayService applePayService;

    @Value("${messageSkip:false}")
    private Boolean messageSkip;

    @KafkaListener(topics = "${spring.kafka.topics.heartbeat-connected}", groupId = "iot-service-heartbeat")
    public void handleHeartbeatConnected(ConsumerRecord<String, String> record) {
        heartbeatConnectedExecutor.execute(() -> {
            String serialNumber = record.key();
            JSONObject object = JSON.parseObject(record.value());
            String ip = object.getString("ip");
            String secret = object.getString("secret");
            long timestamp = object.getLongValue("timestamp");
            if ((Math.random() * 100 < 1)) {
                log.info("receive msg from kiss mq key: {} value: {}", record.key(), record.value());
            } else {
                log.debug("receive msg from kiss mq key: {} value: {}", record.key(), record.value());
            }
            kissAllocationService.updateRealIp4(serialNumber, secret, ip, timestamp);
        });
    }

    @KafkaListener(topics = "${spring.kafka.topics.reid-data}", groupId = "iot-service-reid-data")
    public void handleReIdData(ConsumerRecord<String, String> record) {
        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());
        log.info("receive msg from ai mq key: {} value: {}", record.key(), record.value());
        ReIdData reIdData = JSON.parseObject(record.value(), ReIdData.class);

        PrometheusMetricsUtil.setMetricNotExceptionally(()->PrometheusMetricsUtil.getReidDataEventCountOptional().get().labels(PrometheusMetricsUtil.getHostName()).inc());

        long startTimestamp = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(reIdData.getLabels())) {
            reIdService.save(reIdData);
        }
        if (!CollectionUtils.isEmpty(reIdData.getDeletedImages())) {
            reIdService.deleteImagesFromAlgo(reIdData);
        }

        PrometheusMetricsUtil.setMetricNotExceptionally(()->PrometheusMetricsUtil.getReidDataEventConsumerCostTimeHistogramOptional().get().labels(PrometheusMetricsUtil.getHostName()).observe(System.currentTimeMillis() - startTimestamp));
    }

    /**
     * 消费付费邮件
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.topics.emailSend}", groupId = "iot-service-email-end")
    public void handleSendToUserEmail(ConsumerRecord<String, String> record) {
        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());
        JSONObject message = JSON.parseObject(record.value());
        log.info("handleSendToUserEmail receive msg from emailSend mq key: {} value: {}", record.key(), message);
        UserPayEmailDO userPayEmailDO = JSON.parseObject(message.toJSONString(),UserPayEmailDO.class);
        mailConfirmService.sendToUserPaymentEmail(userPayEmailDO.getUserId(),userPayEmailDO.getSendEmailTypeEnums());
        log.info("handleSendToUserEmail 发送邮件后续处理 userId {}",userPayEmailDO.getUserId());


        switch (userPayEmailDO.getSendEmailTypeEnums()){
            case SIGNIN_PAY_EMAIL:
                userPayEmailDO.setSendEmailTypeEnums(FREE_TRIAL_EMAIL);
                userService.userReminderFreeTrialEmail(userPayEmailDO,true);
                userService.userReminderFreeTrialEmail(userPayEmailDO,false);
                break;
        }
    }


    /**
     * 监听用户套餐设备刷新-创建账号处理
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.topics.user-tier-device}", groupId = "iot-service-user-tier-device-${spring.profiles.active}",
            autoStartup = "${iot.consumer:false}", id = "mq-listener-user-tier-device-${spring.profiles.active}")
    public void handleUserTierDeviceListener(ConsumerRecord<String, String> record) {
        JSONObject message = JSON.parseObject(record.value());
        try{
            this.consumerRafrashTierDeviceMessage(message);
        }catch (Exception e){
            log.error("handleUserTierDeviceListener consumer error message"+message,e);
        }finally{
            MDC.clear();
        }
    }

    /**
     * 监听用户套餐设备刷新-定时任务处理
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.topics.user-tier-device-xxl}", groupId = "iot-service-user-tier-device-xxl",
            autoStartup = "${iot.consumer:false}", id = "mq-listener-user-tier-device-xxl",
            concurrency = "${topicconcurrentcy:5}")
    public void handleUserTierDeviceListenerXxl(ConsumerRecord<String, String> record) {
        if(messageSkip){
            // 消息跳过不做处理
            return;
        }
        try{
            JSONObject message = JSON.parseObject(record.value());
            this.consumerRafrashTierDeviceMessage(message);
        }catch (Exception e){
            log.error("handleUserTierDeviceListenerXxl consumer error message",e);
        }finally{
            MDC.clear();
        }
    }

    private void consumerRafrashTierDeviceMessage(JSONObject message){
        MDC.clear();
        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());

        Integer userId = message.getInteger("userId");
        log.debug("handleUserTierDeviceListenerXxl receive msg mq value {}", message);
        boolean correctMessage = message.containsKey(SHARE_MSG_TYPE_KEY);
        if(!correctMessage){
            log.error("handleUserTierDeviceListener message error {}",message);
            return;
        }
        String tenantId = message.getString("tenantId");
        Integer typeCode = message.getInteger(SHARE_MSG_TYPE_KEY);
        ShareMessageTypeEnum typeEnum = ShareMessageTypeEnum.codeOf(typeCode);
        switch (typeEnum){
            case USER_TIER_DEVICE:
                this.refrashUserTierDevice(userId,tenantId);
                break;
        }
    }


    /**
     * 刷新用户套餐下设备
     * @param userId
     * @param tenantId
     */
    private void refrashUserTierDevice(Integer userId,String tenantId){
        final PaasTenantInfo paasTenantInfo = paasTenantConfig.getPaasTenantInfo(tenantId);
        if (paasTenantInfo.getEnableDeviceVip() || paasTenantInfo.getEnablePaasUserVip()) {
            freeStorageService.getOrCreateFreeUserVip(userId, paasTenantInfo.getOldUserFreeTierId(), PhosUtils.getUTCStamp()); // 送paas老用户
        }

        // 将用户失效、未开始的vip记录取消active标记
        userVipService.updateActiveExpire(userId);

        //获取用户套餐信息
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, "en", tenantId);

        // 标记套餐生效
        userVipActivateService.executeActivateUserVip(userVipTier,userId);
    }

    @KafkaListener(topics = "${spring.kafka.topics.sim-change}", groupId = "iot-service-sim-change", autoStartup = "${iot.consumer:true}", id = "mq-listener-sim-change")
    public void handle4GSimUpdateEvent(ConsumerRecord<String, String> record) {

        JSONObject message = JSON.parseObject(record.value());
        log.info("handle4GSimUpdateEvent receive msg mq key {} value {}", record.key(), message);

        MDC.put(MDCKeys.REQUEST_ID, StringUtils.isBlank(message.getString("requestId")) ? IDUtil.uuid() : message.getString("requestId"));

//        Integer sn = message.getInteger("userId");
        String sn = message.getString("serialNumber");

        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(sn);
        if (device4GSimDO != null) {
            userTierDeviceService.checkDevice4GTierServiceType(sn);

            deviceSettingService.pushSettingMsg(sn);
        }
    }



    /**
     * 监听用户ios订阅订单
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.topics.user-ios-sub-order}", groupId = "iot-service-user-ios-sub-order", autoStartup = "${iot.consumer:true}", id = "mq-listener-user-ios-sub-order")
    public void handleUserIosSubOrderListener(ConsumerRecord<String, String> record) throws Exception {
        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());

        JSONObject message = JSON.parseObject(record.value());
        log.debug("handleUserIosSubOrderListener receive msg mq key {} value {}", record.key(), message);

        String correctMessage = message.getString(SHARE_MSG_TYPE_KEY);
        if(!org.springframework.util.StringUtils.hasLength(correctMessage)){
            log.error("handleUserTierDeviceListener message error {}",message);
            return;
        }

        Long orderId = message.getLong("orderId");
        OrderDO orderDO = orderService.queryOrderInfoByIdReadOnly(orderId);
        applePayService.appleOrderResult(orderDO,correctMessage);
    }
}
