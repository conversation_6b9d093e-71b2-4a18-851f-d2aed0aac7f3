package com.addx.iotcamera.util;

import com.addx.iotcamera.config.app.AppCodeTemplateConfig;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static org.addx.iot.common.constant.AppConstants.APP_LANGUAGE_EN;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;

/**
 * <AUTHOR>
 */
@Component
public class MessageUtil {
    private static final Logger logger = LoggerFactory.getLogger(MessageUtil.class);

    @Autowired
    private IAcsClientUtil iAcsClientUtil;

    @Autowired
    private AppCodeTemplateConfig appCodeTemplateConfig;

    /**
     * 阿里云短信通道
     *
     * @param phone
     * @param confirmCode
     */
    public boolean sendCodePhone(String phone, String confirmCode, String language, String tenantId) throws ClientException {
        boolean result = false;
        String templateCode = this.queryCodeTemplate(language);
        logger.info("sendCodePhone,phone:{},confirmCode:{},language:{},tenantId:{},templateCode:{}", phone, confirmCode, language, tenantId, templateCode);

        IAcsClient client = iAcsClientUtil.getIAcsClient(tenantId);

        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("dysmsapi.aliyuncs.com");
        request.setVersion("2017-05-25");
        request.setAction("SendSms");
        request.putQueryParameter("RegionId", "cn-hangzhou");
        request.putQueryParameter("PhoneNumbers", phone);
        String signName = appCodeTemplateConfig.getSignName().containsKey(tenantId) ? appCodeTemplateConfig.getSignName().get(tenantId) :
                appCodeTemplateConfig.getSignName().get(TENANTID_VICOO);
        request.putQueryParameter("SignName",  signName);
        request.putQueryParameter("TemplateCode", templateCode);
        JSONObject obj = new JSONObject();
        obj.put("code", confirmCode);
        request.putQueryParameter("TemplateParam", obj.toJSONString());
        CommonResponse response = client.getCommonResponse(request);
        logger.info("sendCodePhone response:{}", response.toString());
        if (response.getHttpStatus() == 200) {
            JSONObject resultData = JSONObject.parseObject(response.getData());
            if (resultData.get("Message").equals("OK")) {
                result = true;
                logger.info("sendCodePhone phone:{} send success", phone);
            }
        }
        return result;
    }

    private String queryCodeTemplate(String language){
        String templateLanguage = appCodeTemplateConfig.getTemplate().containsKey(language) ? language : APP_LANGUAGE_EN;
        return appCodeTemplateConfig.getTemplate().get(templateLanguage);
    }
}
