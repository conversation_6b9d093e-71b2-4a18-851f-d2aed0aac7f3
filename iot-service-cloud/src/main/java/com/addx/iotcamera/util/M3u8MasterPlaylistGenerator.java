package com.addx.iotcamera.util;

import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.video.ResolutionInfo;
import com.addx.iotcamera.constants.VideoResolutionConstants;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.VideoTypeEnum;

import java.io.PrintWriter;
import java.util.List;

/**
 * M3U8 Master Playlist生成器
 * 用于生成包含多分辨率信息的主播放列表
 *
 * <AUTHOR>
 * @date 2024-12-XX
 */
@Slf4j
public class M3u8MasterPlaylistGenerator {

    /**
     * 生成Master Playlist（带实际带宽计算）
     * @param traceId 视频追踪ID
     * @param resolutions 可用分辨率列表
     * @param slicesProvider 切片数据提供者
     * @param playListType 播放列表类型
     * @param fillVacancy 是否填充空缺
     * @param token 访问token（必需，用于子播放列表认证）
     * @param baseUrl 基础URL（可选，用于生成绝对URL）
     * @param writer 输出流
     */
    public static void generateMasterPlaylistWithBandwidth(String traceId, List<ResolutionInfo> resolutions,
                                                          SlicesProvider slicesProvider,
                                                          int playListType, boolean fillVacancy, 
                                                          String token, String baseUrl, PrintWriter writer) {
        writer.println("#EXTM3U");
        writer.println("#EXT-X-VERSION:6");

        // 按分辨率从高到低排序
        resolutions.sort((r1, r2) -> {
            int bandwidth1 = getDefaultBandwidth(r1.getResolution());
            int bandwidth2 = getDefaultBandwidth(r2.getResolution());
            return Integer.compare(bandwidth2, bandwidth1); // 降序
        });

        // 为每个分辨率生成EXT-X-STREAM-INF条目
        for (ResolutionInfo resolution : resolutions) {
            String resolutionValue = resolution.getResolution();

            // 获取该分辨率的切片信息用于计算带宽
            List<VideoSliceDO> slices = slicesProvider.getSlicesByResolution(resolutionValue);
            if (slices.isEmpty()) {
                continue;
            }

            // 计算带宽（优先使用实际计算，fallback到默认值）
            int bandwidth = calculateBandwidth(slices, resolutionValue);

            // 获取编码格式
            String codecsValue = getCodecsValue(slices);

            // 生成EXT-X-STREAM-INF标签
            String streamInf = "#EXT-X-STREAM-INF:" + "BANDWIDTH=" + bandwidth +
                    ",RESOLUTION=" + resolutionValue +
                    ",CODECS=\"" + codecsValue + "\"";

            writer.println(streamInf);

            // 生成子播放列表URL
            String playlistUrl = generateSubPlaylistUrl(traceId, resolutionValue, playListType, fillVacancy, token, baseUrl);
            writer.println(playlistUrl);
        }
    }

    /**
     * 计算带宽（简单估算）
     */
    public static int calculateBandwidth(List<VideoSliceDO> slices, String resolution) {
        if (slices.isEmpty()) {
            return getDefaultBandwidth(resolution);
        }

        // 计算平均文件大小和时长
        long totalSize = slices.stream().mapToLong(slice -> slice.getFileSize() != null ? slice.getFileSize() : 0).sum();
        double totalDuration = slices.stream().mapToDouble(slice -> slice.getPeriod() != null ? slice.getPeriod().doubleValue() : 2.0).sum();

        if (totalDuration > 0) {
            // 带宽 = 文件大小(bytes) * 8 / 时长(seconds)
            return (int) ((totalSize * 8) / totalDuration);
        }

        return getDefaultBandwidth(resolution);
    }

    /**
     * 获取默认带宽（根据分辨率）
     */
    public static int getDefaultBandwidth(String resolution) {
        if (VideoResolutionConstants.is4K(resolution)) {
            return 8000000; // 8Mbps for 4K
        } else if (VideoResolutionConstants.is720P(resolution)) {
            return 2000000; // 2Mbps for 720P
        }  else {
            return 1000000; // 1Mbps default
        }
    }

    /**
     * 根据切片信息获取CODECS值
     * @param slices 切片列表
     * @return CODECS值
     */
    public static String getCodecsValue(List<VideoSliceDO> slices) {
        if (slices.isEmpty()) {
            return "avc1.640028"; // 默认值
        }
        
        // 从第一个有效切片获取编码信息
        for (VideoSliceDO slice : slices) {
            if (slice.getCodec() != null && !slice.getCodec().isEmpty()) {
                return convertCodecToCodecs(slice.getCodec());
            }
        }
        
        return "avc1.640028"; // 默认值
    }

    /**
     * 将codec字段值转换为HLS CODECS值
     * @param codec 编码格式（h264, h265等）
     * @return HLS CODECS值
     */
    public static String convertCodecToCodecs(String codec) {
        if (codec == null || codec.isEmpty()) {
            return "avc1.640028"; // 默认H.264
        }
        
        String codecLower = codec.toLowerCase();
        switch (codecLower) {
            case "h264":
            case "h254": // 兼容可能的拼写错误
                return "avc1.640028"; // H.264 High Profile Level 4.0
            case "h265":
            case "hevc":
                return "hev1.1.6.L93.B0"; // H.265 Main Profile Level 3.1
            default:
                log.warn("Unknown codec: {}, using default H.264", codec);
                return "avc1.640028"; // 默认H.264
        }
    }

    /**
     * 生成子播放列表URL
     * @param traceId 视频追踪ID
     * @param resolution 分辨率
     * @param playListType 播放列表类型
     * @param fillVacancy 是否填充空缺
     * @param token 访问token（必需）
     * @param baseUrl 基础URL（可选，用于生成绝对URL）
     * @return 完整的子播放列表URL
     */
    public static String generateSubPlaylistUrl(String traceId, String resolution, int playListType, boolean fillVacancy, String token, String baseUrl) {
        StringBuilder url = new StringBuilder();
        
        // 如果提供了基础URL，生成绝对URL；否则生成相对URL
        if (baseUrl != null && !baseUrl.isEmpty()) {
            // 确保baseUrl以/结尾
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            // 生成绝对URL：baseUrl + video/download/m3u8/traceId.m3u8
            url.append(baseUrl).append("video/download/m3u8/").append(traceId).append(".m3u8");
        } else {
            // 生成相对URL，播放器会基于Master Playlist的路径自动拼接
            // 由于Master Playlist和子播放列表使用同一个接口（仅通过resolution参数区分）
            // 例如：Master Playlist路径为 /video/download/m3u8/trace123.m3u8
            //      子播放列表相对URL为 trace123.m3u8?resolution=xxx
            //      播放器解析后的完整路径为 /video/download/m3u8/trace123.m3u8?resolution=xxx
            url.append(String.format("%s.m3u8", traceId));
        }
        
        // 必须包含token参数，否则子播放列表请求会失败
        if (token != null && !token.isEmpty()) {
            url.append("?token=").append(token);
            url.append("&resolution=").append(resolution);
        } else {
            url.append("?resolution=").append(resolution);
        }
        
        if (playListType != 0) {
            url.append("&playListType=").append(playListType);
        }
        if (fillVacancy) {
            url.append("&fillVacancy=").append(true);
        }
        return url.toString();
    }
    
    /**
     * 生成子播放列表URL（兼容旧方法，不推荐使用）
     * @deprecated 请使用带baseUrl参数的方法 generateSubPlaylistUrl(String, String, int, boolean, String, String)
     */
    @Deprecated
    public static String generateSubPlaylistUrl(String traceId, String resolution, int playListType, boolean fillVacancy, String token) {
        return generateSubPlaylistUrl(traceId, resolution, playListType, fillVacancy, token, null);
    }
    
    /**
     * 生成子播放列表URL（兼容旧方法，不推荐使用）
     * @deprecated 请使用带token参数的方法 generateSubPlaylistUrl(String, String, int, boolean, String)
     */
    @Deprecated
    public static String generateSubPlaylistUrl(String traceId, String resolution, int playListType, boolean fillVacancy) {
        return generateSubPlaylistUrl(traceId, resolution, playListType, fillVacancy, null);
    }

    /**
     * 切片数据提供者接口
     */
    @FunctionalInterface
    public interface SlicesProvider {
        List<VideoSliceDO> getSlicesByResolution(String resolution);
    }

}
