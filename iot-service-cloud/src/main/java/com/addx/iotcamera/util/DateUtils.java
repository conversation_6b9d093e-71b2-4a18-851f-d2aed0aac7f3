package com.addx.iotcamera.util;

import com.addx.iotcamera.bean.db.ProductDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;

import static org.addx.iot.common.constant.AppConstants.*;

/**
 * Date Utils
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtils {
    public final static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public final static String YYYYMMDDDay = "yyyy.MM.dd";
    public final static String YYYYMMDDDayUs = "dd/MM/yyyy";
    public final static String YYYYMMDDDayCn = "yyyy/MM/dd";
    public final static String YYYYMMDDDayUsEn = "MM/dd/yyyy";
    public final static String YYYYMMDD = "yyyyMMdd";
    public final static String YYYY = "yyyy";
    public final static String YYYY_MM_DD = "yyyy-MM-dd";
    public final static String YYYYMMDDHH = "yyyyMMddHH";

    /**
     * 获取精确到秒的时间戳
     *
     * @param date
     * @return
     */
    public static int getSecondTimestamp(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime() / 1000);
        return Integer.valueOf(timestamp);
    }


    /**
     * 获取几天前的时间
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getDateBefore(Date date, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    /**
     * 获取几天后的时间
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getDateAfter(Date date, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    /**
     * 获取N月后的时间
     *
     * @param date
     * @param month
     * @return
     */
    public static Date getDateAfterMonth(Date date, int month) {
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(Calendar.MONTH, now.get(Calendar.MONTH) + month);

        return now.getTime();
    }

    public static Integer getDateAfterMonthSecond(Integer time,int month){
        Date date = new Date(time.longValue() * 1000);
        Date nextMonthDate = getDateAfterMonth(date,month);
        return (int)(nextMonthDate.getTime()/1000);
    }

    public static String dateToString(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * @param seconds
     * @param format
     * @return
     */
    public static String timeStamp2Date(Integer seconds, String format) {
        if (seconds == null || seconds <= 0) {
            return "";
        }
        if (StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(Long.valueOf(seconds) * 1000));
    }

    /**
     * @param format
     * @return
     */
    public static Integer date2time(String date, String format) throws ParseException {
        if (StringUtils.isEmpty(date)) {
            return 0;
        }
        if (StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);

        return (int) (sdf.parse(date).getTime() / 1000);
    }


    /**
     * 计算结束时间
     *
     * @param effectiveTime
     * @return
     */
    public static long computeTime(long effectiveTime) {
        Date date = new Date(effectiveTime);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);

        return cal.getTimeInMillis();
    }

    /**
     * 时间戳（秒）转utc 时间
     *
     * @param secs
     * @return
     */
    public static String timestampSecs2utcTime(int secs) {
        return Instant.ofEpochSecond(secs).atOffset(ZoneOffset.UTC).toString();
    }

    /**
     * 时间戳（毫秒）转utc 时间
     *
     * @param msecs
     * @return
     */
    public static OffsetDateTime timestampMsecs2utcTime(long msecs) {
        return Instant.ofEpochMilli(msecs).atOffset(ZoneOffset.UTC);
    }

    /**
     * 根据时区获取包含了dst信息的offset
     *
     * @param timezone
     * @return
     */
    public static int getOffset(String timezone) {
        return getOffset(timezone, Instant.now());
    }

    public static int getOffset(String timezone, Instant instant) {
        try {
            if(TIME_ZONE_AMERICA_CIUDAD_JUAREZ.equals(timezone)){
                // America/Ciudad_Juarez 因为暂时不能识别，所以拿 相邻时区 America/Ojinaga 作为参照
                return ZoneId.of(TIME_ZONE_AMERICA_OJINAGA).getRules().getOffset(instant).getTotalSeconds() / 60;
            }else if (TIME_ZONE_AMERICA_OJINAGA.equals(timezone)){
                return (ZoneId.of(TIME_ZONE_AMERICA_OJINAGA).getRules().getOffset(instant).getTotalSeconds() / 60) + 60;
            }else {
                return ZoneId.of(timezone).getRules().getOffset(instant).getTotalSeconds() / 60;
            }
        } catch (Throwable e) {
            // 如果再有不能识别的时区，默认返回伦敦时区信息
            return getOffset(DEFAULT_TIME_ZONE_EUROPE_LONDON, instant);
        }
    }

    /**
     * 根据时区获取 标准时间偏移量
     *
     * @param timezone
     * @return
     */
    public static int getStandardOffset(String timezone) {
        return getStandardOffset(timezone, Instant.now());
    }

    public static int getStandardOffset(String timezone, Instant instant) {
        try {
            if(TIME_ZONE_AMERICA_CIUDAD_JUAREZ.equals(timezone)){
                // America/Ciudad_Juarez 因为暂时不能识别，所以拿 相邻时区 America/Ojinaga 作为参照
                return ZoneId.of(TIME_ZONE_AMERICA_OJINAGA).getRules().getStandardOffset(instant).getTotalSeconds() / 60;
            }else if (TIME_ZONE_AMERICA_OJINAGA.equals(timezone)){
                return (ZoneId.of(timezone).getRules().getStandardOffset(instant).getTotalSeconds() / 60) + 60;
            }else {
                return ZoneId.of(timezone).getRules().getStandardOffset(instant).getTotalSeconds() / 60;
            }
        } catch (Throwable e) {
            // 如果再有不能识别的时区，默认返回伦敦时区信息
            return getStandardOffset(DEFAULT_TIME_ZONE_EUROPE_LONDON, instant);
        }
    }


    public static long initDateByDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTimeInMillis();
    }

    // 10位时间戳转13位时间戳
    public static long toMillis(long time) {
        if (time >= 100_0000_0000L) return time;
        return time * 1000;
    }

    // 计算13位时间戳间隔
    public static long diffMillis(long t1, long t2) {
        return toMillis(t2) - toMillis(t1);
    }

    public static int stringToInt(String timeStr){
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        Date date = null;
        int time;
        try {
            date = format.parse(timeStr);
            time = (int) (date.getTime()/1000);
        } catch (ParseException e) {
            time = 0;
        }
        return time;
    }

    /**
     * 计算结束时间
     *
     * @param productDO
     * @param protection
     * @param effectiveTime
     * @return
     */
    public static Integer computeTime(ProductDO productDO, int protection, int effectiveTime) {
        Integer endTime;
        //赠送天数,不足一天当做一天
        long time = effectiveTime;
        Date date = new Date(time * 1000);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if(productDO != null){
            //增加对应月数
            cal.add(Calendar.MONTH, productDO.getMonth());
        }

        cal.add(Calendar.DATE, protection);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);

        endTime = (int) (cal.getTimeInMillis() / 1000);
        return endTime;
    }

    /**
     * 获取N月后的时间
     *
     * @param date
     * @param month
     * @return
     */
    public static Date getDateAfterDate(Date date, int month,Integer day) {
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.add(Calendar.MONTH, month);
        now.add(Calendar.DATE, day);
        return now.getTime();
    }

    public static String getCurrentDateTime(){
        return new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS).format(new Date());
    }

    /**
     * 计算时间期间
     * @param start
     * @param end
     * @return
     */
    public static int getTimePeriodDay(long start,long end){
        return (int)((end - start) / 1000 / 24 / 60 / 60) + 1;
    }
}
