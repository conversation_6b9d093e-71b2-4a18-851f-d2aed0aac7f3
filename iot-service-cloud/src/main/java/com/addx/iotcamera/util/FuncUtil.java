package com.addx.iotcamera.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class FuncUtil {

    public static <T> List<List<T>> partialToList(Iterable<T> set, int batchSize) {
        return partial(set, batchSize, ArrayList::new, List::add);
    }

    public static <T> List<Set<T>> partialToSet(Iterable<T> set, int batchSize) {
        return partial(set, batchSize, LinkedHashSet::new, Set::add);
    }

    public static <K, V> List<Map<K, V>> partialMap(Map<K, V> map, int batchSize) {
        return partial(map.entrySet(), batchSize, LinkedHashMap::new, (map2, en) -> map2.put(en.getKey(), en.getValue()));
    }

    /**
     * 可迭代集合的元素平分成多份
     *
     * @param iterable    可迭代集合
     * @param batchSize   每份大小，最后一份可能小于batchSize
     * @param constructor 拆分后的集合构造函数
     * @param addFunction 拆分后集合添加元素的方法
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> List<R> partial(Iterable<T> iterable, int batchSize
            , Supplier<R> constructor, BiConsumer<R, T> addFunction) {
        assert iterable != null;
        assert batchSize > 0;
        assert constructor != null;
        assert addFunction != null;
        List<R> lists = new LinkedList<>();
        R list = constructor.get();
        int size = 0;
        for (T item : iterable) {
            addFunction.accept(list, item);
            size++;
            if (size == batchSize) {
                lists.add(list);
                list = constructor.get();
                size = 0;
            }
        }
        if (size > 0) {
            lists.add(list);
        }
        return lists;
    }

    public static <T> List<T> subtractToList(Collection<T> set1, Collection... setList) {
        if (CollectionUtils.isEmpty(set1)) return new LinkedList<>();
        HashSet<T> set3 = new LinkedHashSet<>(set1);
        for (Collection set2 : setList) {
            if (set2 == null) continue;
            set3.removeAll(set2);
        }
        return new ArrayList<>(set3);
    }

    public static <T> List<T> retainToList(Collection<T> set1, Collection... setList) {
        HashSet<T> set3 = new LinkedHashSet<>(set1);
        for (Collection set2 : setList) {
            set3.retainAll(set2);
        }
        return new ArrayList<>(set3);
    }

    /**
     * 遍历递归数据结构
     *
     * @param <T>
     * @param iterable
     * @param childrenGetter 获取子集合的方法
     * @param eachHandler    循环体。参数1是所有父级对象，参数2是当前对象。返回true表示继续遍历，返回false表示终止遍历
     * @return
     */
    public static <T> boolean foreachRecursiveStruct(Iterable<T> iterable, Function<T, Iterable<T>> childrenGetter, BiFunction<LinkedList<T>, T, Boolean> eachHandler) {
        return foreachRecursiveStruct(iterable, new LinkedList<>(), childrenGetter, eachHandler);
    }

    private static <T> boolean foreachRecursiveStruct(Iterable<T> iterable, LinkedList<T> parents, Function<T, Iterable<T>> childrenGetter, BiFunction<LinkedList<T>, T, Boolean> eachHandler) {
        if (iterable == null) return true;
        for (T item : iterable) {
            if (!eachHandler.apply(parents, item)) return false;
            Iterable<T> children = childrenGetter.apply(item);
            parents.addLast(item);
            if (!foreachRecursiveStruct(children, parents, childrenGetter, eachHandler)) return false;
            parents.removeLast();
        }
        return true;
    }

    public static <T, K> ImmutableMap<K, T> createImmutableMap(T[] arr, Function<T, K> keyGetter) {
        return createImmutableMap(Arrays.asList(arr), keyGetter, Function.identity());
    }

    public static <T, K, V> ImmutableMap<K, V> createImmutableMap(Iterable<T> iterable, Function<T, K> keyGetter, Function<T, V> valueGetter) {
        ImmutableMap.Builder<K, V> builder = ImmutableMap.builder();
        for (T obj : iterable) {
            builder.put(keyGetter.apply(obj), valueGetter.apply(obj));
        }
        return builder.build();
    }

    /**
     * 递归遍历Map或Collection组成的嵌套结构
     *
     * @param obj
     * @param eachHandler 路径列表，当前遍历的对象
     */
    public static void foreachRecursiveMapOrCollection(Object obj, BiConsumer<LinkedList, Object> eachHandler) {
        foreachRecursiveMapOrCollection(obj, new LinkedList(), eachHandler);
    }

    private static void foreachRecursiveMapOrCollection(Object obj, LinkedList paths, BiConsumer<LinkedList, Object> eachHandler) {
        if (obj == null) {
            return;
        } else if (obj instanceof Map) {
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) obj).entrySet()) {
                paths.addLast(entry.getKey());
                foreachRecursiveMapOrCollection(entry.getValue(), paths, eachHandler);
                paths.removeLast();
            }
        } else if (obj instanceof Collection) {
            int i = 0;
            for (Object value : ((Collection) obj)) {
                paths.addLast(i++);
                foreachRecursiveMapOrCollection(value, paths, eachHandler);
                paths.removeLast();
            }
        } else {
            eachHandler.accept(paths, obj);
        }
    }

    public static <T> T[] copyArray(T[] array) {
        return array != null ? Arrays.copyOf(array, array.length) : null;
    }

    /**
     * 在map中查找key>=minKey的最小key对应的value
     */
    public static <V> Optional<V> getValueByMinKey(Map<Integer, V> map, Integer minKey) {
        if (MapUtils.isEmpty(map) || minKey == null) return Optional.empty();
        return map.entrySet().stream().filter(it -> it.getKey() >= minKey)
                .min(Comparator.comparingInt(it -> it.getKey())).map(it -> it.getValue());
    }

    public static <K, V> Stream<K> getKeyByValue(Map<K, V> map, V value) {
        return map.entrySet().stream().filter(it -> Objects.equals(value, it.getValue())).map(it -> it.getKey());
    }

    public static <K,T> Stream<K> findKeyByProperty(Map<K, List<T>> map, String propertyName, Object propertyValue) {
        return map.entrySet().stream()
                .filter(entry -> entry.getValue().stream()
                        .anyMatch(item -> getPropertyValue(item, propertyName).equals(propertyValue)))
                .map(Map.Entry::getKey);
    }

    private static <T> Object getPropertyValue(T obj, String propertyName) {
        try {
            Field field = obj.getClass().getDeclaredField(propertyName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }
    public static void incrBy(int[] totalResults, int[] results) {
        for (int i = 0; i < totalResults.length && i < results.length; i++) {
            totalResults[i] += results[i];
        }
    }

    public static <T> List<T> concat(List<T>... lists) {
        LinkedList<T> result = new LinkedList<>();
        for (List<T> list : lists) result.addAll(list);
        return result;
    }

    public static <T, R> Optional<R> reduce(List<T> list, Function<T, R> getter, BinaryOperator<R> operator) {
        try {
            if (list == null) return Optional.empty();
            return list.stream().filter(Objects::nonNull).map(getter).filter(Objects::nonNull).reduce(operator);
        } catch(Throwable e) {
            log.error("FuncUtil reduce error!", e);
            return Optional.empty();
        }
    }

    @FunctionalInterface
    public interface ForeachJson {
        void handle(Object parent, String parentKey, Object val);
    }

    /**
     * 遍历json
     *
     * @param val
     */
    public static void foreachJson(Object val, ForeachJson foreachJson) {
        foreachJson(null, "$", val, foreachJson);
    }

    private static void foreachJson(Object parent, String parentKey, Object val, ForeachJson foreachJson) {
        if (val instanceof JSONObject) {
            JSONObject obj = (JSONObject) val;
            for (String key : new ArrayList<>(obj.keySet())) {
                foreachJson(obj, parentKey + "." + key, obj.get(key), foreachJson);
            }
        } else if (val instanceof JSONArray) {
            JSONArray arr = (JSONArray) val;
            for (int i = 0; i < arr.size(); i++) {
                foreachJson(arr, parentKey + ".[" + i + "]", arr.get(i), foreachJson);
            }
        } else {
//            if (parent != null && val != null) {
//                foreachJson.handle(parent, parentKey, val);
//            }
        }
        if (parent != null) {
            foreachJson.handle(parent, parentKey, val);
        }
    }

    /**
     * 遍历所有可能的组合
     * eg: [A,B,C] -> [[A],[B],[C],[A,B],[A,C],[B,C],[A,B,C]]
     */
    public static <T> void foreachCombine(T[] arr, Consumer<List<T>> handler) {
        foreachCombine(arr, 0, new LinkedList<>(), handler);
    }

    private static <T> void foreachCombine(T[] arr, int start, LinkedList<T> list, Consumer<List<T>> handler) {
        if (start >= arr.length) {
            handler.accept(new ArrayList<>(list));
            return;
        }
        foreachCombine(arr, start + 1, list, handler);
        list.add(arr[start]);
        foreachCombine(arr, start + 1, list, handler);
        list.removeLast();
    }

    /**
     * 根据类名和其父类创建一个对象
     *
     * @param parentCls
     * @param clsName
     * @param <T>
     * @return
     */
    public static <T> T instanceByClassName(Class<T> parentCls, String clsName) {
        String parentClsName = parentCls.getName();
        Class policyCls;
        try {
            policyCls = Class.forName(clsName);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("instanceByClassName 指定类名找不到!parentClsName=" + parentClsName + ",clsName=" + clsName);
        }
        if (!parentCls.isAssignableFrom(policyCls)) {
            throw new RuntimeException("instanceByClassName 指定类不是的实现类!parentClsName=" + parentClsName + ",clsName=" + clsName);
        }
        try {
            return (T) policyCls.newInstance();
        } catch (Exception e) {
            throw new RuntimeException("instanceByClassName 指定类无法实例化!parentClsName=" + parentClsName + ",clsName=" + clsName);
        }
    }

    public static <E> E removeFirst(Iterable<E> list, Predicate<? super E> filter) {
        Objects.requireNonNull(filter);
        final Iterator<E> each = list.iterator();
        while (each.hasNext()) {
            E ele = each.next();
            if (filter.test(ele)) {
                each.remove();
                return ele;
            }
        }
        return null;
    }

    public static <T> Iterable<T> iterable(Iterable<T> collection) {
        if (collection == null) return Collections.emptyList();
        return collection;
    }

    /**
     * 转换成标准json字符串，用于比较两个json对象内容是否一样
     * 对象按key排序
     * 数组按value字符串排序
     *
     * @param root
     * @return
     */
    public static String toNormalizingJsonString(Object root) {
        if (root == null) {
            return "null";
        } else if (root instanceof String) {
            return "\"" + root + "\"";
        } else if (root instanceof Map) {
            Map<String, Object> obj = (Map<String, Object>) root;
            return obj.keySet().stream().sorted().map(key -> {
                String value = toNormalizingJsonString(obj.get(key));
                return "\"" + key + "\":" + value;
            }).collect(Collectors.joining(",", "{", "}"));
        } else if (root instanceof Collection) {
            Collection<Object> arr = (Collection<Object>) root;
            return arr.stream().map(it -> toNormalizingJsonString(it)).sorted()
                    .collect(Collectors.joining(",", "[", "]"));
        } else {
            return root.toString();
        }
    }

    // 参数中的工厂方法只执行一次
    public static <T> Supplier<Optional<T>> getOnce(Supplier<T> factory) {
        final Optional<T>[] ref = new Optional[1];
        return () -> (ref[0] == null ? (ref[0] = Optional.ofNullable(factory.get())) : ref[0]);
    }

    public static <T> Supplier<List<T>> getListOnce(Supplier<List<T>> factory) {
        return () -> getOnce(factory).get().orElseGet(Collections::emptyList);
    }

    public static <T> Supplier<Set<T>> getSetOnce(Supplier<Set<T>> factory) {
        return () -> getOnce(factory).get().orElseGet(Collections::emptySet);
    }

    public static <K, V> Supplier<Map<K, V>> getMapOnce(Supplier<Map<K, V>> factory) {
        return () -> getOnce(factory).get().orElseGet(Collections::emptyMap);
    }

    public static <T, V> T findFirst(Collection<T> list, Function<T, V> getter, V value) {
        if (CollectionUtils.isEmpty(list)) return null;
        return list.stream().filter(it -> Objects.equals(getter.apply(it), value)).findFirst().orElse(null);
    }

    /**
     * 按 beans的顺序获取值
     */
    public static <T> String getFirstNotBlank(Iterable<T> beans, Function<T, String> getter) {
        return getFirstNotBlank(beans, getter, null);
    }

    public static <T> String getFirstNotBlank(Iterable<T> beans, Function<T, String> getter, String defaultVal) {
        String val;
        for (T bean : beans) {
            if (bean != null && StringUtils.isNotBlank(val = getter.apply(bean))) return val;
        }
        return defaultVal;
    }

    public static <T, V> V getFirstNotNull(Iterable<T> beans, Function<T, V> getter) {
        return getFirstNotNull(beans, getter, null);
    }

    public static <T, V> V getFirstNotNull(Iterable<T> beans, Function<T, V> getter, V defaultVal) {
        V val;
        for (T bean : beans) {
            if (bean != null && (val = getter.apply(bean)) != null) return val;
        }
        return defaultVal;
    }

    /**
     * 按顺序移除元素，直到满足条件
     */
    public static <T> int removeUntil(Iterable<T> list, Predicate<T> predicate) {
        int num = 0;
        Iterator<T> iter = list.iterator();
        while (iter.hasNext()) {
            if (predicate.test(iter.next())) break;
            iter.remove();
            num++;
        }
        return num;
    }

    /**
     * 移除所有符合条件的元素
     */
    public static <E> int removeIf(Iterable<E> list, Predicate<? super E> filter) {
        int num = 0;
        final Iterator<E> each = list.iterator();
        while (each.hasNext()) {
            if (filter.test(each.next())) {
                each.remove();
                num++;
            }
        }
        return num;
    }

    public static String getStackLog(int depth) {
        return Arrays.stream(Thread.currentThread().getStackTrace())
                .map(stack -> stack.getClassName() + "," + stack.getMethodName() + "," + stack.getLineNumber())
                .limit(depth).collect(Collectors.joining("\n"));
    }

    /**
     * 合并多个列表，根据主键去重，过滤掉null元素或主键为null的元素
     *
     * @param lists    多个列表
     * @param onlyKeyGetter 唯一键getter方法
     * @return 合并后的列表
     */
    public static <T, K> List<T> mergeToList(Iterable<Iterable<T>> lists, Function<T, K> onlyKeyGetter) {
        Set<K> set = new HashSet<>();
        List<T> mergedList = new ArrayList<>();
        for (final Iterable<T> list : lists) {
            for (final T item : list) {
                if (item == null) continue;
                final K onlyKey = onlyKeyGetter.apply(item);
                if (onlyKey == null || !set.add(onlyKey)) continue;
                mergedList.add(item);
            }
        }
        return mergedList;
    }

    /**
     * 检查对象列表中序号是否是从0到N
     *
     * @param list 对象列表，任意顺序
     * @return
     */
    public static <T> boolean checkIndexesCompletely(List<T> list, Function<T, Integer> indexGetter, Function<T, Boolean> isLastGetter) {
        if (CollectionUtils.isEmpty(list)) return false;
        boolean[] exists = new boolean[list.size()]; // default: false
        boolean hasLast = false;
        for (final T item : list) {
            if (item == null) continue;
            final Integer index = indexGetter.apply(item);
            if (index == null || index < 0 || index >= list.size() || exists[index]) return false;
            exists[index] = true;
            hasLast |= Boolean.TRUE.equals(isLastGetter.apply(item));
        }
        return hasLast;
    }

    public static <B, T> List<T> extractDistinct(Collection<B> list, Function<B, T> getter) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) return Collections.emptyList();
        return list.stream().map(getter).distinct().collect(Collectors.toList());
    }

    @Data
    @AllArgsConstructor
    public static class CompareJsonStructResult {
        private String path;
        private Object value1;
        private Object value2;
    }

    public static List<CompareJsonStructResult> compareJsonStruct(Object obj1, Object obj2, boolean quickEnd) {
        final List<CompareJsonStructResult> results = new LinkedList<>();
        compareJsonStruct(obj1, obj2, "$", results, quickEnd);
        return results;
    }

    private static boolean compareJsonStruct(Object obj1, Object obj2, String path, List<CompareJsonStructResult> results, boolean quickEnd) {
        if (obj1 == null && obj2 == null) return true;
        if (isNullAndEmptyMap(obj1, obj2) || isNullAndEmptyMap(obj2, obj1)) return true;
        if (isNullAndEmptyIterable(obj1, obj2) || isNullAndEmptyIterable(obj2, obj1)) return true;
        if (obj1 == null || obj2 == null) {
            results.add(new CompareJsonStructResult(path, obj1, obj2));
            return false;
        }
        if (isEqualedBoolAndNum(obj1, obj2) || isEqualedBoolAndNum(obj2, obj1)) return true;
        if (isEqualedNumAndStr(obj1, obj2) || isEqualedNumAndStr(obj2, obj1)) return true;
        if (isEqualedNumAndNum(obj1, obj2) || isEqualedNumAndNum(obj2, obj1)) return true;
        if (obj1 instanceof Map && obj2 instanceof Map) {
            final Map jsonObj1 = (Map) obj1, jsonObj2 = (Map) obj2;
            final Set<String> keys = ImmutableSet.<String>builder().addAll(jsonObj1.keySet()).addAll(jsonObj2.keySet()).build();
            for (final String key : keys) {
                final boolean flag = compareJsonStruct(jsonObj1.get(key), jsonObj2.get(key), path + "." + key, results, quickEnd);
                if (!flag && quickEnd) return false;
            }
            return true;
        } else if (obj1 instanceof Iterable && obj2 instanceof Iterable) {
            final Iterator<?> iterator1 = ((Iterable) obj1).iterator(), iterator2 = ((Iterable) obj1).iterator();
            for (int i = 0; iterator1.hasNext() || iterator2.hasNext(); i++) {
                final Object item1 = iterator1.hasNext() ? iterator1.next() : null;
                final Object item2 = iterator2.hasNext() ? iterator2.next() : null;
                final boolean flag = compareJsonStruct(item1, item2, path + ".[" + i + "]", results, quickEnd);
                if (!flag && quickEnd) return false;
            }
            return true;
        } else {
            if (Objects.equals(obj1, obj2)) return true;
            results.add(new CompareJsonStructResult(path, obj1, obj2));
            return false;
        }
    }

    // 2个对象是否都是空Map或null
    private static boolean isNullAndEmptyMap(Object obj1, Object obj2) {
        return obj1 == null && obj2 instanceof Map && ((Map) obj2).isEmpty();
    }

    // 2个对象是否都是空列表或null
    private static boolean isNullAndEmptyIterable(Object obj1, Object obj2) {
        return obj1 == null && obj2 instanceof Iterable && !((Iterable) obj2).iterator().hasNext();
    }

    // 2个对象是否是相同的bool值或int值(大于0视为true)
    private static boolean isEqualedBoolAndNum(Object obj1, Object obj2) {
        return obj1 instanceof Boolean && obj2 instanceof Number && (Boolean) obj1 == (((Number) obj2).intValue() > 0);
    }

    // 2个对象是否是相同的数字值或字符串
    private static boolean isEqualedNumAndStr(Object obj1, Object obj2) {
        return obj1 instanceof Number && obj2 instanceof String && (obj1 + "").equals(obj2);
    }

    private static boolean isEqualedNumAndNum(Object obj1, Object obj2) {
        return obj1 instanceof Number && obj2 instanceof Number && new BigDecimal(obj1 + "").equals(new BigDecimal(obj2 + ""));
    }

    public static JSONObject nameValuePairsToJson(JSONArray nameValuePairs) {
        final JSONObject result = new JSONObject();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(nameValuePairs)) return result;
        for (final Object obj : nameValuePairs) {
            if (obj == null || !(obj instanceof JSONObject)) continue;
            JSONObject item = (JSONObject) obj;
            final String name = item.getString("name");
            if (StringUtils.isBlank(name)) continue;
            result.put(name, item.get("value"));
        }
        return result;
    }

    public static <T> boolean isChange(T oldFlagDO, T newFlagDO) {
        if (newFlagDO == null) return false;
        JSONObject oldFlagObj = oldFlagDO != null ? (JSONObject) JSON.toJSON(oldFlagDO) : new JSONObject();
        JSONObject newFlagObj = (JSONObject) JSON.toJSON(newFlagDO);
        for (String key : newFlagObj.keySet()) {
            if (newFlagObj.get(key) == null) continue; // 新对象没有值的字段忽略
            if (!newFlagObj.get(key).equals(oldFlagObj.get(key))) return true; // 任何一个字段变化都返回true
        }
        return false;
    }

    public static <K, V> TreeMap<K, V> filterTreeMap(TreeMap<K, V> map, BiPredicate<K, V> predicate) {
        TreeMap<K, V> map2 = new TreeMap<>();
        map.forEach((k, v) -> {
            if (!predicate.test(k, v)) return;
            map2.put(k, v);
        });
        return map2;
    }

    public static int abs(int value, int defaultResult) {
        return value == Integer.MIN_VALUE ? defaultResult : Math.abs(value);
    }

    public static <T, R> R[] mapping(T[] arr, Function<T, R> function) {
        return (R[]) Arrays.stream(arr).map(function).toArray(Object[]::new);
    }

}
