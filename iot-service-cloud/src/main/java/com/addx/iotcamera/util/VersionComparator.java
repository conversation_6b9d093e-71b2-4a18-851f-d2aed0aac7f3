package com.addx.iotcamera.util;

import org.springframework.util.StringUtils;

/**
 * 版本比较工具类
 */
public class VersionComparator {
    
    /**
     * 比较两个版本号
     * @param version1 版本1
     * @param version2 版本2
     * @return 0:相等, >0:version1>version2, <0:version1<version2
     */
    public static int compare(String version1, String version2) {
        if (!StringUtils.hasText(version1) || !StringUtils.hasText(version2)) {
            return 0;
        }
        
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");
        
        int maxLength = Math.max(v1Parts.length, v2Parts.length);
        
        for (int i = 0; i < maxLength; i++) {
            int v1Part = i < v1Parts.length ? parseVersionPart(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.length ? parseVersionPart(v2Parts[i]) : 0;
            
            if (v1Part != v2Part) {
                return Integer.compare(v1Part, v2Part);
            }
        }
        
        return 0;
    }
    
    /**
     * 检查版本是否在指定范围内
     * @param version 要检查的版本
     * @param minVersion 最小版本(包含)
     * @param maxVersion 最大版本(包含)
     * @return true:在范围内
     */
    public static boolean isInRange(String version, String minVersion, String maxVersion) {
        if (!StringUtils.hasText(version)) {
            return false;
        }
        
        boolean isAboveMin = true;
        if (StringUtils.hasText(minVersion)) {
            isAboveMin = compare(version, minVersion) >= 0;
        }
        
        boolean isBelowMax = true;
        if (StringUtils.hasText(maxVersion)) {
            isBelowMax = compare(version, maxVersion) <= 0;
        }
        
        return isAboveMin && isBelowMax;
    }
    
    /**
     * 解析版本号部分，支持数字和字母组合
     */
    private static int parseVersionPart(String part) {
        if (!StringUtils.hasText(part)) {
            return 0;
        }
        
        try {
            // 提取数字部分
            StringBuilder numStr = new StringBuilder();
            for (char c : part.toCharArray()) {
                if (Character.isDigit(c)) {
                    numStr.append(c);
                } else {
                    break;
                }
            }
            return numStr.length() > 0 ? Integer.parseInt(numStr.toString()) : 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
} 