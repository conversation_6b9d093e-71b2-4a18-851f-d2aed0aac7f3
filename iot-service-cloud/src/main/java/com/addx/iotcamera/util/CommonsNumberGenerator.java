package com.addx.iotcamera.util;

import org.apache.commons.lang.RandomStringUtils;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 编号生成器
 */
public class CommonsNumberGenerator {





    private static final AtomicLong SEQUENCE = new AtomicLong(1);
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成带5位流水号的发票号
     * 格式：yyyyMMdd + 5位流水号 + 7位随机数字 = 20位总长度
     */
    public static String generateSequentialInvoiceNumber() {
        String datePrefix = LocalDate.now().format(FORMATTER);

        // 5位流水号（从00001开始，到99999后重置）
        long seq = SEQUENCE.getAndIncrement();
        if (seq > 99999) {
            SEQUENCE.set(1);
            seq = 1;
        }
        String sequencePart = String.format("%05d", seq);

        // 7位随机数字
        StringBuilder randomPart = new StringBuilder(7);
        for (int i = 0; i < 7; i++) {
            randomPart.append(RANDOM.nextInt(10));
        }

        return datePrefix + sequencePart + randomPart.toString();
    }

    /**
     * 重置流水号
     */
    public static void resetSequence() {
        SEQUENCE.set(1);
    }

    /**
     * 获取当前流水号
     */
    public static long getCurrentSequence() {
        return SEQUENCE.get();
    }

    /**
     * 设置流水号起始值
     */
    public static void setSequenceStart(long startValue) {
        if (startValue < 1 || startValue > 99999) {
            throw new IllegalArgumentException("流水号起始值必须在1-99999之间");
        }
        SEQUENCE.set(startValue);
    }

    /**
     * 生成支付流水表中的发票号
     * @return
     */
    public static String generateInvoiceNumber() {
        // 日期前缀
        String datePrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 使用Apache Commons生成12位随机字母数字
        String randomPart = RandomStringUtils.randomAlphanumeric(12).toUpperCase();

        return datePrefix + randomPart;
    }
}
