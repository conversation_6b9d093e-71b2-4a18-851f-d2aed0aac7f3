package com.addx.iotcamera.util;

import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * <p>
 * promethus util 提供metrics定义，安全set metrics值避免引发调用代码异常
 */

@Component
@Slf4j
public class PrometheusMetricsUtil implements BeanFactoryPostProcessor {

    private static Optional<Counter> httpMessageNotReadableCounterOptional = Optional.empty();
    private static Optional<Counter> repeatBindCounterOptional = Optional.empty();
    @Value("collectThreadPoolMetrics:false")
    private Boolean collectThreadPoolMetrics;

    private static PrometheusMetricsUtil INSTANCE = null;

    public CollectorRegistry collectorRegistry;

    private static String hostName = "";

    private static final String INSTANCE_LABEL_NAME = "instance";
    private static final String BUCKET_LABEL_NAME = "bucket";


    private static Optional<Histogram> sqlLatencyOptional = Optional.empty();

    private static Optional<Counter> sqlErrorCountOptional = Optional.empty();

    private static Optional<Histogram> serverApiCallCostTimeHistogramOptional = Optional.empty();

    // 单个异步任务的指标
    private static Optional<Histogram> asyncTaskCostTimeHistogramOptional = Optional.empty();
    // 线程池整体允许状况的指标
    private static Optional<Gauge> threadPoolInfoHistogramOptional = Optional.empty();

    // alexa consumer metrics
    private static Optional<Histogram> deviceEventConsumerCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> deviceEventCountOptional = Optional.empty();

    // reid consumer metrics
    private static Optional<Histogram> reidDataEventConsumerCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> reidDataEventCountOptional = Optional.empty();

    // mqtt consumer metrics
    private static Optional<Counter> mqttConsumeCountOptional = Optional.empty();
    private static Optional<Histogram> mqttHandleCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> mqttHandleCountOptional = Optional.empty();
    private static Optional<Counter> mqttHandleErrorCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> videoMsgBeginCount = Optional.empty();
    @Getter
    private static Optional<Counter> videoMsgExeCount = Optional.empty();
    @Getter
    private static Optional<Histogram> videoCacheInitCostTimeHistogram = Optional.empty();
    @Getter
    private static Optional<Histogram> videoCacheResumeCostTimeHistogram = Optional.empty();
    @Getter
    private static Optional<Counter> deviceCacheEvictCount = Optional.empty();
    @Getter
    private static Optional<Counter> createParSuccCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> createParFailCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> createParCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> deleteParSuccCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> deleteParFailCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> deleteParCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Gauge> linodeBucketSizeCountOptional = Optional.empty();
    @Getter
    private static Optional<Gauge> linodeBucketQPSRemainingCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> createLinodeParSuccCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> createLinodeParFailCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> createLinodeParCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> deleteLinodeParSuccCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> deleteLinodeParFailCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> deleteLinodeParCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> listLinodeParSuccCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> listLinodeParFailCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> listLinodeParCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> httpRequestCostTimeHistogramOptional = Optional.empty();
    @Getter
    private Optional<Histogram> httpBodySizeHistogramOptional;
    @Getter
    private Optional<Histogram> timeLineCostTimHistogramOptional;
    @Getter
    private static Optional<Histogram> asyncTaskQueuedCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> asyncTaskExeCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> generateLibraryIdBiggerEndIdCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> generateDeviceBindErrorCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> deviceTaskQueuedCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> deviceTaskExeCostTimeHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> sendMqttCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> reportLogCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> updateUserConfigEndCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> msgPushToReceiveHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> receivedOtaFinishCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> sendSaasAiTaskCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> logLevelCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> devicePirResultCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> videoUploadCompleteCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> videoSliceUploadCountOptional = Optional.empty();
    @Getter
    private static Optional<Counter> reportPushMsgCountOptional = Optional.empty();
    @Getter
    private static Optional<Histogram> videoIoHistogramOptional = Optional.empty();
    @Getter
    private static Optional<Counter> pushMsgCountOptional = Optional.empty();

    // http report event metrics
    private static Optional<Histogram> httpReportEventCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> httpReportEventCountOptional = Optional.empty();
    private static Optional<Counter> httpReportEventErrorCountOptional = Optional.empty();

    // factory query service metrics
    private static Optional<Histogram> factoryDataQueryCostTimeHistogramOptional = Optional.empty();

    private static Optional<Counter> deviceWakeupCounterOptional = Optional.empty();

    private static Optional<Counter> shardingJdbcLibraryDAOMethodCallCounterOptional = Optional.empty();
    private static Optional<Counter> shardingJdbcLibraryDAOMethodCallFailCounterOptional = Optional.empty();
    private static Optional<Histogram> shardingJdbcLibraryDAOMethodCallCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> videoLibraryInsertOrDeleteCounterOptional = Optional.empty();
    private static Optional<Counter> videoLibraryStorageClearTriggerCounterOptional = Optional.empty();
    private static Optional<Histogram> videoLibraryStorageClearCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> videoLibraryStorageClearCounterOptional = Optional.empty();

    // bind metrics
    private static Optional<Counter> bindFromCameraRequestCounterOptional = Optional.empty();
    private static Optional<Counter> bindMqttReponseCounterOptional = Optional.empty();

    // ios push metrics
    private static Optional<Counter> iosPushCounterOptional = Optional.empty();
    private static Optional<Histogram> iosPushCostTimeHistogramOptional = Optional.empty();
    private static Optional<Counter> iosPushFailCounterOptional = Optional.empty();

    // app ai
    private static Optional<Counter> appAiConfigCounterOptional = Optional.empty();
    @Getter
    private static Optional<Counter> asyncWakeupDeviceCounterOptional = Optional.empty();
    @Getter
    private static Optional<Counter> deviceTokenTimeElapsedCounterOptional = Optional.empty();

    // xxl job check metrics
    private static Optional<Counter> xxlJobCheckCounterOptional = Optional.empty();
    private static Optional<Counter> xxlJobCheckFailCounterOptional = Optional.empty();

    private static Optional<Counter> preSignUrlCounterOptional = Optional.empty();

    // video slice batch insert size
    private static Optional<Histogram> videoSliceBatchInsertHistogramOptional = Optional.empty();

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) throws BeansException {
        try {
            this.collectorRegistry = configurableListableBeanFactory.getBean(CollectorRegistry.class);

            initHostName();

            initMetrics();

            INSTANCE = this;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "init collector && metrics failed", e);
        }
    }

    public static String getHostName() {
        return hostName;
    }

    void initHostName() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            PrometheusMetricsUtil.hostName = addr.getHostName(); //获取本机计算机名称
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "get hostName failed", e);
        }
    }

    public void initMetrics() {
        PrometheusMetricsUtil.sqlLatencyOptional = Optional.of(Histogram.build("server_sql_duration_milliseconds", "about sql execute duration")
                .exponentialBuckets(10, 10, 4)
                .labelNames("instance", "method")
                .register(collectorRegistry));
        PrometheusMetricsUtil.sqlErrorCountOptional = Optional.of(Counter.build("server_sql_fail_count", "sql execute fail count")
                .labelNames("instance", "method", "exception", "msg")
                .register(collectorRegistry));

        PrometheusMetricsUtil.serverApiCallCostTimeHistogramOptional = Optional.of(Histogram.build("server_api_call_cost_milliseconds", "call external server api cost time")
                .exponentialBuckets(10, 10, 4)
                .labelNames("instance", "host", "path")
                .register(collectorRegistry));

        PrometheusMetricsUtil.asyncTaskCostTimeHistogramOptional = Optional.of(Histogram.build("async_task_cost_time", "async task cost time")
                .exponentialBuckets(10, 10, 4)
                .labelNames("instance", "poolName", "threadId", "key")
                .register(collectorRegistry));
        PrometheusMetricsUtil.threadPoolInfoHistogramOptional = Optional.of(Gauge.build("thread_pool_info", "thread pool cost time")
                .labelNames("instance", "poolName", "poolType", "key")
                .register(collectorRegistry));

        PrometheusMetricsUtil.deviceEventConsumerCostTimeHistogramOptional = Optional.of(Histogram.build("alexa_event_consumer_cost_milliseconds", "alexa event consumer cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "platform", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deviceEventCountOptional = Optional.of(Counter.build("alexa_event_count", "alexa event count")
                .labelNames("instance", "platform", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.reidDataEventConsumerCostTimeHistogramOptional = Optional.of(Histogram.build("reid_data_event_consumer_cost_milliseconds", "reid data event consumer cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance")
                .register(collectorRegistry));
        PrometheusMetricsUtil.reidDataEventCountOptional = Optional.of(Counter.build("reid_data_event_count", "reid data event count")
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.mqttConsumeCountOptional = Optional.of(Counter.build("mqtt_consume_count", "mqtt consume count")
                .labelNames("instance", "msgSrc")
                .register(collectorRegistry));
        PrometheusMetricsUtil.mqttHandleCostTimeHistogramOptional = Optional.of(Histogram.build("mqtt_handle_cost_milliseconds", "mqtt handle cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "msgSrc", "type", "event")
                .register(collectorRegistry));
        PrometheusMetricsUtil.mqttHandleCountOptional = Optional.of(Counter.build("mqtt_handle_count", "mqtt handle count")
                .labelNames("instance", "msgSrc", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.mqttHandleErrorCountOptional = Optional.of(Counter.build("mqtt_handle_error_count", "mqtt handle error count")
                .labelNames("instance", "msgSrc", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoMsgBeginCount = Optional.of(Counter.build("video_msg_begin_count", "video msg begin")
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoMsgExeCount = Optional.of(Counter.build("video_msg_exe_count", "video msg exe")
                .labelNames("instance", "type", "result")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoCacheInitCostTimeHistogram = Optional.of(Histogram.build("video_cache_init_cost_time_histogram", "video cache init cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "result")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoCacheResumeCostTimeHistogram = Optional.of(Histogram.build("video_cache_resume_cost_time_histogram", "video cache resume cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "result")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deviceCacheEvictCount = Optional.of(Counter.build("device_cache_evict_count", "device cache evict")
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.createParSuccCountOptional = Optional.of(Counter.build("create_par_succ_count", "create par succ")
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.createParFailCountOptional = Optional.of(Counter.build("create_par_fail_count", "create par fail")
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.createParCostTimeHistogramOptional = Optional.of(Histogram.build("create_par_cost_time_histogram", "create par cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteParSuccCountOptional = Optional.of(Counter.build("delete_par_succ_count", "delete par succ")
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteParFailCountOptional = Optional.of(Counter.build("delete_par_fail_count", "delete par fail")
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteParCostTimeHistogramOptional = Optional.of(Histogram.build("delete_par_cost_time_histogram", "delete par cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "bucket")
                .register(collectorRegistry));
        PrometheusMetricsUtil.linodeBucketSizeCountOptional = Optional.of(Gauge.build("linode_bucket_size_gauge", "linode_bucket_size_gauge")
                .labelNames(INSTANCE_LABEL_NAME, BUCKET_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.linodeBucketQPSRemainingCountOptional = Optional.of(Gauge.build("linode_bucket_qps_remaining_gauge", "linode_bucket_qps_remaining_gauge")
                .labelNames(INSTANCE_LABEL_NAME, BUCKET_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.createLinodeParSuccCountOptional = Optional.of(Counter.build("create_linode_par_succ_count", "create par succ")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.createLinodeParFailCountOptional = Optional.of(Counter.build("create_linode_par_fail_count", "create par fail")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.createLinodeParCostTimeHistogramOptional = Optional.of(Histogram.build("create_linode_par_cost_time_histogram", "create par cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteLinodeParSuccCountOptional = Optional.of(Counter.build("delete_linode_par_succ_count", "delete par succ")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteLinodeParFailCountOptional = Optional.of(Counter.build("delete_linode_par_fail_count", "delete par fail")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.deleteLinodeParCostTimeHistogramOptional = Optional.of(Histogram.build("delete_linode_par_cost_time_histogram", "delete par cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.listLinodeParSuccCountOptional = Optional.of(Counter.build("list_linode_par_succ_count", "list par succ")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.listLinodeParFailCountOptional = Optional.of(Counter.build("list_linode_par_fail_count", "list par fail")
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.listLinodeParCostTimeHistogramOptional = Optional.of(Histogram.build("list_linode_par_cost_time_histogram", "list par cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames(INSTANCE_LABEL_NAME)
                .register(collectorRegistry));
        PrometheusMetricsUtil.httpRequestCostTimeHistogramOptional = Optional.of(Histogram.build("http_request_cost_time_histogram", "http request cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "uri", "method", "contentType")
                .register(collectorRegistry));
        httpBodySizeHistogramOptional = Optional.of(Histogram.build("http_body_size_histogram", "http body size histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "uri", "method", "contentType", "type") // "/deviceMsg/pr","json"|"proto","req"|"resp"
                .register(collectorRegistry));
        timeLineCostTimHistogramOptional = Optional.of(Histogram.build("time_line_cost_time_histogram", "time line cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "step", "resultCode")
                .register(collectorRegistry));
        PrometheusMetricsUtil.asyncTaskQueuedCostTimeHistogramOptional = Optional.of(Histogram.build("async_task_queued_cost_time_histogram", "async task queued cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "poolName")
                .register(collectorRegistry));
        PrometheusMetricsUtil.asyncTaskExeCostTimeHistogramOptional = Optional.of(Histogram.build("async_task_exe_cost_time_histogram", "async task exe cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "poolName")
                .register(collectorRegistry));
        PrometheusMetricsUtil.generateLibraryIdBiggerEndIdCountOptional = Optional.of(Counter.build("generate_library_id_bigger_end_id_count", "generate libraryId bigger endId count")
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.generateDeviceBindErrorCountOptional = Optional.of(Counter.build("generate_device_bind_mac_addres_error_count", "generate device bind mac_address error count")
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.deviceTaskQueuedCostTimeHistogramOptional = Optional.of(Histogram.build("device_task_queued_cost_time_histogram", "device task queued cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.deviceTaskExeCostTimeHistogramOptional = Optional.of(Histogram.build("device_task_exe_cost_time_histogram", "device task exe cost time histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.sendMqttCountOptional = Optional.of(Counter.build("send_mqtt_count", "send mqtt count")
                .labelNames("instance", "type", "retained", "qos")
                .register(collectorRegistry));
        PrometheusMetricsUtil.reportLogCountOptional = Optional.of(Counter.build("report_log_count", "report log count")
                .labelNames("instance", "reportType", "reportGroup", "reporter")
                .register(collectorRegistry));
        PrometheusMetricsUtil.updateUserConfigEndCountOptional = Optional.of(Counter.build("update_user_config_end_count", "update user config end count")
                .labelNames("instance", "modelNo", "updateStatus", "reason")
                .register(collectorRegistry));
        PrometheusMetricsUtil.msgPushToReceiveHistogramOptional = Optional.of(Histogram.build("msg_push_to_receive_histogram", "msg push to receive histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "tenantId", "pushType", "phoneModel")
                .register(collectorRegistry));
        PrometheusMetricsUtil.receivedOtaFinishCountOptional = Optional.of(Counter.build("received_ota_finish_count", "received ota finish count")
                .labelNames("instance")
                .register(collectorRegistry));
        PrometheusMetricsUtil.sendSaasAiTaskCountOptional = Optional.of(Counter.build("send_saas_ai_task_count", "send saas ai task count")
                .labelNames("instance")
                .register(collectorRegistry));
        PrometheusMetricsUtil.logLevelCountOptional = Optional.of(Counter.build("log_level_count", "log level count")
                .labelNames("instance", "loglevel", "className", "methodName")
                .register(collectorRegistry));
        PrometheusMetricsUtil.devicePirResultCountOptional = Optional.of(Counter.build("device_pir_result_count", "device pir result count")
                .labelNames("instance", "modelNo", "firmwareType", "firmwareId", "isValidTrigger")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoUploadCompleteCountOptional = Optional.of(Counter.build("video_upload_complete_count", "video upload complete count")
                .labelNames("instance", "serviceName", "bucket", "region", "sliceUploadSuccessIsEmpty")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoSliceUploadCountOptional = Optional.of(Counter.build("video_slice_upload_count", "video upload complete count")
                .labelNames("instance", "serviceName", "bucket", "region", "sliceUploadSuccess", "order", "isLastSlice")
                .register(collectorRegistry));
        PrometheusMetricsUtil.reportPushMsgCountOptional = Optional.of(Counter.build("report_push_msg_count", "report push msg count")
                .labelNames("instance", "pushType")
                .register(collectorRegistry));
        PrometheusMetricsUtil.videoIoHistogramOptional = Optional.of(Histogram.build("video_io_histogram", "video io histogram")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "type", "result")
                .register(collectorRegistry));

        PrometheusMetricsUtil.httpReportEventCostTimeHistogramOptional = Optional.of(Histogram.build("http_report_event_cost_milliseconds", "http report event cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.httpReportEventCountOptional = Optional.of(Counter.build("http_report_event_count", "http report event count")
                .labelNames("instance", "type")
                .register(collectorRegistry));
        PrometheusMetricsUtil.httpReportEventErrorCountOptional = Optional.of(Counter.build("http_report_event_error_count", "http report event error count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.factoryDataQueryCostTimeHistogramOptional = Optional.of(Histogram.build("factory_data_query_cost_milliseconds", "factory data query cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.deviceWakeupCounterOptional = Optional.of(Counter.build("device_wakeup_count", "device wakeup count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.shardingJdbcLibraryDAOMethodCallCounterOptional = Optional.of(Counter.build("video_library_service_method_call_count", "video library service method call count")
                .labelNames("instance", "method")
                .register(collectorRegistry));

        PrometheusMetricsUtil.shardingJdbcLibraryDAOMethodCallFailCounterOptional = Optional.of(Counter.build("video_library_service_method_call_fail_count", "video library service method call fail count")
                .labelNames("instance", "method")
                .register(collectorRegistry));

        PrometheusMetricsUtil.shardingJdbcLibraryDAOMethodCallCostTimeHistogramOptional = Optional.of(Histogram.build("video_library_service_method_call_cost_milliseconds", "video library service method call cost time milliseconds")
                .exponentialBuckets(100, 10, 4)
                .labelNames("instance", "method")
                .register(collectorRegistry));

        PrometheusMetricsUtil.videoLibraryInsertOrDeleteCounterOptional = Optional.of(Counter.build("video_library_insert_delete_count", "video library/video event insert or delete count")
                .labelNames("instance", "type", "handle")
                .register(collectorRegistry));

        PrometheusMetricsUtil.videoLibraryStorageClearTriggerCounterOptional = Optional.of(Counter.build("video_library_storage_clear_trigger_count", "video library storage clear trigger count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.videoLibraryStorageClearCostTimeHistogramOptional = Optional.of(Histogram.build("video_library_storage_clear_call_cost_milliseconds", "video library storage clear cost time milliseconds")
                .exponentialBuckets(1000, 10, 4)
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.videoLibraryStorageClearCounterOptional = Optional.of(Counter.build("video_library_storage_clear_count", "video library/video event storage clear count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.bindFromCameraRequestCounterOptional = Optional.of(Counter.build("bind_from_camera_count", "bind from camera count")
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.bindMqttReponseCounterOptional = Optional.of(Counter.build("bind_mqtt_reponse_count", "bind mqtt response count")
                .labelNames("instance", "code")
                .register(collectorRegistry));

        PrometheusMetricsUtil.iosPushCounterOptional = Optional.of(Counter.build("ios_push_count", "ios push count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.iosPushCostTimeHistogramOptional = Optional.of(Histogram.build("ios_push_cost_milliseconds", "ios push cost time milliseconds")
                .exponentialBuckets(10, 10, 5)
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.iosPushFailCounterOptional = Optional.of(Counter.build("ios_push_fail_count", "ios push fail count")
                .labelNames("instance", "type", "exception")
                .register(collectorRegistry));

        PrometheusMetricsUtil.appAiConfigCounterOptional = Optional.of(Counter.build("app_ai_config_count", "app ai config count")
            .labelNames("instance", "config", "value")
            .register(collectorRegistry));

        PrometheusMetricsUtil.asyncWakeupDeviceCounterOptional = Optional.of(Counter.build("async_wakeup_device_count", "async wakeup device count")
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.deviceTokenTimeElapsedCounterOptional = Optional.of(Counter.build("device_token_time_elapsed_count", "device token time elapsed count")
                .labelNames("instance", "modelNo", "minutesLog10")
                .register(collectorRegistry));

        PrometheusMetricsUtil.xxlJobCheckCounterOptional =  Optional.of(Counter.build("xxl_job_check_count", "xxl job check count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.preSignUrlCounterOptional =  Optional.of(Counter.build("pre_sign_url_count", "pre sign url count")
                .labelNames("type", "bucket")
                .register(collectorRegistry));

        PrometheusMetricsUtil.xxlJobCheckFailCounterOptional =  Optional.of(Counter.build("xxl_job_check_fail_count", "xxl job check fail count")
                .labelNames("instance", "type", "errorMsg")
                .register(collectorRegistry));

        PrometheusMetricsUtil.videoSliceBatchInsertHistogramOptional = Optional.of(Histogram.build("video_slice_batch_insert_list_size", "video slice batch insert size")
                .exponentialBuckets(10, 10, 4)
                .labelNames("instance")
                .register(collectorRegistry));

        PrometheusMetricsUtil.httpMessageNotReadableCounterOptional = Optional.of(Counter.build("http_message_not_readable_count", "http_message_not_readable_count count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.repeatBindCounterOptional = Optional.of(Counter.build("repeat_bind_count", "repeat_bind_count count")
                .labelNames("instance", "type")
                .register(collectorRegistry));

        PrometheusMetricsUtil.pushMsgCountOptional = Optional.of(Counter.build("push_msg_count", "push msg count")
                .labelNames("instance", "tenantId", "event","pushResult")
                .register(collectorRegistry));
    }

    public static Optional<Counter> getPreSignUrlCounterOptional() {
        return preSignUrlCounterOptional;
    }


    public static Optional<Histogram> getSqlLatencyOptional() {
        return sqlLatencyOptional;
    }

    public static Optional<Counter> getSqlErrorCountOptional() {
        return sqlErrorCountOptional;
    }

    public static Optional<Histogram> getServerApiCallCostTimeHistogramOptional() {
        return serverApiCallCostTimeHistogramOptional;
    }

    public static Optional<Histogram> getDeviceEventConsumerCostTimeHistogramOptional() {
        return deviceEventConsumerCostTimeHistogramOptional;
    }

    public static Optional<Counter> getDeviceEventCountOptional() {
        return deviceEventCountOptional;
    }

    public static Optional<Histogram> getReidDataEventConsumerCostTimeHistogramOptional() {
        return reidDataEventConsumerCostTimeHistogramOptional;
    }

    public static Optional<Counter> getReidDataEventCountOptional() {
        return reidDataEventCountOptional;
    }

    public static Optional<Histogram> getMqttHandleCostTimeHistogramOptional() {
        return mqttHandleCostTimeHistogramOptional;
    }

    public static Optional<Counter> getMqttConsumeCountOptional() {
        return mqttConsumeCountOptional;
    }

    public static Optional<Counter> getMqttHandleCountOptional() {
        return mqttHandleCountOptional;
    }

    public static Optional<Counter> getMqttHandleErrorCountOptional() {
        return mqttHandleErrorCountOptional;
    }

    public static Optional<Histogram> getHttpReportEventCostTimeHistogramOptional() {
        return httpReportEventCostTimeHistogramOptional;
    }

    public static Optional<Counter> getHttpReportEventCountOptional() {
        return httpReportEventCountOptional;
    }

    public static Optional<Counter> getHttpReportEventErrorCountOptional() {
        return httpReportEventErrorCountOptional;
    }

    public static Optional<Histogram> getFactoryDataQueryCostTimeHistogramOptional() {
        return factoryDataQueryCostTimeHistogramOptional;
    }

    public static Optional<Counter> getDeviceWakeupCounterOptional() {
        return deviceWakeupCounterOptional;
    }

    public static Optional<Counter> getShardingJdbcLibraryDAOMethodCallCounterOptional() {
        return shardingJdbcLibraryDAOMethodCallCounterOptional;
    }

    public static Optional<Counter> getShardingJdbcLibraryDAOMethodCallFailCounterOptional() {
        return shardingJdbcLibraryDAOMethodCallFailCounterOptional;
    }

    public static Optional<Histogram> getShardingJdbcLibraryDAOMethodCallCostTimeHistogramOptional() {
        return shardingJdbcLibraryDAOMethodCallCostTimeHistogramOptional;
    }

    public static Optional<Counter> getVideoLibraryInsertOrDeleteCounterOptional() {
        return videoLibraryInsertOrDeleteCounterOptional;
    }

    public static Optional<Counter> getVideoLibraryStorageClearTriggerCounterOptional() {
        return videoLibraryStorageClearTriggerCounterOptional;
    }

    public static Optional<Histogram> getVideoLibraryStorageClearCostTimeHistogramOptional() {
        return videoLibraryStorageClearCostTimeHistogramOptional;
    }

    public static Optional<Counter> getVideoLibraryStorageClearCounterOptional() {
        return videoLibraryStorageClearCounterOptional;
    }

    public static Optional<Counter> getIosPushCounterOptional() {
        return iosPushCounterOptional;
    }

    public static Optional<Histogram> getIosPushCostTimeHistogramOptional() {
        return iosPushCostTimeHistogramOptional;
    }

    public static Optional<Counter> getIosPushFailCounterOptional() {
        return iosPushFailCounterOptional;
    }

    public static Optional<Counter> getBindFromCameraRequestCounterOptional () {
        return bindFromCameraRequestCounterOptional;
    }

    public static Optional<Counter> getBindMqttReponseCounterOptional() {
        return bindMqttReponseCounterOptional;
    }

    public static Optional<Counter> getAppAiConfigCounterOptional() {
        return appAiConfigCounterOptional;
    }

    public static Optional<Counter> getXxlJobCheckCounterOptional() {
        return xxlJobCheckCounterOptional;
    }

    public static Optional<Counter> getXxlJobCheckFailCounterOptional() {
        return xxlJobCheckFailCounterOptional;
    }

    public static Optional<Histogram> getVideoSliceBatchInsertHistogramOptional() {
        return videoSliceBatchInsertHistogramOptional;
    }


    public static Optional<Counter> getHttpMessageNotReadableCounterOptional() {
        return httpMessageNotReadableCounterOptional;
    }

    public static Optional<Counter> getRepeatBindCounterOptional() {
        return repeatBindCounterOptional;
    }

    public static void recordAsyncTaskCostTime(Map<String, Object> reportData) {
        if (INSTANCE == null || BooleanUtils.isNotTrue(INSTANCE.collectThreadPoolMetrics)) {
            return;
        }

        setMetricNotExceptionally(() -> asyncTaskCostTimeHistogramOptional.ifPresent(histogram -> {
            String poolName = reportData.get("poolName") + "";
            String threadId = reportData.get("threadId") + "";
            histogram.labels(getHostName(), poolName, threadId, "queuedCost").observe((long) reportData.get("queuedCost"));
            histogram.labels(getHostName(), poolName, threadId, "exeCost").observe((long) reportData.get("exeCost"));
            histogram.labels(getHostName(), poolName, threadId, "totalCost").observe((long) reportData.get("totalCost"));
        }));
    }

    public static void recordThreadPoolInfo(Map<String, Object> reportData) {
        if (INSTANCE == null || BooleanUtils.isNotTrue(INSTANCE.collectThreadPoolMetrics)) {
            return;
        }

        setMetricNotExceptionally(() -> threadPoolInfoHistogramOptional.ifPresent(histogram -> {
            String poolName = reportData.get("poolName") + "";
            String poolType = reportData.get("poolType") + "";
            histogram.labels(getHostName(), poolName, poolType, "runNum").set((int) reportData.get("runNum"));
            histogram.labels(getHostName(), poolName, poolType, "queuedNum").set((int) reportData.get("queuedNum"));
            histogram.labels(getHostName(), poolName, poolType, "activeNum").set((int) reportData.get("activeNum"));
        }));
    }

    public static void setMetricNotExceptionally(Runnable setMetricRunnable) {
        if (setMetricRunnable == null) {
            return;
        }

        try {
            setMetricRunnable.run();
        } catch (Exception e) {
            log.warn("setMetric failed", e); // 不用LogUtil，防止递归
        }
    }

    public static void setMetricNotExceptionally(Consumer<PrometheusMetricsUtil> setMetricRunnable) {
        if (setMetricRunnable == null || INSTANCE == null) {
            return;
        }
        try {
            setMetricRunnable.accept(INSTANCE);
        } catch (Exception e) {
            log.warn("setMetric failed", e); // 不用LogUtil，防止递归
        }
    }

}
