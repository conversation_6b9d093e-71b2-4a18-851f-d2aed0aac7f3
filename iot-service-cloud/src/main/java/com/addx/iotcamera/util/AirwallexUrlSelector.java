package com.addx.iotcamera.util;

import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.config.pay.AirwallexTestUrlWhitelistConfig;
import com.addx.iotcamera.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * Airwallex URL选择器
 * 根据用户邮箱是否在白名单中来选择使用标准URL还是测试URL
 */
@Slf4j
@Component
public class AirwallexUrlSelector {

    @Resource
    private AirwallexTestUrlWhitelistConfig airwallexTestUrlWhitelistConfig;
    
    @Resource
    private UserService userService;

    /**
     * 检查用户是否在Airwallex测试URL白名单中
     * 
     * @param userId 用户ID
     * @return 是否在白名单中
     */
    public boolean isUserInAirwallexTestUrlWhitelist(Integer userId) {
        // 查询白名单
        Set<String> whitelist = airwallexTestUrlWhitelistConfig.queryWhitelist();
        log.debug("Airwallex test URL whitelist: {}", whitelist);

        User user = userService.queryUserById(userId);
        if (user == null) {
            log.warn("User with ID {} not found", userId);
            return false;
        }

        String email = user.getEmail();
        if (email == null || email.isEmpty()) {
            log.warn("User with ID {} has no valid email", userId);
            return false;
        }

        return whitelist.contains(email);
    }

    /**
     * 检查用户是否支付失败的在Airwallex测试URL白名单中
     *
     * @param userId 用户ID
     * @return 是否在白名单中
     */
    public boolean isFailPayUserWhitelist(Integer userId) {
        User user = userService.queryUserById(userId);
        if (user == null) {
            log.warn("User with ID {} not found", userId);
            return false;
        }
        // 查询白名单
        Set<String> failWhitelist = airwallexTestUrlWhitelistConfig.queryPayFailWhitelist();
        log.info("isFailPayUserWhitelist test URL whitelist: {}", failWhitelist);
        return failWhitelist.contains(userId.toString());
    }

    /**
     * 获取适合用户的Airwallex API URL
     * 如果用户的邮箱在白名单中，则使用测试URL
     * 
     * @param userId 用户ID
     * @param paymentConfig 支付配置
     * @return 适合用户的API URL
     */
    public String getApiUrl(Integer userId, PaymentConfig paymentConfig) {
        if (userId != null && isUserInAirwallexTestUrlWhitelist(userId)) {
            log.info("使用Airwallex测试URL，userId={}", userId);
            return paymentConfig.getAirwallexApiTestUrl();
        }
        return paymentConfig.getAirwallexApiBaseUrl();
    }
} 