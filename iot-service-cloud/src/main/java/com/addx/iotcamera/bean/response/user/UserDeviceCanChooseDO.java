package com.addx.iotcamera.bean.response.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserDeviceCanChooseDO {
    private String serialNumber;
    @Schema(title = "是否可选择")
    private Boolean canChoose;

    private String icon;
    private String deviceName;

    @Data
    public static class CurrentTierVerifyResult{
        @Schema(title = "是否可以购买")
        boolean canPurchase;
        @Schema(title = "已指定的套餐下设备")
        List<String> tierDeviceSet;
        @Schema(title = "套餐服务类型")
        Integer tierServiceType;
        @Schema(title = "是否切换billingCycle")
        boolean switchBillingCycle;
    }
}
