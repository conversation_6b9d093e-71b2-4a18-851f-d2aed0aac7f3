package com.addx.iotcamera.bean.app.userorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 购买商品信息
 */
@Data
public class BoughtProductInfo {

    @Schema(title = "当前生效订阅订单")
    private List<BoughtSubscriptionProduct> currentSubscriptionProductList;

    private List<BoughtProduct> boughtProductList;

    /**
     * 是否只绑定了喂鸟器设备
     */
    private Boolean onlyBird;

    @Data
    public static class BoughtSubscriptionProduct {
        private String tierName;
        private Integer nextSubscriptionTime;

        private String boughtFrom;

        private Integer orderStatus;

        @Schema(title = "是否首月免费")
        private Boolean freeTrial;

        @Schema(title = "取消连续订阅状态")
        private Boolean orderCancel;
        @Schema(title = "商品id")
        private Integer productId;
        @Schema(title = "订阅对应套餐id")
        private Integer tierId;
        @Schema(title = "订单id")
        private Long orderId;
        @Schema(title = "回看天数")
        private Integer rollingDays;
    }

    @Data
    public static class BoughtProduct {

        private String additionalTierUid;

        private String tierName;

        private Integer boughtTime;

        private String month;

        private String boughtFrom;

        private Integer orderStatus;
    }
}
