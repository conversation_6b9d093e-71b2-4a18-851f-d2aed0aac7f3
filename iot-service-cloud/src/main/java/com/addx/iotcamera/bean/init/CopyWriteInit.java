package com.addx.iotcamera.bean.init;

import com.addx.iotcamera.bean.apollo.ProductExplan;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.GlobalConfig;
import com.addx.iotcamera.config.apollo.ProductExplainConfig;
import com.addx.iotcamera.config.app.ProductExplanReplaceConfig;
import com.addx.iotcamera.config.pay.AppTierKeyTranslatedConfig;
import com.addx.iotcamera.enums.VideoQuestion;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import de.siegmar.fastcsv.reader.CloseableIterator;
import de.siegmar.fastcsv.reader.CsvReader;
import de.siegmar.fastcsv.reader.CsvRow;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.addx.iot.common.constant.AppConstants.*;


@Configuration
@Slf4j
@DependsOn("productConfig")
public class CopyWriteInit {
    @Autowired
    private GlobalConfig globalConfig;


    @Autowired
    private ProductExplainConfig productExplainConfig;

    @Autowired
    private ProductExplanReplaceConfig productExplanReplaceConfig;

    @Autowired
    private TenantTierConfig tenantTierConfig;

    @Resource
    private AppTierKeyTranslatedConfig appTierKeyTranslatedConfig;

    private final static String appTypeIos = "ios";
    private final static String appTypeAndroid = "android";
    private final static String iosAccount = "App Store / Apple ID";
    private final static String IOS_ACCOUNT_TYPE = "iTunes";
    private final static String defaultTenantId = "Vicoo";


    @Bean
    @DependsOn("appTierKeyTranslatedConfig")
    @Lazy
    public CopyWrite initCopyWrite() {
        CopyWrite config = new CopyWrite();

        String resourcePath = "gitconfig/copywrite/iot.csv";
        try {
            readStreamCsv(resourcePath, config);


            initProductExplain(config);
            VideoQuestion.initQuestionBackText(config);
        } catch (IOException e) {
            com.addx.iotcamera.util.LogUtil.error(log, "initCopyWrite init error:", e);
        }
        return config;
    }

    /**
     * 解析csv 文档为指定结构体
     * @param filePath
     * @param config
     * @throws IOException
     */
    public static void readStreamCsv(String filePath, CopyWrite config) throws IOException {
        Map<String, Map<String, String>> resultMap = Maps.newHashMap();
        //根据namespase 获取指定用途的key, language -> namespace -> key-> fieldValue
        Map<String, Map<String, JSONObject>> namespaceMap = Maps.newHashMap();

        //获取文件流
        ClassPathResource classPathResource = new ClassPathResource(filePath);
        InputStream fileStream = classPathResource.getInputStream();
        CsvReader csvReader = CsvReader.builder().build(new InputStreamReader(fileStream,Charset.forName("UTF-8")));

        int rowLine = 0;
        //第一行,获取语言字段值
        CsvRow firstRow = null;
        int namespaceCell = 0;

        for (CloseableIterator<CsvRow> it = csvReader.iterator(); it.hasNext(); ) {
            CsvRow row = it.next();
            if(rowLine == 0){
                // 获取第一行的语言
                firstRow = row;
                namespaceCell = row.getFieldCount();
                log.info("row has {} field",namespaceCell);
                rowLine++;
                continue;
            }
            rowLine++;
            List<String> fieldValueList = row.getFields();
            if(CollectionUtils.isEmpty(fieldValueList)){
                return;
            }

            String key = fieldValueList.get(0);
            if (StringUtils.isEmpty(key)) {
                com.addx.iotcamera.util.LogUtil.error(log, "initCopyWrite init error readStream 跳过空行! row={}", rowLine);
                continue;
            }

            //language-> fieldValue
            Map<String, String> mapField = Maps.newHashMap();

            String namespace = fieldValueList.get(namespaceCell - 1);
            if(StringUtils.isEmpty(namespace)){
                com.addx.iotcamera.util.LogUtil.error(log, "initCopyWrite init error readStream 跳过空namespace! row={}", rowLine);
                continue;
            }
            for (int j = 1; j < namespaceCell - 1; j++) {
                String languageName = firstRow.getField(j);
                String fieldValue = row.getField(j);

                mapField.put(languageName, fieldValue);

                if (!namespaceMap.containsKey(languageName)) {
                    namespaceMap.put(languageName, new HashMap<>());
                }

                if (!namespaceMap.get(languageName).containsKey(namespace)) {
                    JSONObject copyKey = new JSONObject();
                    namespaceMap.get(languageName).put(namespace, copyKey);
                }
                namespaceMap.get(languageName).get(namespace).put(key, fieldValue);
            }
            resultMap.put(key, mapField);
        }

        config.setConfig(resultMap);
        config.setNamespaceMap(namespaceMap);
    }

    /**
     * 初始化商品描述
     */
    private void initProductExplain(CopyWrite config) {
        Map<String, Map<String, Map<String, ProductExplan>>> message = Maps.newHashMap();
        List<String> languageList = globalConfig.getLanguage().get(TENANTID_VICOO);
        //构建不同公司的map
        for (String tenantId : globalConfig.getTenant()) {
            Map<String, Map<String, ProductExplan>> languageMap = Maps.newHashMap();
            //每个公司下会有不同语言的展示
            for (String language : languageList) {
                Map<String, ProductExplan> appType = Maps.newHashMap();
                //安卓、ios 展示的信息不同
                appType.put(appTypeIos, initProductExplan(tenantId, language, appTypeIos, config));
                appType.put(appTypeAndroid, initProductExplan(tenantId, language, appTypeAndroid, config));

                languageMap.put(language, appType);
            }
            message.put(tenantId, languageMap);
        }
        productExplainConfig.setMessage(message);
    }

    /**
     * 商品类型描述
     *
     * @param tenantId
     * @param language
     * @param appType
     * @param config
     * @return
     */
    private ProductExplan initProductExplan(String tenantId, String language, String appType, CopyWrite config) {
        ProductExplan productExplan = new ProductExplan();
        JSONObject productExplainJson = config.getNamespaceMap().get(language).get("productExplain");

        //普通商品的描述列表
        List<String> common = Lists.newArrayList();
        for (int i = 1; i <= 4; i++) {
            String explainKey = "productExplainCommon" + i;
            // 指定的app 不展示所有的项
            if (productExplanReplaceConfig.getFilterExplan().containsKey(tenantId) && productExplanReplaceConfig.getFilterExplan().get(tenantId).contains(explainKey)) {
                continue;
            }

            // 安卓需要展示特殊文案
            if (productExplanReplaceConfig.getConfig().contains(explainKey) && appType.equals(appTypeAndroid)) {
                explainKey = "productExplainCommon" + APP_TYPE_ANDROID + i;
            }
            if (!productExplainJson.containsKey(explainKey)) {
                log.info("initProductExplan productExplainJson not contains explainKey {}", explainKey);
                continue;
            }
            String explain = productExplainJson.getString(explainKey);
            //替换用户账户类型
            explain = this.getExplan(explain, appType, language, tenantId);
            common.add(explain);
        }
        productExplan.setCommon(common);

        //升级商品的描述列表
        List<String> upgrade = Lists.newArrayList();
        for (int i = 1; i <= 5; i++) {
            String explainKey = "productExplainUpgrade" + i;
            // 指定的app 不展示所有的项
            if (productExplanReplaceConfig.getFilterExplan().containsKey(tenantId) && productExplanReplaceConfig.getFilterExplan().get(tenantId).contains(explainKey)) {
                continue;
            }
            // 安卓需要展示特殊文案
            if (productExplanReplaceConfig.getConfig().contains(explainKey) && appType.equals(appTypeAndroid)) {
                explainKey = "productExplainUpgrade" + APP_TYPE_ANDROID + i;
            }
            if (!productExplainJson.containsKey(explainKey)) {
                log.info("initProductExplan productExplainJson not contains explainKey {}", explainKey);
                continue;
            }
            String upgradeExplain = productExplainJson.getString(explainKey);
            upgradeExplain = this.getExplan(upgradeExplain, appType, language, tenantId);
            upgrade.add(upgradeExplain);
        }
        productExplan.setUpgrade(upgrade);

        String serviceNameKey = appTierKeyTranslatedConfig.queryAppTierServiceName(tenantId);
        String serviceName = StringUtils.hasLength(serviceNameKey) ? config.getConfig().get(serviceNameKey).get(language) : "";
        //订阅商品的构建列表
        List<String> sub = Lists.newArrayList();
        for (int i = 1; i <= 7; i++) {
            String explainKey = "productExplainSub" + i;
            // 指定的app 不展示所有的项
            if (productExplanReplaceConfig.getFilterExplan().containsKey(tenantId) && productExplanReplaceConfig.getFilterExplan().get(tenantId).contains(explainKey)) {
                continue;
            }

            explainKey = this.verifyKeySubstitute(tenantId,explainKey,i,appType);

            if (!productExplainJson.containsKey(explainKey)) {
                log.info("initProductExplan productExplainJson not contains explainKey {}", explainKey);
                continue;
            }
            String subExplain = productExplainJson.getString(explainKey);
            subExplain = this.getExplan(subExplain, appType, language, tenantId);
            if(StringUtils.hasLength(serviceName)){
                subExplain = subExplain.replace(B_SERVICE_NAME_PARAM,serviceName);
            }
            sub.add(subExplain);
        }
        productExplan.setSub(sub);

        List<String> fourG = getFourGList(tenantId, language, appType, productExplainJson);

        productExplan.setFourG(fourG);
        return productExplan;
    }

    /**
     * 特殊app 需要替换文案
     * @param tenantId
     * @param explainKey
     * @param num
     * @param appType
     * @return
     */
    public String verifyKeySubstitute(String tenantId,String explainKey,int num,String appType){
        // 安卓需要展示特殊文案
        if (productExplanReplaceConfig.getConfig().contains(explainKey) && appType.equals(appTypeAndroid)) {
            explainKey = "productExplainSub" + APP_TYPE_ANDROID + num;
        }

        return productExplanReplaceConfig.initSubstituteKey(tenantId,explainKey);
    }

    public @NotNull List<String> getFourGList(String tenantId, String language, String appType, JSONObject productExplainJson) {
        List<String> fourG = productExplainJson.keySet().stream()
                .filter(key -> key.startsWith("productExplainFourG"))
                .filter(key -> productExplanReplaceConfig.getFilterExplan() != null && (!productExplanReplaceConfig.getFilterExplan().containsKey(tenantId) || !productExplanReplaceConfig.getFilterExplan().get(tenantId).contains(key)))
                .filter(key -> !key.endsWith("Android") || (key.endsWith("Android") && appType.equals(appTypeAndroid)))
                .filter(key -> !key.endsWith("Ios") || (key.endsWith("Ios") && appType.equals(appTypeIos)))
                .filter(productExplainJson::containsKey)
                .sorted(Comparator.comparingInt(key -> Integer.parseInt(key.replaceAll("\\D+", ""))))
                .collect(Collectors.toList());
        if (!globalConfig.getAccountType().isEmpty() && !globalConfig.getAccount().isEmpty()) {
            fourG = fourG.stream().map(key -> this
                            .getExplan(productExplainJson.getString(key), appType, language, tenantId)
                            .replace("${APP_NAME}",tenantTierConfig.queryAppNameByTenant(tenantId)))
                    .collect(Collectors.toList());
        }
        return fourG;
    }

    /**
     * 根据公司、账户类型，替换成不同的展示文案
     *
     * @param explain
     * @param appType
     * @param language
     * @param tenantId
     * @return
     */
    private String getExplan(String explain, String appType, String language, String tenantId) {
        String tenant = StringUtils.capitalize(tenantId);

        if (appType.equals(appTypeAndroid)) {
            //安卓国内展示支付宝、微信，美国展示google
            String explainLanguage = language.equals(AppConstants.APP_LANGUAGE_ZH) ? AppConstants.APP_LANGUAGE_ZH :
                    AppConstants.APP_LANGUAGE_EN;
            explain = explain.replace(iosAccount, globalConfig.getAccountType().get(explainLanguage));
            explain = explain.replace(IOS_ACCOUNT_TYPE, globalConfig.getAccount().get(explainLanguage));
        }

        //替换所属公司名称、协议名称
        if (explain.contains(defaultTenantId)) {

            //类似全程看家，需要展示中文名而不是tenantId
            if (tenantTierConfig.getConfig() != null && tenantTierConfig.getConfig().containsKey(tenantId)) {
                tenant = tenantTierConfig.getConfig().get(tenantId);
            }
            //替换保密协议等名称
            explain = explain.replace(defaultTenantId, tenant);
        }

        return explain;
    }
}
