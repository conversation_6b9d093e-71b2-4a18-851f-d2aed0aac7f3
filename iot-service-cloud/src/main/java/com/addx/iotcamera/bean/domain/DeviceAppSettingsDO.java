package com.addx.iotcamera.bean.domain;

import com.addx.iotcamera.bean.device.CoolDownDO;
import com.addx.iotcamera.bean.device.DeviceModelSettingDO;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.factory.DoorBellRingDO;
import com.addx.iotcamera.enums.WhiteLightScintillationEnums;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;

@Data
@Accessors(chain = true)
public class DeviceAppSettingsDO {
    private String serialNumber;
    /**
     * 运动检测开关
     */
    private Integer needMotion;
    /**
     * 运动检测灵敏度
     */
    private Integer motionSensitivity;
    /**
     * 夜视开关
     */
    private Integer needNightVision;
    /**
     * 夜视灵敏度-数值
     */
    private Integer nightVisionSensitivity;
    /**
     * 夜视灵敏度-等级
     */
    private Integer nightThresholdLevel;
    /**
     * 摄像机警铃-开关
     */
    private Integer needAlarm;
    /**
     * 摄像机警铃-时长
     */
    private Integer alarmSeconds;
    /**
     * 是否录像
     */
    private Integer needVideo;
    /**
     * 录像时长
     */
    private Integer videoSeconds;
    /**
     * 设备语言
     */
    private String deviceLanguage;
    /**
     * 时区
     */
    private String timeZone;

    /**
     * 夜视模式,0-红外灯，1-白光灯
     */
    private Integer nightVisionMode;
    /**
     * 白光灯闪烁,0：关，1开
     */
    private Integer whiteLightScintillation;
    /**
     * 白光灯操作,0:无变化；1；用户关闭；2用户开启,(准备废弃)
     */
    private Integer whiteLight;

    /**
     * 设备人型检测,0:关闭；1：开启
     */
    private Integer devicePersonDetect;

    // 0:关闭运动跟踪 1:开启运动跟踪 （说白了就是摄像头可以 "摇头跟踪"）
    private Integer motionTrack;

    // 0:移动追踪 1:人形跟踪
    private Integer motionTrackMode;

    /**
     * 设备支持语言
     */
    private List<String> deviceSupportLanguage;

    /**
     * 设备是否打印log
     *
     * @param autoLog
     * @return
     */
    private Integer autoLog;
    /**
     * 频闪开关
     */
    private Integer antiflickerSwitch;
    /**
     * 频闪值
     */
    private Integer antiflicker;

    /**
     * 视频翻转
     */
    private Integer mirrorFlip;

    // 录像指示灯
    private Integer recLamp;

    // 扬声器音量开关
    private Integer voiceVolumeSwitch;

    // 扬声器音量大小
    private Integer voiceVolume;

    // 警铃音量大小
    private Integer alarmVolume;

    // 哭声检测
    private Integer cryDetect;
    // 哭声检测灵敏度
    private Integer cryDetectLevel;

    /** 拍摄间隔 **/
    private CoolDownDO cooldown;
    // 设备默认编码
    private String defaultCodec;
    // 是否设置defaultCodec为null
    private Boolean clearDefaultCodec;

    /**
     * 当前门铃铃音Key
     */
    private Integer doorBellRingKey;
    /**
     * 设备支持的门铃铃音
     */

    private List<DoorBellRingDO> supportDoorBellRingKey;

    // 直播收音
    private Boolean liveAudioToggleOn;
    // 录像直播收音
    private Boolean recordingAudioToggleOn;
    // 直播对讲音量
    private Integer liveSpeakerVolume;
    // 拆除报警开关
    private Boolean alarmWhenRemoveToggleOn;

    // 机械叮咚开关 0关1开
    private Integer mechanicalDingDongSwitch;
    // 机械叮咚时长
    private Integer mechanicalDingDongDuration;

    private Boolean deviceCallToggleOn;

    // 1、允许充电自动开机开关，默认关，chargeAutoPowerOnSwitch
    private  Integer chargeAutoPowerOnSwitch;
    // 2、允许充电自动开机电量，默认25，chargeAutoPowerOnCapacity
    private  Integer chargeAutoPowerOnCapacity;
//    // 允许充电自动开机电量选项
    private List<Integer> chargeAutoPowerOnCapacityOptions;
    /*** 供电方式 ["solar_panel", "plug_in", "battery_power_only", "wired"] */
    private String powerSource;

    @Schema(title = "是否开启ota自动升级开关")
    private Boolean otaAutoUpgrade;

    /*** 运动检测灵敏度 ["high","mid","low"]*/
    private String pirSensitivity;
    /*** 运动检测录制时长 ["auto","10s","15s","20s"]*/
    private String pirRecordTime;
    /*** sdCard运动检测录制时长 ["10s", "15s", "20s", "60s", "120s", "180s", "auto"]*/
    private String sdCardPirRecordTime;
    /*** 运动检测触发间隔时间 ["10s","30s","60s","180s","300s"]*/
    private String pirCooldownTime;
    /*** 视频分辨率 ["high","mid"]*/
    private String videoResolution;
    /*** 视频抗频闪频率 ["50Hz","60Hz"]*/
    private String videoAntiFlickerFrequency;
    /*** 按铃通知开关 */
    private Boolean doorbellPressNotifySwitch;
    /*** 按铃通知方式 ["phone","push"] */
    private String doorbellPressNotifyType;
    /*** wifi发射功率单位。int值，范围:[1,10] */
    private Integer wifiPowerLevel;

    private String nightVisionSensitivityEnum; /* 26.灯光设置-夜视灵敏度控制*/
    private String nightVisionModeEnum; /* 27.灯光设置-夜视模式调节*/
    private String alarmDurationEnum; /* 28.声音设置-报警时长*/

    /** ai edge 相关字段 start */ 
    // pir 人性检测偏好
    private Boolean detectPersonAi;
    // pir 宠物检测偏好
    private Boolean detectPetAi;
    // pir 汽车检测偏好
    private Boolean detectVehicleAi;
    // pir 包裹检测偏好
    private Boolean detectPackageAi;
    // pir 人脸检测偏好
    private Boolean detectFaceAi;

    // 小动物检测开关
    private Boolean detectNuisanceAnimalAi;

    // 鸟类检测开关
    private Boolean detectBirdAi;

    // pir 包裹检测偏好
    private Boolean enableOtherMotionAi;
    // 上报 宠物检测结果开关
    private Boolean reportPetAi;
    // 上报 人形检测结果开关
    private Boolean reportPersonAi;

    // 支持 人 检测结果开关
    private Boolean supportPersonAi;
    // 支持 宠物检测结果开关
    private Boolean supportPetAi;
    // 支持 汽车检测结果开关
    private Boolean supportVehicleAi;
    // 支持 包裹检测结果开关
    private Boolean supportPackageAi;
    // 支持 人脸检测结果开关
    private Boolean supportFaceAi;
    // 支持 人脸检测结果开关
    private Boolean supportCrossCameraAi;
    /** ai edge 相关字段 end */

    // sd卡录像开关
    private Integer sdCardCooldownSwitch;
    // sd卡录像模式
    private String sdCardVideoMode;
    // sd卡录像cooldown时长
    private String sdCardCooldownSeconds;

    /** ---------------------------------------------泛光灯需求 start---------------------------------------------*/
    // 运动检测开关(泛光灯)
    private Boolean motionTriggeredFloodlightSwitch;
    // 运动检测间隔时间(泛光灯)
    private String motionFloodlightTimer;
    // 定时计划开关
    private Boolean floodlightScheduleSwitch;
    // 定时计划内容
    private String floodlightSchedulePlan;
    private String floodlightMode;
    /** ---------------------------------------------泛光灯需求 end-----------------------------------------------*/
    private Boolean video12HourSwitch;
    private JSONObject propertyJson;



    public static DeviceAppSettingsDO ParseFrom(DeviceSettingsDO deviceSettings, CloudDeviceSupport cloudDeviceSupport) {
        DeviceAppSettingsDO appSettings = new DeviceAppSettingsDO();
        appSettings.setSerialNumber(deviceSettings.getSerialNumber());
        appSettings.setNeedMotion(deviceSettings.getPir() > 0 ? 1 : 0);
        appSettings.setMotionSensitivity(deviceSettings.getMotionSensitivity());
        appSettings.setNeedNightVision(deviceSettings.getIrThreshold() > 0 ? 1 : 0);
        appSettings.setNightVisionSensitivity(deviceSettings.getNightVisionSensitivity());
        appSettings.setNeedAlarm(deviceSettings.getNeedAlarm() > 0 ? 1 : 0);
        appSettings.setAlarmSeconds(deviceSettings.getPirSirenDuration());
        appSettings.setNeedVideo(deviceSettings.getNeedVideo() > 0 ? 1 : 0);
        appSettings.setVideoSeconds(deviceSettings.getRecLen());
        appSettings.setDeviceLanguage(deviceSettings.getLanguage());
        appSettings.setTimeZone(deviceSettings.getTimeZone());
        appSettings.setNightVisionMode(deviceSettings.getNightVisionMode());
        appSettings.setWhiteLightScintillation(deviceSettings.getWhiteLightScintillation());
        appSettings.setDevicePersonDetect(deviceSettings.getDevicePersonDetect());
        appSettings.setNightThresholdLevel(deviceSettings.getNightThresholdLevel());

        appSettings.setMotionTrack(deviceSettings.getMotionTrack());
        appSettings.setMotionTrackMode(deviceSettings.getMotionTrackMode());

        appSettings.setRecLamp(deviceSettings.getRecLamp());
        appSettings.setVoiceVolumeSwitch(deviceSettings.getVoiceVolumeSwitch());
        appSettings.setVoiceVolume(deviceSettings.getVoiceVolume());
        appSettings.setAlarmVolume(deviceSettings.getAlarmVolume());
        appSettings.setCryDetect(deviceSettings.getCryDetect());
        appSettings.setCryDetectLevel(deviceSettings.getCryDetectLevel());

        appSettings.setDeviceSupportLanguage(deviceSettings.getDeviceSupportLanguageList(cloudDeviceSupport));
        List<DoorBellRingDO> doorBellRingList = deviceSettings.getDoorBellRingIdList(cloudDeviceSupport).stream()
                .map(id -> new DoorBellRingDO().setId(id)).collect(Collectors.toList());
        appSettings.setSupportDoorBellRingKey(doorBellRingList);

        appSettings.setAutoLog(deviceSettings.getAutoLog());
        appSettings.setAntiflickerSwitch(deviceSettings.getAntiflickerSwitch());
        appSettings.setAntiflicker(deviceSettings.getAntiflicker());
        appSettings.setMirrorFlip(deviceSettings.getMirrorFlip());
        // 拍摄间隔,下发给app
        appSettings.setCooldown(new CoolDownDO().setValue(deviceSettings.getCooldownInS()).setUserEnable(deviceSettings.getCooldownUserEnable()));
        appSettings.setDefaultCodec(deviceSettings.getDefaultCodec());
        appSettings.setClearDefaultCodec(deviceSettings.getClearDefaultCodec());
        appSettings.setDoorBellRingKey(deviceSettings.getDoorBellRingKey());
        appSettings.setLiveAudioToggleOn(deviceSettings.getLiveAudioToggleOn());
        appSettings.setRecordingAudioToggleOn(deviceSettings.getRecordingAudioToggleOn());
        appSettings.setLiveSpeakerVolume(deviceSettings.getLiveSpeakerVolume());
        appSettings.setAlarmWhenRemoveToggleOn(deviceSettings.getAlarmWhenRemoveToggleOn());
        appSettings.setMechanicalDingDongSwitch(deviceSettings.getMechanicalDingDongSwitch());
        appSettings.setMechanicalDingDongDuration(deviceSettings.getMechanicalDingDongDuration());
        appSettings.setDeviceCallToggleOn(deviceSettings.getDeviceCallToggleOn());
        // 充电自动开机
        appSettings.setChargeAutoPowerOnSwitch(deviceSettings.getChargeAutoPowerOnSwitch());
        appSettings.setChargeAutoPowerOnCapacity(deviceSettings.getChargeAutoPowerOnCapacity());
        appSettings.setChargeAutoPowerOnCapacityOptions(DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY_OPTIONS);
        appSettings.setPowerSource(deviceSettings.getPowerSource());
        appSettings.setOtaAutoUpgrade(deviceSettings.getOtaAutoUpgrade());
        // 枚举值
        appSettings.setPirSensitivity(deviceSettings.getPirSensitivity());
        appSettings.setPirRecordTime(deviceSettings.getPirRecordTime());
        appSettings.setSdCardPirRecordTime(deviceSettings.getSdCardPirRecordTime());
        appSettings.setPirCooldownTime(deviceSettings.getPirCooldownTime());
        appSettings.setVideoResolution(deviceSettings.getVideoResolution());
        appSettings.setVideoAntiFlickerFrequency(deviceSettings.getVideoAntiFlickerFrequency());
        // 门铃按下通知
        appSettings.setDoorbellPressNotifySwitch(deviceSettings.getDoorbellPressNotifySwitch());
        appSettings.setDoorbellPressNotifyType(deviceSettings.getDoorbellPressNotifyType());

        appSettings.setWifiPowerLevel(deviceSettings.getWifiPowerLevel());
        appSettings.setNightVisionSensitivityEnum(deviceSettings.getNightVisionSensitivityEnum());
        appSettings.setNightVisionModeEnum(deviceSettings.getNightVisionModeEnum());
        appSettings.setAlarmDurationEnum(deviceSettings.getAlarmDurationEnum());
        appSettings.setReportPetAi(deviceSettings.getReportPetAi());
        appSettings.setReportPersonAi(deviceSettings.getReportPersonAi());
        appSettings.setDetectPersonAi(deviceSettings.getDetectPersonAi());
        appSettings.setDetectPetAi(deviceSettings.getDetectPetAi());
        appSettings.setDetectVehicleAi(deviceSettings.getDetectVehicleAi());
        appSettings.setDetectPackageAi(deviceSettings.getDetectPackageAi());
        appSettings.setDetectFaceAi(deviceSettings.getDetectFaceAi());
        appSettings.setDetectNuisanceAnimalAi(deviceSettings.getDetectNuisanceAnimalAi());
        appSettings.setDetectBirdAi(deviceSettings.getDetectBirdAi());
        appSettings.setEnableOtherMotionAi(deviceSettings.getEnableOtherMotionAi());
        appSettings.setSupportPersonAi(deviceSettings.getSupportPersonAi());
        appSettings.setSupportPetAi(deviceSettings.getSupportPetAi());
        appSettings.setSupportVehicleAi(deviceSettings.getSupportVehicleAi());
        appSettings.setSupportPersonAi(deviceSettings.getSupportPackageAi());
        appSettings.setSupportFaceAi(deviceSettings.getSupportFaceAi());
        appSettings.setSupportCrossCameraAi(deviceSettings.getSupportCrossCameraAi());

        appSettings.setSdCardCooldownSwitch(deviceSettings.getSdCardCooldownSwitch());
        appSettings.setSdCardVideoMode(deviceSettings.getSdCardVideoMode());
        appSettings.setSdCardCooldownSeconds(deviceSettings.getSdCardCooldownSeconds());

        appSettings.setMotionTriggeredFloodlightSwitch(deviceSettings.getMotionTriggeredFloodlightSwitch());
        appSettings.setMotionFloodlightTimer(deviceSettings.getMotionFloodlightTime());
        appSettings.setFloodlightScheduleSwitch(deviceSettings.getFloodlightScheduleSwitch());
        appSettings.setFloodlightSchedulePlan(deviceSettings.getFloodlightSchedulePlan());
        return appSettings;
    }

    public static DeviceAppSettingsDO defaultSettings(DeviceModelSettingDO deviceModelSettingDO) {
        return defaultSettings(deviceModelSettingDO,null);
    }
    
    public static DeviceAppSettingsDO defaultSettings(DeviceModelSettingDO deviceModelSettingDO,DeviceModel deviceModel) {
        DeviceAppSettingsDO appSettings = new DeviceAppSettingsDO();
        appSettings.setNeedMotion(NEED_MOTION_VALUE);//开启运动检测
        appSettings.setMotionSensitivity(deviceModelSettingDO.getMotionSensitivity()); //运动检测灵敏度
        appSettings.setNeedNightVision(NEED_NIGHT_VISION_VALUE);//夜视模式开启
        appSettings.setNightVisionSensitivity(NIGHT_VISION_SENSITIVITY_VALUE); //Medium
        appSettings.setNightThresholdLevel(NIGHT_THRESHOLD_Level_VALUE); //夜视灵敏度-中级
        appSettings.setNeedAlarm(NEED_ALARM_VALUE);//设备报警-开
        appSettings.setAlarmSeconds(ALARM_SECONDS_VALUE);//警报长度
        appSettings.setAlarmDurationEnum(ALARM_DURATION_ENUM_VALUE);//警报长度
        appSettings.setNeedVideo(NEED_VIDEO_VALUE);//录像开关，开启
        appSettings.setVideoSeconds(VIDEO_SECONDS_VALUE);//录像长度，10秒
        appSettings.setMotionTrack(MOTION_TRACK_VALUE);
        appSettings.setMotionTrackMode(MOTION_TRACK_MODE_VALUE);
        appSettings.setRecLamp(REC_LAMP_VALUE);
        appSettings.setVoiceVolumeSwitch(VOICE_VOlUME_SWITCH_VALUE);

        appSettings.setVoiceVolume(deviceModelSettingDO.getDeviceModelVoiceDO() == null ? VOICE_VOLUME_VALUE : deviceModelSettingDO.getDeviceModelVoiceDO().getVoiceVolume());
        appSettings.setAlarmVolume(deviceModelSettingDO.getDeviceModelVoiceDO() == null ? ALARM_VOLUME_VALUE : deviceModelSettingDO.getDeviceModelVoiceDO().getAlarmVolume());

        appSettings.setCryDetect(CRY_DETECT_VALUE);
        appSettings.setCryDetectLevel(CRY_DETECT_LEVEL_VALUE);

        appSettings.setNightVisionMode(NIGHT_VISION_MODE_VALUE); //夜视模式-红外模式
        appSettings.setWhiteLightScintillation(WhiteLightScintillationEnums.CLOSE.getCode()); //白光灯闪烁-关闭
        appSettings.setDevicePersonDetect(DEVICE_PERSON_DETECT_VALUE); //设备人型检测，关
        //设备打印log,默认关
        appSettings.setAutoLog(AUTO_LOG_VALUE);
        //视频翻转，默认关闭
        appSettings.setMirrorFlip(deviceModelSettingDO.getMirrorFlip());
        // 拍摄间隔，默认：关闭，10秒
        appSettings.setCooldown(new CoolDownDO().setUserEnable(CoolDownDO.DEFAULT_USER_ENABLE).setValue(CoolDownDO.DEFAULT_VALUE));
        appSettings.setDefaultCodec(null); // 默认就为null
        appSettings.setLiveAudioToggleOn(DEFAULT_VALUE_LIVE_AUDIO_TOGGLE_ON);
        appSettings.setRecordingAudioToggleOn(DEFAULT_VALUE_RECORDING_AUDIO_TOGGLE_ON);
        appSettings.setLiveSpeakerVolume(DEFAULT_VALUE_LIVE_SPEAKER_VOLUME);
        appSettings.setAlarmWhenRemoveToggleOn(DEFAULT_VALUE_ALARM_WHEN_REMOVE_TOGGLE_ON);
        appSettings.setMechanicalDingDongSwitch(DEFAULT_VALUE_MECHANICAL_DING_DONG_SWITCH);
        appSettings.setMechanicalDingDongDuration(DEFAULT_VALUE_MECHANICAL_DING_DONG_DURATION);
        appSettings.setWhiteLightScintillation(0);
        appSettings.setDeviceCallToggleOn(DEFAULT_VALUE_DEVICE_CALL_TOGGLE_ON);
        // 充电自动开机
        appSettings.setChargeAutoPowerOnSwitch(deviceModelSettingDO.getChargeAutoPowerOnSwitch() == null ? DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_SWITCH : deviceModelSettingDO.getChargeAutoPowerOnSwitch());
        appSettings.setChargeAutoPowerOnCapacity(DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY);
        appSettings.setChargeAutoPowerOnCapacityOptions(DEFAULT_VALUE_CHARGE_AUTO_POWER_ON_CAPACITY_OPTIONS);
        appSettings.setPowerSource(OptionEnumMapping.OPTION_ENUM_UNSELECTED);
        appSettings.setOtaAutoUpgrade(true);
        // 门铃按下通知
        appSettings.setDoorbellPressNotifySwitch(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH);
        appSettings.setDoorbellPressNotifyType(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE);

        appSettings.setDetectPersonAi(false);
        appSettings.setReportPetAi(false);
        appSettings.setReportPersonAi(false);

        appSettings.setSdCardCooldownSwitch(DEFAULT_VALUE_SD_CARD_COOLDOWN_SWITCH);

        //常电设备默认值为 DEFAULT_VALUE_SD_CARD_VIDEO_MODE_CONTINUAL
        String sdCardVideoMode = deviceModel == null || deviceModel.isCanStandby() ? DEFAULT_VALUE_SD_CARD_VIDEO_MODE : DEFAULT_VALUE_SD_CARD_VIDEO_MODE_CONTINUAL;
        appSettings.setSdCardVideoMode(sdCardVideoMode);

        appSettings.setSdCardCooldownSeconds(DEFAULT_VALUE_SD_CARD_COOLDOWN_TIME);
        appSettings.setWifiPowerLevel(Optional.ofNullable(deviceModel).map(it -> it.getWifiPowerLevel()).orElse(null)); // wifi发射功率，允许为null
        appSettings.setMotionTriggeredFloodlightSwitch(false);
        appSettings.setFloodlightScheduleSwitch(false);
        appSettings.setMotionFloodlightTimer("30s");
        appSettings.setFloodlightSchedulePlan("");
        appSettings.setFloodlightMode(DEFAULT_VALUE_FLOOD_LIGHT_MODE);
        appSettings.setVideo12HourSwitch(false);
        return appSettings;
    }

    public void addToPropertyJson(String name, Object value) {
        if(propertyJson == null){
            propertyJson = new JSONObject();
        }
        propertyJson.put(name, value);
    }

    /**
     * 获取detectBirdAi的值，优先从propertyJson中获取
     * @return 鸟类检测偏好设置
     */
    public Boolean getDetectBirdAi() {
        if (propertyJson != null && propertyJson.containsKey("detectBirdAi")) {
            return propertyJson.getBoolean("detectBirdAi");
        }
        return detectBirdAi;
    }

    /**
     * 设置detectBirdAi的值，同时更新到propertyJson中
     * @param detectBirdAi 鸟类检测偏好设置
     */
    public void setDetectBirdAi(Boolean detectBirdAi) {
        this.detectBirdAi = detectBirdAi;
        addToPropertyJson("detectBirdAi", detectBirdAi);
    }

    /**
     * 获取detectNuisanceAnimalAi的值，优先从propertyJson中获取
     * @return 小动物检测偏好设置
     */
    public Boolean getDetectNuisanceAnimalAi() {
        if (propertyJson != null && propertyJson.containsKey("detectNuisanceAnimalAi")) {
            return propertyJson.getBoolean("detectNuisanceAnimalAi");
        }
        return detectNuisanceAnimalAi;
    }

    /**
     * 设置detectNuisanceAnimalAi的值，同时更新到propertyJson中
     * @param detectNuisanceAnimalAi 小动物检测偏好设置
     */
    public void setDetectNuisanceAnimalAi(Boolean detectNuisanceAnimalAi) {
        this.detectNuisanceAnimalAi = detectNuisanceAnimalAi;
        addToPropertyJson("detectNuisanceAnimalAi", detectNuisanceAnimalAi);
    }
}
