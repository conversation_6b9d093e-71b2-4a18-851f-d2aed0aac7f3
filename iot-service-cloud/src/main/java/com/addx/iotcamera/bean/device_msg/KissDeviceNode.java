package com.addx.iotcamera.bean.device_msg;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

// 设备在kiss服务器上的节点信息
@Data
@Accessors(chain = true)
public class KissDeviceNode {
    private String deviceSn;
    private String kissIp;
    private KissDeviceStatus deviceStatus;
    private Long lastUpdateTime;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
