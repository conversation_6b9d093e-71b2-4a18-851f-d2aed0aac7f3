package com.addx.iotcamera.bean.domain.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderVerifyResultDO {
    @Schema(title = "购买的商品Id")
    private Integer productId;
    @Schema(title = "是否首月免费")
    private Integer freeTrial;
    @Schema(title = "验证结果")
    private Boolean verify;
    @Schema(title = "原始订单")
    private String originTradeNo;
    private String tradeNo;

    @Schema(title = "购买时间戳")
    private Integer purchaseTime;
    @Schema(title = "购买时间")
    private String purchaseDate;
    @Schema(title = "购买时间")
    private String purchaseDatePst;

    @Schema(title = "免费时长-天")
    @Builder.Default
    private Integer freeTrialPeriod = 0;

    @Schema(title = "本期账单过期时间")
    private Integer expireTime;
    private Boolean isUpgradedOrder = false;
    private Boolean isTestOrder = false;
    private String linkedPurchaseToken;
    private String orderInfo;
    private String orderInfoV2;
    private String linkedOrderInfo;
}
