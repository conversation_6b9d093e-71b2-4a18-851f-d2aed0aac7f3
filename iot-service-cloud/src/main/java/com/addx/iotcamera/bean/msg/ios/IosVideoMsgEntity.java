package com.addx.iotcamera.bean.msg.ios;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.msg.VideoMsgEntity;
import lombok.Data;

import static com.addx.iotcamera.bean.msg.MsgType.ALARM_MSG;

public class IosVideoMsgEntity extends IosMsgEntity {

    public IosVideoMsgEntity() {
        super();
        this.setType(MsgType.VIDEO_MSG);
    }

    @Override
    public void ParseFrom(MsgEntityBase msg) {
        if (!(msg instanceof VideoMsgEntity)) return;
        VideoMsgEntity entity = (VideoMsgEntity) msg;
        this.setMsgId(String.valueOf(entity.getLibrary().getLibraryId())); // IOS specified   WTF!!!
        this.setMessageId(msg.getMessageId());
        this.getMsg().setImg(entity.getThumbnailUrl());
        this.getMsg().setUrl(entity.getLibrary().getVideoUrl());
        this.setTraceId(msg.getTraceId());
        this.getMsg().setLibraryId(entity.getLibrary().getLibraryId());
        this.getMsg().setSerialNumber(entity.getDevice().getSerialNumber());
        this.getMsg().setCheckPushIntent(entity.getCheckPushIntent());
        this.getMsg().setUserId(entity.getUserId());
        this.getMsg().setTenantId(entity.getTenantId());
        this.getMsg().setNode(entity.getNode());
        this.getMsg().setSupportMagicPix(entity.getSupportMagicPix());
        this.timestamp = (int) (System.currentTimeMillis() / 1000);
        this.getMsg().setAlarmDelayTimeStamp(entity.getAlarmDelayTimeStamp());
        this.getMsg().setAlarmDuration(entity.getAlarmDuration());
        if(ALARM_MSG.contains(entity.getType())) {
            this.setType(entity.getType());
        }
    }

    private IosVideoMsgEntity.IosMsgDoMsg msg = new IosVideoMsgEntity.IosMsgDoMsg();

    public IosMsgDoMsg getMsg() {
        return msg;
    }

    public void setMsg(IosMsgDoMsg msg) {
        this.msg = msg;
    }

    @Data
    public class IosMsgDoMsg {
        private String url;
        private String img;
        private Integer libraryId;
        public String serialNumber;
        // app端推送安全校验参数
        private Boolean checkPushIntent = false;
        private Integer userId;
        private String tenantId;
        // 当前服务器node信息
        private String node;

        // magicPix功能
        private Boolean supportMagicPix;
        private Integer alarmDelayTimeStamp;
        private Integer alarmDuration;
    }
}
