package com.addx.iotcamera.bean.response.user;

import com.addx.iotcamera.bean.app.vip.UserVipTier;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTierRemindResponse {
    @Schema(title = "是否需要弹出提醒")
    private boolean shouldReminder;

    /**  推荐商品旧版本配置，暂时不去掉 start*/
    @Schema(title = "推荐商品")
    private UserVipTier.RecommendProduct recommendProduct;
    @Schema(title = "推荐商品-月订阅")
    private List<UserVipTier.RecommendProduct> recommendProductList;
    /**  推荐商品旧版本配置，暂时不去掉 end*/


    @Schema(title = "推荐商品实例")
    private RecommendProductDO recommendProductDO;

    @Schema(title = "提醒次数")
    private Integer reminderCount;
    @Schema(title = "提醒唯一标记")
    private Long queryId;
    
    @Schema(title = "轮播图list")
    private List<String> rotationChartList;

    private String slotName;


    @Schema(title = "推荐商品-4G")
    private RecommendProduct4GDO recommendProduct4GDO;

    @Schema(title = "推荐商品类型, 0 云服务 1 4G流量套餐")
    private Integer recommendProductType;

    @Schema(title = "4G流量设备是否有插入官方sim卡")
    private boolean containsOfficialSim;

    @Schema(title = "超出了当日弹出次数，直接展示free license领取页面 ")
    private boolean hasPendingFreeLicense;
}
