package com.addx.iotcamera.bean.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class User {
    private Integer id;
    @Schema(title = "用户名-firstName")
    private String name;
    @Schema(title = "用户名-lastName")
    private String lastName;
    private String email;
    private String phone;
    private String hashedPassword;
    private String salt;
    private Integer registTime;
    private String countryNo;
    private String language;
    private String tenantId;

    private int status; // UserStatus
    /* openAp 增加字段 begin */
    private int type; // 0-自有用户；1-第三方平台用户
    private String thirdUserId; // 第三方用户id
    private String accountId; // 第三方平台id
    /* openAp 增加字段 end */
    @Schema(title = "app类型")
    String appType;
    @Schema(title = "app版本")
    Integer appVersion;
    @Schema(title = "app版本名称")
    String appVersionName;

    @Schema(title = "注销原因")
    Integer cancellationReason;
    @Schema(title = "注销备注")
    String cancellationRemark;

    /**
     * 用来手工标记是否是内部测试账号
     */
    private Integer internal;
}
