package com.addx.iotcamera.bean.msg.ios;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class  IosMsgEntity {
    private Integer type;
    private String msgId;
    private String messageId;
    private String traceId;
    private String videoEvent;
    protected Integer timestamp;
    private String serialNumber;
    private UUID voipUUID;

    public void ParseFrom(MsgEntityBase msg) {
    }
}
