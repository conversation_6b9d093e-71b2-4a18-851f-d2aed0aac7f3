package com.addx.iotcamera.bean.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginRequest extends AppRequestBase {
    private Integer id;
    @Builder.Default
    private String email = "";
    private String password;
    private Integer msgType;
    private String msgToken;
    private String iosVoipToken;
    @Builder.Default
    private String phone = "";
    private String code;
    @Builder.Default
    private Integer loginType = 99;
    private String userName;

    @Builder.Default
    @Schema(title = "失败超过3次引导忘记密码")
    private Boolean guideForgetPassword = false;

    //设备ID
    private String userDeviceId;
    //设备名称
    private String userDeviceName;

    //  1: 一期1fa需求， 2： 二期2fa需求
    private Integer verifyVersion = 0;
}
