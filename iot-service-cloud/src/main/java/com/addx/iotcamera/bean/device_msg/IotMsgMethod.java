package com.addx.iotcamera.bean.device_msg;

import com.addx.iotcamera.util.FuncUtil;
import com.google.common.collect.ImmutableMap;

public enum IotMsgMethod {
    CMD,
    RETAINED_MSG,
    KEEP_ALIVE_PARAMS,
    ;

    private static final ImmutableMap<String, IotMsgMethod> name2Enum = FuncUtil.createImmutableMap(values(), it -> it.name());

    public static IotMsgMethod nameOf(String name) {
        return name2Enum.get(name);
    }
}
