package com.addx.iotcamera.bean.domain.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
public class TierCopywriteDiff {
    @Schema(title = "title")
    String title;
    @Schema(title = "类型")
    String type;
    @Schema(title = "free文案key")
    String value;
    @Schema(title = "vip文案key")
    String vipValue;
    @Schema(title = "指定的tenant")
    Set<String> exclusive;
}
