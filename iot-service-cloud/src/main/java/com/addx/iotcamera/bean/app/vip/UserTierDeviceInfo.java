package com.addx.iotcamera.bean.app.vip;

import com.addx.iotcamera.bean.domain.DeviceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTierDeviceInfo {

    private Integer tierId;
    private String tierNameKey;

    private Integer tierGroupId;

    private String tierUid;

    private String tierName;

    private Integer maxDeviceNum;

    private Integer effectiveTime;
    
    private Integer endTime;

    @Schema(title = "用户名下设备list")
    private List<String> supportDeviceSnList;
    @Schema(title = "生效的设备list")
    private List<DeviceDO> activeDeviceList;

    private List<String> activeDeviceSnList;

    @Schema(title = "用户当前支付商品的付款周期，0 表示当前月付，1 表示半年付，2表示年付")
    private Integer billingCycleType;
    @Schema(title = "用户当前支付商品的付款周期长度")
    private Integer billingCycleDuration;
}
