package com.addx.iotcamera.bean.apollo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MailSend {
    /**
     * 注册
     */
    private String signInTitle;
    private String signInContent;

    /**
     * 重置密码
     */
    private String resetTitle;
    private String resetContent;

    /**
     * 失败超过限制次数验证码
     */
    private String loginInTitle;
    private String loginInContent;

    /**
     * 绑定
     */
    private String bindContactTitle;
    private String bindContactContent;


    /**
     * 注册后欢迎邮件（包含官网介绍）
     */
    private String signInPayTitle;

    /**
     * 免费试用邮件title
     */
    private String freeTrialTitle;

    /**
     * 到期提醒邮件title
     */
    private String dueReminderTitle;

    /**
     * 购买提醒邮件title
     */
    private String buyReminderTitle;


    /**
     * 过期时间提醒
     */
    private String payEmailExpireTime;
    /**
     * welcome邮件 欢迎词
     */
    private String payEmailWelcome;
    /**
     * 欢迎词
     */
    private String payEmailHello;
    /**
     * email正文-功能介绍
     */
    private String payEmailAd;
    /**
     * email正文-帮助地址
     */
    private String payEmailWelcomeFeed;
    /**
     * email正文-了解更多
     */
    private String payEmailLearnMore;
    /**
     * email正文-邮件发送者
     */
    private String payEmailFrom;
    /**
     * 智能服务
     */
    private String awarenessService;

    private String payEmailExpireRemindMessage;


    @Schema(title = "基站设备分享邀请邮件-title")
    private String stationShareEmailTitle;
    @Schema(title = "基站设备分享邀请邮件-正文-不存在的用户")
    private String stationShareEmailContentNew;
    @Schema(title = "基站设备分享邀请邮件-正文-已存在用户")
    private String stationShareEmailContent;


    /**
     * 两年free套餐
     */
    private String twoYearFree30DayEmailTitle;
    private String twoYearFree30DayEmailContent;

    private String twoYearFree180DayEmailTitle;
    private String twoYearFree180DayEmailContent;

    private String twoYearFreeExpireEmailTitle;
    private String twoYearFreeExpireEmailContent;

    private String userFreeBack4KBAppTitle;
    private String userFreeBack4KBAppContent;


    private String twoTempFaEmailTitle;
    private String twoTempFaEmailContent;


    private String twoFaEmailTitle;
    private String twoFaEmailContent;
}
