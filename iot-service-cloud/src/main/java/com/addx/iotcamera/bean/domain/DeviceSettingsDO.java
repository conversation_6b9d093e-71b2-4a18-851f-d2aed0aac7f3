package com.addx.iotcamera.bean.domain;

import com.addx.iotcamera.bean.device.CoolDownDO;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.config.device.DoorBellRingConfig.DoorBellRingKeyConfig;
import com.addx.iotcamera.constants.DeviceModelSettingConstants;
import com.addx.iotcamera.enums.NightThresholdLevelEnums;
import com.addx.iotcamera.publishers.vernemq.requests.GetParameterRequest;
import com.addx.iotcamera.publishers.vernemq.requests.SetParameterRequest;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.FormatUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.addx.iotcamera.constants.VideoConstants.STORE_SWITCH_BIT_INDEX_CAMERA_LOCAL;
import static com.addx.iotcamera.constants.VideoConstants.STORE_SWITCH_IDENTIFIER;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DeviceSettingsDO {
    private String serialNumber;
    /**
     * 运动检测开关
     */
    private Integer pir;
    /**
     * 运动检测灵敏度
     */
    private Integer motionSensitivity;
    /**
     * 夜视开关
     */
    private Integer irThreshold;
    /**
     * 夜视灵敏度-数值
     */
    private Integer nightVisionSensitivity;
    /**
     * 摄像机警铃-开关
     */
    private Integer needAlarm;
    /**
     * 摄像机警铃时长
     */
    private Integer pirSirenDuration;
    /**
     * 夜视灵敏度等级
     */
    private Integer nightThresholdLevel;
    /**
     * 录像开关
     */
    private Integer needVideo;
    /**
     * 录像时长
     */
    private Integer recLen;

    private Integer lastAct;

    /**
     * 时区
     */
    private String timeZone;
    /**
     * 设备语言
     */
    private String language;

    /**
     * 夜视模式,0-红外灯，1-白光灯
     */
    private Integer nightVisionMode;
    /**
     * 白光灯闪烁,0：关，1开
     */
    private Integer whiteLightScintillation;
    /**
     * 白光灯操作,0:无变化；1；用户关闭；2用户开启
     */
    private Integer whiteLight;

    /**
     * 设备人型检测,0:关闭；1：开启
     */
    private Integer devicePersonDetect;

    /**
     * 是否跟踪（摇头机）
     */
    private Integer motionTrack;

    /**
     * 跟踪模式 0移动跟踪 1人形跟踪
     */
    private Integer motionTrackMode;

    @Getter(AccessLevel.NONE) // 保存到deviceSupport中。去掉getter方法防止误用
    @Builder.Default
    private String deviceSupportLanguage = "cn,en";

    /**
     * 设备是否打印log
     *
     * @param autoLog
     * @return
     */
    private Integer autoLog;
    /**
     * 频闪开关
     */
    private Integer antiflickerSwitch;
    /**
     * 抗频闪
     */
    private Integer antiflicker;

    /**
     * 视频翻转
     */
    private Integer mirrorFlip;

    // 录像指示灯
    private Integer recLamp;

    // 扬声器音量开关
    private Integer voiceVolumeSwitch;

    // 扬声器音量大小
    private Integer voiceVolume;

    // 警铃音量大小
    private Integer alarmVolume;

    // 哭声检测
    private Integer cryDetect;

    // 哭声检测灵敏度
    private Integer cryDetectLevel;

    /**
     * 当前门铃铃音Key
     */
    private Integer doorBellRingKey;
    /**
     * 设备支持的门铃铃音
     */
    @Getter(AccessLevel.NONE) // 保存到deviceSupport中。去掉getter方法防止误用
    private String supportDoorBellRingKey;

    // 用户是否打开拍摄间隔功能
    private Boolean cooldownUserEnable;
    // 最近一次设置的拍摄间隔值(两次PIR事件的最短时间间隔)
    private Integer cooldownInS;
    // 设备默认编码
    private String defaultCodec;
    // 是否设置defaultCodec为null
    private Boolean clearDefaultCodec;
    // 直播收音
    private Boolean liveAudioToggleOn;
    // 录像直播收音
    private Boolean recordingAudioToggleOn;
    // 直播对讲音量
    private Integer liveSpeakerVolume;
    // 拆除报警开关
    private Boolean alarmWhenRemoveToggleOn;

    // 机械叮咚开关 0关1开
    private Integer mechanicalDingDongSwitch;
    // 机械叮咚时长
    private Integer mechanicalDingDongDuration;

    // 设备呼叫开关
    private Boolean deviceCallToggleOn; /* 18.一键呼叫-一键呼叫开关*/

    // 1、允许充电自动开机开关，默认关，chargeAutoPowerOnSwitch
    private  Integer chargeAutoPowerOnSwitch;
    // 2、允许充电自动开机电量，默认25，chargeAutoPowerOnCapacity
    private  Integer chargeAutoPowerOnCapacity;
    /*** 供电方式 ["solar_panel", "plug_in", "battery_power_only", "wired"] */
    private String powerSource;

    @Schema(title = "是否开启ota自动升级开关")
    Boolean otaAutoUpgrade;

    /*** 运动检测灵敏度 ["high","mid","low"]*/
    private String pirSensitivity;
    /*** 运动检测录制时长 ["auto","10s","15s","20s"]*/
    private String pirRecordTime;
    /*** sdCard运动检测录制时长 ["10s", "15s", "20s", "60s", "120s", "180s", "auto"]*/
    private String sdCardPirRecordTime;
    /*** 运动检测触发间隔时间 ["10s","30s","60s","180s","300s"]*/
    private String pirCooldownTime;
    /*** 视频分辨率 ["high","mid"] 从 device 挪到 deviceSettings */
    private String videoResolution;
    /*** 视频抗频闪频率 ["50Hz","60Hz"]*/
    private String videoAntiFlickerFrequency;
    /*** 按铃通知开关 */
    private Boolean doorbellPressNotifySwitch;
    /*** 按铃通知方式 ["phone","push"] */
    private String doorbellPressNotifyType;
    /*** wifi发射功率单位。int值，范围:[1,10] */
    private Integer wifiPowerLevel;

    private String nightVisionSensitivityEnum; /* 26.灯光设置-夜视灵敏度控制*/
    private String nightVisionModeEnum; /* 27.灯光设置-夜视模式调节*/
    private String alarmDurationEnum; /* 28.声音设置-报警时长*/

    // pir 人性检测偏好
    private Boolean detectPersonAi;
    // pir 宠物检测偏好
    private Boolean detectPetAi;
    // pir 汽车检测偏好
    private Boolean detectVehicleAi;
    // pir 包裹检测偏好
    private Boolean detectPackageAi;
    // pir 人脸检测偏好
    private Boolean detectFaceAi;
    // pir 小动物检测偏好
    private Boolean detectNuisanceAnimalAi;
    // pir 鸟类检测偏好
    private Boolean detectBirdAi;
    // pir 包裹检测偏好
    private Boolean enableOtherMotionAi;

    // 上报 人形检测结果开关
    private Boolean reportPersonAi;
    // 上报 宠物检测结果开关
    private Boolean reportPetAi;

    // 支持 人 检测结果开关
    private Boolean supportPersonAi;
    // 支持 宠物检测结果开关
    private Boolean supportPetAi;
    // 支持 汽车检测结果开关
    private Boolean supportVehicleAi;
    // 支持 包裹检测结果开关
    private Boolean supportPackageAi;
    // 支持 人脸检测结果开关
    private Boolean supportFaceAi;
    // 支持 跨摄像头识别结果开关
    private Boolean supportCrossCameraAi;
    /** ai edge 相关字段 end */

    // sd卡录像cooldown开关
    private Integer sdCardCooldownSwitch;
    // sd卡录像模式
    private String sdCardVideoMode;
    // sd卡录像cooldown时长
    private String sdCardCooldownSeconds;

    /** ---------------------------------------------泛光灯需求 start---------------------------------------------*/
    // 运动检测开关(泛光灯)
    private Boolean motionTriggeredFloodlightSwitch;
    // 运动检测间隔时间(泛光灯)
    private String motionFloodlightTime;
    // 定时计划开关
    private Boolean floodlightScheduleSwitch;
    // 定时计划内容
    private String floodlightSchedulePlan;

    private String floodlightMode;
    /** ---------------------------------------------泛光灯需求 end-----------------------------------------------*/
    private Boolean video12HourSwitch;
    private JSONObject propertyJson;

    private String propertyJsonStr;


    /* sql查出来，设置到JSONObject为null，需要转一次 */
    public JSONObject getPropertyJson(){
        if(propertyJson == null){
            propertyJson = JSON.parseObject(propertyJsonStr);
        }
        return propertyJson;
    }

    // 由 app更新值(部分字段) 和 数据库存储值 组成需要更新的bean
    public static DeviceSettingsDO ParseFrom(DeviceAppSettingsDO appSettings, DeviceSettingsDO storeDeviceSettingsDO) {
        DeviceSettingsDO settings = new DeviceSettingsDO();
        settings.setSerialNumber(appSettings.getSerialNumber());
        //运动检测
        settings.setPir(appSettings.getNeedMotion() != null ? appSettings.getNeedMotion() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getPir()));
        settings.setMotionSensitivity(appSettings.getMotionSensitivity() != null ? appSettings.getMotionSensitivity() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMotionSensitivity()));
        //夜视
        settings.setIrThreshold(appSettings.getNeedNightVision() != null ? appSettings.getNeedNightVision() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getIrThreshold()));
        settings.setNightVisionSensitivity(getNightVisionSensitivity(appSettings,storeDeviceSettingsDO));
        settings.setNightThresholdLevel(appSettings.getNightThresholdLevel() != null ? appSettings.getNightThresholdLevel() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getNightThresholdLevel()));

        //直播报警
        settings.setNeedAlarm(appSettings.getNeedAlarm() != null ? appSettings.getNeedAlarm() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getNeedAlarm()));
        settings.setPirSirenDuration(appSettings.getAlarmSeconds() != null ? appSettings.getAlarmSeconds() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getPirSirenDuration()));
        //录像
        settings.setNeedVideo(appSettings.getNeedVideo() != null ? appSettings.getNeedVideo() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getNeedVideo()));
        settings.setRecLen(appSettings.getVideoSeconds() != null ? appSettings.getVideoSeconds() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getRecLen()));
        settings.setLanguage(appSettings.getDeviceLanguage());
        settings.setTimeZone(appSettings.getTimeZone());

        settings.setNightVisionMode(appSettings.getNightVisionMode());
        settings.setWhiteLightScintillation(appSettings.getWhiteLightScintillation());
        settings.setDevicePersonDetect(appSettings.getDevicePersonDetect());

        settings.setMotionTrack(appSettings.getMotionTrack());
        settings.setMotionTrackMode(appSettings.getMotionTrackMode());
        settings.setRecLamp(appSettings.getRecLamp());
        settings.setVoiceVolumeSwitch(appSettings.getVoiceVolumeSwitch());
        settings.setVoiceVolume(appSettings.getVoiceVolume());
        settings.setAlarmVolume(appSettings.getAlarmVolume());
        settings.setCryDetect(appSettings.getCryDetect());
        settings.setCryDetectLevel(appSettings.getCryDetectLevel());
        settings.setAutoLog(appSettings.getAutoLog());

        settings.setAntiflickerSwitch(appSettings.getAntiflickerSwitch() != null ? appSettings.getAntiflickerSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getAntiflickerSwitch()));
        settings.setAntiflicker(appSettings.getAntiflicker() != null ? appSettings.getAntiflicker() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getAntiflicker()));
        // 视频翻转
        settings.setMirrorFlip(appSettings.getMirrorFlip() != null ? appSettings.getMirrorFlip() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMirrorFlip()));
        // 门铃铃音
        settings.setDoorBellRingKey(appSettings.getDoorBellRingKey() != null ? appSettings.getDoorBellRingKey() : (storeDeviceSettingsDO == null ? DoorBellRingKeyConfig.getINSTANCE().getDefaultRingKey() : storeDeviceSettingsDO.getDoorBellRingKey()));

        // 机械叮咚
        settings.setMechanicalDingDongSwitch(appSettings.getMechanicalDingDongSwitch() != null ? appSettings.getMechanicalDingDongSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMechanicalDingDongSwitch()));
        settings.setMechanicalDingDongDuration(appSettings.getMechanicalDingDongDuration() != null ? appSettings.getMechanicalDingDongDuration() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMechanicalDingDongDuration()));


        // 拍摄间隔
        Optional<DeviceSettingsDO> settingsOpt = Optional.ofNullable(storeDeviceSettingsDO);
        settingsOpt.map(it -> it.getCooldownInS()).ifPresent(settings::setCooldownInS);
        settingsOpt.map(it -> it.getCooldownUserEnable()).ifPresent(settings::setCooldownUserEnable);
        Optional<CoolDownDO> cooldownOpt = Optional.ofNullable(appSettings).map(it -> it.getCooldown());
        cooldownOpt.map(it -> it.getValue()).ifPresent(settings::setCooldownInS);
        cooldownOpt.map(it -> it.getUserEnable()).ifPresent(settings::setCooldownUserEnable);

        settingsOpt.map(it -> it.getDefaultCodec()).ifPresent(settings::setDefaultCodec);
        settingsOpt.map(it -> it.getLiveAudioToggleOn()).ifPresent(settings::setLiveAudioToggleOn);
        settingsOpt.map(it -> it.getRecordingAudioToggleOn()).ifPresent(settings::setRecordingAudioToggleOn);
        settingsOpt.map(it -> it.getLiveSpeakerVolume()).ifPresent(settings::setLiveSpeakerVolume);
        settingsOpt.map(it -> it.getAlarmWhenRemoveToggleOn()).ifPresent(settings::setAlarmWhenRemoveToggleOn);

        Optional.ofNullable(appSettings.getDefaultCodec()).ifPresent(settings::setDefaultCodec);
        Optional.ofNullable(appSettings.getClearDefaultCodec()).ifPresent(settings::setClearDefaultCodec);
        Optional.ofNullable(appSettings.getLiveAudioToggleOn()).ifPresent(settings::setLiveAudioToggleOn);
        Optional.ofNullable(appSettings.getRecordingAudioToggleOn()).ifPresent(settings::setRecordingAudioToggleOn);
        Optional.ofNullable(appSettings.getLiveSpeakerVolume()).ifPresent(settings::setLiveSpeakerVolume);
        Optional.ofNullable(appSettings.getAlarmWhenRemoveToggleOn()).ifPresent(settings::setAlarmWhenRemoveToggleOn);

        Optional.ofNullable(appSettings.getDeviceCallToggleOn()).ifPresent(settings::setDeviceCallToggleOn);
        Optional.ofNullable(appSettings.getChargeAutoPowerOnSwitch()).ifPresent(settings::setChargeAutoPowerOnSwitch);
        Optional.ofNullable(appSettings.getChargeAutoPowerOnCapacity()).ifPresent(settings::setChargeAutoPowerOnCapacity);
        Optional.ofNullable(appSettings.getPowerSource()).ifPresent(settings::setPowerSource);
        Optional.ofNullable(appSettings.getOtaAutoUpgrade()).ifPresent(settings::setOtaAutoUpgrade);
        // 枚举值
        Optional.ofNullable(appSettings.getPirSensitivity()).ifPresent(settings::setPirSensitivity);
        Optional.ofNullable(appSettings.getPirRecordTime()).ifPresent(settings::setPirRecordTime);
        Optional.ofNullable(appSettings.getSdCardPirRecordTime()).ifPresent(settings::setSdCardPirRecordTime);
        Optional.ofNullable(appSettings.getPirCooldownTime()).ifPresent(settings::setPirCooldownTime);
        Optional.ofNullable(appSettings.getVideoResolution()).ifPresent(settings::setVideoResolution);
        Optional.ofNullable(appSettings.getVideoAntiFlickerFrequency()).ifPresent(settings::setVideoAntiFlickerFrequency);
        // 门铃按下通知
        Optional.ofNullable(appSettings.getDoorbellPressNotifySwitch()).ifPresent(settings::setDoorbellPressNotifySwitch);
        Optional.ofNullable(appSettings.getDoorbellPressNotifyType()).ifPresent(settings::setDoorbellPressNotifyType);

        Optional.ofNullable(appSettings.getWifiPowerLevel()).ifPresent(settings::setWifiPowerLevel);
        Optional.ofNullable(appSettings.getNightVisionSensitivityEnum()).ifPresent(settings::setNightVisionSensitivityEnum);
        Optional.ofNullable(appSettings.getNightVisionModeEnum()).ifPresent(settings::setNightVisionModeEnum);
        Optional.ofNullable(appSettings.getAlarmDurationEnum()).ifPresent(settings::setAlarmDurationEnum);

        settings.setDetectPersonAi(appSettings.getDetectPersonAi() != null ? appSettings.getDetectPersonAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getDetectPersonAi()));
        settings.setDetectPetAi(appSettings.getDetectPetAi() != null ? appSettings.getDetectPetAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getDetectPetAi()));
        settings.setDetectVehicleAi(appSettings.getDetectVehicleAi() != null ? appSettings.getDetectVehicleAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getDetectVehicleAi()));
        settings.setDetectPackageAi(appSettings.getDetectPackageAi() != null ? appSettings.getDetectPackageAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getDetectPackageAi()));
        settings.setDetectFaceAi(appSettings.getDetectFaceAi() != null ? appSettings.getDetectFaceAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getDetectFaceAi()));

        settings.setSupportPersonAi(appSettings.getSupportPersonAi() != null ? appSettings.getSupportPersonAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSupportPersonAi()));
        settings.setSupportPetAi(appSettings.getSupportPetAi() != null ? appSettings.getSupportPetAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSupportPetAi()));
        settings.setSupportVehicleAi(appSettings.getSupportVehicleAi() != null ? appSettings.getSupportVehicleAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSupportVehicleAi()));
        settings.setSupportPackageAi(appSettings.getSupportPackageAi() != null ? appSettings.getSupportPackageAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSupportPackageAi()));
        settings.setSupportFaceAi(appSettings.getSupportFaceAi() != null ? appSettings.getSupportFaceAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSupportFaceAi()));
        settings.setEnableOtherMotionAi(appSettings.getEnableOtherMotionAi() != null ? appSettings.getEnableOtherMotionAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getEnableOtherMotionAi()));

        settings.setReportPersonAi(appSettings.getReportPersonAi() != null ? appSettings.getReportPersonAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getReportPersonAi()));
        settings.setReportPetAi(appSettings.getReportPetAi() != null ? appSettings.getReportPetAi() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getReportPetAi()));

        settings.setSdCardCooldownSwitch(appSettings.getSdCardCooldownSwitch() != null ? appSettings.getSdCardCooldownSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSdCardCooldownSwitch()));
        settings.setSdCardVideoMode(appSettings.getSdCardVideoMode() != null ? appSettings.getSdCardVideoMode() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSdCardVideoMode()));
        settings.setSdCardCooldownSeconds(appSettings.getSdCardCooldownSeconds() != null ? appSettings.getSdCardCooldownSeconds() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getSdCardCooldownSeconds()));

        settings.setMotionTriggeredFloodlightSwitch(appSettings.getMotionTriggeredFloodlightSwitch() != null ? appSettings.getMotionTriggeredFloodlightSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMotionTriggeredFloodlightSwitch()));
        settings.setMotionFloodlightTime(appSettings.getMotionFloodlightTimer() != null ? appSettings.getMotionFloodlightTimer() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getMotionFloodlightTime()));
        settings.setFloodlightScheduleSwitch(appSettings.getFloodlightScheduleSwitch() != null ? appSettings.getFloodlightScheduleSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getFloodlightScheduleSwitch()));
        settings.setFloodlightSchedulePlan(appSettings.getFloodlightSchedulePlan() != null ? appSettings.getFloodlightSchedulePlan() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getFloodlightSchedulePlan()));
        settings.setFloodlightMode(appSettings.getFloodlightMode() != null ? appSettings.getFloodlightMode() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getFloodlightMode()));

        settings.setVideo12HourSwitch(appSettings.getVideo12HourSwitch() != null ? appSettings.getVideo12HourSwitch() : (storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getVideo12HourSwitch()));

        settings.setPropertyJson(appSettings.getPropertyJson());
        return settings;
    }

    private static Integer getNightVisionSensitivity(DeviceAppSettingsDO appSettings, DeviceSettingsDO storeDeviceSettingsDO){
        if(appSettings.getNightVisionSensitivity() != null){
            return appSettings.getNightVisionSensitivity();
        }

        if(appSettings.getNightThresholdLevel() != null){
            return NightThresholdLevelEnums.queryByCode(appSettings.getNightThresholdLevel()).getValue();
        }

        return  storeDeviceSettingsDO == null ? null : storeDeviceSettingsDO.getNightVisionSensitivity();
    }

    public static GetParameterRequest InitGetRequest(String requestId) {
        GetParameterRequest request = new GetParameterRequest();
        request.setId(requestId);
        request.setTime(PhosUtils.getUTCStamp());
        request.addDesiredParameter("pir");
        request.addDesiredParameter("irThreshold");
        request.addDesiredParameter("pirSirenDuration");
        request.addDesiredParameter("recLen");
        request.addDesiredParameter("language");
        request.addDesiredParameter("timeZone");
        request.addDesiredParameter("nightVisionMode");
        request.addDesiredParameter("whiteLightScintillation");
        request.addDesiredParameter("devicePersonDetect");
        request.addDesiredParameter("nightThresholdLevel");
        request.addDesiredParameter("motionTrack");
        request.addDesiredParameter("motionTrackMode");
        request.addDesiredParameter("recLamp");
        request.addDesiredParameter("voiceVolumeSwitch");
        request.addDesiredParameter("voiceVolume");
        request.addDesiredParameter("alarmVolume");
        request.addDesiredParameter("cryDetect");
        request.addDesiredParameter("cryDetectLevel");

        request.addDesiredParameter("deviceSupportLanguage");
        request.addDesiredParameter("autoLog");
        request.addDesiredParameter("antiflicker");
        request.addDesiredParameter("mirrorFlip");
        request.addDesiredParameter("doorBellRingKey");
        request.addDesiredParameter("cooldownInS"); // 下发拍摄间隔key
        request.addDesiredParameter("defaultCodec"); // 下发设备默认编码key
        request.addDesiredParameter("liveAudioToggleOn"); // 下发直播收音key
        request.addDesiredParameter("recordingAudioToggleOn"); // 下发录像直播收音key
        request.addDesiredParameter("liveSpeakerVolume"); // 下发直播对讲音量key
        request.addDesiredParameter("alarmWhenRemoveToggleOn"); // 下发拆除报警开关key
        request.addDesiredParameter("deviceCallToggleOn"); // 下发拆除报警开关key
        request.addDesiredParameter("chargeAutoPowerOnSwitch"); // 充电自动开机开关key
        return request;
    }

    public SetParameterRequest ToRequest(String requestId) {
        SetParameterRequest request = new SetParameterRequest();
        request.setId(requestId);
        request.setTime(PhosUtils.getUTCStamp());
        if (null != this.getPir()) {
            request.setParameterValuePair("pir", this.getPir().intValue() == 0 ? 0 : this.getMotionSensitivity());
        }
        if (null != this.getIrThreshold()) {
            request.setParameterValuePair("irThreshold", this.getIrThreshold().intValue() == 0 ? 0 : this.getNightVisionSensitivity());
            request.setParameterValuePair("nightThresholdLevel", this.getIrThreshold().intValue() == 0 ? 0 : this.getNightThresholdLevel());
        }

        if (null != this.getNeedAlarm()) {
            request.setParameterValuePair("pirSirenDuration", this.getNeedAlarm().intValue() == 0 ? 0 : this.getPirSirenDuration());
        }
        if (null != this.getNeedVideo()) {
            request.setParameterValuePair("recLen", this.getNeedVideo().intValue() == 0 ? 0 : this.getRecLen());
        }
        if (null != this.getLanguage()) {
            request.setParameterValuePair("language", this.getLanguage());
        }
        if (null != this.getTimeZone()) {
            request.setParameterValuePair("timeZone", DateUtils.getOffset(this.timeZone));
        }

        if (null != this.getNightVisionMode()) {
            request.setParameterValuePair("nightVisionMode", this.getNightVisionMode());
        }
        if (null != this.getWhiteLightScintillation()) {
            request.setParameterValuePair("whiteLightScintillation", this.getWhiteLightScintillation());
        }
        if (null != this.getWhiteLight()) {
            request.setParameterValuePair("whiteLight", this.getWhiteLight());
        }

        if (null != this.getDevicePersonDetect()) {
            request.setParameterValuePair("devicePersonDetect", this.getDevicePersonDetect());
        }

        if (null != this.getMotionTrack()) {
            request.setParameterValuePair("motionTrack", this.getMotionTrack());
        }

        if (null != this.getMotionTrackMode()) {
            request.setParameterValuePair("motionTrackMode", this.getMotionTrackMode());
        }

        if (null != this.getRecLamp()) {
            request.setParameterValuePair("recLamp", this.getRecLamp());
        }

        if (null != this.getVoiceVolumeSwitch()) {
            request.setParameterValuePair("voiceVolumeSwitch", this.getVoiceVolumeSwitch());
        }

        if (null != this.getVoiceVolume()) {
            request.setParameterValuePair("voiceVolume", this.getVoiceVolume());
        }

        if (null != this.getAlarmVolume()) {
            request.setParameterValuePair("alarmVolume", this.getAlarmVolume());
        }

        if (null != this.getCryDetect()) {
            request.setParameterValuePair("cryDetect", this.getCryDetect());
        }

        if (null != this.getCryDetectLevel()) {
            request.setParameterValuePair("cryDetectLevel", this.getCryDetectLevel());
        }

        if (null != this.getAutoLog()) {
            request.setParameterValuePair("autoLog", this.getAutoLog());
        }
        if (null != this.getAntiflickerSwitch()) {
            request.setParameterValuePair("antiflicker", this.getAntiflickerSwitch().intValue() == 0 ? 0 : this.getAntiflicker());
        }
        if (null != this.getMirrorFlip()) {
            request.setParameterValuePair("mirrorFlip", this.getMirrorFlip());
        }
        if (null != this.getDoorBellRingKey()) {
            request.setParameterValuePair("doorBellRingKey", this.getDoorBellRingKey());
        }
        // 下发拍摄间隔value
        int publishCooldownInS = OpenApiDeviceConfig.getPublishCooldownInS(this.getCooldownUserEnable(), this.getCooldownInS());
        request.setParameterValuePair("cooldownInS", publishCooldownInS);
        if (this.getDefaultCodec() != null) {
            request.setParameterValuePair("defaultCodec", this.getDefaultCodec());
        }
        if (this.getLiveAudioToggleOn() != null) {
            request.setParameterValuePair("liveAudioToggleOn", this.getLiveAudioToggleOn());
        }
        if (this.getRecordingAudioToggleOn() != null) {
            request.setParameterValuePair("recordingAudioToggleOn", this.getRecordingAudioToggleOn());
        }
        if (this.getLiveSpeakerVolume() != null) {
            request.setParameterValuePair("liveSpeakerVolume", this.getLiveSpeakerVolume());
        }
        if (this.getAlarmWhenRemoveToggleOn() != null) {
            request.setParameterValuePair("alarmWhenRemoveToggleOn", this.getAlarmWhenRemoveToggleOn());
        }
        if (this.getDeviceCallToggleOn() != null) {
            request.setParameterValuePair("deviceCallToggleOn", this.getDeviceCallToggleOn());
        }
        if (this.getChargeAutoPowerOnSwitch() != null) {
            request.setParameterValuePair("chargeAutoPowerOnSwitch", this.getChargeAutoPowerOnSwitch());
        }
        if (this.getChargeAutoPowerOnCapacity() != null) {
            request.setParameterValuePair("chargeAutoPowerOnCapacity", this.getChargeAutoPowerOnCapacity());
        }
        if (this.getPowerSource() != null) {
            request.setParameterValuePair(DeviceModelSettingConstants.KEY_POWER_SOURCE, this.getPowerSource());
        }
        request.setParameterValuePair("alarmDuration", 5); // Forcely set to 5 secs

        if(this.getPropertyJson()!=null) {
            for (Map.Entry<String, Object> entry : this.getPropertyJson().entrySet()) {
                request.setParameterValuePair(entry.getKey(), entry.getValue());
            }
        }
        return request;
    }

    // status->request-deviceSupport过渡期间兼容
    public List<Integer> getDoorBellRingIdList(CloudDeviceSupport cloudDeviceSupport) {
        if (cloudDeviceSupport != null && !CollectionUtils.isEmpty(cloudDeviceSupport.getSupportDoorBellRingKey())) {
            return cloudDeviceSupport.getSupportDoorBellRingKey();
        } else if (StringUtils.hasLength(this.supportDoorBellRingKey)) {
            return FormatUtil.initList(Arrays.asList(this.supportDoorBellRingKey.split(",")));
        } else {
            return Lists.newArrayList();
        }
    }

    // status->request-deviceSupport过渡期间兼容
    public List<String> getDeviceSupportLanguageList(CloudDeviceSupport cloudDeviceSupport) {
        if (cloudDeviceSupport != null && !CollectionUtils.isEmpty(cloudDeviceSupport.getDeviceSupportLanguage())) {
            return cloudDeviceSupport.getDeviceSupportLanguage();
        } else if (StringUtils.hasLength(this.deviceSupportLanguage)) {
            return Arrays.asList(this.deviceSupportLanguage.split(","));
        } else {
            return Arrays.asList("cn", "eu");
        }
    }

    public void addToPropertyJson(String name, Object value) {
        if(propertyJson == null){
            propertyJson = new JSONObject();
        }
        propertyJson.put(name, value);
    }

    public static DeviceSettingsDO createFromMap(Map<String, Object> map, ThingModel thingModel) {
        JSONObject propertyJson = null;
        if(thingModel != null) {
            propertyJson = new JSONObject();
            // 使用迭代器安全移除元素
            Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (thingModel.getIdentifier2Property().containsKey(entry.getKey())) {
                    propertyJson.put(entry.getKey(), entry.getValue());
                    iterator.remove();
                }
            }
        }

        ObjectMapper objectMapper = new ObjectMapper();
        DeviceSettingsDO deviceSettingsDO = objectMapper.convertValue(map, DeviceSettingsDO.class);
        if(propertyJson != null){
            deviceSettingsDO.setPropertyJson(propertyJson);
        }
        return deviceSettingsDO;
    }

    // 获取存储开关值
    // 1：开启 camera 存储。2：开启 后端存储。3：同时开启 camera 存储 和 后端存储。
    public static BitSet getStoreSwitch(ThingModel thingModel, DeviceSettingsDO deviceSettingsDO) {
        final JSONObject propertyJson = Optional.ofNullable(deviceSettingsDO).map(DeviceSettingsDO::getPropertyJson).orElse(null);
        final Long value = ThingModel.getPropertyValue(thingModel, propertyJson, STORE_SWITCH_IDENTIFIER, Long.class);
        return BitSet.valueOf(new long[]{value != null ? value : 0});
    }

    // 视频文件是否存在摄像头本地
    public static boolean isStoreCameraLocal(ThingModel thingModel, CloudDeviceSupport deviceSupport, DeviceSettingsDO deviceSettingsDO) {
        BitSet supportStorage = CloudDeviceSupport.getSupportStorage(deviceSupport);
        BitSet storeSwitch = DeviceSettingsDO.getStoreSwitch(thingModel, deviceSettingsDO);
        return supportStorage.get(STORE_SWITCH_BIT_INDEX_CAMERA_LOCAL) && storeSwitch.get(STORE_SWITCH_BIT_INDEX_CAMERA_LOCAL);
    }

    /**
     * 获取detectBirdAi的值，优先从propertyJson中获取
     * @return 鸟类检测偏好设置
     */
    public Boolean getDetectBirdAi() {
        if (propertyJson != null && propertyJson.containsKey("detectBirdAi")) {
            return propertyJson.getBoolean("detectBirdAi");
        }
        return detectBirdAi;
    }

    /**
     * 设置detectBirdAi的值，同时更新到propertyJson中
     * @param detectBirdAi 鸟类检测偏好设置
     */
    public void setDetectBirdAi(Boolean detectBirdAi) {
        this.detectBirdAi = detectBirdAi;
        addToPropertyJson("detectBirdAi", detectBirdAi);
    }

    /**
     * 获取detectNuisanceAnimalAi的值，优先从propertyJson中获取
     * @return 小动物检测偏好设置
     */
    public Boolean getDetectNuisanceAnimalAi() {
        if (propertyJson != null && propertyJson.containsKey("detectNuisanceAnimalAi")) {
            return propertyJson.getBoolean("detectNuisanceAnimalAi");
        }
        return detectNuisanceAnimalAi;
    }

    /**
     * 设置detectNuisanceAnimalAi的值，同时更新到propertyJson中
     * @param detectNuisanceAnimalAi 小动物检测偏好设置
     */
    public void setDetectNuisanceAnimalAi(Boolean detectNuisanceAnimalAi) {
        this.detectNuisanceAnimalAi = detectNuisanceAnimalAi;
        addToPropertyJson("detectNuisanceAnimalAi", detectNuisanceAnimalAi);
    }

}
