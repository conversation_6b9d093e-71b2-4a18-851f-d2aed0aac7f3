package com.addx.iotcamera.bean.app.questionback;

import com.addx.iotcamera.bean.app.AppRequestBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashSet;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionBackCommitRequest extends AppRequestBase {
    @NotNull(message = "视频id不能为空")
    private Integer libraryId;
    private String traceId;
//    @NotNull(message = "用户id不能为空")
    private Integer userId;
    @NotNull(message = "问题反馈选项编号不能为空")
    private LinkedHashSet<Integer> codes;

    private String serialNumber;

    private String feedbackType = "video";

    private String remark;


    public enum FeedbackType {
        VIDEO("video"),
        SHORT_SUMMARY("shortSummary");
        private String value;

        FeedbackType(String value) {
            this.value = value;
        }

        public String value() {
            return value;
        }
    }

}
