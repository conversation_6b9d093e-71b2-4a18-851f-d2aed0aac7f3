package com.addx.iotcamera.bean.db;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductDO implements Serializable {
    private Integer id;
    private Integer type; // ProductTypeEnums,类型。0-购买;1-升级;2-订阅(连续包月)
    private Integer keyId;
    private Integer price;
    private Integer currency;
    private String subject;
    private String body;
    private Integer status;
    private Date cdate;
    private Date mdate;
    private Integer tierId;
    private String additionalTierUid;
    private Integer month;

    // 商品专属于某个APP，不能跨app使用，因此增加这个字段
    private String tenantId;
    @Schema(title = "订阅期间,0月1年")
    private Integer subscriptionPeriod;
    @Schema(title = "是否展示在套餐页")
    private Integer showInTier;

    @Schema(title = "订阅组id")
    private String subscriptionGroupId;
}
