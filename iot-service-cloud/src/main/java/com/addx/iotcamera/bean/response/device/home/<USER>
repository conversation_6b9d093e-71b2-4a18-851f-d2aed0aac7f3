package com.addx.iotcamera.bean.response.device.home;

import com.addx.iotcamera.bean.domain.LocationDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceHomeResponse {
    private Long homeId;
    private String homeName;

    List<LocationDO> location;
}
