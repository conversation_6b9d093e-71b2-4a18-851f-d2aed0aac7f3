package com.addx.iotcamera.bean.response.device.home;

import com.addx.iotcamera.bean.device.attributes.DeviceModifiableAttribute;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceHomeModeSettingResponse {
    @ApiModelProperty("device home model delay")
    private List<DeviceModifiableAttribute> modifiableAttributes;

    @ApiModelProperty("设备Home-mode setting")
    private List<DeviceHomeModeDevice> cameraList;


    @Data
    public static class DeviceHomeModeDevice{
        private String serialNumber;
        private String deviceName;
        private Integer online;
        private String icon;
        @Schema(title = "休眠状态")
        private Integer deviceStatus;

        @Schema(title = "设备类型")
        private Integer deviceStorageType;
        @Schema(title = "是否三方卡")
        private Integer simThirdParty;
        @Schema(title = "是否vip")
        private boolean deviceInVip;


        private List<DeviceModifiableAttribute> modifiableAttributes;
    }
}
