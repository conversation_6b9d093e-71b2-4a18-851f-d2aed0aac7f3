package com.addx.iotcamera.bean.domain.library;

import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.response.user.RecommendProductDO;
import com.addx.iotcamera.bean.response.user.RecommendProduct4GDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TierFreeUserExpireDO {
    @Schema(title = "提醒")
    private boolean notify;
    @Schema(title = "提醒类型")
    private Integer notifyType;

    private Integer lookBackDay;
    private Double storege;

    @Schema(title = "提醒message")
    private String notifyMessage;

    @Schema(title = "推荐商品")
    private UserVipTier.RecommendProduct recommendProduct;
    @Schema(title = "推荐商品-月订阅商品")
    private List<UserVipTier.RecommendProduct> recommendProductV1;

    @Schema(title = "推荐商品实例")
    private RecommendProductDO recommendProductDO;

    @Schema(title = "4G推荐商品实例")
    private RecommendProduct4GDO recommendProduct4GDO;


    @Schema(title = "推荐商品类型, 0 云服务 1 4G流量套餐")
    private Integer recommendProductType;

    @Schema(title = "4G流量设备是否有插入官方sim卡")
    private boolean containsOfficialSim;

    @Schema(title = "banner提醒次数")
    private Integer notifyCount;

    @Schema(title = "免费升级")
    private String upgradeFreeBtn;

    @Schema(title = "坑位名称")
    private String slotName;

    @Schema(title = "是否有4G设备")
    private Boolean hasDevice4G;

    @Schema(title = "4G设备绑定记录")
    private String operationId;
}
