package com.addx.iotcamera.bean.openapi;

import com.addx.iotcamera.bean.keyshot.KeyShot;
import com.addx.iotcamera.enums.CodeEnum;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.addx.iot.common.proto.iot_local.VideoEvent;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class SaasAITaskOM {

    @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
    private Type type;
    private String traceId;
    private String tenantId;
    private String deviceSn;
    private String ownerId;
    private String outParams;

    /* type=AI_RECOGNITION_RESULT 时特有字段 begin*/
    private String taskId;
    private Integer order;
    private List<ImageOM> images;
    /* type=AI_RECOGNITION_RESULT 时特有字段 end*/

    /* type=AI_EVENT_RESULT 时特有字段 begin*/
    private String coverImageUrl;
    private List<EventOM> events;

    private List<KeyShot> keyshots; // 关键帧信息

    private List<ReIdOM> reIds;
    /* type=AI_EVENT_RESULT 时特有字段 end*/

    private String description;

    private SafetyLevel safetyLevel; // 安全等级

    /**
     * Safemo 专用的协议数据，为SS121/SS131，TODO： 二期同时兼容方案和safemo
     */
    private VideoEvent aiExtensionResult;

    @AllArgsConstructor
    @Getter
    public enum Type implements CodeEnum<String> {
        AI_RECOGNITION_RESULT("AI_RECOGNITION_RESULT", "单张图片对象识别结果"),
        AI_EVENT_RESULT("AI_EVENT_RESULT", "视频中中事件分析结果");
        private final String code;
        private final String desc;

        private static final ImmutableMap<String, Type> code2Enum = FuncUtil.createImmutableMap(values(), Type::getCode);

        public static Type codeOf(String code) {
            return code2Enum.get(code);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum SafetyLevel implements CodeEnum<String> {
        NORMAL("NORMAL", "正常"),
        ABNORMAL("ABNORMAL", "不正常");
        private final String code;
        private final String desc;

        private static final ImmutableMap<String, SafetyLevel> code2Enum = FuncUtil.createImmutableMap(values(), SafetyLevel::getCode);

        public static SafetyLevel codeOf(String code) {
            return code2Enum.get(code);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ImageOM {
        private Integer imageOrder; // 图片在视频中的顺序，从0开始
        private String imageUrl; // 图片链接
        private Long timeInVideo; // 图片在视频中的时间戳，相对于视频开始，毫秒值
        private List<RecognisedObject> objects;
    }

    @Data
    @Accessors(chain = true)
    public static class RecognisedObject {
        private String boxId;
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private RecognitionObjectCategory category; // eg:person
        private BigDecimal confidence;
        private List<Integer> activityZoneIds;
        private Identity id;
        private BoxPosition position;
        private List<SubCategory> subCategories;

        private Boolean isInActivityZone = true; // 提供给paas客户的webhook回调
    }

    @Data
    @Accessors(chain = true)
    public static class Identity {
        private Boolean enable;
        private String labelId;
    }

    @Data
    @Accessors(chain = true)
    public static class BoxPosition {
        private BigDecimal left;
        private BigDecimal top;
        private BigDecimal width;
        private BigDecimal height;
    }

    @Data
    @Accessors(chain = true)
    public static class SubCategory {
        private String name;
    }

    @Data
    @Accessors(chain = true)
    public static class EventOM {
        private String name; // eg:vehicle_enter
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private RecognitionObjectCategory objectCategory; // eg:vehicle
        private List<EventTimeRange> timeRanges;
        private String summaryUrl;
        private List<Integer> activityZoneIds;
        private Integer filterResult;
        private String filterReason;
        private List<Integer> boxIds;
        private List<PossibleSubcategory> possibleSubcategory; // 可能的子类别

        private Boolean isInActivityZone = true; // 提供给paas客户的webhook回调
    }

    // eg: "reIds":[{"labelId":"label_qinEGL1QBCIOMUd067yJK5VXSoAPzsT3","eventIndexes":[0]}]
    @Data
    @Accessors(chain = true)
    public static class ReIdOM {
        private String labelId; // 聚类唯一标识
        private int[] eventIndexes; // 与聚类相关联的event的索引
    }

    @Data
    @Accessors(chain = true)
    public static class EventTimeRange {
        private Long begin;
        private Long end;
    }
}
