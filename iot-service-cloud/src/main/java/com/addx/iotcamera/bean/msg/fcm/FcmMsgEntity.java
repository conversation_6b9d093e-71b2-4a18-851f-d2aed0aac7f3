package com.addx.iotcamera.bean.msg.fcm;

import com.addx.iotcamera.bean.msg.MsgEntityBase;
import lombok.Data;

@Data
public class FcmMsgEntity {
    private String title;
    private String body;
    private Integer type;
    private String msgId;
    private String messageId;
    private String traceId;
    private Integer timestamp;
    private String serialNumber;
    private String videoEvent;
    public void ParseFrom(MsgEntityBase msg) {
        this.setMsgId(msg.getMsgId());
        this.setMessageId(msg.getMessageId());
        this.setType(msg.getType());
        this.traceId = msg.getTraceId();
    }
}
