package com.addx.iotcamera.bean.db;

import com.addx.iotcamera.util.MultiResolutionUtil;
import org.addx.iot.domain.extension.video.entity.MultiResolutionInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.addx.iot.domain.extension.video.entity.SliceDetailVO;
import org.springframework.beans.BeanUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * CREATE VIEW `camera`.`user_library` AS
 * select
 * `dl`.`id` AS `id`,
 * `dl`.`date` AS `date`,
 * `dl`.`timestamp` AS `timestamp`,
 * `dl`.`serial_number` AS `serial_number`,
 * `dl`.`image_url` AS `image_url`,
 * `dl`.`video_url` AS `video_url`,
 * `dl`.`period` AS `period`,
 * `dl`.`file_size` AS `file_size`,
 * `dl`.`image_only` AS `image_only`,
 * `dl`.`device_name` AS `device_name`,
 * `dl`.`location_id` AS `location_id`,
 * `dl`.`location_name` AS `location_name`,
 * `ls`.`missing` AS `missing`,
 * `ls`.`marked` AS `marked`,
 * `dr`.`admin_id` AS `admin_id`,
 * `dr`.`admin_name` AS `admin_name`,
 * `dr`.`user_id` AS `user_id`,
 * `dr`.`user_name` AS `user_name`,
 * `dr`.`role` AS `role`
 * from ((`camera`.`device_library` `dl`
 * join `camera`.`library_status` `ls` on((`ls`.`library_id` = `dl`.`id`)))
 * join `camera`.`device_role` `dr` on((`dr`.`serial_number` = `dl`.`serial_number`)));
 */
@Data
public class UserLibraryViewDO extends DeviceLibraryViewDO {
    @Schema(title = "")
    Integer missing;
    Integer marked;
    Integer adminId;
    String adminName;
    Integer userId;
    String userName;
    Integer role;
    /**
     * 所属设备的用户是否vip
     */
    Boolean adminIsVip;

    String activityZoneName;

    String shareUserIds;

    Boolean supportMagicPix;

    private String summaryDescription;

    private List<UserLibraryViewDO> subVideos = new LinkedList<>(); // 双画面子视频列表（videoType=2）

    /* 多分辨率支持 begin */

    private List<MultiResolutionInfo> multiResolutionInfo = new LinkedList<>(); // 多分辨率信息

    private List<UserLibraryViewDO> multiResolutionVideos = new LinkedList<>(); // 多分辨率视频列表（videoType=3）

    /* 多分辨率支持 end */

    private List<SliceDetailVO> sliceList = new LinkedList<>();

    public static String toLogString(UserLibraryViewDO it) {
        if (it != null && it.getSliceList() != null && !it.getSliceList().isEmpty()) {
            UserLibraryViewDO view = new UserLibraryViewDO();
            BeanUtils.copyProperties(it, view, "sliceList"); // 可能很大，只打印部分切片的日志
            JSONObject jsonObj = (JSONObject) JSONObject.toJSON(view);
            jsonObj.fluentPut("firstSlice", it.getSliceList().get(0));
            if (it.getSliceList().size() > 1) {
                jsonObj.fluentPut("lastSlice", it.getSliceList().get(it.getSliceList().size() - 1));
            }
            jsonObj.fluentPut("sliceListSize", it.getSliceList().size());
            return JSON.toJSONString(jsonObj);
        }
        return JSONObject.toJSONString(it);
    }

    public List<MultiResolutionInfo> getMultiResolutionInfo() {
        return MultiResolutionUtil.fillMultiResolutionInfo(this);
    }

}
