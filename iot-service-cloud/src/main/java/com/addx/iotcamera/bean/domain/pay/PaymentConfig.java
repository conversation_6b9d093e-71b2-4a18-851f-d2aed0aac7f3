package com.addx.iotcamera.bean.domain.pay;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PaymentConfig {
    /**
     * 支付宝支付配置开始
     */
    private String aliPublicKey;
    private String aliPrivateKey;
    private String aliOrderUrl;
    private String aliAppId;
    private String aliNotifyUrl;
    private String aliSignType = "RSA2";
    private String aliCharSet = "utf-8";
    private String aliTimeout;
    private String aliBillFilePath;
    /** 支付宝支付配置结束 */

    /**
     * 微信支付配置开始
     */
    private String wechatAppId;
    private String wechatMchId;
    private String wechatNotifyUrl;
    private String wechatKey;
    private String wechatPreOrderUrl;
    private String wechatOrderQueryUrl;
    private String wechatDownloadBillUrl;
    /**
     * 微信支付配置结束
     */

    private String appleVerifyUrl;
    private String appleLocal;
    private String appleBundleId;
    private String applePassword;

    private String googleOrderUrl;
    private String googleTokenUrl;
    private String googleClientId;
    private String googleCilentSecret;
    private String googleRefreshToken;
    private String googlePackage;
    private String googlePublisher;
}
