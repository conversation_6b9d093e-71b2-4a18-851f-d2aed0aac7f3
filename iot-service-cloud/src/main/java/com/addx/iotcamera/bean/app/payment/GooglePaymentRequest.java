package com.addx.iotcamera.bean.app.payment;

import com.addx.iotcamera.bean.app.AppRequestBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class GooglePaymentRequest  extends AppRequestBase {
    /**
     * 令牌
     */
    @NotEmpty(message = "令牌不能为空")
    private String purchaseToken;

    @Min(value = 0,message = "商品id不能小于0")
    private Integer productId;
    @Schema(title = "套餐订阅组id")
    private String subscriptionGroupId;

    @NotEmpty(message = "订单号不能为空")
    private String outTradeNo;

    @Schema(title = "引导来源")
    private Integer guidanceSource = 0;

    @Schema(title = "套餐指定的设备")
    List<String> tierDeviceList;


    private Long orderId;
}
