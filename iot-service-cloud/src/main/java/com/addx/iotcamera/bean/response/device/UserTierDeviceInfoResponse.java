package com.addx.iotcamera.bean.response.device;

import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.app.vip.UserTierDeviceInfo;
import com.addx.iotcamera.bean.domain.DeviceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTierDeviceInfoResponse {
    @Schema(title = "回看天数")
    private Integer rollingDay;
    @Schema(title = "支持的回看天数")
    private List<Integer> supportRollingDay;


    @Schema(title = "服务内生效设备")
    private UserTierDeviceInfo serviceActiveDevice;

    @Schema(title = "不在用户名下的设备list")
    private List<DeviceDO> notInTierDeviceList;
    @Schema(title = "不在用户名下的设备-三方或者空卡")
    private List<DeviceDO> notInTierDeviceThirdPartyList;

    @Schema(title = "服务内鸟类叠加包")
    private List<UserTierDeviceInfo> serviceAdditionList;

    @Schema(title = "服务内鸟类叠加包")
    List<TierInfo.TierDescribe> tierDescribeList;
    @Schema(title = "用vip记录id")
    private Long userVipId;

    @Schema(title = "无套餐名称Key")
    private String outOfPlanKey;
}
