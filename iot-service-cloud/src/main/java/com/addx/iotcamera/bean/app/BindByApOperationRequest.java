package com.addx.iotcamera.bean.app;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BindByApOperationRequest extends AppRequestBase {
    private Integer locationId;
    private String deviceLanguage = "en";
    private String timeZone = "Asia/Shanghai";

    private String bindCode;

    @NotNull(message = "networkName不能为空")
    private String networkName;
    @Builder.Default
    private String password = "";

    @Builder.Default
    private String nodeUrl = "";
}
