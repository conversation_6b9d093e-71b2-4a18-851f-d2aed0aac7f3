package com.addx.iotcamera.bean.response.user;

import com.addx.iotcamera.bean.app.vip.UserVipTier;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RecommendProductDO {
    @Schema(title = "推荐商品-月订阅")
    private List<UserVipTier.RecommendProduct> recommendProductList;
    @Schema(title = "推荐商品-年订阅")
    private List<UserVipTier.RecommendProduct> recommendProductYearList;

    @Schema(title = "推荐商品默认选中商品Id")
    private Integer defaultSelectedProductId;
    @Schema(title = "年订阅ab实验组")
    private String yearFeatureGroup;

    @Schema(title = "button ab实验组")
    private String buttonFeatureGroup;

    @Schema(title = "freeTrial天数")
    private Integer freeTrialDay;

    @Schema(title = "ab实验组命中结果")
    private Map<String, Integer> abFeatureSimpleResultList;

}
