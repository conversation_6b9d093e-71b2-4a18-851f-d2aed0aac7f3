package com.addx.iotcamera.bean.app.payment;

import com.addx.iotcamera.bean.app.AppRequestBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplePaymentRequest  extends AppRequestBase {
    /**
     * 苹果返回的信息
     */
    @NotEmpty(message = "返回信息不能为空")
    private String receiptData;

    /**
     * 苹果订单号
     */
    @NotEmpty(message = "transactionId不能为空")
    private String transactionId;

    /**
     * 商品Id
     */
    private Integer productId;

    @Schema(title = "引导来源")
    private Integer guidanceSource = 0;

    @Schema(title = "套餐指定的设备")
    List<String> tierDeviceList;
}
