package com.addx.iotcamera.bean.msg;

import com.addx.iotcamera.bean.msg.fcm.FcmMsgEntity;
import com.addx.iotcamera.bean.msg.ios.IosMsgEntity;
import com.addx.iotcamera.bean.msg.umeng.UmengMsgEntity;
import lombok.Data;
import org.addx.iot.common.utils.PhosUtils;

import java.util.UUID;

@Data
public class MsgEntityBase {
    private String title;
    private String body;
    private Integer type;
    private Integer time;
    private String thumbnailUrl;
    // 老的字段，不建议使用
    private String msgId;
    // snowplow 埋点新增字段 消息唯一ID，用户服务端和APP能够串联
    private String messageId;
    // add the field for backward-compatible
    private String traceId;
    private String videoEvent;

    public FcmMsgEntity defaultFcmEntity() {
        return new FcmMsgEntity();
    }

    public UmengMsgEntity defaultUmengEntity() {
        return new UmengMsgEntity();
    }

    public IosMsgEntity defaultIosEntity() {
        return new IosMsgEntity();
    }

    public MsgEntityBase() {
        this.time = PhosUtils.getUTCStamp();
        this.setMsgId((PhosUtils.getUTCStamp() % 10000) + "");
        this.setMessageId(UUID.randomUUID().toString());
    }
}
