package com.addx.iotcamera.bean.db;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PaymentFlow {
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 流水号
     */
    private String outTradeNo;
    /**
     * 交易平台交易号
     */
    private String tradeNo;
    /**
     * 支付渠道
     */
    private Integer type;
    /**
     * 用户Id
     */
    private Integer userId;
    private Integer productId;
    /**
     * 商品主题
     */
    private String productSubject;
    /**
     * 商品详情
     */
    private String productBody;
    /**
     * 总金额,单位分
     */
    private Integer amount;
    /**
     * 币种
     */
    private Integer currency;
    /**
     * 开启时间
     */
    private Integer timeStart;
    /**
     * 结束时间
     */
    private Integer timeEnd;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 支付类型
     */
    private Integer tradeType;
    /**
     * 扩展信息
     */
    private String extend;
    /**
     * 返回信息
     */
    private String returnInfo;
    /**
     * 所属公司
     */
    private String tenantId;
    /**
     * 创建时间
     */
    private Integer cdate;
    /**
     * 最后修改时间
     */
    private Integer mdate;
    @Schema(title = "付款时app版本")
    String appVersionName;
    @Schema(title = "付款时serveVersion")
    String serveVersion;
    @Schema(title = "退款时app版本")
    String refundAppVersionName;
    @Schema(title = "退款时serveVersion")
    String refundServeVersion;

    @Schema(title = "是否退款,0否1是")
    Integer refund;

    @Schema(title = "退款时间")
    Integer refundTime;

    @Schema(title = "退款原因")
    Integer refundReason;

    @Schema(title = "首月免费")
    Integer freeTrial;

    @Schema(title = "购买时间戳")
    private Integer purchaseTime;

    @Schema(title = "购买时间")
    private String purchaseDatePst;
    @Schema(title = "购买时间")
    private String purchaseDate;
    @Schema(title = "本期账单过期时间-也是下次续订时间")
    private Integer expireTime;
    private String orderInfo;
    private String orderInfoV2;
}
