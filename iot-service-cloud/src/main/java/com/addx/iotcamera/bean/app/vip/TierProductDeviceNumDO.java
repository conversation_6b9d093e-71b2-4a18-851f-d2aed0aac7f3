package com.addx.iotcamera.bean.app.vip;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class TierProductDeviceNumDO {
    @Schema(title = "v2套餐列表")
    SubscriptionPeriodProduct tierV2List;
    
    @Schema(title = "v1套餐列表")
    SubscriptionPeriodProduct tierV1List;
    
    @Data
    public static class SubscriptionPeriodProduct{
        @Schema(title = "月订阅套餐商品list")
        private List<SubscriptionProductInfo> monthlyProductList;

        @Schema(title = "年订阅套餐商品list")
        private List<SubscriptionProductInfo> yearlyProductList;
    }

    @Data
    public static class SubscriptionProductInfo{
        @Schema(title = "商品id")
        private Integer productId;
        @Schema(title = "套餐id")
        private Integer tierId;
        @Schema(title = "限制设备数量,0无限制")
        private Integer deviceNum;
        @Schema(title = "会看天数")
        private Integer dayLookBack;
        @Schema(title = "是否展示在套餐页")
        private Boolean showInTier;
        @Schema(title = "套餐等级")
        private String tierLevel;
        @Schema(title = "订阅组id")
        private String subscriptionGroupId;
    }
}
