package com.addx.iotcamera.bean.app;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 状态标记请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusMarkRequest extends AppRequestBase {

    /**
     * 记录状态的key
     */
    private String statusKey;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 设备序列号
     */
    private String serialNumber;
} 