package com.addx.iotcamera.bean.openapi;

import com.addx.iotcamera.bean.domain.library.ActivityZone;
import com.addx.iotcamera.enums.CodeEnum;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.*;

@Data
@Accessors(chain = true)
public class SaasAITaskIM {

    @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
    private InputType inputType; // 1.输入类型
    @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
    private OutputType outputType; // 2.输出类型
    private String traceId; // 3.视频唯一标识
    private String deviceSn; // 4.设备唯一标识
    private String ownerId; // 5.视频拥有者Id。用于id识别，如果不需要id，允许为Null
    private String videoUrl; // 6.视频切片url
    private List<ImageIM> images; // 7.图片序列
    private Integer order; // 8.ai分析请求的顺序
    private Integer isLast; // 9.是否最后是一个片段 0-否，1-是
    private Long timeout; // 10.超时时间，单位:ms
    private OutStorage outStorage; // 11.输出内容的存储
    private String outParams; // 12.输出的参数,ai服务直接透传即可
    private String taskId; // 13.任务唯一标识
    private Integer taskSendTime; // 14.任务发送时间，unix_timestamp
    private List<ActivityZone> activityZoneList; // 15.需要分析的活动区域列表
    private List<RecognitionObject> recognitionObjects; // 16.需要识别的对象列表
    private String tenantId; // 17.app的唯一标识
    private IdBox idBox; // 18.事件识别框的配置
    private String modelNo; // 19.设备型号。字符串，允许为空
    private String outputTopic; // 20.算法对外输出结果的kafka topic

    private WatermarkPosition timeWatermarkPosition; // 21.时间水印位置信息
    private WatermarkPosition logoWatermarkPosition; // 22.logo水印位置信息

    private Context context; // 调用链中的公共参数。 { "isVip":1 }

    private Integer sliceTotalNum; // 切片总数。只有设备端切片且order=-1的消息才有值

    // 优化项目集合。类型：String[]。目前取值自有一个: "a4x_sdk"：自有业务减少一些不必要的s3上传。
    private List<String> developerId;

    private String deviceIp; // 切片上报接口的调用方ip地址
    private String countryNo; // 设备当前绑定管理员所在国家编码

    public SaasAITaskIM() {
        this.images = new LinkedList<>();
        this.activityZoneList = new LinkedList<>();
        this.recognitionObjects = new LinkedList<>();
        this.idBox = new IdBox();
        this.context = new Context();
    }

    @AllArgsConstructor
    @Getter
    public enum InputType implements CodeEnum<Integer> {
        SLICE(0), IMAGE(1);
        private final Integer code;

        private static final ImmutableMap<Integer, InputType> code2Enum = FuncUtil.createImmutableMap(values(), it -> it.getCode());

        public static InputType codeOf(Integer code) {
            return code2Enum.get(code);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum OutputType implements CodeEnum<Integer> {
        KAFKA(1);
        private final Integer code;

        private static final ImmutableMap<Integer, OutputType> code2Enum = FuncUtil.createImmutableMap(values(), it -> it.getCode());

        public static OutputType codeOf(Integer code) {
            return code2Enum.get(code);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum RecognitionObjectFunction implements CodeEnum<String> {
        RECOGNITION("RECOGNITION", "单张图片对象识别"),
        EVENT("EVENT", "分析视频中对象的事件"),
        ID("ID", "对ownerId下的所有视频中的同一个对象进行标记");
        private final String code;
        private final String desc;

        private static final ImmutableMap<String, RecognitionObjectFunction> code2Enum = FuncUtil.createImmutableMap(values(), it -> it.getCode());

        public static RecognitionObjectFunction codeOf(String code) {
            return code2Enum.get(code);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ImageIM {
        private Integer imageOrder; // 图片在视频中的顺序，从0开始
        private String imageUrl; // 图片链接
        private Long timeInVideo; // 图片在视频中的时间戳，相对于视频开始，毫秒值
    }

    @Data
    @Accessors(chain = true)
    public static class OutStorage {
        private String serviceName;
        private String clientRegion;
        private String bucket;
        private String keyPrefix;
        private Integer expiredSeconds;
        private ExpiredConfig expiredConfig;

        @Data
        @Accessors(chain = true)
        public static class ExpiredConfig {
            private Integer objectImage; // 对象结果的图片 iot:86400
            private Integer eventCoverImage; // 事件结果的封面图 iot:5184000
            private Integer eventSummary; // 事件结果的事件图 iot:86400
        }
    }

    @Data
    @Accessors(chain = true)
    public static class RecognitionObject {
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private RecognitionObjectCategory category;
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private Set<RecognitionObjectFunction> functions;
    }

    /*
    example:
    {
        "colors": [
            {"name":"person","color":"FF6A6A"},
            {"name":"pet","color":"FF6A6A"},
            {"name":"vehicle","color":"FF6A6A"},
            {"name":"package","color":"FF6A6A"}
        ],
        "visualizeRecognition": ["person", "pet", "vehicle", "vehicle"]
    }
     */
    @Data
    @Accessors(chain = true)
    public static class IdBox {
        private List<IdBoxColor> colors; // 配置各种对象识别框的颜色。
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private Set<RecognitionObjectCategory> visualizeRecognition; // 只有在列表中的对象在“对象识别”返回的图片中，才画识别框。

        public IdBox() {
            this.colors = new LinkedList<>();
            this.visualizeRecognition = new LinkedHashSet<>();
        }

        public IdBox addIdBoxColor(IdBoxColor idBoxColor) {
            this.colors.add(idBoxColor);
            return this;
        }

        public IdBox addVisualizeRecognition(RecognitionObjectCategory category) {
            this.visualizeRecognition.add(category);
            return this;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class IdBoxColor {
        @JSONField(serializeUsing = CodeEnum.JSONConvertor.class, deserializeUsing = CodeEnum.JSONConvertor.class)
        private RecognitionObjectCategory name;
        private String color;
    }

    public SaasAITaskIM addImage(ImageIM image) {
        this.images.add(image);
        return this;
    }

    public SaasAITaskIM addActivityZone(ActivityZone activityZone) {
        this.activityZoneList.add(activityZone);
        return this;
    }

    public SaasAITaskIM addRecognitionObject(RecognitionObject recognitionObject) {
        this.recognitionObjects.add(recognitionObject);
        return this;
    }

    @Data
    @Accessors(chain = true)
    public static class Context {
        private Integer vipCountType;
    }

    // 水印位置信息结构
    @Data
    @Accessors(chain = true)
    public static class WatermarkPosition {
        private Integer x;      // 左上角x坐标
        private Integer y;      // 左上角y坐标
        private Integer width;  // 宽度
        private Integer height; // 高度
    }

}
