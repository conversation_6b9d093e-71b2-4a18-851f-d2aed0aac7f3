package com.addx.iotcamera.bean.msg.xinge;

import com.addx.iotcamera.bean.msg.MsgType;
import lombok.Data;

@Data
public class VideoCustomerContentMessage extends CustomerContentBase {
    public VideoCustomerContentMessage() {
        super();
        this.setType(MsgType.NEW_VIDEO_MSG);
    }


    private Boolean checkPushIntent = false;
    private Integer userId;
    private String tenantId;
    private String node;

    // magicPix功能
    private Boolean supportMagicPix;

    private Integer alarmDelayTimeStamp;
    private Integer alarmDuration;

    private VideoCustomerContentMessage.MsgDevice device = new VideoCustomerContentMessage.MsgDevice();
    private VideoCustomerContentMessage.MsgLibrary library = new VideoCustomerContentMessage.MsgLibrary();

    @Data
    public class MsgLibrary {
        private Integer libraryId;
        private String imageUrl;
        private String videoUrl;
    }

    @Data
    public class MsgDevice {
        private String serialNumber;
        private String deviceName;
    }    

}
