package com.addx.iotcamera.bean.app;

import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.enums.BindContentSrc;
import com.addx.iotcamera.enums.DeviceNetType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BindOperationRequest extends AppRequestBase {
    private Integer locationId;
    private String deviceLanguage = "en";
    private String timeZone = "Asia/Shanghai";

    @NotNull(message = "二维码长度不能为空")
    private Integer width;
    @NotNull(message = "二维码高度不能为空")
    private Integer height;
//    @NotNull(message = "networkName不能为空")
    private String networkName;
    @Min(value = 0, message = "type 最小为0")
    @Max(value = 2, message = "type 最大为2")
    private Integer type; // 1-当前绑定用户重选绑定
    @Builder.Default
    private String password = "";
    @Builder.Default
    private Integer codeType = 0;
    private String bindCode;

    /**
     * 绑定内容来源。0-扫码绑定；1-ap绑定；2-modeMatcher转发
     * app通过什么途径给设备传递绑定信息
     * 枚举：BindContentSrc
     */
    @Builder.Default
    private Integer bindContentSrc = BindContentSrc.QRCODE.getCode();
    /**
     * 设备网络类型。0-WIFI；1-有线
     * 这个字段只代表绑定时设备用的什么网络。设备可能同时支持WIFI和有线
     * 枚举：DeviceNetType
     */
    @Builder.Default
    private Integer deviceNetType = DeviceNetType.WIFI.getCode();

    /** /bindCableDevice 接口特有字段 **/
    private String userSn;

    @Builder.Default
    private String apn = "";

    @Builder.Default
    private String mcc = "";

    @Builder.Default
    private String mnc = "";

    @Builder.Default
    private Integer apnv = 0;

    private String nodeUrl;
}
