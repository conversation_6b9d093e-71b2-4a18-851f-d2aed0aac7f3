package com.addx.iotcamera.bean.app.vip;

import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.uservip.UserTierExpire;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class UserVipTier implements Serializable {
    /**
     * 是否vip(用户维度vip)
     */
    private boolean vip;
    /**
     * 用户绑定的设备sn(vipLevel:0-非VIP；1-基础版；2-高级版；3-专业版)
     */
    public Map<String, Integer> sn2VipLevel;
    /**
     * 是否属于保护期
     */
    private boolean protection;
    /**
     * 当前套餐Id
     */
    private Integer tierId;
    /**
     * 套餐限制设备
     */
    private Integer tierDeviceNum;
    private List<Integer> tierIdList;

    /**
     * 套餐会员名称
     */
    private String tierName;
    /**
     * tierName 文案 key
     */
    private String tierNameKey;
    /**
     * 当前套餐描述
     */
    private String currentTierName;
    /**
     * 属性列表
     */
    private List<TierTerm> tierTermList;
    /**
     * 套餐开始时间
     */
    private Integer effectiveTime;
    /**
     * 套餐结束时间
     */
    private Integer endTime;

    /**
     * 是否需要提醒客户
     */
    private boolean notify;
    /**
     * 提醒到期日
     */
    private Integer notifyTime;
    /**
     * 原套餐名称
     */
    private String lastTierName;

    private List<TierList> subTierList;
    /**
     * 套餐组内已购买套餐列表
     */
    private Map<Integer,List<UserVipDO>> subTierListGroup;
    private boolean tierReceive;
    /**
     * 是否弹出领取提醒
     */
    private boolean shouldReminder;
    /**
     * 0:无需提醒，1：套餐到期提醒,2:套餐变更提醒
     */
    private Integer notifyType;

    private Integer nextTierId;

    private String nextTierName;

    /**
     * 保护期提示消息
     */
    private String protectionMessage;

    /**
     * 提醒剩余天数
     */
    private Integer notifyDay;

    /**
     * 弹出框信息
     */
    private String popupMessage;

    /**
     * 交易记录
     */
    private String tradeNo;

    /**
     * 叠加包套餐信息
     */
    private Map additionalTierInfo;
    /**
     * 是否订阅订单
     */
    private Boolean isSubOrder;

    // ab实验命中结果，要写入header
    private Map<String, Integer> abFeatureSimpleResultList;

    @Schema(title = "当前userVip信息")
    private CurrentUserVipInfo currentUserVipInfo;
    /**
     * 到期提醒list
     */
    List<UserTierExpire> userTierExpireList;

    @Schema(title = "推荐商品")
    private RecommendProduct recommendProduct;


    @Schema(title = "套餐设备信息")
    private Map<Integer,TierDeviceInfo> tierDeviceInfoMap;

    @Data
    public static class TierList {
        private Integer tierId;
        private String additionalTierUid;
        private String tierName;
        private String tierDateStart;
        private String tierDateEnd;
        /**
         * 套餐起始时间戳
         */
        private Integer tierStartTime;
        /**
         * 套餐结束时间戳
         */
        private Integer tierEndTime;
    }


    @Data
    public static class RecommendProduct{
        @Schema(title = "推荐商品id")
        private Integer productId;
        @Schema(title = "最大支持设备数量")
        private Integer maxDeviceNum;
        @Schema(title = "订阅组id")
        private String subscriptionGroupId;

    }

    @Data
    public static class TierDeviceInfo{
        @Schema(title = "是否有生效套餐")
        private Boolean hasVip;
        @Schema(title = "当前套餐类型下生效套餐id")
        private Integer currentTierId;
        @Schema(title = "是否绑定对应类型设备")
        private Boolean hasDevice;
        private Boolean hasReceive;
        private List<UserVipDO> userVipDOList;
    }

    @Data
    public static class CurrentUserVipInfo{
        @Schema(title = "用vip记录id")
        private Long userVipId;

        @Schema(title = "回看天数")
        private Integer rollingDay;
    }
}
