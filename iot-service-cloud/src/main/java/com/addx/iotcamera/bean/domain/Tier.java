package com.addx.iotcamera.bean.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.xmlbeans.impl.xb.xsdschema.All;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Tier {
    int tierId;
    /**
     * 支持回看天数
     */
    int rollingDays;
    /**
     * 支持的存储大小，单位：B
     */
    Long storage;

    /**
     * 套餐限制使用最大设备数量
     */
    Integer maxDeviceNum;

    int status;
    /**
     * 支持的存储大小，单位：GB
     */
    Integer size;
    /**
     * 套餐等级
     */
    Integer level;

    /**
     * 套餐组id
     */
    Integer tierGroupId ;

    @Schema(title = "套餐类型")
    Integer tierType;

    @Schema(title = "套餐服务类型 0云服务套餐 1 4G流量套餐")
    @Builder.Default
    Integer tierServiceType = 0;

    @Schema(title = "app")
    String tenantId;
}
