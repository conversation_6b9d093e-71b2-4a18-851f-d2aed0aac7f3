package com.addx.iotcamera.bean.mqtt.Request;

import lombok.Data;
import org.springframework.util.StringUtils;

public class MqttDeviceBindRequest extends MqttRequestBase {
    private MqttDeviceBindRequestValue value = new MqttDeviceBindRequestValue();

    public MqttDeviceBindRequestValue getValue() {
        return value;
    }

    public void setValue(MqttDeviceBindRequestValue value) {
        this.value = value;
    }

    @Data
    public static class MqttDeviceBindRequestValue {
        private String rid;

        private String userSn = "";          // 产品序列号
        private String serialNumber = "";    // 产品序列号（系统）
        private String originModelNo = "";         // 产品源型号
        private String modelNo = "";         // 产品型号
        private String displayModelNo = "";  // 用户指定展示的型号
        private String macAddress = "";      // mac地址
        private String firmwareId = "";      // 构建版本
        private String mcuNumber = "";       // mcu版本

        public boolean hasDeviceManualInfo() {
            return !StringUtils.isEmpty(userSn)
                    && !StringUtils.isEmpty(serialNumber)
                    && !StringUtils.isEmpty(modelNo)
                    && !StringUtils.isEmpty(firmwareId);
        }
    }
}
