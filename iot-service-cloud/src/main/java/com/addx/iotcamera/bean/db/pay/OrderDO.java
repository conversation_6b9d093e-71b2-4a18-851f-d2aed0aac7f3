package com.addx.iotcamera.bean.db.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDO {
    private Long id;
    private String orderSn;
    private Integer orderType;
    private Integer userId;
    private Integer status;
    private String tenantId;
    private Integer timeStart;
    private Integer timeEnd;
    private String extend;
    private Integer cdate;
    private Integer mdate;
    private String tradeNo;
    private Integer subType;
    private Integer productMonth;
    private Integer sandbox;
    private Integer isClose;
    @Schema(title = "首月免费")
    private Integer freeTrial;
    @Schema(title = "订单取消状态")
    private Integer orderCancel;
    @Schema(title = "订单取消原因")
    private Integer orderCancelReason;
    @Schema(title = "订单取消时间")
    private Integer orderCancelTime;
    @Schema(title = "支付引导")
    @Builder.Default
    private Integer guidanceSource = 0;
    private String cancelEventKey;
    private String orderInfo;
    private String orderInfoV2;
}
