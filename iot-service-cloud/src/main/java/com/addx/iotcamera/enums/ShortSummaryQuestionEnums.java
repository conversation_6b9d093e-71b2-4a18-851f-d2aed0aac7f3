package com.addx.iotcamera.enums;

import com.addx.iotcamera.bean.domain.questionback.QuestionBackOption;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * 视频问题枚举
 * camera.library_question_back.codes
 */
@Slf4j
@ToString
@AllArgsConstructor
public enum ShortSummaryQuestionEnums implements VideoQuestion {

    // 增加视频问题反馈：播放卡顿，播放跳帧，播放器黑屏，音视频不同步，黑屏
    /*
    PLAY_CATON(11, Type.VIDEO_PLAY),
    PLAY_FRAME_SKIPPING(12, Type.VIDEO_PLAY),
    PLAYER_BLACK(13, Type.VIDEO_PLAY),
    AUDIO_VIDEO_NOT_SYNC(14, Type.VIDEO_PLAY),
    CAN_NOT_PLAY(15, Type.VIDEO_PLAY);
    */

    AI_RECOGNITION_ERROR(1100, "feedback_option_AI_results"), // 画面问题
    MOTION_NOT_DETECT(1200, "feedback_option_motion_not_detected"), // 音频问题
    EVENT_NOT_INTERESTED(1300, "feedback_option_not_interested"), // 有趣视频分享
    PLAY_OR_LOADING_ISSUE(1400, "feedback_option_video_issues"), // ai识别错误
    OTHER(1500, "feedback_option_other"); // 其他问题

    // code值设置大一点，留出间隔，方便以后添加
    @Getter
    private final int code;
    // 标题键。值定义在 gitconfig/copywrite/iot.xlsx
    @Getter
    private final String titleKey;

    /**
     * 转换tags为问题反馈选项列表
     *
     * @param language
     * @param checkedCodes
     * @return
     */
    public static List<QuestionBackOption> getQuestionBackOptions(String language, Set<Integer> checkedCodes) {
        List<QuestionBackOption> optionList = new LinkedList<>();
        for (ShortSummaryQuestionEnums enums : ShortSummaryQuestionEnums.values()) {
            QuestionBackOption.QuestionBackOptionBuilder optionBuilder = QuestionBackOption.builder()
                    .code(enums.getCode())
                    .title(enums.getTitle(language));
            optionBuilder.checked(checkedCodes.contains(enums.getCode()));
            optionList.add(optionBuilder.build());
        }
        return optionList;
    }
}
