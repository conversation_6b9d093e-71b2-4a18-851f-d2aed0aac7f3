package com.addx.iotcamera.enums.pay;

import com.addx.iotcamera.util.FuncUtil;
import com.google.common.collect.ImmutableMap;

public enum TierTypeEnums {
    TIER_LEVEL(0, "普通等级套餐"),
    TIER_LEVEL_DEVICE_LIMIT(1, "等级+设备数量限制套餐"),
    TIER_DEVICE_LIMIT(2, "设备数量限制套餐"),
    TIER_4GDATA(4, "4G流量套餐"),
    TIER_FREE_LICENSE(5, "free license 套餐"),
    TIER_REDEEM_CODE(7, "兑换码套餐")
    ;

    private int code;
    private String desc;

    TierTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }


    private static final ImmutableMap<Integer, TierTypeEnums> code2Enum = FuncUtil.createImmutableMap(values(), TierTypeEnums::getCode);
    public static TierTypeEnums codeOf(Integer code) {
        return code2Enum.get(code);
    }
}
