package com.addx.iotcamera.enums;

import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.google.api.client.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * <AUTHOR>
 */

public enum ProductTypeEnums {
    /**
     * 商品归属购买分类
     */
    PURCHASE(0, "购买"),
    RENEW(1, "升级"),
    SUBSCRIBE(2, "订阅"),

    PRODUCT_DEVICE_NUM(3, "设备数量限制-订阅-v2"),
    PRODUCT_LEVEL_DEVICE_NUM(4, "套餐等级-设备数量限制-订阅-v1"),
    PRODUCT_DEVICE_4G(5, "4G设备流量套餐"),
    PRODUCT_BLACK_FRIDAY(6, "黑五套餐套餐")
    ;

    private int code;
    private String desc;

    ProductTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Set<Integer> NEW_PRODUCT_TYPE_SET = new HashSet<>(Arrays.asList(PRODUCT_DEVICE_NUM.getCode(),PRODUCT_LEVEL_DEVICE_NUM.getCode(),PRODUCT_DEVICE_4G.getCode()));

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return desc;
    }

    public static ProductTypeEnums codeOf(Integer code) {
        if (code == null) return null;
        for (ProductTypeEnums value : values()) {
            if (value.getCode() == code) return value;
        }
        return null;
    }


    /**
     * 获取套餐类型对应商品类型
     * @param tierServiceType
     * @return
     */
    public static List<Integer> queryProductTypeListByTierServiceType(@NotNull TierServiceTypeEnums tierServiceType){
        List<Integer> result ;
        switch (tierServiceType){
            case TIER_CLOID_SERVICE:
                result = Arrays.asList(PRODUCT_DEVICE_NUM.getCode(),PRODUCT_LEVEL_DEVICE_NUM.getCode());
                break;
            case TIER_4G:
                result = Collections.singletonList(PRODUCT_DEVICE_4G.getCode());
                break;
            default:
                result = Lists.newArrayList();
                break;
        }
        return result;
    }


    /**
     * 高优先展示的套餐列表
     * @param tierServiceType
     * @return
     */
    public static Integer queryProductTypeByTierServiceTypeV2(@NotNull TierServiceTypeEnums tierServiceType){
        Integer result ;
        switch (tierServiceType){
            case TIER_CLOID_SERVICE:
                result = PRODUCT_DEVICE_NUM.getCode();
                break;
            case TIER_4G:
                result = PRODUCT_DEVICE_4G.getCode();
                break;
            default:
                result = null;
                break;
        }
        return result;
    }

    /**
     * 低优先展示的套餐列表
     * @param tierServiceType
     * @return
     */
    public static Integer queryProductTypeByTierServiceTypeV1(@NotNull TierServiceTypeEnums tierServiceType){
        Integer result ;
        switch (tierServiceType){
            case TIER_CLOID_SERVICE:
                result = PRODUCT_LEVEL_DEVICE_NUM.getCode();
                break;
            default:
                result = null;
                break;
        }
        return result;
    }
}
