package com.addx.iotcamera.enums;

public enum PaymentTypeEnums {
    /**
     * 支付渠道
     */
    ALI(0, "支付宝"),
    WECHAT(1, "微信"),
    APPLE(2, "苹果内购"),
    GOOGLE(3, "谷歌内购"),
    REGISTER(4, "新用户注册"),
    MOMOPAY(5, "MOMOPAY"),
    PRODUCT_EXCHANGE_CODE(6, "兑换码"),
    THIRD_PAY(7, "第三方支付"),
    ADDITIONAL_TIER_FREE_RECEIVE(108, "免费领取叠加包"),
    ;

    private int code;
    private String desc;

    PaymentTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return desc;
    }

    public static PaymentTypeEnums queryByCode(Integer code) {
        for (PaymentTypeEnums typeEnums : values()) {
            if (typeEnums.getCode().equals(code)) {
                //获取指定的枚举
                return typeEnums;
            }
        }
        return null;
    }
}
