package com.addx.iotcamera.publishers.vernemq.responses;


import lombok.Getter;
import lombok.Setter;

public class MqttDeviceBindResponse extends MqttResponseBase {
    public String getName() {
        return "bindOperation";
    }

    private MqttDeviceBindResponseValue value = new MqttDeviceBindResponseValue();

    public MqttDeviceBindResponseValue getValue() {
        return value;
    }

    public void setValue(MqttDeviceBindResponseValue value) {
        this.value = value;
    }

    public class MqttDeviceBindResponseValue {
        private String result = "";
        private Integer userId;
        @Getter
        @Setter
        private String tenantId;

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public Integer getUserId() {
            return userId;
        }

        public void setUserId(Integer userId) {
            this.userId = userId;
        }
    }
}

