package com.addx.iotcamera.publishers.zendesk;

import com.addx.iotcamera.bean.UserBindFailFeedbackEmailDO;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.ZendeskTicketDO;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.app.*;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.UserBindFailFeedbackEmailDAO;
import com.addx.iotcamera.dao.dbt.DbtSafemoBindFailDAO;
import com.addx.iotcamera.dynamo.dao.ZendeskTicketDao;
import com.addx.iotcamera.enums.ZendeskActionEnums;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.EmailUtils;
import com.addx.iotcamera.util.ZendeskUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.IDUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;
import org.zendesk.client.v2.Zendesk;
import org.zendesk.client.v2.model.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static org.addx.iot.common.constant.AppConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Data
public class ZendeskClient implements InitializingBean {

    public static final String APP_TAG_PREFIX = "app_";
    @Value("#{'${zendesk.notSupportedTenantIdSet}'.split(',')}")
    private Set<String> notSupportedTenantIdSet;

    @Value("#{'${zendesk.supportedTenantIdSet}'.split(',')}")
    private Set<String> supportedTenantIdSet;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    private Map<String, String> welcomeHtmlBodyMap;
    private Map<String, String> welcomeSubjectMap;

    private Map<String, String> noBindCameraAfter48hHtmlBodyMap;

    private Map<String, String> noBindCameraAfter48hSubjectMap;

    private Map<String, String> newNoBindCameraAfter48hHtmlBodyMap;

    private Map<String, String> newNoBindCameraAfter48hSubjectMap;

    private Map<String, String> noBindCameraAfter24hHtmlBodyMap;

    private Map<String, String> noBindCameraAfter24hSubjectMap;

    private Map<String, String> safemoBindFailFeedbackSubjectMap;

    private Map<String, String> safemoBindFailFeedbackHtmlBodyMap;

    @Autowired(required = false)
    private AppAccountConfig appAccountConfig;

    @Autowired(required = false)
    private CopyWrite copyWrite;

    @Value("${spring.kafka.topics.zendesk}")
    private String zendeskTopic;

    @Autowired(required = false)
    private UserService userService;

    @Autowired(required = false)
    private IDeviceDAO deviceDAO;

    @Autowired(required = false)
    private IShareDAO shareDAO;

    @Autowired(required = false)
    private ZendeskTicketDao zendeskTicketDao;

    @Autowired(required = false)
    private MqSender mqSender;

    @Autowired(required = false)
    private RedisService redisService;

    @Resource
    private ZendeskConfig zendeskConfig;

    @Resource
    private TenantTierConfig tenantTierConfig;

    @Resource
    private NoSendEmailConfig noSendEmailConfig;

    @Resource
    private EmailGuideConfig emailGuideConfig;

    @Resource
    private NewEmailConfig newEmailConfig;

    @Resource
    private UserBindFailFeedbackEmailDAO userBindFailFeedBackEmailDAO;

    @Resource
    private DbtSafemoBindFailDAO dbtSafemoBindFailDAO;

    @Resource
    private AppWelcomeEmailConfig appWelcomeEmailConfig;

    private static final String LINE_BREAK = "<br/>";
    private static final String HTML_TEMPLATE = "<html><body>$BODY</body></html>";
    private static final String PLACEHOLDER_USER_NAME = "$USER_NAME";
    private static final String PLACEHOLDER_APP_NAME = "$APP_NAME";
    private static final String PLACEHOLDER_SUPPORT_EMAIL = "$SUPPORT_EMAIL";



    public long createUser(String name, String email, Map<String, String> identitiesMap, List<String> tags,String tenantId) throws BaseException {
        User user = new User();
        user.setName(name);
        user.setEmail(email);

        List<Identity> identityList = MapUtils.isEmpty(identitiesMap) ? null : identitiesMap.entrySet().stream()
                .map(identity -> new Identity(identity.getKey(), identity.getValue()))
                .collect(Collectors.toList());
        user.setIdentities(identityList);
        user.setActive(true);

        user.setTags(tags);

        User zdUser = null;
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);
            zdUser = zendesk.createOrUpdateUser(user);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "调用zendesk创建user api失败", e);
            throw new BaseException("调用zendesk创建user api失败");
        }

        return zdUser.getId();
    }

    public boolean updateUser(User user,String tenantId) throws BaseException {
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);

            zendesk.updateUser(user);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "调用zendesk更新user api失败", e);
            throw new BaseException("调用zendesk更新user api失败");
        }
        return true;
    }

    public long createTicket(Ticket.Requester requester, String recipient, List<String> tags, String subject, Comment comment, boolean isPublic,String tenantId) throws BaseException {
        Ticket ticket = new Ticket();
        ticket.setSubject(subject);
        ticket.setComment(comment);
        ticket.setTags(tags);
        ticket.setRequester(requester);
        ticket.setRecipient(recipient);
        ticket.setIsPublic(isPublic);

        Ticket zdTicket = null;
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);

            zdTicket = zendesk.createTicket(ticket);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "调用zendesk创建ticket api失败", e);
            throw new BaseException("调用zendesk创建ticket api失败");
        }

        return zdTicket.getId();
    }

    //ticket可能会被zendesk关闭，调用api失败不throw
    public Ticket getTicket(long ticketId,String tenantId) {
        Ticket ticket = null;
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);

            ticket = zendesk.getTicket(ticketId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "调用zendesk查询ticket api失败", e);
        }

        return ticket;
    }

    //ticket可能会被zendesk关闭，调用api失败不throw
    public boolean updateTicket(Ticket ticket,String tenantId) {
        boolean isSuccess = false;
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);

            zendesk.updateTicket(ticket);
            isSuccess = true;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "调用zendesk更新ticket api失败", e);
        }

        return isSuccess;
    }

    public User getCurrentUser(String tenantId) {
        User user = null;
        try {
            Zendesk zendesk = zendeskConfig.queryZendesk(tenantId);

            user = zendesk.getCurrentUser();
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "调用zendesk获取currentUser api失败", e);
        }
        return user;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        if (copyWrite != null) {
            this.welcomeHtmlBodyMap = this.copyWrite.getConfig().get("zendeskWelcomeHtmlBody");
            this.welcomeSubjectMap = this.copyWrite.getConfig().get("zendeskWelcomeSubject");
            this.noBindCameraAfter48hHtmlBodyMap = this.copyWrite.getConfig().get("noBindCameraAfter48hHtmlBody");
            this.noBindCameraAfter48hSubjectMap = this.copyWrite.getConfig().get("noBindCameraAfter48hSubject");
            this.newNoBindCameraAfter48hHtmlBodyMap = this.copyWrite.getConfig().get("new_noBindCameraAfter48hHtmlBody");
            this.newNoBindCameraAfter48hSubjectMap = this.copyWrite.getConfig().get("new_noBindCameraAfter48hSubject");
        }

        if (MapUtils.isEmpty(welcomeHtmlBodyMap)) {
            this.welcomeHtmlBodyMap = new HashMap<>();
        }

        if (MapUtils.isEmpty(welcomeSubjectMap)) {
            this.welcomeSubjectMap = new HashMap<>();
        }

        if (MapUtils.isEmpty(noBindCameraAfter48hHtmlBodyMap)) {
            this.noBindCameraAfter48hHtmlBodyMap = new HashMap<>();
        }

        if (MapUtils.isEmpty(noBindCameraAfter48hSubjectMap)) {
            this.noBindCameraAfter48hSubjectMap = new HashMap<>();
        }

        if (MapUtils.isEmpty(newNoBindCameraAfter48hHtmlBodyMap)) {
            this.newNoBindCameraAfter48hHtmlBodyMap = new HashMap<>();
        }

        if (MapUtils.isEmpty(newNoBindCameraAfter48hSubjectMap)) {
            this.newNoBindCameraAfter48hSubjectMap = new HashMap<>();
        }

        Yaml zendeskNewUserBindCameraEmailYaml = new Yaml();
        Map zendeskNewUserBindCameraEmailMap = zendeskNewUserBindCameraEmailYaml.load(ZendeskClient.class.getClassLoader().getResourceAsStream("app/zendesk_new_user_bind_camera_email.yml"));
        this.noBindCameraAfter24hHtmlBodyMap = (Map<String, String>) zendeskNewUserBindCameraEmailMap.get("noBindCameraAfter24hHtmlBodyMap");
        if (MapUtils.isEmpty(noBindCameraAfter24hHtmlBodyMap)) {
            this.noBindCameraAfter24hHtmlBodyMap = new HashMap<>();
        }

        this.noBindCameraAfter24hSubjectMap = (Map<String, String>) zendeskNewUserBindCameraEmailMap.get("noBindCameraAfter24hSubjectMap");
        if (MapUtils.isEmpty(noBindCameraAfter24hSubjectMap)) {
            this.noBindCameraAfter24hSubjectMap = new HashMap<>();
        }

        Yaml safemoBindFailFeedbackYaml = new Yaml();
        Map safemoBindFailFeedbackMap = safemoBindFailFeedbackYaml.load(ZendeskClient.class.getClassLoader().getResourceAsStream("app/safemo_bind_fail_feedback_email.yml"));
        this.safemoBindFailFeedbackSubjectMap = (Map<String, String>) safemoBindFailFeedbackMap.get("safemoBindFailFeedbackSubjectMap");
        if (MapUtils.isEmpty(safemoBindFailFeedbackSubjectMap)) {
            this.safemoBindFailFeedbackSubjectMap = new HashMap<>();
        }

        this.safemoBindFailFeedbackHtmlBodyMap = (Map<String, String>) safemoBindFailFeedbackMap.get("safemoBindFailFeedbackHtmlBodyMap");
        if (MapUtils.isEmpty(safemoBindFailFeedbackHtmlBodyMap)) {
            this.safemoBindFailFeedbackHtmlBodyMap = new HashMap<>();
        }
    }

    /**
     * 因zendesk自身api不稳定，为尽量不影响业务，zendesk操作都应该使用这个异步方法
     * 具体业务逻辑处理见方法 zendeskKafkaMsgListener
     *
     * @param actionType
     * @param zendeskActionParamMap
     */
    public void actionAsync(ZendeskActionEnums actionType, Map zendeskActionParamMap) {
        try {
            zendeskActionParamMap.put("actionFrom", actionType);
            mqSender.send(zendeskTopic, String.valueOf(System.currentTimeMillis()), zendeskActionParamMap);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "send handle async zendeskHandleObj failed", e);
        }
    }

    /**
     * zendesk操作异步处理方法 根据actionFrom判断是新建用户还是创建工单、添加工单tag
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.topics.zendesk}", groupId = "iot-service-zendesk")
    public void zendeskKafkaMsgListener(ConsumerRecord<String, String> record) {
        MDC.clear();
        String requestId = IDUtil.uuid();
        MDC.put(MDCKeys.REQUEST_ID, requestId);
        log.info("receive handle async zendeskHandleObj key {}", record.key());

        if (StringUtils.isEmpty(record.value())) {
            log.debug("zendeskKafkaMsgListener message error");
            return;
        }

        JSONObject object = JSON.parseObject(record.value());
        if (object == null || !object.containsKey("actionFrom")) {
            log.debug("zendeskKafkaMsgListener object null");
            return;
        }

        String tenantId = object.getString("tenantId");
        if (!StringUtils.isEmpty(tenantId) && notSupportedTenantIdSet.contains(tenantId.toLowerCase())) {
            com.addx.iotcamera.util.LogUtil.warn(log, "not support tenantId:{}", tenantId);
            return;
        }

        switch (ZendeskActionEnums.valueOf(object.getString("actionFrom"))) {
            case USER_CREATE:
                // 保存进redis zset，好做后续的新用户回访邮件
                Integer userId = object.getInteger("userId");
                String zendeskNewUserKey = "zendesk_new_user";
                long currentTimeMillis = Instant.now().toEpochMilli();
                redisService.zadd("zendesk_new_user", String.join("#", userId.toString(), object.getString("appName")), currentTimeMillis);
                Long zendeskNewUserCount = redisService.zCard(zendeskNewUserKey);
                if (zendeskNewUserCount != null && zendeskNewUserCount > 100000) {
                    redisService.zremoveByScore("zendesk_new_user", 0, currentTimeMillis - 7 * 24 * 3600 * 1000);
                }

                Map<String, String> identitiesMap = new HashMap<>();
                identitiesMap.put("userId", userId.toString());
                if (object.containsKey("language")) {
                    identitiesMap.put("language", object.getString("language"));
                }
                if (object.containsKey("countryNo")) {
                    identitiesMap.put("countryNo", object.getString("countryNo"));
                }

                List<String> tagList = new LinkedList<>();
                tagList.add(String.format("welcome_group_%d", Double.valueOf(Math.random() * 20).intValue() + 1));
                if(object.containsKey("tenantId")) {
                    tagList.add(createTenantTag(object.getString("tenantId")));
                }

                if(object.containsKey("countryNo")) {
                    tagList.add(String.join ("_", "country", object.getString("countryNo")));
                }

                String[] activeProfileStrArray = activeProfiles.split("-");
                tagList.add(String.join ("_", "dc", activeProfileStrArray[activeProfileStrArray.length - 1]));

                long zdUserId = createUser(object.getString("name"), object.getString("email"), identitiesMap, tagList,tenantId);
                log.info("create user success zdUserId:{}", zdUserId);
                break;
            case WELCOME_TICKET_CREATE:
                userId = object.getInteger("userId");

                String requesterEmail = object.getString("email");
                String requesterName = object.getString("name");
                if (StringUtils.isEmpty(requesterName)) {
                    requesterName = requesterEmail;
                }

                EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);
                String recipient = emailAccount.getAccount();

                String appName = tenantTierConfig.queryAppNameByTenant(tenantId);

                String language = "en";
                if (object.containsKey("language")) {
                    language = object.getString("language");
                }

                // 获取环境邮件title
                String subject = this.queryWelcomeEmailTitle(tenantId,language,appName);


                // welcome body
                String welcomeHtmlBody = this.queryAppWelComEmailBody(tenantId,language,requesterName,recipient,appName);

                // send email


                EmailUtils.sendEmail(Collections.singletonList(requesterEmail), subject, welcomeHtmlBody, emailAccount);
                log.info("send new user welcome email success. userId {} appName {}", userId, appName);
                break;
            default:
                break;
        }
    }

    @NotNull
    private static String createTenantTag(String  tenantId) {
        return String.join("_", "app", tenantId);
    }

    Long getWelcomeTicketId(Integer userId) {
        ZendeskTicketDO zendeskWelcomeTicketDO = zendeskTicketDao.queryByUserIdAndTicketType(userId, "WELCOME_TICKET");
        return zendeskWelcomeTicketDO != null ? zendeskWelcomeTicketDO.getZdTicketId() : null;
    }

    /**
     * send safemo bind fail feedback email
     */
    public void executeSafemoBindFailFeedbackEmail() throws InterruptedException {
        List<Integer> userIdList = dbtSafemoBindFailDAO.querySafemoBindFailUserIdList();

        for (Integer userId : userIdList) {

            com.addx.iotcamera.bean.domain.User user = userService.queryUserById(userId);
            if (user == null) {
                log.info("can not find user with userId {}", userId);
                continue;
            }

            if (!StringUtils.hasLength(user.getEmail())) {
                log.info("no email for userId {}", userId);
                continue;
            }

            if (!TENANTID_SAFEMO.equals(user.getTenantId())) {
                log.info("user not in safemo userId {}", userId);
                continue;
            }
            UserBindFailFeedbackEmailDO userBindFailFeedBackEmailDO = userBindFailFeedBackEmailDAO.queryUserBindFailFeedBackEmailByUserId(userId);
            if (userBindFailFeedBackEmailDO != null && Integer.valueOf(1).equals(userBindFailFeedBackEmailDO.getSendStatus())) {
                log.info("user already send feedback email userId {}", userId);
                continue;
            }

            EmailAccount emailAccount = appAccountConfig.queryEmailAccount(user.getTenantId());

            String language = user.getLanguage();

            String subject = safemoBindFailFeedbackSubjectMap.get(language);
            if (StringUtils.isEmpty(subject)) {
                subject = safemoBindFailFeedbackSubjectMap.get("en");
            }

            String htmlBody = safemoBindFailFeedbackHtmlBodyMap.get(language);
            if (StringUtils.isEmpty(htmlBody)) {
                htmlBody = safemoBindFailFeedbackHtmlBodyMap.get("en");
            }
            htmlBody = htmlBody.replaceAll("\n", "<br/>");
            htmlBody = "<html><body>$BODY</body></html>".replace("$BODY", htmlBody);

            EmailUtils.sendEmail(Collections.singletonList(ZendeskUtil.generateZendeskEmail(user)), subject, htmlBody, emailAccount);

            userBindFailFeedBackEmailDAO.addUserBindFailFeedBackEmail(userId);
            log.info("send bind fail feedback email. userId {} ", userId);
        }
    }

    /**
     * send zendesk new user feedback email
     */
    public void executeZendeskNewUserFeedbackEmail() {
        String zendeskNewUserKey = "zendesk_new_user";
        long currentTimeMillis = Instant.now().toEpochMilli();

        int page = 0;
        int pageSize = 100;
        while (page++ < 1000) {
            Set<String> userIdWithAppNames = redisService.zrangeByScore(zendeskNewUserKey, 0d, currentTimeMillis - 2 * 24 * 3600 * 1000, (page - 1) * pageSize, pageSize);
            if (CollectionUtils.isEmpty(userIdWithAppNames)) {
                break;
            }

            for (String userIdWithAppName : userIdWithAppNames) {
                try {
                    String userId = userIdWithAppName.substring(0, userIdWithAppName.indexOf("#"));
                    String appName = userIdWithAppName.substring(userId.length() + 1);

                    sendNewUserNotBindCameraAfter48hEmail(Integer.valueOf(userId), appName);

                    // 直接删除已发送feedback的用户
                    redisService.zremove(zendeskNewUserKey, userIdWithAppName);
                    // 防止请求太快,API请求峰值过高
                    Thread.sleep(1000);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "send new user not bind camera after 48h failed. userIdWithAppName {}", userIdWithAppName, e);
                }
            }

            if (userIdWithAppNames.size() < pageSize) {
                break;
            }
        }
    }

    /**
     * 发送超过48小时未绑定camera邮件
     *
     * @param userId
     * @param appName
     */
    public void sendNewUserNotBindCameraAfter48hEmail(Integer userId, String appName) {
        com.addx.iotcamera.bean.domain.User user = userService.queryUserById(userId);
        if (user == null) {
            com.addx.iotcamera.util.LogUtil.warn(log, "can not find user with userId {}", userId);
            return;
        }

        if(noSendEmailConfig.getZendeskEmail().contains(user.getTenantId())){
            //不需要发送zendesk邮件的app
            return;
        }

        // 判断当前用户是否绑定成功过，若是则跳过
        List<BindOperationTb> bindOperationTbList = deviceDAO.queryBindHistory(userId);
        boolean hasBindCamera = CollectionUtils.isEmpty(bindOperationTbList) ? false :
                bindOperationTbList.stream().anyMatch(bindOperationTb -> bindOperationTb.getAnswered() != null && bindOperationTb.getAnswered().intValue() == 1);
        if (hasBindCamera) {
            log.info("user has bind camera userId {}", userId);
            return;
        }

        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(user.getTenantId());

        String language = user.getLanguage();
        Map<String, String> emailGuideConfigGuide = emailGuideConfig.getGuideByTenantId(user.getTenantId());
        String redirectedUrl = emailGuideConfigGuide.get(language);
        log.info("redirected url {}", redirectedUrl);
        String subject = "";
        String htmlBody = "";
        if (newEmailConfig.getZendeskEmail().contains(user.getTenantId())) {
            subject = getSubject(newNoBindCameraAfter48hSubjectMap, language, appName);
            htmlBody = getHtmlBody(newNoBindCameraAfter48hHtmlBodyMap, language, appName, user.getName(), emailAccount.getAccount(), redirectedUrl, true);
        }else if (user.getTenantId().equals(TENANTID_KIWIBIT)){
            subject = this.copyWrite.queryValue(APP_48_NO_BIND_TITLE.replace("{tenantId}",Matcher.quoteReplacement(user.getTenantId())),language)
                    .replace(PLACEHOLDER_APP_NAME,Matcher.quoteReplacement(appName));
            htmlBody = getHtmlBody24NoBindDevice(language, appName, emailAccount.getAccount(),user.getTenantId() );
        }else {
            subject = getSubject(noBindCameraAfter48hSubjectMap, language, appName);
            htmlBody = getHtmlBody(noBindCameraAfter48hHtmlBodyMap, language, appName, user.getName(), emailAccount.getAccount(), null, false);
        }

        log.info("subject is: {}, htmlBody is: {}", subject, htmlBody);
        EmailUtils.sendEmail(Collections.singletonList(ZendeskUtil.generateZendeskEmail(user)), subject, htmlBody, emailAccount);

        log.info("send new user not bind camera after 48h success. userId {} appName {}", userId, appName);
    }

    private String getSubject(Map<String, String> subjectMap, String language, String appName) {
        String subject = subjectMap.get(language);
        if (org.apache.commons.lang3.StringUtils.isEmpty(subject)) {
            subject = subjectMap.getOrDefault(APP_LANGUAGE_EN, "");
        }
        return subject.replace(PLACEHOLDER_APP_NAME, Matcher.quoteReplacement(appName));
    }

    private String getHtmlBody(Map<String, String> htmlBodyMap, String language, String appName, String userName, String supportEmail, String redirectedUrl, boolean isRedirected) {
        String htmlBody = htmlBodyMap.get(language);
        if (org.apache.commons.lang3.StringUtils.isEmpty(htmlBody)) {
            htmlBody = htmlBodyMap.getOrDefault(APP_LANGUAGE_EN, "");
        }
        htmlBody = htmlBody.replace(PLACEHOLDER_USER_NAME, Matcher.quoteReplacement(userName))
                .replace(PLACEHOLDER_APP_NAME, Matcher.quoteReplacement(appName))
                .replace(PLACEHOLDER_SUPPORT_EMAIL, Matcher.quoteReplacement(supportEmail));

        if (!org.apache.commons.lang3.StringUtils.isEmpty(redirectedUrl)) {
            htmlBody = htmlBody.replace("<a href=\"\">", "<a href=\"" + redirectedUrl + "\">");
        }

        // 如果是老邮件，则将换行符替换为 <br/>
        if (!isRedirected) {
            htmlBody = htmlBody.replace("\n", LINE_BREAK);
        }

        return HTML_TEMPLATE.replace("$BODY", htmlBody);
    }

    /**
     * 拼接邮件内容
     * @param language
     * @param appName
     * @param supportEmail
     * @param tenantId
     * @return
     */
    private String getHtmlBody24NoBindDevice(String language, String appName, String supportEmail, String tenantId) {

        AppWelcomeEmailConfig.AppWelcomeEmail appWelcomeEmail = appWelcomeEmailConfig.queryAppWelcomeEmail(tenantId);
        if(appWelcomeEmail == null){
            //无特殊配置，无需拼接
            log.info("appWelcomeEmailConfig not contain tenantId {}",tenantId);
            return "";
        }
        String bodyKey = APP_48_NO_BIND_BODY.replace("{tenantId}",tenantId);
        String htmlBody = copyWrite.queryValue(bodyKey,language);
        log.debug("getHtmlBody24NoBindDevice bodyKey {} htmlBody {} language {}",bodyKey,htmlBody,language);
        htmlBody = htmlBody.replace(PLACEHOLDER_TOP_IMAGE_URL, appWelcomeEmail.getTopImageUrl())
                .replace(PLACEHOLDER_APP_NAME, Matcher.quoteReplacement(appName))
                .replace(PLACEHOLDER_SUPPORT_EMAIL, Matcher.quoteReplacement(supportEmail))
                .replace(PLACEHOLDER_SUPPORT_PHONE, appWelcomeEmail.getPhoneSupport())
                .replace(PLACEHOLDER_GUIDE_LINK1, appWelcomeEmail.getGuideLink1())
                .replace(PLACEHOLDER_GUIDE_LINK2, appWelcomeEmail.getGuideLink2())
                .replace(PLACEHOLDER_TEXT_ALIGN,PLACEHOLDER_TEXT_ALIGN_LANGUAGE.contains(language) ? PLACEHOLDER_TEXT_ALIGN_RIGHT : PLACEHOLDER_TEXT_ALIGN_LEFT)
        ;
        return APP_WELCOME_SPECIAL_HTML
                .replace("$BODY", htmlBody)
                .replace(PLACEHOLDER_TEXT_DIRECTION,PLACEHOLDER_TEXT_ALIGN_LANGUAGE.contains(language) ? PLACEHOLDER_TEXT_DIRECTION_RIGHT : PLACEHOLDER_TEXT_DIRECTION_LEFT)
                .replace(PLACEHOLDER_TOP_IMAGE_URL,appWelcomeEmail.getTopImageUrl());
    }

    /**
     * send zendesk new user feedback email
     */
    public void executeAppReportHomeAddCameraFeedbackEmail() {
        String zendeskNewUserKey = "zendesk_new_user";
        String appReportHomeAddCameraClickKey = "app_report_home_addCamera_click";
        long currentTimeMillis = Instant.now().toEpochMilli();

        int page = 0;
        int pageSize = 100;
        while (page++ < 1000) {
            Set<String> userIdWithAppNames = redisService.zrangeByScore(appReportHomeAddCameraClickKey, 0d, currentTimeMillis - 24 * 3600 * 1000, (page - 1) * pageSize, pageSize);
            if (CollectionUtils.isEmpty(userIdWithAppNames)) {
                break;
            }

            for (String userIdWithAppName : userIdWithAppNames) {
                try {
                    // 通过zendesk_new_user cache判断是否新用户
                    boolean isZendeskNewUser = (redisService.zScore(zendeskNewUserKey, userIdWithAppName) != null);
                    if (!isZendeskNewUser) {
                        // 直接删除已发送feedback的用户
                        redisService.zremove(appReportHomeAddCameraClickKey, userIdWithAppName);
                        continue;
                    }

                    String userId = userIdWithAppName.substring(0, userIdWithAppName.indexOf("#"));
                    String appName = userIdWithAppName.substring(userId.length() + 1);

                    sendNewUserNotBindCameraAfter24hEmail(Integer.valueOf(userId), appName);

                    // 直接删除已发送feedback的用户
                    redisService.zremove(appReportHomeAddCameraClickKey, userIdWithAppName);

                    // 防止请求太快,API请求峰值过高
                    Thread.sleep(1000);
                } catch (Exception e) {
                    com.addx.iotcamera.util.LogUtil.error(log, "send new user not bind camera after 24h failed. userIdWithAppName {}", userIdWithAppName, e);
                }
            }

            if (userIdWithAppNames.size() < pageSize) {
                break;
            }
        }
    }

    /**
     * 发送超过24小时未绑定camera邮件
     *
     * @param userId
     * @param appName
     */
    public void sendNewUserNotBindCameraAfter24hEmail(Integer userId, String appName) {
        com.addx.iotcamera.bean.domain.User user = userService.queryUserById(userId);
        if (user == null) {
            com.addx.iotcamera.util.LogUtil.warn(log, "can not find user with userId {}", userId);
            return;
        }

        if (StringUtils.isEmpty(user.getTenantId()) || !supportedTenantIdSet.contains(user.getTenantId().toLowerCase())) {
            com.addx.iotcamera.util.LogUtil.warn(log, "supportedTenantIdSet not contains {}", user.getTenantId());
            return;
        }

        if(noSendEmailConfig.getZendeskEmail().contains(user.getTenantId())){
            //不需要发送zendesk邮件的app
            return;
        }

        // 判断当前用户是否绑定成功过，若是则跳过
        List<BindOperationTb> bindOperationTbList = deviceDAO.queryBindHistory(userId);
        boolean hasBindCamera = CollectionUtils.isEmpty(bindOperationTbList) ? false :
                bindOperationTbList.stream().anyMatch(bindOperationTb -> bindOperationTb.getAnswered() != null && bindOperationTb.getAnswered().intValue() == 1);
        if (hasBindCamera) {
            log.info("user {} has bind camera", userId);
            return;
        }

        // 判断当前用户尝试绑定的相机是否被其他用户绑定成功过，若是则跳过
        boolean snHasBindSuccess = false;
        String try2BindSn = redisService.get(String.join("_", "addCamera_scanCamCode_show_serialNumber", String.valueOf(userId)));
        if(!StringUtils.isEmpty(try2BindSn)) {
            List<BindOperationTb> snBindOperationTbList = deviceDAO.queryBindHistoryBySn(try2BindSn);
            snHasBindSuccess = CollectionUtils.isEmpty(snBindOperationTbList) ? false :
                    snBindOperationTbList.stream().anyMatch(bindOperationTb -> bindOperationTb.getAnswered() != null && bindOperationTb.getAnswered().intValue() == 1);
        }
        if (snHasBindSuccess) {
            log.info("user {} try to bind camera was bind success", userId);
            return;
        }

        // 判断当前用户是否已提交其他zendesk反馈工单
        AtomicBoolean hasOtherTicketAtom = new AtomicBoolean(false);

        // 使用zendesk 的配置，如有配置则使用配置的，未配置则使用vicoo的配置
        Zendesk zendesk = zendeskConfig.queryZendesk(user.getTenantId());

        Iterable<User> usersIterable = zendesk.lookupUserByEmail(ZendeskUtil.generateZendeskEmail(user));
        Iterator<User> usersIt = usersIterable != null ? usersIterable.iterator() : null;
        if (usersIt != null && usersIt.hasNext()) {
            User zendeskUser = usersIt.next();
            Iterable<Request> requestIterable = zendesk.getUserRequests(zendeskUser);
            Iterator<Request> requestIt = requestIterable != null ? requestIterable.iterator() : null;
            if (requestIt != null) {
                requestIt.forEachRemaining(request -> hasOtherTicketAtom.compareAndSet(false, request.getVia() != null && !"api".equalsIgnoreCase(request.getVia().getChannel())));
            }
        }
        if (hasOtherTicketAtom.get()) {
            log.info("user {} has requested other ticket", userId);
            return;
        }

        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(user.getTenantId());

        String language = user.getLanguage();
        String subject = noBindCameraAfter24hSubjectMap.get(language);
        if (StringUtils.isEmpty(subject)) {
            subject = noBindCameraAfter24hSubjectMap.get("en");
        }
        subject = subject.replaceAll("\\$APP_NAME", Matcher.quoteReplacement(appName));

        String htmlBody = null;
        if ("vicoo".equalsIgnoreCase(user.getTenantId()) && "en".equalsIgnoreCase(language)) {
            htmlBody = noBindCameraAfter24hHtmlBodyMap.get("en2");
        } else {
            htmlBody = noBindCameraAfter24hHtmlBodyMap.get(language);
        }
        if (StringUtils.isEmpty(htmlBody)) {
            htmlBody = noBindCameraAfter24hHtmlBodyMap.get("en");
        }
        htmlBody = htmlBody.replaceAll("\\$USER_NAME", Matcher.quoteReplacement(user.getName())).replaceAll("\\$APP_NAME", Matcher.quoteReplacement(appName)).replaceAll("\\$SUPPORT_EMAIL", Matcher.quoteReplacement(emailAccount.getAccount()));
        htmlBody = htmlBody.replaceAll("\n", "<br/>");
        htmlBody = "<html><body>$BODY</body></html>".replace("$BODY", htmlBody);

        EmailUtils.sendEmail(Collections.singletonList(ZendeskUtil.generateZendeskEmail(user)), subject, htmlBody, emailAccount);

        log.info("send new user not bind camera after 24h success. userId {} appName {}", userId, appName);
    }

   public void updateUserTags(com.addx.iotcamera.bean.domain.User user, List<String> tags) {
        if(!CollectionUtils.isEmpty(tags)) {
            Zendesk zendesk = zendeskConfig.queryZendesk(user.getTenantId());
            Iterable<User> usersIterable = zendesk.lookupUserByEmail(ZendeskUtil.generateZendeskEmail(user));
            Iterator<User> usersIt = usersIterable != null ? usersIterable.iterator() : null;
            
            if (usersIt != null && usersIt.hasNext()) {
                User zendeskUser = usersIt.next();
                List<String> existingTags = zendeskUser.getTags();
                
                // 检查现有tags中是否已有app_开头的tag
                boolean hasAppTag = CollectionUtils.isEmpty(existingTags) ? false :
                    existingTags.stream().anyMatch(tag -> tag.startsWith(APP_TAG_PREFIX));
                    
                // 过滤新tags
                List<String> newTags = tags.stream()
                    .filter(tag -> {
                        if (tag.startsWith(APP_TAG_PREFIX)) {
                            // 如果是app_开头的tag，只有在没有现有app_tag时才添加
                            return !hasAppTag;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());
                
                // 合并现有tags和新tags
                Set<String> finalTags = new HashSet<>();
                if (!CollectionUtils.isEmpty(existingTags)) {
                    finalTags.addAll(existingTags);
                }
                finalTags.addAll(newTags);
                
                zendeskUser.setTags(new ArrayList<>(finalTags));
                updateUser(zendeskUser, user.getTenantId());
            }
        }
    }

    long createZendeskWelcomeTicket(com.addx.iotcamera.bean.domain.User user, String recipient, String subject, String commentHtmlBody, List<String> tags) {
        String requesterEmail = user.getEmail();
        String requesterName = user.getName();
        if (StringUtils.isEmpty(requesterName)) {
            requesterName = requesterEmail;
        }

        if (StringUtils.isEmpty(recipient)) {
            //全局默认工单发送人
            recipient = VICOO_SEND_EMAIL;
        }

        Comment comment = new Comment();

        User zdUser = getCurrentUser(user.getTenantId());
        comment.setAuthorId(zdUser != null ? zdUser.getId() : null);
        comment.setHtmlBody(commentHtmlBody);
        comment.setPublic(true);

        Long zdTicketId = createTicket(null, recipient, tags, subject, comment, true,user.getTenantId());
        Ticket welcomeTicket = getTicket(zdTicketId,user.getTenantId());
        welcomeTicket.setRequester(new Ticket.Requester(requesterName, requesterEmail));
        updateTicket(welcomeTicket,user.getTenantId());
        log.info("update ticket requester success zdTicketId:{}", zdTicketId);

        ZendeskTicketDO zendeskTicketDO = ZendeskTicketDO.builder()
                .zdTicketId(zdTicketId)
                .userId(user.getId().intValue())
                .ticketType("WELCOME_TICKET")
                .createTimestamp(System.currentTimeMillis())
                .zdTicketTags(new HashSet<>(tags))
                .build();
        zendeskTicketDao.insert(zendeskTicketDO);
        log.info("create ticket success zdTicketId:{}", zdTicketId);
        return zdTicketId;
    }

    /**
     * 获取文案
     * @param tenantId
     * @param language
     * @return
     */
    public String queryAppWelComEmailBody(String tenantId,String language,String requesterName,String recipient,String appName){
        String welcomeHtmlBody;
        log.debug("queryAppWelComEmailBody tenantId {} language {}",tenantId,language);
        AppWelcomeEmailConfig.AppWelcomeEmail appWelcomeEmail = appWelcomeEmailConfig.queryAppWelcomeEmail(tenantId);

        if(tenantId.equals(TENANTID_VICOO) && language.equals(APP_LANGUAGE_EN)){
            // vicoohome 且 英文的时候发特殊的文案
            welcomeHtmlBody = WELCOM_BODY_VICOO;
        }else if(APP_WELCOME_SPECIAL.contains(tenantId)){
            log.debug("queryAppWelComEmailBody tenantId {} appWelcomeEmail {}",tenantId,appWelcomeEmail);
            if(appWelcomeEmail == null){
                // 无特殊配置，
                welcomeHtmlBody = copyWrite.queryValue(WELCOME_BODY_SAFEMO,language);
            }else{
                welcomeHtmlBody = copyWrite.queryValue(APP_WELCOME_SPECIAL_BODY.replace("{tenantId}",tenantId),language);
                log.debug("queryAppWelComEmailBody welcomeHtmlBody {}",welcomeHtmlBody);
                welcomeHtmlBody = welcomeHtmlBody
                        .replace(PLACEHOLDER_SUPPORT_PHONE,appWelcomeEmail.getPhoneSupport())
                        .replace(PLACEHOLDER_HELP_CENTER,appWelcomeEmail.getHelpCenterLink())
                        .replace(PLACEHOLDER_YOUTWOBE_LINK,appWelcomeEmail.getYoutubeLink())

                        .replace(PLACEHOLDER_TEXT_ALIGN,PLACEHOLDER_TEXT_ALIGN_LANGUAGE.contains(language) ? PLACEHOLDER_TEXT_ALIGN_RIGHT : PLACEHOLDER_TEXT_ALIGN_LEFT)
                ;
            }


        }
        else{
            welcomeHtmlBody = copyWrite.queryValue(TENANT_DZEES.contains(tenantId) ? "zendeskWelcomeHtmlBodyDzees" :"zendeskWelcomeHtmlBody",language);
        }
        welcomeHtmlBody = welcomeHtmlBody
                .replaceAll("\\$USER_NAME", Matcher.quoteReplacement(requesterName))
                .replaceAll("\\$SUPPORT_EMAIL", Matcher.quoteReplacement(recipient))
                .replaceAll("\\$APP_NAME", appName);

        welcomeHtmlBody = this.queryWelcomeHtml(tenantId,welcomeHtmlBody,appWelcomeEmail,language);
        return welcomeHtmlBody;
    }

    /**
     * 获取环境邮件title
     * @param tenantId
     * @param language
     * @param appName
     * @return
     */
    private String queryWelcomeEmailTitle(String tenantId,String language,String appName){
        String subject = tenantId.equals(TENANTID_KIWIBIT) ? this.copyWrite.queryValue(APP_WELCOME_SPECIAL_TITLE.replace("{tenantId}",tenantId),language) :
                welcomeSubjectMap.containsKey(language) ? welcomeSubjectMap.get(language) : welcomeSubjectMap.get(APP_LANGUAGE_EN);

        return subject.replaceAll("\\$APP_NAME", appName);
    }

    private String queryWelcomeHtml(String tenantId,String welcomeHtmlBody, AppWelcomeEmailConfig.AppWelcomeEmail appWelcomeEmail,String language){
        // send email
        return tenantId.equals(TENANTID_KIWIBIT) ?
                APP_WELCOME_SPECIAL_HTML.replace("$BODY", welcomeHtmlBody)
                        .replace(PLACEHOLDER_TEXT_DIRECTION,PLACEHOLDER_TEXT_ALIGN_LANGUAGE.contains(language) ? PLACEHOLDER_TEXT_DIRECTION_RIGHT : PLACEHOLDER_TEXT_DIRECTION_LEFT)
                        .replace("$TOP_IMAGE_URL",appWelcomeEmail.getTopImageUrl()) :
                "<html><body>$BODY</body></html>".replace("$BODY", welcomeHtmlBody.replaceAll("\n", "<br/>"));
    }
}
