package com.addx.iotcamera.service.factory;

import com.addx.iotcamera.bean.app.device.DeviceBindInfoDO;
import com.addx.iotcamera.bean.warranty.WarrantyOrderDO;
import com.addx.iotcamera.bean.warranty.WarrantyOrderRequest;
import com.addx.iotcamera.dao.factory.DeviceBindInfoDao;
import com.addx.iotcamera.dao.factory.DeviceWarrantyOrderDAO;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@ConfigurationProperties(prefix = "device-bind-info")
public class DeviceBindInfoService {

    @Autowired
    private DeviceBindInfoDao deviceBindInfoDao;

    private static final int TWO_YEARS_IN_SECONDS = 2 * 365 * 24 * 60 * 60;
    @Autowired
    private DeviceWarrantyOrderDAO deviceWarrantyOrderDAO;

    private Set<String> SUPPORTED_MODELS_SET = DEFAULT_SUPPORTED_MODELS_SET;
    private Set<String> PRIORITY_MODELS_SET = DEFAULT_PRIORITY_MODELS_SET;

    public void setSupportModelSet(String str) {
        log.info("setSupportModelSet begin! value={},oldValue={}", str, JSON.toJSONString(this.SUPPORTED_MODELS_SET));
        Set<String> set = TextUtil.splitToNotBlankSet(str, ',');
        this.SUPPORTED_MODELS_SET = set.isEmpty() ? DEFAULT_SUPPORTED_MODELS_SET : set;
        log.info("setSupportModelSet end! value={}", JSON.toJSONString(this.SUPPORTED_MODELS_SET));
    }

    public void setPriorityModelsSet(String str) {
        log.info("setPriorityModelsSet begin! value={},oldValue={}", str, JSON.toJSONString(this.PRIORITY_MODELS_SET));
        Set<String> set = TextUtil.splitToNotBlankSet(str, ',');
        this.PRIORITY_MODELS_SET = set.isEmpty() ? DEFAULT_PRIORITY_MODELS_SET : set;
        log.info("setPriorityModelsSet end! value={}", JSON.toJSONString(this.PRIORITY_MODELS_SET));
    }

    private static final Set<String> DEFAULT_SUPPORTED_MODELS_SET = new HashSet<>(Arrays.asList("SS1131W1", "SS0111W1", "CX124A", "CX124"
            , "CX124-A4X", "CX124A-A4X", "BX150", "BX150-A4X", "SS1311W1", "SS1211W1"));
    private static final Set<String> DEFAULT_PRIORITY_MODELS_SET = new HashSet<>(Arrays.asList("SK0111W1", "SK0121W1"));



    public List<DeviceBindInfoDO> queryAllDeviceBindInfoDO(List<String> serialNumberList) {
        Map<String, DeviceBindInfoDO> resultMap = new HashMap<>();

        for (String serialNumber : serialNumberList) {
            // 从device_manufacture表获取user_sn
            String userSn = deviceBindInfoDao.findUserSnBySerialNumber(serialNumber);
            if (userSn == null) {
                log.debug("No user_sn found for serial_number: {}", serialNumber);
                continue;
            }

            // 从device_component_bind_info表根据user_sn获取parent_uniq_code
            String parentUniqCode = deviceBindInfoDao.findParentUniqCodeByUniqCode(userSn);
            List<DeviceBindInfoDO> bindInfoList = deviceBindInfoDao.findDeviceBindInfoByParentUniqCode(parentUniqCode);

            // 在绑定关系表中无记录，继而检查是否存在于SUPPORTED_MODELS_SET
            if (parentUniqCode == null || bindInfoList == null || bindInfoList.isEmpty()) {
                log.debug("No parent_uniq_code found for uniq_code: {}", userSn);
                String modelNo = deviceBindInfoDao.findModelNoByUserSn(userSn);
                if (SUPPORTED_MODELS_SET.contains(modelNo)) {
                    // 是支持机型
                    String iconUrl = deviceBindInfoDao.findIconUrlByModelNo(modelNo);
                    String saleName = deviceBindInfoDao.findSaleNameByModelNo(modelNo);
                    DeviceBindInfoDO bindInfo = DeviceBindInfoDO.builder()
                            .uniqCode(userSn)
                            .modelNo(modelNo)
                            .icon(iconUrl)
                            .productName(saleName)
                            .isWarrantySupported(true)
                            .build();

                    // 检查warrantyRegistered状态
                    WarrantyOrderDO warrantyOrder = deviceWarrantyOrderDAO.getWarrantyOrder(userSn);
                    if (warrantyOrder != null) {
                        // 已注册过售后服务
                        bindInfo.setWarrantyRegistered(true);
                        bindInfo.setExpiredTimeStamp(warrantyOrder.getExpirationTime());
                    } else {
                        // 未注册过
                        bindInfo.setWarrantyRegistered(false);
                    }
                    // 将结果存入Map中
                    resultMap.put(userSn, bindInfo);
                    continue;
                } else {
                    log.debug("No parent_uniq_code found for uniq_code: {} and not in SUPPORTED_MODELS_SET", userSn);
                    continue;
                }
            }

            // 遍历bindInfoList，根据modelNo优先级筛选合适的bindInfo
            DeviceBindInfoDO bindInfo = DeviceBindInfoDO.builder()
                    .uniqCode(userSn)
                    .isWarrantySupported(false)
                    .build();
            log.debug("bindInfoList: {}", bindInfoList);

            for (DeviceBindInfoDO info : bindInfoList) {
                String modelNo = info.getModelNo();
                if (PRIORITY_MODELS_SET.contains(modelNo)) {
                    bindInfo.setModelNo(info.getModelNo());
                    bindInfo.setUniqCode(info.getUniqCode());
                    bindInfo.setIsWarrantySupported(true);
                    break;
                } else if (SUPPORTED_MODELS_SET.contains(modelNo)) {
                    bindInfo.setModelNo(info.getModelNo());
                    bindInfo.setUniqCode(info.getUniqCode());
                    bindInfo.setIsWarrantySupported(true);
                }
            }

            // 获取iconUrl和saleName
            String iconUrl = deviceBindInfoDao.findIconUrlByModelNo(bindInfo.getModelNo());
            bindInfo.setIcon(iconUrl != null ? iconUrl : "");
            String saleName = deviceBindInfoDao.findSaleNameByModelNo(bindInfo.getModelNo());
            bindInfo.setProductName(saleName != null ? saleName : "");

            // 检查warrantyRegistered状态
            WarrantyOrderDO warrantyOrder = deviceWarrantyOrderDAO.getWarrantyOrder(bindInfo.getUniqCode());
            if (warrantyOrder != null) {
                bindInfo.setWarrantyRegistered(true);
                bindInfo.setExpiredTimeStamp(warrantyOrder.getExpirationTime());
            } else {
                bindInfo.setWarrantyRegistered(false);
            }

            resultMap.put(bindInfo.getUniqCode(), bindInfo);
        }

        return new ArrayList<>(resultMap.values());
    }

    public void insertWarrantyOrders(List<WarrantyOrderRequest.Order> warrantyOrderList, Integer userId) {
        for (WarrantyOrderRequest.Order warrantyOrder : warrantyOrderList) {
            //支持多个售后order批量插入
            if (StringUtils.isEmpty(warrantyOrder.getOrderNo()) || StringUtils.isEmpty(warrantyOrder.getPurchasedAt())) {
                throw new IllegalArgumentException("OrderNo and PurchasedAt must not be null or empty. userId: " + userId);
            }

            for (WarrantyOrderRequest.Order.Product product : warrantyOrder.getProducts()) {
                //遍历单个order中的产品
                if (StringUtils.isEmpty(product.getUserSn()) || StringUtils.isEmpty(product.getModelNo())) {
                    throw new IllegalArgumentException("UserSn and ModelNo must not be null or empty. userId: " + userId);
                }

                WarrantyOrderDO existingOrder = deviceWarrantyOrderDAO.getWarrantyOrder(product.getUserSn());
                if (existingOrder != null) {
                    throw new IllegalArgumentException("Warranty order for this userSn already exists and has not expired. userSn" +
                            product.getUserSn());
                }
                int currentTime = (int) (System.currentTimeMillis() / 1000);
                int expirationTime = currentTime + TWO_YEARS_IN_SECONDS;

                WarrantyOrderDO warrantyOrderDO = WarrantyOrderDO.builder()
                        .userId(userId)
                        .userSn(product.getUserSn())
                        .modelNo(product.getModelNo())
                        .purchasedAt(warrantyOrder.getPurchasedAt())
                        .orderNo(warrantyOrder.getOrderNo())
                        .insertTime(currentTime)
                        .expirationTime(expirationTime)
                        .build();

                deviceWarrantyOrderDAO.insertWarrantyOrder(warrantyOrderDO);
            }
        }
    }

}
