package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.cache.DevicePirNotifyFactor;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.bean.domain.InsertLibraryRequest;
import com.addx.iotcamera.bean.domain.SegmentVideoRequest;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.ai.AiTaskConfigRequest;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.response.device.DevicePushImage;
import com.addx.iotcamera.config.AiTaskParamsConfig;
import com.addx.iotcamera.enums.ERecordingTrigger;
import com.addx.iotcamera.helper.DeviceDetectHelper;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.openapi.SaasAIService;
import com.addx.iotcamera.service.video.VideoAIService;
import com.addx.iotcamera.util.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.proto.*;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.core.IExtensionManager;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_WOWZA_COMPLETE_VIDEO;
import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.helper.TimeRecorder.videoSliceTimeRecorder;

@Component
@Slf4j
public class AIService {
    @Value("${spring.kafka.topics.ai-video-segment}")
    private String videoSegmentTopic;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Autowired
    @Lazy
    private ActivityZoneService activityZoneService;

    @Autowired
    private PersistentRedisService persistentRedisService;

    @Autowired
    private VideoEventRedisService videoEventRedisService;

    @Autowired
    private UserService userService;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private PushService pushService;

    @Resource
    private MqSender mqSender;

    @Resource
    private ReportLogService reportLogService;

    @Resource
    private DeviceDetectHelper deviceDetectHelper;

    @Autowired
    private AiTaskParamsConfig aiTaskParamsConfig;

    @Autowired
    private MessageNotificationSettingsService messageNotificationSettingsService;

    @Autowired
    private SaasAIService aiSaasService;
    @Autowired
    private DeviceSupportService deviceSupportService;
    @Autowired
    private VideoAIService videoAIService;

    @Autowired
    private IExtensionManager extensionManager;

    public void produceNewVideoTask(InsertLibraryRequest request) {
        // TODO 这个字段是唬人的???
        if (request.getSilent() > 0) {
            return;
        }

        String serialNumber = request.getSerialNumber();
        DevicePirNotifyFactor factor = pushService.getDevicePirNotifyFactor(serialNumber);
        if (factor == null) return;

        reportLogService.sysReportWakeup(REPORT_TYPE_WOWZA_COMPLETE_VIDEO,
                MapUtil.builder()
                        .put("serialNumber", serialNumber)
                        .put("period", request.getPeriod())
                        .put("traceId", request.getTraceId())
                        .put("needAI", factor.isVip())
                        .put("libraryId", request.getId())
                        .build());

        if (factor.isVip()) {
            s3Service.presignInsertLibraryRequest(request);
            String modelNo = deviceManualService.getModelNoBySerialNumber(request.getSerialNumber());
            int adminId = factor.getUserRole().getAdminId();

            AITask task = AITask.builder()
                    .serialNumber(request.getSerialNumber())
                    .userId(adminId + "")
                    .taskId(PhosUtils.randomNumString(8)) // TODO 这里的id太随意了
                    .taskSendTime(PhosUtils.getUTCStamp())
                    .videoUrl(request.getVideoUrl())
                    .traceId(request.getTraceId())
                    .modelNo(modelNo)
                    .events(factor.getAiDetectNotifyEvents())
                    .segment(0)
                    .order(-1)
                    .activityZoneList(activityZoneService.queryActivityZone(serialNumber))
                    .identificationBox(getIdentificationBoxBySn(adminId))
                    .sliceTotalNum(request.getSliceTotalNum())
                    .build();
//            mqSender.send(videoSegmentTopic, request.getTraceId(), task);
            aiSaasService.sendSaasAiTask(task, true);
        }
    }

    /**
     * 切片任务
     *
     * @param request
     */
    public void processSegmentVideoTask(SegmentVideoRequest request) {
        String serialNumber = request.getSerialNumber();
        String traceId = request.getTraceId();

        videoSliceTimeRecorder.recordBegin("getDevicePirNotifyFactor");
        DevicePirNotifyFactor factor = pushService.getDevicePirNotifyFactor(serialNumber);
        videoSliceTimeRecorder.recordEnd("getDevicePirNotifyFactor");
        if (factor == null) return;

        videoSliceTimeRecorder.recordBegin("getModelNoBySerialNumber");
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        videoSliceTimeRecorder.recordEnd("getModelNoBySerialNumber");
        String s3ImageURL = s3Service.preSignUrl(request.getImageUrl());
        // 记录设备视频最新截图
        videoSliceTimeRecorder.recordBegin("saveLibraryVideoImage");
        this.saveLibraryVideoImage(factor.getUserRole().getAdminId(), request.getSerialNumber(), request.getImageUrl(), VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode());
        videoSliceTimeRecorder.recordEnd("saveLibraryVideoImage");

        ERecordingTrigger recordVideoTrigger = ERecordingTrigger.findByTraceId(traceId);

        MessageNotificationSetting notificationSetting = messageNotificationSettingsService.queryMessageNotificationSetting(serialNumber, factor.getUserRole().getAdminId());
        boolean enableOther = Optional.ofNullable(notificationSetting).filter(it -> it.getEnableOther() != null).map(it -> it.getEnableOther() == 1).orElse(true) || !factor.isVip();

        // 如果PIR触发的录像
        if (enableOther && recordVideoTrigger == ERecordingTrigger.PIR) {
            // 判断是否在一个videoEvent 中
            this.getVideoEventKey(serialNumber, traceId);

            // 如果只推送普通运动， 那么发通知
            if (factor.decidePirNotifyType() == DevicePirNotifyFactor.PirNotifyType.PIR_SEGMENT) {
                log.debug("准备推送PIR检测到运动 {} {}", serialNumber, traceId);
                videoSliceTimeRecorder.recordBegin("pirNotify");
                pirNotify(serialNumber, traceId, s3ImageURL, "");
                videoSliceTimeRecorder.recordEnd("pirNotify");
            }

            // 设备端检测的人形
            if (factor.decidePirNotifyType() == DevicePirNotifyFactor.PirNotifyType.DEVICE_POST) {
                // 检测到人形
                videoSliceTimeRecorder.recordBegin("process_person");
                deviceDetectHelper.process(
                        serialNumber,
                        traceId,
                        AiObjectEnum.PERSON.getName(),
                        (eventObject, eventType) -> {
                            log.info("准备推送设备检测到的人形 {} {}", serialNumber, traceId);
                            videoSliceTimeRecorder.recordBegin("notify_person");
                            pirNotify(serialNumber, traceId, s3ImageURL, AiObjectEnum.PERSON.getName());
                            videoSliceTimeRecorder.recordEnd("notify_person");
                        });
                videoSliceTimeRecorder.recordEnd("process_person");

                // 检测到普通运动
                videoSliceTimeRecorder.recordBegin("process_motion");
                deviceDetectHelper.process(
                        serialNumber,
                        traceId,
                        AiActionEnum.MOTION.getName(),
                        (eventObject, eventType) -> {
                            log.info("准备推送设备检测到的运动 {} {}", serialNumber, traceId);
                            videoSliceTimeRecorder.recordBegin("notify_motion");
                            pirNotify(serialNumber, traceId, s3ImageURL, AiActionEnum.MOTION.getName());
                            videoSliceTimeRecorder.recordEnd("notify_motion");
                        });
                videoSliceTimeRecorder.recordEnd("process_motion");
            }
        }

        // 检测哭声
        if (enableOther && factor.decideCryNotifyType() == DevicePirNotifyFactor.CryNotifyType.DEVICE_POST) {

            // 发现哭声
            videoSliceTimeRecorder.recordBegin("process_cry");
            deviceDetectHelper.process(
                    serialNumber,
                    traceId,
                    AiActionEnum.CRY.getName(),
                    (eventObject, eventType) -> {
                        log.info("准备推送哭声消息 {} {}", serialNumber, traceId);
                        videoSliceTimeRecorder.recordBegin("notify_cry");
                        cryNotify(serialNumber, traceId, s3ImageURL);
                        videoSliceTimeRecorder.recordEnd("notify_cry");
                    });
            videoSliceTimeRecorder.recordEnd("process_cry");
        }


        if (factor.isVip()) {
            videoSliceTimeRecorder.recordBegin("pushAiTask");
            int adminId = factor.getUserRole().getAdminId();
            AITask task = AITask.builder()
                    .serialNumber(serialNumber)
                    .userId(adminId + "")
                    .taskId(PhosUtils.randomNumString(8)) // TODO 这里的id太随意了
                    .taskSendTime(PhosUtils.getUTCStamp())
                    .videoUrl(s3Service.preSignUrl(request.getVideoUrl()))
                    .traceId(traceId)
                    .modelNo(modelNo)
                    .events(factor.getAiDetectNotifyEvents())
                    .segment(1)
                    .order(request.getOrder())
                    .activityZoneList(activityZoneService.queryActivityZone(serialNumber))
                    .identificationBox(getIdentificationBoxBySn(adminId))
                    .build();

//            mqSender.send(videoSegmentTopic, request.getTraceId(), task);
            aiSaasService.sendSaasAiTask(task, request.getIsLast());

            AITask aiTaskLog = new AITask();
            BeanUtils.copyProperties(task, aiTaskLog);
            // 为了精简日志
            aiTaskLog.setVideoUrl(null);
            log.info("推送切片任务给AI task:{}", JSON.toJSONString(aiTaskLog));
            videoSliceTimeRecorder.recordEnd("pushAiTask");
        }
    }

    public AITask.IdentificationBox getIdentificationBoxBySn(Integer adminId) {
        User user = userService.queryUserById(adminId);
        if (user != null) {
            return aiTaskParamsConfig.getIdentificationBoxByTenantId(user.getTenantId());
        }
        return null;
    }

    /**
     * 缓存设备最新视频截图
     *
     * @param serialNumber
     * @param imageUrl
     * @param videoType
     */
    public void saveLibraryVideoImage(Integer userId, String serialNumber, String imageUrl, Integer videoType) {
        if (StringUtils.isEmpty(imageUrl)) {
            log.info("saveLibraryVideoImage serialNumber 【{}】 imageUrl empty", serialNumber);
            return;
        }
        final String DEVICE_LIBRARY_VIDEO_IMAGE = VideoTypeEnum.codeOf(videoType).getLastImageKeyTemplate();
        String libraryVideoImage = DEVICE_LIBRARY_VIDEO_IMAGE.replace("{serialNumber}", serialNumber).replace("{userId}", String.valueOf(userId));
        DevicePushImage devicePushImage = DevicePushImage.builder()
                .lastPushImageUrl(imageUrl)
                .lastPushTime(Instant.now().getEpochSecond())
                .build();
        Gson gson = new Gson();
        persistentRedisService.set(libraryVideoImage, gson.toJson(devicePushImage));
        //log.info("saveLibraryVideoImage serialNumber {},key:{},devicePushImage:{}", serialNumber, libraryVideoImage, devicePushImage);
    }

    public void pirNotify(String serialNumber, String traceId, String imageUrl, String eventObject) {
        notificationService.PirNotify(serialNumber, traceId, imageUrl, eventObject);
    }

    /**
     * 获取算法需要的算法任务参数
     * @param request
     * @return
     */
    public SaasAITaskIM getSaasAiTaskConfig(AiTaskConfigRequest request) {
        DevicePirNotifyFactor factor = pushService.getDevicePirNotifyFactor(request.getSerialNumber());
        AITask task = AITask.builder()
                .serialNumber(request.getSerialNumber())
                .userId(request.getUserId())
                .taskId(PhosUtils.randomNumString(8))
                .taskSendTime(PhosUtils.getUTCStamp())
                .traceId(request.getTraceId())
                .segment(0)
                .order(-1)
                .activityZoneList(activityZoneService.queryActivityZone(request.getSerialNumber()))
                .identificationBox(getIdentificationBoxBySn(Integer.valueOf(request.getUserId())))
                .events(factor.getAiDetectNotifyEvents())
                .build();
        CloudDeviceSupport cloudDeviceSupport = deviceSupportService.queryDeviceSupportBySn(request.getSerialNumber());
        if(!Boolean.valueOf(true).equals(cloudDeviceSupport.getSupportSendAiImageDirect())){
            cloudDeviceSupport = new CloudDeviceSupport();
            cloudDeviceSupport.setSupportSendAiImageDirect(true);
            deviceSupportService.updateDeviceSupport(request.getSerialNumber(), cloudDeviceSupport);
        }
        return videoAIService.getAiTaskConfig(task, false);
    }

    public AiCloudParam getAiCloudParam(AiTaskConfigRequest request) {
        if(request.getTraceId() == null) {
            request.setTraceId(PhosUtils.randomNumString(8));
        }
        SaasAITaskIM saasAITaskIM = getSaasAiTaskConfig(request);
        boolean isSafemo = AppConstants.TENANTID_SAFEMO.equals(saasAITaskIM.getTenantId());
        log.info("getAiCloudParam start, saasAITaskIM:{}", JSON.toJSONString(saasAITaskIM));
        AiCloudParam aiCloudParam = AiCloudParam.newBuilder()
                .setOwnerId(Integer.valueOf(saasAITaskIM.getOwnerId()))
                .setTenantId(saasAITaskIM.getTenantId())
                .setCountryNo(saasAITaskIM.getCountryNo())
                .setOutParams(saasAITaskIM.getOutParams())
                .setContext(JSON.toJSONString(saasAITaskIM.getContext()))
                .setOutputTopic(saasAITaskIM.getOutputTopic())
                .setOutStorage(Outstorage.newBuilder()
                        .setBucket(saasAITaskIM.getOutStorage().getBucket())
                        .setKeyPrefix(saasAITaskIM.getOutStorage().getKeyPrefix())
                        .setServiceName(saasAITaskIM.getOutStorage().getServiceName()).build())
                .setTimeWatermarkPosition(WatermarkPosition.newBuilder()
                        .setX(saasAITaskIM.getTimeWatermarkPosition() != null ? saasAITaskIM.getTimeWatermarkPosition().getX() : 0)
                        .setY(saasAITaskIM.getTimeWatermarkPosition() != null ? saasAITaskIM.getTimeWatermarkPosition().getY() : 0)
                        .setWidth(saasAITaskIM.getTimeWatermarkPosition() != null ? saasAITaskIM.getTimeWatermarkPosition().getWidth() : 0)
                        .setHeight(saasAITaskIM.getTimeWatermarkPosition() != null ? saasAITaskIM.getTimeWatermarkPosition().getHeight() : 0)
                        .build())
                .setLogoWatermarkPosition(WatermarkPosition.newBuilder()
                        .setX(saasAITaskIM.getLogoWatermarkPosition() != null ? saasAITaskIM.getLogoWatermarkPosition().getX() : 0)
                        .setY(saasAITaskIM.getLogoWatermarkPosition() != null ? saasAITaskIM.getLogoWatermarkPosition().getY() : 0)
                        .setWidth(saasAITaskIM.getLogoWatermarkPosition() != null ? saasAITaskIM.getLogoWatermarkPosition().getWidth() : 0)
                        .setHeight(saasAITaskIM.getLogoWatermarkPosition() != null ? saasAITaskIM.getLogoWatermarkPosition().getHeight() : 0)
                        .build())
                .addAllRecognitionObjects(isSafemo ? Lists.newArrayList() : saasAITaskIM.getRecognitionObjects().stream().map(it-> it.getCategory().getEventObject().getObjectName()).collect(Collectors.toList()))
                .addAllIdentificationObjects(isSafemo ? Lists.newArrayList() : saasAITaskIM.getIdBox().getVisualizeRecognition().stream().map(it-> it.getEventObject().getObjectName()).collect(Collectors.toList()))
                // All features 目前只给safemo 云端使用， TODO: 后期会统一方案产品
                .addAllFeatures(isSafemo ? extensionManager.getInstalledAiExtensionSupportAiFeatures(Integer.valueOf(saasAITaskIM.getOwnerId()))
                        .stream()
                        .map(e-> AIFeature.newBuilder().setId(e.getId()).setName(e.getName()).build())
                        .collect(Collectors.toList())
                        : Lists.newArrayList())
                .addAllActivityZoneList(saasAITaskIM.getActivityZoneList().stream().map((it-> Activityzonelist.newBuilder()
                            .setId(it.getId())
                            .addAllVertices(Arrays.stream(it.getVertices().split(",")).map((Double::valueOf)).collect(Collectors.toList()))
                            .build()
                )).collect(Collectors.toList()))
                .addAllColors(saasAITaskIM.getIdBox().getColors().stream().map(it-> Color.newBuilder()
                                .setName(it.getName().getCode())
                                .setColor(it.getColor())
                                .build()).collect(Collectors.toList())
                ).build();
        return aiCloudParam;
    }

    /**
     * 哭声通知
     *
     * @param serialNumber
     * @param traceId
     * @param imageUrl
     */
    public void cryNotify(String serialNumber, String traceId, String imageUrl) {
        AiEvent event = new AiEvent()
                .setEventType(AiObjectActionEnum.CRY)
                .setActivatedZones(new ArrayList<>());

        AiTaskResult aiTaskResult = new AiTaskResult()
                .setTraceId(traceId)
                .setSerialNumber(serialNumber)
                .setEvents(Arrays.asList(event))
                .setImageUrl(imageUrl);

        notificationService.cryNotify(aiTaskResult);
    }


    /**
     * 获取视频当前所属eventKey
     *
     * @param serialNumber
     * @return
     */
    public String getVideoEventKey(String serialNumber, String traceId) {
        return getVideoEventKey(serialNumber, traceId, System.currentTimeMillis());
    }

    public String getVideoEventKey(String serialNumber, String traceId, long currentTime) {
        if(!StringUtils.hasLength(serialNumber) || !StringUtils.hasLength(traceId)){
            return "";
        }
        String videoEvent;
        String eventKey = DEVICE_VIDEO_EVENT.replace("{serialNumber}", serialNumber);
        String traceToEventKey = LIBRARY_TRACE_TO_VIDEO_EVENT.replace("{traceId}", traceId);

        Map<Object, Object> videoEventMap = videoEventRedisService.getHashEntries(eventKey);
        if (validateVideoEventMap(videoEventMap)) {
            videoEvent = videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME).toString();
            final VideoInVideoEvent inVideoEvent = videoInVideoEvent(videoEventMap, currentTime);
            log.debug("videoInVideoEvent end! sn={},traceId={},currentTime={},videoEventMap={},inVideoEvent={}", serialNumber, traceId, currentTime, JSON.toJSONString(videoEventMap), inVideoEvent);
            if (inVideoEvent == VideoInVideoEvent.EXPIRED) {
                // 在当前事件之前，返回输入的时间作为videoEvent，不更新redis
                return String.valueOf(currentTime);
            } else if (inVideoEvent == VideoInVideoEvent.APPEND) {
                videoEventRedisService.set(traceToEventKey, videoEvent, LIBRARY_TRACE_TIMEOUT);
                //更新最后更新时间，整体过期时间不需要处理
                videoEventRedisService.setHashFieldValue(eventKey, DEVICE_VIDEO_EVENT_LAST_TIME, String.valueOf(currentTime));
                // 符合当前事件规则,返回当前事件key
                return videoEvent;
            } else if (inVideoEvent == VideoInVideoEvent.NOT_APPEND) {
                // 符合当前事件规则,返回当前事件key，不更新redis
                return videoEvent;
            }
        }
        // 记录 videoEvent 最新更新时间、开始时间
        videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, String.valueOf(currentTime));
        videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, String.valueOf(currentTime));
        videoEventRedisService.setHashFieldValueMap(eventKey, videoEventMap, LIBRARY_VIDEO_EVENT_EXPIRE);

        videoEvent = String.valueOf(currentTime);
        // 记录trace -> videoEvent key
        videoEventRedisService.set(traceToEventKey, videoEvent, LIBRARY_TRACE_TIMEOUT);

        return videoEvent;
    }

    private static boolean validateVideoEventMap(Map<Object, Object> videoEventMap) {
        if (MapUtils.isEmpty(videoEventMap)) return false;
        try {
            if (!NumberUtils.isDigits(videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME) + "")) return false;
            if (!NumberUtils.isDigits(videoEventMap.get(DEVICE_VIDEO_EVENT_LAST_TIME) + "")) return false;
        } catch (Exception e) {
            log.error("validateVideoEventMap error! videoEventMap={}", JSON.toJSONString(videoEventMap), e);
            return false;
        }
        return true;
    }

    public void updateVideoEventLastTime(String serialNumber, String traceId, long currentTime, String videoEventKey) {
        String eventKey = DEVICE_VIDEO_EVENT.replace("{serialNumber}", serialNumber);
        Map<Object, Object> videoEventMap = videoEventRedisService.getHashEntries(eventKey);
        final String startTime = videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME).toString();
        final long lastTime = Long.parseLong(videoEventMap.get(DEVICE_VIDEO_EVENT_LAST_TIME).toString());
        boolean videoInVideoEvent = videoEventKey.equals(startTime) && currentTime > lastTime;
        if (videoInVideoEvent) {
            videoEventRedisService.setHashFieldValue(eventKey, DEVICE_VIDEO_EVENT_LAST_TIME, String.valueOf(currentTime));
        }
        log.debug("updateVideoEventLastTime end! sn={},traceId={},currentTime={},videoEventKey={},startTime={},lastTime={},videoInVideoEvent={}", serialNumber, traceId, currentTime, videoEventKey, startTime, lastTime, videoInVideoEvent);
    }

    public enum VideoInVideoEvent {
        EXPIRED, // 过时的视频
        NOT_APPEND, // 属于当前事件
        APPEND, // 延伸当前事件长度
        NEW, // 新事件
    }

    /**
     * 根据traceId 获取 videoEventKey
     * @param traceId
     * @return
     */
    public String getVideoEventKeyByTraceId( String traceId) {
        if(!StringUtils.hasLength(traceId)){
            return "";
        }
        String traceToEventKey = LIBRARY_TRACE_TO_VIDEO_EVENT.replace("{traceId}", traceId);
        return videoEventRedisService.get(traceToEventKey);
    }

    /**
     * 判断是否符合当前事件key
     *
     * @param videoEventMap
     * @return
     */
    public static VideoInVideoEvent videoInVideoEvent(Map<Object, Object> videoEventMap, Long currentTime) {
        long lastTime = Long.valueOf(videoEventMap.get(DEVICE_VIDEO_EVENT_LAST_TIME).toString());
        if ((currentTime - lastTime) > DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT) {
            // 距离最近一次视频超过15s
            return VideoInVideoEvent.NEW;
        }

        long startTime = Long.valueOf(videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME).toString());
        if ((currentTime - startTime) > DEVICE_VIDEO_EVENT_START_TIME_LIMIT) {
            // 距离本次事件开始超过30m
            return VideoInVideoEvent.NEW;
        }

        if (currentTime < startTime) {
            return VideoInVideoEvent.EXPIRED; // 如果当前视频在本次事件之前，则不聚合
        }
        if (currentTime <= lastTime) {
            return VideoInVideoEvent.NOT_APPEND; // 如果当前视频在本次事件中，则不更新DEVICE_VIDEO_EVENT_LAST_TIME
        }
        return VideoInVideoEvent.APPEND;
    }
}
