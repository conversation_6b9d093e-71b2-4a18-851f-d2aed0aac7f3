package com.addx.iotcamera.service;

import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.constants.ReportLogConstants;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.user.UserLiveReportService;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.LiveLogUtil;
import com.addx.iotcamera.util.MapUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSONObject;
import org.addx.iot.common.utils.PhosUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;

import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.enums.user.UserLiveResultEnum.*;

@Service
public class ReportLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReportLogService.class);

    @Resource
    private StreamService streamService;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private UserLiveReportService userLiveReportService;
    
    /**
     * app上报直播log
     *
     * @param jsonObject
     */
    public void appReportLive(String reportType, Integer userId, JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", ReportLogConstants.REPORT_GROUP_LIVE);
            jsonObject.put("userId", userId);
            jsonObject.put("reporter", ReportLogConstants.REPORTER_APP);

            String modelNo = deviceManualService.getModelNoBySerialNumber(jsonObject.getString("serialNumber"));
            jsonObject.put("modelNo", modelNo);
            jsonObject.put("time", PhosUtils.getUTCStamp());
            printLogInfo(jsonObject);

            String liveId = jsonObject.getString("liveId");
            String serialNumber = jsonObject.getString("serialNumber");
            LiveLogUtil.setLocalLiveInfo(liveId, serialNumber, userId);
            LiveLogUtil.log(liveId, LiveLogUtil.getNoQuotationStr(jsonObject.toJSONString()));
            LiveLogUtil.cleanLocalInfo();

            this.userAppScoreReload(userId,reportType,liveId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "SysReportLive error, msg: " + e.getMessage(), e);
        }
    }

    public static void printLogInfo(Map obj) {
        // 根据key 确定是否需要标记日志收集
        markLogCollectByFieldKey(obj);
        
        if (obj instanceof JSONObject) {
            LOGGER.info(((JSONObject) obj).toJSONString());
        } else {
            LOGGER.info(JsonUtil.toJson(obj));
        }
        
        final String reportType = Optional.ofNullable(obj.get("reportType")).map(it -> it + "").orElse("");
        final String reportGroup = Optional.ofNullable(obj.get("reportGroup")).map(it -> it + "").orElse("");
        final String reporter = Optional.ofNullable(obj.get("reporter")).map(it -> it + "").orElse("");
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getReportLogCountOptional()
                .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), reportType, reportGroup, reporter).inc()));
    }

    /**
     * 标记是否需要标记收集
     * @param logObj
     */
    public static void markLogCollectByFieldKey(Map logObj){
        if(!logObj.containsKey("key")){
            return;
        }
        String logKey = Optional.ofNullable(logObj.get("key")).map(String::valueOf).orElse("");
        boolean needCollect = REPORT_LOG_NEED_COLLECT_KEY.contains(logKey);
        logObj.put(REPORT_LOG_NEED_COLLECT, needCollect);
    }
    

    private void userAppScoreReload(Integer userId,String result,String liveId){
        if(LIVE_SUCCESS.getMessage().equals(result) || LIVE_INTER_RUPT.getMessage().equals(result) || LIVE_FAIL.getMessage().equals(result)){
            userLiveReportService.saveLiveReport( userId,result,liveId);
        }
    }

    /**
     * app上报
     *
     * @param jsonObject
     */
    public void appReport(String module,String reportType, Integer userId, JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", ReportLogConstants.REPORT_GROUP_APP_REPORT);
            jsonObject.put("userId", userId);
            jsonObject.put("reporter", ReportLogConstants.REPORTER_APP);
            jsonObject.put("module",module);
            jsonObject.put("time", PhosUtils.getUTCStamp());
            
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "appReport error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 系统上报直播log
     *
     * @param reportType
     * @param map
     */
    public void sysReportLive(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_LIVE);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "SysReportLive error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * wowza服务器上报log
     *
     * @param reportType
     * @param jsonObject
     */
    public void wowzaReportLive(String reportType, JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", ReportLogConstants.REPORT_GROUP_LIVE);
            jsonObject.put("reporter", ReportLogConstants.REPORTER_WOWZA);
            String modelNo = deviceManualService.getModelNoBySerialNumber(jsonObject.getString("serialNumber"));
            jsonObject.put("modelNo", modelNo);
            jsonObject.put("time", PhosUtils.getUTCStamp());
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "wowzaReportLive error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 设备上报log
     *
     * @param reportGroup
     * @param reportType
     * @param jsonObject
     */
    public void deviceReport(String reportGroup, String reportType, JSONObject jsonObject) {
        try {
            // 特别处理一下liveId
            if (reportType.equals(ReportLogConstants.REPORT_TYPE_DEVICE_WOWZA)
                    && !jsonObject.containsKey("liveId")) {
                String liveUrl = jsonObject.getJSONObject("value").getString("liveUrl");
                jsonObject.put("liveId", streamService.getLiveIdFromLiveUrl(liveUrl));
            }

            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", reportGroup);
            jsonObject.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            printLogInfo(jsonObject);

            LiveLogUtil.setLocalLiveInfo(null, jsonObject.getString("serialNumber"));
            LiveLogUtil.log(null, LiveLogUtil.getNoQuotationStr(jsonObject));
            LiveLogUtil.cleanLocalInfo();
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "deviceReportLive error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 设备上报状态
     *
     * @param jsonObject
     */
    public void deviceStatusReport(JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", "deviceStatus");
            jsonObject.put("reportGroup", "report");
            jsonObject.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            jsonObject.put("key", "device_status_report");
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "deviceStatusReport status to DH error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 设备上报支持功能
     * @param jsonObject
     */
    public void deviceSupportReport(JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", "deviceSupport");
            jsonObject.put("reportGroup", "report");
            jsonObject.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            jsonObject.put("key", "device_support_report");
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "deviceSupportReport status to DH error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 设备上报唤醒
     * @param jsonObject
     */
    public void deviceWakeupReport(JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", "deviceWakeup");
            jsonObject.put("reportGroup", "report");
            jsonObject.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            jsonObject.put("key", "device_wakeup_report");
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "deviceWakeupReport status to DH error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 设备上报
     *
     * @param reportGroup
     * @param reportType
     * @param jsonObject
     */
    public void deviceEventReport(String reportGroup, String reportType, JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", reportGroup);
            jsonObject.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "deviceReportLive error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 系统上报udp
     *
     * @param reportType
     * @param map
     */
    public void sysReportUdp(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_UDP);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportUdp error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 系统上报绑定log
     *
     * @param reportType
     * @param map
     */
    public void sysReportBind(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_BIND);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportUdp error, msg: " + e.getMessage(), e);
        }
    }

    /**
     * 上报电量消耗log
     *
     * @param reportType
     * @param map
     */
    public void sysReportBattery(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_CENSUS);
            map.put("reporter", ReportLogConstants.REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportBattery error, msg: " + e.getMessage(), e);
        }
    }

    public void sysReportDeviceOffline(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_CENSUS);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportUdp error, msg: " + e.getMessage(), e);
        }
    }

    public void sysReportWakeup(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_WAKEUP);
            map.put("reporter", ReportLogConstants.REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportWakeup error", e);
        }
    }

    public void sysReportEvent(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_REPORT);
            map.put("reporter", ReportLogConstants.REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportEvent error", e);
        }
    }

    public void debugSysReportEvent(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_REPORT);
            map.put("reporter", ReportLogConstants.REPORTER_SYS);
            LOGGER.debug(JsonUtil.toJson(map));
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportEvent error", e);
        }
    }

    public void appReportMsgReceive(String reportType, JSONObject map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_MSG);
            map.put("reporter", ReportLogConstants.REPORTER_APP);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "appReportMsgReceive error, msg: " + e.getMessage(), e);
        }
    }

    public void sysReportMsgPush(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_MSG);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportMsgPush error, msg: " + e.getMessage(), e);
        }
    }

    public void sysReportUpdateUserConfig(String reportType, Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_USERCONFIG);
            map.put("reporter", REPORTER_SYS);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportUpdateUserConfig error, msg: " + e.getMessage(), e);
        }
    }

    public void sysReportDeviceBatteryEvent(String reportType, Map<Object, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", ReportLogConstants.REPORT_GROUP_CENSUS);
            map.put("reporter", ReportLogConstants.REPORTER_DEVICE);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "sysReportDeviceBatteryEvent error, msg: " + e.getMessage(), e);
        }
    }


    public void reportLog(String reportType,
                          String reportGroup,
                          String reporter,
                          Map<String, Object> map) {
        try {
            map.put("reportType", reportType);
            map.put("reportGroup", reportGroup);
            map.put("reporter", reporter);
            printLogInfo(map);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, reportType + " error ", e);
        }
    }

    public void reportLog(String reportType,
                          String reportGroup,
                          String reporter,
                          JSONObject jsonObject) {
        try {
            jsonObject.put("reportType", reportType);
            jsonObject.put("reportGroup", reportGroup);
            jsonObject.put("reporter", reporter);
            printLogInfo(jsonObject);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, reportType + " error ", e);
        }
    }

    /**
     * 系统统计log
     * @param reportType
     * @param reportGroup
     * @param jsonObject
     */
    public void reportSystemLog(String reportType,
                          String reportGroup,
                          JSONObject jsonObject) {
        this.reportLog(reportType,reportGroup,REPORTER_SYS,jsonObject);
    }

    /**
     * app 推送message log
     *
     * @param userId
     * @param pushResult
     * @param traceId
     * @param msgType
     * @param serialNumber
     * @param content
     */
    public void reportPushMessageLog(Integer userId,
                                     Boolean pushResult,
                                     String traceId,
                                     Integer msgType,
                                     String serialNumber,
                                     String content) {
        Map<String, Object> map = MapUtil.builder()
                .put("userId", userId)
                .put("pushResult", pushResult)
                .put("traceId", traceId)
                .put("msgType", msgType)
                .put("serialNumber", serialNumber)
                .put("content", content)
                .put("endTime", Instant.now().getEpochSecond())
                .build();
        MDC.put(MDCKeys.SERIAL_NUMBER, serialNumber);
        this.reportLog(REPORT_TYPE_APP_PUSH_END, REPORT_GROUP_MESSAGE_PUSH, REPORTER_SYS, map);
    }

    public void reportChangeToBackupLog(String serviceName) {
        this.reportLog(REPORT_TYPE_CHANGE_TO_BACK_UP, REPORT_GROUP_REPORT, REPORTER_DEVICE, MapUtil.builder().put("serviceName", serviceName).build());
    }

}
