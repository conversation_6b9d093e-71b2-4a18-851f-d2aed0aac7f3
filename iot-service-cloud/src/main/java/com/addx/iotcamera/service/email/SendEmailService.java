package com.addx.iotcamera.service.email;

import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.vip.SendInvoiceMailRequest;
import com.addx.iotcamera.bean.db.PaymentConsent;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.mail.SendMailChangeInfo;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.bean.manage.SendEmailInfo;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.app.AppSendEmailInfoConfig;
import com.addx.iotcamera.constants.SendEmailConstants;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.Mail;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import groovy.util.logging.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

@lombok.extern.slf4j.Slf4j
@Service
@Slf4j
public class SendEmailService {


    @Autowired
    private MailSendConfig mailSendConfig;

    @Autowired
    private AppAccountConfig appAccountConfig;

    @Autowired
    private UserService userService;

    @Resource
    @Lazy
    private PaymentService paymentService;

    @Autowired
    private ProductService productService;

    @Autowired
    private UserOrderService userOrderService;

    @Autowired
    private UserVipService userVipService;

    @Autowired
    private PaymentConsentService paymentConsentService;

    @Resource
    private AppSendEmailInfoConfig appSendEmailInfoConfig;

    @Autowired
    private OrderService orderService;

    @Autowired
    private TierService tierService;


    @Autowired
    private TenantTierConfig tenantTierConfig;

    private final List<String> langList= Arrays.asList("ar", "he");

    /**
     * 发送三方支付发票邮件
     * @param userId
     * @param request
     * @return
     */
    public Result sendInvoiceEmail(Integer userId,  SendInvoiceMailRequest request) {
        log.info("sendInvoiceEmail request invoice email:{}, userId:{}", request, userId);
        if (request == null || request.getPaymentFlowId() == null || request.getLanguage() == null || request.getApp() == null || request.getApp().getTenantId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "参数异常");
        }

        User user = userService.queryUserById(userId);
        if (user == null || user.getEmail() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "用户不存在或已经被禁用");
        }
        SendEmailInfo sendEmailInfo = appSendEmailInfoConfig.getSendEmailInfo(request.getApp().getTenantId(), request.getApp().getAppType());
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(request.getApp().getTenantId());
        PaymentFlow paymentFlow = paymentService.queryPaymentFlowById(request.getPaymentFlowId());
        UserVipDO userVipDO = userVipService.queryUserVipInfoByTradeNo(paymentFlow.getTradeNo());
        ProductDO productDO = productService.queryProductById(paymentFlow.getProductId());
        String payMethod = getPayMethodByConsentId(paymentFlow.getPaymentConsentId(), paymentFlow.getType());

        SendMailChangeInfo sendMailChangeInfo = new SendMailChangeInfo();
        sendMailChangeInfo.setPaymentDate(paymentFlow.getPurchaseDate());

        // 获取当前时间并格式化
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String invoiceDate = now.format(formatter);
        sendMailChangeInfo.setInvoiceNo(paymentFlow.getInvoiceNo() == null ? "" : paymentFlow.getInvoiceNo());
        sendMailChangeInfo.setAppName(request.getApp().getAppName() == null ? "" : request.getApp().getAppName());
        sendMailChangeInfo.setInvoiceDate(invoiceDate);
        sendMailChangeInfo.setPaymentDate(convertToDateOnly(paymentFlow.getPurchaseDate()));
        sendMailChangeInfo.setNameOnCard(user.getName() == null ? "" : user.getName());
        sendMailChangeInfo.setUserEmail(user.getEmail() == null ? "" : user.getEmail());
        sendMailChangeInfo.setTextAlign(langList.contains(request.getLanguage())? "right" : "left");
        sendMailChangeInfo.setTextDirection(langList.contains(request.getLanguage())? "rtl" : "ltr");
        sendMailChangeInfo.setFloatDirection(langList.contains(request.getLanguage())? "left" : "right");
        sendMailChangeInfo.setCompanyName(sendEmailInfo.getCompanyName());
        sendMailChangeInfo.setPayMethod(payMethod);
        sendMailChangeInfo.setSupportEmail(emailAccount.getAccount());
        //转化为dollor格式
        Integer amount = paymentFlow.getAmount();
        String amountUS = formatCurrency(amount);
        sendMailChangeInfo.setTotal(amountUS);
        sendMailChangeInfo.setSubtotalPrice(amountUS);
        sendMailChangeInfo.setProductAmount(amountUS);
        sendMailChangeInfo.setProductName(tierService.queryTierName(request,productDO));
        sendMailChangeInfo.setTopLogoImage(getTopLogoImageByTopLogoImageIsNull(sendEmailInfo, langList.contains(request.getLanguage())? "right" : "left"));
        sendMailChangeInfo.setProductPeriod(formatDateRange(userVipDO.getEffectiveTime(), userVipDO.getEndTime()));
        log.info("sendInvoiceEmail send mail change info:{}", JSON.toJSONString(sendMailChangeInfo));

        return new Result(this.sendThreeInvoiceMail(user.getEmail(), request.getApp().getTenantId(), request.getLanguage(), sendMailChangeInfo));
    }


    /**
     * 发送三方支付续订成功后邮件
     * @param userId
     * @param productId
     * @param orderId
     * @return
     */
    public Result sendThreeSuccessMail(Integer userId, Integer productId, Long orderId, String paymentIntentId) {
        log.info("sendThreeSuccessMail userId={}, productId={},orderId={}", userId, productId, orderId);
        if (orderId == null || productId == null || userId == null || paymentIntentId == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS);
        }

        User user = userService.queryUserById(userId);
        if (user == null || user.getEmail() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "用户不存在或已经被禁用");
        }
        String tenantId = user.getTenantId();
        //邮件
        if (!mailSendConfig.getConfig().containsKey(tenantId)) {
            log.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
            throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
        }
        if (!mailSendConfig.getConfig().get(tenantId).containsKey(user.getLanguage())) {
            log.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, user.getLanguage());
            throw new ParamException(INVALID_PARAMS.getCode(), "no language");
        }
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);
        ProductDO productDO = productService.queryProductById(productId);
        OrderDO orderDO = orderService.queryOrderInfoById(orderId);
        if (orderDO == null || productDO == null) {
            log.warn("三方支付发送成功邮件失败，订单或商品信息为空");
            return Result.Error(ResultCollection.INVALID_PARAMS, "订单或商品信息为空");
        }
        PaymentFlow paymentFlow = paymentService.queryPaymentFlowByTradeNo(paymentIntentId, orderDO.getOrderSn());
        if (paymentFlow == null) {
            log.error("支付流水为空, tradeNo:{}, outTradeNo:{}", paymentIntentId, orderDO.getOrderSn());
            return Result.Error(ResultCollection.INVALID_PARAMS, "支付流水为空");
        }
        UserVipDO userVipDO = userVipService.queryUserVipInfoByTradeNo(paymentFlow.getTradeNo());
        SendEmailInfo sendEmailInfo = appSendEmailInfoConfig.getSendEmailInfo(tenantId, user.getAppType());
        String payMethod = getPayMethodByConsentId(paymentFlow.getPaymentConsentId(), paymentFlow.getType());
        SendMailChangeInfo sendMailChangeInfo = new SendMailChangeInfo();
        int time = (int) (System.currentTimeMillis() / 1000);
        sendMailChangeInfo.setUserEmail(user.getEmail());
        sendMailChangeInfo.setProductName(tierService.queryTierName(productDO,user.getLanguage(),user.getTenantId()));
        Integer amount = paymentFlow.getAmount();
        String amountUS = formatCurrency(amount);
        sendMailChangeInfo.setAmount(amountUS);
        sendMailChangeInfo.setDateCharged(DateUtils.timeStamp2Date(time,DateUtils.YYYY_MM_DD_HH_MM_SS));
        sendMailChangeInfo.setPeriod(formatDateRange(userVipDO.getEffectiveTime(), userVipDO.getEndTime()));
        sendMailChangeInfo.setPayMethod(payMethod);
        sendMailChangeInfo.setTopLogoImage(getTopLogoImageByTopLogoImageIsNull(sendEmailInfo, langList.contains(user.getLanguage())? "right" : "left"));
        sendMailChangeInfo.setSupportEmail(emailAccount.getAccount() == null ? "" : emailAccount.getAccount());
        sendMailChangeInfo.setTextAlign(langList.contains(user.getLanguage())? "right" : "left");
        sendMailChangeInfo.setTextDirection(langList.contains(user.getLanguage())? "rtl" : "ltr");
        sendMailChangeInfo.setFloatDirection(langList.contains(user.getLanguage())? "left" : "right");
        sendMailChangeInfo.setAppName(tenantTierConfig.queryAppNameByTenant(tenantId));
        log.info("sendThreeSuccessMail send mail change info:{}", JSON.toJSONString(sendMailChangeInfo));

        return new Result(this.sendThreeSuccessMail(user.getEmail(), user.getTenantId(), user.getLanguage(), sendMailChangeInfo));
    }

    public Result sendThreeFailMail(Integer userId, Integer productId, Integer date) {
        log.info("sendThreeFailMail request userId={}, productId={}, date={}", userId, productId, date);
        if (productId == null || userId == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS);
        }

        User user = userService.queryUserById(userId);
        if (user == null || user.getEmail() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "用户不存在或已经被禁用");
        }
        String tenantId = user.getTenantId();
        String appType = user.getAppType();
        //邮件
        if (!mailSendConfig.getConfig().containsKey(tenantId)) {
            log.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
            throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
        }
        if (!mailSendConfig.getConfig().get(tenantId).containsKey(user.getLanguage())) {
            log.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, user.getLanguage());
            throw new ParamException(INVALID_PARAMS.getCode(), "no language");
        }
        SendEmailInfo sendEmailInfo = appSendEmailInfoConfig.getSendEmailInfo(tenantId, appType);
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);
        ProductDO productDO = productService.queryProductById(productId);
        SendMailChangeInfo sendMailChangeInfo = new SendMailChangeInfo();
        sendMailChangeInfo.setUserEmail(user.getEmail());
        sendMailChangeInfo.setProductName(tierService.queryTierName(productDO,user.getLanguage(),user.getTenantId()));
        sendMailChangeInfo.setServiceName(sendEmailInfo.getServiceName() == null ? "" : sendEmailInfo.getServiceName());
        sendMailChangeInfo.setSupportEmail(emailAccount.getAccount() == null ? "" : emailAccount.getAccount());
        sendMailChangeInfo.setTextAlign(langList.contains(user.getLanguage())? "right" : "left");
        sendMailChangeInfo.setDate(DateUtils.timeStamp2Date(date,DateUtils.YYYY_MM_DD_HH_MM_SS));
        sendMailChangeInfo.setAppName(tenantTierConfig.queryAppNameByTenant(tenantId));
        sendMailChangeInfo.setTextDirection(langList.contains(user.getLanguage())? "rtl" : "ltr");
        sendMailChangeInfo.setTopLogoImage(getTopLogoImageByTopLogoImageIsNull(sendEmailInfo, langList.contains(user.getLanguage())? "right" : "left"));
        log.info("sendThreeFailMail send mail change info:{}", sendMailChangeInfo);

        return new Result(this.sendThreeFailMail(user.getEmail(), user.getTenantId(), user.getLanguage(), sendMailChangeInfo));
    }




    private boolean sendThreeSuccessMail(String email, String tenantId, String language,  SendMailChangeInfo sendMailChangeInfo) {
        log.info("util sendThreeSuccessMail email:{}, tenantId:{}, language:{}", email, tenantId, language);
        //邮件
        if (!mailSendConfig.getConfig().containsKey(tenantId)) {
            log.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
            throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
        }
        if (!mailSendConfig.getConfig().get(tenantId).containsKey(language)) {
            log.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, language);
            throw new ParamException(INVALID_PARAMS.getCode(), "no language");
        }
        MailSend mailSend = mailSendConfig.getConfig().get(tenantId).get(language);
        String title = mailSend.getPay3rdSubscribeSuccessTitle();
        title = title.replace("$APP_NAME", sendMailChangeInfo.getAppName());
        String body = mailSend.getPay3rdSubscribeSuccessBody();
        log.info("util sendThreeSuccessMail title:{}, body:{}", title, body);
        body = SendEmailConstants.THREE_INVOICE_BODY.replace("$BODY",body);
        body = body.replace("$USER_EMAIL",sendMailChangeInfo.getUserEmail())
                .replace("$APP_NAME", sendMailChangeInfo.getAppName())
                .replace("$TOP_LOGO_IMAGE", sendMailChangeInfo.getTopLogoImage())
                .replace("$PRODUCT_NAME", sendMailChangeInfo.getProductName())
                .replace("$AMOUNT", sendMailChangeInfo.getAmount())
                .replace("$DATE_CHARGED", sendMailChangeInfo.getDateCharged())
                .replace("$PERIOD", sendMailChangeInfo.getPeriod())
                .replace("$PAYMENT_METHOD", sendMailChangeInfo.getPayMethod())
                .replace("$SUPPORT_EMAIL", sendMailChangeInfo.getSupportEmail())
                .replace("$TEXT_DIRECTION", sendMailChangeInfo.getTextDirection())
                .replace("$FLOAT_DIRECTION", sendMailChangeInfo.getFloatDirection())
                .replace("$TEXT_ALIGN", sendMailChangeInfo.getTextAlign());
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);
        return Mail.SendHtmlEmail(email, title, body,emailAccount);
    }


    private boolean sendThreeFailMail(String email, String tenantId, String language, SendMailChangeInfo sendMailChangeInfo) {
        //邮件
        if (!mailSendConfig.getConfig().containsKey(tenantId)) {
            log.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
            throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
        }
        if (!mailSendConfig.getConfig().get(tenantId).containsKey(language)) {
            log.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, language);
            throw new ParamException(INVALID_PARAMS.getCode(), "no language");
        }
        MailSend mailSend = mailSendConfig.getConfig().get(tenantId).get(language);
        String title = mailSend.getPay3rdSubscribeFailedTitle();
        title = title.replace("$APP_NAME", sendMailChangeInfo.getAppName());
        String body = mailSend.getPay3rdSubscribeFailedBody();
        log.info("util sendThreeFailMail title:{}, body:{}", title, body);
        body = SendEmailConstants.THREE_FAIL_BODY.replace("$BODY",body);
        body = body.replace("$USER_EMAIL",sendMailChangeInfo.getUserEmail())
                .replace("$APP_NAME", sendMailChangeInfo.getAppName())
                .replace("$TOP_LOGO_IMAGE", sendMailChangeInfo.getTopLogoImage())
                .replace("$PRODUCT_NAME", sendMailChangeInfo.getProductName())
                .replace("$DATE", sendMailChangeInfo.getDate())
                .replace("$SERVICE_NAME", sendMailChangeInfo.getServiceName())
                .replace("$SUPPORT_EMAIL", sendMailChangeInfo.getSupportEmail())
                .replace("$TEXT_DIRECTION", sendMailChangeInfo.getTextAlign())
                .replace("$TEXT_ALIGN", sendMailChangeInfo.getTextAlign())
                .replace("$TEXT_DIRECTION", sendMailChangeInfo.getTextDirection());
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);

        return Mail.SendHtmlEmail(email, title, body,emailAccount);
    }


    private boolean sendThreeInvoiceMail(String email, String tenantId, String language, SendMailChangeInfo sendMailChangeInfo) {
        //邮件
        if (!mailSendConfig.getConfig().containsKey(tenantId)) {
            log.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
            throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
        }
        if (!mailSendConfig.getConfig().get(tenantId).containsKey(language)) {
            log.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, language);
            throw new ParamException(INVALID_PARAMS.getCode(), "no language");
        }
        MailSend mailSend = mailSendConfig.getConfig().get(tenantId).get(language);
        String title = mailSend.getPay3rdInvoiceEmailTitle();
        title = title.replace("$APP_NAME",sendMailChangeInfo.getAppName());
        String body = mailSend.getPay3rdInvoiceEmailBody();
        body = SendEmailConstants.THREE_INVOICE_BODY.replace("$BODY", body);
        body = body.replace("$INVOICE_NUMBER",sendMailChangeInfo.getInvoiceNo())
                .replace("$TOP_LOGO_IMAGE", sendMailChangeInfo.getTopLogoImage())
                .replace("$INVOICE_DATE", sendMailChangeInfo.getInvoiceDate())
                .replace("$PAYMENT_DATE", sendMailChangeInfo.getPaymentDate())
                .replace("$COMPANY_NAME", sendMailChangeInfo.getCompanyName())
                .replace("$CUSTOMER_NAME", sendMailChangeInfo.getNameOnCard())
                .replace("$CUSTOMER_EMAIL", sendMailChangeInfo.getUserEmail())
                .replace("$PRODUCT_NAME", sendMailChangeInfo.getProductName())
                .replace("$PRODUCT_AMOUNT", sendMailChangeInfo.getProductAmount())
                .replace("$PRODUCT_PERIOD", sendMailChangeInfo.getProductPeriod())
                .replace("$SUBTOTAL", sendMailChangeInfo.getSubtotalPrice())
                .replace("$TOTAL", sendMailChangeInfo.getTotal())
                .replace("$PAYMENT_METHOD", sendMailChangeInfo.getPayMethod())
                .replace("$SUPPORT_EMAIL", sendMailChangeInfo.getSupportEmail())
                .replace("$TEXT_ALIGN", sendMailChangeInfo.getTextAlign())
                .replace("$FLOAT_DIRECTION", sendMailChangeInfo.getFloatDirection())
                .replace("$TEXT_DIRECTION", sendMailChangeInfo.getTextDirection());
        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);

        return Mail.SendHtmlEmail(email, title, body,emailAccount);
    }


    private String getTopLogoImageByTopLogoImageIsNull(SendEmailInfo sendEmailInfo, String textAlign) {
        if (sendEmailInfo == null || sendEmailInfo.getTopImageUrl() == null || sendEmailInfo.getTopImageUrl().isEmpty()) {
            return "";
        }
        return SendEmailConstants.TOP_LOGO_IMAGE.replace("$TOP_IMAGE_URL", sendEmailInfo.getTopImageUrl())
                .replace("$TEXT_ALIGN", textAlign == null ? "left" : textAlign);
    }


    private String getPayMethodByConsentId(String paymentConsentId, Integer type) {
        PaymentConsent paymentConsent = paymentConsentService.getByPaymentConsentId(paymentConsentId);
        log.info("paymentConsent:{}", JSON.toJSON(paymentConsent));
        String payMethod = userOrderService.getBoughtFromByOrderType(type);
        if (paymentConsent != null && paymentConsent.getPaymentMethod() != null) {
            JSONObject paymentMethod = JSON.parseObject(paymentConsent.getPaymentMethod());
            String payType = paymentMethod.getString("type");
            if (Objects.equals(payType, "card") && paymentMethod.containsKey("card")) {
                JSONObject cardInfo = paymentMethod.getJSONObject("card");
                // 提取brand
                if (cardInfo.containsKey("brand")) {
                    String brand = cardInfo.getString("brand");
                    String last4 = cardInfo.getString("last4");
                    payMethod = brand + "       *****" + last4;
                }
            }
        }
        return payMethod == null ? "" : payMethod;
    }


    /**
     * 将开始时间戳和结束时间戳转换为日期范围格式
     * 支持同年和跨年的情况：
     * 同年：Apr 27 - May 27, 2025
     * 跨年：Dec 27, 2024 - Jan 27, 2025
     *
     * @param startTimestamp 开始时间戳（秒）
     * @param endTimestamp 结束时间戳（秒）
     * @return 格式化后的日期范围字符串
     */
    public String formatDateRange(Integer startTimestamp, Integer endTimestamp) {
        if (startTimestamp == null || endTimestamp == null) {
            return "";
        }

        try {
            // 转换为毫秒时间戳
            long startMillis = startTimestamp * 1000L;
            long endMillis = endTimestamp * 1000L;

            // 创建Calendar对象
            Calendar startCal = Calendar.getInstance();
            Calendar endCal = Calendar.getInstance();
            startCal.setTimeInMillis(startMillis);
            endCal.setTimeInMillis(endMillis);

            // 创建日期格式化器
            // 创建日期格式化器 - 修改为yyyy-MM-dd格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            //SimpleDateFormat fullFormat = new SimpleDateFormat("MMM d, yyyy", Locale.ENGLISH);

            String startFormatted = dateFormat.format(new Date(startMillis));
            String endFormatted = dateFormat.format(new Date(endMillis));
            return String.format("%s - %s", startFormatted, endFormatted);


        } catch (Exception e) {
            log.error("格式化日期范围时发生异常，startTimestamp={}, endTimestamp={}", startTimestamp, endTimestamp, e);
            return "";
        }
    }

    /**
     * 将 yyyy-MM-dd HH:mm:ss 格式的日期时间字符串转换为 yyyy-MM-dd 格式
     *
     * @param dateTimeString 日期时间字符串
     * @return 日期字符串
     */
    public String convertToDateOnly(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.isEmpty()) {
            return "";
        }

        try {
            // 解析原格式
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeString, inputFormatter);

            // 输出为新格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return dateTime.format(outputFormatter);

        } catch (Exception e) {
            log.error("日期格式转换失败，输入：{}", dateTimeString, e);
            return "";
        }
    }

    /**
     * 将整形转换为美国货币格式
     * 举例 449  -->  $4.49
     * @param cents
     * @return
     */
    public String formatCurrency(int cents) {
        NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance(Locale.US);
        double dollars = cents / 100.0;
        return currencyFormatter.format(dollars);
    }

}
