package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.video.CxsTsFileInfo;
import com.addx.iotcamera.bean.video.StorageCredentials;
import com.addx.iotcamera.bean.video.StorageDest;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.*;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.helper.aws.BucketHelper;
import com.addx.iotcamera.service.StorageServiceAvailableManager;
import com.addx.iotcamera.service.CosService;
import com.addx.iotcamera.service.OciService;
import com.addx.iotcamera.service.device.DeviceWhiteListService;
import com.addx.iotcamera.util.BeanUtil;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.MyAwsUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.AmazonS3;
import com.google.cloud.storage.BlobInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.PirServiceName.*;

@Slf4j
@Accessors(chain = true)
@Component
public class StorageAllocateService {

    @Setter
    @Getter
    @Autowired
    private StorageAllocateConfig config;
    @Autowired
    private DeviceWhiteListService deviceWhiteListService;

    @PostConstruct
    public void init() {
        log.info("StorageAllocateService init! config={}", JSON.toJSONString(config));
    }

    @Autowired
    @Setter
    private S3 s3;
    @Autowired
    private VideoSliceConfig videoSliceConfig;
    @Autowired
    private AmazonS3 s3Client;
    @Autowired
    private GoogleStorageService gcsService;
    @Autowired
    @Setter
    private GcsOptions gcsOptions;
    @Autowired
    private CosService cosService;
    @Autowired
    @Setter
    private CosConfig cosConfig;
    @Autowired
    private OciService ociService;
    @Autowired
    @Setter
    private OciConfig ociConfig;
    @Autowired
    @Setter
    private StorageServiceAvailableManager storageServiceAvailableManager;

    public boolean isEnableServiceName(PirServiceName serviceName) {
        switch (serviceName) {
            case s3:
                return true; // 都支持s3
            case gcs:
                return gcsOptions.getEnable();
            case cos:
                return cosConfig.getEnable();
            case oci:
                return ociConfig.getEnable();
            case bxs:
            case cxs:
                return true; // 视频文件存在bx/cx本地，只上传索引，都支持。
            default:
                return false;
        }
    }

    /**
     * 根据回看天数计算存储目的地
     */
    public StoreBucket getBucketByLookBackDays(PirServiceName serviceName, int lookBackDays, int adminId) {
        switch (serviceName) {
            case s3:
                return s3.getBucketByLookBackDays(lookBackDays, adminId);
            case gcs:
                return new StoreBucket().setBucket(gcsOptions.getBucketByLookBackDays(lookBackDays));
            case cos:
                return new StoreBucket().setBucket(cosConfig.getBucketByLookBackDays(lookBackDays)).setRegion(cosConfig.getParams().getRegion());
            case oci:
                return ociConfig.getBucketByLookBackDays(lookBackDays, adminId);
            default:
                return null;
        }
    }

    /**
     * 根据bucket计算上传根路径
     */
    public String getRootUrlByBucket(PirServiceName serviceName, StoreBucket bucket) {
        if (bucket == null) return null;
        switch (serviceName) {
            case s3:
                final BucketHelper bucketHelper = new BucketHelper(s3Client, bucket.getBucket(), videoSliceConfig.getDeviceS3AccelerateEnable());
                return bucketHelper.getDeviceUploadRootPath();
            case gcs:
                return GoogleStorageService.buildGscUrlForUploadObject(bucket.getBucket());
            case cos:
                return cosService.getRootUrl(bucket.getBucket());
            case oci:
                return ociService.getRootUrl(bucket);
            default:
                return null;
        }
    }

    public PirServiceName getServiceNameForWhiteUserId(Set<PirServiceName> supportServiceNames, Integer adminId) {
        if (CollectionUtils.isEmpty(supportServiceNames)) return null;
        for (final PirServiceName serviceName : supportServiceNames) {
            if (!isEnableServiceName(serviceName)) continue;
            if (Optional.ofNullable(config.getWhiteUserIds()).map(it -> it.get(serviceName.name()))
                    .filter(it -> it.contains(adminId)).isPresent()) {
                return serviceName;
            }
        }
        return null;
    }

    /**
     * 根据服务比例设置去获取要使用的云存储服务
     *
     * @param ratios              [ { "min": 0, "max": 50, "serviceNames": [ "gcs", "oci", "s3" ] } ]
     * @param supportServiceNames 设备支持的云存储服务
     * @param adminId             管理员id
     * @return
     */
    public PirServiceName getServiceNameForRatios(List<StorageAllocateConfig.RatioItem> ratios, Set<PirServiceName> supportServiceNames, Integer adminId) {
        if (CollectionUtils.isEmpty(ratios) || CollectionUtils.isEmpty(supportServiceNames)) {
            com.addx.iotcamera.util.LogUtil.error(log, "getServiceNameForRatios fail! ratios or supportServiceName is empty! ratios={},supportServiceNames={},adminId={}"
                    , JSON.toJSONString(ratios), JSON.toJSONString(supportServiceNames), JSON.toJSONString(adminId));
            return PirServiceName.s3; // s3兜底
        }
        final Integer totalRatio = ratios.stream().map(it -> it.getMax()).max(Integer::compareTo).orElse(0);
        if (totalRatio <= 0) {
//            com.addx.iotcamera.util.LogUtil.error(log, "getServiceNameForRatios fail! totalRatio<0! ratios={},supportServiceNames={},adminId={}"
//                    , JSON.toJSONString(ratios), JSON.toJSONString(supportServiceNames), JSON.toJSONString(adminId));
            return PirServiceName.s3; // s3兜底
        }
        final int modValue = adminId % totalRatio;
        for (final StorageAllocateConfig.RatioItem ratioItem : ratios) {
            if (!(modValue >= ratioItem.getMin() && modValue < ratioItem.getMax())) continue;
            for (final String serviceName : ratioItem.getServiceNames()) {
                final PirServiceName enums = valueOf(serviceName);
                if (!isEnableServiceName(enums)) continue;
                if (supportServiceNames.contains(enums)) return enums;
            }
        }
//        com.addx.iotcamera.util.LogUtil.error(log, "getServiceNameForRatios fail! not found ratioItem! ratios={},supportServiceNames={},adminId={}"
//                , JSON.toJSONString(ratios), JSON.toJSONString(supportServiceNames), JSON.toJSONString(adminId));
        return PirServiceName.s3; // s3兜底
    }

    public PirServiceName getServiceName(List<StorageAllocateConfig.RatioItem> ratios, Set<PirServiceName> supportServiceNames, String sn, Integer adminId) {
        try {
            // 计算可用服务与支持服务的交集，保持availableServices的顺序
            Set<PirServiceName> availableServices = storageServiceAvailableManager.filterUnavailableServiceName(sn, supportServiceNames);
            log.debug("getServiceName availableServices={}", JSON.toJSONString(availableServices));

            // 如果交集为空，返回默认的s3服务
            if (availableServices.isEmpty()) {
                return PirServiceName.s3;
            }

            // 使用交集继续原有的逻辑
            final PirServiceName whiteListServiceName = deviceWhiteListService.getPirVideoStorageServiceBySn(sn);
            if (whiteListServiceName != null && availableServices.contains(whiteListServiceName)) {
                log.debug("getServiceName whiteListServiceName={}", JSON.toJSONString(whiteListServiceName));
                return whiteListServiceName;
            }
            final PirServiceName serviceName = getServiceNameForWhiteUserId(availableServices, adminId); // 按白名单用户id
            if (serviceName != null) {
                log.debug("getServiceName serviceName={}", JSON.toJSONString(serviceName));
                return serviceName;
            }
            return getServiceNameForRatios(ratios, availableServices, adminId); // 按固定比例分
        } catch (Exception e) {
            log.error("getServiceName error", e);
            return PirServiceName.s3;
        }
    }

    /**
     * 获取存储目标
     *
     * @param ratios              serviceName -> 比例值
     * @param supportServiceNames 设备支持的云存储服务
     * @param lookBackDays        回看天数
     * @return
     */
    public StorageDest getStorageDest(List<StorageAllocateConfig.RatioItem> ratios, Set<PirServiceName> supportServiceNames, String sn, Integer adminId, Integer lookBackDays) {
        final PirServiceName serviceName = getServiceName(ratios, supportServiceNames, sn, adminId);
        log.debug("getStorageDest serviceName={}", JSON.toJSONString(serviceName));
        final StoreBucket bucket = getBucketByLookBackDays(serviceName, lookBackDays, adminId);
        final String rootUrl = getRootUrlByBucket(serviceName, bucket);
        final StorageDest storageDest;
        if (rootUrl == null) {
            com.addx.iotcamera.util.LogUtil.error(log, "找不到云存储服务下指定回看天数的bucket! serviceName={},lookBackDays={}", serviceName, lookBackDays);
            final StoreBucket s3Bucket = getBucketByLookBackDays(PirServiceName.s3, lookBackDays, adminId);
            storageDest = new StorageDest().setServiceName(PirServiceName.s3).setBucket(s3Bucket.getBucket())
                    .setRootUrl(getRootUrlByBucket(PirServiceName.s3, s3Bucket));
        } else {
            storageDest = new StorageDest().setServiceName(serviceName).setBucket(bucket.getBucket()).setRootUrl(rootUrl);
        }
        log.info("getStorageDest end! ratios={},supportServiceNames={},adminId={},lookBackDays={},storageDest={}"
                , JSON.toJSONString(ratios), JSON.toJSONString(supportServiceNames), adminId, lookBackDays, JSON.toJSONString(storageDest));
        return storageDest;
    }

    public StorageDest getVipStorageDest(Set<PirServiceName> supportServiceNames, String sn, Integer adminId, Integer lookBackDays) {
        return getStorageDest(config.getVipRatios(), supportServiceNames, sn, adminId, lookBackDays);
    }

    public StorageDest getNoVipStorageDest(Set<PirServiceName> supportServiceNames, String sn, Integer adminId, Integer lookBackDays) {
        return getStorageDest(config.getNoVipRatios(), supportServiceNames, sn, adminId, lookBackDays);
    }

    /**
     * 获取上传视频临时凭证
     */
    public StorageCredentials getStorageCredentials(Set<PirServiceName> supportServiceNames, Integer adminId, String sn) {
        final Set<PirServiceName> serviceNames = supportServiceNames.stream()
                .filter(this::isEnableServiceName).collect(Collectors.toSet()); //
//        serviceNames.add(getServiceName(config.getVipRatios(), supportServiceNames, adminId));
//        serviceNames.add(getServiceName(config.getNoVipRatios(), supportServiceNames, adminId));
        final StorageCredentials result = new StorageCredentials();
        if (serviceNames.contains(gcs)) {
            result.setGcsCredentials(gcsService.createTempCredentials(sn));
        }
        if (serviceNames.contains(cos)) {
            result.setCosCredentials(cosService.createTempCredential(sn));
        }
        if (serviceNames.contains(oci)) {
            result.setOciCredentials(ociService.createTempCredential(sn, adminId));
        }
        return result;
    }

    /**
     * 解析切片url，给算法端提供保存生成图片的参数
     *
     * @param defaultOutStorage 原始的对象，来自配置文件中，大部分参数不用都动。只需要复制后修改 serviceName，bucket，region
     * @param storeBucket       设备上传切片和图片使用的存储桶
     * @return
     */
    public SaasAITaskIM.OutStorage createOutStorage(SaasAITaskIM.OutStorage defaultOutStorage, StoreBucket storeBucket) {
        final SaasAITaskIM.OutStorage outStorage = BeanUtil.copy(defaultOutStorage, new SaasAITaskIM.OutStorage());
        final String serviceName = storeBucket != null ? storeBucket.getServiceName().name() : null;
        if (config.getSaasAiSupportServiceNames().contains(serviceName)) {
            return outStorage.setServiceName(storeBucket.getServiceName().name())
                    .setBucket(storeBucket.getBucket()).setClientRegion(storeBucket.getRegion());
        }
        final StoreBucket s3Bucket;
        if (cos.name().equals(serviceName)) {
            // vip使用cos的过渡期，切片放在s3-bucket，算法生成图片放在相同回看天数的cos的bucket中
            s3Bucket = FuncUtil.getKeyByValue(cosConfig.getLookBackDays2Bucket(), storeBucket.getBucket())
                    .findFirst().map(it -> s3.getBucketByLookBackDays(it, -1)).orElse(s3.getMaxLookBackDaysBucket(-1));
        } else if (PirServiceName.oci.name().equals(serviceName)) {
            // vip使用oci的过渡期，切片放在s3-bucket，算法生成图片放在相同回看天数的oci的bucket中
            s3Bucket = FuncUtil.findKeyByProperty(ociConfig.getLookBackDays2Buckets(), "bucket", storeBucket.getBucket()).findFirst()
                    .map(it -> s3.getBucketByLookBackDays(it, -1)).orElse(s3.getMaxLookBackDaysBucket(-1));
        } else {  // s3-最大回看时间的bucket兜底
            s3Bucket = s3.getMaxLookBackDaysBucket(-1);
        }
        return outStorage.setServiceName(PirServiceName.s3.name()).setBucket(s3Bucket.getBucket()).setClientRegion(s3Bucket.getRegion() != null ? s3Bucket.getRegion() : s3.getClientRegion());
    }

    /**
     * 从切片url中获取存储桶
     */
    public StoreBucket getStoreBucketFromUrl(String sliceUrl) {
        if (StringUtils.isBlank(sliceUrl)) return null;
        final CxsTsFileInfo cxsTsFileInfo = CxsTsFileInfo.parse(sliceUrl);
        if (cxsTsFileInfo != null) {
            return new StoreBucket().setServiceName(PirServiceName.cxs).setBucket("").setRegion("");
        }
        final MyAwsUtil.S3Object s3Obj = MyAwsUtil.parseS3Url(sliceUrl);
        if (s3Obj != null) { // 切片在s3-bucket，算法生成图片放在同一个bucket中
            return new StoreBucket().setServiceName(PirServiceName.s3)
                    .setBucket(s3Obj.getBucket()).setRegion(s3.getClientRegion()); // 用了s3加速的话就url中就没有region
        }
        final CosService.CosUrlParts cosObj = CosService.parseCosUrl(sliceUrl);
        if (cosObj != null) {
            return new StoreBucket().setServiceName(cos)
                    .setBucket(cosObj.getBucket()).setRegion(cosObj.getRegion());
        }
        final BlobInfo gcsObj = GoogleStorageService.parseGcsUrl(sliceUrl);
        if (gcsObj != null) {
            return new StoreBucket().setServiceName(gcs)
                    .setBucket(gcsObj.getBucket()).setRegion(""); // gcs没有region的概念
        }
        final OciService.OciUrlParts ociObj = OciService.parseOciUrl(sliceUrl);
        if (ociObj != null) {
            return new StoreBucket().setServiceName(PirServiceName.oci)
                    .setBucket(ociObj.getBucket()).setRegion(ociObj.getRegion());
        }
        return null;
    }

    public static String getObjectKeyFromUrl(String sliceUrl) {
        if (StringUtils.isBlank(sliceUrl)) return null;
        final CxsTsFileInfo cxsTsFileInfo = CxsTsFileInfo.parse(sliceUrl);
        if (cxsTsFileInfo != null) {
            return cxsTsFileInfo.getFileName();
        }
        final MyAwsUtil.S3Object s3Obj = MyAwsUtil.parseS3Url(sliceUrl);
        if (s3Obj != null) { // 切片在s3-bucket，算法生成图片放在同一个bucket中
            return s3Obj.getKey();
        }
        final CosService.CosUrlParts cosObj = CosService.parseCosUrl(sliceUrl);
        if (cosObj != null) {
            return cosObj.getKey();
        }
        final BlobInfo gcsObj = GoogleStorageService.parseGcsUrl(sliceUrl);
        if (gcsObj != null) {
            return gcsObj.getName();
        }
        final OciService.OciUrlParts ociObj = OciService.parseOciUrl(sliceUrl);
        if (ociObj != null) {
            return ociObj.getKey();
        }
        return null;
    }

}
