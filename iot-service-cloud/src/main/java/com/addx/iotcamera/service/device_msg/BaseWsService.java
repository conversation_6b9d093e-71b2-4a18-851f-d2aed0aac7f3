package com.addx.iotcamera.service.device_msg;


import com.addx.iotcamera.bean.device_msg.KissDeviceNode;
import com.addx.iotcamera.bean.exception.ResultException;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.kiss.bean.KissApiResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseWsService {

    public abstract KissDeviceNodeManager getSn2KissDeviceNode();

    public abstract KissWsClientManager getIp2KissWsClient();

    // 发送设备消息
    public KissApiResult sendDeviceMsg(String sn, String msg) {
        final KissDeviceNode kissDeviceNode = getSn2KissDeviceNode().get(sn);
        try {
            if (kissDeviceNode == null) {
                throw new ResultException().setCode(-200).setMsg("找不到设备所在的kiss节点!");
            }
//            if (kissDeviceNode.getDeviceStatus() == KissDeviceStatus.offline) {
//                throw new ResultException().setCode(-200).setMsg("设备已离线!");
//            }
            final KissWsClient wsClient = getIp2KissWsClient().get(kissDeviceNode.getKissIp());
            if (wsClient == null || !wsClient.isOpen()) {
                throw new ResultException().setCode(-200).setMsg("无法访问设备所在的kiss节点!");
            }
            wsClient.send(msg);
            log.info("sendDeviceMsg end! sn={},kissDeviceNode={},msg={}", sn, JSON.toJSONString(kissDeviceNode), msg);
            return new KissApiResult().setCode(0);
        } catch (ResultException e) {
            log.info("sendDeviceMsg fail! sn={},kissDeviceNode={},msg={},failCode={},failMsg={}", sn, JSON.toJSONString(kissDeviceNode), msg, e.getCode(), e.getMsg());
            return new KissApiResult().setCode(e.getCode()).setMsg(e.getMsg());
        } catch (Throwable e) {
            log.error("sendDeviceMsg error! sn={},kissDeviceNode={},msg={}", sn, JSON.toJSONString(kissDeviceNode), msg, e);
            return new KissApiResult().setCode(-500).setMsg("访问设备所在的kiss节点发生异常!");
        }
    }

    public boolean closeGroupByUserId(Integer userId) {
        final JSONObject msg = new JSONObject().fluentPut("method", "CLOSE_GROUP_BY_USER_ID")
                .fluentPut("userId", userId).fluentPut("requestId", MDC.get(MDCKeys.REQUEST_ID));
        int sendErrorNum = 0;
        for (KissWsClient client : getIp2KissWsClient().values()) {
            try {
                if (!client.isOpen()) continue;
                client.send(JSON.toJSONString(msg));
            } catch (Throwable e) {
                sendErrorNum++;
                log.error("sendDeviceMsgToAll error! kissUri={},msg={}", client.getURI(), msg, e);
            }
        }
        return sendErrorNum == 0;
    }

    public List<KissWsClient> getKissWsClientByKissIp(List<String> kissIpList) {
        log.info("KissWsService getKissWsClientByKissIp connected kissIp {} user kissIp {}",
                JSON.toJSONString(getIp2KissWsClient().keySet()), JSON.toJSONString(kissIpList));
        return kissIpList.stream().map(getIp2KissWsClient()::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
