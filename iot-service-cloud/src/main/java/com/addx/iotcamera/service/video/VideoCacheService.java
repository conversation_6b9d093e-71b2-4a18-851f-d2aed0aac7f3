package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.db.*;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.reid.UserReId;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.dao.ActivityZoneDAO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.UserMessageMergeSwitchEnums;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.service.device.DoorbellService.getNotifyMsgType;
import static com.addx.iotcamera.service.device.DoorbellService.getNotifySwitch;
import static com.addx.iotcamera.service.video.DeviceCache.VIDEO_COMMON_CACHE_EXPIRATION_MILLIS;
import static com.addx.iotcamera.service.video.VideoCache.Flag.*;

/**
 * 构造vidoeCache对象
 */
@Slf4j(topic = "videoGenerate")
@Component
public class VideoCacheService {

    @Autowired
    @Lazy
    private VideoStoreService videoStoreService;
    @Autowired
    private AIService aiService;
    @Autowired
    private UserTierDeviceService userTierDeviceService;
    @Autowired
    private DeviceConfigService deviceConfigService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserService userService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceManualService deviceManualService;
    @Autowired
    private DeviceSettingService deviceSettingService;
    @Autowired
    @Lazy
    private TenantSettingService tenantSettingService;
    @Autowired
    private VipService vipService;
    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;
    @Autowired
    private ThingModelConfig thingModelConfig;
    @Autowired
    private VideoAIService videoAIService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private VideoEventRedisService videoEventRedisService;
    @Autowired
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Autowired
    private PaasTenantConfig paasTenantConfig;
    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private ReIdService reIdService;
    @Autowired
    private PushInfoService pushInfoService;
    @Autowired
    private ActivityZoneDAO activityZoneDAO;
    @Autowired
    private TimeTicker timeTicker;
    @Autowired
    @Lazy
    private StorageAllocateService storageAllocateService;

    /**
     * 初始化视频缓存
     * 1.只在创建视频时调用一次
     *
     * @return
     */
    public boolean initVideoCache(VideoCache video, VideoCommonCache commonCache) {
        if (video.getFlag(INIT_SUCCESS)) return true; // 已初始化
        if (!initVideoCommonCache(commonCache)) return false; // 初始化公共缓存失败
        final VideoCache.ExeStep initVideoCacheStep = video.loggingStep("initVideoCache");
        try {
            BeanUtils.copyProperties(commonCache, video, VideoCommonCache.class); // 公共缓存复制到视频缓存
            video.setVideoUrl(video.getIotEndpoint() + PATH_VIDEO_DOWNLOAD_M3U8 + "/" + video.getTraceId() + POSTFIX_M3U8);
//            video.setVideoEvent(aiService.getVideoEventKey(video.getSerialNumber(), video.getTraceId())); // 视频创建时生成videoEvent，createTime
            video.setFlag(INIT_SUCCESS); // 初始化成功
            return true;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache error! video={}", JSON.toJSONString(video), e);
            return false;
        } finally {
            initVideoCacheStep.exeEnd();
        }
    }

    private enum VideoCacheInitResult {
        RESULT, NOT_DEVICE_INFO, NOT_USER_ROLE, NOT_ADMIN, NOT_DEVICE_SETTING
    }

    public boolean initVideoCommonCache(VideoCommonCache video) {
        long currentMillis = timeTicker.readMillis();
        if (currentMillis - video.getInitTime() < VIDEO_COMMON_CACHE_EXPIRATION_MILLIS) {
            return true; // 缓存未过期
        }
        final long beginTime = System.currentTimeMillis();
        final BitSet result = new BitSet(VideoCacheInitResult.values().length);
        try {
            DeviceDO deviceDO = deviceService.getAllDeviceInfo(video.getSerialNumber());
            if (deviceDO == null) {
                com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache fail! 找不到设备信息! video={}", JSON.toJSONString(video));
                result.set(VideoCacheInitResult.NOT_DEVICE_INFO.ordinal());
                return false;
            }
            video.setDeviceName(deviceDO.getDeviceName());
            video.setDeviceModelNo(Optional.ofNullable(deviceManualService.getModelNoBySerialNumber(video.getSerialNumber())).orElse(""));
            List<Integer> userIds = userRoleService.findAllUsersForDevice(video.getSerialNumber());
            if (userIds.isEmpty()) {
                com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache fail! 设备未绑定! video={}", JSON.toJSONString(video));
                result.set(VideoCacheInitResult.NOT_USER_ROLE.ordinal());
                return false;
            }
            final List<User> users = userIds.stream().map(userService::queryUserById).collect(Collectors.toList());
            video.setUsers(users.stream().map(VideoCommonCache.UserSimple::fromUserDO).collect(Collectors.toList()));
            User adminDO = users.get(0);
            if (adminDO == null) {
                com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache fail! 找不到管理员信息! video={}", JSON.toJSONString(video));
                result.set(VideoCacheInitResult.NOT_ADMIN.ordinal());
                return false;
            }
            video.getUsers().removeIf(Objects::isNull); // 分享用户为null时直接忽略，不阻碍流程
            video.setAdminId(adminDO.getId());
            video.setTenantId(adminDO.getTenantId());
            video.setCountryNo(adminDO.getCountryNo());
            final TenantSetting tenantSetting = tenantSettingService.getTenantSettingByTenantId(adminDO.getTenantId());
            video.setIotEndpoint(tenantSetting.getIotEndpoint());
            // 通知设置
            DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(video.getSerialNumber());
            if (deviceSettingsDO == null) {
                com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache fail! 找不到设备用户设置! video={}", JSON.toJSONString(video));
                result.set(VideoCacheInitResult.NOT_DEVICE_SETTING.ordinal());
                return false;
            }

            video.setIsEnableSmartAlertPro(deviceSettingsDO.getPropertyJson() != null && Long.valueOf(1).equals(deviceSettingsDO.getPropertyJson().getLong("enableSmartAlertProSwitch")));
            video.setDoorbellPressNotifySwitch(getNotifySwitch(deviceSettingsDO));
            video.setDoorbellPressNotifyMsgType(getNotifyMsgType(deviceSettingsDO));
            // 查询设备所属tierGroup信息
            Tuple2<Integer, Integer> deviceUseCurrentTierTuple = userTierDeviceService.getDeviceCurrentTierWithTierGroupId(video.getAdminId(), video.getSerialNumber());
            video.setTierId(deviceUseCurrentTierTuple != null ? deviceUseCurrentTierTuple.v0() : null);
            video.setTierGroupId(deviceUseCurrentTierTuple != null ? deviceUseCurrentTierTuple.v1() : null);
            Tuple2<Integer, Long> storageDaysTuple = deviceConfigService.getVideoRollingDaysOrNoPlan(video.getTenantId(), video.getAdminId(), video.getSerialNumber());
            video.clearRollingDays();
            video.setRollingDays(storageDaysTuple.v0());
            video.setVipExpireTime(storageDaysTuple.v1());
            video.setIsCheckActivityZone(true); // 检查az
            video.setIsCheckNotifySwitch(true); // 检查通知开关
            //            video.setDeviceSupport(deviceInfoService.getDeviceSupport(video.getSerialNumber()));
            video.setIsVip(vipService.isVipDevice(video.getAdminId(), video.getSerialNumber()));
            video.setIsBirdVip(vipService.isBirdVipDevice(video.getAdminId(), video.getSerialNumber()));
            CloudDeviceSupport deviceSupport = deviceInfoService.getDeviceSupport(video.getSerialNumber());
            final ThingModel thingModel = thingModelConfig.getThingModelByModelNo(video.getDeviceModelNo());
            video.setIsStoreCameraLocal(DeviceSettingsDO.isStoreCameraLocal(thingModel, deviceSupport, deviceSettingsDO));
            video.setHasAiAbility(video.getIsVip() || video.getIsStoreCameraLocal());
            video.setIsNotify(!deviceDO.getPushIgnored());
            final MessageNotificationSetting notifySetting = messageNotificationSettingsService.queryMessageNotificationSetting(video.getSerialNumber(), video.getAdminId());
            video.setNotifyEventObjects(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getEventObjects).orElse(null), ','));
            video.setNotifyEventTypes(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getPackageEventType).orElse(null), ','));
            if(AppConstants.TENANTID_SAFEMO.equals(video.getTenantId())) {
                Set<String> eventTypeSet  =  new HashSet<>();
                if(video.getNotifyEventObjects().contains(AiObjectEnum.PACKAGE.getObjectName())){
                    eventTypeSet.addAll(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getPackageEventType).orElse(null), ','));
                }
                if(video.getNotifyEventObjects().contains(AiObjectEnum.PERSON.getObjectName())) {
                    eventTypeSet.addAll(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getPersonEventType).orElse(null), ','));
                }
                if(video.getNotifyEventObjects().contains(AiObjectEnum.VEHICLE.getObjectName())) {
                    eventTypeSet.addAll(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getVehicleEventType).orElse(null), ','));
                }
                if(video.getNotifyEventObjects().contains(AiObjectEnum.PET.getObjectName())) {
                    eventTypeSet.addAll(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getPetEventType).orElse(null), ','));
                }
                if(video.getNotifyEventObjects().contains(AiObjectEnum.FACE.getObjectName())) {
                    eventTypeSet.addAll(TextUtil.splitToNotBlankSet(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getFaceEventType).orElse(null), ','));
                }
                video.setNotifyEventTypes(eventTypeSet);
            }
            video.setEnableOther(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getEnableOther).map(it -> it == 1).orElse(true));
            video.setEnableLabelIdKnown(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getEnableVehicleKnown).map(it -> it == 1).orElse(true));
            video.setEnableLabelIdUnknown(Optional.ofNullable(notifySetting).map(MessageNotificationSetting::getEnableVehicleUnknown).map(it -> it == 1).orElse(true));
            video.setKnownLabelIds(reIdService.queryMarkedReId(video.getAdminId() + "").stream().map(UserReId::getLabelId).collect(Collectors.toSet()));
            final Collection<String> extraAiDetectEvents = new LinkedHashSet<>();
            if (video.getIsBirdVip() && paasTenantConfig.getPaasTenantInfo(video.getTenantId()).getBirdDetectSwitchOn()) {
                extraAiDetectEvents.add(AiObjectEnum.BIRD.getName()); // 部分tenantId固定打开鸟类分析开关
                video.getNotifyEventObjects().add(AiObjectEnum.BIRD.getName()); // 部分tenantId固定打开鸟类通知开关
            }
            video.setDefaultSaasAITask(videoAIService.createDefaultSaasAITask(video, extraAiDetectEvents));
            final String deviceIsWowzaKey = DeviceInfoConstants.DEVICE_IS_WOWZA.replace("{serialNumber}", video.getSerialNumber());
            video.setIsWowza("1".equals(redisService.get(deviceIsWowzaKey)));
            UserSettingsDO userSetting = userSettingService.queryUserSetting(video.getAdminId());
            video.setIsMergeMessage(Objects.equals(Optional.ofNullable(userSetting).map(UserSettingsDO::getMessageMergeSwitch)
                    .orElse(UserMessageMergeSwitchEnums.NOWAKE.getCode()), UserMessageMergeSwitchEnums.WAKE.getCode()));
            log.info("initVideoCommonCache end! video={}", JSON.toJSONString(video));
            video.setInitTime(currentMillis); // 必须设置初始化时间
            result.set(VideoCacheInitResult.RESULT.ordinal());
            return true;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "initVideoCommonCache error! video={}", JSON.toJSONString(video), e);
            return false;
        } finally {
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoCacheInitCostTimeHistogram()
                    .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), Arrays.stream(result.toLongArray()).mapToObj(i -> i + "").findFirst().orElse("")).observe(System.currentTimeMillis() - beginTime)));
        }
    }

    private enum VideoCacheResultResult {
        RESULT, QUERY_VIDEO, QUERY_SLICE
    }

    /**
     * 恢复视频数据
     * 视频所在实例可能重启
     *
     * @return
     */
    public boolean resumeVideoData(VideoCache video) {
        if (video.getFlag(RESUME_SUCCESS)) return true; // 已恢复
        if (!video.getFlag(INIT_SUCCESS)) return false; // 未初始化成功，无法恢复
        final VideoCache.ExeStep resumeVideoDataStep = video.loggingStep("resumeVideoData");
        final long beginTime = System.currentTimeMillis();
        final BitSet result = new BitSet(VideoCacheResultResult.values().length); // 最终结果，是否查询视频表，是否查询切片表
        try {
            DeviceLibraryViewDO library = videoStoreService.selectLibraryViewByAdminIdAndTraceId(video.getAdminId(), video.getTraceId());
            result.set(VideoCacheResultResult.QUERY_VIDEO.ordinal());

            if (library == null) {
                video.setFlag(RESUME_SUCCESS); // 已恢复，视频未创建
                return true; // 没找到现成的视频表数据，是正常情况
            }
//            video.setTimestamp(library.getTimestamp());
            video.setFlag(VideoCache.Flag.LIBRARY_CREATED); // 恢复视频时发现视频已创建
            video.setServiceName(library.getServiceName());
            video.setCodec(library.getCodec());
            video.setSummaryDescription(library.getSummaryDescription());
            video.setVideoType(library.getVideoType());
            video.setMainTraceId(library.getMainTraceId());
            video.setVideoEvent(getVideoEventByTraceId(video));
            setImageUrlFromLibrary(video, library.getImageUrl()); // 恢复封面图
            if (JSON.isValidArray(library.getEventInfo())) {
                List<AiEvent> events = JSON.parseArray(library.getEventInfo(), AiEvent.class);
                video.getEvents().addAll(0, events); // 数据库里查出的放在最前面
                events.forEach(event -> video.addActivatedZoneId(event.getActivatedZones()));
            }
            if (JSON.isValidArray(library.getAiEdgeEventInfo())) {
                List<AiEvent> events = JSON.parseArray(library.getAiEdgeEventInfo(), AiEvent.class);
                video.getAiEdgeEvents().addAll(0, events); // 数据库里查出的放在最前面
            }
            if (JSON.isValidArray(library.getDoorbellEventInfo())) {
                List<VideoReportEvent> reportEvents = JSON.parseArray(library.getDoorbellEventInfo(), VideoReportEvent.class);
                video.getDoorbellEvents().addAll(0, reportEvents); // 数据库里查出的放在最前面
            }
            if (StringUtils.isNotBlank(library.getDeviceCallEventTag())) {
                List<VideoReportEvent> reportEvents = TextUtil.splitToNotBlankStream(library.getDeviceCallEventTag(), ',')
                        .map(eventName -> new VideoReportEvent(EReportEvent.valueOf(eventName))).collect(Collectors.toList());
                video.getDeviceCallEvents().addAll(0, reportEvents); // 数据库里查出的放在最前面
            }
            {
                List<VideoSliceDO> sliceList = videoStoreService.querySliceByAdminUserIdAndTraceId(video.getAdminId(), video.getTraceId());
                if (sliceList.isEmpty()) {
                    log.warn("resumeVideoCache slice empty! video={}", JSON.toJSONString(video));
                }
                result.set(VideoCacheResultResult.QUERY_SLICE.ordinal());
                sliceList.forEach(video::addSlice);
            }
            video.setFlag(RESUME_SUCCESS); // 已恢复，视频已创建
            log.info("resumeVideoCache end! video={}", JSON.toJSONString(video));
            result.set(VideoCacheResultResult.RESULT.ordinal());
            return true;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "resumeVideoCache error! video={}", JSON.toJSONString(video), e);
            return false;
        } finally {
            resumeVideoDataStep.exeEnd();
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoCacheResumeCostTimeHistogram()
                    .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), result.toLongArray()[0] + "").observe(System.currentTimeMillis() - beginTime)));
        }
    }

    public long getMinSliceTimestamp(VideoCache video) {
        return video.getOrder2Slice().values().stream().map(VideoCache.SliceCache::getS3EventTime)
                .reduce(Long::min).orElseGet(timeTicker::readMillis);
    }

    public long getMaxSliceEndTimestamp(VideoCache video) {
        return video.getOrder2Slice().values().stream().map(VideoCache.SliceCache::getEndUtcTimestampMillis)
                .reduce(Long::max).orElseGet(timeTicker::readMillis);
    }

    public String getSnapshotVideoEvent(VideoCache video) {
        return getMinSliceTimestamp(video) + "";
    }

    // floor(min(slice.s3_event_time)) 向下取整，确保相册筛选时不会遗漏
    public int getVideoTimestamp(VideoCache video) {
        return new BigDecimal(getMinSliceTimestamp(video)).divide(new BigDecimal(1000), 0, RoundingMode.DOWN).intValue();
    }

    // ceil(max(slice.end_utc_timestamp_millis)) 向上取整，确保相册筛选时不会遗漏
    public int getVideoEndTimestamp(VideoCache video) {
        return new BigDecimal(getMaxSliceEndTimestamp(video)).divide(new BigDecimal(1000), 0, RoundingMode.UP).intValue();
    }

    private String getVideoEventByTraceId(VideoCache video) {
        if (video.isSnapshotRecording()) {
            return getSnapshotVideoEvent(video); // 不跟事件录像聚合
        }
        String traceIdKey = LIBRARY_TRACE_TO_VIDEO_EVENT.replace("{traceId}", video.getVideoEventTraceId());
        return videoEventRedisService.get(traceIdKey);
    }

    public String getVideoEvent(VideoCache video) {
        if (video.getVideoEvent() == null) {
            if (video.isSnapshotRecording()) {
                video.setVideoEvent(getSnapshotVideoEvent(video)); // 不跟事件录像聚合
            } else {
                video.setVideoEvent(aiService.getVideoEventKey(video.getSerialNumber(), video.getVideoEventTraceId(), getMinSliceTimestamp(video)));
            }
        }
        return video.getVideoEvent();
    }

    public void updateVideoEventLastTime(VideoCache video) {
        if (video.getVideoEvent() == null || video.isSnapshotRecording()) return;
        try {
            aiService.updateVideoEventLastTime(video.getSerialNumber(), video.getTraceId(), getMaxSliceEndTimestamp(video), video.getVideoEvent());
        } catch (Throwable e) {
            log.error("updateVideoEventLastTime error! video={}", JSON.toJSONString(video), e);
        }
    }

    /*
    private static DeviceReportEventDO createReportEvent(String traceId, Integer event) {
        DeviceReportEventDO reportEvent = new DeviceReportEventDO();
        reportEvent.setValue(reportEvent.new ReportEventRequestValue());
        reportEvent.getValue().setEvent(event);
        reportEvent.getValue().setTraceId(traceId);
        return reportEvent;
    }
    */

    public List<String> getActivityZoneNames(VideoCache video, List<AiEvent> events) {
        List<String> list = new LinkedList<>();
        if (CollectionUtils.isEmpty(events)) return list;
        for (AiEvent event : events) {
            if (CollectionUtils.isEmpty(event.getActivatedZones())) continue;
            for (Integer azId : event.getActivatedZones()) {
                String name = video.getActivityZoneId2Name().get(azId);
                if (name == null) {
                    ActivityZoneDO activityZone = activityZoneDAO.getActivityZonesById(azId);
                    if (activityZone == null) continue;
                    name = activityZone.getZoneName();
                    video.getActivityZoneId2Name().put(azId, name);
                }
                list.add(name);
            }
        }
        return list;
    }

    public PushInfo getPushInfo(VideoCache video, Integer userId) {
        return video.getUserId2PushInfo().computeIfAbsent(userId, pushInfoService::getPushInfo);
    }

    public boolean setImageUrlFromDevice(VideoCache video, String imageUrl, Integer videoType) {
        if (StringUtils.isNotBlank(imageUrl) && StringUtils.isBlank(video.getImageUrl())) { // 现有封面图为空才用设备上传封面图
            video.setImageUrl(imageUrl);
            extractStoreBucket(video, imageUrl);
            if (VideoTypeEnum.codeOf(videoType).isUpdateLastImage()) {
                final VideoCache.ExeStep saveLibraryVideoImageStep = video.loggingStep("saveLibraryVideoImage");
                aiService.saveLibraryVideoImage(video.getAdminId(), video.getSerialNumber(), imageUrl, videoType); // 更新设备最新封面图
                saveLibraryVideoImageStep.exeEnd();
            }
            return true;
        }
        return false;
    }

    public boolean setImageUrlFromAI(VideoCache video, String imageUrl) {
        if (StringUtils.isNotBlank(imageUrl)) { // 优先使用算法封面图
            video.setImageUrl(imageUrl);
            return true;
        }
        return false;
    }

    public void setImageUrlFromLibrary(VideoCache video, String imageUrl) {
        if (StringUtils.isNotBlank(imageUrl) && StringUtils.isBlank(video.getImageUrl())) { // 现有封面图为空才用设备上传封面图
            video.setImageUrl(imageUrl);
            extractStoreBucket(video, imageUrl);
        }
    }

    // 视频是否需要推送
    public static boolean getIsNotify(boolean hasAiAbility, boolean pushIgnored) {
        return hasAiAbility || !pushIgnored;
    }

    /**
     * 提取url中的bucket。
     * 同一个视频的图片和切片应该是相同的bucket。
     */
    public void extractStoreBucket(VideoCache video, String url) {
        final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(url);
        if (storeBucket == null) return;
        if (video.getStoreBucket() == null) {
            video.setStoreBucket(storeBucket);
        } else if (!video.getStoreBucket().equals(storeBucket)) {
            com.addx.iotcamera.util.LogUtil.error(log, "extractStoreBucket 同一个视频的用了多个存储桶! {},storeBucket={},url={}", video.toSimpleDesc(), video.getStoreBucket(), url);
        }
    }

    public void saveSnapshotImageUrlToRedis(VideoImageDO image) {
        try {
            if (image == null || StringUtils.isBlank(image.getSerialNumber()) || StringUtils.isBlank(image.getTraceId())
                    || !VideoTypeEnum.SNAPSHOT_RECORDING.getCode().equals(image.getVideoType())
                    || StringUtils.isBlank(image.getImageUrl())) {
                return;
            }
            final String snapshotImageUrlKey = DeviceInfoConstants.REDIS_KEY_SNAPSHOT_IMAGE_URL.replace("{sn}", image.getSerialNumber()).replace("{traceId}", image.getTraceId());
            redisService.set(snapshotImageUrlKey, image.getImageUrl(), DeviceInfoConstants.REDIS_TIMEOUT_SNAPSHOT_IMAGE_URL);
            log.debug("saveSnapshotImageUrlToRedis end! key={},value={}", snapshotImageUrlKey, image.getImageUrl());
        } catch (Throwable e) {
            log.error("saveSnapshotImageUrlToRedis error! image={}", JSON.toJSONString(image), e);
        }
    }

    public void getSnapshotImageUrlFromRedis(VideoCache video) {
        try {
            if (video == null || StringUtils.isBlank(video.getSerialNumber()) || StringUtils.isBlank(video.getTraceId())
                    || !VideoTypeEnum.SNAPSHOT_RECORDING.getCode().equals(video.getVideoType())
                    || StringUtils.isNotBlank(video.getImageUrl())) {
                return;
            }
            final String snapshotImageUrlKey = DeviceInfoConstants.REDIS_KEY_SNAPSHOT_IMAGE_URL.replace("{sn}", video.getSerialNumber()).replace("{traceId}", video.getTraceId());
            final String snapshotImageUrl = redisService.get(snapshotImageUrlKey);
            setImageUrlFromDevice(video, snapshotImageUrl, video.getVideoType());
            log.debug("getSnapshotImageUrlFromRedis end! key={},value={}", snapshotImageUrlKey, snapshotImageUrl);
        } catch (Throwable e) {
            log.error("getSnapshotImageUrlFromRedis error! video={}", video, e);
        }
    }

    public void clearSnapshotImageUrlOnRedis(VideoCache video) {
        try {
            if (video == null || StringUtils.isBlank(video.getSerialNumber()) || StringUtils.isBlank(video.getTraceId())
                    || !VideoTypeEnum.SNAPSHOT_RECORDING.getCode().equals(video.getVideoType())
                    || StringUtils.isBlank(video.getImageUrl()) || video.getFlag(IMAGE_URL_SAVED)) {
                return;
            }
            final String snapshotImageUrlKey = DeviceInfoConstants.REDIS_KEY_SNAPSHOT_IMAGE_URL.replace("{sn}", video.getSerialNumber()).replace("{traceId}", video.getTraceId());
            redisService.delete(snapshotImageUrlKey);
            video.setFlag(IMAGE_URL_SAVED);
            log.debug("clearSnapshotImageUrlOnRedis end! key={}", snapshotImageUrlKey);
        } catch (Throwable e) {
            log.error("clearSnapshotImageUrlOnRedis error! video={}", video, e);
        }
    }

}
