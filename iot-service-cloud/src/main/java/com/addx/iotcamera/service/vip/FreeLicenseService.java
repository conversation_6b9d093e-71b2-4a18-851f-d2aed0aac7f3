package com.addx.iotcamera.service.vip;

import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.vip.FreeLicenseRedeemRequest;
import com.addx.iotcamera.bean.app.vip.FreeLicenseRequest;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.db.user.UserShowFreeLicenseDO;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.addx.iotcamera.bean.response.vip.FreeLicenseResponse;
import com.addx.iotcamera.dao.factory.AppModelReleaseConfigDAO;
import com.addx.iotcamera.dao.factory.DeviceBindInfoDao;
import com.addx.iotcamera.dao.factory.DeviceSupportFreeTierDAO;
import com.addx.iotcamera.dao.vip.IUserDeviceFreeTierDao;
import com.addx.iotcamera.dao.vip.IUserFreeLicenseNoThanksDAO;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelIconService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.user.UserShowFreeLicenseService;
import com.addx.iotcamera.util.DateUtils;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_1_DAY;
import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_7_DAY;
import static com.addx.iotcamera.constants.UserConstants.*;
import static com.addx.iotcamera.enums.user.ShareMessageTypeEnum.USER_TIER_DEVICE;
import static org.addx.iot.common.constant.AppConstants.TENANTID_KIWIBIT;


@Service
@Slf4j
public class FreeLicenseService {

    @Resource
    private UserShowFreeLicenseService userShowFreeLicenseService;

    @Autowired
    private IUserDeviceFreeTierDao userDeviceFreeTierDao;

    @Resource
    @Lazy
    private UserService userService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceBindInfoDao deviceBindInfoDao;


    @Value("${spring.kafka.topics.user-tier-device-xxl}")
    private String userTierDeviceTopic;



    @Resource
    private MqSender mqSender;

    @Resource
    private RedisService redisService;

    @Autowired
    private IUserFreeLicenseNoThanksDAO userFreeLicenseNoThanksDAO;
    @Autowired
    private DeviceModelIconService deviceModelIconService;

    @Autowired
    private FactoryDataQueryService factoryDataQueryService;

    private static final int DEFAULT_FREE_TIER_ID = 1003;

    @Autowired
    private VipService vipService;
    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Autowired
    private TierService tierService;
    @Autowired
    private Device4GService device4GService;

    @Resource
    private DeviceModelConfigService deviceModelConfigService;

    @Resource
    private UserSettingService userSettingService;


    public FreeLicenseResponse getFreeLicenseData(Integer userId, List<String> deviceList) {
        List<FreeLicenseResponse.FreeLicenseDeviceList> deviceInfos = new ArrayList<>();

        // 如果 deviceList 为空，则获取用户所有设备freeLicense
        if (CollectionUtils.isEmpty(deviceList)) {
            deviceList = userRoleService.getUserSerialNumberByUserId(userId);
        }

        Integer maxFreeTier = getEligibleFreeTierId(userId, "");
        deviceList.stream()
                .filter(serialNumber -> userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(userId, serialNumber) == null
                && !vipService.isVipDevice(userId, serialNumber) && device4GService.queryDevice4GSimDO(serialNumber) == null)
                .forEach(serialNumber -> {

                    // 构建 FreeLicenseDeviceList 对象
                    Optional<String> userSnBySerialNumber = Optional.ofNullable(deviceBindInfoDao.findUserSnBySerialNumber(serialNumber));
                    Optional<String> modelNo = userSnBySerialNumber.flatMap(sn -> Optional.ofNullable(deviceBindInfoDao.findModelNoByUserSn(sn)));
                    Optional<DeviceDO> device = Optional.ofNullable(deviceService.getAllDeviceInfo(serialNumber));
                    Optional<DeviceModelIconDO> deviceModelIconDO = modelNo.flatMap(no -> Optional.ofNullable(deviceModelIconService.queryDeviceModelIcon(no)));

                    FreeLicenseResponse.FreeLicenseDeviceList responseDevice = FreeLicenseResponse.FreeLicenseDeviceList.builder()
                            .userSn(userSnBySerialNumber.orElse(null))
                            .serialNumber(serialNumber)
                            .icon(deviceModelIconDO.map(DeviceModelIconDO::getIconUrl).orElse(null))
                            .deviceName(device.map(DeviceDO::getDeviceName).orElse(null))
                            .build();

                    deviceInfos.add(responseDevice);
                });

        // 统一设置所有设备的 freeLicenseId
        deviceInfos.forEach(responseDevice -> responseDevice.setFreeLicenseId(maxFreeTier));

        return FreeLicenseResponse.builder().deviceList(deviceInfos).build();
    }

    /**
     * 获取用户应该领取free license
     * @param userId
     * @param sn
     * @return
     */
    public Integer getEligibleFreeTierId(Integer userId, String sn) {
        // 先根据历史记录查询应领取free license
        UserShowFreeLicenseDO userShowFreeLicenseDO = userShowFreeLicenseService.queryUserShowFreeLicenseByUserId(userId);

        FreeLicenseResponse.AutoRedeemFreeLicense redeemFreeLicense = this.queryFreeLicenseByHistoryCord(userId,userShowFreeLicenseDO);
        if(redeemFreeLicense != null){
            return redeemFreeLicense.getFreeLicenseId();
        }


        List<String> deviceList = userRoleService.getUserSerialNumberByUserId(userId);
        AtomicReference<Integer> maxFreeTierId = new AtomicReference<>(null);

        deviceList.stream()
                .filter(serialNumber -> userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(userId, serialNumber) == null
                && device4GService.queryDevice4GSimDO(serialNumber) == null)
                .forEach(serialNumber -> {

                    FreeLicenseResponse.AutoRedeemFreeLicense freeLicenseDevice = this.queryFreeLicenseByDevice(userId,serialNumber,userShowFreeLicenseDO);
                    Integer currentFreeTierId = freeLicenseDevice.getFreeLicenseId();

                    // 更新最大 free_tier_id
                    if (currentFreeTierId != null && (maxFreeTierId.get() == null || currentFreeTierId > maxFreeTierId.get())) {
                        maxFreeTierId.set(currentFreeTierId);
                    }
                });
        return maxFreeTierId.get();
    }

    /**
     * 获取厂测中配置的free license 规则
     * @param serialNumber
     * @return
     */
    public Integer getFreeTierId(String serialNumber) {

        // 获取设备的基本信息
        Optional<String> userSn = Optional.ofNullable(deviceBindInfoDao.findUserSnBySerialNumber(serialNumber));
        Optional<String> modelNoBySn = userSn.map(sn -> deviceBindInfoDao.findModelNoByUserSn(sn));

        Integer deviceFreeTierId = userSn
                .map(factoryDataQueryService::queryFreeTierIdByUserSn)
                .orElseGet(() -> modelNoBySn
                        .map(factoryDataQueryService::queryFreeTierIdByModelNo)
                        .orElse(DEFAULT_FREE_TIER_ID)
                );

        // 确定当前设备的 free_tier_id
        return deviceFreeTierId;
    }

    public void redeemFreeLicense(FreeLicenseRedeemRequest request, Integer userId) {
        List<String> deviceList = request.getDeviceList();
        Integer finalFreeTierId = request.getFreeLicenseId();
        Tier tier = tierService.queryTierById(finalFreeTierId);

        if (finalFreeTierId != null) {
            deviceList.forEach(serialNumber -> {
                // 检查是否已经存在记录
                UserDeviceFreeTierDO existingRecord = userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(userId, serialNumber);
                if (existingRecord != null) {
                    log.debug("redeemFreeLicense record already exists userId {} sn {}", userId, serialNumber);
                    return;
                }

                // 插入新表 user_device_free_tier 记录
                UserDeviceFreeTierDO newRecord = new UserDeviceFreeTierDO();
                newRecord.setUserId(userId);
                newRecord.setSerialNumber(serialNumber);
                newRecord.setTierId(finalFreeTierId);
                newRecord.setCdate((int) (System.currentTimeMillis() / 1000));

                // 设置 Free License 的起始和结束时间
                newRecord.setStartTime((int) (System.currentTimeMillis() / 1000));
                newRecord.setEndTime(calculateEndTime(finalFreeTierId));
                newRecord.setAutoReem(request.getAutoReem() ? 1 : 0);
                userDeviceFreeTierDao.insertUserDeviceFreeTier(newRecord);
                userTierDeviceService.setDeviceActiveTier(request.getApp().getTenantId(), userId, serialNumber, finalFreeTierId, tier.getTierGroupId());
                log.debug("redeemFreeLicense insert user_tier_device userId {} sn {}",userId,serialNumber);
                if(finalFreeTierId.equals(FREE_LICENSE_7_DAY)){
                    //vip状态变更引起设备设置变化
                    userTierDeviceService.modifyDeviceSettingAfterTierChange(serialNumber,finalFreeTierId);
                }
            });
        }
        userTierDeviceService.refreshUserDevice(userId,request.getApp().getTenantId());
    }

    // 根据不同的 free_tier_id 计算结束时间
    private int calculateEndTime(Integer freeTierId) {
        int currentTime = (int) (System.currentTimeMillis() / 1000);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(currentTime * 1000L);

        switch (freeTierId) {
            case 1001: // 7天 Free License
                cal.add(Calendar.DATE, 7);
                break;
            case 1002: // 永久 1 天循环云存储
                return Integer.MAX_VALUE;
            case 1003: // 2年 3 天循环云存储
                cal.add(Calendar.YEAR, 2);
                break;
            default:
                return currentTime;
        }

        cal.add(Calendar.DATE, 1);
        // 设置时间到当天零点
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);

        return (int) (cal.getTimeInMillis() / 1000);
    }

    public void reportFreeLicenseNoThanks(FreeLicenseRequest request, Integer userId) {
        List<String> serialNumberList = request.getDeviceList();
        int currentTime = (int) Instant.now().getEpochSecond();

        serialNumberList.forEach(serialNumber ->
                userFreeLicenseNoThanksDAO.insertUserFreeLicenseNoThanks(userId, serialNumber, currentTime)
        );
    }

    @Cacheable(value = "queryUserDeviceFree", key = "#userId+'-'+#serialNumber", unless = "#result==null")
    public UserDeviceFreeTierDO queryUserDeviceFreeTierBySn(Integer userId,String serialNumber){
       return userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(userId,serialNumber);
    }


    /**
     * 免费套餐刷新
     * @return
     */
    public Long refreshUserDeviceFreeTierCache(){
        String currentDayKey = USER_FREE_TIER_REFRASH_KEY.replace("{date}", DateUtils.dateToString(new Date(),DateUtils.YYYYMMDD));
        String lastQueryId = redisService.getFromSlave(currentDayKey);

        //上次处理id
        Long startId = StringUtils.hasLength(lastQueryId) ? Long.parseLong(lastQueryId) : 0L;
        int yesterdayTime = (int) Instant.now().getEpochSecond() - 24 * 60 * 60;
        List<UserDeviceFreeTierDO> userDeviceFreeTierDOList = userDeviceFreeTierDao.queryUserDeviceFreeTierList(yesterdayTime,startId);
        log.info("refreshUserDeviceFreeTierCache startUserId {} list size {}",startId,userDeviceFreeTierDOList.size());
        if (CollectionUtils.isEmpty(userDeviceFreeTierDOList)) {
            return startId;
        }

        for (UserDeviceFreeTierDO userDeviceFreeTierDO : userDeviceFreeTierDOList) {
            try{
                Integer userId = userDeviceFreeTierDO.getUserId();
                User user = userService.queryUserById(userId);
                if(user == null){
                    continue;
                }
                Map<String,Object> dataMap = Maps.newHashMap();
                dataMap.put("userId",userId);
                dataMap.put("tenantId",user.getTenantId());
                dataMap.put(SHARE_MSG_TYPE_KEY,USER_TIER_DEVICE.getCode());
                mqSender.send(userTierDeviceTopic,userId,dataMap);
            }catch (Exception e){
                log.error("refreshUserDeviceFreeTierCache 需要计算的用户"+ userDeviceFreeTierDO.getUserId(),e);
            }
        }
        startId = userDeviceFreeTierDOList.get(userDeviceFreeTierDOList.size() - 1).getId();
        redisService.set(currentDayKey, String.valueOf(startId),USER_VIP_ACTIVE_EXPIRE_TIME);
        return startId;
    }



    /**
     * 用户正在生效的free tierId  list
     * @return
     */
    public List<Integer> queryUserFreeTierIdList(Integer userId){
        int currentTime = (int)Instant.now().getEpochSecond();
        return userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndTierId(userId,null)
                .stream()
                .filter(userDeviceFreeTierDO -> userDeviceFreeTierDO.getEndTime() > currentTime)
                .map(UserDeviceFreeTierDO::getTierId).collect(Collectors.toList());
    }

    /**
     * 正在生效或者将要过期
     * @return
     */
    public List<UserDeviceFreeTierDO> queryUserFreeTierIdExpire(Integer userId){
        int currentTime = (int)Instant.now().getEpochSecond();
        return userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndTierId(userId,null)
                .stream()
                .filter(userDeviceFreeTierDO -> userDeviceFreeTierDO.getEndTime() > (currentTime - USER_VIP_ACTIVE_EXPIRE_TIME))
                .collect(Collectors.toList());
    }


    /**
     * 已经领取free license tier
     * @param userId
     * @return
     */
    public List<UserDeviceFreeTierDO> queryUserDeviceByFreeTierId(Integer userId,Integer tierId){
        return userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndTierId(userId, tierId);
    }

    public void autoRedeemFreeLicense(User user, String serialNumber,Integer freeLicenseId){
        FreeLicenseRedeemRequest freeLicenseRedeemRequest = new FreeLicenseRedeemRequest();
        freeLicenseRedeemRequest.setApp(AppInfo.builder().tenantId(user.getTenantId()).build());
        freeLicenseRedeemRequest.setDeviceList(Collections.singletonList(serialNumber));
        freeLicenseRedeemRequest.setFreeLicenseId(freeLicenseId);
        freeLicenseRedeemRequest.setAutoReem(true);
        this.redeemFreeLicense(freeLicenseRedeemRequest, user.getId());
    }

    /**
     * 判断是否自动领取
     * @param userId
     * @param serialNumber
     * @return
     */
    public FreeLicenseResponse.AutoRedeemFreeLicense isAutoRedeemFreeLicense(Integer userId, String serialNumber){
        UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(userId);
        if(userSettingsDO != null && userSettingsDO.getSupportFreeLicense().equals(0)){
            FreeLicenseResponse.AutoRedeemFreeLicense response = new FreeLicenseResponse.AutoRedeemFreeLicense();
            response.setAutoRedeem(false);
            log.debug("isAutoRedeemFreeLicense user not support freeLicense {}",userId);
            return response;
        }
        //根据历史记录获取绑定记录
        UserShowFreeLicenseDO userShowFreeLicenseDO = userShowFreeLicenseService.queryUserShowFreeLicenseByUserId(userId);

        FreeLicenseResponse.AutoRedeemFreeLicense response = this.queryFreeLicenseByHistoryCord(userId,userShowFreeLicenseDO);

        // 无历史领取记录，使用设备配置的型号领取规则
        response = response == null ? this.queryFreeLicenseByDevice(userId,serialNumber,userShowFreeLicenseDO) : response;

        // 记录领取方式、应领取 free license
        userShowFreeLicenseService.insertUserShowFreeLicense(userId,response.getFreeLicenseId(),response.isAutoRedeem() ? 1 : 0,response.getPromotionPeriod());
        return response;
    }

    /**
     * 根据历史记录获取用户应选择free lincense
     * @param userId
     * @return
     */
    private FreeLicenseResponse.AutoRedeemFreeLicense queryFreeLicenseByHistoryCord(Integer userId, UserShowFreeLicenseDO userShowFreeLicenseDO){
        FreeLicenseResponse.AutoRedeemFreeLicense response = new FreeLicenseResponse.AutoRedeemFreeLicense();
        log.debug("queryFreeLicenseByHistoryCord userShowFreeLicenseDO {}",userShowFreeLicenseDO);
        if (userShowFreeLicenseDO != null) {
            response.setAutoRedeem(userShowFreeLicenseDO.getAutoReem().equals(1));
            response.setFreeLicenseId(userShowFreeLicenseDO.getFreeLicenseId());
            response.setPromotionPeriod(userShowFreeLicenseDO.getPromotionPeriod());
            return response;
        }

        // 2. 查询已领取的freeLicense记录
        UserDeviceFreeTierDO highestTier = userDeviceFreeTierDao.selectHighestTierByUserId(userId);
        log.debug("queryFreeLicenseByHistoryCord highestTier {}",highestTier);

        if (highestTier != null ) {
            response.setAutoRedeem(highestTier.getAutoReem().equals(1));
            response.setFreeLicenseId(highestTier.getTierId());
            response.setPromotionPeriod(highestTier.getAutoReem());
            return response;
        }

        // 无历史记录
        return null;
    }

    /**
     * 根据设备获取 free lincese
     * @param serialNumber
     * @return
     */
    private FreeLicenseResponse.AutoRedeemFreeLicense queryFreeLicenseByDevice(Integer userId,String serialNumber,UserShowFreeLicenseDO userShowFreeLicenseDO){
        FreeLicenseResponse.AutoRedeemFreeLicense response = new FreeLicenseResponse.AutoRedeemFreeLicense();
        DeviceModel deviceModel = deviceModelConfigService.queryDeviceModelConfig(serialNumber);
        boolean promotionPeriod = userShowFreeLicenseService.userDevicePromotionPeriod(userShowFreeLicenseDO,deviceModel);

        response.setPromotionPeriod(promotionPeriod ? 1 : 0);
        response.setAutoRedeem(promotionPeriod);
        response.setFreeLicenseId(promotionPeriod ? FREE_LICENSE_1_DAY : this.getFreeTierId(serialNumber));

        log.debug("queryFreeLicenseByDevice response {}",response);
        return response;
    }

}
