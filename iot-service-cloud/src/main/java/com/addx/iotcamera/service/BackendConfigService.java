package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.dynamic.BackendConfigItem;
import com.addx.iotcamera.config.StorageAllocateConfig;
import com.addx.iotcamera.dao.BackendConfigDAO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashSet;
import java.util.List;

import static com.addx.iotcamera.config.StorageAllocateConfig.*;

@Slf4j
//@Component
public class BackendConfigService {

    @Autowired
    private BackendConfigDAO backendConfigDAO;

    public StorageAllocateConfig queryStorageAllocateConfig() {
        final List<BackendConfigItem> allItems = backendConfigDAO.queryByGroup("storage-allocate");
        if (CollectionUtils.isEmpty(allItems)) return null;
        final StorageAllocateConfig config = new StorageAllocateConfig();
        for (final BackendConfigItem item : allItems) {
            if (VIP_RATIOS.equals(item.getKey())) {
                config.getVipRatios().add(JSON.parseObject(item.getValue(), StorageAllocateConfig.RatioItem.class));
            } else if (NO_VIP_RATIOS.equals(item.getKey())) {
                config.getNoVipRatios().add(JSON.parseObject(item.getValue(), StorageAllocateConfig.RatioItem.class));
            } else if (item.getKey().startsWith(WHITE_USER_IDS)) {
                final PirServiceName serviceName = PirServiceName.valueOf(item.getKey().substring(WHITE_USER_IDS.length()));
                if (serviceName != null) {
                    config.getWhiteUserIds().computeIfAbsent(serviceName.name(), k -> new LinkedHashSet<>())
                            .addAll(JSON.parseArray(item.getValue(), Integer.class));
                }
            } else if (SAAS_AI_SUPPORT_SERVICE_NAMES.equals(item.getKey())) {
                config.getSaasAiSupportServiceNames().addAll(JSON.parseArray(item.getValue(), String.class));
            }
        }
        log.info("queryStorageAllocateConfig end! config={}", JSON.toJSONString(config, true));
        return config;
    }

}
