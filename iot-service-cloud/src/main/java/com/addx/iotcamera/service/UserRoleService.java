package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.IUserRoleDAO;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.helper.BatchQueryIterator;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.addx.iotcamera.constants.DeviceInfoConstants.DEVICE_USER_ROLE_PREFIX;

@Service
public class UserRoleService {
    private final static Logger logger = LoggerFactory.getLogger(UserRoleService.class);
    @Autowired
    IUserRoleDAO userRoleDAO;
    @Autowired
    private UserService userService;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private LibraryStatusService libraryStatusService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private VideoSearchService videoSearchService;

    @Autowired
    private IShareDAO shareDAO;

    public int getDeviceAdminUser(String serialNumber) {
        logger.debug("deviceAdmin init {}", serialNumber);
        UserRoleDO userRoleDO = userRoleDAO.getDeviceAdminUser(serialNumber);
        if (userRoleDO == null) {
            // 设备未绑定
            return 0;
        }
        return userRoleDO.getAdminId();
    }

    public UserRoleDO getDeviceAdminUserRole(String serialNumber) {
        logger.debug("deviceAdminRole init {}", serialNumber);
        return userRoleDAO.getDeviceAdminUser(serialNumber);
    }

    @Cacheable(value = "deviceRole", key = "#serialNumber", unless = "#result==null")
    public List<Integer> findAllUsersForDevice(String serialNumber) {
        logger.debug("deviceRole init {}", serialNumber);
        return userRoleDAO.findAllUsersForDevice(serialNumber).stream()
                .map(UserRoleDO::getUserId)
                .collect(Collectors.toList());
    }

    public List<String> getUserSerialNumberByUserId(Integer userId) {
        return userRoleDAO.getUserSerialNumberByRoleId(userId);
    }

    public List<String> getSerialNumbersByUserId(Integer userId) {
        return userRoleDAO.queryUserRoleByUserId(userId).stream()
                .map(UserRoleDO::getSerialNumber)
                .collect(Collectors.toList());
    }

    /**
     * 用户名下设备列表
     *
     * @param userId
     * @return
     */
    public List<UserRoleDO> getUserRoleByUserId(Integer userId, Integer roleId) {
        return userRoleDAO.getDeviceRoleByUserId(userId, roleId);
    }

    /**
     * 用户名下设备列表
     *
     * @param userId
     * @return
     */
    public List<UserRoleDO> getUserRoleByUserId(Integer userId) {
        return this.getUserRoleByUserId(userId, null);
    }

    /**
     * 通过用户Id/序列号查询对应记录
     *
     * @param userId
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "userDeviceRole", key = "#userId+'-'+#serialNumber", unless = "#result==null")
    public UserRoleDO getUserRoleDOByUserIdAndSerialNumber(Integer userId, String serialNumber) {
        logger.debug("userDeviceRole init {}", serialNumber);
        return userRoleDAO.getDeviceUserAndSerialNumber(userId, serialNumber);
    }

    /**
     * 清理用户-设备对应关系、视频
     *
     * @param userId
     */
    public void deleteUserRole(Integer userId) {
        logger.debug("deleteUserRole userId:{}", userId);
        List<UserRoleDO> userRoleDOList = userRoleDAO.getDeviceRoleByUserId(userId, null);
        if (CollectionUtils.isEmpty(userRoleDOList)) {
            return;
        }
        for (UserRoleDO userRole : userRoleDOList) {
            if (userRole.getUserId() == userRole.getAdminId()) {
                // 重置设备绑定信息
                deviceInfoService.deactivateDevice(userRole.getSerialNumber());
                //用户是设备的admin
                deleteAdminRole(userRole.getAdminId());
            } else {
                //作为被分享者，删除与admin、设备的关系
                userRoleDAO.deleteUserRole(userRole.getUserId(), userRole.getSerialNumber());
            }
        }
        //解除用户与视频关系
        libraryStatusService.deleteLibraryStatus(userId);
    }

    /**
     * 删除当前注销用户的关系、视频
     *
     * @param adminId
     */
    public void deleteAdminRole(Integer adminId) {
        logger.info("当前用户是admin:{}", adminId);
        //删除admin与被分享者关系
        userRoleDAO.deleteUserRoleByAdminId(adminId);
    }


    /**
     * 通过设备号获取所属国家地区
     *
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "deviceAdminCountryNo", key = "#serialNumber", unless = "#result==null")
    public String getCountryNoBySerialNumber(String serialNumber) {
        logger.debug("deviceAdminCountryNo init {}", serialNumber);
        return userRoleDAO.getCountryNoBySerialNumber(serialNumber);
    }

    /**
     * 更新设备绑定时间
     */
    public void updateDeviceBindTime(UserRoleDO userRoleDO) {
        userRoleDAO.updateDeviceBindTime(userRoleDO);
    }

    @CacheEvict(value = {
            "deviceRole",
            "userDeviceRole",
            "deviceAdminCountryNo",
            DeviceInfoConstants.DEVICE_SN
    }, key = "#serialNumber")
    public Integer cleanUserRole(String serialNumber, Integer adminId) {
        try {
            return userRoleDAO.cleanUserRole(serialNumber);
        } catch (Exception e) {
            logger.error("cleanUserRole Failed to clean user role for serialNumber: {}", serialNumber, e);
            throw e;
        } finally {
            try {
                // 确保缓存清除
                clearUserRoleCache(serialNumber, Collections.singletonList(adminId));
            } catch (Exception e) {
                // 不要在finally中抛出异常，否则会覆盖原始异常
                logger.error("cleanUserRole  to clear cache in finally block for serialNumber: {}",
                        serialNumber, e);
            }
        }
    }

    // 清理掉设备与用户关系的缓存
    public Long clearUserRoleCache(String serialNumber, List<Integer> adminIds) {
        final List<String> keys = new LinkedList<>();
        Stream.of(DeviceInfoConstants.DEVICE_USER_ROLE_PREFIXES).map(it -> it.replace("{serialNumber}", serialNumber)).forEach(keys::add);
        for (Integer adminId : adminIds) {
            if (adminId == null || adminId <= 0) continue;
            keys.add(DEVICE_USER_ROLE_PREFIX.replace("{userId}", String.valueOf(adminId)).replace("{serialNumber}", serialNumber));
        }
        final Long delete = redisService.deleteCacheableAnnotationCache(keys);
        logger.info("clearUserRoleCache end! keys={},delete={}", JSON.toJSONString(keys), delete);
        return delete;
    }

    /**
     * 保存用户与设备的关系
     *
     * @param userRoleDO
     * @return
     */
    @CacheEvict(value = {
            "deviceRole",
            "userDeviceRole",
            "deviceAdminCountryNo",
            DeviceInfoConstants.DEVICE_SN
    }, key = "#userRoleDO.serialNumber")
    public Integer saveUserRole(UserRoleDO userRoleDO) {
        String key = DEVICE_USER_ROLE_PREFIX.replace("{userId}", String.valueOf(userRoleDO.getUserId()))
                .replace("{serialNumber}", userRoleDO.getSerialNumber());
        redisService.dropDeviceOperationDo(key);
        videoSearchService.clearSearchOptionCache(Arrays.asList(userRoleDO.getUserId()));
        return userRoleDAO.saveUserRole(userRoleDO);
    }

    /**
     * 根据sn获取绑定的管理员用户
     *
     * @param sn
     * @return
     */
    public Result<User> getAdminUserBySn(String sn) {
        int adminId = getDeviceAdminUser(sn);
        if (adminId <= 0) {
            return Result.Error(ResultCollection.DEVICE_NO_ACCESS, "设备未绑定！serialNumber=" + sn);
        }
        User user = userService.queryUserById(adminId);
        if (user == null) {
            return Result.Error(ResultCollection.DEVICE_NO_ACCESS, "用户不存在！serialNumber=" + sn);
        }
        return new Result(user);
    }

    @AllArgsConstructor
    @Getter
    public static class UserRoles {
        private final Integer adminId; // 管理员id
        private final List<Integer> shareUserIds; // 被分享者id

        public List<Integer> getUserIds() {
            List<Integer> userIds = new LinkedList<>(shareUserIds);
            userIds.add(adminId);
            return userIds;
        }
    }

    public UserRoles queryUserRolesBySn(String sn) {
        return queryUserRolesBySn(sn, true);
    }

    public UserRoles queryUserRolesBySn(String sn, boolean usingCache) {
        List<UserRoleDO> userRoles;
        if (usingCache) {
            userRoles = userRoleDAO.queryUserRoleBySn(sn);
        } else {
            userRoles = userRoleDAO.queryUserRoleBySnNoCache(sn);
        }
        Map<Integer, List<Integer>> roleId2UserIds = userRoles.stream().collect(Collectors.groupingBy(it ->
                Integer.parseInt(it.getRoleId()), Collectors.mapping(it -> it.getUserId(), Collectors.toList())));
        Integer userId = Optional.ofNullable(roleId2UserIds.get(UserRoleEnums.ADMIN.getCode())).map(it -> it.get(0)).orElse(null);
        List<Integer> shareUserIds = roleId2UserIds.getOrDefault(UserRoleEnums.USER.getCode(), Collections.emptyList());
        return new UserRoles(userId, shareUserIds);
    }

    /**
     * 查询所有已绑定的设备sn和管理员用户id
     * userRole没有唯一主键，查询结果可能会有少量重复
     */
    public Iterator<UserRoleDO> queryAllAdminUserRoleIterator(String logPrefix, int batchSize) {
        return new BatchQueryIterator<Integer, UserRoleDO>(logPrefix,
                userRoleDAO::queryAdminUserRoleByUserIdGtAndNum, UserRoleDO::getUserId,
                Integer::compare, batchSize, null);
    }

    /**
     * 查询所有已绑定的设备设置和管理员用户id
     * userRole没有唯一主键，查询结果可能会有少量重复
     */
    public Iterator<AdminAndSettingDO> queryAllAdminAndSettingIterator(String logPrefix, int batchSize) {
        return new BatchQueryIterator<Integer, AdminAndSettingDO>(logPrefix,
                userRoleDAO::queryAdminAndSettingByUserIdGtAndNum, AdminAndSettingDO::getUserId,
                Integer::compare, batchSize, null);
    }

    /**
     * 查询所有已绑定设备数量
     *
     * @return
     */
    public Integer queryAllAdminUserRoleNum() {
        return userRoleDAO.queryAllBindDeviceNum();
    }

    /**
     * 设备解除分享后，清空缓存
     * @param approvalDO
     */
    @CacheEvict(value = "deviceRole", key = "#approvalDO.serialNumber")
    public Integer cleanShareUserRole(ShareApprovalDO approvalDO){
       Integer res = shareDAO.undoShareSelf(approvalDO);
        StringBuilder userRoleKey = new StringBuilder();
        userRoleKey.append("userDeviceRole::");
        userRoleKey.append(approvalDO.getTargetId());
        userRoleKey.append("-");
        userRoleKey.append(approvalDO.getSerialNumber());
        redisService.deleteCacheableAnnotationCache(userRoleKey.toString());

        return res;
    }

    /**
     * 查询用户名下绑定设备-不包含分享设备
     * @param adminId
     * @param readOnly
     * @return
     */
    public List<DeviceDO> queryUserDeviceAdmin(Integer adminId,boolean readOnly){
        List<String> userRoleDOList = readOnly ?
                this.getUserRoleByUserId(adminId,UserRoleEnums.ADMIN.getCode()).stream().map(UserRoleDO::getSerialNumber).collect(Collectors.toList()) :
                this.getUserSerialNumberByUserId(adminId);

        return userRoleDOList.stream()
                .map(serialNumber -> DeviceDO.builder().serialNumber(serialNumber).build())
                .collect(Collectors.toList());
    }
}
