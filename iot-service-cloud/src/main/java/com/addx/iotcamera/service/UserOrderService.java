package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.app.userorder.BoughtProductInfo;
import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.app.vip.TierListRequest;
import com.addx.iotcamera.bean.app.vip.TierServiceInfoRequest;
import com.addx.iotcamera.bean.app.vip.UserOrderSubscriptionCancelRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserOrderCancelDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.init.TierProductInit;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.dao.user.IUserOrderCancelDAO;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.service.pay.GooglePayService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.ORDER_CANCEL;

/**
 * 用户订单历史service
 */
@Service
@Slf4j
public class UserOrderService {
    @Autowired
    @Lazy
    private TierService tierService;

    @Autowired
    private ProductService productService;

    @Autowired
    private TierProductInit tierProductInit;

    @Resource
    private CopyWrite copyWrite;

    @Resource
    private OrderService orderService;

    @Resource
    private TenantTierConfig tenantTierConfig;

    @Resource
    @Lazy
    private UserVipService userVipService;

    @Resource
    @Lazy
    private PaymentService paymentService;

    @Resource
    @Lazy
    private ApplePayService applePayService;
    @Resource
    @Lazy
    private GooglePayService googlePayService;

    @Resource
    private IUserOrderCancelDAO iUserOrderCancelDAO;
    @Autowired
    private UserRoleService userRoleService;


    /**
     * 获取购买历史
     *
     * @param request
     * @param userId
     * @return
     */
    public BoughtProductInfo getBoughtProductList(TierServiceInfoRequest request, Integer userId) {
        TierListRequest tierListRequest = new TierListRequest();
        BeanUtils.copyProperties(request,tierListRequest);
        tierListRequest.setProductVersion(null);
        // 查询所有历史订单
        List<OrderDO> userOrderList = orderService.queryUserOrderList(userId);

        // 获取当前生效的订阅订单
        List<OrderDO> currentActiveSubOrderList = userOrderList.stream().filter(orderDO ->
                ObjectUtils.equals(orderDO.getSubType(), 1) && ObjectUtils.equals(orderDO.getStatus(), 1)
                        && this.currentSubOrder(orderDO))
                .collect(Collectors.toList());

        // 获取历史购买成功的订单
        List<OrderDO> historyUserOrderList = userOrderList.stream().filter(orderDO ->
                ObjectUtils.equals(orderDO.getSubType(), 0) && (ObjectUtils.equals(orderDO.getStatus(), 1)))
                .collect(Collectors.toList());

        // 当前生效订阅商品列表
        List<BoughtProductInfo.BoughtSubscriptionProduct> currentSubscriptionProductList = new LinkedList<>();
        currentActiveSubOrderList.forEach(orderDO -> {
            OrderProductDo orderProductDo = orderService.queryOrderProductDO(orderDO.getId());
            if (orderProductDo == null) {
                return;
            }

            ProductDO productDO = productService.queryProductById(orderProductDo.getProductId());
            if (productDO == null) {
                return;
            }
            Tier tier = tierService.queryTierById(productDO.getTierId());
            if(tier == null){
                return;
            }
            if(!tier.getTierServiceType().equals(request.getTierServiceType())){
                return;
            }

            List<TierInfo> tenantIdTierInfoList = tierService.queryTierListV2(tierListRequest);
            TierInfo tierInfo = CollectionUtils.isEmpty(tenantIdTierInfoList) ? null :
                    tenantIdTierInfoList.stream()
                            .filter(tenantIdTierInfo -> ObjectUtils.equals(tenantIdTierInfo.getId(), productDO.getTierId()))
                            .findAny().orElse(null);
            if(tierInfo == null && productDO.getType() <= ProductTypeEnums.SUBSCRIBE.getCode()){
                //没有套餐文案
                return;
            }
            // tierInfo == null则走新套餐商品文案
            String tierName = tierInfo == null ? tierService.queryTierName(productDO,request.getLanguage(),request.getApp().getTenantId()) : tierInfo.getName();
            BoughtProductInfo.BoughtSubscriptionProduct boughtSubscriptionProduct = new BoughtProductInfo.BoughtSubscriptionProduct();
            boughtSubscriptionProduct.setTierName(StringUtils.hasLength(tierName) ? tierName : orderDO.getOrderSn());
            boughtSubscriptionProduct.setBoughtFrom(getBoughtFromByOrderType(orderDO.getOrderType()));
            boughtSubscriptionProduct.setNextSubscriptionTime(nextSubOrderPaymentTime(orderDO));
            boughtSubscriptionProduct.setOrderStatus(orderDO.getStatus());
            currentSubscriptionProductList.add(boughtSubscriptionProduct);
        });

        // 历史购买成功商品列表
        List<BoughtProductInfo.BoughtProduct> boughtProductList = new LinkedList<>();
        Map<Long,ProductDO> productDOMap = orderService.queryProductBatch(historyUserOrderList.stream().map(OrderDO::getId).collect(Collectors.toList()));

        historyUserOrderList.forEach(historyOrderDO -> {
            if(!productDOMap.containsKey(historyOrderDO.getId())){
                com.addx.iotcamera.util.LogUtil.error(log, "未查询到订单商品{}",historyOrderDO.getId());
                return;
            }
            ProductDO historyProductDO = productDOMap.get(historyOrderDO.getId());
            if(historyProductDO == null){
                log.info("找不到商品,订单{}",historyOrderDO.getId());
                return;
            }

            Tier tier = tierService.queryTierById(historyProductDO.getTierId());
            if(tier == null){
                return;
            }
            if(!tier.getTierServiceType().equals(request.getTierServiceType())){
                return;
            }

            String tierName = null;
            AdditionalTierInfo additionalTierInfo = null;
            if (historyProductDO.getTierId() > 0) {
                List<TierInfo> tenantIdTierInfoList = tierService.queryTierListV2(tierListRequest);
                TierInfo tierInfo = CollectionUtils.isEmpty(tenantIdTierInfoList) ? null :
                        tenantIdTierInfoList.stream()
                                .filter(tenantIdTierInfo -> ObjectUtils.equals(tenantIdTierInfo.getId(), historyProductDO.getTierId()))
                                .findAny()
                                .orElse(null);
                tierName = tierInfo != null ? tierInfo.getName() : tierService.queryTierName(historyProductDO,request.getLanguage(),request.getApp().getTenantId());
            } else {
                List<AdditionalTierInfo> additionalTierInfoList = tierService.queryAdditionalTierList(request, userId);
                additionalTierInfo = CollectionUtils.isEmpty(additionalTierInfoList) ? null :
                        additionalTierInfoList.stream()
                        .filter(tenantAdditionalTierInfo -> ObjectUtils.equals(tenantAdditionalTierInfo.getTierUid(), historyProductDO.getAdditionalTierUid()))
                                .findAny()
                                .orElse(null);
            }
            if (!StringUtils.hasLength(tierName) && additionalTierInfo == null) {
                return;
            }

            BoughtProductInfo.BoughtProduct boughtProduct = new BoughtProductInfo.BoughtProduct();
            if (StringUtils.hasLength(tierName)) {
                boughtProduct.setTierName(tierName);
            }
            if (additionalTierInfo != null) {
                boughtProduct.setAdditionalTierUid(additionalTierInfo.getTierUid());
            }

            String monthStr = tierProductInit.getProductMonth(historyProductDO.getId(), request.getLanguage(), copyWrite);
            boughtProduct.setMonth(monthStr);
            boughtProduct.setBoughtFrom(getBoughtFromByOrderType(historyOrderDO.getOrderType()));
            boughtProduct.setBoughtTime(historyOrderDO.getCdate());
            boughtProduct.setOrderStatus(historyOrderDO.getStatus());
            boughtProductList.add(boughtProduct);
        });

        BoughtProductInfo boughtProductInfo = new BoughtProductInfo();
        boughtProductInfo.setCurrentSubscriptionProductList(currentSubscriptionProductList);
        boughtProductInfo.setBoughtProductList(boughtProductList);
        return boughtProductInfo;
    }

    /**
     * 0:支付宝;1:微信;2:IOS;3:谷歌内购
     *
     * @param orderType
     * @return
     */
    public String getBoughtFromByOrderType(Integer orderType) {
        return ObjectUtils.equals(orderType, 0) ? PaymentTypeEnums.ALI.name()
                : ObjectUtils.equals(orderType, 1) ? PaymentTypeEnums.WECHAT.name()
                : ObjectUtils.equals(orderType, 2) ? PaymentTypeEnums.APPLE.name()
                : ObjectUtils.equals(orderType, 3) ? PaymentTypeEnums.GOOGLE.name() : null;
    }


    /**
     * 判断订阅订单是否在生效中
     * @param orderDO
     * @return
     */
    private boolean currentSubOrder(OrderDO orderDO){
        // 订阅套餐有7天的延展期
        return DateUtils.getDateAfterDate( new Date(orderDO.getTimeEnd().longValue()*1000), ObjectUtils.defaultIfNull(orderDO.getProductMonth(), 1),7).getTime() > System.currentTimeMillis();
    }

    /**
     * 首次购买记录的timeStart、timeEnd 时间一样，需要区别判读
     * @param orderDO
     * @return
     */
    public Integer nextSubOrderPaymentTime(OrderDO orderDO){
        return orderDO.getTimeStart().equals(orderDO.getTimeEnd()) ?
                DateUtils.getDateAfterMonthSecond(orderDO.getTimeStart(), ObjectUtils.defaultIfNull(orderDO.getProductMonth(), 1)) : orderDO.getTimeEnd();
    }


    /**
     * 查询用户当前生效订阅订单
     * @return BoughtProductInfo
     */
    public BoughtProductInfo queryUserCurrentSubscriptionOrder(Integer userId, TierServiceInfoRequest request){
        BoughtProductInfo response = new BoughtProductInfo();
        Integer currentTime = (int) Instant.now().getEpochSecond();
        //只查询购买的套餐
        List<UserVipDO> currentUserVip = userVipService.queryUserVipList(userId,currentTime,request.getTierServiceType()).stream()
                .filter(uv -> uv.getTierId()%10 > 0 && uv.getOrderId() > 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(currentUserVip)){
            //无购买套餐
            return response;
        }

        //还未过期的订单订阅订单
        List<Long> orderIds = currentUserVip.stream().map(UserVipDO::getOrderId).collect(Collectors.toList());
        List<OrderDO> subOrderDOList = orderService.queryOrderBatch(orderIds);
        if(CollectionUtils.isEmpty(subOrderDOList)){
            //没有订阅订单
            return response;
        }

        //订阅订单支付流水记录
        Set<Long> subOrderIds = subOrderDOList.stream().map(OrderDO::getId).collect(Collectors.toSet());
        List<String> tradeNos = currentUserVip.stream()
                .filter(uv -> subOrderIds.contains(uv.getOrderId()) && StringUtils.hasLength(uv.getTradeNo()))
                .map(UserVipDO::getTradeNo)
                .collect(Collectors.toList());
        List<PaymentFlow> paymentFlowList = paymentService.queryPaymentBatch(tradeNos);

        // 订阅订单-> 订阅订单每期账单流水list
        Map<String,List<PaymentFlow>> orderSn2PaymentFlowMap = paymentFlowList.stream().collect(Collectors.groupingBy(PaymentFlow::getOutTradeNo));

        List<BoughtProductInfo.BoughtSubscriptionProduct> currentSubscriptionProductList = Lists.newArrayList();
        for(OrderDO orderDO : subOrderDOList){
            //订单下支付流水
            List<PaymentFlow> orderPaymentFlowList = orderSn2PaymentFlowMap.get(orderDO.getOrderSn());
            if(CollectionUtils.isEmpty(orderPaymentFlowList)){
                continue;
            }
            // 当前单最新一期账单流水--计算下一次续订时间
            PaymentFlow lastPaymentFlow = orderPaymentFlowList.get(orderPaymentFlowList.size()-1);
            BoughtProductInfo.BoughtSubscriptionProduct orderInfo = new BoughtProductInfo.BoughtSubscriptionProduct();
            ProductDO productDO = productService.queryProductById(lastPaymentFlow.getProductId());
            orderInfo.setTierName(tierService.queryTierName(request,productDO));
            orderInfo.setBoughtFrom(getBoughtFromByOrderType(lastPaymentFlow.getType()));
            orderInfo.setOrderStatus(orderDO.getStatus());
            orderInfo.setProductId(lastPaymentFlow.getProductId());
            orderInfo.setOrderId(orderDO.getId());
            orderInfo.setTierId(productDO.getTierId());
            orderInfo.setRollingDays(tierService.queryTierById(productDO.getTierId()).getRollingDays());

            // freeTrial 账单
            Optional<PaymentFlow> paymentFlowFreeTrial = orderPaymentFlowList.stream().filter(f -> f.getFreeTrial().equals(1)).findFirst();
            if(paymentFlowFreeTrial.isPresent()){
                PaymentFlow freeFlow = paymentFlowFreeTrial.get();
                Integer freeTrialExpireTime = this.queryPaymentExpire(freeFlow);
                orderInfo.setFreeTrial(true);
                orderInfo.setNextSubscriptionTime(freeTrialExpireTime);
            }else{
                orderInfo.setFreeTrial(false);
                orderInfo.setNextSubscriptionTime(this.queryPaymentExpire(lastPaymentFlow));
            }


            orderInfo.setOrderCancel(orderDO.getOrderCancel().equals(1));
            currentSubscriptionProductList.add(orderInfo);
        }
        response.setCurrentSubscriptionProductList(currentSubscriptionProductList);
        response.setOnlyBird(userRoleService.getUserSerialNumberByUserId(userId).stream().allMatch(s -> tierService.isBirdDevice(s)));
        return response;
    }


    private Integer queryPaymentExpire(PaymentFlow paymentFlow){
        // 已经记录过期时间，直接返回
        if(paymentFlow.getExpireTime()!= null && paymentFlow.getExpireTime()>0){
            return paymentFlow.getExpireTime();
        }

        //因为订单查询参数太大，只历史没有账单过期时间的才需要重新查询
        Integer expireTime = null;
        if(paymentFlow.getType().equals(PaymentTypeEnums.APPLE.getCode())){
            ApplePaymentRequest request = JSONObject.parseObject(paymentFlow.getExtend(),ApplePaymentRequest.class);
            expireTime = applePayService.queryAppleOrderExpireTime(request,paymentFlow.getTradeNo());
        }else{
            GooglePaymentRequest request = JSONObject.parseObject(paymentFlow.getExtend(),GooglePaymentRequest.class);
            expireTime = googlePayService.queryGoogleOrderExpireTime(request);
        }

        //查询出账单过期时间，更新记录，以后不用再查询平台
        if(expireTime != null && expireTime > 0){
            paymentService.updatePaymentFlowExpire(paymentFlow.getTradeNo(),expireTime);
        }
        return Optional.ofNullable(expireTime).orElse(0);
    }






    /**
     * 用户app内取消订阅
     * @param userId
     * @param request
     */
    @Transactional
    public void userSubscriptionOrderCancel(Integer userId, UserOrderSubscriptionCancelRequest request){
        OrderDO orderDO = orderService.queryOrderInfoById(request.getOrderId());
        if(orderDO == null || !orderDO.getUserId().equals(userId) || orderDO.getSubType().equals(0) || CollectionUtils.isEmpty(request.getCancelList())){
            throw new BaseException(INVALID_PARAMS);
        }

        if( orderDO.getOrderCancel().equals(1)){
            log.info("连续订阅订单已取消");
            throw new BaseException(ORDER_CANCEL);
        }
        int currentTime = (int)Instant.now().getEpochSecond();

        for(UserOrderSubscriptionCancelRequest.OrderSubscriptionCancel reason : request.getCancelList()){
            UserOrderCancelDO userOrderCancelDO = UserOrderCancelDO.builder()
                    .orderId(orderDO.getId())
                    .userId(userId)
                    .cancelReason(reason.getCancelReason())
                    .cancelReasonRemark(reason.getCancelRemark())
                    .cancelTime(currentTime)
                    .onlyBird(request.getOnlyBird() ? 1 : 0)
                    .build();
            iUserOrderCancelDAO.insertUserOrderCancel(userOrderCancelDO);
        }

        if(orderDO.getOrderType().equals(PaymentTypeEnums.GOOGLE.getCode())){
            // 谷歌订单实时调用取消接口
            Gson gson = new Gson();
            GooglePaymentRequest paymentRequest = gson.fromJson(orderDO.getExtend(),GooglePaymentRequest.class);
            String subscriptionId = StringUtils.hasLength(paymentRequest.getSubscriptionGroupId()) ?
                    paymentRequest.getSubscriptionGroupId() : String.valueOf(paymentRequest.getProductId());
            boolean cancelOrder = googlePayService.subscriptionOrderCancel(subscriptionId,paymentRequest.getPurchaseToken(),request.getApp().getBundle());
            if(!cancelOrder){
                throw new BaseException(INVALID_PARAMS);
            }
        }
    }
}



