package com.addx.iotcamera.service.video;

import com.addx.debug.annotation.ForwardKafkaMsg;
import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.db.VideoImageDO;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.db.birdtab.BirdTabDO;
import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.openapi.RecognitionObjectCategory;
import com.addx.iotcamera.bean.openapi.SaasAITaskOM;
import com.addx.iotcamera.bean.video.UploadVideoCompleteRequest;
import com.addx.iotcamera.bean.video.VideoMsgType;
import com.addx.iotcamera.bean.video.VideoSliceRequest;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.helper.AsyncTaskExecuteLogger;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.BirdLoversService;
import com.addx.iotcamera.service.NewRedisService;
import com.addx.iotcamera.service.S3Service;
import com.addx.iotcamera.service.app.BirdTabService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.LogUtil;
import com.addx.iotcamera.util.MapUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.proto.JpegImageFile;
import org.addx.iot.common.proto.iot_local.Object;
import org.addx.iot.common.proto.iot_local.VideoEvent;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.ProtobufJsonUtils;
import org.addx.iot.domain.extension.ai.constant.AiFeatures;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.AiObjectActionEnumUtil;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.model.AiObjectAction;
import org.addx.iot.domain.extension.ai.model.AiObjectDetail;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;
import org.addx.iot.domain.extension.ai.service.IAiVideoObjectService;
import org.addx.iot.domain.extension.ai.utils.AiObjectActionUtil;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.ai.vo.AiVideoObjectInfoResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.service.openapi.SaasAIService.getEventIndex2LabelId;
import static com.addx.iotcamera.service.video.DeviceCache.*;
import static com.addx.iotcamera.service.video.VideoCache.Flag.*;
import static com.addx.iotcamera.service.video.VideoHandleResult.*;
import static org.addx.iot.domain.extension.ai.enums.AiObjectEnum.FACE;

/**
 * 视频生成
 * 不兼容老视频表
 */
@Slf4j(topic = "videoGenerate")
@Component
public class VideoGenerateService {

    @Autowired
    private VideoStoreService videoStoreService;
    @Autowired
    private VideoAIService videoAIService;
    @Autowired
    private VideoNotifyService videoNotifyService;
    @Autowired
    private VideoCacheService videoCacheService;
    @Autowired
    private BirdLoversService birdLoversService;
    @Autowired
    private DevicePlatformEventPublisher devicePlatformEventPublisher;
    @Autowired
    @Qualifier("videoSliceReport")
    private Executor executor;
    @Resource
    private MqSender mqSender;
    @Getter
    @Value("${spring.kafka.topics.video-generate}")
    private String videoGenerateTopic;
    @Value("${spring.kafka.topics.video-generate-by-user-id}")
    private String videoGenerateTopicByUserId;

    @Autowired
    private TimeTicker timeTicker;
    @Autowired
    private VideoReportLogService videoReportLogService;
    @Autowired
    private NewRedisService newRedisService;
    @Autowired
    private OpenApiWebhookService openApiWebhookService;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private SafemoAiNotificationService safemoAiNotificationService;

    private static final String REDIS_KEY_VIDEO_CREATED = "video_created:{traceId}";
    private static final String REDIS_VALUE_VIDEO_CREATE_OK = "1";

    @Setter
    private int executeDeviceTaskMaxLoopNum = 100;
    @Getter
    public final String serverId = System.currentTimeMillis() + ""; // 服务器实例id

    public static final String MSG_REQUEST_ID = "requestId"; // 视频消息原始的requestId,kafka重发后沿用这个Id,方便查日志
    public static final String MSG_SRC = "videoMsgSrc"; // 视频消息来源
    public static final String MSG_SRC_AI_SAAS = "ai-saas";
    public static final String MSG_SRC_IOT_SERVICE = "iot-service";
    @Autowired
    private BirdTabService birdTabService;

    @Autowired
    private IAiVideoObjectService aiVideoObjectService;

    /**
     * 判断是否使用新逻辑
     * 白名单可能会变化，但是同一个视频不能使用2种处理逻辑
     */
    public boolean isEnable(String sn, String traceId) {
//        return traceId2EnableCache.get(traceId, k -> {
//            if (videoGenerateConfig.isEnableSn(sn)) return true;
//            UserRoleDO userRole = userRoleService.getDeviceAdminUserRole(sn);
//            if (userRole == null) return false;
//            return videoGenerateConfig.isEnableUserId(userRole.getAdminId());
//        });
        return true; // 去掉视频灰度开关
    }

//    /**
//     * 设备的视频缓存开关。按视频维度确定是否走新逻辑。
//     * 使用这个map时不加锁，使用并发集合
//     */
//    @Getter
//    private Cache<String, Boolean> traceId2EnableCache;

    /**
     * 设备当前的视频录制信息。保存：封面图、切片、设备事件
     * 1、一个设备同一时间只可能录制一个视频
     * 2、有可能下一个视频已经开始录制，但是上一个视频的AI结果依然需要处理
     */
    @Getter
    private Cache<String, DeviceCache> sn2DeviceCache;

    @PostConstruct
    public void init() {
//        traceId2EnableCache = Caffeine.newBuilder().executor(executor).ticker(timeTicker)
//                .expireAfterWrite(VIDEO_ENABLE_EXPIRATION_MILLIS, TimeUnit.MILLISECONDS)
//                .build();
        /*** 这个日志占了新逻辑日志量的95% **/
//                .<String, Boolean>removalListener((traceId, enable, cause) -> {
//                    log.info("traceId2EnableCache evict! cause={},traceId={},enable={}", cause, traceId, enable);
//                }).build();
        sn2DeviceCache = Caffeine.newBuilder().executor(executor).ticker(timeTicker)
                .expireAfterAccess(DEVICE_CACHE_EXPIRATION_MILLIS, TimeUnit.MILLISECONDS)
                .<String, DeviceCache>removalListener((sn, deviceCache, cause) -> {
                    PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceCacheEvictCount()
                            .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), cause.name()).inc()));
                }).build();
        log.info("videoGenerateService init! serverId={}", serverId);
    }

    public DeviceCache getDeviceCache(String sn) {
        return sn2DeviceCache.get(sn, it -> new DeviceCache(sn, timeTicker.readMillis()));
    }

    public VideoCache getVideoCache(DeviceCache deviceCache, String traceId) {
        return deviceCache.getTraceId2VideoCache().computeIfAbsent(traceId,
                it -> new VideoCache(deviceCache.getSerialNumber(), traceId, timeTicker.readMillis()));
    }

    /**
     * 把视频消息发送到kafka中
     * 不同实例的http接口可能接收到同一个视频的http消息。用这个方法发送到kafka中
     */
    public void sendVideoMsg(Integer userId, VideoMsgType type, String traceId, java.lang.Object obj) {
        try {
            JSONObject root = (JSONObject) JSON.toJSON(obj);
            root.fluentPut("type", type.getCode());
            root.fluentPut(MSG_SRC, MSG_SRC_IOT_SERVICE);
            root.fluentPut(MSG_REQUEST_ID, MDC.get(MDCKeys.REQUEST_ID));
            root.fluentPut("userId", userId);
            root.fluentPut("traceId", traceId);
            mqSender.send(videoGenerateTopicByUserId, userId, root);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "sendVideoGenerateMsg error! type={},traceId={},value={}", type, traceId, JSON.toJSONString(obj));
        }
    }

    /**
     * 接收kafka中的视频消息
     * key都是traceId，由kafka的分区策略保证同一个视频的所有消息都由一个实例处理
     */
    @KafkaListener(topics = "${spring.kafka.topics.video-generate}", groupId = "video-generate", autoStartup = "${iot.consumer:false}", id = "video-generate-service")
    public void handleVideoMsg(ConsumerRecord<String, String> record) {
        MDC.put(MDCKeys.SERVER_ID, serverId);
        MDC.put(MDCKeys.TRACE_ID, record.key());
        String traceId = record.key();
        final String value = record.value();
        final JSONObject root = JSON.parseObject(value);
        handleVideoMsg(traceId, value, root);
    }

    @KafkaListener(topics = "${spring.kafka.topics.video-generate-by-user-id}", groupId = "video-generate", autoStartup = "${iot.consumer:false}", id = "video-generate-service-by_user-id")
    @ForwardKafkaMsg
    public void handleVideoMsgByUserId(ConsumerRecord<String, String> record) {
        final String value = record.value();
        final JSONObject root = JSON.parseObject(value);
        if(root == null) {
            com.addx.iotcamera.util.LogUtil.error(log, "handleVideoMsgByUserId begin! root is null");
            return;
        }
        String traceId = root.getString("traceId");
        MDC.put(MDCKeys.SERVER_ID, serverId);
        MDC.put(MDCKeys.TRACE_ID, traceId);
        if(traceId == null) {
            log.error("handleVideoMsg::traceId is null");
            return;
        }
        handleVideoMsg(traceId, value, root);
    }

    public void handleVideoMsg(String traceId, String value, JSONObject root){
        log.debug("handleVideoMsg begin! traceId={},value={}", traceId, value);
        if (!JSON.isValidObject(value)) {
            com.addx.iotcamera.util.LogUtil.error(log, "handleVideoMsg begin! 不认识的消息格式! key={},value={}", traceId, value);
            return; // 不认识的消息格式
        }
        final String requestId = Optional.ofNullable(root.getString(MSG_REQUEST_ID)).orElseGet(IDUtil::uuid);
        MDC.put(MDCKeys.REQUEST_ID, requestId);
        final VideoMsgType type = VideoMsgType.codeOf(root.getString("type"));
        if (type == null) {
            com.addx.iotcamera.util.LogUtil.error(log, "handleVideoMsg begin! 不认识的type! key={},value={}", traceId, value);
            return; // 不认识的type
        }
        if (MSG_SRC_AI_SAAS.equals(Optional.ofNullable(root.getString(MSG_SRC)).orElse(type.getMsgSrc()))) {
            openApiWebhookService.callWebhookForSaasAiResult(type, traceId, root);
        }
        if (resendVideoMsg(type, traceId, root)) {
            log.debug("handleVideoMsg begin! resend key={},value={}", traceId, value);
            return; // 如果需要重发消息，则不消费
        }
        final String sn = type.getSnGetter().apply(root);
        if (StringUtils.isBlank(sn)) {
            com.addx.iotcamera.util.LogUtil.error(log, "handleVideoMsg begin! 消息体中找不到sn! key={},value={}", traceId, value);
            return; // 消息体中找不到sn
        }
        MDC.put(MDCKeys.SERIAL_NUMBER, sn);
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoMsgBeginCount()
                .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), type.getCode()).inc()));
        final DeviceCache deviceCache = getDeviceCache(sn);
        final DeviceTask task = new DeviceTask(requestId, sn, traceId, type, value, timeTicker.readMillis());
        deviceCache.offerTask(task);
        executor.execute(() -> executeDeviceTasks(deviceCache)); // 异步执行，不阻塞kafka消费
    }

    /**
     * 重发视频消息，以确保一个视频由一个实例消费
     */
    public boolean resendVideoMsg(VideoMsgType type, String traceId, JSONObject root) {
        Integer userId = root.getInteger("userId");
        if(userId == null) {
            userId = root.getInteger("user_id");
        }
        Integer ownerId = root.getInteger("ownerId");
        if(ownerId == null) {
            ownerId = root.getInteger("owner_id");
        }
        String msgSrc = Optional.ofNullable(root.getString(MSG_SRC)).orElse(type.getMsgSrc());
        if (MSG_SRC_IOT_SERVICE.equals(msgSrc)) return false; // 来源于iot-service自身的不需要重发
        if(userId != null) {
            sendVideoMsg(userId, type, traceId, root);
        } else {
            sendVideoMsg(ownerId, type, traceId, root);
        }
        return true;
    }

    /**
     * 根据消息类型找到合适的处理器
     *
     * @param type    消息类型
     * @param content 消息体
     * @return
     */
    private BiFunction<DeviceCache, VideoCache, VideoHandleResult> getDeviceTaskHandler(VideoMsgType type, String content) {
        log.debug("getDeviceTaskHandler begin! type={},content={}", type, content);
        switch (type) {
            case IMAGE_REPORT: {
                VideoImageDO videoImage = JSON.parseObject(content, VideoImageDO.class);
                return (deviceCache, video) -> handleImageReport(deviceCache, video, videoImage, content.indexOf("coverImageName") >=0 );
            }
            case SLICE_REPORT: {
                VideoSliceDO videoSlice = JSON.parseObject(content, VideoSliceDO.class);
                return (deviceCache, video) -> handleSliceReport(deviceCache, video, videoSlice);
            }
            case UPLOAD_COMPLETE: {
                return (deviceCache, video) -> handleUploadComplete(deviceCache, video, content);
            }
            case AI_RECOGNITION_RESULT: {
                SaasAITaskOM input = JSON.parseObject(content, SaasAITaskOM.class);
                return (deviceCache, video) -> aroundAiResultHandler(video, () -> handleAIRecognitionResult(deviceCache, video, input));
            }
            case SMART_ALERT_PRO:{
                SaasAITaskOM input = JSON.parseObject(content, SaasAITaskOM.class);
                return (deviceCache, video) -> aroundAiResultHandler(video, () -> handlerSmartAlertProEventResult(deviceCache, video, input));
            }
            case AI_EVENT_RESULT: {
                SaasAITaskOM input = JSON.parseObject(content, SaasAITaskOM.class);
                if(content.contains("aiExtensionResult")) {
                    input.setAiExtensionResult(ProtobufJsonUtils.jsonToMessage(JSONObject.parseObject(content).getString("aiExtensionResult"), VideoEvent.class));
                }
                return (deviceCache, video) -> aroundAiResultHandler(video, () -> handleAIEventResult(deviceCache, video, input));
            }
            case REPORT_EVENT: {
                DeviceReportEventDO reportEvent = JSON.parseObject(content, DeviceReportEventDO.class);
                return (deviceCache, video) -> handleReportEvent(deviceCache, video, reportEvent);
            }
            default: {
                return null;
            }
        }
    }

    // 环绕ai结果处理器，进行未推送原因的收集
    private VideoHandleResult aroundAiResultHandler(VideoCache video, Supplier<VideoHandleResult> handler) {
        video.getPushProcessRecorder().getValidFailList().clear();
        try {
            return handler.get();
        } finally {
            if (!video.getPushProcessRecorder().getValidFailList().isEmpty()) { // 对于没有任何推送的视频，打印它的所有信息
                log.info("aiResultNotPush! sn={},adminId={},hasAiAbility={},traceId={},reasons={},pirNotifyPushedKeys={}"
                        , video.getSerialNumber(), video.getAdminId(), video.getHasAiAbility(), video.getTraceId()
                        , JSON.toJSONString(video.getPushProcessRecorder()), JSON.toJSONString(video.getPirNotifyPushedKeys()));
            }
        }
    }

    /**
     * 执行设备缓存中的任务
     * 按sn维度加锁，拿到锁的线程执行剩余的所有任务，避免竞争
     */
    public void executeDeviceTasks(DeviceCache deviceCache) {
        MDC.put(MDCKeys.SERVER_ID, serverId);
        MDC.put(MDCKeys.SERIAL_NUMBER, deviceCache.getSerialNumber());
        for (int i = 0; !deviceCache.getTaskQueue().isEmpty(); i++) {
            /*** 如果释放锁后发现任务队列不为空，重新去执行。
             确保其他线程可以通过判断是否上锁，来确定任务被添加到队列后是否一定能被执行!
             */
            if (i >= executeDeviceTaskMaxLoopNum) { // 避免单个设备的任务长期占用线程池里的一个线程
                executor.execute(() -> executeDeviceTasks(deviceCache)); // 异步执行，不阻塞kafka消费
                break;
            }
            if (!lockingExecuteDeviceTasks(deviceCache, i)) break; // 没拿到锁，让其他线程执行
        }
        MDC.clear();
    }

    /**
     * 异步执行日志占了videoGenerate日志的一半。需要优化日志打印，累计多次后打印一次
     */
    private AsyncTaskExecuteLogger.Config asyncTaskExecuteLoggerConfig = AsyncTaskExecuteLogger.Config.builder()
            .numKeys(Arrays.stream(VideoHandleResult.values()).map(it -> it.getNumKey()).toArray(String[]::new))
            .logPrinter(data -> {
                final Integer totalNum = (Integer) data.get("totalNum");
                final double avgQueuedCost = ((Long) data.get("sumQueuedCost")).doubleValue() / totalNum;
                final double avgSumExeCost = ((Long) data.get("sumExeCost")).doubleValue() / totalNum;
                final String type = (String) data.get("type");
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceTaskQueuedCostTimeHistogramOptional()
                        .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), type).observe(avgQueuedCost)));
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceTaskExeCostTimeHistogramOptional()
                        .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), type).observe(avgSumExeCost)));
                for (final VideoHandleResult result : VideoHandleResult.values()) {
                    final Integer num = (Integer) data.get(result.getNumKey());
                    if (num == null || num <= 0) continue;
                    PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoMsgExeCount()
                            .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), type, result.name()).inc(num)));
                }
            })
            .name(REPORT_TYPE_EXECUTE_DEVICE_TASKS).minPeriod(1000).build();

    /**
     * 锁定执行设备的任务
     *
     * @return 是否拿到锁。不管执行的内容
     */
    public boolean lockingExecuteDeviceTasks(final DeviceCache deviceCache, final int runIndex) {
        boolean locked = false;
        try {
            if (!(locked = deviceCache.tryLock())) {
                return false; // 获取锁失败则直接退出，不等待。放在try-finally中，防止未解锁
            }
            final AsyncTaskExecuteLogger exeLogger = AsyncTaskExecuteLogger.getInstance(asyncTaskExecuteLoggerConfig);
            DeviceTask task;
            while ((task = deviceCache.pollTask()) != null) {
                MDC.put(MDCKeys.REQUEST_ID, task.getRequestId());
                MDC.put(MDCKeys.TRACE_ID, task.getTraceId());
//                log.info("executeDeviceTasks begin! runIndex={},task={}", runIndex, task.toSimpleDesc());
                exeLogger.logExecuteBegin(task.getQueuedTime());
                final VideoCache video = getVideoCache(deviceCache, task.getTraceId());
                if (!videoCacheService.initVideoCache(video, deviceCache.getCommonCache())) {
                    continue; // 初始化失败，跳过
                }
                final VideoCache.ExeStep handleTypeStep = video.loggingStep("handle:" + task.getType());
                try {
                    BiFunction<DeviceCache, VideoCache, VideoHandleResult> handler = getDeviceTaskHandler(task.getType(), task.getValue());
                    VideoHandleResult result = handler != null ? handler.apply(deviceCache, video) : NOT_HANDLER;
                    log.debug("executeDeviceTasks end! result={},runIndex={},task={},video={},value={}", result, runIndex, task.toSimpleDesc(), video.toSimpleDesc(), task.getValue());
                    handleTypeStep.exeEnd();
                } catch (Throwable e) {
                    handleTypeStep.exeEnd();
                    exeLogger.logExecuteEnd(task.getType().getCode(), ERROR.ordinal(), video.clearExeSteps());
                    com.addx.iotcamera.util.LogUtil.error(log, "executeDeviceTasks error! runIndex={},task={},video={},value={}", runIndex, task.toSimpleDesc(), video.toSimpleDesc(), task.getValue(), e);
                }
            }
            if (runIndex % 10 == 0) { // 避免清理过于频繁
                clearDeviceCache(deviceCache);
            }
        } finally {
            if (locked) {
                deviceCache.unlock(); // 解锁
            }
        }
        return true;
    }

    /*** 处理老视频: 生成超过1分钟 且 其它更新事件未全部执行 */
    public void clearDeviceCache(DeviceCache deviceCache) {
        long currentMillis = timeTicker.readMillis();
        final long minCreateTimeForTimeout = currentMillis - LIBRARY_UPDATE_TIMEOUT_MILLIS;
        final long minCreateTimeForExpiration = currentMillis - VIDEO_CACHE_EXPIRATION_MILLIS;
        Iterator<VideoCache> iterator = deviceCache.getTraceId2VideoCache().values().iterator();
        while (iterator.hasNext()) {
            VideoCache video = iterator.next();
            if (video.getCreateTime() < minCreateTimeForTimeout && !video.isLibraryUpdateAllExecuted()) {
                /* debug：出现了把tags覆盖了的情况，先注释掉
                updateVideo(video, LIBRARY_UPDATED_ON_TIMEOUT); // 视频产生超时，更新视频表
                */
                /*
                if (!(video.getFlag(LIBRARY_UPDATED_ON_TIMEOUT)
                        && !videoCacheService.resumeVideoData(video)
                        && !video.getFlag(LIBRARY_CREATED))) {
                    // 符合updateVideo的判断条件才能进入到这里
                    log.info("updateVideoOnTimeOut! video={}", JSON.toJSONString(video));
                }
                */
            }
            if (video.getCreateTime() < minCreateTimeForExpiration) {
                iterator.remove(); // 视频缓存超过最大时间，清除
//                log.info("traceId2VideoCache evict! video={}", video.toSimpleDesc());
            }
        }
    }

    /**
     * 创建视频，只在收到切片时调用
     *
     * @return 视频表是否已经插入成功,与LIBRARY_CREATED一致
     */
    public boolean createVideo(VideoCache video) {
        if (video.getFlag(LIBRARY_CREATED)) return true; // 已创建，直接返回
        final String videoCreatedKey = REDIS_KEY_VIDEO_CREATED.replace("{traceId}", video.getTraceId());
        if (REDIS_VALUE_VIDEO_CREATE_OK.equals(newRedisService.get(videoCreatedKey))) {
            video.setFlag(LIBRARY_CREATED); // 已被其它实例创建
            return true; // 已被其它实例创建，直接返回
        }
        VideoStoreService.CreateResult result = videoStoreService.createVideo(video);
        if (result == VideoStoreService.CreateResult.CREATED) { // 创建成功
            video.setFlag(LIBRARY_CREATED); // 创建视频成功
            video.setFlag(RESUME_SUCCESS);  // 新创建视频标记为已恢复
            if (video.getReceiveAllSlice()) {
                video.setFlag(LIBRARY_UPDATED_ON_SLICE_FULL); // 如果新创建视频的切片完整，则标记已更新
            }
            video.setFlag(LIBRARY_UPDATED_ON_SLICE); // 视频创建后
            newRedisService.set(videoCreatedKey, REDIS_VALUE_VIDEO_CREATE_OK, Duration.ofMillis(VIDEO_COMMON_CACHE_EXPIRATION_MILLIS)); // 10秒过期
            return true;
        } else if (result == VideoStoreService.CreateResult.DUPLICATE_KEY) { // 遇到重复traceId
            video.setFlag(LIBRARY_CREATED); // 标记创建成功
            videoCacheService.resumeVideoData(video); // 恢复老数据
            video.setFlag(LIBRARY_UPDATED_ON_SLICE); // 视频恢复后
            return true;
        }
        return false;
    }

    /**
     * 更新视频。视频创建成功才更新，且每个flag只更新一次
     * @return 更新操作是否执行，与flag一致。不管sql是否成功，防止反复执行影响性能
     */
    public boolean updateVideo(VideoCache video, VideoCache.Flag flag) {
//        log.debug("updateVideo begin! video={},flag={}", video, flag);
        if (video.getFlag(flag)) return true; // 视频已更新
        if (!videoCacheService.resumeVideoData(video)) return false; // 恢复视频失败，不更新视频
        if (!video.getFlag(LIBRARY_CREATED)) return false; // 视频未创建，不更新视频
        videoStoreService.updateVideo(video, flag.name());
        video.setFlag(flag); // 标记视频已更新
        video.setFlag(LIBRARY_UPDATED_ON_SLICE); // 视频更新后
        return true;
    }

    /*** 视频事件处理逻辑 begin */

    /**
     * 1、处理视频封面图上报
     * 老逻辑：
     * videoService.handleVideoSliceUpdateAsync(image.getTraceId(), image.getSerialNumber(), image.getImageUrl(), Arrays.asList());
     */
    public VideoHandleResult handleImageReport(DeviceCache deviceCache, VideoCache video, VideoImageDO image, boolean fromAi) {
        //        final boolean fromAi = Optional.ofNullable(image).map(VideoImageDO::getCoverImage)
//                .map(VideoImageDO.CoverImage::getCoverImageName).isPresent();
        if (fromAi) {
            if (videoCacheService.setImageUrlFromAI(video, image.getImageUrl())) {
                videoStoreService.updateImageUrlByTraceId(video.getAdminId(), video.getTraceId(), video.getImageUrl(), true);
            }
            return SUCCESS;
        }
        // 新增加字段，kafka消息中的可能还没有，需要兼容
        image.setVideoType(VideoTypeEnum.codeOf(image.getVideoType()).getCode());
        if (videoCacheService.setImageUrlFromDevice(video, image.getImageUrl(), image.getVideoType())) {
            retryNotifyVideoReportEvent(video); // 收到封面图后重新发门铃或一键呼叫事件
            videoStoreService.updateImageUrlByTraceId(video.getAdminId(), video.getTraceId(), video.getImageUrl(), false);
        }
        videoCacheService.saveSnapshotImageUrlToRedis(image);
        return SUCCESS;
    }

    /**
     * 2、处理视频切片上报
     * 老逻辑：
     * List<VideoSliceDO> sliceList = new ArrayList<>(Arrays.asList(slice));
     * videoService.handleVideoSliceUpdateAsync(slice.getTraceId(), slice.getSerialNumber(), slice.getImageUrl(), sliceList);
     */
    public VideoHandleResult handleSliceReport(DeviceCache deviceCache, VideoCache video, VideoSliceDO slice) {
        // 新增加字段，kafka消息中的可能还没有，需要兼容
        slice.setVideoType(VideoTypeEnum.codeOf(slice.getVideoType()).getCode());
        slice.setEndUtcTimestampMillis(VideoSliceDO.computeEndUtcTimestampMillis(slice));
        if (videoCacheService.setImageUrlFromDevice(video, slice.getImageUrl(), slice.getVideoType())) {
            retryNotifyVideoReportEvent(video); // 收到封面图后重新发门铃或一键呼叫事件
        }
        if (!video.addSlice(slice)) {
            return SKIP; // 重复收到切片
        }
        if(slice.getActivateZoneIds() != null && !slice.getActivateZoneIds().isEmpty()){
            video.addActivatedZoneId(slice.getActivateZoneIds());
        }
        videoCacheService.extractStoreBucket(video, slice.getVideoUrl());
        slice.setUserId(video.getAdminId());
        VideoSliceDO.fillTimestampIfNull(slice);
        slice.setTtlTimestamp(slice.getTimestamp() + (int) Duration.ofDays(video.getRollingDays()).getSeconds());
        final VideoCache.ExeStep saveVideoSliceStep = video.loggingStep("saveVideoSlice");

        boolean needSaveVideoSlice = BooleanUtils.isNotTrue(slice.getBatchInsert()) ? true : video.getSliceNum() <= 1;
        if (needSaveVideoSlice && !videoStoreService.saveVideoSlice(slice)) {
            saveVideoSliceStep.exeEnd();
            return FAIL; // 保存切片失败
        }
        saveVideoSliceStep.exeEnd();
        video.setMediaType(slice.getMediaType());
        createVideo(video); // 首次收到切片，创建新视频
        if (video.getSliceNum() == 1) {
            videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_S3_EVENT, slice.getOrder(), slice.getS3EventTime());
        }
        if (video.getReceiveAllSlice()) {
            updateVideo(video, LIBRARY_UPDATED_ON_SLICE_FULL); // 切片收全，更新视频表
        } else if (video.isSnapshotRecording()) {
            video.clearFlag(LIBRARY_UPDATED_ON_SLICE);
            updateVideo(video, LIBRARY_UPDATED_ON_SLICE); // snapshot视频每个切片都更新
        } else if (slice.getIsLast()) {
            updateVideo(video, LIBRARY_UPDATED_ON_LAST_SLICE); // 收到最后一个切片，更新视频表
        } else {
            video.clearFlag(LIBRARY_UPDATED_ON_SLICE); // 收到切片但是没更新，标记一下
        }
        try {
            // 收到非子画面视频的首个切片时，更新其它视频
            if (!video.isEventRecordingSubView() && video.getSliceNum() == 1) {
                for (VideoCache otherVideoCache : deviceCache.getTraceId2VideoCache().values()) {
                    if (otherVideoCache.getTraceId().equals(video.getTraceId())) continue;
                    updateVideo(otherVideoCache, LIBRARY_UPDATED_ON_SLICE);
                }
            }
        } catch (Throwable e) {
            log.error("handle otherVideoCache error!", e);
        }
        log.debug("handleSliceReport={}", JSON.toJSONString(video));
        if (video.getHasAiAbility()) {
            videoAIService.sendSaasAiTask(video, slice); // 发送AI分析任务
            if (video.getReceiveAllSlice()) {
                videoAIService.sendLastSaasAiTask(video); // 切片收全，发送AI汇总任务
            }
            // 对于方案产品， 如果只开启other通知， 但是没有开拍过滤也没有tag对象，则推送运动通知
            if(!AppConstants.TENANTID_SAFEMO.equals(video.getTenantId())
                    && video.getEnableOther()
                    && video.getNotifyEventObjects().isEmpty()
                    && video.getDefaultSaasAITask().getRecognitionObjects().isEmpty()) {
                videoNotifyService.sendMotionNotify(video);
            }
        } else {
            if(AppConstants.TENANTID_SAFEMO.equals(video.getTenantId())) {
                safemoAiNotificationService.sendNotify(video, null);
            }else {
                videoNotifyService.sendMotionNotify(video); // 非vip，设备端也没有检测出结果 推送"运动检测"
            }
        }
        videoNotifyService.sendMotionNotifyToAlexa(video);
        return SUCCESS;
    }

    /**
     * 3、处理视频完成上报
     * 老逻辑：
     * videoService.videoUploadComplete(request);
     */
    public VideoHandleResult handleUploadComplete(DeviceCache deviceCache, VideoCache video, String content) {
        if (video.getFlag(LIBRARY_UPDATED_ON_SLICE_FULL)) {
            video.setFlag(LIBRARY_UPDATED_ON_UPLOAD_COMPLETE); //如果已经因为切片齐全而更新过，完成上报时就没必要重复更新了
        } else {
            updateVideo(video, LIBRARY_UPDATED_ON_UPLOAD_COMPLETE); // 收到完成上报，更新视频表
        }
        videoAIService.sendLastSaasAiTask(video); // 收到完成上报，发送AI汇总任务

        final JSONObject reqBody = JSON.parseObject(content);
        final UploadVideoCompleteRequest request = reqBody.toJavaObject(UploadVideoCompleteRequest.class);
        UploadVideoCompleteRequest.preHandle(request);
        // 设备端上传切片失败会重试,同一个order可能会有多条记录，但最多有一条是成功的
        List<VideoSliceRequest> sliceList = request.getSliceList().stream()
                .collect(Collectors.groupingBy(it -> it.getOrder())).values().stream()
                // 同一个order的记录中，优先使用上传成功的那条记录
                .map(it -> it.stream().max(Comparator.comparingInt(it2 -> it2.getUploadSuccess())).get())
                .sorted(Comparator.comparingInt(it -> it.getOrder())).collect(Collectors.toList());

        int sliceUploadSuccessNum = 0;
        long recordingTime = 0;
        long beforeUploadingTime = 0;
        long uploadingTime = 0;
        long s3NotifyCostTime = 0;
        Integer lastOrder = null;
        BigDecimal period = BigDecimal.ZERO;
        int fileSize = 0;
        for (VideoSliceRequest sliceRequest : sliceList) {
            sliceUploadSuccessNum += sliceRequest.getUploadSuccess();
            recordingTime += DateUtils.diffMillis(sliceRequest.getStartRecordingTimestamp(), sliceRequest.getEndRecordingTimestamp());
            beforeUploadingTime += DateUtils.diffMillis(sliceRequest.getEndRecordingTimestamp(), sliceRequest.getStartUploadingTimestamp());
            uploadingTime += DateUtils.diffMillis(sliceRequest.getStartUploadingTimestamp(), sliceRequest.getEndUploadingTimestamp());
            if (sliceRequest.getIsLast()) {
                lastOrder = sliceRequest.getOrder();
            }
            VideoCache.SliceCache slice = video.getOrder2Slice().get(sliceRequest.getOrder());
            if (slice != null) {
                s3NotifyCostTime += DateUtils.diffMillis(sliceRequest.getEndUploadingTimestamp(), slice.getS3EventTime());
            }
            if (sliceRequest.getUploadSuccess() > 0) {
                period = period.add(sliceRequest.getPeriod());
                fileSize += sliceRequest.getFileSize();
            }

            // 统计单个切片上传的成功情况
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
                final String[] labels = {
                        PrometheusMetricsUtil.getHostName(),
                        request.getServiceName(),
                        video.getStoreBucket() != null ? video.getStoreBucket().getBucket() : "unknown",
                        video.getStoreBucket() != null ? video.getStoreBucket().getRegion() : "unknown",
                        sliceRequest.getUploadSuccess() + "",
                        sliceRequest.getOrder() + "",
                        sliceRequest.getIsLast() + "",
                };
                PrometheusMetricsUtil.getVideoSliceUploadCountOptional().ifPresent(it -> it.labels(labels).inc());
            });
        }
        final int num = sliceList.size();

        request.setServiceNameIfBlank(Optional.ofNullable(video.getServiceName()).orElse("unknown"));
        MapUtil.MapBuilder reportDataBuilder = MapUtil.builder()
                .putAll(reqBody.fluentRemove("sliceList")) // 透传所有字段，除了sliceList
                .put("traceId", request.getTraceId())
                .put("serialNumber", request.getSerialNumber())
                .put("serviceName", request.getServiceName())
                .put("bucket", video.getStoreBucket() != null ? video.getStoreBucket().getBucket() : "unknown")
                .put("region", video.getStoreBucket() != null ? video.getStoreBucket().getRegion() : "unknown")
                .put("s3AddressReceivedTimestamp", request.getS3AddressReceivedTimestamp())
                .put("totalStartRecordingTimestamp", request.getTotalStartRecordingTimestamp())
                .put("totalEndRecordingTimestamp", request.getTotalEndRecordingTimestamp())
                .put("sliceTotalNum", sliceList.size())
                .put("sliceUploadSuccessNum", sliceUploadSuccessNum)
                .put("recordingTime", recordingTime)
                .put("beforeUploadingTime", beforeUploadingTime)
                .put("uploadingTime", uploadingTime)
                .put("receiveS3EventNum", video.getSliceNum())
                .put("s3NotifyCostTime", s3NotifyCostTime)
                .put("saveSuccess", true)
                .put("lastOrder", lastOrder) // 最后一个切片的编号，如果录制提前终止，就为Null
                .put("s3AddressReceived2TotalStartRecording", DateUtils.diffMillis(request.getS3AddressReceivedTimestamp(), request.getTotalStartRecordingTimestamp()))
                .put("totalStartRecording2TotalEndRecording", DateUtils.diffMillis(request.getTotalStartRecordingTimestamp(), request.getTotalEndRecordingTimestamp()));
        // 设备端试生成traceId后，收到complete前可能没有调pir,recordPirTriggerTime没执行
        JSONObject data = video.getPirAttachment();
        Long mqttEventTime = data.getLong(S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME);
        Long pirStartTimestamp = data.getLong(S3_VIDEO_SLICE_INFO_PIR_START_TIME);
        Long pirEndTimestamp = data.getLong(S3_VIDEO_SLICE_INFO_PIR_END_TIME);
        if (mqttEventTime != null && pirStartTimestamp != null && pirEndTimestamp != null) {
            reportDataBuilder = reportDataBuilder
                    .put("time", mqttEventTime) // 设备端pir触发时间
                    .put("pirStartTimestamp", pirStartTimestamp)
                    .put("pirEndTimestamp", pirEndTimestamp)
                    .put("time2PirStart", DateUtils.diffMillis(mqttEventTime, pirStartTimestamp))
                    .put("pirStart2PirEnd", DateUtils.diffMillis(pirStartTimestamp, pirEndTimestamp))
                    .put("pirEnd2S3AddressReceived", DateUtils.diffMillis(pirEndTimestamp, request.getS3AddressReceivedTimestamp()));
        }
        /*** 记录视频统计 recordVideoSummary **/
        reportDataBuilder.put("segmentNums", num).put("period", period)
                .put("needAi", video.getHasAiAbility() && !video.getDefaultSaasAITask().getRecognitionObjects().isEmpty());
        videoReportLogService.infoSysReport(REPORT_TYPE_VIDEO_UPLOAD_COMPLETE, reportDataBuilder.build());

        final boolean sliceUploadSuccessIsEmpty = sliceUploadSuccessNum == 0;
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
            final String[] labels = {
                    PrometheusMetricsUtil.getHostName(),
                    request.getServiceName(),
                    video.getStoreBucket() != null ? video.getStoreBucket().getBucket() : "unknown",
                    video.getStoreBucket() != null ? video.getStoreBucket().getRegion() : "unknown",
                    sliceUploadSuccessIsEmpty + "",
            };
            PrometheusMetricsUtil.getVideoUploadCompleteCountOptional().ifPresent(it -> it.labels(labels).inc());
        });
        return SUCCESS;
    }

    /**
     * 4、处理AI对象识别结果
     * 老逻辑：
     * saasAIService.handleAIRecognitionResult(input);
     */
    public VideoHandleResult handleAIRecognitionResult(DeviceCache deviceCache, VideoCache video, SaasAITaskOM input) {

        log.debug("handleAIRecognitionResult video {} input {}", JSON.toJSONString(video), input);

        videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_AI_RESULT, input.getOrder(), timeTicker.readMillis());
        videoReportLogService.reportSliceAiTaskDuration(video, REPORT_TYPE_NOTIFY_AI_TO_RESULT, input.getOrder(), timeTicker.readMillis());

        if (video.getIsWowza()) {
            video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "跳过wowza视频的AI对象识别结果");
            return SKIP;
        }
        List<AiTaskResult> aiTaskResults = new LinkedList<>();
        for (SaasAITaskOM.ImageOM imageOM : input.getImages()) {
            if (StringUtils.isBlank(imageOM.getImageUrl())) {
                video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "AI对象识别的图片url为空");
                log.info("handleAIRecognitionResult the image url for AI object recognition is empty SaasAITaskOM {}", input);
                continue;
            }
            for (SaasAITaskOM.RecognisedObject recogObj : imageOM.getObjects()) {
                if (recogObj.getCategory() == null) {
                    video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "收到未知objectCategory");
                    //问了ai说没有例外的category，打日志看一下是什么数据
                    log.error("handleAIRecognitionResult 收到未知objectCategory sn={},traceId={}, object={}", input.getDeviceSn(), input.getTraceId(), JSON.toJSON(recogObj));
                    continue;
                }
                if (recogObj.getCategory() != RecognitionObjectCategory.PERSON) {
                    video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "非person对象事件统一到汇总事件处理");
                    log.debug("handleAIRecognitionResult delay push except person");
                    continue; // 非 人的事件汇总时再推送
                }
                // id.enable 是否开启reId功能
                String labelId = !recogObj.getId().getEnable() ? "" :
                        Optional.ofNullable(recogObj.getId().getLabelId()).orElse("");
                AiEvent event = new AiEvent()
                        .setEventObject(recogObj.getCategory().getEventObject())
                        .setEventType(AiObjectActionEnum.EXIST)
                        .setActivatedZones(recogObj.getActivityZoneIds())
                        .setLabelId(labelId);
                // 事件无论推没推过，都应该放入最后的事件汇总中
//                video.addRecognitionEvents(event);
                video.addActivatedZoneId(event.getActivatedZones());
                if (CollectionUtils.isNotEmpty(recogObj.getSubCategories())) {
                    PossibleSubcategory possibleSubcategory = new PossibleSubcategory()
                            .setName(recogObj.getSubCategories().get(0).getName()).setConfidence(recogObj.getConfidence());
                    event.setPossibleSubcategory(Arrays.asList(possibleSubcategory));
                }
                List<AiEvent> eventList = new ArrayList<>();
                eventList.add(event);

                AiTaskResult aiTaskResult = new AiTaskResult()
                        .setTraceId(input.getTraceId())
                        .setSerialNumber(input.getDeviceSn())
                        .setImageUrl(imageOM.getImageUrl())
                        //Arrays.asList 返回的列表是固定的，不支持添加和删除。
                        //后边调用addAll 会报 UnsupportedOperationException: null
                        //.setEvents(Arrays.asList(event))
                        .setAiEdgeResult(false)
                        .setEvents(eventList)
                        .setOrder(input.getOrder());
                aiTaskResults.add(aiTaskResult);
            }
        }
//        log.info("handleAIRecognitionResult sn={},traceId={},aiTaskResults={}", input.getDeviceSn(), input.getTraceId(), JSON.toJSONString(aiTaskResults));
        for (AiTaskResult aiTaskResult : aiTaskResults) {
            try {
                // safemo 没有这个任务消息， 可以忽略
                videoNotifyService.sendAIResultNotify(video, aiTaskResult);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleSaasAiTaskResult aiTaskNotify发生未知异常,sn={},traceId={}", input.getDeviceSn(), input.getTraceId(), e);
            }
        }
        return SUCCESS;
    }

    public VideoHandleResult handlerSmartAlertProEventResult(DeviceCache deviceCache, VideoCache video, SaasAITaskOM input) {

        if(!video.getIsEnableSmartAlertPro()) {
            log.info("handlerSmartAlertProEventResult smartAlertPro is not enable, sn={}, traceId={}", input.getDeviceSn(), input.getTraceId());
            return SKIP;
        }

        // 处理short summary
        if(StringUtils.isNotEmpty(input.getDescription()) && StringUtils.isNotEmpty(input.getSafetyLevel().getCode())) {
            video.setSummaryDescription(input.getDescription());
            video.setSafeLevel(input.getSafetyLevel().getCode());
            updateVideo(video, LIBRARY_UPDATED_ON_AI_SMART_ALERT_PRO); // 收到AI事件结果时，更新数据库
        }
        return SUCCESS;
    }

    /**
     * 4、处理AI事件汇总结果
     * 老逻辑：
     * saasAIService.handleAIEventResult(input);
     */
    public VideoHandleResult handleAIEventResult(DeviceCache deviceCache, VideoCache video, SaasAITaskOM input) {

        log.debug("handleAIEventResult deviceCache {} video{} input {}", JSON.toJSONString(deviceCache), JSON.toJSONString(video), input);

        // 如果是safemo的来的事件， 单独处理
        if(AppConstants.TENANTID_SAFEMO.equals(input.getTenantId())
                || input.getAiExtensionResult() != null){
            return handleAIEventResultForSafemo(deviceCache, video, input.getAiExtensionResult());
        }

        // 以下是方案产品的算法处理逻辑，TODO： 二期统一归一化

        videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_AI_RESULT, -1, timeTicker.readMillis());

        String[] eventIndex2LabelId = getEventIndex2LabelId(input.getReIds(), input.getEvents().size());
        List<AiTaskResult> aiTaskResults = new LinkedList<>();
        int eventIndex = -1;
        for (SaasAITaskOM.EventOM eventOM : input.getEvents()) {
            eventIndex++;
            if (eventOM.getObjectCategory() == null) {
                video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "收到未知objectCategory");
                log.warn("handleAIEventResult 收到未知objectCategory sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                continue;
            }
            AiObjectActionEnum eventType = EnumFindUtil.findByEnumName(eventOM.getName(), AiObjectActionEnum.class);
            if (eventType == null) {
                video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "收到未知eventName");
                log.warn("handleAIEventResult 收到未知eventName:{},sn={},traceId={}", eventOM.getName(), input.getDeviceSn(), input.getTraceId());
                continue;
            }
            if (eventOM.getFilterResult() == 1) {
                // 算法端认为要过滤掉的事件
                video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "算法端认为要过滤掉的事件");
                log.debug("handleAIEventResult filter video {}", JSON.toJSONString(video));
                continue;
            }
            // 处理鸟的事件，跟新用户级别的鸟信息
            if(eventOM.getObjectCategory() == RecognitionObjectCategory.BIRD
                    && !eventOM.getPossibleSubcategory().isEmpty()){
//                log.info("handleAIEventResult handle bird event sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                BirdTabDO birdTabDO = new BirdTabDO();
                birdTabDO.setUserId(Integer.valueOf(input.getOwnerId()));
                birdTabDO.setLastTraceId(input.getTraceId());
                birdTabDO.setImgSrcTraceId(input.getTraceId());
                BigDecimal confidence = new BigDecimal(0.0E00);
                for( PossibleSubcategory possibleSubcategory : eventOM.getPossibleSubcategory()){
                    if(confidence.compareTo(possibleSubcategory.getConfidence()) < 0){
                        confidence = possibleSubcategory.getConfidence();
                        birdTabDO.setBirdStdName(possibleSubcategory.getName());
                    }
                }
                birdTabDO.setScore(confidence.floatValue());
                birdTabDO.setBirdImageUrl(eventOM.getSummaryUrl());
                birdTabDO.setLastVisitTime((int)(System.currentTimeMillis()/1000));
                if(birdTabDO.getBirdImageUrl() != null){
                    Map<Integer, Integer> result = birdTabService.insertOrUpdateBirdTabInfo(birdTabDO, video.getUserIds());
                    eventOM.getPossibleSubcategory().forEach(possibleSubcategory -> {
                        if(Objects.equals(birdTabDO.getBirdStdName(), possibleSubcategory.getName())
                                && result.get(video.getAdminId()) == 0){
                            possibleSubcategory.setFirstVisit(true);
                        }else{
                            possibleSubcategory.setFirstVisit(false);
                        }
                    });
                }

            }

            AiEvent event = new AiEvent()
                    .setSummaryUrl(eventOM.getObjectCategory() == RecognitionObjectCategory.BIRD ? eventOM.getSummaryUrl() : null)
                    .setEventObject(eventOM.getObjectCategory().getEventObject())
                    .setEventType(eventType)
                    .setActivatedZones(eventOM.getActivityZoneIds())
                    .setPossibleSubcategory(birdLoversService.handleAiEventPossibleSubcategory(eventOM.getPossibleSubcategory()))
                    .setLabelId(eventIndex2LabelId[eventIndex]);
            // 事件无论推没推过，都应该放入最后的事件汇总中
            video.addEvent(event);
            video.addActivatedZoneId(event.getActivatedZones());
            if (StringUtils.isBlank(eventOM.getSummaryUrl())) {
                // 目前线上的APP推送中，人和宠物不需要事件识别，因此算法端为了节省资源，没传图片过来
                if (eventOM.getObjectCategory() == RecognitionObjectCategory.PERSON) {
                } else if (eventOM.getObjectCategory() == RecognitionObjectCategory.PET) {
                } else {
                    video.getPushProcessRecorder().recordNotPushResult(input.getOrder(), "收到summaryUrl为空");
                    com.addx.iotcamera.util.LogUtil.warn(log, "handleAIEventResult 收到summaryUrl为空! eventName={},sn={},traceId={}", eventOM.getName(), input.getDeviceSn(), input.getTraceId());
                }
                log.debug("handleAIEventResult summaryUrl is null");
                continue;
            }
            List<AiEvent> eventList = new ArrayList<>();
            eventList.add(event);

            AiTaskResult aiTaskResult = new AiTaskResult()
                    .setTraceId(input.getTraceId())
                    .setSerialNumber(input.getDeviceSn())
                    .setImageUrl(eventOM.getSummaryUrl()) // 算法挑选的事件图
                    //Arrays.asList 返回的列表是固定的，不支持添加和删除。
                    //后边调用addAll 会报 UnsupportedOperationException: null
                    //.setEvents(Arrays.asList(event))
                    .setAiEdgeResult(false)
                    .setEvents(eventList)
                    .setOrder(input.getOrder());
            aiTaskResults.add(aiTaskResult);
        }
//        log.info("handleAIEventResult sn={},traceId={},aiTaskResults={}", input.getDeviceSn(), input.getTraceId(), JSON.toJSONString(aiTaskResults));
        for (AiTaskResult aiTaskResult : aiTaskResults) {
            try {
                videoNotifyService.sendAIResultNotify(video, aiTaskResult);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleAIEventResult aiTaskNotify发生未知异常,sn={},traceId={}", input.getDeviceSn(), input.getTraceId(), e);
            }
        }
        // keyshot 处理
        video.addKeyshots(input.getKeyshots());
        videoCacheService.setImageUrlFromAI(video, input.getCoverImageUrl());
        updateVideo(video, LIBRARY_UPDATED_ON_AI_EVENT); // 收到AI事件结果时，更新数据库
        if (video.isMatchAnyActivityZone(video.getEvents())) { // 如果在az中
            if (video.getEnableOther() && video.getPirNotifyPushedKeys().isEmpty()) {
                videoNotifyService.sendMotionNotify(video); // 如果打开了推送其它 且 没推送过，就推送"运动检测"
            }
        }
        return SUCCESS;
    }

    private  VideoHandleResult handleAIEventResultForSafemo(DeviceCache deviceCache, VideoCache video, VideoEvent input) {
        final String sn = input.getDeviceSn();
        final String traceId = input.getTraceId();
        log.info("handleAIEventResultForSafemo begin! sn={},traceId={},objectNum={}", sn, traceId, input.getObjectsCount());
        String aiFeatureName = input.getSource().getName();
        if (video == null) return ERROR;
        final AiTaskResult aiTaskResult = video.createAiTaskResult();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(input.getObjectsList())
                && !AiFeatures.SMART_ALERT_PRO.equals(input.getSource().getName())) {
            log.error("handleAIEventResultForSafemo objects is empty and source is not SMART_ALERT_PRO! sn={},traceId={}", sn, traceId);
            return ERROR;
        }
        // 所有对象
        List<AiObjectDetail> objectDetails = AiObjectActionUtil.aiVideoEvent2ObjectsDetail(input);
        Map<String, JpegImageFile> imageNameToData = AiObjectActionUtil.aiObjectDetail2ImageMap(input, objectDetails);
        // 将图片构造为imageHelper
//        final AIResultImageDataHelper imageDataHelper = new AIResultImageDataHelper(input.getDeviceSn(), input.getTraceId(), imageNameToData);
//        final String summaryImageLocalPath = imageDataHelper.saveImage(input.getSummaryImageName()); // 推送大图
//        final String croppedImageBase64 = imageDataHelper.imageToBase64(input.getCropedSummaryImageName()); // 推送小图
        // 生成关键帧截图

        String objectType = AiObjectActionUtil.getObjectTypeFromVideoEvent(input);
        // 生成tags
        //TODO : 后续确认是否需要
//        video.addTagByEventObject(EnumFindUtil.findByName(objectType, AiObjectEnum.class));

        // 批量添加videoObject
//        List<AiVideoObject> videoObjects = AiObjectActionUtil.objectsDetail2VideoObjects(sn, traceId, aiFeatureName, input.getSummaryImageName(), objectDetails, BaseAIResultImageDataHelper.class);
//        if (!org.apache.commons.collections.CollectionUtils.isEmpty(videoObjects)) {
//            aiVideoObjectService.batchAdd(Integer.valueOf(input.getOwnerId()), videoObjects);
//        }

        // 过滤掉已屏蔽的人脸   TODO: 后期人脸迁移云端才需要统一归一化
//        List<AiObjectNotificationSetting> objectNotificationSettings = objectNotificationSettingDAO.selectByObjectType(FACE.getObjectName());
        List<String> objectIds = new ArrayList<>();
//        if (!org.apache.commons.collections.CollectionUtils.isEmpty(objectNotificationSettings)) {
//            objectIds = objectNotificationSettings.stream().map(AiObjectNotificationSetting::getObjectId).distinct().collect(Collectors.toList());
//        }
        List<AiVideoObjectInfoResult> videoObjectInfoResults;
        if (org.apache.commons.collections.CollectionUtils.isEmpty(objectIds)) {
            videoObjectInfoResults = new ArrayList<>();
        } else {
            videoObjectInfoResults = aiVideoObjectService.queryMuteNotifyObjects(objectIds, Collections.singletonList(FACE.getObjectName()));
        }
        objectDetails = AiObjectActionUtil.filterByObjectNotificationSetting(videoObjectInfoResults, objectDetails);
        // 所有事件
        List<AiObjectAction> aiObjectActions = AiObjectActionUtil.aiObjectsDetail2AiObjectAction(input, objectDetails)
                .stream().map(
                        (aiObjectAction) -> {
                            final AiObjectActionEnum  eventType = EnumFindUtil.findByName(AiObjectActionEnumUtil.getEventNameByObjectAction(aiObjectAction.getObjectAction()), AiObjectActionEnum.class);
                            aiObjectAction.setAction(eventType.getEventAction().getActionName());
                            return aiObjectAction;
                        }
                ).collect(Collectors.toList());

        List<String> actionForLog = aiObjectActions.stream().map((e) -> e.getObjectName() + "_" + e.getObjectAction()).collect(Collectors.toList());
        log.info("handleAIEventResultForSafemo traceId={}, actions={}", traceId, JSON.toJSONString(actionForLog));
        List<List<Integer>> zone = input.getObjectsList().stream().map(Object::getActivityZoneIdsList).collect(Collectors.toList());
        log.info("handleAIEventResultForSafemo traceId={}, zone={}", traceId, JSON.toJSONString(zone));
        for (AiObjectAction action : aiObjectActions) {
            try {
                final AiObjectActionEnum  eventType = EnumFindUtil.findByName(action.getObjectAction(), AiObjectActionEnum.class);
                if (eventType == null) {
                    log.warn("handleAIEventResultForSafemo event error! sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                    continue;
                }

                List<AiObjectAction> sameAiObjectActions = aiObjectActions.stream()
                        .filter((aiObjectAction) -> aiObjectAction.getObjectType().equals(action.getObjectType()))
                        .collect(Collectors.toList());
                final AiEvent event = new AiEvent()
                        .setCurrentSameObjectInstance(sameAiObjectActions)
                        .setEventObject(EnumFindUtil.findByName(action.getObjectType(), AiObjectEnum.class))
                        .setEventType(eventType)
                        .setAiObjectAction(action)
                        .setActivatedZones(action.getActivityZoneIds())
                        .setPossibleSubcategory(new ArrayList<>())
                        .setSummaryUrl(input.getSummaryImageName())
                        .setCropedImageName(input.getCropedSummaryImageName())
//                        .setCropedImageBase64(croppedImageBase64)
                        .setLabelId(null);
                // 添加已经通知过的对象事件实例
                List<AiObjectAction> oldObjectInstance = video.getEvents().stream()
                        .map((AiEvent::getAiObjectAction))
                        // 按照score倒序排序
                        .sorted(Comparator.comparingDouble(AiObjectAction::getScore).reversed())
                        .collect(Collectors.toList());
                event.setOldObjectInstance(oldObjectInstance);
//                if (!aiResultIsUpdateVideo) continue;
                // 事件无论推没推过，都应该放入最后的事件汇总中
                video.addEvent(event);
                video.addActivatedZoneId(event.getActivatedZones());
                aiTaskResult.getEvents().add(event);
                // log.info("handleIotLocalAIResult handle input end! aiTaskResult={}", JSON.toJSONString(aiTaskResult));
            } catch (Throwable e) {
                log.error("handleAIEventResultForSafemo error! sn={},traceId={}", sn, traceId, e);
                return FAIL;
            }
        }
        // 处理short summary
        if(video.getIsEnableSmartAlertPro()) {
            if (StringUtils.isNotEmpty(input.getDescription()) && StringUtils.isNotEmpty(input.getSafetyLevel())) {
                video.setSummaryDescription(input.getDescription());
                video.setSafeLevel(input.getSafetyLevel().toLowerCase());
                if (safemoAiNotificationService.shouldSend()) {
                    safemoAiNotificationService.sendShortSummaryNotification(video, input.getSummaryImageName(), input.getCropedSummaryImageName());
                } else {
                    log.info("handleAIEventResultForSafemo short summary skiped as safePush disabled! sn={},traceId={}", sn, traceId);
                }
            }
        }

        updateVideo(video, LIBRARY_UPDATED_ON_AI_EVENT); // 收到AI事件结果时，更新数据库
        video.clearFlag(LIBRARY_UPDATED_ON_AI_EVENT); // safemo  会有多次更新数据库  todo: 算法改事件类型， 保障所有需要改数据库只事件只能有一次
        if (!safemoAiNotificationService.shouldSend()) {
            log.info("handleAIEventResultForSafemo skip safePush! sn={},traceId={}", sn, traceId);
            return SUCCESS;
        }
        safemoAiNotificationService.sendNotify(video, aiTaskResult);
        return SUCCESS;

    }

    /**
     * 5、处理设备上报事件
     */
    public VideoHandleResult handleReportEvent(DeviceCache deviceCache, VideoCache video, DeviceReportEventDO reportEvent) {
        EReportEvent event = EReportEvent.findByEventId(reportEvent.getValue().getEvent());
        switch (event) {
            case PIR: {
                if (reportEvent.getAttachment() != null) {
                    video.setPirAttachment(reportEvent.getAttachment());
                    if (video.getSliceNum() > 0) {
                        VideoCache.SliceCache slice = video.getOrder2Slice().values().stream().min(Comparator.comparingLong(it -> it.getS3EventTime())).get();
                        videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_S3_EVENT, slice.getOrder(), slice.getS3EventTime());
                    }
                }
                break;
            }
            case DOORBELL_PRESS: {
                video.getDoorbellEvents().add(new VideoReportEvent(event.getEventId()));
                /*** 老逻辑：doorbellService.onPress(event); */
                if (video.getDoorbellPressNotifySwitch()) {
                    videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, video.getDoorbellPressNotifyMsgType());
                    // 发送alexa事件
                    if (!Optional.ofNullable(reportEvent.getAttachment()).map(it -> it.getBoolean(S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA)).orElse(false)) {
                        final VideoCache.ExeStep sendPlatformEventStep = video.loggingStep("sendPlatformEvent");
                        devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DOORBELL_PRESS, new HashMap<>(Collections.singletonMap("serialNumber", reportEvent.getSerialNumber())));
                        sendPlatformEventStep.exeEnd();
                    }
                }
                break;
            }
            case DOORBELL_REMOVE: {
                video.getDoorbellEvents().add(new VideoReportEvent(event.getEventId()));
                /*** 老逻辑：doorbellService.onRemove(event); */
                videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_REMOVE, MsgType.NEW_VIDEO_MSG);
                break;
            }
            case DEVICE_CALL: {
                video.getDeviceCallEvents().add(new VideoReportEvent(event.getEventId()));
                /*** 老逻辑：deviceCallService.call(event.getValue().getTraceId(), event.getSerialNumber()); */
                if (video.getDoorbellPressNotifySwitch()) {
                    videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DEVICE_CALL, video.getDoorbellPressNotifyMsgType());
                }
                break;
            }
            case AI_EDGE_EVENT: {
                String imageUrl = reportEvent.getValue().getImageUrl();
                if(CollectionUtils.isNotEmpty(reportEvent.getValue().getEvents())) {
                    video.getAiEdgeEvents().addAll(reportEvent.getValue().getEvents());
                    imageUrl = reportEvent.getValue().getEvents().get(0).getSummaryUrl();
                }

                // check needWaitAiSaasResult
                boolean needWaitAiSaasResultPush = video.getHasAiAbility();
                if(!needWaitAiSaasResultPush) {
                    AiTaskResult aiTaskResult = new AiTaskResult()
                        .setTraceId(reportEvent.getValue().getTraceId())
                        .setSerialNumber(reportEvent.getSerialNumber())
                        .setImageUrl(imageUrl) // 算法挑选的事件图
                        .setEvents(reportEvent.getValue().getEvents())
                        .setAiEdgeResult(true)
                        .setOrder(-1);
                    if(AppConstants.TENANTID_SAFEMO.equals(video.getTenantId())){
                        safemoAiNotificationService.sendNotify(video, aiTaskResult);
                    } else {
                        videoNotifyService.sendAIResultNotify(video, aiTaskResult);
                    }
                }
                break;
            }
            default: {
                return SKIP;
            }
        }
        return SUCCESS;
    }

    /*** 视频事件处理逻辑 end */

    // 收到封面图后重新发门铃或一键呼叫事件
    public void retryNotifyVideoReportEvent(VideoCache video) {
        try {
            if (video.getDoorbellEvents().stream().anyMatch(it -> EReportEvent.DOORBELL_PRESS.getEventId().equals(it.getEvent()))) {
                if (video.getDoorbellPressNotifySwitch()) {
                    videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_PRESS, video.getDoorbellPressNotifyMsgType());
                }
            }
            if (video.getDoorbellEvents().stream().anyMatch(it -> EReportEvent.DOORBELL_REMOVE.getEventId().equals(it.getEvent()))) {
                videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DOORBELL_REMOVE, MsgType.NEW_VIDEO_MSG);
            }
            if (!video.getDeviceCallEvents().isEmpty()) {
                videoNotifyService.notifyVideoReportEvent(video, EReportEvent.DEVICE_CALL, video.getDoorbellPressNotifyMsgType());
            }
        } catch(Throwable e) {
            LogUtil.error(log, "retryNotifyVideoReportEvent error! video={}", video, e);
        }
    }



}
