package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.dao.user.IUserSettingsDAO;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class UserSettingService {
    private static final Map<String, Long> USER_MARK_MAP = new HashMap<>();
    public static final String TEST_MARK = "TEST_MARK";
    public static final String NOCODB_KEY = "6Fry4HYP3aNunGbWhTpiM6XYlRBRQ-7V4r7OnKIq";
    private static final String NOCODB_API_V1 = "https://nocodb-internal.addx.live/api/v1";
    public static final String APPLICATION_JSON = "application/json";
    public static final String SERVER_ERROR_MSG = "server error";

    static{
        //测试用户标记
        USER_MARK_MAP.put(TEST_MARK, 0L);
    }

    private static final String USER_MARK_REDIS_KEY_PREFIX = "umark::";

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Autowired
    private RedisService redisService;
    @Autowired
    private IUserSettingsDAO iUserSettingsDAO;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserVipService userVipService;

    @Cacheable(value = "userSettingV1", key = "#userId", unless = "#result==null")
    public UserSettingsDO queryUserSetting(Integer userId) {
        log.info("query userSetting {}", userId);
        return iUserSettingsDAO.queryUserSettings(userId);
    }

    @CacheEvict(value = "userSettingV1", key = "#userSettingsDO.userId")
    public boolean updateUserSetting(UserSettingsDO userSettingsDO) {
        return iUserSettingsDAO.updateUserSetting(userSettingsDO) > 0;
    }

    public void insertUserSetting(UserSettingsDO userSettingsDO) {
        iUserSettingsDAO.insertUserSetting(userSettingsDO);
    }

    public Boolean queryUserMark(Integer userId, String key) {
        if (!USER_MARK_MAP.containsKey(key)) {
            throw new BaseException(ResultCollection.INVALID_PARAMS);
        }
        if (TEST_MARK.equals(key)) {
            if(!supportNode(activeProfile)){
                throw new BaseException(ResultCollection.NODE_NOT_SUPPORT);
            }

            String table = buildTableNameFromEnv(activeProfile);
            String station = buildStationValueFromEnv(activeProfile);
            /*
             * 
             * 
             *  curl   'https://nocodb-test.addx.live/api/v1/db/data/v1/BI/test_user/find-one?fields=Id,user_id&where=(user_id,eq,1986)~and(id,eq,242)' \                          
            -H 'accept: application/json' \
            -H 'Content-Type: application/json' -H 'xc-token:6Fry4HYP3aNunGbWhTpiM6XYlRBRQ-7V4r7OnKIq'
             * 
             */
            Map<String, String> heads = new HashMap<>();
            heads.put("accept", APPLICATION_JSON);
            heads.put("Content-Type", APPLICATION_JSON);
            heads.put("xc-token", NOCODB_KEY);
            String url = String.format(NOCODB_API_V1 + "/db/data/v1/BI/%s/find-one?", table);
            Map<String, String> recipt = new HashMap<>();
            recipt.put("fields", "Id");
            recipt.put("where", String.format("(user_id,eq,%s)~and(station,eq,%s)",userId, station));
            String result = HttpUtils.httpGet(url, recipt, heads);
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                return jsonObject.containsKey("Id");
            } else {
                log.error("nocodb return error ,{},{}", url, result);
                throw new BaseException(500, SERVER_ERROR_MSG);
            }
        }
        return redisService.getBit(String.format("%s%s", USER_MARK_REDIS_KEY_PREFIX, userId), USER_MARK_MAP.get(key));
    }

    private boolean supportNode(String activeProfile) {
        String[] activeProfileArray = activeProfile.split("-");
        if(activeProfileArray.length>1){
            return "us".equals(activeProfileArray[1]);
        }
        return false;
    }

    public Boolean setUserMark(Integer userId, String key, Boolean flag) {
        if (!USER_MARK_MAP.containsKey(key)) {
            throw new BaseException(ResultCollection.INVALID_PARAMS);
        }
        if (TEST_MARK.equals(key)) {
            if(!supportNode(activeProfile)){
                throw new BaseException(ResultCollection.NODE_NOT_SUPPORT);
            }

            //already set
            if(queryUserMark(userId,TEST_MARK)){
                return true;
            }

            Map<String, String> heads = new HashMap<>();
            heads.put("accept", APPLICATION_JSON);
            heads.put("Content-Type", APPLICATION_JSON);
            heads.put("xc-token", NOCODB_KEY);
            Map<String, String> recipt = new HashMap<>();
            recipt.put("user_id", userId.toString());
            recipt.put("station", buildStationValueFromEnv(activeProfile));
            //test_user is the table name,it is in url


            String jsonBody = JSON.toJSONString(recipt);

            String url = String.format(NOCODB_API_V1 +"/db/data/v1/BI/%s", buildTableNameFromEnv(activeProfile));

            ResponseEntity result = HttpUtils.httpsPost(heads, jsonBody, url);

            // if success return true
            if (result.getStatusCode().is2xxSuccessful()) {
                log.info("nocodb insert ok {}, {}", url, result);
                //check json object has  Id param 
                String json = (String) result.getBody();
                JSONObject jsonObject = JSON.parseObject(json);
                if (jsonObject.containsKey("Id")) {
                    return true;
                } else {
                    log.error("nocodb result error, {}", result);
                    throw new BaseException(500, SERVER_ERROR_MSG);
                }

            } else {
                log.error("nocodb result error, {}", result);
                throw new BaseException(500, SERVER_ERROR_MSG);
            }
        }

        return redisService.setBit(String.format("%s%s", USER_MARK_REDIS_KEY_PREFIX, userId), USER_MARK_MAP.get(key), flag);
    }


    public String buildTableNameFromEnv(String activeProfile) {
        //activeProfile is like staging,staging-eu,staging-us,pre-eu,prod,prod-us
        //return test_user_pre test_user_staging test_user
        String[] activeProfileArray = activeProfile.split("-");
        String prefix = "test_user";
        //test_user|test_user_pre|test_user_staging

        if("prod".equals(activeProfileArray[0])){
            return prefix;
        }
        return prefix + "_" + activeProfileArray[0];

    }

    public String buildStationValueFromEnv(String activeProfile) {
        //activeProfile is like staging,staging-eu,staging-us,pre-eu,prod,prod-us
        //return cn us eu
        String[] activeProfileArray = activeProfile.split("-");
        //if it is staging|pre|prod,return cn 
        if (activeProfileArray.length == 1) {
            return "cn";
        } else {
            return activeProfileArray[1];
        }
    }

    /**
     * 判断用户是否可以显示兑换码功能
     * @param userId 用户ID
     * @return 是否显示兑换码功能
     */
    public boolean showRedeem(Integer userId) {
        // 从 UserRoleService 获取用户角色信息，检查是否为管理员
        List<UserRoleDO> userRoleByUserId = userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode());
        // 如果用户在兑换码黑名单中或者是管理员，则不显示兑换码功能
        if (userVipService.isUserInExchangeCodeBlacklist(userId) || userRoleByUserId.isEmpty()) {
            return false;
        } else {
            return true;
        }
    }

}
