package com.addx.iotcamera.service.pay;

import com.addx.iotcamera.bean.app.payment.GoogleOrderResult;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePublisher;
import com.addx.iotcamera.bean.app.payment.GooglePublisherData;
import com.addx.iotcamera.bean.app.vip.GoogleHistoryRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.domain.pay.GoogleCancelOrderDO;
import com.addx.iotcamera.bean.domain.pay.OrderVerifyResultDO;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.bean.domain.pay.SubscriptionPurchaseV2;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.dao.redis.PayRedis;
import com.addx.iotcamera.enums.PaymentFlagEnums;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.pay.GoogleHistoryNotifyEnums;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.PayUtil;
import com.addx.iotcamera.util.RedisUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.google.api.client.util.Lists;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;

import static com.addx.iotcamera.constants.PayConstants.*;


@Service
public class GooglePayService {
    private final static Logger logger = LoggerFactory.getLogger(WxPayService.class);

    @Autowired
    private PayRedis payRedis;

    @Autowired
    private IOrderDAO iOrderDAO;
    @Autowired
    private IOrderProductDAO iOrderProductDAO;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ProductService productService;

    @Autowired
    private PaymentCenterConfig paymentCenterConfig;

    @Resource
    @Lazy
    private OrderService orderService;

    @Autowired
    private RedisUtil redisUtil;

    private final static String defaultPackageName = "com.smartcamera.vicoopro";

    /**
     * @return
     */
    public OrderVerifyResultDO googleOrderVerify(GooglePaymentRequest request) {

        Map<String, String> params = Maps.newHashMap();

        String tenantId = this.getUserTenantId(request.getApp().getTenantId(), request.getApp().getBundle());
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);

        //参数是否必要待验证
        String accessToken = getAccessToken(paymentConfig, tenantId);
        if (StringUtils.isEmpty(accessToken)) {
            logger.info("googlePay no accessToken,{}", request.toString());
            return OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
        }
        params.put("access_token", accessToken);

        Integer productId = Optional.ofNullable(request.getProductId()).orElse(0);
        if(productId.equals(0)){
            // 验证时传的时商品订阅组id,先根据订阅组id 查询商品id
            productId = org.springframework.util.StringUtils.hasLength(request.getSubscriptionGroupId()) ?
                    queryProductIdBySubscriptionGroup(paymentConfig.getGooglePackage(),request.getPurchaseToken(),accessToken) : 0;
            request.setProductId(productId);
        }

        ProductDO productDO = productService.queryProductById(request.getProductId());
        if (productDO == null) {
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePay product null:{}", request.getProductId());
            return OrderVerifyResultDO.builder()
                    .verify(false)
                    .build();
        }

        String requestUrl = SUB_TIER_PRODUCT_SET.contains(productDO.getType()) ? paymentConfig.getGooglePublisher()
                : paymentConfig.getGoogleOrderUrl();
        String url = String.format(requestUrl, paymentConfig.getGooglePackage(),
                org.springframework.util.StringUtils.hasLength(request.getSubscriptionGroupId()) ? request.getSubscriptionGroupId() : request.getProductId(),
                request.getPurchaseToken(), accessToken);
        String orderInfo = HttpUtils.httpGet(url, params);
        logger.info("googleOrderVerify httpGet url:{},result:{}", url, orderInfo);
        //logger.info("googlePay url:{},result:{}", url, orderInfo);
//        {
//            "kind": "androidpublisher#productPurchase",
//                "purchaseTimeMillis": "1572520219904",
//                "purchaseState": 0,  订单的采购状态 0购买1取消,2 free trial
//                "consumptionState": 0,  0有待消费1已消耗
//                "developerPayload": "",
//                "orderId": "GPA.3395-8268-9414-05568",    客户支付订单ID（google　play 订单ID）
//                "purchaseType": 0,
//                "acknowledgementState": 1
//        }
        String orderInfoV2 = getSubscriptionPurchaseV2OrderInfo(paymentConfig.getGooglePackage(), request.getPurchaseToken(), accessToken);
        OrderVerifyResultDO result = OrderVerifyResultDO.builder().verify(false).build();
        if (!StringUtils.isEmpty(orderInfo)) {
            result = verifyOrderResult(orderInfo, productDO, request.getOutTradeNo(), orderInfoV2);
        }
        Gson gson = new Gson();
        SubscriptionPurchaseV2 subscriptionPurchaseV2 = gson.fromJson(orderInfoV2, SubscriptionPurchaseV2.class);
        if (subscriptionPurchaseV2 != null && subscriptionPurchaseV2.getLinkedPurchaseToken() != null) {
            String linkedOrderInfo = getSubscriptionPurchaseV2OrderInfo(paymentConfig.getGooglePackage(), subscriptionPurchaseV2.getLinkedPurchaseToken(), accessToken);
            result.setLinkedOrderInfo(linkedOrderInfo);
        }
        result.setOrderInfo(orderInfo);
        result.setOrderInfoV2(orderInfoV2);
        return result;
    }

    public OrderVerifyResultDO verifyOrderResult(String orderInfo, ProductDO productDO, String outTradeNo, String orderInfoV2) {
        boolean result = false;
        Integer freeTrial = 0;
        Integer purchaseTime = 0;
        String purchaseDatePst = "";
        String purchaseDate = "";
        String tradeNo = "";
        Integer freeTrialPeriod = 0;
        Integer expireTime = null;
        Boolean isUpgradedOrder = false;
        Boolean isTestOrder = false;
        if (SUB_TIER_PRODUCT_SET.contains(productDO.getType())) {
            GoogleOrderResult orderResult = JSONObject.parseObject(orderInfo, GoogleOrderResult.class);
            purchaseTime = (int)(orderResult.getStartTimeMillis()/1000);
            purchaseDatePst = DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
            purchaseDate = DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
            expireTime = (int) (orderResult.getExpiryTimeMillis()/1000);
            tradeNo = orderResult.getOrderId();

            if (orderResult.getPaymentState()!= null && (!orderResult.getPaymentState().equals(1) && !orderResult.getPaymentState().equals(2))) {
                com.addx.iotcamera.util.LogUtil.warn(logger, "googlePay verifyOrderResult orderResult 未续订");
                return OrderVerifyResultDO.builder()
                        .purchaseTime(purchaseTime)
                        .purchaseDatePst(purchaseDatePst)
                        .purchaseDate(purchaseDate)
                        .expireTime(expireTime)
                        .verify(false)
                        .tradeNo(tradeNo)
                        .build();
            }
            result = true;
            freeTrial = orderResult.getPaymentState() != null && orderResult.getPaymentState().equals(2) ? 1 : 0;

            if(freeTrial.equals(1)){
                freeTrialPeriod = DateUtils.getTimePeriodDay(orderResult.getStartTimeMillis(),orderResult.getExpiryTimeMillis());
            }
            Gson gson = new Gson();
            SubscriptionPurchaseV2 subscriptionPurchaseV2 = gson.fromJson(orderInfoV2, SubscriptionPurchaseV2.class);
            // 有LinkedPurchaseToken字段，且订单号一致（排除续订订单的可能）
            if (subscriptionPurchaseV2 != null && subscriptionPurchaseV2.getLinkedPurchaseToken() != null && outTradeNo.equals(subscriptionPurchaseV2.getLatestOrderId())) {
                isUpgradedOrder = true;
            }
            if (subscriptionPurchaseV2 != null && subscriptionPurchaseV2.getTestPurchase() != null) {
                isTestOrder = true;
            }
        } else {
            JSONObject obj = JSONObject.parseObject(orderInfo);
            tradeNo = obj.getString("orderId");
            if (obj == null || !obj.containsKey("consumptionState") || !obj.containsKey("purchaseState")) {
                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay googleOrderVerify false 状态你对");
                return OrderVerifyResultDO.builder()
                        .verify(false)
                        .tradeNo(tradeNo)
                        .build();
            }

            if (obj.getIntValue("consumptionState") != 0) {
                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay googleOrderVerify false 商品已消费:consumptionState:{},orderId:{}", obj.getIntValue("purchaseState"), outTradeNo);
                return OrderVerifyResultDO.builder()
                        .verify(false)
                        .tradeNo(tradeNo)
                        .build();
            }

            if (obj.getIntValue("purchaseState") != 0) {
                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay googleOrderVerify false:purchaseState:{},", obj.getIntValue("purchaseState"));
                return OrderVerifyResultDO.builder()
                        .verify(false)
                        .tradeNo(tradeNo)
                        .build();
            }

            if (!obj.getString("orderId").equals(outTradeNo)) {
                com.addx.iotcamera.util.LogUtil.error(logger, "googlePay googleOrderVerify false:order:{},googleOrder:{}", outTradeNo,
                        obj.getString("orderId"));
                return OrderVerifyResultDO.builder()
                        .verify(false)
                        .tradeNo(tradeNo)
                        .build();
            }
            result = true;
            freeTrial = 0;
            purchaseTime = (int)(obj.getLongValue("purchaseTimeMillis")/1000);
            purchaseDatePst = DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
            purchaseDate = DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
            tradeNo = obj.getString("orderId");
        }
        return OrderVerifyResultDO.builder()
                .tradeNo(tradeNo)
                .verify(result)
                .freeTrial(freeTrial)
                .freeTrialPeriod(freeTrialPeriod)
                .purchaseTime(purchaseTime)
                .purchaseDatePst(purchaseDatePst)
                .purchaseDate(purchaseDate)
                .expireTime(expireTime)
                .isUpgradedOrder(isUpgradedOrder)
                .isTestOrder(isTestOrder)
                .build();
    }

    /**
     * 获取谷歌token
     *
     * @return
     */
    public String getAccessToken(PaymentConfig paymentConfig, String tenantId) {
        String key = PayRedis.tokenKey.replace("{tenantId}", tenantId).replace("{hour}", DateUtils.YYYYMMDDHH);
        String value = payRedis.get(key);
        if (StringUtils.isEmpty(value)) {
            Map<String, String> params = Maps.newHashMap();
            params.put("grant_type", "refresh_token");
            params.put("client_id", paymentConfig.getGoogleClientId());
            params.put("client_secret", paymentConfig.getGoogleCilentSecret());
            params.put("refresh_token", paymentConfig.getGoogleRefreshToken());
            String resultInfo = HttpUtils.httpPostGoogle(params, paymentConfig.getGoogleTokenUrl());
            logger.info("googlePay token paymentConfig {} resultInfo:{}", paymentConfig,resultInfo);
            if (!StringUtils.isEmpty(resultInfo)) {
                JSONObject obj = JSONObject.parseObject(resultInfo);
                if (obj.containsKey("access_token")) {
                    value = obj.getString("access_token");
                    payRedis.set(key, value, 3600);
                }
            }
        }

        return value;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean publisherMessage(GooglePublisher pub, String tenantId) throws Exception {
        String data = pub.getMessage().getData();
        data = new String(Base64.getDecoder().decode(data), "utf-8");
        logger.info("googlePublisher getSubscriptionNotification data:{}", data);

        GooglePublisherData publisherData = JSONObject.parseObject(data, GooglePublisherData.class);

        if (publisherData.getSubscriptionNotification() == null) {
            logger.info("googlePublisher getSubscriptionNotification test:{}", publisherData.toString());
            return true;
        }

        if (!pubCheck(publisherData, tenantId)) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            return false;
        }

        if (publisherData.getSubscriptionNotification().getNotificationType() == 4) {
            com.addx.iotcamera.util.LogUtil.warn(logger,  "googlePublisher getNotificationType 首次购买:{}", publisherData.getSubscriptionNotification().getNotificationType());
            return true;
        }

        logger.info("googlePublisher NotificationType:【{}】,packageName:【{}】,subscriptionId:【{}】,purchaseToken:【{}】",
                publisherData.getSubscriptionNotification().getNotificationType(),
                publisherData.getPackageName(),
                publisherData.getSubscriptionNotification().getSubscriptionId(),
                publisherData.getSubscriptionNotification().getPurchaseToken());

        // 获取purchaseToken，用于分布式锁
        String purchaseToken = null;
        if (publisherData.getSubscriptionNotification() != null) {
            purchaseToken = publisherData.getSubscriptionNotification().getPurchaseToken();
        }

        // 如果无法获取purchaseToken，这是谷歌通知数据的问题，应该抛出异常
        if (StringUtils.isEmpty(purchaseToken)) {
            logger.error("Google notification data error: unable to get purchaseToken, cannot process order. publisherData: {}", JSONObject.toJSONString(publisherData));
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            throw new Exception("Google notification data error: unable to get purchaseToken");
        }

        // 构建分布式锁的key
        String lockKey = "google_payment_lock:" + tenantId + ":" + purchaseToken;

        // 获取锁
        long lockValue = redisUtil.tryLock(lockKey);

        if (lockValue == -1) {
            logger.warn("Failed to acquire distributed lock, this order may be processing by another thread. purchaseToken: {}", purchaseToken);
            // 获取锁失败，通知谷歌服务器处理失败，需要重试
            return false;
        }

        try {
            // 获取到锁，执行数据库相关操作
            return queryGooglePublisher(publisherData.getPackageName(),
                    publisherData.getSubscriptionNotification().getSubscriptionId(),
                    publisherData.getSubscriptionNotification().getPurchaseToken(),
                    tenantId);
        } finally {
            // 释放锁
            boolean unlockResult = redisUtil.unlock(lockKey, lockValue);
            if (!unlockResult) {
                logger.error("Failed to release distributed lock or lock expired. purchaseToken: {}", purchaseToken);
            }
        }
    }

    /**
     * 校验订阅通知消息
     *
     * @param publisherData
     * @return
     */
    private boolean pubCheck(GooglePublisherData publisherData, String tenantId) {
        boolean result = true;

        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);

        if (!paymentConfig.getGooglePackage().equals(publisherData.getPackageName())) {
            logger.info("googlePublisher package not equal:{}", publisherData.toString());
            return false;
        }
        return result;
    }


    private boolean queryGooglePublisher(String packageName, String subscriptionId, String token, String tenantId) throws Exception {
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);
        String accessToken = getAccessToken(paymentConfig, tenantId);
        if (StringUtils.isEmpty(accessToken)) {
            throw new Exception("googlePublisher 获取token异常");
        }

        Map<String, String> params = Maps.newHashMap();
        params.put("access_token", accessToken);

        String url = String.format(paymentConfig.getGooglePublisher(), packageName,
                subscriptionId, token);

        String orderInfo = HttpUtils.httpGet(url, params);
        logger.info("googlePublisher httpGet url:{},orderInfo:{}", url, orderInfo);

        String orderInfoV2 = getSubscriptionPurchaseV2OrderInfo(paymentConfig.getGooglePackage(), token, accessToken);
        Gson gson = new Gson();
        SubscriptionPurchaseV2 subscriptionPurchaseV2 = gson.fromJson(orderInfoV2, SubscriptionPurchaseV2.class);

        if (StringUtils.isEmpty(orderInfo)) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePublisher orderInfo empty,packageName:{},subscriptionId:{},token:{}", packageName, subscriptionId, token);
            return false;
        }

        GoogleOrderResult orderResult = JSONObject.parseObject(orderInfo, GoogleOrderResult.class);

        //订单退款、取消
        if(orderResult.getCancelReason() != null){
            return this.refundOrder(orderResult);
        }

        long currentTime = System.currentTimeMillis();
        if (orderResult.getExpiryTimeMillis() < currentTime || !orderResult.getPaymentState().equals(1)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePublisher orderResult 未续订");
            return true;
        }
        logger.info("googlePublisher 已支付到期时间:{}", orderResult.getExpiryTimeMillis());

        int index = orderResult.getOrderId().indexOf("..");

        String orderId = index > 0 ? orderResult.getOrderId().substring(0, index) : orderResult.getOrderId();
        OrderDO orderDO = iOrderDAO.queryBytradeNo(orderId);

        if (orderDO == null) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.warn(logger,  "googlePublisher orderDO empty,orderId:{}", orderId);
            // 安卓通知跨节点，美国、欧洲都会接受，真实订单只会存在于一处，查找不到默认成功，否则会一直通知
            return true;
        }

        PaymentFlow paymentFlow = paymentService.queryPaymentFlow(orderResult.getOrderId());
        if (paymentFlow != null) {
            logger.info("googlePublisher paymentFlow 已经存在:{}", paymentFlow.toString());
            return true;
        }
        orderDO.setTimeEnd((int) (orderResult.getExpiryTimeMillis() / 1000));
        iOrderDAO.updateOrderEndDate(orderDO);
        logger.info("googlePublisher orderSn:{},更新本期到期时间:{}", orderDO.getOrderSn(), orderResult.getExpiryTimeMillis());

        OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());

        orderDO.setTradeNo(orderResult.getOrderId());
        paymentService.initUserVip(orderDO, orderProductDo);
        logger.info("googlePublisher initUserVip:{}", orderDO.getOrderSn());
        orderDO.setTimeStart((int) (orderResult.getStartTimeMillis() / 1000));
        orderDO.setFreeTrial(orderResult.getPaymentState() == 2 ? 1 : 0);

        int purchaseTime = (int)(orderResult.getStartTimeMillis()/1000);
        boolean isTestOrder = false;
        boolean isUpgradedOrder = false;
        if (subscriptionPurchaseV2 != null && subscriptionPurchaseV2.getTestPurchase() != null) {
            isTestOrder = true;
        }
        OrderVerifyResultDO verifyResultDO =  OrderVerifyResultDO.builder()
                .tradeNo(orderResult.getOrderId())
                .purchaseTime(purchaseTime)
                .purchaseDate(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .purchaseDatePst(DateUtils.timeStamp2Date(purchaseTime,DateUtils.YYYY_MM_DD_HH_MM_SS))
                .isTestOrder(isTestOrder)
                .isUpgradedOrder(isUpgradedOrder)
                .build();
        orderDO.setOrderInfo(orderInfo);
        orderDO.setOrderInfoV2(orderInfoV2);
        paymentService.initPayment(orderDO, orderProductDo, orderInfo,verifyResultDO,true);
        logger.info("googlePublisher initPayment:{},tradeNo:{}", orderDO.getOrderSn(), orderResult.getOrderId());

        return true;
    }

    /**
     * 首次订阅
     * @param orderInfo
     * @return
     */
    public boolean queryGooglePublisherFreeTrial(String orderInfo ) {
        if (StringUtils.isEmpty(orderInfo)) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            return false;
        }

        GoogleOrderResult orderResult = JSONObject.parseObject(orderInfo, GoogleOrderResult.class);

        if(orderResult.getPaymentState().intValue() != 2){
            return true;
        }

        String orderId = orderResult.getOrderId();
        OrderDO orderDO = iOrderDAO.queryBytradeNo(orderId);
        if (orderDO == null) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.warn(logger,  "queryGooglePublisherFreeTrial orderDO empty,orderId:{}", orderId);
            // 安卓通知跨节点，美国、欧洲都会接受，真实订单只会存在于一处，查找不到默认成功，否则会一直通知
            return true;
        }

        PaymentFlow paymentFlow = paymentService.queryPaymentFlow(orderResult.getOrderId());
        if (paymentFlow == null) {
            logger.info("queryGooglePublisherFreeTrial paymentFlow 不存在:{}", orderResult.getOrderId());
            return true;
        }

        paymentFlow.setFreeTrial(orderResult.getPaymentState().intValue() == 2 ? 1 : 0);
        paymentService.updateRefundInfo(paymentFlow);
        logger.info("queryGooglePublisherFreeTrial freeTrial {} tradeNo {}", orderDO.getOrderSn(), orderResult.getOrderId());
        return true;
    }

    private String getUserTenantId(String tenantId, String packageName) {
        return defaultPackageName.equals(packageName) ? "vicooPro" : tenantId;
    }


    public String queryGoogleOrderInfo(String packageName, String subscriptionId, String token,String tenantId){
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);
        String accessToken = getAccessToken(paymentConfig, tenantId);
        if (StringUtils.isEmpty(accessToken)) {
            return "";
        }

        Map<String, String> params = Maps.newHashMap();
        params.put("access_token", accessToken);

        String url = String.format(paymentConfig.getGooglePublisher(), packageName,
                subscriptionId, token);

        final String result = HttpUtils.httpGet(url, params);
        logger.info("queryGoogleOrderInfo httpGet url:{},result:{}", url, result);
        return result;
    }

    /**
     * 订单取消
     * @param orderResult
     * @return
     */
    public boolean refundOrder(GoogleOrderResult orderResult){
        if(orderResult.getCancelReason() == null){
            return false;
        }
        GoogleHistoryRequest request = new GoogleHistoryRequest();
        request.setTransactionId(orderResult.getOrderId());

        List<GoogleHistoryRequest.GoogleOrderNotifyEven> notifyTypeList = new ArrayList<>();

        if(!orderResult.isAutoRenewing()){
            GoogleHistoryRequest.GoogleOrderNotifyEven cancelEven = new GoogleHistoryRequest.GoogleOrderNotifyEven();
            cancelEven.setNotifyReason(orderResult.getCancelReason());
            cancelEven.setNotifyType(GoogleHistoryNotifyEnums.CANCEL_AUTO_RENEW.getCode());
            cancelEven.setNotifyTime(orderResult.getUserCancellationTimeMillis() == null ? (int) Instant.now().getEpochSecond() : (int)(orderResult.getUserCancellationTimeMillis()/1000));
            notifyTypeList.add(cancelEven);
        }
        request.setNotifyTypeList(notifyTypeList);

        orderService.notifyGoogleOrder(request);
        return true;
    }

    /**
     * 查询已取消的订单
     * @param tenantId
     * @return
     */
    public List<GoogleCancelOrderDO.VoidedPurchaseDO>  queryCancelOrderList(String tenantId){
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get(tenantId);
        if(paymentConfig == null){
            return Lists.newArrayList();
        }
        //参数是否必要待验证
        String accessToken = getAccessToken(paymentConfig, tenantId);
        if (StringUtils.isEmpty(accessToken)) {
            logger.info("googlePay no accessToken,{}", tenantId);
            return Lists.newArrayList();
        }

        Map<String,String> param = Maps.newHashMap();
        param.put("access_token",accessToken);
        param.put("type","1");
        String cancelList = HttpUtils.httpGet(GOOGLE_CANCEL_ORDER_LIST,param);
        if(StringUtils.isEmpty(cancelList)){
            //无取消订单
            return Lists.newArrayList();
        }

        Gson gson = new Gson();
        GoogleCancelOrderDO googleCancelOrderDO = gson.fromJson(cancelList, GoogleCancelOrderDO.class);
        return googleCancelOrderDO.getVoidedPurchases();
    }


    /**
     * 获取支付时套餐指定设备list
     * @param extend
     * @return
     */
    public List<String> queryUserTierDeviceRequest(String extend){
        if(!org.springframework.util.StringUtils.hasLength(extend)){
            return Lists.newArrayList();
        }

        GooglePaymentRequest request = JSONObject.parseObject(extend,GooglePaymentRequest.class);
        return request.getTierDeviceList();
    }


    /**
     * 查询谷歌订单过期时间
     * @param request
     * @return
     */
    public Integer queryGoogleOrderExpireTime(GooglePaymentRequest request){
        OrderVerifyResultDO verifyResultDO = this.googleOrderVerify(request);
        return verifyResultDO.getExpireTime();
    }


    /**
     * 谷歌取消订阅
     * @param productId
     * @param purchaseToken
     * @param bundle
     * @return
     */
    public boolean subscriptionOrderCancel(String productId,String purchaseToken,String bundle) {
        boolean result = false;
        try {
            // Build Android Publisher service.
            AndroidPublisher publisher = PayUtil.getAndroidPublisher(bundle);

            // Get subscription details.
            AndroidPublisher.Purchases.Subscriptions.Get request = publisher.purchases().subscriptions()
                    .get(bundle, String.valueOf(productId), purchaseToken);
            SubscriptionPurchase subscription = request.execute();

            logger.info("subscription info {}",JSONObject.toJSON(subscription));
            // Check subscription status and cancel if active.
            if (subscription.getAutoRenewing()) { // 1 means payment received
                AndroidPublisher.Purchases.Subscriptions.Cancel cancelRequest = publisher.purchases().subscriptions()
                        .cancel(bundle, productId, purchaseToken);
                cancelRequest.execute();
                result = true;
                logger.info("Subscription cancelled successfully.");
            } else {
                logger.info("Subscription is not autoRenew.");
            }
        } catch (Exception e) {
            logger.error("谷歌订单验证退订失败",e);
        }
        return result;
    }

    /**
     * 根据商品订阅组查询商品Id
     * @param appPackage
     * @param purchaseToken
     * @param accessToken
     * @return
     */
    public Integer queryProductIdBySubscriptionGroup(String appPackage,
                                                     String purchaseToken,
                                                     String accessToken){
        Integer productId = 0;
        Gson gson = new Gson();

        String orderInfo = getSubscriptionPurchaseV2OrderInfo(appPackage, purchaseToken, accessToken);
        logger.info("打印查询planId 的 orderInfo {}",orderInfo);
        if(!org.springframework.util.StringUtils.hasLength(orderInfo)){
            return productId;
        }

        SubscriptionPurchaseV2 purchaseProductInfo = gson.fromJson(orderInfo,SubscriptionPurchaseV2.class);
        return CollectionUtils.isEmpty(purchaseProductInfo.getLineItems()) ?
                0 : Integer.parseInt(purchaseProductInfo.getLineItems().get(0).getOfferDetails().getBasePlanId());
    }

    public String getSubscriptionPurchaseV2OrderInfo(String appPackage, String purchaseToken, String accessToken) {
        String url = QUERY_PRODUCT_ID_BY_SUBSCRIPTION_URL.replace("{packageName}", appPackage)
                .replace("{token}", purchaseToken);
        Map<String,String> params = Maps.newHashMap();
        params.put("access_token", accessToken);
        String orderInfo = HttpUtils.httpGet(url, params);
        return orderInfo;
    }
}
