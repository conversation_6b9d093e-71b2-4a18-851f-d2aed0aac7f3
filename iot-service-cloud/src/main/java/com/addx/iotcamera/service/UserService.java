package com.addx.iotcamera.service;

import com.addx.iotcamera.annotation.QueryCache;
import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.AppFormOptionsRequest;
import com.addx.iotcamera.bean.app.MailConfirmRequest;
import com.addx.iotcamera.bean.app.UpdatePasswordRequest;
import com.addx.iotcamera.bean.app.UserRequest;
import com.addx.iotcamera.bean.app.user.SendMailRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.user.UserPayEmailDO;
import com.addx.iotcamera.bean.domain.user.UserTrustDevice;
import com.addx.iotcamera.bean.domain.verfiy.VerifyCodeResponseDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.bean.msg.FeishuMessage;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.config.TenantCompatibleConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.app.AppUserTenantIdConfig;
import com.addx.iotcamera.config.device.FreeUserVipTier2Config;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.enums.EUserProfile;
import com.addx.iotcamera.enums.UserStatus;
import com.addx.iotcamera.enums.UserType;
import com.addx.iotcamera.helper.TrackerTokenHelper;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.message.EventRedisService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.user.UserSecondVerifyService;
import com.addx.iotcamera.service.user.UserTrustDeviceService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.Mail;
import com.addx.iotcamera.util.OKHttpUtils;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Setter;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.UserConstants.*;
import static com.addx.iotcamera.enums.SendEmailTypeEnums.USER_FEEDBACK_4_KB_APP;
import static org.addx.iot.common.constant.AppConstants.TENANTID_SHENMOU;
import static org.addx.iot.common.constant.AppConstants.TENANTID_VICOO;
import static org.addx.iot.common.enums.ResultCollection.*;


@Component
public class UserService {
    private final static Logger logger = LoggerFactory.getLogger(UserService.class);

    @Setter
    @Autowired
    private IUserDAO userDAO;

    @Autowired
    private TokenService tokenService;

    @Resource
    private UserVipService userVipService;

    @Autowired
    private TrackerTokenHelper trackerTokenHelper;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private MailConfirmService mailConfirmService;

    @Autowired
    private CenterNotifyConfig centerNotifyConfig;

    @Autowired
    private TenantCompatibleConfig tenantCompatibleConfig;

    @Autowired
    private PushInfoService pushInfoService;

    @Autowired
    private AppUserTenantIdConfig appUserTenantIdConfig;

    @Resource
    private EventRedisService eventRedisService;

    @Autowired
    private PaasVipConfig paasVipConfig;

    @Autowired
    private DeviceManualService deviceManualService;

    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private MailSendConfig mailSendConfig;
    @Resource
    private AppAccountConfig appAccountConfig;

    @Autowired
    private UserTrustDeviceService userTrustDeviceService;

    @Autowired
    private UserSecondVerifyService userSecondVerifyService;


    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Value("${safe-push.rootUrl:}")
    private String safePushEndpoint;
    @Value("${servernode}")
    private String serverNode;
    @Value("${nodeEvn}")
    private String nodeEnv;
    @Autowired
    @Lazy
    private TenantSettingService tenantSettingService;

    @Autowired
    @Lazy
    private UserQueryService userQueryService;

    public User getUserByEmailAndTenantId(String email, String phone, String tenantId) {
        User user = userDAO.getUserByEmailAndTenantId(email, phone, tenantId);
        if (user == null && tenantCompatibleConfig.getConfig().containsKey(tenantId)) {
            for (String tenant : tenantCompatibleConfig.getConfig().get(tenantId)) {
                logger.info("查找用户,{}", tenant);
                user = userDAO.getUserByEmailAndTenantId(email, phone, tenant);
                if (user != null) {
                    break;
                }
            }
        }
        return user;
    }

    public String getUserTenantId(Integer userId) {
        if (userId == null) return null;
        return userQueryService.getUserTenantIdInner(userId);
    }

    public Integer insertUser(User user) {
        return userDAO.insertUser(user);
    }

    @CacheEvict(value = {"userInfoV1"}, key = "#user.id")
    public Integer updateUserPassword(User user) {
        return userDAO.updateUserPassword(user);
    }

    public void insertUserPwdChangeLog(User oldUser, String reason) {
        try {
            userDAO.insertUserPwdHistory(oldUser, reason);
        } catch (Exception e) {
            logger.error("insertUserPwdChangeLog error! oldUser={},reason={}", JSON.toJSONString(oldUser), reason, e);
        }
    }

    public List<User> selectAllValidUser(Integer startUserId) {
        Set<String> excludeTenantIds = paasVipConfig.getExcludeTenantIds();
        return userDAO.selectAllBatch(startUserId, Arrays.asList(UserStatus.NORMAL.getCode()), excludeTenantIds, 1000,0);
    }

    public List<User> selectAllValidUser(Integer startUserId,Integer registerTime) {
        Set<String> excludeTenantIds = paasVipConfig.getExcludeTenantIds();
        return userDAO.selectAllBatch(startUserId, Arrays.asList(UserStatus.NORMAL.getCode()), excludeTenantIds, 200,registerTime);
    }

    public List<User> selectAllUser(Integer startUserId, Integer limitNum) {
        // 长视虽然只用设备维度vip。但是它的非vip用户依然要按免费7天的逻辑进行视频删除
        return userDAO.selectAllBatch(startUserId, null, null, limitNum,0);
    }

    public String getUserName(Integer id) {
        return userDAO.getUserName(id);
    }

    @CacheEvict(value = {"userInfoV1"}, key = "#user.id")
    public Result updateUserName(User user) {
        return Result.SqlOperationResult(userDAO.updateUserName(user));
    }

    @CacheEvict(value = {"userInfoV1"}, key = "#userId")
    public Result updatePassword(Integer userId, UpdatePasswordRequest request) {

        User user = new User();
        user.setId(userId);
        User stored = userDAO.getUserById(user);

        // 判断用户状态
        if (stored == null) { //用户未注册
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }
        // 用户提交旧密码错误
        else if (!stored.getHashedPassword().equals(PhosUtils.CalcSalt(request.getOldPassword(), stored.getSalt()))) {
            return Result.Error(-1021, "WRONG_PASSWORD");
        }
        // 用户新密码不合规
        else if (PhosUtils.pswdInvalid(request.getNewPassword())) {
            return Result.Error(-1012, "INVALID_PASSWORD");
        } else {
            String salt = stored.getSalt();
            user.setEmail(stored.getEmail());
            user.setSalt(salt);
            user.setName(stored.getName());

            user.setHashedPassword(PhosUtils.CalcSalt(request.getNewPassword(), salt));
            user.setTenantId(request.getApp().getTenantId());

            // 更新用户信息表
            Integer res = userDAO.updateUserPassword(user);

            if (res <= 0) {
                return Result.SqlOperationResult(res);
            }

            String seed = PhosUtils.getUUID();

            // 生成用户token
            UserToken tokenDO = new UserToken();
            tokenDO.setUserId(stored.getId());
            tokenDO.setActiveTime(PhosUtils.getUTCStamp());
            tokenDO.setSeed(seed);

            // 更新userToken
            tokenService.setUserToken(tokenDO);

            final HttpTokenDO httpTokenDO = tokenService.generateMsgToken(seed, stored);
            final LoginResponseDO responseDO = buildLoginResponseDO(stored, httpTokenDO);
            return new Result(responseDO);
        }
    }

    public LoginResponseDO buildLoginResponseDO(User stored, HttpTokenDO httpTokenDO) {
        LoginResponseDO responseDO = new LoginResponseDO();
        responseDO.setId(stored.getId());
        responseDO.setName(stored.getName());
        responseDO.setEmail(stored.getEmail());
        responseDO.setPhone(stored.getPhone());
        responseDO.setLastName(stored.getLastName());
        responseDO.setHasPassword(StringUtils.hasLength(stored.getHashedPassword()));
        // 更新masToken
        responseDO.setToken(httpTokenDO);
        responseDO.setNode(activeProfile);
        // 给app下发访问其它服务的token
        responseDO.setTrackerToken(trackerTokenHelper.generateTrackerToken(responseDO.getId()));
        final TenantSetting tenantSetting = tenantSettingService.getTenantSettingByTenantId(stored.getTenantId());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(tenantSetting.getSafePushEndpoint())) {
            responseDO.setSafePushToken(trackerTokenHelper.generateSafePushToken(responseDO.getId(), activeProfile));
            responseDO.setSafePushEndpoint(tenantSetting.getSafePushEndpoint());
        }
        return responseDO;
    }

    public Result passwordValidation(Integer userId, UpdatePasswordRequest request) {

        User user = new User();
        user.setId(userId);
        User stored = userDAO.getUserById(user);

        // 判断用户状态
        if (stored == null) {
            //用户未注册
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }
        // 用户提交旧密码错误
        else if (!stored.getHashedPassword().equals(PhosUtils.CalcSalt(request.getOldPassword(), stored.getSalt()))) {
            return Result.Error(-1021, "WRONG_PASSWORD");
        }

        return Result.Success();
    }

    /**
     * 根据userId返回用户map
     *
     * @param list
     * @return
     */
    public Map<Integer, User> queryUserMap(List<Integer> list) {
        String ids = org.apache.commons.lang3.StringUtils.join(list.toArray(), ",");
        return userDAO.selectUserByUserIds(ids).stream()
                .collect(Collectors.toMap(User::getId, User -> User));
    }

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     */
    @QueryCache(value = "userInfoV1", key = "#userId")
    public User queryUserById(Integer userId) {
        User user = new User();
        user.setId(userId);
        return userDAO.getUserById(user);
    }

    /**
     * 更新用户通信方式
     *
     * @param user
     * @return
     */
    public Integer updateUserContact(User user) {
        return userDAO.updateUserContact(user.getId(), user.getEmail(), user.getPhone());
    }

    /**
     * 更新用户APP语言
     *
     * @param user
     * @return
     */
    @CacheEvict(value = {"userTenantId","userInfoV1"}, key = "#user.id")
    public Result updateUserLanguage(User user) {

        if (!centerNotifyConfig.getLanguage().get(TENANTID_VICOO).containsKey(user.getLanguage())) {
            logger.info("update language param error,no language:{}", user.toString());
            return Result.Error(INVALID_PARAMS, "params error");
        }

        return Result.SqlOperationResult(userDAO.updateUserLanguage(user));
    }

    /**
     * 查询用户基础信息
     *
     * @param userId
     * @return
     */
    public LoginResponseDO getUserBasicInfo(Integer userId) {
        User user = new User();
        user.setId(userId);
        User storedUser = userDAO.getUserById(user);
        LoginResponseDO responseDO = new LoginResponseDO();
        responseDO.setId(userId);
        responseDO.setName(storedUser.getName());
        responseDO.setLastName(storedUser.getLastName());
        responseDO.setEmail(storedUser.getEmail());
        responseDO.setPhone(storedUser.getPhone());

        responseDO.setHasPassword(StringUtils.hasLength(storedUser.getHashedPassword()));
        responseDO.setCountryNo(storedUser.getCountryNo());
        responseDO.setNode(activeProfile);
        return responseDO;
    }


    /**
     * 用户手机号保护，前三后四
     *
     * @param phone
     * @return
     */
    public String userPhoneProtect(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return phone;
        }

        StringBuilder phoneSb = new StringBuilder();
        phoneSb.append(phone.substring(0, 3));
        phoneSb.append("****");
        phoneSb.append(phone.substring(phone.length() - 4));

        return phoneSb.toString();
    }

    /**
     * 用户邮箱保护，前三+@以后的内容
     *
     * @param email
     * @return
     */
    public String userEmailProtect(String email) {
        if (StringUtils.isEmpty(email)) {
            return email;
        }

        StringBuilder emailSb = new StringBuilder();
        int index = email.indexOf("@");
        emailSb.append((index > 3) ? email.substring(0, 3) : email.substring(0, index));
        if (index > 3) {
            for (int i = 3; i < index; i++) {
                emailSb.append("*");
            }
        }
        emailSb.append(email.substring(email.indexOf("@")));

        return emailSb.toString();
    }

    /**
     * 判断是不是内部用户
     *
     * @param userId
     * @return
     */
    public EUserProfile getUserProfileById(Integer userId) {
        if (!userDAO.getUserInternal(userId).equals(0)) {
            return EUserProfile.TEST_USER;
        }
        return EUserProfile.NORMAL_USER;
    }

    /**
     * 用户账号注销
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancellationUser(UserRequest request) {
        User user = this.queryUserById(request.getId());
        if (user == null) {
            logger.info("用户记录不存在:{}", request.getId());
            throw new BaseException(USER_ACCOUNT_CANCEL, USER_ACCOUNT_CANCEL.getResult().getMsg());
        }

        user.setCancellationReason(request.getCancellationReason());
        user.setCancellationRemark(request.getCancellationRemark());

        //删除用户-设备绑定关系
        userRoleService.deleteUserRole(request.getId());
        //更新用户表
        userDAO.deleteUser(user);

        //清除用户注册记录
        mailConfirmService.deleteMailConfirm(user);
        //清除用户登录缓存
        tokenService.deleteUserToken(String.valueOf(request.getId()));

        // 清除用户app推送秘钥
        pushInfoService.deletePushInfo(request.getId());
    }


    /**
     * 获取此用户之前所有相关的用户（包括之前注销的用户）
     *
     * @param user
     * @return
     */
    public Set<Integer> queryUserListByUser(User user) {
        List<User> emailUserList = this.queryUserListByEmail(user);
        List<User> phoneUserList = this.queryUserListByPhone(user);

        Set userIdSet = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(emailUserList)) {
            emailUserList.forEach(u -> {
                if (!userIdSet.contains(u.getId())) {
                    userIdSet.add(u.getId());
                }
            });
        }
        if (!CollectionUtils.isEmpty(phoneUserList)) {
            phoneUserList.forEach(u -> {
                if (!userIdSet.contains(u.getId())) {
                    userIdSet.add(u.getId());
                }
            });
        }
        // 第三方账号可能没有邮箱和手机号，此时userIdSet为空。因此把账号自身的userId加进去。
        userIdSet.add(user.getId());
        return userIdSet;
    }

    private List<User> queryUserListByEmail(User user) {
        if (StringUtils.isEmpty(user.getEmail())) {
            return Lists.newArrayList();
        }
        return userDAO.selectUserByEmail(user);
    }

    private List<User> queryUserListByPhone(User user) {
        if (StringUtils.isEmpty(user.getPhone())) {
            return Lists.newArrayList();
        }
        return userDAO.selectUserByPhone(user);
    }


    /**
     * 获取用户app
     *
     * @param userId
     * @return
     */
    @Cacheable(value = "queryUserTenantId", key = "#userId", unless = "#result==null")
    public String queryTenantIdById(Integer userId) {
        User user = userDAO.getUserUserInfo(userId);
        if (user == null) {
            return "";
        }
        if (user.getType() == UserType.THIRD.getCode()) {
            return user.getTenantId();
        }
        PushInfo pushInfo = pushInfoService.getPushInfo(userId);
        if (pushInfo == null || pushInfo.getBundleName() == null) {
            return user.getTenantId();
        }
        String appPackage = pushInfo.getBundleName().replace(".", "");

        // 配置根据包名返回tenantId 的判断--主要是多个APP 通用一个账号的情况
        if (appUserTenantIdConfig.getConfig().containsKey(appPackage)) {
            return appUserTenantIdConfig.getConfig().get(appPackage);
        }

        // 非vicoo用户直接返回
        if (!user.getTenantId().equals(TENANTID_VICOO)) {
            return user.getTenantId();
        }
        // 根据app的包判断是 vicoo or guard
        return AppConstants.GUARD_PACKAGE.equals(appPackage) ? AppConstants.TENANTID_GUARD : TENANTID_VICOO;
    }

    public User queryByTenantIdAndThirdUserId(String tenantId, String thirdUserId) {
        return userDAO.queryByTenantIdAndThirdUserId(tenantId, thirdUserId);
    }

    public int updateUserById(User userModify) {
        return userDAO.updateUserById(userModify);
    }

    /**
     * 设置邮件触发时机
     * @param userPayEmailDO
     * @param firstTime
     */
    public void userReminderFreeTrialEmail(UserPayEmailDO userPayEmailDO, Boolean firstTime){
        logger.info("userReminderFreeTrialEmail firstTime {} userPayEmailDO {}",firstTime,userPayEmailDO);
        String key = USER_REMINDER_EVENT_KEY.replace("{userId}",String.valueOf(userPayEmailDO.getUserId()))
                .replace("{type}",String.valueOf(userPayEmailDO.getSendEmailTypeEnums().getCode()))
                .replace("{firstTime}",String.valueOf(firstTime ? 1 : 0));

        long currentTime = Instant.now().getEpochSecond();
        long expectTime = 0;
        if(firstTime){
            // 第一次提醒是注册15天后
            expectTime = DateUtils.getDateAfter(new Date(),15).getTime() / 1000;
        }else{
            // 非第一次提醒是注册时刻（每月一次）
            expectTime = DateUtils.getDateAfterMonth(new Date(),1).getTime() / 1000;
        }

        // 计算过期事件，过期时触发事件监控，发送邮件
        long expire = expectTime - currentTime;
        eventRedisService.set(key,String.valueOf(expire), (int) expire);
    }


    /**
     * 设置邮件触发时机-套餐到期提醒
     * @param userPayEmailDO
     * @param firstTime
     */
    public void userReminderVipExpireEmail(UserPayEmailDO userPayEmailDO, Boolean firstTime, int expire){
        logger.info("userReminderVipExpireEmail firstTime {} userPayEmailDO {}",firstTime,userPayEmailDO);
        String key = USER_REMINDER_EVENT_KEY.replace("{userId}",String.valueOf(userPayEmailDO.getUserId()))
                .replace("{type}",String.valueOf(userPayEmailDO.getSendEmailTypeEnums().getCode()))
                .replace("{firstTime}",String.valueOf(firstTime ? 1 : 0));

        eventRedisService.set(key,String.valueOf(expire), expire);
    }

    /**
     * 多线程遍历所有用户
     *
     * @param executor
     * @param startUserId 让定时任务可以从中断处重新执行，而没必要从头开始
     * @param handler
     */
    public void foreachUser(Executor executor, Integer startUserId, Consumer<User> handler) {
        final String requestId = MDC.get(MDCKeys.REQUEST_ID);
        while (true) {
            // 每批次查询用户数由1000改为100。当用户需要过期的视频数过多时，forkJoinPool中内置队列会占用大量内存
            List<User> userList = selectAllUser(startUserId, 100);
            if (CollectionUtils.isEmpty(userList)) {
                break;
            }
            try {
                for (User user : userList) {
                    executor.execute(() -> {
                        MDC.put(MDCKeys.REQUEST_ID, requestId);
                        handler.accept(user);
                    });
                }
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(logger, "foreachUser 执行异常", e);
            }
            startUserId = userList.get(userList.size() - 1).getId();
        }
    }

    /**
     * 获取app表单选项
     * @param userId
     * @param appFormOptionsRequest
     * @return
     */
    public AppFormOptionsDO getAppFormOptions(Integer userId, AppFormOptionsRequest appFormOptionsRequest) {
        AppFormOptionsDO appFormOptionsDO = new AppFormOptionsDO();

        Map<String, Collection<AppFormOptionsDO.AppFormOptionDO>> deviceFormOptionsMap = new HashMap<>();
        appFormOptionsDO.setDeviceFormOptions(deviceFormOptionsMap);

        // 查询用户设备列表
        UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(userId, appFormOptionsRequest.getSerialNumber());
        if(userRoleDO == null) {
            com.addx.iotcamera.util.LogUtil.warn(logger,  "user {} not have device {}, return", userId, appFormOptionsRequest.getSerialNumber());
            return appFormOptionsDO;
        }

        String modelNo = deviceManualService.getModelNoBySerialNumber(appFormOptionsRequest.getSerialNumber());

        // 获取用户vip信息
        final boolean isNoVipOrFreeTier2 = userTierDeviceService.getIsNoVipOrFreeTier2(userId, appFormOptionsRequest.getSerialNumber());

        // 视频拍摄间隔option
        List<AppFormOptionsDO.AppFormOptionDO> cooldownInSOptionList = new LinkedList<>();
        deviceFormOptionsMap.put("cooldown_in_s", cooldownInSOptionList);
        Set<AppFormOptionsDO.CooldownOptionValue> enabledCooldownOptionValues = new HashSet<>();
        if (isNoVipOrFreeTier2) {
            User user = queryUserById(userId);
            Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(userId, appFormOptionsRequest.getSerialNumber());
            if(currentTierId == null){
                currentTierId = userVipService.queryUserFreeTierId(userId);
            }
            enabledCooldownOptionValues.addAll(FreeUserVipTier2Config.getCooldownOptionValueList(currentTierId, user.getRegistTime(), modelNo, appFormOptionsRequest.getSerialNumber()));
        } else {
            enabledCooldownOptionValues.addAll(Arrays.asList(AppFormOptionsDO.CooldownOptionValue.values()));
        }
        for (AppFormOptionsDO.CooldownOptionValue cooldownOptionValue : AppFormOptionsDO.CooldownOptionValue.values()) {
            cooldownInSOptionList.add(AppFormOptionsDO.AppFormOptionDO.builder().value(cooldownOptionValue.getValue()).enabled(enabledCooldownOptionValues.contains(cooldownOptionValue)).build());
        }

        // 视频拍摄间隔开关
        List<AppFormOptionsDO.AppFormOptionDO> cooldownUserEnableOptionList = new LinkedList<>();
        deviceFormOptionsMap.put("cooldownUserEnable", cooldownUserEnableOptionList);
        if (cooldownInSOptionList.size() == enabledCooldownOptionValues.size()) {
            cooldownUserEnableOptionList.add(AppFormOptionsDO.AppFormOptionDO.builder().value(true).build());
            cooldownUserEnableOptionList.add(AppFormOptionsDO.AppFormOptionDO.builder().value(false).build());
        }

        // 视频拍摄时长option
        List<AppFormOptionsDO.AppFormOptionDO> videoSecondsOptionList = new LinkedList<>();
        deviceFormOptionsMap.put("videoSeconds", videoSecondsOptionList);
        for (AppFormOptionsDO.VideoSecondOptionValue videoSecondOptionValue : AppFormOptionsDO.VideoSecondOptionValue.values()) {
            boolean enable = true;
            if (isNoVipOrFreeTier2 && !videoSecondOptionValue.isFreeOption()) {
                enable = false;
            }
            videoSecondsOptionList.add(AppFormOptionsDO.AppFormOptionDO.builder().value(videoSecondOptionValue.getValue()).enabled(enable).build());
        }
        return appFormOptionsDO;
    }

    /**
     * 更新用户app类型、app版本
     * @param appType
     * @param appVersion
     */
    @CacheEvict(value = {"userTenantId","userInfoV1"}, key = "#user.id")
    public void updateUserAppInfo(User user,String appType,Integer appVersion,String versionName){
        if(ObjectUtils.nullSafeEquals(user.getAppType(),appType) &&
                ObjectUtils.nullSafeEquals(user.getAppVersion(),appVersion) &&
                ObjectUtils.nullSafeEquals(user.getAppVersionName(),versionName)){
            // app版本信息无变化，不需要更新
            return;
        }

        user.setAppType(appType)
                .setAppVersion(appVersion)
                .setAppVersionName(versionName);
        userDAO.updateUserById(user);
    }

    /**
     * 验证邮箱是否已注册
     * @param email
     * @param tenantId
     * @return
     */
    public Result verifyUserExist(Integer userId,@NotBlank String email,String tenantId){
        User adminUser = this.queryUserById(userId);
        if(adminUser.getEmail().equals(email)){
            return Result.Error(SHARE_TO_SELF);
        }
        return this.verifyUserAccountExist(email,null,tenantId);
    }


    /**
     * 验证账号是否已注册
     * @param email
     * @param tenantId
     * @return
     */
    public Result verifyUserAccountExist(String email,String phone,String tenantId){
        User user = userDAO.getUserByEmailAndTenantId(email,phone,tenantId);
        Map<String,Object> result = Maps.newHashMap();
        result.put("hasExist",user != null);
        return new Result<>(result);
    }

    public void reviewAccountCheck() {
        List<User> users = userDAO.selectUserForReviewAccount();
        if (CollectionUtils.isEmpty(users)) return;
        userDAO.updateUserStatusForReviewAccount();
        String notifyUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/9213d37d-0ce9-4088-a47d-b1a584418281";
        String body= "{"
                + "\"msg_type\": \"post\","
                + "\"content\": {"
                + "    \"post\": {"
                + "        \"zh-CN\": {"
                + "            \"title\": \"提审账号恢复通知"+serverNode+"-"+nodeEnv+"\","
                + "            \"content\": [["
                + "                {\"tag\": \"text\", \"text\": \"%s\\n\"},"
                + "                {\"tag\": \"at\", \"user_id\": \"all\"}"
                + "            ]]"
                + "        }"
                + "    }"
                + "}"
                + "}";
        String emails = users.stream().map(User::getEmail).collect(Collectors.joining("\n"));
        OKHttpUtils.doPost(notifyUrl, JSON.parseObject(String.format(body, emails), FeishuMessage.class));
    }

    public Result sendMail4Kb(Integer userId, SendMailRequest request) {
        if (userId == null) {
            logger.info("sendMail4Kb user id is null");
            return Result.Failure("userNotExist");
        }
        User userParam = new User();
        userParam.setId(userId);
        User user = userDAO.getUserById(userParam);
        if (user == null || StringUtil.isBlank(user.getEmail())) {
            logger.info("sendMail4Kb user is null, userId:{}, user:{}", userId, user);
            return Result.Failure("userNotExist");
        }

        MailSend mailSend = mailSendConfig.getConfig().get(request.getApp().getTenantId()).get(request.getLanguage());
        String title = mailConfirmService.getSendEmailTitle(mailSend,  USER_FEEDBACK_4_KB_APP);
        String body = mailConfirmService.getSendEmailBody(user.getEmail(), "",  USER_FEEDBACK_4_KB_APP, mailSend, request.getApp().getTenantId(), request.getApp());

        title = title.replace("Kiwibit", mailConfirmService.queryTenantName(request.getApp().getTenantId(), request.getApp().getApiVersion()));
        body = body.replace("Kiwibit", mailConfirmService.queryTenantName(request.getApp().getTenantId(), request.getApp().getApiVersion()));

        EmailAccount emailAccount = appAccountConfig.queryEmailAccount(request.getApp().getTenantId());

        String htmlBody = "<html><body>$BODY</body></html>".replace("$BODY", body);

        Mail.SendHtmlEmail(user.getEmail(), title, htmlBody,emailAccount);
        return Result.Success();
    }

    /**
     * 判断早期用户使用者
     * @return
     */
    public boolean isEarlyRegisteredUser(User user) {
        logger.debug("isEarlyRegisteredUser user {}", user);
        if (user == null) {
            return false;
        }
        String tenantId = user.getTenantId();
        int registTime = user.getRegistTime();

        // vicoo用户且注册时间小于**********
        if (TENANTID_VICOO.equals(tenantId) && registTime < USER_EARLY_REGISTERE_USER_VICOO) {
            logger.debug("vicoo用户且注册时间小于**********");
            return true;
        }

        // shenmou或shenmou_sdk用户且注册时间小于**********
        if (tenantId.startsWith(TENANTID_SHENMOU) && registTime < USER_EARLY_REGISTERE_USER_SHENMOU) {
            logger.debug("shenmou用户且注册时间小于**********");
            return true;
        }

        return false;
    }


    @Transactional
    public void appendData(MailConfirmRequest confirmRequest, VerifyCodeResponseDO verifyCodeResponseDO) {
        //生成随机种子
        String seed = PhosUtils.getUUID();
        final HttpTokenDO httpTokenDO = tokenService.generateMsgToken(seed, User.builder().id(confirmRequest.getUserId()).build());
        tokenService.setUserSeed(String.valueOf(confirmRequest.getUserId()), seed);
        verifyCodeResponseDO.setToken(httpTokenDO);


        //验证成功后添加该设备为可信任的同时添加二次验证信息
        UserTrustDevice userTrustDevice = new UserTrustDevice();
        userTrustDevice.setUserId(confirmRequest.getUserId());
        userTrustDevice.setDeviceName(confirmRequest.getUserDeviceName() == null ? "" : confirmRequest.getUserDeviceName());
        userTrustDevice.setDeviceId(confirmRequest.getUserDeviceId() == null ? "" : confirmRequest.getUserDeviceId());
        userTrustDeviceService.createUserTrustDevice(userTrustDevice);
    }
}
