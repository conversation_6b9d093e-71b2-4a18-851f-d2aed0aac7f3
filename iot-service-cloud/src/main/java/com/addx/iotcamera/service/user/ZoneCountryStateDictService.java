package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.db.manage.ZoneCountryStateDictDO;
import com.addx.iotcamera.bean.domain.manage.Country;
import com.addx.iotcamera.dao.manage.ZoneCountryStateDictDAO;
import com.addx.iotcamera.service.AppInfoService;
import com.google.api.client.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ZoneCountryStateDictService {
    @Autowired
    private ZoneCountryStateDictDAO zoneCountryStateDictDAO;

    @Resource
    @Lazy
    private AppInfoService appInfoService;

    @Cacheable(value = "countryStates", key = "#language")
    public Map<String,List<ZoneCountryStateDictDO>> getStatesByCountryAndLanguage(String language) {
        return zoneCountryStateDictDAO.queryCountryState(language)
                .stream()
                .collect(Collectors.groupingBy(ZoneCountryStateDictDO::getCountryNo));
    }
}
