package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.DeviceAppSettingsAloneConfig;
import com.addx.iotcamera.bean.app.DeviceAppSettingsAloneRequest;
import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.openapi.PaasVipLevel;
import com.addx.iotcamera.bean.openapi.PaasVipProduct;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.constants.NoPlanRollingDays;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.enums.DeviceUserConfigEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.SetParameterRequest;
import com.addx.iotcamera.publishers.vernemq.requests.WhiteLightParameterRequest;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.openapi.PaasVipProductService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;

@Service
@Slf4j
public class DeviceConfigService implements InitializingBean {
    private final static Logger logger = LoggerFactory.getLogger(DeviceConfigService.class);

    private static DeviceConfigService INSTANCE = null;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DeviceOperationHelper deviceOperationHelper;

    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    @Lazy
    private DeviceSettingService deviceSettingService;

    @Autowired
    @Lazy
    private OpenApiConfigService deviceThirdConfigService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserVipService userVipService;

    @Autowired
    private VipService vipService;
    @Autowired
    private ThingModelConfig thingModelConfig;
    @Autowired
    private PaasVipProductService paasVipProductService;

    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Autowired
    private TierService tierService;

    @Autowired
    @Lazy
    private DeviceManualService deviceManualService;

    @Autowired
    @Qualifier("commonPool")
    private Executor executor;

    private Map<String, PaasVipProduct> deviceVipProductMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        INSTANCE = this;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result setDeviceWhiteLight(DeviceAppSettingsAloneRequest request) throws MqttException, IdNotSetException {

        String operateId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();
        redisService.setDeviceOperationDOWithEmpty(operateId);


        SetParameterRequest setParameterRequest = new SetParameterRequest();
        setParameterRequest.setId(operateId);
        setParameterRequest.setTime(PhosUtils.getUTCStamp());

        initParam(request.getSerialNumber(), request.getAloneConfigList(), setParameterRequest);

        boolean operationRes = VernemqPublisher.setParameter(request.getSerialNumber(), setParameterRequest);
        //新增发送白光灯设置
        boolean operationWhiteLightRes = this.pushWhiteLightSwtich(request.getSerialNumber(), setParameterRequest);
        if (!operationRes && !operationWhiteLightRes) {
            com.addx.iotcamera.util.LogUtil.error(logger, "setDeviceWhiteLight push mqtt error:{}", request.getSerialNumber());
            throw new BaseException(ResultCollection.PUSH_MQTT_ERROR, "push mqtt error");
        }

        Result result = deviceOperationHelper.waitOperation(operateId);
        if (!Result.successFlag.equals(result.getResult())) {
            com.addx.iotcamera.util.LogUtil.error(logger, "setDeviceWhiteLight mqtt result error:{}", request.getSerialNumber());
            throw new BaseException(ResultCollection.DEVICE_NO_RESPONSE, "device_no_response");
        }
        return result;
    }

    /**
     * 初始化mqtt参数
     *
     * @param serialNumber
     * @param aloneConfigList
     * @param setParameterRequest
     * @return
     */
    private boolean initParam(String serialNumber, List<DeviceAppSettingsAloneConfig> aloneConfigList, SetParameterRequest setParameterRequest) {
        if (CollectionUtils.isEmpty(aloneConfigList)) {
            com.addx.iotcamera.util.LogUtil.error(logger, "aloneConfigList empty");
            return false;
        }

        for (DeviceAppSettingsAloneConfig config : aloneConfigList) {
            switch (DeviceUserConfigEnums.queryByCode(config.getCode())) {
                case WHITELIGHT:
                    setParameterRequest.setParameterValuePair("whiteLight", config.getValue());
                    break;
                default:
                    com.addx.iotcamera.util.LogUtil.error(logger, "setDeviceWhiteLight initParam error aloneConfigList 有值不在范围内:{}", serialNumber);
                    return false;
            }
        }
        return true;
    }

    /**
     * 新增的直播白光灯开关分类发送
     *
     * @param serialNumber
     * @param setParameterRequest
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    private boolean pushWhiteLightSwtich(String serialNumber, SetParameterRequest setParameterRequest) throws MqttException, IdNotSetException {
        WhiteLightParameterRequest whiteLightParameterRequest = new WhiteLightParameterRequest(serialNumber, setParameterRequest.getNameValuePairs());
        // 新旧两种通知方式，只要一个成功即可
        whiteLightParameterRequest.setId(setParameterRequest.getId());
        return VernemqPublisher.pushCmdMqttMessage(serialNumber, whiteLightParameterRequest);
    }

    /**
     * 修改设备默认编码
     *
     * @param config
     * @return
     */
    public Result updateDefaultCodec(DeviceConfigRequest config) {
        if (StringUtils.isEmpty(config.getDefaultCodec())) {
            return ResultCollection.getResult(ResultCollection.ILLEGAL_VALUE.getCode());
        }
        long startTime = System.currentTimeMillis();
        DeviceAppSettingsDO appSettingsDO = new DeviceAppSettingsDO().setSerialNumber(config.getSerialNumber())
                .setDefaultCodec(config.getDefaultCodec());
        Result result;
        try {
            result = deviceSettingService.updateUserConfig(config.getUserId(), appSettingsDO, startTime, false);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "设置设备默认编码发生异常! config={}", JSON.toJSONString(config), e);
            result = Result.Failure("设置设备默认编码发生异常!");
        }
        if (result.getResult() == Result.successFlag) {
            result = Result.KVResult("defaultCodec", config.getDefaultCodec());
        }
        return result;
    }

    /**
     * 修改设备声音
     *
     * @param config
     * @return
     */
    public Result updateDeviceAudio(DeviceConfigRequest config) {
        if (config.getDeviceAudio() == null) {
            return ResultCollection.getResult(ResultCollection.ILLEGAL_VALUE.getCode());
        }
        long startTime = System.currentTimeMillis();
        Optional<DeviceConfigRequest.DeviceAudio> deviceAudioOpt = Optional.ofNullable(config.getDeviceAudio());
        // 设置默认值，foolish demand
        DeviceAppSettingsDO appSettingsDO = new DeviceAppSettingsDO().setSerialNumber(config.getSerialNumber())
                .setLiveAudioToggleOn(deviceAudioOpt.map(it -> it.getLiveAudioToggleOn()).orElse(DEFAULT_VALUE_LIVE_AUDIO_TOGGLE_ON))
                .setRecordingAudioToggleOn(deviceAudioOpt.map(it -> it.getRecordingAudioToggleOn()).orElse(DEFAULT_VALUE_RECORDING_AUDIO_TOGGLE_ON))
                .setLiveSpeakerVolume(deviceAudioOpt.map(it -> it.getLiveSpeakerVolume()).orElse(DEFAULT_VALUE_LIVE_SPEAKER_VOLUME))
                .setDoorBellRingKey(config.getDeviceAudio().getDoorBellRingKey());
        Result result;
        try {
            result = deviceSettingService.updateUserConfig(config.getUserId(), appSettingsDO, startTime, false);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "设置设备音量发生异常! config={}", JSON.toJSONString(config), e);
            result = Result.Failure("设置设备音量发生异常!");
        }
        if (result.getResult() == Result.successFlag) {
            result = Result.KVResult("deviceAudio", config.getDeviceAudio());
        }
        return result;
    }

    /**
     * 获取设备声音
     *
     * @param userId
     * @param serialNumber
     * @return
     */
    public DeviceConfigRequest.DeviceAudio queryDeviceAudio(Integer userId, String serialNumber) {
        DeviceAppSettingsDO deviceAppSettingsDO = deviceSettingService.getDeviceSetting(serialNumber);
        if (deviceAppSettingsDO == null) return null;
        DeviceConfigRequest.DeviceAudio deviceAudio = new DeviceConfigRequest.DeviceAudio();
        deviceAudio.setLiveAudioToggleOn(deviceAppSettingsDO.getLiveAudioToggleOn());
        deviceAudio.setRecordingAudioToggleOn(deviceAppSettingsDO.getRecordingAudioToggleOn());
        deviceAudio.setLiveSpeakerVolume(deviceAppSettingsDO.getLiveSpeakerVolume());
        deviceAudio.setSupportDoorBellRingKey(deviceAppSettingsDO.getSupportDoorBellRingKey());
        deviceAudio.setDoorBellRingKey(deviceAppSettingsDO.getDoorBellRingKey());
        return deviceAudio;
    }

    /**
     * 修改门铃设置
     *
     * @param config
     * @return
     */
    @CacheEvict(value = "doorbellConfig", key = "#config.serialNumber")
    public Result updateDoorbellConfig(DeviceConfigRequest config) {
        if (config.getDoorbellConfig() == null) {
            return ResultCollection.getResult(ResultCollection.ILLEGAL_VALUE.getCode());
        }
        long startTime = System.currentTimeMillis();
        Optional<DeviceConfigRequest.DoorbellConfig> doorBellConfigOpt = Optional.ofNullable(config.getDoorbellConfig());
        // 设置默认值，foolish demand
        DeviceAppSettingsDO appSettingsDO = new DeviceAppSettingsDO().setSerialNumber(config.getSerialNumber())
                .setAlarmWhenRemoveToggleOn(doorBellConfigOpt.map(it -> it.getAlarmWhenRemoveToggleOn()).orElse(DEFAULT_VALUE_ALARM_WHEN_REMOVE_TOGGLE_ON));
        Result result;
        try {
            result = deviceSettingService.updateUserConfig(config.getUserId(), appSettingsDO, startTime, false);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "设置门铃发生异常! config={}", JSON.toJSONString(config), e);
            result = Result.Failure("设置门铃发生异常!");
        }
        return result;
    }

    /**
     * 获取门铃设置
     *
     * @param userId
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "doorbellConfig", key = "#serialNumber", unless = "#result==null")
    public DeviceConfigRequest.DoorbellConfig queryDoorbellConfig(Integer userId, String serialNumber) {
        DeviceAppSettingsDO deviceAppSettingsDO = deviceSettingService.getDeviceSetting(serialNumber);
        DeviceConfigRequest.DoorbellConfig doorbellConfig = new DeviceConfigRequest.DoorbellConfig();
        doorbellConfig.setAlarmWhenRemoveToggleOn(deviceAppSettingsDO.getAlarmWhenRemoveToggleOn());
        return doorbellConfig;
    }

    /**
     * 刷新设备的s3视频过期时间设置
     * 会查dynamodb和发送mqtt消息，应避免频繁刷新
     *
     * @param serialNumber
     * @return
     */
    public Result refreshS3VideoStorageDays(String serialNumber) {
        Result<JSONObject> result = deviceThirdConfigService.publishDeviceConfig(serialNumber, null);
        if (result.getResult() == Result.successFlag) {
            JSONObject bucket = JsonUtil.getJsonObjectByPath(result.getData(), "motion", "config", "config", "bucket");
            return Result.KVResult("storageDays", bucket.getInteger("defaultStorageDays"));
        } else {
            return Result.Error(result.getResult(), result.getMsg());
        }
    }

    /**
     * 按天刷新设备的s3视频过期时间设置，一天执行一次
     *
     * @param serialNumber
     * @return
     */
    public void refreshS3VideoStorageDaysByDay(String serialNumber) {
        String refreshS3VideoExpireDaysKey = String.join("", "refreshS3VideoExpireDays#", serialNumber);
        if (redisService.containsKey(refreshS3VideoExpireDaysKey)) {
            return;
        }

        redisService.set(refreshS3VideoExpireDaysKey, String.valueOf(System.currentTimeMillis()), 60);

        executor.execute(() -> {
            int adminId = userRoleService.getDeviceAdminUser(serialNumber);

            refreshS3VideoStorageDays(serialNumber);

            redisService.set(refreshS3VideoExpireDaysKey, String.valueOf(System.currentTimeMillis()), 24 * 60 * 60);

        });
    }

    /**
     * 根据用户vip和设备vip获取视频存储时长
     *
     * @param tenantId
     * @param userId
     * @param serialNumber
     * @return
     */
    public Tuple2<Integer, Long> getVideoRollingDays(String tenantId, Integer userId, String serialNumber) {
        // 默认按最长存储时长算，避免程序bug导致vip用户视频存储天数太短
        int rollingDays = 60;
        long vipExpireTime = System.currentTimeMillis() / 1000 + 365 * 24 * 60 * 60;
        try {
            PaasVipProduct deviceVipProduct = null;
            DeviceVipLog deviceVipLog = vipService.queryLastDeviceVip(tenantId, serialNumber);
            if (deviceVipLog != null && (PaasVipLevel.codeOf(deviceVipLog.getVipLevel()) != PaasVipLevel.NO_VIP) && org.apache.commons.lang3.StringUtils.isNotEmpty(deviceVipLog.getProductId())) {
                deviceVipProduct = deviceVipProductMap.get(deviceVipLog.getProductId());
                // 如果事先保存的deviceVipProductMap没有则实时查询一次
                if (deviceVipProduct == null) {
                    this.deviceVipProductMap = paasVipProductService.queryAllPaasVipProduct()
                            .stream().collect(Collectors.toMap(it -> it.getId(), it -> it));
                    deviceVipProduct = deviceVipProductMap.get(deviceVipLog.getProductId());
                }
            }
            if (deviceVipProduct != null) {
                // 设备vip没有过期时间
                rollingDays = deviceVipProduct.getLookBackDays();
            } else {
                Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(userId, serialNumber);
                if(currentTierId == null) {
                    UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, "en", tenantId);
                    currentTierId = userVipTier.getTierId();
                }

                Tier currentTier = tierService.queryTierById(currentTierId);
                if(currentTier != null) {
                    rollingDays = currentTier.getRollingDays();
                }
                vipExpireTime = userVipService.queryUserVipEndTime(userId, currentTierId, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "getVideoSliceRollingDays failed userId {} serialNumber {}", userId, serialNumber, e);
        }
        /** 下列日志不可去掉，查vip相关问题必备 **/
        MDC.put(MDCKeys.SERIAL_NUMBER, serialNumber);
        log.info("getVideoRollingDays tenantId={},userId={},sn={},rollingDays={},vipExpireTime={}", tenantId, userId, serialNumber, rollingDays, vipExpireTime);
        return new Tuple2<>(rollingDays, vipExpireTime);
    }

    public Tuple2<Integer, Long> getVideoRollingDaysOrNoPlan(String tenantId, Integer userId, String serialNumber) {
        boolean isNoPlan = vipService.isDeviceNoPlan(userId, serialNumber);
        // 图文事件 config
        if (isNoPlan) {
            int rollingDays = NoPlanRollingDays.NO_PLAN_VIDEO_ROW_ROLLING_DAYS;
            long vipExpireTime = 0L;
            return new Tuple2<>(rollingDays, vipExpireTime);
        }
        final CloudDeviceSupport support = deviceInfoService.getDeviceSupport(serialNumber);
        final DeviceSettingsDO settings = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        final String modelNo = StringUtils.isNotBlank(serialNumber) ? deviceManualService.getModelNoBySerialNumber(serialNumber) : "";
        final ThingModel thingModel = thingModelConfig.getThingModelByModelNo(modelNo);
        final boolean storeCameraLocal = DeviceSettingsDO.isStoreCameraLocal(thingModel, support, settings);
        // 摄像头本地视频，云端封面图和索引回看60天
        if (storeCameraLocal) {
            long vipExpireTime = PhosUtils.getUTCStamp() + VideoConstants.CAMERA_LOCAL_EXPIRE_DAYS;
            return new Tuple2<>(VideoConstants.CAMERA_LOCAL_ROLLING_DAYS, vipExpireTime);
        }
        Tuple2<Integer, Long> videoRollingDays = getVideoRollingDays(tenantId, userId, serialNumber);
        return videoRollingDays;
    }

}
