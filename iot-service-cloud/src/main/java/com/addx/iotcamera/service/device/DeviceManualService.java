package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.device.DeviceBindCode;
import com.addx.iotcamera.bean.device.DeviceModelNoResponse;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.response.device.ModelFirmwareUpgradeCount;
import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.service.device.model.DeviceModelZendeskService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.KernelFirmwareService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

import static org.addx.iot.common.enums.ResultCollection.DEVICE_NO_EXIT;

@Service
@Slf4j
public class DeviceManualService {

    @Resource
    private IDeviceManualDAO deviceManualDAO;

    @Autowired
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired
    private DeviceModelZendeskService deviceModelZendeskService;

    @Autowired
    private DeviceBindCodeService deviceBindCodeService;

    @Resource
    KernelFirmwareService kernelFirmwareService;

    @CacheEvict(value = {"deviceModelNoV2","deviceManual"}, key = "#deviceManual.serialNumber")
    public void addDeviceManual(DeviceManualDO deviceManual) {
        deviceManualDAO.addDeviceManual(deviceManual);
    }


    /**
     * 通过serialNumber获取modelNo
     *
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "deviceModelNoV2", key = "#serialNumber", unless = "#result==null")
    public String getModelNoBySerialNumber(String serialNumber) {
        log.debug("getModelNoBySerialNumber {}",serialNumber);
        return deviceManualDAO.getModelNoBySerialNumber(serialNumber);
    }

    @Cacheable(value = "deviceOriginalModelNo", key = "#serialNumber", unless = "#result==null")
    public String getOriginalModelNoBySerialNumber(String serialNumber) {
        return deviceManualDAO.getOriginalModelNoBySerialNumber(serialNumber);
    }

    /**
     * 更新mcu版本
     *
     * @param serialNumber
     * @param mcuNumber
     * @return
     */
    @CacheEvict(value = "deviceMcuVersion", key = "#serialNumber")
    public void updateMcuVersionBySerialNumber(String serialNumber, String mcuNumber) {
        log.info("设备{}更新mcuVersion{}", serialNumber, mcuNumber);
        if (mcuNumber != null) {
            deviceManualDAO.updateMcuVersionBySerialNumber(serialNumber, mcuNumber);
        }
    }

    /**
     * 查询设备mcuVersion
     *
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "deviceMcuVersion", key = "#serialNumber", unless = "#result==null")
    public String queryMcuVersionBySerialNumber(String serialNumber) {
        return deviceManualDAO.queryMcuVersionBySerialNumber(serialNumber);
    }


    @Cacheable(value = "deviceManual", key = "#serialNumber", unless = "#result==null")
    public DeviceManualDO getDeviceManualBySerialNumber(String serialNumber) {
        return deviceManualDAO.getDeviceManualBySerialNumber(serialNumber);
    }

    /**
     * 通过userSN获取serialNumber
     *
     * @param userSn
     * @return
     */
    public String getSerialNumberByUserSn(String userSn) {
        return deviceManualDAO.getSerialNumberByUserSn(userSn);
    }


    /**
     * 通过userSn获取modelNo
     *
     * @param userSn
     * @return
     */
    public Result getModelNoByUserSn(String userSn, String bindCode) {
        DeviceManualDO deviceManualDO = deviceManualDAO.getModelNoByUserSn(userSn);
        if (deviceManualDO != null) {
            this.updateDeviceBindCode(bindCode, userSn, deviceManualDO.getModelNo());

            return new Result(DeviceModelNoResponse.builder()
                    .modelNo(deviceManualDO.getModelNo())
                    .zendesk(this.getDeviceZendesk(deviceManualDO.getModelNo()))
                    .build());
        }
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureByUserSn(userSn);
        if (deviceManufactureTableDO == null) {
            // 无效的设备
            return Result.Error(DEVICE_NO_EXIT, "DEVICE_NO_EXIT");
        }
        String modelNo = StringUtils.isEmpty(deviceManufactureTableDO.getRegisterModelNo()) ? deviceManufactureTableDO.getModelNo() :
                deviceManufactureTableDO.getRegisterModelNo();
        this.updateDeviceBindCode(bindCode, userSn, modelNo);
        return new Result(DeviceModelNoResponse.builder()
                .modelNo(modelNo)
                .zendesk(this.getDeviceZendesk(modelNo))
                .build());
    }

    private String getDeviceZendesk(String modelNo) {
        return deviceModelZendeskService.queryDeviceModelZendesk(modelNo);
    }

    /**
     * 根据bindCode 更新设备号、设备型号到 bind_operation
     *
     * @param bindCode
     * @param userSn
     * @param modelNo
     */
    private void updateDeviceBindCode(String bindCode, String userSn, String modelNo) {
        if (StringUtils.isEmpty(bindCode)) {
            // 老版本APP 不传bindCode
            return;
        }
        DeviceBindCode deviceBindCode = DeviceBindCode.builder()
                .bindCode(bindCode)
                .userSn(userSn)
                .modelNo(modelNo)
                .build();
        log.info("updateBindOperation param:{}", deviceBindCode);
        deviceBindCodeService.updateDeviceBindCode(deviceBindCode);
    }

    /**
     * 升级到指定固件版本的型号
     * @param firmwareId
     * @return
     */
    public List<ModelFirmwareUpgradeCount> queryModelFirmwareUpgradeCount(String firmwareId){
        //firmwareId 对应的设备 modelNo
        List<String> modelNoList = kernelFirmwareService.queryFirmwareByFirmwareId(firmwareId);
        if(CollectionUtils.isEmpty(modelNoList)){
            return Lists.newArrayList();
        }
        //升级到firmwareId 的设备数量,按modelNo 分组
       return deviceManualDAO.queryModelFirmwareUpgradeCount(modelNoList,firmwareId);
    }

    public List<DeviceManualDO> batchQueryDeviceManualDO(Integer offset, Integer maxNum) {
        return deviceManualDAO.batchQueryDeviceManualDO(offset, maxNum);
    }

    public List<DeviceManualDO> batchQueryDeviceManualDOByMaxId(Long maxId, Integer maxNum) {
        return deviceManualDAO.batchQueryDeviceManualDOByMaxId(maxId, maxNum);
    }

    public DeviceManualDO getDeviceManualByUserSn(String userSn) {
        return deviceManualDAO.getModelNoByUserSn(userSn);
    }
}
