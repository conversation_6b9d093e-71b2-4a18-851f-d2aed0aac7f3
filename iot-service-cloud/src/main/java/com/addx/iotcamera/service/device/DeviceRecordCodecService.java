package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.device.CodecProfileListDO;
import com.addx.iotcamera.bean.device.CodecProfileValueDO;
import com.addx.iotcamera.bean.device.RecordingCodecProfileDO;
import com.addx.iotcamera.bean.device.RecordingCodecUpdateRequest;
import com.addx.iotcamera.bean.device.vo.RecordingCodecConfigVO;
import com.addx.iotcamera.dao.device.recordcodec.CodecProfileListDAO;
import com.addx.iotcamera.dao.device.recordcodec.CodecProfileValueDAO;
import com.addx.iotcamera.dao.device.recordcodec.RecordingCodecProfileDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceRecordCodecService {
    @Autowired
    private CodecProfileListDAO codecProfileListDAO;
    @Autowired
    private CodecProfileValueDAO codecProfileValueDAO;
    @Autowired
    private RecordingCodecProfileDAO recordingCodecProfileDAO;

    /**
     * 查询设备 recordCodec 配置
     *
     * @param serialNumber 设备序列号
     * @return 结果VO
     */
    public RecordingCodecConfigVO queryConfig(String serialNumber) {
        RecordingCodecProfileDO recordingCodecProfileDO = recordingCodecProfileDAO.selectOneBySn(serialNumber);
        if (recordingCodecProfileDO == null) {
            return null;
        }
        RecordingCodecConfigVO recordingCodecConfigVO = new RecordingCodecConfigVO();
        recordingCodecConfigVO.setSerialNumber(recordingCodecProfileDO.getSerialNumber());
        recordingCodecConfigVO.setVersion(recordingCodecProfileDO.getVersion());
        recordingCodecConfigVO.setUploadFileCnt(recordingCodecProfileDO.getUploadFileCnt());
        recordingCodecConfigVO.setUploadTimeSpentUpperLimit(recordingCodecProfileDO.getUploadTimeSpentUpperLimit());
        recordingCodecConfigVO.setUploadTimeSpentLowerLimit(recordingCodecProfileDO.getUploadTimeSpentLowerLimit());
        List<CodecProfileListDO> codecProfileListDOS = codecProfileListDAO.selectListByConfigId(recordingCodecProfileDO.getId());
        if (codecProfileListDOS != null && !codecProfileListDOS.isEmpty()) {
            List<RecordingCodecConfigVO.ProfileConfig> profileList = new ArrayList<>();
            for (CodecProfileListDO codecProfileListDO : codecProfileListDOS) {
                RecordingCodecConfigVO.ProfileConfig profileConfig = new RecordingCodecConfigVO.ProfileConfig();
                profileConfig.setName(codecProfileListDO.getName());
                profileConfig.setDisplayName(codecProfileListDO.getName());
                List<CodecProfileValueDO> codecProfileValueDOList = codecProfileValueDAO.selectListByProfileId(codecProfileListDO.getId());
                if (codecProfileValueDOList != null && !codecProfileValueDOList.isEmpty()) {
                    List<RecordingCodecConfigVO.ProfileValue> values = new ArrayList<>();
                    for (CodecProfileValueDO codecProfileValueDO : codecProfileValueDOList) {
                        RecordingCodecConfigVO.ProfileValue profileValue = getProfileValue(codecProfileValueDO);
                        values.add(profileValue);
                    }
                    profileConfig.setValues(values);
                }
                profileList.add(profileConfig);
            }
            recordingCodecConfigVO.setProfileList(profileList);
        }
        return recordingCodecConfigVO;
    }

    private static RecordingCodecConfigVO.ProfileValue getProfileValue(CodecProfileValueDO codecProfileValueDO) {
        RecordingCodecConfigVO.ProfileValue profileValue = new RecordingCodecConfigVO.ProfileValue();
        profileValue.setBr(codecProfileValueDO.getBr());
        profileValue.setCc(codecProfileValueDO.getCc());
        profileValue.setCh(codecProfileValueDO.getCh());
        profileValue.setMbr(codecProfileValueDO.getMbr());
        profileValue.setRes(codecProfileValueDO.getRes());
        profileValue.setTh(codecProfileValueDO.getTh());
        profileValue.setXbr(codecProfileValueDO.getXbr());
        return profileValue;
    }

    /**
     * 更新某个设备recordCodec配置，采用全删加的模式
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(RecordingCodecUpdateRequest request) {
        // 先全量删
        RecordingCodecProfileDO recordingCodecProfileDO = recordingCodecProfileDAO.selectOneBySn(request.getSerialNumber());
        if (recordingCodecProfileDO != null) {
            long configId = recordingCodecProfileDO.getId();
            List<Long> listIds = new ArrayList<>();
            List<Long> valueIds = new ArrayList<>();
            List<CodecProfileListDO> codecProfileValueDOList = codecProfileListDAO.selectListByConfigId(configId);
            if (CollectionUtils.isNotEmpty(codecProfileValueDOList)) {
                listIds.addAll(codecProfileValueDOList.stream().map(CodecProfileListDO::getId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(listIds)) {
                    List<Long> longs = codecProfileValueDAO.selectIds(listIds);
                    if (CollectionUtils.isNotEmpty(longs)) {
                        valueIds.addAll(longs);
                        codecProfileValueDAO.deleteByids(valueIds);
                    }
                    codecProfileListDAO.deleteByIds(listIds);
                }
            }
            recordingCodecProfileDAO.deleteById(configId);
        }

        // 再全量加
        if (request.getThresholdConfig() != null && CollectionUtils.isNotEmpty(request.getProfileList())) {
            // 1. 插入主配置记录
            RecordingCodecProfileDO newProfileDO = new RecordingCodecProfileDO()
                    .setSerialNumber(request.getSerialNumber())
                    .setModelNo(request.getModelNo())
                    .setVersion("1.0")  // 可根据实际需要修改版本号
                    .setUploadFileCnt(request.getThresholdConfig().getUploadFileCnt())
                    .setUploadTimeSpentUpperLimit(request.getThresholdConfig().getUploadTimeSpentUpperLimit())
                    .setUploadTimeSpentLowerLimit(request.getThresholdConfig().getUploadTimeSpentLowerLimit());

            recordingCodecProfileDAO.insert(newProfileDO);
            long newConfigId = newProfileDO.getId();  // 获取生成的主配置ID

            // 2. 逐个插入档位配置并获取生成的ID，然后插入对应的编码参数值
            List<CodecProfileValueDO> valueListToInsert = new ArrayList<>();
            for (RecordingCodecConfigVO.ProfileConfig profile : request.getProfileList()) {
                // 先插入单个档位配置
                CodecProfileListDO profileListDO = new CodecProfileListDO()
                        .setConfigId(newConfigId)
                        .setName(profile.getName());
                codecProfileListDAO.insert(profileListDO);

                // 获取生成的profileId
                long profileId = profileListDO.getId();

                // 为这个档位插入所有编码参数值
                if (CollectionUtils.isNotEmpty(profile.getValues())) {
                    for (RecordingCodecConfigVO.ProfileValue value : profile.getValues()) {
                        CodecProfileValueDO valueDO = new CodecProfileValueDO()
                                .setProfileId(profileId)
                                .setCh(value.getCh())
                                .setCc(value.getCc())
                                .setRes(value.getRes())
                                .setBr(value.getBr())
                                .setMbr(value.getMbr())
                                .setXbr(value.getXbr())
                                .setTh(value.getTh());
                        valueListToInsert.add(valueDO);
                    }
                }
            }

            // 3. 批量插入所有编码参数值
            if (CollectionUtils.isNotEmpty(valueListToInsert)) {
                codecProfileValueDAO.insertBatch(valueListToInsert);
            }
        }
    }

    /**
     * 根据型号查询有单独录像编码配置的设备序列号列表
     *
     * @param modelNo 设备型号
     * @return 设备序列号集合
     */
    public Set<String> querySerialNumbersByModelNo(String modelNo) {
        return recordingCodecProfileDAO.selectSerialNumbersByModelNo(modelNo);
    }

    /**
     * 查询设备的录像编码配置，用于动态配置
     *
     * @param serialNumber 设备序列号
     * @return 录像编码配置Map
     */
    public Map<String, Object> queryDeviceRecordCodecConfig(String serialNumber) {
        RecordingCodecConfigVO config = queryConfig(serialNumber);
        if (config == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();

        // 构建阈值配置
        Map<String, Object> thresholdConfig = new HashMap<>();
        thresholdConfig.put("uploadFileCnt", config.getUploadFileCnt());
        thresholdConfig.put("uploadTimeSpentUpperLimit", config.getUploadTimeSpentUpperLimit());
        thresholdConfig.put("uploadTimeSpentLowerLimit", config.getUploadTimeSpentLowerLimit());
        result.put("thresholdConfig", thresholdConfig);

        // 构建档位配置列表
        if (CollectionUtils.isNotEmpty(config.getProfileList())) {
            List<Map<String, Object>> profileList = new ArrayList<>();
            for (RecordingCodecConfigVO.ProfileConfig profile : config.getProfileList()) {
                Map<String, Object> profileMap = new HashMap<>();
                profileMap.put("name", profile.getName());
                profileMap.put("displayName", profile.getDisplayName());

                if (CollectionUtils.isNotEmpty(profile.getValues())) {
                    List<Map<String, Object>> values = new ArrayList<>();
                    for (RecordingCodecConfigVO.ProfileValue value : profile.getValues()) {
                        Map<String, Object> valueMap = new HashMap<>();
                        valueMap.put("ch", value.getCh());
                        valueMap.put("cc", value.getCc());
                        if (value.getRes().contains("auto")) {
                            valueMap.put("res", "auto");
                        } else {
                            valueMap.put("res", value.getRes());
                        }
                        valueMap.put("br", value.getBr());
                        valueMap.put("mbr", value.getMbr());
                        valueMap.put("xbr", value.getXbr());
                        valueMap.put("th", value.getTh());

                        // 默认值
                        valueMap.put("rc", "cbr");
                        valueMap.put("fr", 15);
                        valueMap.put("gop", 30);
                        valueMap.put("mqp", 9);
                        valueMap.put("xqp", 48);
                        values.add(valueMap);
                    }
                    profileMap.put("values", values);
                }
                profileList.add(profileMap);
            }
            result.put("profileList", profileList);
        }

        return result;
    }

}
