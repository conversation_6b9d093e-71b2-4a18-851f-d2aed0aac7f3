package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.device.DeviceMessageNotification;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.device.*;
import com.addx.iotcamera.dao.MessageNotificationSettingsDao;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.Setter;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitch;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Boolean.TRUE;

@Service
public class MessageNotificationSettingsService {
    private final static Logger logger = LoggerFactory.getLogger(MessageNotificationSettingsService.class);

    public static final String OTHER_MESSAGE_NOTIFICATION = "other";

    @Setter
    @Autowired
    private MessageNotificationSettingsDao messageNotificationSettingsDao;

    @Autowired
    private VicoMessageNotificationConfig vicoMessageNotificationConfig;
    @Autowired
    private SafemoMessageNotificationConfig safemoMessageNotificationConfig;

    @Autowired
    private MessageNotificationConfigUnified messageNotificationConfigUnified;

    @Autowired
    private ModelAiEventConfig modelAiEventConfig;

    @Autowired
    private DeviceManualService deviceManualService;

    @Resource
    private DeviceModelEventService deviceModelEventService;
    @Autowired
    private VipService vipService;

    @Autowired
    private DeviceAiSettingsService deviceAiSettingsService;
    @Autowired
    private AiAssistService aiAssistService;
    @Autowired
    private UserService userService;

    @Autowired @Lazy
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceSettingService deviceSettingService;


    private MessageNotificationConfig getVicoMessageNotificationConfig(Integer userId) {
        User user = userService.queryUserById(userId);
        return AppConstants.TENANTID_SAFEMO.equals(user.getTenantId()) ? safemoMessageNotificationConfig : vicoMessageNotificationConfig;
    }

    public MessageNotificationConfig getMessageNotificationConfig(Integer userId, boolean isUnified) {
        User user = userService.queryUserById(userId);
        return isUnified ? messageNotificationConfigUnified.getConfig(user.getTenantId())
                : getVicoMessageNotificationConfig(userId);
    }


    public List<DeviceMessageNotification> queryMessageNotificationSettingsListV1(String serialNumber, Integer userId) {
        logger.info("queryMessageNotificationSettingsListV1,userId:{},serialNumber:{}", userId, serialNumber);        
        MessageNotificationSetting messageNotificationSetting = messageNotificationSettingsDao.getMessageNotificationSettingEvent(userId, serialNumber);
        Set<String> enableEventObjectNames = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber)
                .stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        String tenantId = userService.queryTenantIdById(userId);
        List<DeviceMessageNotification> options = AppConstants.TENANTID_SAFEMO.equals(tenantId) ?
                initDeviceMessageNotificationListSafemo(userId, serialNumber, messageNotificationSetting, enableEventObjectNames)
                : initDeviceMessageNotificationListV2(userId, serialNumber, messageNotificationSetting, enableEventObjectNames);
//        if (userVipState == UserVipState.VIP) {
        Integer enableOther = Optional.ofNullable(messageNotificationSetting).filter(it -> it.getEnableOther() != null).map(it -> it.getEnableOther()).orElse(0);
        options.add(new DeviceMessageNotification(OTHER_MESSAGE_NOTIFICATION, enableOther == 1, Collections.emptyList()));
        return options;
    }

    public List<DeviceMessageNotification> queryMessageNotificationSettingsListV3(String serialNumber, Integer userId) {
        logger.info("queryMessageNotificationSettingsListV3,userId:{},serialNumber:{}", userId, serialNumber);
        MessageNotificationSetting messageNotificationSetting = messageNotificationSettingsDao.getMessageNotificationSettingEvent(userId, serialNumber);
        List<DeviceMessageNotification> options =  initDeviceMessageNotificationListV3(userId, serialNumber, messageNotificationSetting);
        boolean showEnableOther = options.stream().anyMatch(DeviceMessageNotification::getEnable);
        Integer enableOther = Optional.ofNullable(messageNotificationSetting).filter(it -> it.getEnableOther() != null).map(it -> it.getEnableOther()).orElse(0);

        options.add(new DeviceMessageNotification(OTHER_MESSAGE_NOTIFICATION, true, new ArrayList<>(){{ add(new DeviceMessageNotification("other_any", enableOther == 1, new ArrayList<>())); }}).setEnable(showEnableOther));
        return options;
    }


    /**
     * 查询单条设备推送选项
     *
     * @param serialNumber
     * @param userId
     * @return
     */
    @Cacheable(value = "messageNotificationSetting", key = "#userId+'-'+#serialNumber", unless = "#result==null")
    public MessageNotificationSetting queryMessageNotificationSetting(String serialNumber, Integer userId) {
        return messageNotificationSettingsDao.getMessageNotificationSettingEvent(userId, serialNumber);
    }

    @CacheEvict(value = {"messageNotificationSetting"}, key = "#model.userId+'-'+#model.serialNumber")
    public int insertMessageNotificationSettings(MessageNotificationSetting model) {
        logger.info("insertMessageNotificationSettings userId:{},serialNumber:{},eventObjects={},eventTypes={},enableOther={}"
                , model.getUserId(), model.getSerialNumber(), model.getEventObjects(), model.getPackageEventType(), model.getPersonEventType(), model.getPetEventType(), model.getVehicleEventType(), model.getEnableOther());
        return messageNotificationSettingsDao.insertMessageNotificationSettings(model);
    }

    @CacheEvict(value = { "messageNotificationSetting"}, key = "#model.userId+'-'+#model.serialNumber")
    public int updateMessageNotificationSettings(MessageNotificationSetting model) {
        logger.info("updateMessageNotificationSettings userId:{},serialNumber:{},eventObjects={},eventTypes={},enableOther={}"
                , model.getUserId(), model.getSerialNumber(), model.getEventObjects(), model.getPackageEventType(), model.getEnableOther());
        return messageNotificationSettingsDao.updateMessageNotificationSettings(model);
    }


    @CacheEvict(value = {"messageNotificationSetting"}, key = "#userId+'-'+#serialNumber")
    public int deleteMessageNotificationSettings(Integer userId, String serialNumber) {
        logger.info("deleteMessageNotificationSettings,userId:{},serialNumber:{}", userId, serialNumber);
        return messageNotificationSettingsDao.deleteMessageNotificationSettings(userId, serialNumber);
    }

    @CacheEvict(value = {"messageNotificationSetting"}, key = "#userId+'-'+#serialNumber")
    public int updateEnableOther(Integer userId, String serialNumber, Integer enableOther) {
        logger.info("updateEnableOther userId={},serialNumber={},enableOther={}", userId, serialNumber, enableOther);
        return messageNotificationSettingsDao.messageNotificationSettingsDao(userId, serialNumber, enableOther);
    }

    // 构建MessageNotificationSetting初始状态
    public MessageNotificationSetting buildMessageNotificationSetting(Integer userId, String serialNumber) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        LinkedHashSet<String> modelEventObjects = (LinkedHashSet<String>) deviceModelEventService.queryDeviceModelEvent(modelNo);
        List<String> eventObjects = new LinkedList<>();
        List<String> eventTypes = new LinkedList<>();
        for (Map.Entry<String, ImmutableSet<String>> entry : DeviceAiSettingsService.getInitNotifyEventTypes(userService.getUserTenantId(userId)).entrySet()) {
            if (!modelEventObjects.contains(entry.getKey())) continue;
            eventObjects.add(entry.getKey());
            eventTypes.addAll(entry.getValue());
        }
        return MessageNotificationSetting.builder()
                .userId(userId)
                .serialNumber(serialNumber)
                .eventObjects(org.apache.commons.lang3.StringUtils.join(eventObjects, ','))
                .packageEventType(org.apache.commons.lang3.StringUtils.join(eventTypes, ','))
                .enableOther(0)
                .build();
    }

    /**
     * 获取初始结构
     *
     * @return
     */
    public List<DeviceMessageNotification> initDeviceMessageNotificationListV2(Integer userId, String serialNumber
            , MessageNotificationSetting messageNotificationSetting, Set<String> enableEventObjectNames) {

        Set<String> eventObjects = messageNotificationSetting == null ? Collections.emptySet() :
                TextUtil.splitToNotBlankSet(messageNotificationSetting.getEventObjects(), ',');
        Set<String> eventTypes = messageNotificationSetting == null ? Collections.emptySet() :
                TextUtil.splitToNotBlankSet(messageNotificationSetting.getPackageEventType(), ',');

        DeviceAiSwitch deviceAiSwitch = aiAssistService.queryEventObjectSwitch(userId, serialNumber).getData();
        Set<String> canModifyEventObjects = deviceAiSwitch.getList().stream().filter(eventObjectSwitch -> BooleanUtils.isTrue(eventObjectSwitch.getCanModify()))
                .map(EventObjectSwitch::getEventObject).collect(Collectors.toSet());

        List<DeviceMessageNotification> result = new LinkedList<>();
        for (DeviceMessageNotification item : getMessageNotificationConfig(userId, false).getMessage()) {
            if (!canModifyEventObjects.contains(item.getName())) continue;
            DeviceMessageNotification bean = item.copy().setSubEvent(new LinkedList<>())
                    .setEnable(enableEventObjectNames.contains(item.getName()))
                    .setChoice(eventObjects.contains(item.getName()));
            result.add(bean);
            if (CollectionUtils.isEmpty(item.getSubEvent())) continue;
            int choiceNum = 0;
            for (DeviceMessageNotification subItem : item.getSubEvent()) {
                DeviceMessageNotification subBean = new DeviceMessageNotification(subItem.getName(), eventTypes.contains(subItem.getName()), new LinkedList<>());
                bean.getSubEvent().add(subBean);
                if (subBean.getChoice()) choiceNum++;
            }
            bean.setChoice(choiceNum > 0);
        }
        return result;
    }

    /**
     * 获取初始结构
     *
     * @return
     */
    public List<DeviceMessageNotification> initDeviceMessageNotificationListV3(Integer userId, String serialNumber
            , MessageNotificationSetting messageNotificationSetting) {
        Set<String> eventObjects = messageNotificationSetting == null ? Collections.emptySet() :
                TextUtil.splitToNotBlankSet(messageNotificationSetting.getEventObjects(), ',');
        Set<String> eventTypes = messageNotificationSetting == null ? Collections.emptySet() :
                TextUtil.splitToNotBlankSet(messageNotificationSetting.getPackageEventType(), ',');
        if(messageNotificationSetting != null && messageNotificationSetting.getPersonEventType() != null){
            eventTypes.addAll(TextUtil.splitToNotBlankSet(messageNotificationSetting.getPersonEventType(), ','));
        }
        if(messageNotificationSetting != null && messageNotificationSetting.getPetEventType() != null){
            eventTypes.addAll(TextUtil.splitToNotBlankSet(messageNotificationSetting.getPetEventType(), ','));
        }
        if(messageNotificationSetting != null && messageNotificationSetting.getVehicleEventType() != null){
            eventTypes.addAll(TextUtil.splitToNotBlankSet(messageNotificationSetting.getVehicleEventType(), ','));
        }

        boolean isVip = vipService.isVipDevice(userId, serialNumber);
        CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getRowDeviceSupport(serialNumber);
        DeviceAppSettingsDO deviceAppSettingsDO = deviceSettingService.getDeviceSetting(serialNumber);
        Set<String> deviceSupportTagEventObjects = new HashSet<>();
        if(Objects.equals(cloudDeviceSupport.getSupportJson().getBoolean("supportPersonTag"), true)
            || TRUE.equals(deviceAppSettingsDO.getReportPersonAi())) {
            deviceSupportTagEventObjects.add(AiObjectEnum.PERSON.getName());
        }
        if(Objects.equals(cloudDeviceSupport.getSupportJson().getBoolean("supportPetTag"), true)
                || TRUE.equals(deviceAppSettingsDO.getReportPetAi())) {
            deviceSupportTagEventObjects.add(AiObjectEnum.PET.getName());
        }
        DeviceAiSwitch deviceAiSwitch = aiAssistService.queryEventObjectSwitch(userId, serialNumber).getData(); // 老的AI 包含云端和设备端支持的事件对象
        Set<String> canModifyEventObjects = deviceAiSwitch.getList().stream()
                .filter(eventObjectSwitch -> BooleanUtils.isTrue(eventObjectSwitch.getCanModify()))
                .map(EventObjectSwitch::getEventObject).collect(Collectors.toSet());
        List<DeviceMessageNotification> result = new LinkedList<>();
        for (DeviceMessageNotification item : getMessageNotificationConfig(userId, true).getMessage()) {
            if (!canModifyEventObjects.contains(item.getName())
                    && !deviceSupportTagEventObjects.contains(item.getName())) continue;
            DeviceMessageNotification bean = item.copy().setSubEvent(new LinkedList<>())
                    .setEnable(isVip || deviceSupportTagEventObjects.contains(item.getName()))
                    .setChoice(eventObjects.contains(item.getName()));
            result.add(bean);
            if (CollectionUtils.isEmpty(item.getSubEvent())) continue;
            for (DeviceMessageNotification subItem : item.getSubEvent()) {
                DeviceMessageNotification subBean = new DeviceMessageNotification(subItem.getName(), eventTypes.contains(subItem.getName()), new LinkedList<>());
                subBean.setEnable(isVip || deviceSupportTagEventObjects.contains(subItem.getName()));
                bean.getSubEvent().add(subBean);
            }

        }
        return result;
    }

    public List<DeviceMessageNotification> initDeviceMessageNotificationListSafemo(Integer userId, String serialNumber
            , MessageNotificationSetting messageNotificationSetting, Set<String> enableEventObjectNames) {

        Set<String> eventObjects = messageNotificationSetting == null ? Collections.emptySet() :
                TextUtil.splitToNotBlankSet(messageNotificationSetting.getEventObjects(), ',');

        DeviceAiSwitch deviceAiSwitch = aiAssistService.queryEventObjectSwitchSafemo(userId, serialNumber).getData();
        Set<String> canModifyEventObjects = deviceAiSwitch.getList().stream().filter(eventObjectSwitch -> BooleanUtils.isTrue(eventObjectSwitch.getCanModify()))
                .map(EventObjectSwitch::getEventObject).collect(Collectors.toSet());

        List<DeviceMessageNotification> result = new LinkedList<>();
        for (DeviceMessageNotification item : safemoMessageNotificationConfig.getMessage()) {
            if (!canModifyEventObjects.contains(item.getName())) continue;
            DeviceMessageNotification bean = item.copy().setSubEvent(new LinkedList<>())
                    .setEnable(enableEventObjectNames.contains(item.getName()))
                    .setChoice(eventObjects.contains(item.getName()));
            result.add(bean);
        }
        return result;
    }

    public Map<String, List<String>> filterEventTypeMapByMessageRule(String serialNumber, Map<String, List<String>> eventObjectTypeMap) {

        logger.debug("filterEventTypeMapByMessageRule messageNotificationConfig {}", JSON.toJSONString(vicoMessageNotificationConfig));

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        Set<String> queryDeviceModelEvent = deviceModelEventService.queryDeviceModelEvent(modelNo);
        Map<String, List<String>> map = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : eventObjectTypeMap.entrySet()) {
            if(!queryDeviceModelEvent.contains(entry.getKey())){
                continue;
            }
            LinkedList<String> list = new LinkedList<>();
            if (!vicoMessageNotificationConfig.getParentEvents().contains(entry.getKey())) {
                // 无子事件， 默认加入any表示所有事件
                if(CollectionUtils.isEmpty(entry.getValue())){
                    map.put(entry.getKey(), List.of(String.format("%s_any", entry.getKey())));
                    logger.debug("filterEventTypeMapByMessageRule map put {}, map:{}", entry.getKey(), map);
                    continue;
                }
                logger.debug("filterEventTypeMapByMessageRule map put {}", JSON.toJSONString(vicoMessageNotificationConfig.getParentEvents()));
                map.put(entry.getKey(), list);
                continue;
            }
            // 有子级的，必须子级集合不为空
            if (CollectionUtils.isEmpty(entry.getValue())) {
                logger.error("filterEventTypeMapByMessageRule map not put {}, map:{}", entry.getKey(), map);
                continue;
            }
            map.put(entry.getKey(), list);
            for (String subEventType : new HashSet<>(entry.getValue())) {
                // 增加消息类型关系校验
                if (!vicoMessageNotificationConfig.isSubEvent(subEventType, entry.getKey())) {
                    throw new IllegalArgumentException("参数错误！" + subEventType + "不是" + entry.getKey() + "的子消息类型");
                }
                list.add(subEventType);
            }
        }
        logger.info("filterEventTypeMapByMessageRule serialNumber:{} eventObjectTypeMap:{} eventObjectTypeMap2:{}", serialNumber, eventObjectTypeMap, map);
        return map;
    }

    public Map<String, List<String>> filterEventTypeMapByMessageUnifiedRule(Integer userId, String serialNumber, Map<String, List<String>> eventObjectTypeMap) {
        MessageNotificationConfig config = messageNotificationConfigUnified.getConfig(userService.queryTenantIdById(userId));
        logger.debug("filterEventTypeMapByMessageUnifiedRule messageNotificationConfig {}", JSON.toJSONString(config));

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        Map<String, List<String>> map = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : eventObjectTypeMap.entrySet()) {
            if(!deviceModelEventService.queryDeviceModelEvent(modelNo).contains(entry.getKey())){
                continue;
            }
            LinkedList<String> list = new LinkedList<>();
            map.put(entry.getKey(), list);
            if (!config.getParentEvents().contains(entry.getKey())) {
                logger.debug("filterEventTypeMapByMessageUnifiedRule map put {}", JSON.toJSONString(config.getParentEvents()));
                continue;
            }
            for (String subEventType : new HashSet<>(entry.getValue())) {
                // 增加消息类型关系校验
                if (!config.isSubEvent(subEventType, entry.getKey())) {
                    throw new IllegalArgumentException("参数错误！" + subEventType + "不是" + entry.getKey() + "的子消息类型");
                }
                list.add(subEventType);
            }
        }
        logger.info("filterEventTypeMapByMessageUnifiedRule serialNumber:{} eventObjectTypeMap:{} eventObjectTypeMap2:{}", serialNumber, eventObjectTypeMap, map);
        return map;
    }

    public Map<String, List<String>> filterEventTypeMapByMessageRuleSafemo(String serialNumber, Map<String, List<String>> eventObjectTypeMap) {
        final ImmutableMap<String, ImmutableSet<String>> initEventType= DeviceAiSettingsService.getInitNotifyEventTypes(AppConstants.TENANTID_SAFEMO);
        Map<String, List<String>> map = new LinkedHashMap<>();
        for(Map.Entry<String, List<String>> entry : eventObjectTypeMap.entrySet()){
            if(! initEventType.containsKey(entry.getKey())) {
                throw new IllegalArgumentException("Argument error！Event object" + entry.getKey() + "is invalid!");
            }
            map.put(entry.getKey(), initEventType.get(entry.getKey()).stream().collect(Collectors.toList()));
        }
        return map;
    }

    public Map<String, Object> queryReIdNotifySetting(Integer userId, String serialNumber) {
        return messageNotificationSettingsDao.queryReIdNotifySetting(userId, serialNumber);
    }

    @CacheEvict(value = {"messageNotificationSetting"}, key = "#userId+'-'+#serialNumber")
    public int updateReIdNotifySetting(Integer userId, String serialNumber, Map<String, Object> updateMap) {
        logger.info("updateNotifySetting userId={},sn={},updateMap={}", userId, serialNumber, JSON.toJSONString(updateMap));
        if (updateMap == null || updateMap.isEmpty()) return 0;
        return messageNotificationSettingsDao.updateReIdNotifySetting(userId, serialNumber, updateMap);
    }
}
