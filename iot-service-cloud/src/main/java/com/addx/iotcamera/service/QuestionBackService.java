package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.questionback.QuestionBackCommitRequest;
import com.addx.iotcamera.bean.app.questionback.QuestionBackOptionsRequest;
import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackDO;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackData;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackOption;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.video.StoreBucket;
import com.addx.iotcamera.config.CosConfig;
import com.addx.iotcamera.config.GcsOptions;
import com.addx.iotcamera.config.OciConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.dao.library.QuestionBackDAO;
import com.addx.iotcamera.enums.ShortSummaryQuestionEnums;
import com.addx.iotcamera.enums.VideoQuestionEnums;
import com.addx.iotcamera.enums.VideoTagQuestionEnums;
import com.addx.iotcamera.enums.VideoType;
import com.addx.iotcamera.helper.GoogleStorageService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3Object;
import com.google.cloud.storage.BlobInfo;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.URI;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.NO_LIBRARY_ACCESS;


@Slf4j
@Component
public class QuestionBackService {

    @Autowired
    private QuestionBackDAO questionBackDAO;

    @Lazy
    @Autowired
    private S3Service s3Service;
    @Lazy
    @Autowired
    private VipService vipService;

    @Lazy
    @Autowired
    private CosService cosService;
    @Lazy
    @Autowired
    private OciService ociService;
    @Autowired
    private GoogleStorageService googleStorageService;
    @Lazy
    @Autowired
    private LibraryService libraryService;
    @Lazy
    @Autowired
    private LibraryStatusService libraryStatusService;
    @Lazy
    @Autowired
    private DeviceModelEventService deviceModelEventService;
    @Lazy
    @Autowired
    private DeviceManualService deviceManualService;
    @Lazy
    @Autowired
    private VideoService videoService;
    @Lazy
    @Autowired
    private VideoStoreService videoStoreService;
    @Lazy
    @Autowired
    private S3 s3;
    @Lazy
    @Autowired
    private GcsOptions gcsOptions;
    @Lazy
    @Autowired
    private CosConfig cosConfig;
    @Lazy
    @Autowired
    private OciConfig ociConfig;
    @Autowired
    private UserService userService;

    /**
     * 问题反馈选项
     *
     * @param request
     * @return
     */
    public Result<QuestionBackData> getQuestionBackOptions(QuestionBackOptionsRequest request) {
        log.info("getQuestionBackOptions begin request : {}", JSON.toJSONString(request));
        if (request.getLibraryId() == null && request.getTraceId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "libraryId和traceId不能都为空");
        }
        if (request.getUserId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "userId不能为空");
        }
        Integer libraryId = request.getLibraryId();
        Integer userId = request.getUserId();

        DeviceLibraryViewDO view = libraryService.queryUserLibraryViewByIdOrTraceId(request.getUserId(), request.getTraceId(), request.getLibraryId());
        if (view == null) {
            return Result.Error(NO_LIBRARY_ACCESS, "视频不存在");
        }
        // 分享用户跟随管理员可看到此入口并反馈问题
        /* sn-userId关系可能会变化
        UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(userId, view.getSerialNumber());
        if (userRoleDO == null) {
            return Result.Error(ResultCollection.NO_LIBRARY_ACCESS, "无权访问此视频");
        }
        */
        // app查询视频列表接口 /library/selectlibrary 连接了library_status表
//        libraryStatusService.selectLibraryStatus()
        final LibraryStatusTb libraryStatusTb = libraryStatusService.selectLibraryStatusByTraceIdAndUserId(view.getTraceId(), request.getUserId());
        if (libraryStatusTb == null) {
            return Result.Error(NO_LIBRARY_ACCESS, "无权访问此视频");
        }
        Set<String> modelSupportEventObjects = Optional.ofNullable(deviceManualService.getModelNoBySerialNumber(view.getSerialNumber()))
                .map(deviceModelEventService::queryRowDeviceModelEvent).orElse(null);
        Result<List<QuestionBackOption>> optionListResult = VideoTagQuestionEnums.getQuestionBackOptions(modelSupportEventObjects, view.getTags(), request.getLanguage());
        if (optionListResult.getResult() != Result.successFlag) {
            return (Result<QuestionBackData>) (Result) optionListResult;
        }
        List<QuestionBackOption> optionList = optionListResult.getData();
        QuestionBackData data = QuestionBackData.builder().libraryId(libraryId).userId(userId).options(optionList)
                .serialNumber(view.getSerialNumber()).traceId(view.getTraceId()).build();
        List<QuestionBackDO> questionBackDOList = StringUtils.isEmpty(request.getTraceId()) ? questionBackDAO.queryQuestionBackByLibraryIdAndUserId(request.getLibraryId(), request.getUserId(), request.getFeedbackType())
                : questionBackDAO.queryQuestionBackByTraceIdAndUserId(request.getTraceId(), request.getUserId(), request.getFeedbackType());
        if (questionBackDOList.size() > 0) {
            QuestionBackDO questionBackDO = questionBackDOList.get(0); // libraryId-userId有唯一索引
            // 回显上一次的选择和备注
            Set<Integer> checkedCodes = questionBackDOList.stream().findFirst().map(it -> it.getCodeSet()).orElseGet(Collections::emptySet);
            optionList.forEach(it -> it.setChecked(checkedCodes.contains(it.getCode())));
            data.setRemark(questionBackDO.getRemark());
            data.setIsFirst(false);
            data.setCheckedCodes(checkedCodes);
        } else {
            optionList.forEach(it -> it.setChecked(false));
            data.setRemark("");
            data.setIsFirst(true);
            data.setCheckedCodes(Collections.EMPTY_SET);
        }
        log.info("getQuestionBackOptions end request : {} , data : {}", JSON.toJSONString(request), JSON.toJSONString(data));

        List<QuestionBackOption> aiOptions = data.getOptions();

        List<QuestionBackOption> options = VideoQuestionEnums.getQuestionBackOptions(request.getLanguage(), data.getCheckedCodes());
        if (vipService.isVipDevice(userId, data.getSerialNumber())) {
            options.stream().filter(it -> it.getCode() == VideoQuestionEnums.AI_RECOGNITION.getCode())
                    .forEach(it -> it.setChildOptions(aiOptions));
        } else {
            options.removeIf(it -> it.getCode() == VideoQuestionEnums.AI_RECOGNITION.getCode());
        }

        return new Result<>(data);
    }
    /**
     * 问题反馈选项
     *
     * @param request
     * @return
     */
    public Result<QuestionBackData> getShortSummaryQuestionBackOptions(QuestionBackOptionsRequest request) {
        log.info("getShortSummaryQuestionBackOptions begin request : {}", JSON.toJSONString(request));

        if (request.getUserId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "userId不能为空");
        }
        Integer libraryId = request.getLibraryId();
        Integer userId = request.getUserId();


        List<QuestionBackOption> optionList= ShortSummaryQuestionEnums.getQuestionBackOptions(request.getLanguage(), new HashSet<>());

        QuestionBackData data = QuestionBackData.builder().libraryId(libraryId).userId(userId).options(optionList)
                .serialNumber(request.getSerialNumber()).traceId(request.getTraceId()).build();
        List<QuestionBackDO> questionBackDOList = StringUtils.isEmpty(request.getTraceId()) ? new ArrayList<>()
                : questionBackDAO.queryQuestionBackByTraceIdAndUserId(request.getTraceId(), request.getUserId(), request.getFeedbackType());
        if (questionBackDOList.size() > 0) {
            QuestionBackDO questionBackDO = questionBackDOList.get(0); // libraryId-userId有唯一索引
            // 回显上一次的选择和备注
            Set<Integer> checkedCodes = questionBackDOList.stream().findFirst().map(it -> it.getCodeSet()).orElseGet(Collections::emptySet);
            optionList.forEach(it -> it.setChecked(checkedCodes.contains(it.getCode())));
            data.setRemark(questionBackDO.getRemark());
            data.setIsFirst(false);
            data.setCheckedCodes(checkedCodes);
        } else {
            optionList.forEach(it -> it.setChecked(false));
            data.setRemark("");
            data.setIsFirst(true);
            data.setCheckedCodes(Collections.EMPTY_SET);
        }
        log.info("getShortSummaryQuestionBackOptions end request : {} , data : {}", JSON.toJSONString(request), JSON.toJSONString(data));
        return new Result<>(data);
    }


    public  Result commitQuestionBackShortSummary(QuestionBackCommitRequest request) {
        log.info("commitQuestionBackShortSummary begin request : {}", JSON.toJSONString(request));
        if (request.getLibraryId() == null && request.getTraceId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "libraryId和traceId不能都为空");
        }
        if (request.getUserId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "userId不能为空");
        }

        LinkedHashSet<Integer> inputCodes = Optional.ofNullable(request.getCodes()).orElseGet(LinkedHashSet::new);
        Result<String> remarkResult = trimAndCheckRemark(request.getRemark());
        if (remarkResult.getResult() != Result.successFlag) return remarkResult;

        List<QuestionBackOption> optionListResult = ShortSummaryQuestionEnums.getQuestionBackOptions(request.getLanguage(),request.getCodes());

        Set<Integer> optionCodes = optionListResult.stream().map(it -> it.getCode()).collect(Collectors.toSet());
        Arrays.stream(VideoQuestionEnums.values()).map(it -> it.getCode()).forEach(optionCodes::add);
        if (!optionCodes.containsAll(inputCodes)) {
            List<Integer> errCodes = FuncUtil.subtractToList(inputCodes, optionCodes);
            return Result.Failure("请求参数中的codes不合法!codes=" + errCodes + ",checkableCodes=" + optionCodes);
        }

        List<QuestionBackDO> questionBackDOList = questionBackDAO.queryQuestionBackByTraceIdAndUserId(request.getTraceId(), request.getUserId(), request.getFeedbackType());

        QuestionBackDO questionBackDO = QuestionBackDO.builder().serialNumber(request.getSerialNumber())
                .libraryId(request.getLibraryId() == null ? new Random().nextInt(1000000000): request.getLibraryId())
                .traceId(request.getTraceId())
                .optionCodeSet(optionCodes).codeSet(inputCodes)
                .codeSet(inputCodes)
                .userId(request.getUserId())
                .isBackup(false)
                .feedbackType("shortSummary")
                .remark(remarkResult.getData()).build();
        if(!questionBackDOList.isEmpty()) {
            questionBackDO.setId(questionBackDOList.get(0).getId());
            questionBackDAO.updateQuestionBackById(questionBackDO);
        }else {
            questionBackDAO.insertQuestionBack(questionBackDO);
        }
        log.info("commitQuestionBackShortSummary end request : {}", JSON.toJSONString(request));
        return Result.Success();
    }

    @SentinelResource("commitQuestionBack")
    public Result commitQuestionBack(QuestionBackCommitRequest request) {

        if( QuestionBackCommitRequest.FeedbackType.SHORT_SUMMARY.value().equals(request.getFeedbackType())){
            return commitQuestionBackShortSummary(request);
        }
        log.info("commitQuestionBack begin request : {}", JSON.toJSONString(request));
        if (request.getLibraryId() == null && request.getTraceId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "libraryId和traceId不能都为空");
        }
        if (request.getUserId() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "userId不能为空");
        }

        LinkedHashSet<Integer> inputCodes = Optional.ofNullable(request.getCodes()).orElseGet(LinkedHashSet::new);
        Result<String> remarkResult = trimAndCheckRemark(request.getRemark());
        if (remarkResult.getResult() != Result.successFlag) return remarkResult;

        DeviceLibraryViewDO view = libraryService.queryUserLibraryViewByIdOrTraceId(request.getUserId(), request.getTraceId(), request.getLibraryId());
        if (view == null) {
            return Result.Error(NO_LIBRARY_ACCESS, "视频不存在");
        }
        Set<String> modelSupportEventObjects = Optional.ofNullable(deviceManualService.getModelNoBySerialNumber(view.getSerialNumber()))
                .map(deviceModelEventService::queryRowDeviceModelEvent).orElse(null);
        Result<List<QuestionBackOption>> optionListResult = VideoTagQuestionEnums.getQuestionBackOptions(modelSupportEventObjects, view.getTags(), null);
        if (optionListResult.getResult() != Result.successFlag) {
            return optionListResult;
        }
        Set<Integer> optionCodes = optionListResult.getData().stream().map(it -> it.getCode()).collect(Collectors.toSet());
        Arrays.stream(VideoQuestionEnums.values()).map(it -> it.getCode()).forEach(optionCodes::add);
        if (!optionCodes.containsAll(inputCodes)) {
            List<Integer> errCodes = FuncUtil.subtractToList(inputCodes, optionCodes);
            return Result.Failure("请求参数中的codes不合法!codes=" + errCodes + ",checkableCodes=" + optionCodes);
        }
        // app查询视频列表接口 /library/selectlibrary 连接了library_status表
        final LibraryStatusTb libraryStatusTb = libraryStatusService.selectLibraryStatusByTraceIdAndUserId(view.getTraceId(), request.getUserId());
        if (libraryStatusTb == null) {
            return Result.Error(NO_LIBRARY_ACCESS, "无权访问此视频");
        }
        QuestionBackDO questionBackDO = QuestionBackDO.builder().serialNumber(view.getSerialNumber())
                .libraryId(libraryStatusTb.getLibraryId()).traceId(request.getTraceId()).userId(request.getUserId())
                .optionCodeSet(optionCodes).codeSet(inputCodes)
                .remark(remarkResult.getData()).build();
        List<QuestionBackDO> questionBackDOList = StringUtils.isEmpty(request.getTraceId()) ? questionBackDAO.queryQuestionBackByLibraryIdAndUserId(request.getLibraryId(), request.getUserId(), request.getFeedbackType())
                : questionBackDAO.queryQuestionBackByTraceIdAndUserId(request.getTraceId(), request.getUserId(), request.getFeedbackType());
        if (questionBackDOList.isEmpty() || !questionBackDOList.get(0).getIsBackup()) {
            Integer userId = libraryStatusService.queryAdminIdByUserIdAndTraceId(request.getUserId(), request.getTraceId());
            if (userId == null) {
                throw new BaseException(NO_LIBRARY_ACCESS, "视频不存在");
            }
            Result<JSONObject> backupResult = backupVideo(userId, view); // 视频备份
            questionBackDO.setIsBackup(Result.successFlag == backupResult.getResult());
        }
        if (!questionBackDOList.isEmpty()) {
            questionBackDO.setId(questionBackDOList.get(0).getId());
            questionBackDAO.updateQuestionBackById(questionBackDO);
        } else {
            questionBackDAO.insertQuestionBack(questionBackDO);
        }
        log.info("commitQuestionBack end request : {}", JSON.toJSONString(request));
        return Result.Success();
    }

    private Result<String> trimAndCheckRemark(String remark) {
        return TextUtil.trimAndCheckRemark(remark, 100, "问题反馈");
    }

    /*** 视频问题反馈时备份视频 */
    public Result<JSONObject> backupVideo(Integer userId, DeviceLibraryViewDO view) {
        try {
            JSONObject video = (JSONObject) JSON.toJSON(view);
            final Map<String, String> fileCopyMap = new LinkedHashMap<>();
            final BiConsumer<JSONObject, String> backupFile = (obj, key) -> {
                if (StringUtils.isBlank(obj.getString(key))) return;
                final String backupUrl = backupFileV2(obj.getString(key));
                if (backupUrl == null) return;
                fileCopyMap.put(obj.getString(key), backupUrl);
                obj.put("raw" + key.substring(0, 1).toUpperCase() + key.substring(1), obj.getString(key));
                obj.put(key, backupUrl);
            };
            backupFile.accept(video, "imageUrl");
            if (view.getType() == VideoType.DEVICE_SLICE.getCode()) {
                final List<JSONObject> sliceList = videoStoreService.querySliceByAdminUserIdAndTraceId(userId, view.getTraceId()).stream()
                        .map(it -> (JSONObject) JSON.toJSON(it)).collect(Collectors.toList());
                video.put("sliceList", sliceList);
                sliceList.forEach(slice -> backupFile.accept(slice, "videoUrl"));
            } else if (view.getType() == VideoType.NORMAL.getCode()) {
                backupFile.accept(video, "videoUrl");
            } else {
                log.info("backupVideo 无法备份该类型视频! traceId={},type={},videoTypes={}", view.getTraceId(), view.getType(), Arrays.stream(VideoType.values()).map(it -> it.getCode()).collect(Collectors.toList()));
                return Result.Error(1, "无法备份该类型视频");
            }
            video.fluentPut("backupTime", System.currentTimeMillis());
            final String desVideoJsonKey = buildBackupVideoJsonKey(view.getSerialNumber(), view.getTraceId(), "video.json");
            s3Service.putObject(s3.getBucket(), desVideoJsonKey, JSON.toJSONString(video, true));
            log.info("backupVideo end! traceId={},fileCopyMap={}", view.getTraceId(), JSON.toJSONString(fileCopyMap));
            return new Result(new JSONObject().fluentPut("copyNum", fileCopyMap.size()));
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "backupVideo error! traceId={}", view.getTraceId(), e);
            return Result.Failure(e.getMessage());
        }
    }

    private String backupFile(String srcUrl) {
        final URI uri = URI.create(srcUrl);
        if (srcUrl.contains(".s3.") || srcUrl.contains(".s3-accelerate.")) {
            final String srcBucket = uri.getHost().substring(0, uri.getHost().indexOf('.'));
            final String srcKey = uri.getPath().substring(1);
            final String desBucket = s3.getBucket();
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + srcKey;
            s3Service.copyObject(srcBucket, srcKey, desBucket, desKey);
            return s3Service.getUrl(desBucket, desKey);
        } else if ("storage.googleapis.com".equals(uri.getHost())) {
            final BlobInfo srcBlob = GoogleStorageService.parseGcsUrl(srcUrl);
            final String desBucket = gcsOptions.getBucket();
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + srcBlob.getName();
            googleStorageService.copyObject(srcBlob.getBucket(), srcBlob.getName(), desBucket, desKey);
            return GoogleStorageService.buildGscUrlForDownloadObject(desBucket, desKey);
        }
        final CosService.CosUrlParts cosUrlParts = CosService.parseCosUrl(srcUrl);
        if (cosUrlParts != null) {
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + cosUrlParts.getKey();
            cosService.copyObject(cosUrlParts.getBucket(), cosUrlParts.getKey(), cosConfig.getBucket(), desKey);
            return cosService.getObjectUrl(cosConfig.getBucket(), desKey).toString();
        }
        final OciService.OciUrlParts ociUrlParts = OciService.parseOciUrl(srcUrl);
        if (ociUrlParts != null) {
            final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + ociUrlParts.getKey();
            ociService.copyObject(new StoreBucket().setBucket(ociUrlParts.getBucket()).setRegion(ociUrlParts.getRegion()), ociUrlParts.getKey(), new StoreBucket().setBucket(ociConfig.getBucket()).setRegion(ociConfig.getParams().getDefaultRegion()), desKey);
            return ociService.getObjectUrl(new StoreBucket().setBucket(ociConfig.getBucket()).setRegion(ociConfig.getParams().getDefaultRegion()), desKey).toString();
        }
        log.info("backupVideo 不支持备份的视频文件url! srcUrl={}", srcUrl);
        return null;
    }

    private String backupFileV2(String srcUrl) {
        final String srcKey = StorageAllocateService.getObjectKeyFromUrl(srcUrl);
        if (StringUtils.isBlank(srcKey)) {
            log.warn("backupFile v2 getObjectKeyFromUrl fail! srcUrl={}", srcUrl);
            return null;
        }
        final String desBucket = s3.getBucket();
        final String desKey = VideoConstants.VIDEO_BACKUP_PREFIX + "/" + srcKey;
        final String signedUrl = s3Service.preSignUrl(srcUrl);
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            try (CloseableHttpResponse httpResp = httpClient.execute(new HttpGet(signedUrl))) {
                if (httpResp.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                    final Long contentLength = Arrays.stream(httpResp.getHeaders(HttpHeaders.CONTENT_LENGTH))
                            .findFirst().map(Header::getValue).map(Long::valueOf).orElse(null);
                    try (InputStream inputStream = httpResp.getEntity().getContent()) {
                        s3Service.putObject(desBucket, desKey, contentLength, inputStream);
                        return s3Service.getUrl(desBucket, desKey);
                    }
                }
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
        log.info("backupVideo v2 不支持备份的视频文件url! srcUrl={}", srcUrl);
        return null;
    }

    public static String buildBackupVideoJsonKey(String sn, String traceId, String postfix) {
        return VideoConstants.VIDEO_BACKUP_PREFIX + "/" + VideoConstants.UPLOAD_KEY_PREFIX
                .replace("${sn}", sn).replace("${traceId}", traceId) + postfix;
    }

    public List<JSONObject> queryQuestionBackVideos(Integer adminId) {
        if (adminId == null) {
            return Collections.emptyList();
        }
        List<QuestionBackDO> backList = questionBackDAO.queryQuestionBackByLibraryIdAndUserId(null, adminId,null);
        List<JSONObject> results = new LinkedList<>();
        for (QuestionBackDO back : backList) {
            DeviceLibraryViewDO library = getBackupLibraryView(back, adminId);
            JSONObject result = new JSONObject().fluentPut("video", library)
                    .fluentPut("questionBack", back);
            results.add(result);
        }
        return results;
    }

    public DeviceLibraryViewDO getBackupLibraryView(QuestionBackDO back, Integer adminId) {
        final DeviceLibraryViewDO library;
        if (!back.getIsBackup()) {
            library = libraryService.queryUserLibraryViewByIdOrTraceId(back.getUserId(), back.getTraceId(), back.getLibraryId());
            if (library == null) {
                log.info("queryQuestionBackVideos 问题反馈serialNumber为空且没有备份! backId={}", back.getId());
                return null;
            }
            Result<JSONObject> backupResult = backupVideo(adminId, library); // 视频备份
            if (Result.successFlag != backupResult.getResult()) {
                log.info("queryQuestionBackVideos 问题反馈serialNumber为空且备份失败! backId={}", back.getId());
                return null;
            }
            back.setSerialNumber(library.getSerialNumber()); // 补上字段
            back.setTraceId(library.getTraceId());
            back.setIsBackup(true); // 补上字段
            questionBackDAO.updateQuestionBackById(back);
        } else {
            if (StringUtils.isBlank(back.getTraceId())) {
                log.info("queryQuestionBackVideos 问题反馈traceId为空! backId={}", back.getId());
                return null;
            }
            List<String> sns = StringUtils.isNotBlank(back.getSerialNumber()) ? Arrays.asList(back.getSerialNumber())
                    : questionBackDAO.queryBoundDeviceSn(back.getUserId());
            // 有可能没有存serialNumber字段，就从所有绑定过的sn中找。
            library = sns.stream().map(sn -> getBackupLibraryViewFromS3(sn, back.getTraceId())).filter(it -> it != null).findFirst().orElse(null);
            if (library == null) {
                log.info("queryQuestionBackVideos 问题反馈serialNumber为空且备份文件没找到! backId={}", back.getId());
                return null;
            }
            if (StringUtils.isBlank(back.getSerialNumber())) {
                back.setSerialNumber(library.getSerialNumber()); // 补上字段
                questionBackDAO.updateQuestionBackById(back);
            }
        }
        library.setVideoUrl(videoService.preSignM3u8Url(library.getVideoUrl(), adminId));
        library.setImageUrl(s3Service.preSignUrl(library.getImageUrl()));
        return library;
    }

    private DeviceLibraryViewDO getBackupLibraryViewFromS3(String sn, String traceId) {
        final String desVideoJsonKey = buildBackupVideoJsonKey(sn, traceId, "video.json");
        try {
            S3Object videoJsonObj = s3Service.getObject(s3.getBucket(), desVideoJsonKey);
            String videoJson = IOUtils.toString(videoJsonObj.getObjectContent(), "UTF-8");
            return JSON.parseObject(videoJson, DeviceLibraryViewDO.class);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "download video.json error!", e);
            return null;
        }
    }
}
