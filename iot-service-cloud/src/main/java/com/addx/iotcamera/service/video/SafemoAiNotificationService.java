package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.response.MessageShieldResponse;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.message.MessagePushManageService;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.tracking.config.snowplow.SnowPlowManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.utils.LanguageBase;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.common.vo.SendNotifyResult;
import org.addx.iot.domain.config.service.ICopywriteLanguageService;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.notification.IAiNotificationContentGenerator;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.core.IExtensionManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@Slf4j
public class SafemoAiNotificationService {

    @Autowired
    private IExtensionManager extensionManager;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private VideoCacheService videoCacheService;

    @Autowired
    private SafePushService safePushService;

    @Autowired
    private ICopywriteLanguageService copywriteLanguageService;

    @Autowired
    private IAiNotificationContentGenerator aiNotificationContentGenerator;

    public static String MOTION_NOTIFY_CROPED_IMAGE_NAME = "croped_image.jpeg";
    @Autowired
    private S3Service s3Service;

    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Autowired
    @Lazy
    private VideoService videoService;
    @Autowired
    private RedisService redisService;

    @Value("${notification.notCheckVideoCreatedUserIds}")
    private String notCheckVideoCreatedUserIds;

    @Autowired
    private MessagePushManageService messagePushManageService;

    private boolean getNeedCheckVideoCreated(Integer userId){
        return userId== null || notCheckVideoCreatedUserIds == null || !(new HashSet<>(Arrays.asList(notCheckVideoCreatedUserIds.split(","))).contains(userId.toString()));
    }


    /**
     * 是否需要发送消息
     * @return
     */
    public Boolean shouldSend(){
        return safePushService.isEnable();
    }

    public void sendNotify(VideoCache video, AiTaskResult aiTaskResult){
        final String sn = video.getSerialNumber();
        final String traceId = video.getTraceId();
        if (!video.getFlag(VideoCache.Flag.LIBRARY_CREATED) && getNeedCheckVideoCreated(video.getAdminId())) {
            log.info("safemo: sendNotify video not created, drop message");
            video.getOldAiTaskResultList().add(aiTaskResult); // 视频未创建时，不发送推送，把未推送的event缓存起来
            return;
        }
        video.mergeAndClearOldAiTaskResultList(aiTaskResult); // 把未推送的event合并到当前aiTaskResult
        if (aiTaskResult != null && aiTaskResult.getEvents() != null && aiTaskResult.getEvents().isEmpty()) {
            // 如果当前分析结果中没有事件, 在motion alert 开启的 和当前视频第一次ai任务的条件下， 要么关闭Amartalert 要么开启enableOtherMotion
            if ((video.getEnableOther() || extensionManager.getInstalledExtensions(video.getAdminId()).stream().noneMatch(extension -> extension.getName().equals("smartAlert")))
                    && video.getPirNotifyPushedKeys().isEmpty() && !deviceService.getAllDeviceInfo(video.getSerialNumber()).getPushIgnored()) {
                SendNotifyResult sendResult = sendMotionNotify(video);// ai-station检测结果为空
                log.info("SafemoAiNotificationService sendMotionNotify end! sn={},traceId={},sendResult={}", sn, traceId, sendResult);
            } else {
                log.info("SafemoAiNotificationService not need send as other motion is not enabled or already event sent end! sn={},traceId={}", sn, traceId);
            }
        } else {
            SendNotifyResult sendResult = sendAIResultNotify(video, aiTaskResult);
            log.info("SafemoAiNotificationService sendAIResultNotify end! sn={},traceId={},sendResult={}", sn, traceId, sendResult);
        }
    }

    // 发送运动检测通知
    public SendNotifyResult sendMotionNotify(VideoCache video) {

        log.debug("sendMotionNotify video={}", JSON.toJSONString(video));

        final String sn = video.getSerialNumber();
        final String traceId = video.getTraceId();
        final Set<String> pushedKeys = new LinkedHashSet<>();
        try {
            if (!video.getIsNotify()) {
                log.debug("sendMotionNotify not enable push sn={},traceId={}", sn, traceId);
                return SendNotifyResult.NOT_ENABLE_PUSH;
            }
//            final String uncompressImageUrl = videoFileService.getAbsVideoFileUri(video.getImageUrl());
//            if (StringUtils.isBlank(uncompressImageUrl)) {
//                return SendNotifyResult.NOT_IMAGE;
//            }
            if (video.getSliceNum() == 0) {
                log.debug("sendMotionNotify not any slice sn={},traceId={}", sn, traceId);
                return SendNotifyResult.NOT_ANY_SLICE;
            }
            final AiEvent event = new AiEvent()
                    .setEventObject(AiObjectEnum.MOTION)
                    .setActivatedZones(Arrays.asList())
                    .setPossibleSubcategory(new ArrayList<>())
                    .setSummaryUrl(video.getImageUrl())
                    .setLabelId(null);
            final String pushedKey = event.toIdentity();
            if (!video.getPirNotifyPushedKeys().add(pushedKey)) { // 检查事件是否推送过
                log.debug("sendMotionNotify already pushed sn={},traceId={}", sn, traceId);
                return SendNotifyResult.EVENT_PUSHED;
            }
            pushedKeys.add(pushedKey);
            // 压缩图片比较耗时，放在所有判断条件的后面
//            event.setCropedImageBase64(videoAIService.compressImage(uncompressImageUrl));
            event.setCropedImageName(MOTION_NOTIFY_CROPED_IMAGE_NAME); // 固定

            final String reqBodyStr = buildSafePushRequestBody(video, Arrays.asList(event));
            final Result result = safePushService.send(video.getAdminId(), reqBodyStr);
            if (!Result.successFlag.equals(result.getResult())) {
                video.getPirNotifyPushedKeys().removeAll(pushedKeys); // 推送失败则还原推送标记
                log.debug("sendMotionNotify send fail sn={},traceId={}", sn, traceId);
                return SendNotifyResult.SEND_FAIL;
            }
            return SendNotifyResult.SEND_SUCCESS;
        } catch (Throwable e) {
            log.error("sendMotionNotify error! sn={},traceId={},pushedKeys={}", sn, traceId, JSON.toJSONString(pushedKeys), e);
            video.getPirNotifyPushedKeys().removeAll(pushedKeys); // 推送失败则还原推送标记
            return SendNotifyResult.SEND_ERROR;
        }
    }

    // 发送AI结果通知
    public SendNotifyResult sendAIResultNotify(VideoCache video, AiTaskResult aiTaskResult) {

        log.debug("sendAIResultNotify video={} aiTaskResult={}", JSON.toJSONString(video), JSON.toJSONString(aiTaskResult));

        final String sn = video.getSerialNumber();
        final String traceId = video.getTraceId();
        final Set<String> pushedKeys = new LinkedHashSet<>();
        final Map<String, SendNotifyResult> event2Result = new LinkedHashMap<>();
        final boolean isFirstPush = video.getPirNotifyPushedKeys().isEmpty();
        try {
            if (!video.getIsNotify()) {
                log.debug("sendAIResultNotify not enable sn={},traceId={}", sn, traceId);
                return SendNotifyResult.NOT_ENABLE_PUSH;
            }
            final List<AiEvent> events = new LinkedList<>();
            for (AiEvent event : aiTaskResult.getEvents()) {
                final String pushedKey = event.toIdentity();
                if (video.getIsCheckActivityZone()) {
                    if (!video.isMatchAnyActivityZone(Arrays.asList(event))) {
                        event2Result.put(pushedKey, SendNotifyResult.EVENT_NOT_IN_AZ);
                        log.error("sendAIResultNotify event not in a match activity zone");
                        continue; // 识别到的事件 不在activeZone中
                    }
                }
                if (video.getIsCheckNotifySwitch()) {
                    final String eventObjectName = event.getEventObject().getObjectName();
                    if (!video.getNotifyEventObjects().contains(eventObjectName)
                            && !(AiObjectEnum.MOTION.getObjectName().equals(eventObjectName) && video.getEnableOther())) {
                        event2Result.put(pushedKey, SendNotifyResult.EVENT_OBJECT_NOT_ENABLE);
                        log.debug(" sendAIResultNotify not in the eventType range of the notification 1 settings video {} pushedKey {}",
                                JSON.toJSONString(video), JSON.toJSONString(eventObjectName));
                        continue; // 识别到的事件 不在通知设置的eventObject范围中
                    }
                    final String actionName = event.getAiObjectAction().getObjectAction();
                    if ((!video.getNotifyEventTypes().contains(actionName) || video.getNotifyEventTypes().contains(String.format("%s_any",eventObjectName)))
                        && !(AiObjectEnum.MOTION.getObjectName().equals(eventObjectName) && video.getEnableOther())) {
                        event2Result.put(pushedKey, SendNotifyResult.EVENT_TYPE_NOT_ENABLE);
                        log.debug("sendAIResultNotify not in the eventType range of the notification 2 settings video {} actionName {}",
                                JSON.toJSONString(video), JSON.toJSONString(actionName));
                        continue; // 识别到的事件 不在通知设置的eventType范围中
                    }
                }
                if (!video.getPirNotifyPushedKeys().add(pushedKey)) { // 检查事件是否推送过
                    event2Result.put(pushedKey, SendNotifyResult.EVENT_PUSHED);
                    log.debug("sendAIResultNotify pirNotifyPushedKeys contains pushedKey video {} pushedKey {}", JSON.toJSONString(video), pushedKey);
                    continue;
                }
                pushedKeys.add(pushedKey);
                events.add(event);
            }
            if (!event2Result.isEmpty()) {
                log.info("sendAIResultNotify skip event! sn={},traceId={},event2Result={}", sn, traceId, JSON.toJSONString(event2Result));
            }
            if (events.isEmpty()) {
                // 如果AI事件被完全filter后，还是需要发motion通知的
                if ((video.getEnableOther() || extensionManager.getInstalledExtensions(video.getAdminId()).stream().noneMatch(extension -> extension.getName().equals("smartAlert")))
                        && isFirstPush && !deviceService.getAllDeviceInfo(video.getSerialNumber()).getPushIgnored()) {  // 没有开启其它事件通知  或者已经发过其它事件通知了，就不发普通motion通知
                    SendNotifyResult sendResult = sendMotionNotify(video);// ai-station检测结果为空
                    log.info("sendAIResultNotify sendMotionNotify end! sn={},traceId={},sendResult={}", sn, traceId, sendResult);
                }
                return SendNotifyResult.EVENTS_EMPTY;
            }

            final String reqBodyStr = buildSafePushRequestBody(video, events);
            log.debug("sendAIResultNotify start push message reqBodyStr{}", JSON.toJSONString(reqBodyStr));
            final Result result = safePushService.send(video.getAdminId(), reqBodyStr);
            if (!Result.successFlag.equals(result.getResult())) {
                video.getPirNotifyPushedKeys().removeAll(pushedKeys); // 推送失败则还原推送标记
                log.error("sendAIResultNotify send failed");
                return SendNotifyResult.SEND_FAIL;
            }
            return SendNotifyResult.SEND_SUCCESS;
        } catch (Throwable e) {
            log.error("sendAIResultNotify error! sn={},traceId={},pushedKeys={}", sn, traceId, JSON.toJSONString(pushedKeys), e);
            video.getPirNotifyPushedKeys().removeAll(pushedKeys); // 推送失败则还原推送标记
            return SendNotifyResult.SEND_ERROR;
        }
    }

    public SendNotifyResult sendShortSummaryNotification(VideoCache video, String summaryUrl, String cropedImageName){
        log.debug("sendShortSummaryNotificaion video={}", JSON.toJSONString(video));

        if (!video.getIsNotify()) {
            log.debug("sendShortSummaryNotification not enable push sn={},traceId={}", video.getSerialNumber(), video.getTraceId());
            return SendNotifyResult.NOT_ENABLE_PUSH;
        }

        if (!video.getFlag(VideoCache.Flag.LIBRARY_CREATED) && getNeedCheckVideoCreated(video.getAdminId())) {
            log.info("sendShortSummary Notification video not created, drop message");
            return SendNotifyResult.NOT_ANY_SLICE;
        }

        UserRoleDO deviceAdminUserRole = userRoleService.getDeviceAdminUserRole(video.getSerialNumber());
        boolean analyticsSwitch = Objects.nonNull(deviceAdminUserRole) && deviceInfoService.getEventTrackingSwitch(video.getSerialNumber());
        try{
            final List<JSONObject> notifyEvents = new LinkedList<>(); // 需要通知的事件
            final List<JSONObject> notifyUsers = new LinkedList<>(); // 需要通知的事件和用户
            // 翻译标题

            for (VideoCommonCache.UserSimple user : video.getUsers()) {
                if (user.getId() == null) {
                    continue; // 不是云端账号
                }

                MessageShieldResponse messageShieldResponse = messagePushManageService.getMessagePushShield(user.getId());
                if (messageShieldResponse.shield) {
                    log.info("User {} has set do not disturb, skip notification", user.getId());
                    continue;
                }

                String title = String.format("Motion detected - %s", deviceService.queryDeviceNameBySerialNumber(video.getSerialNumber()));
                notifyUsers.add(new JSONObject(new LinkedHashMap<>())
                        .fluentPut("title", title)
                        .fluentPut("content", video.getSummaryDescription())
                        .fluentPut("userId", user.getId())
                        .fluentPut("language", user.getLanguage())
                        .fluentPut("cloudUserId", user.getId())
                        .fluentPut("cloudUserNode", activeProfile)
                );
            }

            JSONObject firstEvent = new JSONObject(new LinkedHashMap<>())
                    .fluentPut("object", "motion")
                    .fluentPut("event", "motion_exist")
                    .fluentPut("safetyLevel", video.getSafeLevel())
                    .fluentPut("imageUrl", s3Service.preSignUrl(summaryUrl))
                    .fluentPut("cropedImageName", s3Service.preSignUrl(cropedImageName))
                    .fluentPut("notifyUsers", notifyUsers);

            if (analyticsSwitch) {
                firstEvent.fluentPut("messageId", PhosUtils.getUUID());
            }
            notifyEvents.add(firstEvent);
            final JSONObject reqBody = new JSONObject(new LinkedHashMap<>())
                    .fluentPut("sn", video.getSerialNumber())
                    .fluentPut("traceId", video.getTraceId())
                    .fluentPut("adminId", video.getAdminId())
                    .fluentPut("type", MsgType.VIDEO_MSG)
                    .fluentPut("coverImageUrl", s3Service.preSignUrl(video.getImageUrl()))
                    .fluentPut("notifyEvents", notifyEvents)
                    .fluentPut("deviceName", video.getDeviceName())
                    .fluentPut("deviceModelNo", video.getDeviceModelNo())
                    .fluentPut("videoEvent", video.getVideoEvent())
                    .fluentPut("messageFrom", "cloud")  //让safepush 区分消息是从云端还是 local BX发送的
                    .fluentPut("videoUrl", videoService.preSignM3u8Url(video.getVideoUrl(), video.getAdminId()));

            final String reqBodyStr = JSON.toJSONString(reqBody, SerializerFeature.DisableCircularReferenceDetect);
            log.debug("sendAIResultNotify start push message reqBodyStr{}", JSON.toJSONString(reqBodyStr));
            String key = String.format("shortSummaryNotification:{%s}", video.getSerialNumber());
            if(redisService.containsKey(key) && "normal".equals(video.getSafeLevel())){
                return SendNotifyResult.NORMAL_FREQUENCY_NOT_SEND;
            }
            final Result result = safePushService.send(video.getAdminId(), reqBodyStr);
            if (!Result.successFlag.equals(result.getResult())) {
                log.error("sendAIResultNotify send failed");
                return SendNotifyResult.SEND_FAIL;
            }
            if("normal".equals(video.getSafeLevel())) {
                redisService.set(key, "1", 15);  // 十五秒内不重复发送abnormal通知
            }
            return SendNotifyResult.SEND_SUCCESS;
        }catch (Exception e){
            log.error("sendShortSummaryNotificaion error! video={}", JSON.toJSONString(video), e);
            return SendNotifyResult.SEND_ERROR;
        }
    }

    // 构建app推送请求体
    public String buildSafePushRequestBody(VideoCache video, List<AiEvent> events) {
        log.info("buildSafePushRequestBody start video:{}. event:{}", video, events);
//        final DeviceLibraryViewDO libraryView = libraryService.selectLibraryViewByTraceId(null, input.getTraceId());
        final List<JSONObject> notifyEvents = new LinkedList<>(); // 需要通知的事件
        int imageBase64Length = 0;
        int imageUrlLength = 0;
        UserRoleDO deviceAdminUserRole = userRoleService.getDeviceAdminUserRole(video.getSerialNumber());
        boolean analyticsSwitch = Objects.nonNull(deviceAdminUserRole) && deviceInfoService.getEventTrackingSwitch(video.getSerialNumber());
        for (AiEvent event : events) {
            final List<JSONObject> notifyUsers = new LinkedList<>(); // 需要通知的事件和用户
            for (VideoCommonCache.UserSimple user : video.getUsers()) {
                if (user.getId() == null) {
                    continue; // 不是云端账号
                }

                MessageShieldResponse messageShieldResponse = messagePushManageService.getMessagePushShield(user.getId());
                if (messageShieldResponse.shield) {
                    log.info("User {} has set do not disturb, skip notification", user.getId());
                    continue;
                }

                final List<String> zoneNames = videoCacheService.getActivityZoneNames(video, Lists.newArrayList(event));
                // at zone1
                String zoneDesc = zoneNames.size() == 0 ? "" : copywriteLanguageService.queryCopywriteWithEnglishIfNotExist(user.getLanguage(), CopyWriteConstans.atZone) +
                        LanguageBase.MotionBodyForActivityZone(zoneNames,
                                copywriteLanguageService.queryCopywriteWithEnglishIfNotExist(user.getLanguage(), CopyWriteConstans.motionTitleForActivityZone));
                log.info("buildSafePushRequestBody, zoneNames,{},{}", zoneNames, zoneDesc);

                String eventIdentity = event.toIdentity();

                Map<String, String> vars = new HashMap<>();
                // 翻译标题
                String title = aiNotificationContentGenerator.generateTitle(event,video.getSerialNumber(), user.getLanguage());
                String content= aiNotificationContentGenerator.generateContent(event, video.getSerialNumber(), user.getLanguage());

                notifyUsers.add(new JSONObject(new LinkedHashMap<>())
                        .fluentPut("title", title)
                        .fluentPut("content", content)
                        .fluentPut("userId", user.getId())
                        .fluentPut("language", user.getLanguage())
                        .fluentPut("cloudUserId", user.getId())
                        .fluentPut("cloudUserNode", activeProfile)
                );
            }
            if (notifyUsers.isEmpty()) continue;
            final String preSignSummaryUrl = s3Service.preSignUrl(event.getSummaryUrl());


            // 第一次不含imageUrl 的纯文本消息， 但包含ios 3D touch 的专用的图片url
            if (video.isFirstSendNotification()) {
                JSONObject firstEvent = new JSONObject(new LinkedHashMap<>())
                        .fluentPut("object", event.getEventObject().getObjectName())
                        .fluentPut("event", event.getEventType().getEventTypeName())
                        //.fluentPut("imageUrl", preSignSummaryUrl)
                        .fluentPut("lazyImageUrl", preSignSummaryUrl)
                        .fluentPut("cropedImageName", event.getCropedImageName())
                        .fluentPut("notifyUsers", notifyUsers);
                if (analyticsSwitch) {
                    firstEvent.fluentPut("messageId", PhosUtils.getUUID());
                }
                notifyEvents.add(firstEvent);

            }
            JSONObject secondEvent = new JSONObject(new LinkedHashMap<>())
                    .fluentPut("object", event.getEventObject().getObjectName())
                    .fluentPut("event", event.getEventType().getEventTypeName())
                    .fluentPut("imageUrl", preSignSummaryUrl)
                    .fluentPut("lazyImageUrl", preSignSummaryUrl)
                    .fluentPut("cropedImageName", event.getCropedImageName())
                    .fluentPut("notifyUsers", notifyUsers);

            if (analyticsSwitch) {
                secondEvent.fluentPut("messageId", PhosUtils.getUUID());
            }
            // 第二次含imageUrl的消息
            notifyEvents.add(secondEvent);
            imageBase64Length += Optional.ofNullable(event.getCropedImageBase64()).map(it -> it.length()).orElse(0);
            imageUrlLength += preSignSummaryUrl.length();
        }

        if (analyticsSwitch) {
            analyticsPush(notifyEvents, video);
        }
        final JSONObject reqBody = new JSONObject(new LinkedHashMap<>())
                .fluentPut("sn", video.getSerialNumber())
//                .fluentPut("bxSn", video.getBxSn())   // 云端不需要
                .fluentPut("traceId", video.getTraceId())
                .fluentPut("adminId", video.getAdminId())
                .fluentPut("type", MsgType.VIDEO_MSG)
                .fluentPut("coverImageUrl", s3Service.preSignUrl(video.getImageUrl()))
                .fluentPut("notifyEvents", notifyEvents)
                .fluentPut("deviceName", video.getDeviceName())
                .fluentPut("deviceModelNo", video.getDeviceModelNo())
                .fluentPut("videoEvent", video.getVideoEvent())
//                .fluentPut("endPoint", snowPlowManager.getUrl())
//                .fluentPut("namespace", snowPlowManager.getNamespace())
                .fluentPut("messageFrom", "cloud")  //让safepush 区分消息是从云端还是 local BX发送的
                .fluentPut("videoUrl", videoService.preSignM3u8Url(video.getVideoUrl(), video.getAdminId()));


        final String reqBodyStr = JSON.toJSONString(reqBody, SerializerFeature.DisableCircularReferenceDetect);
        final String reqBodyNotContainsBase64 = JSON.toJSONString(reqBody, (PropertyFilter) (object, name, value) -> !"cropedImageBase64".equals(name));
        final BigDecimal imageBase64Ratio = new BigDecimal(imageBase64Length).divide(new BigDecimal(reqBodyStr.length()), 4, RoundingMode.HALF_UP);
        log.info("handleIotLocalAIResult build reqBody end! reqBodyLength={},imageUrlLength={},imageBase64Length={},imageBase64Ratio={},reqBodyNotContainsBase64={}",
                reqBodyStr.length(), imageUrlLength, imageBase64Length, imageBase64Ratio, reqBodyNotContainsBase64);

        video.setFirstSendNotification(false);
        return reqBodyStr;
    }
    public void analyticsPush(List<JSONObject> notifyEvents, VideoCache video) {
        try {
            String jsonStr = JsonUtil.toJson(notifyEvents);
            List<JSONObject> notifyEventList = JsonUtil.fromJson(jsonStr, List.class);
            if (CollectionUtils.isNotEmpty(notifyEventList)) {
                for (JSONObject eventJSONObject : notifyEventList) {
                    try {
                        Map<String, java.lang.Object> map = new HashMap<>();
                        map.put("message_id", eventJSONObject.getString("messageId"));
                        map.put("cx_sn", video.getSerialNumber());
                        map.put("trace_id", video.getTraceId());
                        map.put("tenant_id", AppConstants.TENANTID_SAFEMO);
                        SnowPlowManager.getInstance().analyticSelfEvent("cloud_send_notification_message", map);
                    } catch (Exception e) {
                        log.error("eventAnalytics error, uuid:{}, serialNumber:{}", eventJSONObject.getString("messageId"), video.getSerialNumber(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("analytics error, notifyEvents:{}", notifyEvents, e);
        }
    }
}
