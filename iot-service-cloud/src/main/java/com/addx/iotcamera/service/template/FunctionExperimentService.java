package com.addx.iotcamera.service.template;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.domain.library.TierFreeNotifyPeriodDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.config.template.GrayscaleTemplateConfig;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.creative.CreativeService;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.PayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;
import java.util.Set;

import static com.addx.iotcamera.constants.PayConstants.TEMPLATE_EMAIL_SEND_NUM;
import static com.addx.iotcamera.constants.PayConstants.TIER_EXPIRE_START_USER_EXPIRE;

@Service
@Slf4j
public abstract class FunctionExperimentService {

    @Resource
    GrayscaleTemplateConfig grayscaleTemplateConfig;

    @Resource
    private RedisService redisService;


    public abstract String queryFunctionName();

    /**
     * 判断是否符合条件
     * @return
     */
    public abstract TierFreeUserExpireDO eligible(Integer userId, AppRequestBase requestBase);

    /**
     * 是否符合保护期
     * @return
     */
    public abstract TierFreeNotifyPeriodDO inProtectionPeriod(Integer userId);


    public void functionOperation(Integer userId, AppRequestBase requestBase){
        //是否符合通知条件
        TierFreeUserExpireDO tierFreeUserExpireDO = this.eligible(userId,requestBase);
        if(!tierFreeUserExpireDO.isNotify()){
            log.debug("TwoYearFree180DayNotifyService eligible 不符合");
            return;
        }

        // 是否在发送间隔期间内
        TierFreeNotifyPeriodDO tierFreeNotifyPeriodDO = this.inProtectionPeriod(userId);
        if(tierFreeNotifyPeriodDO.isInPeriod()){
            return;
        }

        //灰度规则验证
        if(!this.verifyGrayscale(userId)){
            return;
        }

        this.updateProtectionPeriod(userId);
    }
    /**
     * 更新保护期
     * @param
     * @return
     */
    public abstract void updateProtectionPeriod(Integer userId);


    public TierFreeUserExpireDO verify(Integer userId, AppRequestBase requestBase){
        //灰度规则验证
        if(!this.verifyGrayscale(userId)){
            log.debug("banner not verifyGrayscale, userid is: {}", userId);
            return TierFreeUserExpireDO.builder()
                    .notify(false)
                    .build();
        }

        TierFreeNotifyPeriodDO tierFreeNotifyPeriodDO = this.inProtectionPeriod(userId);
        //操作保护期验证
        if(tierFreeNotifyPeriodDO.isInPeriod()){
            log.debug("banner not isInPeriod, userid is: {}", userId);
            return TierFreeUserExpireDO.builder()
                    .notify(false)
                    .build();
        }

        TierFreeUserExpireDO tierFreeUserExpireDO = this.eligible(userId,requestBase);
        tierFreeUserExpireDO.setNotifyCount(tierFreeNotifyPeriodDO.getNotifyCount());
        tierFreeUserExpireDO.setSlotName(CreativeService.GUIDE_AFTER_CLOUD_BANNER);
        return tierFreeUserExpireDO;
    }


    /**
     * 白名单验证
     * @param userId
     * @return
     */
    public boolean verifyWhiteConfig(Integer userId){
        Set<Integer> whiteList = grayscaleTemplateConfig.queryWhiteList(this.queryFunctionName());
        if(!CollectionUtils.isEmpty(whiteList) && whiteList.contains(userId)){
            return true;
        }
        return false;
    }

    /**
     * 灰度规则校验
     * @param userId
     * @return
     */
    public boolean verifyGrayscale(Integer userId){
        if(this.verifyWhiteConfig(userId)){
            return true;
        }


        GrayscaleTemplateConfig.GrayscaleTemplate template = grayscaleTemplateConfig.queryGrayscaleTemplate(this.queryFunctionName());
        if(template == null){
            return true;
        }

        if(template.getGrayscaleTotal() == null || template.getGrayscaleScale() == null){
            return true;
        }

        return template.getGrayscaleScale() >= (userId % template.getGrayscaleTotal());
    }


    /**
     * 已全量灰度，不再使用
     * 重复reminder功能灰度
     * @param userId
     * @return
     */
    public boolean verifyRepeatedMinder(Integer userId){
        if(this.verifyWhiteConfig(userId)){
            return true;
        }

        GrayscaleTemplateConfig.GrayscaleTemplate template = grayscaleTemplateConfig.queryGrayscaleRepeatedReminder(this.queryFunctionName());
        if(template == null){
            return true;
        }

        if(template.getGrayscaleTotal() == null || template.getGrayscaleScale() == null){
            return true;
        }

        return template.getGrayscaleScale() >= (userId % template.getGrayscaleTotal());
    }

    /**
     * 已发送的邮件数量
     * @return
     */
    public int querySendEmailNum(){
        String key = TEMPLATE_EMAIL_SEND_NUM
                .replace("{template}",this.queryFunctionName())
                .replace("{date}", DateUtils.dateToString(new Date(),DateUtils.YYYY_MM_DD));
        String value = redisService.get(key);
        return StringUtils.hasLength(value) ? Integer.parseInt(value) : 0;
    }

    public void increSendEmailNum(){
        String key = TEMPLATE_EMAIL_SEND_NUM
                .replace("{template}",this.queryFunctionName())
                .replace("{date}",DateUtils.dateToString(new Date(),DateUtils.YYYY_MM_DD));
        redisService.increNum(key);
        redisService.setExpired(key,TIER_EXPIRE_START_USER_EXPIRE);
    }
}
