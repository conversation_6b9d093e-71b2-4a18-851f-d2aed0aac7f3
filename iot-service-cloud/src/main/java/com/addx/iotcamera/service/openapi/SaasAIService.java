package com.addx.iotcamera.service.openapi;

import com.addx.iotcamera.bean.db.birdtab.BirdTabDO;
import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.RecognitionObjectCategory;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.bean.openapi.SaasAITaskOM;
import com.addx.iotcamera.config.opanapi.SaasAiTaskConfig;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.enums.VipCountType;
import com.addx.iotcamera.helper.FirstKeyHelper;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.app.BirdTabService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.Executor;

import static com.addx.iotcamera.bean.openapi.SaasAITaskIM.*;
import static com.addx.iotcamera.bean.openapi.SaasAITaskOM.*;
import static com.addx.iotcamera.constants.VideoConstants.S3_VIDEO_SLICE_INFO_TIMEOUT;

/**
 * 对接新saas服务
 * saas与算法交互协议：http://192.168.31.7:8090/pages/viewpage.action?pageId=43131140
 */
@Slf4j
@Component
public class SaasAIService {

    @Value("${spring.kafka.topics.saas-ai-task}")
    private String saasAiTaskTopic;
    @Value("${spring.kafka.topics.saas-ai-task-iot-result}")
    private String saasAiTaskIotResultTopic;
    @Autowired
    private SaasAiTaskConfig aiTaskConfig;

    @Value("${spring.kafka.topics.ai-video-segment}")
    private String videoSegmentTopic;

    @Value("${bird-tab.image-store-bucket}")
    private String imageStoreBucket;

    @Resource
    private MqSender mqSender;
    @Autowired
    private UserService userService;
    @Autowired
    private VipService vipService;
    @Autowired
    RedisService redisService;
    @Autowired
    @Qualifier("saasAiResultConsume")
    private Executor executor;
    @Autowired
    @Lazy
    private NotificationService notificationService;
    @Autowired
    private BirdLoversService birdLoversService;
    @Autowired
    @Lazy
    private DeviceSupportService deviceSupportService;
    @Autowired
    private BirdTabService birdTabService;


    private FirstKeyHelper firstKeyHelper;

    // 算法端每次对象识别都上传图片会有较大性能开销。所以针对自有APP做优化：第一次发现的事件对象，结果中才都带有图片。
    private boolean deduceAIResultImageNum = true;
    @Autowired
    private S3Service s3Service;

    @Value("${ai-cloud.enable}")
    private Boolean enableAiCloudImageFeature;

    @Value("${ai-cloud.blackUserIds}")
    private String blackUserIds;
    @Autowired
    private UserRoleService userRoleService;

    @PostConstruct
    public void init() {
        firstKeyHelper = FirstKeyHelper.create(executor, redisService, Duration.ofSeconds(S3_VIDEO_SLICE_INFO_TIMEOUT));
    }

    // traceId是否在配置的比例中
    public static boolean traceIdInPercent(String traceId, Integer minPercent) {
        return (Math.abs(traceId.hashCode()) % 100) < minPercent;
    }

    private boolean userInBlackList(String userId) {
        return (new HashSet<>(Arrays.asList(blackUserIds.split(",")))).contains(userId);
    }

    /**
     * 用于控制新老任务发送的比例，以及使用新老任务结果的比例
     *
     * @param aiTask
     * @return
     */
    public String[] handleOldAndNewTaskPercent(AITask aiTask) {
        final String traceId = aiTask.getTraceId();
        final String taskId = aiTask.getTaskId();
        final String deviceSn = aiTask.getSerialNumber();
        String[] idArr = {traceId, taskId, deviceSn};
        if (aiTaskConfig.getWhiteUserIds().contains(aiTask.getUserId())) {
            return idArr; // 白名单用户，只发新任务，使用新结果
        }
        if (traceIdInPercent(traceId, aiTaskConfig.getSendSaasTaskPercent())) {
            if (aiTaskConfig.getUseSaasResult()) {
                /** 在老任务比例范围内，老任务发送 **/
                if (traceIdInPercent(traceId, aiTaskConfig.getSendOldTaskPercent())) {
                    // 带前缀的的traceId的返回结果查不到，会被抛弃掉
                    aiTask.setTraceId("old:" + traceId);
                    aiTask.setTaskId("old:" + taskId);
                    aiTask.setSerialNumber("old:" + deviceSn);
                    mqSender.send(videoSegmentTopic, aiTask.getTraceId(), aiTask);
                }
                return idArr;
            } else {
                /** 不使用新任务结果，老任务必须发送 **/
                mqSender.send(videoSegmentTopic, aiTask.getTraceId(), aiTask);
                // 带前缀的的traceId的返回结果查不到，会被抛弃掉
                return new String[]{"saas:" + traceId, "saas:" + taskId, "saas:" + deviceSn};
            }
        } else {
            /** 不发新任务，老任务必须发送 **/
            mqSender.send(videoSegmentTopic, aiTask.getTraceId(), aiTask);
            return null;
        }
    }

    @Deprecated
    public SaasAITaskIM getSaasAiTaskConfig(AITask aiTask, boolean isLast) {
        final String[] idArr = handleOldAndNewTaskPercent(aiTask);
        if (idArr == null) return null;
        final String traceId = idArr[0], taskId = idArr[1], deviceSn = idArr[2];

        User user = userService.queryUserById(Integer.valueOf(aiTask.getUserId()));
        List<RecognitionObject> recognitionObjects = new LinkedList<>();
        IdBox idBox = new IdBox();
        for (String event : FuncUtil.iterable(aiTask.getEvents())) {
            RecognitionObjectCategory category = RecognitionObjectCategory.eventObjectNameOf(event);
            recognitionObjects.add(new RecognitionObject().setCategory(category)
                    .setFunctions(category.getPaasOwnedBizFunctions()));
            idBox.addVisualizeRecognition(category);
        }
        if (aiTask.getIdentificationBox() != null) {
            for (AITask.Color color : FuncUtil.iterable(aiTask.getIdentificationBox().getColors())) {
                idBox.addIdBoxColor(new IdBoxColor().setColor(color.getColor())
                        .setName(RecognitionObjectCategory.eventObjectNameOf(color.getName())));
            }
        }
        SaasAITaskIM input = new SaasAITaskIM()
                .setInputType(InputType.SLICE)
                .setOutputType(OutputType.KAFKA)
                .setTraceId(traceId)
                .setCountryNo(user.getCountryNo())
                .setDeviceSn(deviceSn)
                .setOwnerId(aiTask.getUserId() + "")
                .setVideoUrl(aiTask.getVideoUrl())
                .setImages(Collections.EMPTY_LIST)
                .setOrder(aiTask.getOrder())
                .setIsLast(isLast ? 1 : 0)
                .setTimeout(aiTaskConfig.getTimeout())
                .setOutStorage(aiTaskConfig.getOutStorage())
                .setTaskId(taskId)
                .setTaskSendTime(aiTask.getTaskSendTime())
                .setActivityZoneList(aiTask.getActivityZoneList())
                .setRecognitionObjects(recognitionObjects)
                .setTenantId(user.getTenantId())
                .setIdBox(idBox)
                .setModelNo(aiTask.getModelNo())
                .setOutputTopic(saasAiTaskIotResultTopic); // 不走webhook转发，iotService直接接收
        input.setSliceTotalNum(Optional.ofNullable(aiTask.getSliceTotalNum()).orElse(-1)); // 避免设备端先收到汇总消息，抛弃调后续切片消息
        VipCountType vipCountType = vipService.queryVipCountType(user.getTenantId(), Integer.valueOf(aiTask.getUserId()), aiTask.getSerialNumber());
        input.getContext().setVipCountType(vipCountType != null ? vipCountType.getCode() : 0);
        input.setOutParams(buildOutParams(input));
        return input;
    }

    public void sendSaasAiTask(AITask aiTask, boolean isLast) {
        log.info("sendSaasAiTask begin aiTask={}", JSON.toJSONString(aiTask));
        CloudDeviceSupport cloudDeviceSupport = deviceSupportService.queryDeviceSupportBySn(aiTask.getSerialNumber());
        if (cloudDeviceSupport != null && Boolean.TRUE.equals(cloudDeviceSupport.getSupportSendAiImageDirect())
                && enableAiCloudImageFeature && !userInBlackList(aiTask.getUserId())) {
            log.info("sendSaasAiTask deviceSupport supportSendAiImageDirect is true, need not send ai kafka task!");
            return;
        }
        if(AppConstants.TENANTID_SAFEMO.equals(userService.queryUserById(Integer.valueOf(aiTask.getUserId())).getTenantId())){
            log.info("sendSaasAiTask: safemo tenantId need not send ai kafka task!");
            return;
        }
        SaasAITaskIM input = getSaasAiTaskConfig(aiTask, isLast);
        if (input == null) {
            log.info("sendSaasAiTask input is null");
            return;
        }
        log.info("sendSaasAiTask input={}", JSON.toJSONString(input));
        mqSender.send(saasAiTaskTopic, input.getTraceId(), input);
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getSendSaasAiTaskCountOptional()
                .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName()).inc()));
    }

    public static String buildOutParams(SaasAITaskIM input) {
        JSONObject params = new JSONObject()
                .fluentPut("userSetAz", CollectionUtils.isNotEmpty(input.getActivityZoneList())) // 用户是否设置az
                .fluentPut("outEncodeType", 0); // URL(0), BINARY(1), BASE64(2);
        return params.toJSONString();
    }

    @KafkaListener(topics = "${spring.kafka.topics.saas-ai-task-iot-result}", groupId = "saas-ai-task-iot-result")
    public void handleSaasAiTaskResult(ConsumerRecord<String, String> record) {
        MDC.put(MDCKeys.REQUEST_ID, IDUtil.uuid());
        String traceId = record.key();
        String value = record.value();
        log.info("handleSaasAiTaskResult kafka : key={},value={}", traceId, value);
        executor.execute(() -> {
            SaasAITaskOM input = JSON.parseObject(record.value(), SaasAITaskOM.class);
            if (input.getType() == Type.AI_RECOGNITION_RESULT) {
                handleAIRecognitionResult(input);
            } else if (input.getType() == Type.AI_EVENT_RESULT) {
                handleAIEventResult(input);
            } else {
                com.addx.iotcamera.util.LogUtil.error(log, "handleSaasAiTaskResult 收到未知type sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
            }
        });
    }

    // 老流程， 不在需要
    @Deprecated
    private void handleAIRecognitionResult(SaasAITaskOM input) {
        String deviceIsWowzaKey = DeviceInfoConstants.DEVICE_IS_WOWZA.replace("{serialNumber}", input.getDeviceSn());
        if ("1".equals(redisService.get(deviceIsWowzaKey))) {
            log.info("handleAIRecognitionResult wowza上传视频此时还没存进数据库，忽略对象识别结果 sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
            return;
        }
        // 单个ts可能识别到多个对象
        List<AiTaskResult> aiTaskResults = new LinkedList<>();
        for (ImageOM imageOM : input.getImages()) {
            if (StringUtils.isBlank(imageOM.getImageUrl())) {
                if (deduceAIResultImageNum) {
                    log.debug("handleAIRecognitionResult 未发现新对象，不带图片 sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                } else {
                    com.addx.iotcamera.util.LogUtil.error(log, "handleAIRecognitionResult 图片为空 sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                }
                continue;
            }
            for (RecognisedObject recogObj : imageOM.getObjects()) {
                if (recogObj.getCategory() == null) {
                    com.addx.iotcamera.util.LogUtil.error(log, "handleAIRecognitionResult 收到未知objectCategory sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                    continue;
                }
                if (recogObj.getCategory() == RecognitionObjectCategory.BIRD) {
                    continue; // 鸟类等到事件汇总时再推送,否则推送的鸟类品种可能跟最高置信度品种不一致
                }
                if (isPushed(input.getTraceId(), recogObj.getCategory(), AiObjectActionEnum.EXIST)) continue;
                // id.enable 是否开启reId功能
                String labelId = !recogObj.getId().getEnable() ? "" :
                        Optional.ofNullable(recogObj.getId().getLabelId()).orElse("");
                AiEvent event = new AiEvent()
                        .setEventObject(recogObj.getCategory().getEventObject())
                        .setEventType(AiObjectActionEnum.EXIST)
                        .setActivatedZones(recogObj.getActivityZoneIds())
                        .setLabelId(labelId);
                if (CollectionUtils.isNotEmpty(recogObj.getSubCategories())) {
                    PossibleSubcategory possibleSubcategory = new PossibleSubcategory()
                            .setName(recogObj.getSubCategories().get(0).getName()).setConfidence(recogObj.getConfidence());
                    event.setPossibleSubcategory(Arrays.asList(possibleSubcategory));
                }
                AiTaskResult aiTaskResult = new AiTaskResult()
                        .setTraceId(input.getTraceId())
                        .setSerialNumber(input.getDeviceSn())
                        .setImageUrl(imageOM.getImageUrl())
                        .setEvents(Arrays.asList(event))
                        .setOrder(input.getOrder());
                aiTaskResults.add(aiTaskResult);
            }
        }
        log.info("handleAIRecognitionResult sn={},traceId={},aiTaskResults={}", input.getDeviceSn(), input.getTraceId(), JSON.toJSONString(aiTaskResults));
        for (AiTaskResult aiTaskResult : aiTaskResults) {
            try {
                notificationService.aiTaskNotify(aiTaskResult, true);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleSaasAiTaskResult aiTaskNotify发生未知异常,sn={},traceId={}", input.getDeviceSn(), input.getTraceId(), e);
            }
        }
    }

    // 老流程， 不在需要
    @Deprecated
    private void handleAIEventResult(SaasAITaskOM input) {
        String[] eventIndex2LabelId = getEventIndex2LabelId(input.getReIds(), input.getEvents().size());
        List<AiEvent> allEvents = new LinkedList<>();
        List<AiTaskResult> aiTaskResults = new LinkedList<>();
        int eventIndex = -1;
        for (EventOM eventOM : input.getEvents()) {
            eventIndex++;
            if (eventOM.getObjectCategory() == null) {
                com.addx.iotcamera.util.LogUtil.warn(log, "handleAIEventResult 收到未知objectCategory sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                continue;
            }
            AiObjectActionEnum eventType = EnumFindUtil.findByEnumName(eventOM.getName(), AiObjectActionEnum.class);
            if (eventType == null) {
                com.addx.iotcamera.util.LogUtil.warn(log, "handleAIEventResult 收到未知eventName:{},sn={},traceId={}", eventOM.getName(), input.getDeviceSn(), input.getTraceId());
                continue;
            }
            if (eventOM.getFilterResult() == 1) {
                // 算法端认为要过滤掉的事件
                continue;
            }
            // 处理鸟的事件，跟新用户级别的鸟信息
            if(eventOM.getObjectCategory() == RecognitionObjectCategory.BIRD
                    && !eventOM.getPossibleSubcategory().isEmpty()){
//                log.info("handleAIEventResult handle bird event sn={},traceId={}", input.getDeviceSn(), input.getTraceId());
                BirdTabDO birdTabDO = new BirdTabDO();
                birdTabDO.setUserId(Integer.valueOf(input.getOwnerId()));
                birdTabDO.setLastTraceId(input.getTraceId());
                birdTabDO.setImgSrcTraceId(input.getTraceId());
                BigDecimal confidence = new BigDecimal(0.0E00);
                for( PossibleSubcategory possibleSubcategory : eventOM.getPossibleSubcategory()){
                    if(confidence.compareTo(possibleSubcategory.getConfidence()) < 0){
                        confidence = possibleSubcategory.getConfidence();
                        birdTabDO.setBirdStdName(possibleSubcategory.getName());
                    }
                }
                birdTabDO.setScore(confidence.floatValue());
                birdTabDO.setBirdImageUrl(eventOM.getSummaryUrl());
                birdTabDO.setLastVisitTime((int)(System.currentTimeMillis()/1000));
                if(birdTabDO.getBirdImageUrl() != null){
                    Map<Integer, Integer> result = birdTabService.insertOrUpdateBirdTabInfo(birdTabDO, userRoleService.findAllUsersForDevice(input.getDeviceSn()) );
                    eventOM.getPossibleSubcategory().forEach(possibleSubcategory -> {
                        if(Objects.equals(birdTabDO.getBirdStdName(), possibleSubcategory.getName())
                                && result.get(input.getOwnerId()) == 0){
                            possibleSubcategory.setFirstVisit(true);
                        }else{
                            possibleSubcategory.setFirstVisit(false);
                        }
                    });
                }

            }
            AiEvent event = new AiEvent()
                    .setSummaryUrl(eventOM.getSummaryUrl())
                    .setEventObject(eventOM.getObjectCategory().getEventObject())
                    .setEventType(eventType)
                    .setActivatedZones(eventOM.getActivityZoneIds())
                    .setPossibleSubcategory(birdLoversService.handleAiEventPossibleSubcategory(eventOM.getPossibleSubcategory()))
                    .setLabelId(eventIndex2LabelId[eventIndex]);
            // 事件无论推没推过，都应该放入最后的事件汇总中
            allEvents.add(event);
            if (StringUtils.isBlank(eventOM.getSummaryUrl())) {
                // 目前线上的APP推送中，人和宠物不需要事件识别，因此算法端为了节省资源，没传图片过来
                if (eventOM.getObjectCategory() == RecognitionObjectCategory.PERSON) {
                } else if (eventOM.getObjectCategory() == RecognitionObjectCategory.PET) {
                } else {
                    com.addx.iotcamera.util.LogUtil.error(log, "handleAIEventResult 收到summaryUrl为空! eventName={},sn={},traceId={}", eventOM.getName(), input.getDeviceSn(), input.getTraceId());
                }
                continue;
            }
            if (isPushed(input.getTraceId(), eventOM.getObjectCategory(), eventType)) continue;
            AiTaskResult aiTaskResult = new AiTaskResult()
                    .setTraceId(input.getTraceId())
                    .setSerialNumber(input.getDeviceSn())
                    .setImageUrl(eventOM.getSummaryUrl()) // 算法挑选的事件图
                    .setEvents(Arrays.asList(event))
                    .setOrder(input.getOrder());
            aiTaskResults.add(aiTaskResult);
        }
        log.info("handleAIEventResult sn={},traceId={},aiTaskResults={}", input.getDeviceSn(), input.getTraceId(), JSON.toJSONString(aiTaskResults));
        for (AiTaskResult aiTaskResult : aiTaskResults) {
            try {
                notificationService.aiTaskNotify(aiTaskResult, true);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleAIEventResult aiTaskNotify发生未知异常,sn={},traceId={}", input.getDeviceSn(), input.getTraceId(), e);
            }
        }
        AiTaskResult aiTaskResult = new AiTaskResult()
                .setTraceId(input.getTraceId())
                .setSerialNumber(input.getDeviceSn())
                .setImageUrl(input.getCoverImageUrl()) // 算法挑选的封面图
                .setEvents(allEvents) // 最后的汇总结果要包含发现过的所有事件
                .setOrder(input.getOrder());
        log.info("handleAIEventResult aiTaskResult={}", JSON.toJSONString(aiTaskResult));
        notificationService.aiTaskNotify(aiTaskResult, false);
    }

    private boolean isPushed(String traceId, RecognitionObjectCategory category, AiObjectActionEnum eventType) {
        String localKey = category.getCode() + ":" + eventType.name();
        String globalKey = "saasAiTaskResult:isPushed:" + localKey + ":" + traceId;
        return !firstKeyHelper.isFirstKey(traceId, localKey, globalKey);
    }

    @PreDestroy
    public void preDestroy() {
    }

    public static String[] getEventIndex2LabelId(List<ReIdOM> reIds, int eventNum) {
        String[] labelIds = new String[eventNum];
        Arrays.fill(labelIds, "");
        if (reIds == null) return labelIds;
        for (ReIdOM reId : reIds) {
            if (StringUtils.isBlank(reId.getLabelId()) || reId.getEventIndexes() == null) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleAIEventResult labelId或eventIndexes为空! reIds={},eventNum={}", JSON.toJSONString(reIds), eventNum);
                continue;
            }
            for (int index : reId.getEventIndexes()) {
                if (index >= 0 && index < labelIds.length) {
                    labelIds[index] = reId.getLabelId();
                } else {
                    com.addx.iotcamera.util.LogUtil.error(log, "handleAIEventResult eventIndexes数组越界! reIds={},eventNum={}", JSON.toJSONString(reIds), eventNum);
                }
            }
        }
        return labelIds;
    }

}
