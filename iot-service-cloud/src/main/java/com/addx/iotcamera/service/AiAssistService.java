package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.device.DeviceMessageNotification;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.collect.ImmutableSet;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.AiObjectEnumUtil;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.addx.iot.domain.extension.entity.EventObjectSwitch;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Builder
@Component
public class AiAssistService {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private ModelAiEventConfig modelAiEventConfig;
    @Autowired
    private DeviceManualService deviceManualService;
    @Autowired
    private DeviceAiSettingsService deviceAiSettingsService;
    @Autowired
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Autowired
    private VipService vipService;
    @Autowired
    private DeviceModelEventService deviceModelEventService;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceSettingService deviceSettingService;
    @Autowired
    private UserService userService;
    @Autowired
    @Lazy
    private PushService pushService;

    @Autowired
    @Lazy
    private DeviceModelConfigService deviceModelConfigService;

    public Result<DeviceAiSwitch> queryEventObjectSwitch(Integer userId, String serialNumber) {

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo == null) {
            return ResultCollection.DEVICE_NO_EXIT.getNullDataResult();
        }

        Set<String> aiSaasEventObjects = deviceModelEventService.queryDeviceModelEvent(modelNo).stream()
                .sorted(AiObjectEnumUtil::compare).collect(Collectors.toSet());

        Set<String> aiEdgeEventObjects = new HashSet<>();
        CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getRowDeviceSupport(serialNumber);
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportPersonAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PERSON.getName());
        }
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportPetAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PET.getName());
        }
        String tenantId = userService.getUserTenantId(userId);
        if(AppConstants.TENANTID_SAFEMO.equals(tenantId)) {
            aiEdgeEventObjects.add(AiActionEnum.All_OTHER_MOTIONS.getActionName());
        }

        Set<String> modelEventObjects = new HashSet<>();
        modelEventObjects.addAll(aiSaasEventObjects);
        modelEventObjects.addAll(aiEdgeEventObjects);

        boolean isVip = vipService.isVipDevice(userId, serialNumber);

        log.debug("queryEventObjectSwitch aiSaasEventObjects {} aiEdgeEventObjects {} isVip {}", aiSaasEventObjects, aiEdgeEventObjects, isVip);

        Set<String> userEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber).stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        List<EventObjectSwitch> list = new LinkedList();
        for (String eventObject : modelEventObjects) {
            Boolean canModify = isVip || aiEdgeEventObjects.contains(eventObject);
            if(!canModify) {
                log.debug("filter out can not modify eventObject {}", eventObject);
                continue;
            }
            list.add(new EventObjectSwitch().setEventObject(eventObject)
                    .setChecked(userEventObjects.contains(eventObject)).setCanModify(canModify));
        }
        DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);
        String deviceName = Optional.ofNullable(deviceInfo).map(DeviceDO::getDeviceName).orElse("");
        return new Result(new DeviceAiSwitch().setSerialNumber(serialNumber).setList(list)
                .setDeviceName(deviceName));
    }

    /**
     * motion 开拍选项展示
     * @param userId
     * @param serialNumber
     * @return
     */
    public Result<DeviceAiSwitch> queryEventObjectSwitchV2(Integer userId, String serialNumber) {

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo == null) {
            return ResultCollection.DEVICE_NO_EXIT.getNullDataResult();
        }

        Set<String> aiSaasEventObjects = deviceModelEventService.queryDeviceModelEvent(modelNo).stream()
                .sorted(AiObjectEnumUtil::compare).collect(Collectors.toSet());

        Set<String> aiEdgeEventObjects = new HashSet<>();
        CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getRowDeviceSupport(serialNumber);
        // 设备开拍的对象
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportPersonAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PERSON.getName());
        }
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportPetAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PET.getName());
        }
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportPackageAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PACKAGE.getName());
        }
        if(BooleanUtils.isTrue(cloudDeviceSupport.getSupportVehicleAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.VEHICLE.getName());
        }
        if(BooleanUtils.isTrue(Objects.equals(cloudDeviceSupport.getSupportJson().getInteger("supportBirdDetect"), 1))){
            aiEdgeEventObjects.add(AiObjectEnum.BIRD.getName());
        }
        if(BooleanUtils.isTrue(Objects.equals(cloudDeviceSupport.getSupportJson().getInteger("supportNuisanceAnimalDetect"), 1))){
            aiEdgeEventObjects.add(AiObjectEnum.NUISANCE_ANIMAL.getName());
        }

        aiEdgeEventObjects.add(AiObjectEnum.All_OTHER_MOTIONS.getName());


        //modelEventObjects 取aiEdgeEventObjects和aiSaasEventObjects交集
        Set<String> modelEventObjects = new HashSet<>(aiSaasEventObjects);
        modelEventObjects.retainAll(aiEdgeEventObjects);
        // 强制添加一个All_OTHER_MOTIONS事件对象
        modelEventObjects.add(AiObjectEnum.All_OTHER_MOTIONS.getName());

        log.debug("queryEventObjectSwitch aiSaasEventObjects {} aiEdgeEventObjects {}", aiSaasEventObjects, aiEdgeEventObjects);

        Set<String> userEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber).stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        List<EventObjectSwitch> list = new LinkedList();
        for (String eventObject : modelEventObjects) {
            Boolean canModify = aiEdgeEventObjects.contains(eventObject);
            if(!canModify) {
                log.debug("filter out can not modify eventObject {}", eventObject);
                continue;
            }
            List<String> supportedSources = new ArrayList<>();
            if(aiEdgeEventObjects.contains(eventObject)){
                supportedSources.add(AppConstants.SUPPORT_FROM_DEVICE);
            }
            if(aiSaasEventObjects.contains(eventObject)){
                supportedSources.add(AppConstants.SUPPORT_FROM_MODEL);
            }
            list.add(new EventObjectSwitch().setEventObject(eventObject)
                    .setChecked(userEventObjects.contains(eventObject)).setCanModify(canModify)
                    .setSupportedSources(supportedSources));
        }
        DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);
        String deviceName = Optional.ofNullable(deviceInfo).map(DeviceDO::getDeviceName).orElse("");
        return new Result(new DeviceAiSwitch().setSerialNumber(serialNumber).setList(list)
                .setDeviceName(deviceName));
    }

    public Result<DeviceAiSwitch> queryEventObjectSwitchSafemo(Integer userId, String serialNumber) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo == null) {
            return ResultCollection.DEVICE_NO_EXIT.getNullDataResult();
        }

        Set<String> aiSaasEventObjects = deviceModelEventService.queryDeviceModelEvent(modelNo).stream()
                .sorted(AiObjectEnumUtil::compare).collect(Collectors.toSet());

        Set<String> aiEdgeEventObjects = new HashSet<>();
        CloudDeviceSupport deviceSupport = deviceInfoService.getRowDeviceSupport(serialNumber);
        if(BooleanUtils.isTrue(deviceSupport.getSupportPersonAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PERSON.getObjectName());
        }
        if(BooleanUtils.isTrue(deviceSupport.getSupportPetAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PET.getObjectName());
        }
        if(BooleanUtils.isTrue(deviceSupport.getSupportPackageAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.PACKAGE.getObjectName());
        }
        if(BooleanUtils.isTrue(deviceSupport.getSupportVehicleAi())) {
            aiEdgeEventObjects.add(AiObjectEnum.VEHICLE.getObjectName());
        }
        aiEdgeEventObjects.add(AiActionEnum.All_OTHER_MOTIONS.getActionName());

        Set<String> modelEventObjects = new HashSet<>();
        modelEventObjects.addAll(aiSaasEventObjects);
        modelEventObjects.addAll(aiEdgeEventObjects);


        log.debug("queryEventObjectSwitchSafemo aiSaasEventObjects {} aiEdgeEventObjects {} ", aiSaasEventObjects, aiEdgeEventObjects);

        Set<String> userEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber).stream().map(AiObjectEnum::getObjectName).collect(Collectors.toSet());
        List<EventObjectSwitch> list = new LinkedList();
        for (String eventObject : modelEventObjects) {
            list.add(new EventObjectSwitch().setEventObject(eventObject)
                    .setChecked(userEventObjects.contains(eventObject)).setCanModify(true));
        }
        DeviceDO deviceInfo = deviceService.getAllDeviceInfo(serialNumber);
        String deviceName = Optional.ofNullable(deviceInfo).map(DeviceDO::getDeviceName).orElse("");
        return new Result(new DeviceAiSwitch().setSerialNumber(serialNumber).setList(list)
                .setDeviceName(deviceName));
    }

    @SentinelResource("updateEventObjectSwitch")
    public Result updateEventObjectSwitch(Integer userId, String serialNumber, List<EventObjectSwitch> list) throws Exception {
        if(CollectionUtils.isEmpty(list)) {
            return Result.Success();
        }

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo == null) {
            return ResultCollection.DEVICE_NO_EXIT.getNullDataResult();
        }
        Set<String> enableEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber)
                .stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        MessageNotificationSetting notifySetting = messageNotificationSettingsService.queryMessageNotificationSetting(serialNumber, userId);
        Set<String> notifyEventObjects = TextUtil.splitToNotBlankSet(notifySetting.getEventObjects(), ',');
        Set<String> notifyEventTypes = TextUtil.splitToNotBlankSet(notifySetting.getPackageEventType(), ',');

        String tenantId = userService.queryTenantIdById(userId);

        List<EventObjectSwitch> aiSwitchList = AppConstants.TENANTID_SAFEMO.equals(tenantId) ? queryEventObjectSwitchSafemo(userId,serialNumber).getData().getList() : queryEventObjectSwitch(userId, serialNumber).getData().getList();
        List<String> modelEventObjects = aiSwitchList.stream().map(EventObjectSwitch::getEventObject).collect(Collectors.toList());
        Set<String> canModifyModelEventObjects = aiSwitchList.stream().filter(eventObjectSwitch -> BooleanUtils.isTrue(eventObjectSwitch.getCanModify()))
            .map(EventObjectSwitch::getEventObject)
            .collect(Collectors.toSet());

        boolean notifyChaned = false;
        /* 过滤掉当前数据中的脏数据 begin */
        List<String> moreEventObjects = FuncUtil.subtractToList(notifyEventObjects, modelEventObjects);
        if (moreEventObjects.size() > 0) {
            notifyChaned = true;
            modelEventObjects.removeAll(moreEventObjects);
            for (String moreEventObject : moreEventObjects) {
                Optional.ofNullable(modelAiEventConfig.getParentEvent().get(moreEventObject)).ifPresent(notifyEventTypes::removeAll);
            }
        }
        /* 过滤掉当前数据中的脏数据 end */

        Set<AiObjectEnum> enableList = new HashSet<>();
        Set<AiObjectEnum> disableList = new HashSet<>();
        for (EventObjectSwitch item : list) {
            if (!canModifyModelEventObjects.contains(item.getEventObject())) continue;
            boolean oldChecked = enableEventObjects.contains(item.getEventObject());
            // 未变化的不更新
            if (oldChecked == item.getChecked()) continue;
            if (item.getChecked()) {
                enableList.add(EnumFindUtil.findByName(item.getEventObject(), AiObjectEnum.class));
            } else {
                disableList.add(EnumFindUtil.findByName(item.getEventObject(), AiObjectEnum.class));
            }
            Set<String> allEventTypes = messageNotificationSettingsService.getMessageNotificationConfig(userId, false).getParentEvent2ChildEvents().getOrDefault(item.getEventObject(), Collections.emptySet());
            ImmutableSet<String> initEventTypes = DeviceAiSettingsService.getInitNotifyEventTypes(userService.getUserTenantId(userId)).get(item.getEventObject());
            // 由未选择变为已选择，且需要初始化值的eventObject，才设置初始化值
            if (!oldChecked && item.getChecked() && initEventTypes != null) {
                // 把eventObject添加到事件通知开关中
                notifyChaned |= notifyEventObjects.add(item.getEventObject());
                // 把eventObject下需要默认勾选的eventType添加到事件通知开关中的
                notifyChaned |= notifyEventTypes.addAll(initEventTypes);
                notifyChaned |= notifyEventTypes.removeAll(FuncUtil.subtractToList(allEventTypes, initEventTypes));
            } else {
                // 移除事件通知开关中的eventObject
                notifyChaned |= notifyEventObjects.remove(item.getEventObject());
                // 移除事件通知开关中eventObject下所有的eventType
                notifyChaned |= notifyEventTypes.removeAll(allEventTypes);
            }
        }
        log.info("updateEventObjectSwitch notifySetting changed={},eventObjects={}->{},eventTypes={}->{}", notifyChaned
                , notifySetting.getEventObjects(), notifyEventObjects, notifySetting.getPackageEventType(), notifyEventTypes);
        if (enableList.size() > 0 || disableList.size() > 0) {
            deviceAiSettingsService.updateEnableEventObjectsPartly(userId, serialNumber, enableList, disableList);

            boolean enabledPersonEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PERSON);
            boolean disabledPersonEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PERSON);
            boolean enabledPetEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PET);
            boolean disabledPetEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PET);

            boolean enabledVehicleEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.VEHICLE);
            boolean disabledVehicleEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.VEHICLE);
            boolean enabledPackageEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PACKAGE);
            boolean disabledPackageEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PACKAGE);
            boolean enabledFaceEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.FACE);
            boolean disabledFaceEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.FACE);
            boolean enabledNuisanceAnimalEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.NUISANCE_ANIMAL);
            boolean disabledNuisanceAnimalEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.NUISANCE_ANIMAL);
            boolean enabledBirdEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.BIRD);
            boolean disabledBirdEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.BIRD);
            boolean enabledAllOtherMotionsEventObject = enableList.stream().anyMatch(eventObject -> eventObject.getObjectName() == AiActionEnum.All_OTHER_MOTIONS.getActionName());
            boolean disabledAllOtherMotionsEventObject = disableList.stream().anyMatch(eventObject -> eventObject.getObjectName() == AiActionEnum.All_OTHER_MOTIONS.getActionName());

            if (enabledPersonEventObject || disabledPersonEventObject || enabledPetEventObject || disabledPetEventObject
                    || enabledVehicleEventObject || disabledVehicleEventObject || enabledPackageEventObject || disabledPackageEventObject
                    || enabledFaceEventObject || disabledFaceEventObject
                    || enabledNuisanceAnimalEventObject || disabledNuisanceAnimalEventObject
                    || enabledBirdEventObject || disabledBirdEventObject
                    || enabledAllOtherMotionsEventObject || disabledAllOtherMotionsEventObject) {
                CloudDeviceSupport deviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
                Boolean updateDetectPersonAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportPersonAi()) && (enabledPersonEventObject || disabledPersonEventObject)) {
                    updateDetectPersonAi = enabledPersonEventObject || !disabledPersonEventObject;
                }
                Boolean updateDetectPetAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportPetAi()) && (enabledPetEventObject || disabledPetEventObject)) {
                    updateDetectPetAi = enabledPetEventObject || !disabledPetEventObject;
                }
                Boolean updateDetectVehicleAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportVehicleAi()) && (enabledVehicleEventObject || disabledVehicleEventObject)) {
                    updateDetectVehicleAi = enabledVehicleEventObject || !disabledVehicleEventObject;
                }

                Boolean updateDetectPackageAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportPackageAi()) && (enabledPackageEventObject || disabledPackageEventObject)) {
                    updateDetectPackageAi = enabledPackageEventObject || !disabledPackageEventObject;
                }
                Boolean updateDetectFaceAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportFaceAi()) && (enabledFaceEventObject || disabledFaceEventObject)) {
                    updateDetectFaceAi = enabledFaceEventObject || !disabledFaceEventObject;
                }
                Boolean updateNuisanceAnimalDetect = null;
                if (BooleanUtils.isTrue(CloudDeviceSupport.getSupportNuisanceAnimalDetect(deviceSupport)) && (enabledNuisanceAnimalEventObject || disabledNuisanceAnimalEventObject)) {
                    updateNuisanceAnimalDetect = enabledNuisanceAnimalEventObject || !disabledNuisanceAnimalEventObject;
                }
                Boolean updateDetectBirdAi = null;
                if (BooleanUtils.isTrue(CloudDeviceSupport.getSupportBirdDetect(deviceSupport)) && (enabledBirdEventObject || disabledBirdEventObject)) {
                    updateDetectBirdAi = enabledBirdEventObject || !disabledBirdEventObject;
                }

                Boolean updateAllOtherMotionsAi = null;
                if (BooleanUtils.isTrue(deviceSupport.getSupportVehicleAi()) && (enabledAllOtherMotionsEventObject || disabledAllOtherMotionsEventObject)) {
                    updateAllOtherMotionsAi = enabledAllOtherMotionsEventObject || !disabledAllOtherMotionsEventObject;
                }

                // 非safemo 情况， 老的APP 面对支持开拍过滤的新固件时： 强制打开otherMotion 开关， 防止不录制视频问题
                boolean supportRecordingFilterEnable = deviceSupport.getSupportJson() != null && Objects.equals(deviceSupport.getSupportJson().getBoolean("supportRecordingFilterEnable"),true);
                if(!AppConstants.TENANTID_SAFEMO.equals(tenantId) && supportRecordingFilterEnable) {
                    updateAllOtherMotionsAi = true;
                }

                Boolean updateReportPersonAi = null;
                Boolean updateReportPetAi = null;
                if(!supportRecordingFilterEnable) {
                    // 长电设备 老固件
                    DeviceModel deviceModel= deviceModelConfigService.queryRowDeviceModelByModelNo(deviceManualService.getModelNoBySerialNumber(serialNumber));
                    if(!deviceModel.isCanStandby()){
                        updateReportPersonAi = updateDetectPersonAi;
                        updateReportPetAi = updateDetectPetAi;
                    }
                }
                triggerAiEdgeSettingUpdate(userId, serialNumber, updateReportPersonAi, updateReportPetAi, updateDetectPersonAi, updateDetectVehicleAi, updateDetectPackageAi, updateDetectPetAi, updateDetectFaceAi, updateNuisanceAnimalDetect, updateDetectBirdAi, updateAllOtherMotionsAi);
            }
        }
        // 只有方案产品检测修改需要同时改动通知设计， safemo是分开的，独立设置
        if (notifyChaned && !AppConstants.TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))) {
            MessageNotificationSetting notifySettingUpdate = MessageNotificationSetting.builder()
                    .eventObjects(StringUtils.join(notifyEventObjects, ","))
                    .packageEventType(StringUtils.join(notifyEventTypes, ","))
                    // 如果所有的ai通知选项都被关闭了，"其它"自动打开
                    .enableOther(notifySetting.getEnableOther())
                    .userId(userId).serialNumber(serialNumber).build();
            messageNotificationSettingsService.updateMessageNotificationSettings(notifySettingUpdate);
        }
        // 清除pir视频ai分析选项缓存
        pushService.clearDevicePirNotifyFactorCache(serialNumber);
        return Result.Success();
    }

    @SentinelResource("updateEventObjectSwitchV2")
    public Result updateEventObjectSwitchV2(Integer userId, String serialNumber, List<EventObjectSwitch> list) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return Result.Success();
        }

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo == null) {
            return ResultCollection.DEVICE_NO_EXIT.getNullDataResult();
        }
        Set<String> enableEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber)
                .stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        List<EventObjectSwitch> aiSwitchList = queryEventObjectSwitchV2(userId, serialNumber).getData().getList();
        Set<String> canModifyModelEventObjects = aiSwitchList.stream().filter(eventObjectSwitch -> BooleanUtils.isTrue(eventObjectSwitch.getCanModify()))
                .map(EventObjectSwitch::getEventObject)
                .collect(Collectors.toSet());

        Set<AiObjectEnum> enableList = new HashSet<>();
        Set<AiObjectEnum> disableList = new HashSet<>();
        for (EventObjectSwitch item : list) {
            if (!canModifyModelEventObjects.contains(item.getEventObject())) continue;
            boolean oldChecked = enableEventObjects.contains(item.getEventObject());
            // 未变化的不更新
            if (oldChecked == item.getChecked()) continue;
            if (item.getChecked()) {
                enableList.add(EnumFindUtil.findByName(item.getEventObject(), AiObjectEnum.class));
            } else {
                disableList.add(EnumFindUtil.findByName(item.getEventObject(), AiObjectEnum.class));
            }
            Set<String> allEventTypes = messageNotificationSettingsService.getMessageNotificationConfig(userId, true).getParentEvent2ChildEvents().getOrDefault(item.getEventObject(), Collections.emptySet());
            ImmutableSet<String> initEventTypes = DeviceAiSettingsService.getInitNotifyEventTypes(userService.getUserTenantId(userId)).get(item.getEventObject());
        }
        if (!enableList.isEmpty() || !disableList.isEmpty()) {
            deviceAiSettingsService.updateEnableEventObjectsPartly(userId, serialNumber, enableList, disableList);

            boolean enabledPersonEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PERSON);
            boolean disabledPersonEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PERSON);
            boolean enabledPetEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PET);
            boolean disabledPetEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PET);

            boolean enabledVehicleEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.VEHICLE);
            boolean disabledVehicleEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.VEHICLE);
            boolean enabledPackageEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PACKAGE);
            boolean disabledPackageEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.PACKAGE);
            boolean enabledFaceEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.FACE);
            boolean disabledFaceEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.FACE);
            boolean enabledNuisanceAnimalEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.NUISANCE_ANIMAL);
            boolean disabledNuisanceAnimalEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.NUISANCE_ANIMAL);
            boolean enabledBirdEventObject = enableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.BIRD);
            boolean disabledBirdEventObject = disableList.stream().anyMatch(eventObject -> eventObject == AiObjectEnum.BIRD);
            boolean enabledAllOtherMotionsEventObject = enableList.stream().anyMatch(eventObject -> eventObject.getObjectName() == AiActionEnum.All_OTHER_MOTIONS.getActionName());
            boolean disabledAllOtherMotionsEventObject = disableList.stream().anyMatch(eventObject -> eventObject.getObjectName() == AiActionEnum.All_OTHER_MOTIONS.getActionName());

            if (enabledPersonEventObject || disabledPersonEventObject || enabledPetEventObject || disabledPetEventObject
                    || enabledVehicleEventObject || disabledVehicleEventObject || enabledPackageEventObject || disabledPackageEventObject
                    || enabledFaceEventObject || disabledFaceEventObject
                    || enabledNuisanceAnimalEventObject || disabledNuisanceAnimalEventObject
                    || enabledBirdEventObject || disabledBirdEventObject
                    || enabledAllOtherMotionsEventObject || disabledAllOtherMotionsEventObject) {
                CloudDeviceSupport deviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
                DeviceAppSettingsDO appSetting = deviceSettingService.getDeviceSetting(serialNumber);
                boolean needUpdateDeviceSetting = false;
                if (BooleanUtils.isTrue(deviceSupport.getSupportPersonAi()) && (enabledPersonEventObject || disabledPersonEventObject)
                        && ObjectUtils.notEqual(appSetting.getSupportPersonAi(), enabledPersonEventObject || !disabledPersonEventObject)) {
                    appSetting.setDetectPersonAi(enabledPersonEventObject || !disabledPersonEventObject);
                    needUpdateDeviceSetting = true;
                }
                if (BooleanUtils.isTrue(deviceSupport.getSupportPetAi()) && (enabledPetEventObject || disabledPetEventObject)
                        && ObjectUtils.notEqual(appSetting.getSupportPetAi(), enabledPetEventObject || !disabledPetEventObject)) {
                    appSetting.setDetectPetAi(enabledPetEventObject || !disabledPetEventObject);
                    needUpdateDeviceSetting = true;
                }
                if (BooleanUtils.isTrue(deviceSupport.getSupportVehicleAi()) && (enabledVehicleEventObject || disabledVehicleEventObject)
                        && ObjectUtils.notEqual(appSetting.getSupportVehicleAi(), enabledVehicleEventObject || !disabledVehicleEventObject)) {
                    appSetting.setDetectVehicleAi(enabledVehicleEventObject || !disabledVehicleEventObject);
                    needUpdateDeviceSetting = true;
                }
                if (BooleanUtils.isTrue(deviceSupport.getSupportPackageAi()) && (enabledPackageEventObject || disabledPackageEventObject)
                        && ObjectUtils.notEqual(appSetting.getSupportPackageAi(), enabledPackageEventObject || !disabledPackageEventObject)) {
                    appSetting.setDetectPackageAi(enabledPackageEventObject || !disabledPackageEventObject);
                    needUpdateDeviceSetting = true;
                }
                if (BooleanUtils.isTrue(deviceSupport.getSupportFaceAi()) && (enabledFaceEventObject || disabledFaceEventObject)
                        && ObjectUtils.notEqual(appSetting.getSupportFaceAi(), enabledFaceEventObject || !disabledFaceEventObject)) {
                    appSetting.setDetectFaceAi(enabledFaceEventObject || !disabledFaceEventObject);
                    needUpdateDeviceSetting = true;
                }
                if (BooleanUtils.isTrue(CloudDeviceSupport.getSupportNuisanceAnimalDetect(deviceSupport)) && (enabledNuisanceAnimalEventObject || disabledNuisanceAnimalEventObject)
                        && ObjectUtils.notEqual(appSetting.getPropertyJson().getInteger("supportNuisanceAnimalDetect"), (enabledNuisanceAnimalEventObject || !disabledNuisanceAnimalEventObject )? 1 : 0)) {
                    appSetting.getPropertyJson().put("detectNuisanceAnimalAi", (enabledNuisanceAnimalEventObject || !disabledNuisanceAnimalEventObject )? 1 : 0);
                    needUpdateDeviceSetting = true;
                }

                if (BooleanUtils.isTrue(CloudDeviceSupport.getSupportBirdDetect(deviceSupport)) && (enabledBirdEventObject || disabledBirdEventObject)
                        && ObjectUtils.notEqual(appSetting.getPropertyJson().getInteger("supportBirdDetect"), (enabledBirdEventObject || !disabledBirdEventObject) ? 1 : 0)) {
                    appSetting.getPropertyJson().put("detectBirdAi", (enabledBirdEventObject || !disabledBirdEventObject) ? 1 : 0);
                    needUpdateDeviceSetting = true;
                }

                if((enabledAllOtherMotionsEventObject || disabledAllOtherMotionsEventObject)
                        && ObjectUtils.notEqual(appSetting.getEnableOtherMotionAi(), enabledAllOtherMotionsEventObject || !disabledAllOtherMotionsEventObject)){
                    appSetting.setEnableOtherMotionAi(enabledAllOtherMotionsEventObject || !disabledAllOtherMotionsEventObject);
                    needUpdateDeviceSetting = true;
                }
                // update device setting
                if(needUpdateDeviceSetting) {
                    log.info("updateEventObjectSwitchV2 appSetting：{} ", appSetting);
                    final long startTime = System.currentTimeMillis();
                    deviceSettingService.updateUserConfig(userId, appSetting, startTime, false);
                }
            }
        }
        return Result.Success();
    }

    public void triggerAiEdgeSettingUpdate(Integer userId, String serialNumber, Boolean updateReportPersonAi, Boolean updateReportPetAi) throws Exception {
        DeviceAppSettingsDO appSetting = deviceSettingService.getDeviceSetting(serialNumber);
        boolean needUpdateDeviceSetting = false;

        if(updateReportPersonAi != null && ObjectUtils.notEqual(appSetting.getReportPersonAi(), updateReportPersonAi)) {
            appSetting.setReportPersonAi(updateReportPersonAi);
            needUpdateDeviceSetting = true;
        }
        if(updateReportPetAi != null && ObjectUtils.notEqual(appSetting.getReportPetAi(), updateReportPetAi)) {
            appSetting.setReportPetAi(updateReportPetAi);
            needUpdateDeviceSetting = true;
        }

        // update device setting
        if(needUpdateDeviceSetting) {
            log.info("updateEventObjectSwitchV2 updateReportPersonAi {} updateReportPetAi {}", updateReportPersonAi, updateReportPetAi);
            final long startTime = System.currentTimeMillis();
            deviceSettingService.updateUserConfig(userId, appSetting, startTime, false);
        }
    }

    public void triggerAiEdgeSettingUpdate(Integer userId, String serialNumber, Boolean updateReportPersonAi, Boolean updateReportPetAi,
                                           Boolean updateDetectPersonAi, Boolean updateDetectVehicleAi, Boolean updateDetectPackageAi, Boolean updateDetectPetAi, Boolean updateDetectFaceAi,
                                           Boolean updateNuisanceAnimalDetect, Boolean updateDetectBirdAi, Boolean updateAllOtherMotionsAi) throws Exception {
        DeviceAppSettingsDO appSetting = deviceSettingService.getDeviceSetting(serialNumber);
        boolean needUpdateDeviceSetting = false;

        if(updateReportPersonAi != null && ObjectUtils.notEqual(appSetting.getReportPersonAi(), updateReportPersonAi)) {
            appSetting.setReportPersonAi(updateReportPersonAi);
            needUpdateDeviceSetting = true;
        }
        if(updateReportPetAi != null && ObjectUtils.notEqual(appSetting.getReportPetAi(), updateReportPetAi)) {
            appSetting.setReportPetAi(updateReportPetAi);
            needUpdateDeviceSetting = true;
        }
        if(updateDetectPersonAi != null && ObjectUtils.notEqual(appSetting.getDetectPersonAi(), updateDetectPersonAi)) {
            appSetting.setDetectPersonAi(updateDetectPersonAi);
            needUpdateDeviceSetting = true;
        }

        if(updateDetectPetAi != null && ObjectUtils.notEqual(appSetting.getDetectPetAi(), updateDetectPetAi)) {
            appSetting.setDetectPetAi(updateDetectPetAi);
            needUpdateDeviceSetting = true;
        }

        if(updateDetectVehicleAi != null && ObjectUtils.notEqual(appSetting.getDetectVehicleAi(), updateDetectVehicleAi)) {
            appSetting.setDetectVehicleAi(updateDetectVehicleAi);
            needUpdateDeviceSetting = true;
        }

        if(updateDetectPackageAi != null && ObjectUtils.notEqual(appSetting.getDetectPackageAi(), updateDetectPackageAi)) {
            appSetting.setDetectPackageAi(updateDetectPackageAi);
            needUpdateDeviceSetting = true;
        }

        if(updateDetectFaceAi != null && ObjectUtils.notEqual(appSetting.getDetectFaceAi(), updateDetectFaceAi)) {
            appSetting.setDetectFaceAi(updateDetectFaceAi);
            needUpdateDeviceSetting = true;
        }

        if(updateNuisanceAnimalDetect != null && ObjectUtils.notEqual(appSetting.getDetectNuisanceAnimalAi(), updateNuisanceAnimalDetect)){
            appSetting.setDetectNuisanceAnimalAi(updateNuisanceAnimalDetect);
            needUpdateDeviceSetting = true;
        }
        
        if(updateDetectBirdAi != null && ObjectUtils.notEqual(appSetting.getDetectBirdAi(), updateDetectBirdAi)) {
            appSetting.setDetectBirdAi(updateDetectBirdAi);
            needUpdateDeviceSetting = true;
        }

        if(updateAllOtherMotionsAi != null && ObjectUtils.notEqual(appSetting.getEnableOtherMotionAi(), updateAllOtherMotionsAi)) {
            appSetting.setEnableOtherMotionAi(updateAllOtherMotionsAi);
            needUpdateDeviceSetting = true;
        }

        // update device setting
        if(needUpdateDeviceSetting) {
            log.info("triggerAiEdgeSettingUpdate updateReportPersonAi {} updateReportPetAi {}", updateReportPersonAi, updateReportPetAi);
            final long startTime = System.currentTimeMillis();
            deviceSettingService.updateUserConfig(userId, appSetting, startTime, false);
        }
    }

    public void triggerAiEdgeSettingUpdateFromDeviceSupport(Integer userId, String serialNumber, CloudDeviceSupport cloudDeviceSupport) throws Exception {
        boolean enablePersonEventObject = BooleanUtils.isTrue(cloudDeviceSupport.getSupportPersonAi());
        boolean enablePetEventObject = BooleanUtils.isTrue(cloudDeviceSupport.getSupportPetAi());
        boolean enableVehicleEventObject = BooleanUtils.isTrue(cloudDeviceSupport.getSupportVehicleAi());
        boolean enablePackageEventObject = BooleanUtils.isTrue(cloudDeviceSupport.getSupportPackageAi());
        boolean enableBirdEventObject = BooleanUtils.isTrue(CloudDeviceSupport.getSupportBirdDetect(cloudDeviceSupport));
        boolean enableNuisanceAnimalEventObject = BooleanUtils.isTrue(CloudDeviceSupport.getSupportNuisanceAnimalDetect(cloudDeviceSupport));

        boolean supportRecordingFilterEnable = cloudDeviceSupport.getSupportJson() != null && Objects.equals(cloudDeviceSupport.getSupportJson().getBoolean("supportRecordingFilterEnable"),true);
        boolean supportPersonTag = cloudDeviceSupport.getSupportJson() != null && Objects.equals(cloudDeviceSupport.getSupportJson().getBoolean("supportPersonTag"),true);
        boolean supportPetTag = cloudDeviceSupport.getSupportJson() != null && Objects.equals(cloudDeviceSupport.getSupportJson().getBoolean("supportPetTag"),true);

        Set<String> enabledEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber).stream().map(AiObjectEnum::getName).collect(Collectors.toSet());

        boolean updateReportPersonAi = enablePersonEventObject && enabledEventObjects.contains(AiObjectEnum.PERSON.getName());
        boolean updateReportPetAi = enablePetEventObject && enabledEventObjects.contains(AiObjectEnum.PET.getObjectName());
        if(supportRecordingFilterEnable) {
            Set<String> enableObjectTags =  messageNotificationSettingsService.queryMessageNotificationSettingsListV3(serialNumber, userId).stream()
                    .filter(DeviceMessageNotification::getChoice).map(DeviceMessageNotification::getName).collect(Collectors.toSet());
            updateReportPersonAi = supportPersonTag  && enableObjectTags.contains(AiObjectEnum.PERSON.getObjectName());
            updateReportPetAi = supportPetTag  && enableObjectTags.contains(AiObjectEnum.PET.getObjectName());
        }else{
            // 长电设备 老固件 默认开启 打标服务  这个分支  很快就走不到
            DeviceModel deviceModel= deviceModelConfigService.queryRowDeviceModelByModelNo(deviceManualService.getModelNoBySerialNumber(serialNumber));
            if(!deviceModel.isCanStandby()){
                updateReportPersonAi = true;
                updateReportPetAi = true;
            }
        }

        boolean updateDetectPersonAi = enablePersonEventObject && enabledEventObjects.contains(AiObjectEnum.PERSON.getName());
        boolean updateDetectVehicleAi = enableVehicleEventObject && enabledEventObjects.contains(AiObjectEnum.VEHICLE.getObjectName());
        boolean updateDetectPackageAi = enablePackageEventObject && enabledEventObjects.contains(AiObjectEnum.PACKAGE.getObjectName());
        boolean updateDetectPetAi = enablePetEventObject && enabledEventObjects.contains(AiObjectEnum.PET.getObjectName());
        boolean updateDetectFaceAi = enablePackageEventObject && enabledEventObjects.contains(AiObjectEnum.FACE.getObjectName());
        boolean updateDetectNuisanceAnimalAi = enableNuisanceAnimalEventObject && enabledEventObjects.contains(AiObjectEnum.NUISANCE_ANIMAL.getObjectName());
        boolean updateDetectBirdAi = enableBirdEventObject && enabledEventObjects.contains(AiObjectEnum.BIRD.getObjectName());

        boolean updateAllOtherMotionAi = enabledEventObjects.remove(AiActionEnum.All_OTHER_MOTIONS.getActionName());


        if(!AppConstants.TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))) {
            if (enabledEventObjects.size() <= 0) {
                updateAllOtherMotionAi = true;
            }
            // 新支持开拍过滤的设备， 有其它开拍选项就关闭other motion
            if (supportRecordingFilterEnable && enabledEventObjects.size() > 0) {
                updateAllOtherMotionAi = false;
            }
        }


        triggerAiEdgeSettingUpdate(userId, serialNumber, updateReportPersonAi, updateReportPetAi, updateDetectPersonAi, updateDetectVehicleAi, updateDetectPackageAi, updateDetectPetAi, updateDetectFaceAi, updateDetectNuisanceAnimalAi, updateDetectBirdAi, updateAllOtherMotionAi);
    }
}
