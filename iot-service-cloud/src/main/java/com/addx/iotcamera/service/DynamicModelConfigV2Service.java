package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.device.CodecFullDetailDo;
import com.addx.iotcamera.bean.device.SingleDeviceCodecDo;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.report.ReportInfo;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChangedV2;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfigV2;
import com.addx.iotcamera.dao.DynamicModelConfigV2DAO;
import com.addx.iotcamera.dao.device.DeviceCodecDAO;
import com.addx.iotcamera.helper.ProcessLogHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.util.FuncUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.yaml.snakeyaml.Yaml;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;

@Slf4j
@Service
public class DynamicModelConfigV2Service {

    public static final String CODEC_PROFILE = "codecProfile";
    @Autowired
    private DynamicModelConfigV2DAO dynamicModelConfigV2DAO;
    @Autowired
    private DeviceManualService deviceManualService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private DeviceSettingService deviceSettingService;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceCodecDAO deviceCodecDAO;

    private static Set<String> filterRootSet = new HashSet<>(Arrays.asList(CODEC_PROFILE));

    public static Result parseDynamicModelConfigV2(String name, String content, Map<String, Map<String, DynamicModelConfigV2>> rootKey2ModelMap) {
        Yaml yaml = new Yaml();
        JSONObject root;
        try {
            root = new JSONObject(yaml.loadAs(content, Map.class));
        } catch (Throwable e) {
            String partContent = content != null ? content.substring(Math.min(200, content.length())) : "null";
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfigV2 配置文件不是合法的yml格式! name={},content={}", name, partContent, e);
            return new Result(10, "配置文件不是合法的yml格式! name=" + name + ",content=" + partContent, null);
        }
        for (String rootKey : root.keySet()) {
            // 即使rootKey中没有任何内容，也要创建一个modelMap，确保能删除
            Map<String, DynamicModelConfigV2> modelMap = rootKey2ModelMap.computeIfAbsent(rootKey, k -> new LinkedHashMap<>());
            JSONObject models = root.getJSONObject(rootKey);
            if (models == null) continue;
            for (String modelNo : models.keySet()) {
                Object value = models.get(modelNo);
                if (value == null) continue;
                String normalizedContent = FuncUtil.toNormalizingJsonString(JSON.toJSON(value));
                DynamicModelConfigV2 model = new DynamicModelConfigV2().setRootKey(rootKey)
                        .setModelNo(modelNo).setContent(normalizedContent);
                modelMap.put(modelNo, model);
            }
        }
        return Result.Success();
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = {Throwable.class})
    public Result<DynamicModelChangedV2> updateDynamicModelConfigV2(Map<String, Map<String, DynamicModelConfigV2>> rootKey2ModelMap) {
        String rootKey2Size = rootKey2ModelMap.entrySet().stream()
                .map(it -> "\"" + it.getKey() + "\":" + (it.getValue() == null ? null : it.getValue().size()))
                .collect(Collectors.joining(",", "{", "}"));
        log.info("updateDynamicModelConfigV2 total begin! rootKey2Size={}", rootKey2Size);
        DynamicModelChangedV2 totalChanged = new DynamicModelChangedV2();
        for (Map.Entry<String, Map<String, DynamicModelConfigV2>> entry : rootKey2ModelMap.entrySet()) {
            final String rootKey = entry.getKey();
            final Map<String, DynamicModelConfigV2> modelMap = entry.getValue();
            final List<DynamicModelConfigV2> oldModelList = dynamicModelConfigV2DAO.queryByRootKey(rootKey);
            DynamicModelChangedV2 changed = new DynamicModelChangedV2();
            List<String> deleteModelNos = new LinkedList<>();
            List<DynamicModelConfigV2> updateModels = new LinkedList<>();
            for (DynamicModelConfigV2 oldModel : oldModelList) {
                final String modelNo = oldModel.getModelNo();
                DynamicModelConfigV2 newModel = modelMap.get(modelNo);
                if (newModel == null) {
                    deleteModelNos.add(modelNo);
                    changed.getModelNos().add(modelNo);
                    continue;
                }
                modelMap.remove(modelNo); // 移除掉已存在的,留下新增的
                if (oldModel.contentIsUpdate(newModel.getContent())) {
                    updateModels.add(newModel);
                    changed.getModelNos().add(modelNo);
                    continue;
                }
                log.info("updateDynamicModelConfigV2 型号配置不变！该型号下设备settings不重发 rootKey={},modelNo={}", rootKey, modelNo);
            }
            changed.getModelNos().addAll(modelMap.keySet());
            List<DynamicModelConfigV2> saveModels = new ArrayList<>(modelMap.values());
            changed.setSaveNum(saveModels.size() > 0 ? dynamicModelConfigV2DAO.save(saveModels) : 0);
            changed.setDeleteNum(deleteModelNos.size() > 0 ? dynamicModelConfigV2DAO.deleteByRootKeyAndModelNos(rootKey, deleteModelNos) : 0);
            changed.setUpdateNum(updateModels.stream().map(dynamicModelConfigV2DAO::updateContentByRootKeyAndModelNo).reduce(0, Integer::sum));
            log.info("updateDynamicModelConfigV2 型号配置更新成功 rootKey={},changed={}", rootKey, changed);
            totalChanged.merge(changed);
        }
        log.info("dynamicModelConfig total end! totalChanged={}", totalChanged);
        return new Result(totalChanged);
    }

    public Map<String, Object> queryDynamicSettingFields(String sn) {
        if (StringUtils.isBlank(sn)) return Collections.emptyMap();
        try {
            String modelNo = deviceManualService.getModelNoBySerialNumber(sn);
            return queryByModelNo(modelNo);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "queryDynamicSettingFields error! sn={}", sn, e);
            return Collections.emptyMap(); // 避免影响settings消息发送
        }
    }

    private Map<String, Object> queryByModelNo(String modelNo) {
        if (StringUtils.isBlank(modelNo)) return Collections.emptyMap();
        List<DynamicModelConfigV2> list = dynamicModelConfigV2DAO.queryByModelNo(modelNo);
        return list.stream().collect(Collectors.toMap(it -> it.getRootKey(), it -> JSON.parse(it.getContent())));
    }

    @Async("xxlJobPool")
    public void publishDynamicSetting(DynamicModelChangedV2 changed, int batchSize) {
        Set<String> modelNos = changed.getModelNos();
        ProcessLogHelper processHelper = new ProcessLogHelper("publishDynamicSetting", 2, RoundingMode.DOWN);
        processHelper.setTotalNum(userRoleService.queryAllAdminUserRoleNum());
        Iterator<UserRoleDO> iterator = userRoleService.queryAllAdminUserRoleIterator("publishDynamicSetting", batchSize);

        Map<String,Set<String>> modelSerialNumbersMap = new HashMap<>();

        while (iterator.hasNext()) {
            UserRoleDO userRole = iterator.next();
            try {
                String modelNo = deviceManualService.getModelNoBySerialNumber(userRole.getSerialNumber());
                if (!modelNos.contains(modelNo)) {
                    processHelper.onSkip();
                    continue;
                }
                DeviceSettingsDO storeDeviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(userRole.getSerialNumber());
                if (storeDeviceSettingsDO == null) {
                    processHelper.onSkip();
                    continue;
                }
                long startTime = System.currentTimeMillis();
                String operationId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();
                ReportInfo reportInfo = ReportInfo.builder()
                        .serialNumber(userRole.getSerialNumber())
                        .startTime(startTime)
                        .userId(userRole.getUserId())
                        .operationId(operationId)
                        .build();
                CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(userRole.getSerialNumber());
                DeviceAppSettingsDO appSettings = DeviceAppSettingsDO.ParseFrom(storeDeviceSettingsDO, cloudDeviceSupport);
                DeviceSettingsDO settings = DeviceSettingsDO.ParseFrom(appSettings, storeDeviceSettingsDO);
                Map<String, Object> dynamicSettingFields = queryByModelNo(modelNo);

                //过滤掉有单独配置的设备
                for(String rootKey : filterRootSet) {
                    if (dynamicSettingFields.containsKey(rootKey)){
                        dealWithCodecProfile(modelSerialNumbersMap, userRole.getSerialNumber(), modelNo, dynamicSettingFields, rootKey);
                    }
                }

                SetRetainParamRequest setParameterRequestRetain = deviceSettingService.initSetParameterRequest(
                        reportInfo.getOperationId(), settings, storeDeviceSettingsDO, dynamicSettingFields);
                VernemqPublisher.setParameterRetain(reportInfo.getSerialNumber(), setParameterRequestRetain);
                processHelper.onProcess(Result.Success());
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "publishDynamicSetting error!", e);
                processHelper.onProcess(Result.Failure(""));
            }
        }
        processHelper.onEnd();
    }

    public void dealWithCodecProfile(Map<String, Set<String>> modelSerialNumbersMap, String serialNumber, String modelNo, Map<String, Object> dynamicSettingFields, String rootKey) {
        if (rootKey.equals(CODEC_PROFILE)){
            Set<String> codecSerialNumbers;
            if(modelSerialNumbersMap.get(modelNo) == null) {
                codecSerialNumbers= deviceCodecDAO.queryListByModelNo(modelNo);
                modelSerialNumbersMap.put(modelNo, codecSerialNumbers);
                log.info("codec single settings init, queryListByModelNo , {}, size={}", modelNo,codecSerialNumbers.size());
            }
            //使用后台设置的单独配置
            if(modelSerialNumbersMap.get(modelNo).contains(serialNumber)){
                resetSingleDeviceCodec(serialNumber, dynamicSettingFields, rootKey);
                log.info("CODEC_PROFILE setting use single device, {}, {}, {}", serialNumber, modelNo, dynamicSettingFields.get(rootKey) );
            }else{
                log.info("CODEC_PROFILE setting use model setting, {}, {}, {}", serialNumber, modelNo, dynamicSettingFields.get(rootKey) );
            }
        }
    }

    public void resetSingleDeviceCodec(String serialNumber, Map<String, Object> dynamicSettingFields, String rootKey) {
        List<SingleDeviceCodecDo> list = deviceCodecDAO.queryDeviceCodec(serialNumber);
        List<CodecFullDetailDo> codecFullDetailDoList = new ArrayList<>();
        for (SingleDeviceCodecDo item : list) {
            CodecFullDetailDo codecFullDetailDo = new CodecFullDetailDo();
            codecFullDetailDo.setCh(item.getCh());
            codecFullDetailDo.setBr(item.getBr());
            codecFullDetailDo.setCc(item.getCc());
            if(item.getRes().contains("auto")){
                codecFullDetailDo.setRes("auto");
            }else {
                codecFullDetailDo.setRes(item.getRes());
            }
            codecFullDetailDo.setMbr(item.getMbr());
            codecFullDetailDo.setXbr(item.getXbr());
            codecFullDetailDo.setTh(item.getTh());

            //默认值
            codecFullDetailDo.setRc("cbr");
            codecFullDetailDo.setFr(15);
            codecFullDetailDo.setGop(30);
            codecFullDetailDo.setMqp(9);
            codecFullDetailDo.setXqp(48);
            codecFullDetailDoList.add(codecFullDetailDo);
        }
        Map<String, List<CodecFullDetailDo>> map = new HashMap<>();
        map.put("value", codecFullDetailDoList);

        dynamicSettingFields.put(rootKey, map);
    }

    public void sendSettingIncludeCodec(String serialNo, Map<String, Object> map) {
        try {
            log.info("sendSettingIncludeCodec {} ,{}" ,serialNo, map);
            DeviceSettingsDO storeDeviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNo);

            CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNo);
            DeviceAppSettingsDO appSettings = DeviceAppSettingsDO.ParseFrom(storeDeviceSettingsDO, cloudDeviceSupport);
            DeviceSettingsDO settings = DeviceSettingsDO.ParseFrom(appSettings, storeDeviceSettingsDO);

            String modelNo = deviceManualService.getModelNoBySerialNumber(serialNo);
            Map<String, Object> dynamicSettingFields = queryByModelNo(modelNo);
            //把codecProfile替换为需要覆盖的kv
            dynamicSettingFields.put(CODEC_PROFILE, map);

            String operationId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();
            SetRetainParamRequest setParameterRequestRetain = deviceSettingService.initSetParameterRequest(
                    operationId, settings, storeDeviceSettingsDO, dynamicSettingFields);

            VernemqPublisher.setParameterRetain(serialNo, setParameterRequestRetain);

        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "publishDynamicSetting error!", e);
        }
    }
}