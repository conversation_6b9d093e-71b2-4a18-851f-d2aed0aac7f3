package com.addx.iotcamera.service.message;

import com.addx.iotcamera.bean.msg.InvitedNewCameraEntity;
import com.addx.iotcamera.bean.msg.MsgEntityBase;
import com.addx.iotcamera.bean.msg.VideoMsgEntity;
import com.addx.iotcamera.publishers.notification.Firebase.FcmV1Config;
import com.addx.iotcamera.publishers.notification.Firebase.FcmV1Entity;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.service.ReportLogService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

import static com.addx.iotcamera.helper.MsgHelper.getFcmBundId;

/**
 * https://firebase.google.com/docs/cloud-messaging/migrate-v1#use-credentials-to-mint-access-tokens
 * https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages
 */
@Slf4j
@Component
public class FcmV1Service {

    @Autowired
    private ReportLogService reportLogService;

    @Getter
    private ConcurrentHashMap<String, FcmV1Config> bundle2Config = new ConcurrentHashMap<>();

    private static final String SCOPE_FCM = "https://www.googleapis.com/auth/firebase.messaging";

    //文件名等于push_info表中的bundle_name值去掉 “.”
    private static FcmV1Config getConfigByBundleId(String bundleId) {
        try {
            String path = "app/fcm_v1/" + bundleId + ".json";

            ClassPathResource resource = new ClassPathResource(path);
            if (!resource.exists()) {
                return null;
            }

            InputStream is =resource.getInputStream();

            byte[] bytes = IOUtils.toByteArray(is);
            String str = new String(bytes, StandardCharsets.UTF_8);
            log.info("fcmV1 getConfigByBundleId success! bundleId={},str={}", bundleId, str);
            FcmV1Config config = JSON.parseObject(str, FcmV1Config.class);
            config.setCredentials(GoogleCredentials.fromStream(new ByteArrayInputStream(bytes))
                    .createScoped(Arrays.asList(SCOPE_FCM)));
            return config;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "fcmV1 getConfigByBundleId error! bundleId={}", bundleId, e);
            return null;
        }
    }

    public Boolean pushMessage(MsgEntityBase msg, PushArgs pushArgs) {
        String bundleId = getFcmBundId(pushArgs.getBundleName());
        Map<String, String> data = new LinkedHashMap<>();
        data.put("title", msg.getTitle());
        data.put("body", msg.getBody());
        data.put("msgId", msg.getMsgId() + "");
        data.put("messageId", msg.getMessageId());
        data.put("type", msg.getType() + "");
        data.put("timestamp", msg.getTime() + "");
        data.put("serialNumber", pushArgs.getSerialNumber());
        data.put("videoEvent", msg.getVideoEvent());
        data.put("traceId", msg.getTraceId());

        if (msg instanceof VideoMsgEntity) {
            VideoMsgEntity entity = (VideoMsgEntity) msg;
            data.put("checkPushIntent", String.valueOf(entity.getCheckPushIntent()));
            data.put("userId", String.valueOf(entity.getUserId()));
            data.put("tenantId", entity.getTenantId());
            data.put("node", entity.getNode());
            data.put("supportMagicPix", String.valueOf(entity.getSupportMagicPix()));
            data.put("thumbnailUrl", entity.getThumbnailUrl());
            data.put("alarmDelayTimeStamp",entity.getAlarmDelayTimeStamp() + "");
            data.put("alarmDuration",entity.getAlarmDuration() + "");

            data.put("extras", new JSONObject()
                    .fluentPut("serialNumber", pushArgs.getSerialNumber())
                    .fluentPut("deviceName", entity.getDevice().getDeviceName())
                    .fluentPut("timestamp", String.valueOf(msg.getTime()))
                    .fluentPut("videoUrl", entity.getLibrary().getVideoUrl())
                    .fluentPut("thumbnailUrl", entity.getThumbnailUrl())
                    .fluentPut("locationName", entity.getLocation().getLocationName()).toJSONString()
            );
        }else if (msg instanceof InvitedNewCameraEntity) {
            InvitedNewCameraEntity entity = (InvitedNewCameraEntity) msg;
            data.put("extras", new JSONObject()
                    .fluentPut("serialNumber", entity.getSerialNumber())
                    .fluentPut("deviceName", entity.getDeviceName())
                    .fluentPut("adminName", entity.getAdminName())
                    .fluentPut("adminEmail", entity.getAdminEmail()).toJSONString()
            );
        }

        FcmV1Entity fcmV1Entity = new FcmV1Entity().setToken(pushArgs.getMsgToken())
                .setAndroid(new FcmV1Entity.Android().setCollapse_key(msg.getVideoEvent()))
                .setData(data);
        boolean pushResult = pushMessage(bundleId, fcmV1Entity, false);
        reportLogService.reportPushMessageLog(pushArgs.getUserId(), pushResult, msg.getTraceId(), pushArgs.getMsgType(),
                pushArgs.getSerialNumber(), "");
        return pushResult;
    }

    @Setter
    private Supplier<CloseableHttpClient> httpClientFactory = HttpClients::createDefault;

    public boolean checkIfConfigExist(String bundleId){
        final FcmV1Config config = bundle2Config.computeIfAbsent(bundleId, FcmV1Service::getConfigByBundleId);
        if (config == null) return false;
        AccessToken accessToken = config.getAccessToken();
        if (accessToken == null) return false;
        return true;
    }

    public boolean pushMessage(String bundleId, FcmV1Entity entity, boolean validateOnly) {
        final FcmV1Config config = bundle2Config.computeIfAbsent(bundleId, FcmV1Service::getConfigByBundleId);
        if (config == null) return false;
        AccessToken accessToken = config.getAccessToken();
        if (accessToken == null) return false;
        final String url = "https://fcm.googleapis.com/v1/projects/" + config.getProjectId() + "/messages:send";
        final String reqBodyStr = new JSONObject().fluentPut("message", entity).fluentPut("validate_only", validateOnly).toJSONString();
        try (CloseableHttpClient client = httpClientFactory.get()) {
            HttpPost post = new HttpPost(url);
            post.addHeader("Content-Type", "application/json;charset=utf-8");
            post.addHeader("Authorization", "Bearer " + accessToken.getTokenValue());
            post.setEntity(new ByteArrayEntity(reqBodyStr.getBytes(StandardCharsets.UTF_8)));
            try (CloseableHttpResponse resp = client.execute(post)) {
                int respCode = resp.getStatusLine().getStatusCode();
                String respBodyStr = EntityUtils.toString(resp.getEntity(), StandardCharsets.UTF_8.name());
                if (respCode == HttpStatus.SC_OK) {
                    return true;
                }
            }
            return false;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "fcmV1 pushMessage http error!", e);
            return false;
        }
    }

}
