package com.addx.iotcamera.service.vip;

import com.addx.iotcamera.bean.domain.uservip.DeviceExchangecodeTierDO;
import com.addx.iotcamera.dao.vip.DeviceExchangecodeTierDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DeviceExchangecodeTierService {
    @Autowired
    private DeviceExchangecodeTierDAO deviceExchangecodeTierDAO;

    public DeviceExchangecodeTierDO queryDeviceExchangecodeTierBySerialNumber(String serialNumber) {
        return deviceExchangecodeTierDAO.selectDeviceExchangecodeTierBySerialNumber(serialNumber);
    }
}