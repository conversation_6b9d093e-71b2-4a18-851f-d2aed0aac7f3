package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Device4GSimDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.openapi.PaasUserVipLog;
import com.addx.iotcamera.bean.openapi.PaasVipLevel;
import com.addx.iotcamera.config.PaasVipConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.VipCountType;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.openapi.DeviceVipService;
import com.addx.iotcamera.service.openapi.PaasUserVipService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.TierIdUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.openapi.PaasVipLevel.FREE_TRAIL;
import static com.addx.iotcamera.bean.openapi.PaasVipLevel.NO_VIP;
import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_7_DAY;
import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_TIER_NOT_VIP;
import static com.addx.iotcamera.service.openapi.DeviceVipService.buildDefaultDeviceVipLog;

/**
 * vip服务类。
 * 根据tenantId配置的不同，分别调用：
 * 1、paas设备vip服务类：deviceVipService
 * 2、paas用户vip服务类：paasUserVipService
 * 3、自有用户vip服务类：userVipService
 */
@Slf4j
@Component
public class VipService {

    @Autowired
    private PaasVipConfig paasVipConfig;
    @Autowired
    private PaasTenantConfig paasTenantConfig;
    @Autowired
    private DeviceVipService deviceVipService;
    @Autowired
    private PaasUserVipService paasUserVipService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserVipService userVipService;
    @Autowired
    private UserTierDeviceService userTierDeviceService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AdditionalUserTierService additionalUserTierService;
    @Autowired
    @Lazy
    private UserRoleService userRoleService;
    @Autowired
    private TierService tierService;

    /**
     * 查询设备最新的vip日志
     */
    public DeviceVipLog queryLastDeviceVip(String tenantId, String sn) {
        if (paasVipConfig.tenantIsSupportDeviceVip(tenantId)) {
            return deviceVipService.queryLastDeviceVip(tenantId, sn);
        } else if (paasVipConfig.tenantIsSupportPaasUserVip(tenantId)) {
            final Result<User> adminResult = userRoleService.getAdminUserBySn(sn);
            if (adminResult.getResult() != Result.successFlag) return buildDefaultDeviceVipLog(tenantId, sn);
            final String thirdUserId = adminResult.getData().getThirdUserId();
            return paasUserVipService.queryLastPaasUserVip(tenantId, thirdUserId).toDeviceVipLog(sn);
        } else {
            return buildDefaultDeviceVipLog(tenantId, sn);
        }
    }

    /**
     * 查询用户下所有设备的vip等级
     */
    public Map<String, Integer> querySn2VipLevel(Integer userId) {
        if (userVipService.isVipUser(userId)) return Collections.EMPTY_MAP;
        User user = userService.queryUserById(userId);
        if (user == null) return Collections.EMPTY_MAP;
        if (paasVipConfig.tenantIsSupportDeviceVip(user.getTenantId())) {
            return deviceVipService.querySn2VipLevel(user);
        } else if (paasVipConfig.tenantIsSupportPaasUserVip(user.getTenantId())) {
            return paasUserVipService.querySn2VipLevel(user);
        }
        return Collections.EMPTY_MAP;
    }

    /**
     * 是否是vip用户
     */
    public boolean isVipUser(Integer userId) {
        if (userVipService.isVipUser(userId)) return true;
        User user = userService.queryUserById(userId);
        if (user == null) return false;
        if (paasVipConfig.tenantIsSupportDeviceVip(user.getTenantId())) {
            return deviceVipService.querySn2VipLevel(user).values().stream().anyMatch(it -> it > 0);
        } else if (paasVipConfig.tenantIsSupportPaasUserVip(user.getTenantId())) {
            PaasUserVipLog vipLog = paasUserVipService.queryLastPaasUserVip(user.getTenantId(), user.getThirdUserId());
            return vipLog.getVipLevel() != PaasVipLevel.NO_VIP.getCode();
        }
        return false;
    }

    /**
     * 是否是vip设备
     */
    public boolean isVipDevice(Integer userId, String serialNumber) {

        if(AppConstants.TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))){
            return true;
        }
        Integer tierId = this.tierDeviceTierId(userId,serialNumber);
        if(FREE_LICENSE_TIER_NOT_VIP.contains(tierId)){
            // 免费0元license ，非vip
            return false;
        }
        if(TierIdUtil.getTierLevelFromTierId(tierId) > 0 || FREE_LICENSE_7_DAY.equals(tierId)){
            //需要购买的套餐 + 0元License中的7天试用
            return true;
        }else{
            return this.queryPaasDeviceVip(userId, serialNumber) > 0;
        }
    }


    /**
     * 查询当前套餐Level
     * @param userId
     * @param serialNumber
     * @return
     */
    private Integer tierDeviceTierId(Integer userId, String serialNumber){
        if (userId == null || StringUtils.isBlank(serialNumber)) return 0;
        return userTierDeviceService.getDeviceCurrentTier(userId, serialNumber);
    }


    /**
     * 是否是noplan设备
     */
    public boolean isDeviceNoPlan(@NotNull Integer userId,@NotBlank String serialNumber) {
        Integer currentTier = userTierDeviceService.getDeviceCurrentTier(userId, serialNumber);
        if (currentTier != null ) {
            return false;
        }

        return this.queryPaasDeviceVip(userId,serialNumber) == 0;
    }

    private Integer queryPaasDeviceVip(Integer userId,String serialNumber){
        User user = userService.queryUserById(userId);
        if (user == null) return 0;
        if (paasVipConfig.tenantIsSupportDeviceVip(user.getTenantId())) {
            DeviceVipLog vipLog = deviceVipService.queryLastDeviceVip(user.getTenantId(), serialNumber);
            log.debug("isVipDevice 2arg deviceVip userId={},serialNumber={},vipLog={}", userId, serialNumber, JSON.toJSONString(vipLog));
            return vipLog.getVipLevel();
        } else if (paasVipConfig.tenantIsSupportPaasUserVip(user.getTenantId())) {
            PaasUserVipLog vipLog = paasUserVipService.queryLastPaasUserVip(user.getTenantId(), user.getThirdUserId());
            log.debug("isVipDevice 2arg paasUserVip userId={},serialNumber={},vipLog={}", userId, serialNumber, JSON.toJSONString(vipLog));
            return vipLog.getVipLevel();
        }
        return 0;
    }

    /**
     * 获取vip统计类型
     */
    public VipCountType queryVipCountType(String tenantId, Integer userId, String sn) {
        try {
            if (paasVipConfig.tenantIsSupportDeviceVip(tenantId)) {
                DeviceVipLog vipLog = deviceVipService.queryLastDeviceVip(tenantId, sn);
                if (vipLog != null && vipLog.getVipLevel() != NO_VIP.getCode()) {
                    if (FREE_TRAIL.getCode() == vipLog.getVipLevel()) {
                        return VipCountType.DEVICE_VIP_FREE_TRIAL;
                    } else if (PaasVipLevel.isExchange(vipLog.getVipLevel())) {
                        return VipCountType.DEVICE_VIP_EXCHANGE_CODE;
                    } else {
                        return VipCountType.DEVICE_VIP_PAID;
                    }
                }
            } else if (paasVipConfig.tenantIsSupportPaasUserVip(tenantId)) {
                final User admin = userService.queryUserById(userId);
                PaasUserVipLog vipLog = paasUserVipService.queryLastPaasUserVip(tenantId, admin.getThirdUserId());
                if (vipLog != null && vipLog.getVipLevel() != NO_VIP.getCode()) {
                    if (FREE_TRAIL.getCode() == vipLog.getVipLevel()) {
                        return VipCountType.PAAS_USER_VIP_PAID;
                    } else if (PaasVipLevel.isExchange(vipLog.getVipLevel())) {
                        return VipCountType.PAAS_USER_VIP_FREE_TRIAL;
                    } else {
                        return VipCountType.PAAS_USER_VIP_EXCHANGE_CODE;
                    }
                }
            } else {
                Integer currentTime = PhosUtils.getUTCStamp();
                List<UserVipDO> userVipList = userVipService.queryUserVipList(userId, currentTime, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
                userVipList = userVipList.stream().filter(userVipDO -> TierIdUtil.getTierLevelFromTierId(userVipDO.getTierId()) > 0).collect(Collectors.toList());
                if (userVipList.size() > 0) {
                    UserVipDO userVip = userVipList.stream().sorted(Comparator.comparingInt(it -> it.getEffectiveTime())).limit(1).findFirst().get();
                    Integer orderType = orderService.queryOrderTypeByOrderId(userVip.getOrderId());
                    if (PaymentTypeEnums.REGISTER.getCode() == orderType) {
                        return VipCountType.USER_VIP_FREE_TRIAL;
                    } else if (PaymentTypeEnums.PRODUCT_EXCHANGE_CODE.getCode() == orderType) {
                        return VipCountType.USER_VIP_EXCHANGE_CODE;
                    } else {
                        return VipCountType.USER_VIP_PAID;
                    }
                }
            }
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "queryVipCountType error! tenantId={},userId={},serialNumber={}", tenantId, userId, sn, e);
        }
        return null;
    }

    // 用户-设备 是否是鸟类vip
    public Boolean isBirdVipDevice(Integer adminId, String serialNumber) {
        final User user = userService.queryUserById(adminId);
        if (paasVipConfig.tenantIsSupportDeviceVip(user.getTenantId())) { // paas-deviceVip都带上鸟类
            if (!paasVipConfig.tenantIsSupportBirdVip(user.getTenantId())) return false;
            final DeviceVipLog vipLog = deviceVipService.queryLastDeviceVip(user.getTenantId(), serialNumber);
            final PaasVipLevel vipLevel = PaasVipLevel.codeOf(vipLog.getVipLevel());
            final List<Integer> vipLevels = paasTenantConfig.getPaasTenantInfo(user.getTenantId()).getSupportBirdVipLevels();
            return vipLevel != null && vipLevels.contains(vipLevel.getCode()); // 只有实际购买的paas-device套餐才支持鸟类
        } else if (paasVipConfig.tenantIsSupportPaasUserVip(user.getTenantId())) { // paas-userVip都不支持鸟类
            if (!paasVipConfig.tenantIsSupportBirdVip(user.getTenantId())) return false;
            final PaasUserVipLog vipLog = paasUserVipService.queryLastPaasUserVip(user.getTenantId(), user.getThirdUserId());
            final PaasVipLevel vipLevel = PaasVipLevel.codeOf(vipLog.getVipLevel());
            final List<Integer> vipLevels = paasTenantConfig.getPaasTenantInfo(user.getTenantId()).getSupportBirdVipLevels();
            return vipLevel != null && vipLevels.contains(vipLevel.getCode()); // 只有实际购买的paas-user套餐才支持鸟类
        } else {
            //去掉叠加包逻辑
            return isVipDevice(adminId, serialNumber) && tierService.isBirdDevice(serialNumber);
        }
    }


    /**
     * 判断设备是有拥有4G流量套餐vip
     * @param userId
     * @param device4G
     * @param serialNumber
     * @return
     */
    public boolean deviceHas4GDataVip(Integer userId,boolean device4G,String serialNumber){

        return device4G && this.isVipDevice(userId,serialNumber);
    }


    /**
     * 判断设备是否4G且有4G流量套餐
     * @param userId
     * @param device4GSimDO
     * @return
     */
    public boolean isDeivce4GVip(Integer userId, Device4GSimDO device4GSimDO){
        if (device4GSimDO == null){
            return false;
        }

        Integer currentTier = userTierDeviceService.getDeviceCurrentTier(userId, device4GSimDO.getSerialNumber());
        return currentTier != null && TierIdUtil.getTierLevelFromTierId(currentTier) > 0;
    }
}
