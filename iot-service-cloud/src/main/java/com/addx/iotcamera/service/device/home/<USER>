package com.addx.iotcamera.service.device.home;

import com.addx.iotcamera.bean.app.device.home.EmergencySettingRequest;
import com.addx.iotcamera.bean.app.validatorpackage.EmergencyInterface;
import com.addx.iotcamera.bean.db.alarm.DeviceHomeEmergencyContactDO;
import com.addx.iotcamera.bean.db.alarm.DeviceHomeEmergencySettingDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.response.device.home.DeviceHomeModeSettingResponse;
import com.addx.iotcamera.bean.response.home.DeviceHomeEmergencySettingResponse;
import com.addx.iotcamera.dao.alarm.DeviceHomeEmergencySettingDAO;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.device.DeviceHomeEmergencySetStepEnums;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.pay.order.TierHomeService;
import com.addx.iotcamera.service.user.ZoneCountryStateDictService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.AppConstants.NOOTLIGHT_COOLDOWN_TIME;
import static com.addx.iotcamera.constants.AppConstants.NOOTLIGHT_PHONE_LIST;
import static com.addx.iotcamera.constants.PayConstants.PRACTIVE_MODE_TIME;
import static com.addx.iotcamera.constants.UserConstants.NOONLIGHT_PRACTICE_MODE_EXPIRE;
import static com.addx.iotcamera.enums.device.DeviceHomeEmergencySetStepEnums.EMERGENCY_CONTACT;

@Service
@Slf4j
public class DeviceHomeEmergencySettingService {
    private final Validator validator;
    @Resource
    private DeviceHomeEmergencySettingDAO deviceHomeEmergencySettingDAO;

    @Resource
    @Lazy
    private TierHomeService tierHomeService;

    @Resource
    @Lazy
    private UserRoleService userRoleService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private ZoneCountryStateDictService zoneCountryStateDictService;

    public DeviceHomeEmergencySettingService(Validator validator) {
        this.validator = validator;
    }

    @Transactional
    public void saveDeviceHomeEmergencySetting(Integer userId,EmergencySettingRequest request){
        DeviceHomeEmergencySettingDO storeModel = deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencySettingDO(request.getHomeId());
        if(storeModel ==null){
            this.validateStep(request, EmergencyInterface.StepAddress.class);
            DeviceHomeEmergencySettingDO model = DeviceHomeEmergencySettingDO.builder()
                    .homeId(request.getHomeId())
                    .setupStep(DeviceHomeEmergencySetStepEnums.ADDRESS.getCode())
                    .countryNo(request.getAddress().getCountryNo())
                    .line1(request.getAddress().getLine1())
                    .line2(request.getAddress().getLine2())
                    .city(request.getAddress().getCity())
                    .state(request.getAddress().getState())
                    .zip(request.getAddress().getZip())
                    .cooldownTime(10)
                    .build();
            deviceHomeEmergencySettingDAO.insertDeviceHomeEmergencySetting(model);
        }else{
            this.updateEmergencySetting(userId,request,storeModel);
        }
    }

    /**
     * 更新emergency setting
     * @param request
     * @param storeModel
     */
    private void updateEmergencySetting(Integer userId,EmergencySettingRequest request,DeviceHomeEmergencySettingDO storeModel){
        DeviceHomeEmergencySetStepEnums setStepEnums = DeviceHomeEmergencySetStepEnums.queryByCode(request.getSetupStep());
        switch (setStepEnums){
            case ADDRESS:
                this.validateStep(request, EmergencyInterface.StepBase.class);
                storeModel.setCountryNo(request.getAddress().getCountryNo());
                storeModel.setLine1(request.getAddress().getLine1());
                storeModel.setLine2(request.getAddress().getLine2());
                storeModel.setCity(request.getAddress().getCity());
                storeModel.setState(request.getAddress().getState());
                storeModel.setZip(request.getAddress().getZip());
                break;
            case CONTACT_DETAIL:
                this.validateStep(request,EmergencyInterface.StepMainContact.class);

                storeModel.setFirstName(request.getPrimaryContact().getFirstName());
                storeModel.setLastName(request.getPrimaryContact().getLastName());
                storeModel.setPhone(request.getPrimaryContact().getPhone());
                storeModel.setPhoneCode(request.getPrimaryContact().getPhoneCode());
                storeModel.setCountryCode(request.getPrimaryContact().getCountryCode());
                break;
            case PIN_CODE:
                this.validateStep(request,EmergencyInterface.StepPinCode.class);

                storeModel.setPin(request.getPin());
                break;
            case INFORMATION:
                storeModel.setInformation(request.getInstructions());
                break;
            case EMERGENCY_CONTACT:
                // 联系人
                this.saveEmergencyContact(request.getHomeId(),userId,setStepEnums,request.getContactList());
                break;
            case CRITICAL_ALERT:
                storeModel.setCriticalAlerts(Optional.ofNullable(request.getCriticalAlerts()).orElse(false).equals(false) ? 0 : 1);
                break;
            case ALARM_PERMIT:
                if(request.getAlarmPermit() != null){
                    storeModel.setAlarmPermitNumber(request.getAlarmPermit().getPermitNumber());
                    storeModel.setAlarmPermitExpireDate(request.getAlarmPermit().getExpirationDate());
                }
                break;
            case PRATICE_MODE:
                if(request.getPracticeMode() != null){
                    storeModel.setPracticeMode(Optional.ofNullable(request.getPracticeMode().getEnable()).orElse(false) ? 1 : 0);
                    storeModel.setPracticeModeEndTime(storeModel.getPracticeMode().equals(1) ? ((int)Instant.now().getEpochSecond() + PRACTIVE_MODE_TIME) : 0);
                }
                break;
            default:
                break;
        }

        if(request.getCooldownTime() != null){
            storeModel.setCooldownTime(request.getCooldownTime());
        }
        if(request.getSetupStep() > storeModel.getSetupStep()){
            storeModel.setSetupStep(request.getSetupStep());
        }
        if(request.getComplete()!= null){
            storeModel.setComplete(request.getComplete() ? 1 : 0);
        }
        deviceHomeEmergencySettingDAO.updateDeviceHomeEmergencySettingDO(storeModel);

        // clean cache
        Integer tierId = tierHomeService.queryTierByHomeId(userId,request.getHomeId());
        tierHomeService.cleanHomeEmergencySetCache(userId,tierId);
    }

    private void saveEmergencyContact(Long homeId,Integer userId,DeviceHomeEmergencySetStepEnums setStepEnums ,List<EmergencySettingRequest.DeviceHomeEmergencyContactRequest> contactList){
        if(setStepEnums.getCode() != EMERGENCY_CONTACT.getCode()){
            return;
        }

        if(CollectionUtils.isEmpty(contactList)){
            return;
        }


        List<String> nameList = contactList.stream()
                .map(EmergencySettingRequest.DeviceHomeEmergencyContactRequest::getName)
                .distinct()
                .collect(Collectors.toList());
        List<String> phoneList = contactList.stream()
                .map(EmergencySettingRequest.DeviceHomeEmergencyContactRequest::getPhone)
                .distinct()
                .collect(Collectors.toList());

        if(nameList.size() != contactList.size() ||
                phoneList.size() != contactList.size()){
            log.debug("联系人参数有重复");
            throw new BaseException(ResultCollection.INVALID_PARAMS);
        }

        //校验更新的时候传值是否是Home 下已存在的记录
        Map<Long,DeviceHomeEmergencyContactDO> existContactSet = deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencyContactDOList(homeId)
                .stream()
                .collect(Collectors.toMap(DeviceHomeEmergencyContactDO::getId, Function.identity()));
        List<EmergencySettingRequest.DeviceHomeEmergencyContactRequest> errorList = contactList.stream()
                .filter(contact -> contact.getId()!=null && !existContactSet.containsKey(contact.getId()))
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(errorList)){
            log.debug("联系人 id 参数不符合条件");
            throw new BaseException(ResultCollection.INVALID_PARAMS);
        }

        // 删除不存在的紧急联系人
        this.deleteContact(contactList,homeId);

        // 更新联系人记录
        for(EmergencySettingRequest.DeviceHomeEmergencyContactRequest contactRequest : contactList){
            if(contactRequest.getId() == null){
                DeviceHomeEmergencyContactDO contactDO = DeviceHomeEmergencyContactDO.builder()
                        .homeId(homeId)
                        .userId(userId)
                        .name(contactRequest.getName())
                        .phone(contactRequest.getPhone())
                        .phoneCode(contactRequest.getPhoneCode())
                        .countryCode(contactRequest.getCountryCode())
                        .build();
                deviceHomeEmergencySettingDAO.insertContact(contactDO);
            }else {
                DeviceHomeEmergencyContactDO contactDO = existContactSet.get(contactRequest.getId());
                contactDO.setName(contactRequest.getName());
                contactDO.setPhone(contactRequest.getPhone());
                contactDO.setPhoneCode(contactRequest.getPhoneCode());
                contactDO.setCountryCode(contactRequest.getCountryCode());
                deviceHomeEmergencySettingDAO.updateContact(contactDO);
            }
        }
    }


    private void deleteContact(List<EmergencySettingRequest.DeviceHomeEmergencyContactRequest> contactList,Long homeId){
        //获取当前home_id下所有已存在的联系人记录
        Set<Long> existingContactIds = deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencyContactDOList(homeId).stream()
                .map(DeviceHomeEmergencyContactDO::getId)
                .collect(Collectors.toSet());
        Set<Long> newContactIds = contactList.stream().filter(contact -> contact.getId() != null)
                .map(EmergencySettingRequest.DeviceHomeEmergencyContactRequest::getId)
                .collect(Collectors.toSet());

        for(Long existContactId : existingContactIds){
            if(!newContactIds.contains(existContactId)){
                deviceHomeEmergencySettingDAO.deleteDeviceHomeEmergencyContactDO(existContactId);
            }
        }
    }

    private void validateStep(EmergencySettingRequest model, Class<?> validationGroup) {
        if (validationGroup == null) return;  // 某些步骤可能不需要校验

        Set<ConstraintViolation<EmergencySettingRequest>> violations = validator.validate(model, validationGroup);
        if (!violations.isEmpty()) {
            String errorMsg = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .reduce((msg1, msg2) -> msg1 + ", " + msg2)
                    .orElse("Invalid request parameters");
            throw new org.addx.iot.common.exception.BaseException(ResultCollection.INVALID_PARAMS,errorMsg);
        }
    }

    /**
     * setting 是否已经完成
     * @param homeIds
     * @return
     */
    public List<DeviceHomeEmergencySettingDO> emergencySettingComplete(Set<Long> homeIds){
        if(CollectionUtils.isEmpty(homeIds)){
            return Lists.newArrayList();
        }
        return deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencySettingDOListBatch(homeIds);
    }



    /**
     * contact 是否已经完成
     * @param homeIds
     * @return
     */
    public List<DeviceHomeEmergencyContactDO> queryEmergencyContactDOListBatch(List<Long> homeIds){
        if(CollectionUtils.isEmpty(homeIds)){
            return Lists.newArrayList();
        }
        return deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencyContactDOListBatch(homeIds);
    }

    /**
     * 查询设备所属Home emergency setting 情况
     * @param homeId
     * @param language
     * @return
     */
    public DeviceHomeEmergencySettingResponse queryDeviceHomeEmergencySetting(Long homeId,String language){
        DeviceHomeEmergencySettingResponse response = new DeviceHomeEmergencySettingResponse();
        response.setApp(null);
        response.setNoonlightPhone(NOOTLIGHT_PHONE_LIST);
        DeviceHomeEmergencySettingDO deviceHomeEmergencySettingDO = deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencySettingDO(homeId);
        if(deviceHomeEmergencySettingDO == null){
            response.setHomeId(homeId);
            response.setSetupStep(0);
            response.setComplete(false);
            return response;
        }
        List<DeviceHomeEmergencyContactDO> deviceHomeEmergencyContactDOS = deviceHomeEmergencySettingDAO.queryDeviceHomeEmergencyContactDOList(homeId);
        response.setHomeId(homeId);
        this.initDeviceHomeEmergencySettingResponse(deviceHomeEmergencySettingDO,deviceHomeEmergencyContactDOS,response,language);

        return response;
    }

    public void initDeviceHomeEmergencySettingResponse(
            DeviceHomeEmergencySettingDO deviceHomeEmergencySettingDO,
            List<DeviceHomeEmergencyContactDO> deviceHomeEmergencyContactDOS,
            DeviceHomeEmergencySettingResponse response,
            String language
    ){
        if(deviceHomeEmergencySettingDO == null){
            response.setSetupStep(0);
            return;
        }
        response.setHomeId(deviceHomeEmergencySettingDO.getHomeId());
        response.setSetupStep(deviceHomeEmergencySettingDO.getSetupStep());
        response.setComplete(deviceHomeEmergencySettingDO.getComplete().equals(1));

        // alarm 间隔
        DeviceHomeEmergencySettingResponse.DeviceHomeEmergencyCooldownTimeRequest cooldownTimeRequest = new DeviceHomeEmergencySettingResponse.DeviceHomeEmergencyCooldownTimeRequest();
        cooldownTimeRequest.setValue(deviceHomeEmergencySettingDO.getCooldownTime());
        cooldownTimeRequest.setOptions(NOOTLIGHT_COOLDOWN_TIME);
        response.setCooldownTimeOptions(cooldownTimeRequest);
        response.setCooldownTime(deviceHomeEmergencySettingDO.getCooldownTime());
        for (DeviceHomeEmergencySetStepEnums step : DeviceHomeEmergencySetStepEnums.values()){
            if(deviceHomeEmergencySettingDO.getSetupStep() >= step.getCode()){
                switch (step){
                    case ADDRESS:
                        EmergencySettingRequest.DeviceHomeEmergencyAddressRequest addressRequest = new EmergencySettingRequest.DeviceHomeEmergencyAddressRequest();
                        addressRequest.setCountryNo(deviceHomeEmergencySettingDO.getCountryNo());
                        addressRequest.setLine1(deviceHomeEmergencySettingDO.getLine1());
                        addressRequest.setLine2(deviceHomeEmergencySettingDO.getLine2());
                        addressRequest.setCity(deviceHomeEmergencySettingDO.getCity());
                        addressRequest.setState(deviceHomeEmergencySettingDO.getState());
                        addressRequest.setZip(deviceHomeEmergencySettingDO.getZip());
                        response.setAddress(addressRequest);
                        break;
                    case CONTACT_DETAIL:
                        EmergencySettingRequest.DeviceHomeEmergencymainContactRequest contactRequest = new EmergencySettingRequest.DeviceHomeEmergencymainContactRequest();
                        contactRequest.setFirstName(deviceHomeEmergencySettingDO.getFirstName());
                        contactRequest.setLastName(deviceHomeEmergencySettingDO.getLastName());
                        contactRequest.setPhone(deviceHomeEmergencySettingDO.getPhone());
                        contactRequest.setPhoneCode(deviceHomeEmergencySettingDO.getPhoneCode());
                        contactRequest.setCountryCode(deviceHomeEmergencySettingDO.getCountryCode());
                        response.setPrimaryContact(contactRequest);
                        break;
                    case PIN_CODE:
                        response.setPin(deviceHomeEmergencySettingDO.getPin());
                        break;
                    case INFORMATION:
                        response.setInstructions(deviceHomeEmergencySettingDO.getInformation());
                        break;
                    case EMERGENCY_CONTACT:
                        List<EmergencySettingRequest.DeviceHomeEmergencyContactRequest> contactList = Lists.newArrayList();
                        if(!CollectionUtils.isEmpty(deviceHomeEmergencyContactDOS)){
                            for(DeviceHomeEmergencyContactDO contactDO : deviceHomeEmergencyContactDOS){
                                EmergencySettingRequest.DeviceHomeEmergencyContactRequest emergencyContactRequest = new EmergencySettingRequest.DeviceHomeEmergencyContactRequest();
                                emergencyContactRequest.setId(contactDO.getId());
                                emergencyContactRequest.setName(contactDO.getName());
                                emergencyContactRequest.setPhone(contactDO.getPhone());
                                emergencyContactRequest.setPhoneCode(contactDO.getPhoneCode());
                                emergencyContactRequest.setCountryCode(contactDO.getCountryCode());
                                contactList.add(emergencyContactRequest);
                            }
                        }
                        response.setContactList(contactList);
                        break;
                    case CRITICAL_ALERT:
                        response.setCriticalAlerts(Optional.ofNullable(deviceHomeEmergencySettingDO.getCriticalAlerts()).orElse(0).equals(1));
                        break;
                    case ALARM_PERMIT:
                        // 出警许可
                        EmergencySettingRequest.DeviceHomeEmergencyAlarmPermitRequest permitRequest = new EmergencySettingRequest.DeviceHomeEmergencyAlarmPermitRequest();
                        permitRequest.setPermitNumber(deviceHomeEmergencySettingDO.getAlarmPermitNumber());
                        permitRequest.setExpirationDate(deviceHomeEmergencySettingDO.getAlarmPermitExpireDate());
                        response.setAlarmPermit(permitRequest);
                    case PRATICE_MODE:
                        // 体验模式
                        EmergencySettingRequest.DeviceHomeEmergencyPracticeModeRequest practiceModeRequest = new EmergencySettingRequest.DeviceHomeEmergencyPracticeModeRequest();
                        practiceModeRequest.setEnable(deviceHomeEmergencySettingDO.getPracticeMode().equals(1));
                        practiceModeRequest.setEndTime((int) Instant.now().getEpochSecond() + NOONLIGHT_PRACTICE_MODE_EXPIRE);
                        response.setPracticeMode(practiceModeRequest);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 初始化home setting 信息
     * @param homeList
     * @return
     */
    public Map<Long,DeviceHomeEmergencySettingResponse> initUserHomeEmergencySettingResponse(List<Long> homeList,String language){
        if(CollectionUtils.isEmpty(homeList)){
            return Maps.newHashMap();
        }
        // 批量获取home setting
        Map<Long,DeviceHomeEmergencySettingDO> deviceHomeEmergencySettingDOList = this.emergencySettingComplete(new HashSet<>(homeList))
                .stream()
                .collect(Collectors.toMap(DeviceHomeEmergencySettingDO::getHomeId, Function.identity()));

        // 批量获取紧急联系人
        Map<Long,List<DeviceHomeEmergencyContactDO>> contactMap = this.queryEmergencyContactDOListBatch(homeList)
                .stream()
                .collect(Collectors.groupingBy(DeviceHomeEmergencyContactDO::getHomeId));

        return homeList.stream().map(homeId -> {
            if(!deviceHomeEmergencySettingDOList.containsKey(homeId)){
                return null;
            }
            DeviceHomeEmergencySettingDO deviceHomeEmergencySettingDO = deviceHomeEmergencySettingDOList.get(homeId);
            List<DeviceHomeEmergencyContactDO> contactDOList = contactMap.getOrDefault(homeId,Lists.newArrayList());
            DeviceHomeEmergencySettingResponse res = new DeviceHomeEmergencySettingResponse();
            res.setHomeId(homeId);
            this.initDeviceHomeEmergencySettingResponse(deviceHomeEmergencySettingDO,contactDOList,res,language);
            return res;
        })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(DeviceHomeEmergencySettingResponse::getHomeId,Function.identity()));
    }

    /**
     * 设备列表
     * @param userId
     * @param homeId
     * @return
     */
    public List<DeviceHomeModeSettingResponse.DeviceHomeModeDevice> queryHomeDeviceList(Integer userId,Long homeId){
        return userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode())
                .stream()
                .map(userRoleDO -> deviceInfoService.initHomeDeviceInfo(userId,homeId,userRoleDO.getSerialNumber(),true))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
