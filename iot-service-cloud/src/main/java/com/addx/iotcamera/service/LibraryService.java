package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.bird.BirdName;
import com.addx.iotcamera.bean.db.*;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.library.LibraryCountDay;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.interservices.LibraryDonateParam;
import com.addx.iotcamera.bean.keyshot.KeyShot;
import com.addx.iotcamera.bean.response.library.LibraryEventView;
import com.addx.iotcamera.bean.response.library.QueryLibraryResult;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.video.VideoAndEventCount;
import com.addx.iotcamera.config.LibraryDonateConfig;
import com.addx.iotcamera.config.S3;
import com.addx.iotcamera.config.device.ModelAiEventConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.StaleDataTransferDAO;
import com.addx.iotcamera.dao.library.LibraryDonateDAO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.VideoLibraryEventEnums;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.shardingjdbc.dao.library.IShardingVideoSliceDAO;
import com.addx.iotcamera.util.*;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import io.reactivex.Observable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.VideoTagEnumUtil;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;
import org.addx.iot.domain.extension.video.entity.MultiResolutionInfo;
import org.addx.iot.domain.extension.video.entity.SliceDetailVO;
import org.addx.iot.domain.extension.video.entity.SubcategoryInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.addx.iotcamera.constants.VideoConstants.SECONDS_PER_DAY;
import static com.addx.iotcamera.constants.VideoConstants.THREAD_POOL_SIZE;
import static org.addx.iot.common.enums.ResultCollection.NO_LIBRARY_ACCESS;

@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Service
public class LibraryService {
    private static Logger LOGGER = LoggerFactory.getLogger(LibraryService.class);
    private static int limitNum = 100;

    public final static String AI_EVENT_INFO_REDIS_KEY = "ai_event_info::{userId}::{traceId}";

    @Autowired
    private IShardingLibraryDAO shardingLibraryDAO;
    @Autowired
    private IShardingVideoSliceDAO shardingVideoSliceDAO;

    @Autowired
    private LibraryStatusService libraryStatusService;

    @Autowired
    private StaleDataTransferService staleDataTransferService;

    @Autowired
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private IUserDAO iUserDAO;

    @Autowired
    private IShareDAO shareDAO;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private LibraryDonateConfig libraryDonateConfig;

    @Autowired
    private LibraryDonateDAO libraryDonateDAO;

    @Setter
    @Autowired
    private S3Service s3Service;

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private VipService vipService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private ActivityZoneService activityZoneService;

    @Autowired
    private ModelAiEventConfig modelAiEventConfig;

    @Autowired
    private AIService aiService;

    @Autowired
    private DeviceAuthService deviceAuthService;

    @Autowired
    private DeviceConfigService deviceConfigService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private DeviceModelService deviceModelService;

    @Autowired
    private DeviceModelEventService deviceModelEventService;
    @Autowired
    private DeviceManualService deviceManualService;

    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Autowired(required = false)
    private MqSender mqSender;

    @Autowired
    private S3 s3;

    @Value("${spring.kafka.topics.video-library}")
    private String videoLibraryTopic;

    private Boolean isRealDeleteSlice;
    @Autowired
    private TierService tierService;

    @Autowired
    private BirdLoversService birdLoversService;

    @Value("${isRealDeleteSlice:false}")
    public void setIsRealDeleteSlice(Boolean isRealDeleteSlice) {
        log.info("LibraryService isRealDeleteSlice={}", isRealDeleteSlice);
        this.isRealDeleteSlice = isRealDeleteSlice;
    }

    public boolean insertLibrary(InsertLibraryRequest request, UserRoleDO userRoleDO, DeviceDO deviceDO) {
//        int videoLibraryId = ShardingJdbcUtil.generateVideoLibraryId();
//        request.setId(videoLibraryId == -1 ? null : videoLibraryId);
        request.setId(0);
        //设置录像所属adminId
        if (userRoleDO != null) {
            request.setAdminId(userRoleDO.getAdminId());
        }

        if (deviceDO != null) {
            request.setDeviceName(deviceDO.getDeviceName());
        }

        Integer adminId = ObjectUtils.defaultIfNull(request.getAdminId(), userRoleDO != null ? userRoleDO.getAdminId() : null);
        String tenantId = userService.queryTenantIdById(adminId);

        // 查询设备所属tierGroup信息
        Tuple2<Integer, Integer> deviceUseCurrentTierTuple = userTierDeviceService.getDeviceCurrentTierWithTierGroupId(adminId, request.getSerialNumber());
        Integer deviceUseCurrentTierId = deviceUseCurrentTierTuple != null ? deviceUseCurrentTierTuple.v0() : null;
        Integer deviceUseCurrentTierGroupId = deviceUseCurrentTierTuple != null ? deviceUseCurrentTierTuple.v1() : null;
        request.setTierId(deviceUseCurrentTierId);
        request.setTierGroupId(deviceUseCurrentTierGroupId);

        List<Integer> userIdList = new LinkedList<>(userRoleService.findAllUsersForDevice(request.getSerialNumber()));
        userIdList.remove(request.getAdminId());
        request.setShareUserIds(StringUtils.join(userIdList, ","));

        Tuple2<Integer, Long> storageDaysTuple = deviceConfigService.getVideoRollingDaysOrNoPlan(tenantId, request.getAdminId(), request.getSerialNumber());
        request.setTtlTimestamp(Long.valueOf(System.currentTimeMillis() / 1000 + storageDaysTuple.v0() * 24 * 60 * 60).intValue());
        //shardingLibraryDAO.insertLibrary(request);
        shardingLibraryDAO.insertLibrary(request);

        // 获取写入的用户id列表，admin的优先写入
        userIdList.add(0, request.getAdminId());
        String videoEventKey = aiService.getVideoEventKey(request.getSerialNumber(), request.getTraceId());
/*
        libraryStatusService.insertLiraryStatus(userIdList, request.getTraceId(), request.getSerialNumber(), request.getTimestamp(),
                StringUtils.isEmpty(videoEventKey) ? request.getId().toString() : videoEventKey, request.getTtlTimestamp(), deviceUseCurrentTierId, deviceUseCurrentTierGroupId);
*/
        libraryStatusService.insertLiraryStatus(request.getAdminId(), userIdList, request.getTraceId(), request.getSerialNumber(), request.getTimestamp(),
                StringUtils.isEmpty(videoEventKey) ? request.getId().toString() : videoEventKey, request.getTtlTimestamp(), deviceUseCurrentTierId, deviceUseCurrentTierGroupId, request.getExtension());

        // 发送添加视频事件消息
        if (mqSender != null && request.getType() != null && request.getType().intValue() == 0) {
            Map<String, Object> insertLibraryInfoMap = new HashMap<>();
            insertLibraryInfoMap.put("type", VideoLibraryEventEnums.INSERT);
            insertLibraryInfoMap.put("insertLibraryRequest", request);
            mqSender.send(videoLibraryTopic, String.valueOf(System.currentTimeMillis()), insertLibraryInfoMap);
        }

        // 更新添加视频metric
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "videoLibrary", "insert").inc(1));
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "libraryStatus", "insert").inc(userIdList.size()));
        return (request.getId() != null);
    }

    public DeviceLibraryViewDO selectLibraryViewByTraceId(Integer userId, String traceId) {
        Set<Integer> userIds = shardingLibraryStatusDAO.selectAdminsByUserId(userId);
        DeviceLibraryViewDO deviceLibraryViewDO = shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(userIds, traceId);
        return deviceLibraryViewDO;
    }

    @SentinelResource("deletelibrary")
    public RemoveLibraryResponse deleteLibraries(Integer userId, RemoveLibraryRequest request) {
        RemoveLibraryResponse response = new RemoveLibraryResponse();

        if (!CollectionUtils.isEmpty(request.getTraceIdList())) {
            return deleteLibrariesByTraceId(userId, request);
        }

        Set<Integer> deleteLibraryIdSet = CollectionUtils.isEmpty(request.getLibraryList()) ? Collections.emptySet() : new HashSet<>(request.getLibraryList());
        for (Integer libraryId : deleteLibraryIdSet) {
            UserLibraryViewDO remove = new UserLibraryViewDO();

            // AdminId 用来校验权限， userId 用来保证查询结果唯一
            remove.setAdminId(userId);
            remove.setUserId(userId);
            remove.setId(libraryId);

            // 判断此条录像是否存在且用户有权限删除
            UserLibraryViewDO stored = selectLibraryAsAdmin(remove);
            if (stored == null) {
                response.getFailed().add(libraryId);
                continue;
            }

            // 删除录像
            Integer flag = 0;

            AtomicInteger deleteShareLibrariesCnt = new AtomicInteger(0);
            if (StringUtils.isNotEmpty(stored.getShareUserIds())) {
                Arrays.asList(stored.getShareUserIds().split(",")).forEach(shareUserIdStr -> {
                    Integer deleteCount = libraryStatusService.deleteLibraryStatus(Integer.valueOf(shareUserIdStr), Collections.singletonList(libraryId), Collections.singletonList(stored.getTraceId()));
                    if (deleteCount > 0) {
                        deleteShareLibrariesCnt.incrementAndGet();
                    }
                });
            }

            libraryStatusService.deleteLibraryStatus(userId, Collections.singletonList(libraryId), Collections.singletonList(stored.getTraceId()));
            Integer deleteCount = shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(userId, stored.getTraceId());
            final Integer deleteSliceNum = shardingVideoSliceDAO.deleteSliceByAdminUserIdAndTraceId(userId, stored.getTraceId());
            if (ObjectUtils.notEqual(deleteCount, Integer.valueOf(0))) {
                flag = 1;
                // 发送添加视频事件消息
                if (mqSender != null) {
                    // 删除视频事件消息
                    JSONObject deleteLibraryObj = new JSONObject();
                    deleteLibraryObj.put("type", VideoLibraryEventEnums.DELETE);
                    deleteLibraryObj.put("userId", userId);
                    deleteLibraryObj.put("serialNumber", stored.getSerialNumber());
                    deleteLibraryObj.put("traceId", stored.getTraceId());
                    deleteLibraryObj.put("fileSize", stored.getFileSize());
                    mqSender.send(videoLibraryTopic, String.valueOf(System.currentTimeMillis()), deleteLibraryObj);
                }

                // 更新删除视频metric
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "library", "delete").inc(1));
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "libraryStatus", "delete").inc(deleteShareLibrariesCnt.incrementAndGet()));
            }

            if (flag == 0) { // 删除失败
                response.getFailed().add(libraryId);
            } else { // 删除成功
                response.setRemoved(response.getRemoved() + 1);
            }
        }
        return response;
    }

    public RemoveLibraryResponse deleteLibrariesByTraceId(Integer userId, RemoveLibraryRequest request) {
        RemoveLibraryResponse response = new RemoveLibraryResponse();

        Set<String> deleteLibraryTraceIdSet = CollectionUtils.isEmpty(request.getTraceIdList()) ? Collections.emptySet() : new HashSet<>(request.getTraceIdList());
        for (String traceId : deleteLibraryTraceIdSet) {
            UserLibraryViewDO remove = new UserLibraryViewDO();

            // AdminId 用来校验权限， userId 用来保证查询结果唯一
            remove.setAdminId(userId);
            remove.setUserId(userId);
            remove.setTraceId(traceId);

            // 判断此条录像是否存在且用户有权限删除
            UserLibraryViewDO stored = selectLibraryAsAdmin(remove);
            if (stored == null) {
                response.getFailedTraceIdList().add(traceId);
                response.getSkipTraceIdList().add(traceId);
                continue;
            }
            // 删除录像
            Integer res = 0;

            AtomicInteger deleteShareLibrariesCount = new AtomicInteger(0);
            if (StringUtils.isNotEmpty(stored.getShareUserIds())) {
                Arrays.asList(stored.getShareUserIds().split(",")).forEach(shareUserIdStr -> {
                    Integer deleteCount = libraryStatusService.deleteLibraryStatus(Integer.valueOf(shareUserIdStr), null, Collections.singletonList(stored.getTraceId()));
                    if (deleteCount > 0) {
                        deleteShareLibrariesCount.incrementAndGet();
                    }
                });
            }

            libraryStatusService.deleteLibraryStatus(userId, null, Collections.singletonList(stored.getTraceId()));
            Integer deleteCount = shardingLibraryDAO.deleteLibraryByUserIdAndTraceId(userId, stored.getTraceId());
            final Integer deleteSliceNum = shardingVideoSliceDAO.deleteSliceByAdminUserIdAndTraceId(userId, stored.getTraceId());
            if (ObjectUtils.notEqual(deleteCount, Integer.valueOf(0))) {
                res = 1;

                // 发送添加视频事件消息
                if (mqSender != null) {
                    // 删除视频事件消息
                    JSONObject deleteLibraryObj = new JSONObject();
                    deleteLibraryObj.put("type", VideoLibraryEventEnums.DELETE);
                    deleteLibraryObj.put("userId", userId);
                    deleteLibraryObj.put("serialNumber", stored.getSerialNumber());
                    deleteLibraryObj.put("traceId", stored.getTraceId());
                    deleteLibraryObj.put("fileSize", stored.getFileSize());
                    mqSender.send(videoLibraryTopic, String.valueOf(System.currentTimeMillis()), deleteLibraryObj);
                }

                // 更新删除视频metric
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "library", "delete").inc(1));
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getVideoLibraryInsertOrDeleteCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "libraryStatus", "delete").inc(deleteShareLibrariesCount.incrementAndGet()));
            }

            if (Objects.equals(deleteCount, 0) && (res == 0)) { // 删除失败
                response.getFailedTraceIdList().add(traceId);
            } else { // 删除成功
                response.setRemoved(response.getRemoved() + 1);
            }
        }
        return response;
    }

    /**
     * 获取视频admin是否当前用户
     *
     * @param remove
     * @return
     */
    private UserLibraryViewDO selectLibraryAsAdmin(UserLibraryViewDO remove) {
        String traceId = StringUtils.defaultString(remove.getTraceId(), ShardingJdbcUtil.queryTraceIdByUserIdAndLibraryId(remove.getUserId(), remove.getId()));
        UserLibraryViewDO stored = StringUtils.isEmpty(traceId) ? null : shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(remove.getUserId(), traceId);
        if (stored == null) {
            return null;
        }
        if (!stored.getAdminId().equals(remove.getUserId())) {
            return null;
        }
        return stored;
    }

    public boolean updateLibraryEvent(LibraryTb libraryTb, Integer startTimestamp) {
        Integer updateResult = shardingLibraryDAO.updateLibraryInfoByUserIdAndTraceId(libraryTb, startTimestamp);
        boolean res = updateResult != null && updateResult > 0;
        // 目前算法结果可能会在视频落库之前发生， 如果视频还没落库， 所欲需要缓存结果到redis
        if (!res && libraryTb != null) {
            redisService.set(AI_EVENT_INFO_REDIS_KEY.replace("{traceId}", libraryTb.getTraceId()).replace("{userId}", libraryTb.getUserId().toString())
                    , libraryTb.getEventInfo(), 60 * 20);
        }
        return res;
    }

    // 视频过期或删除时，删除切片视频
    public int deleteSliceVideoUrl(Integer adminUserId, List<String> traceIds) {
        List<String> videoUrls = videoService.queryVideoUrlByAdminUserIdAndTraceIds(adminUserId, traceIds);
        videoUrls.removeIf(url -> {
            try {
                // https://addx-test.s3.cn-north-1.amazonaws.com.cn
                return s3.getLookBackBuckets().contains(URI.create(url).getHost().split("\\.")[0]);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "deleteSliceVideoUrl 根据lookBackBuckets过滤切片发生异常!", e);
                return false;
            }
        });
        if (CollectionUtils.isEmpty(videoUrls)) return 0;
        LOGGER.info("deleteSliceVideoUrl traceIds.size={},videoUrls.size={}", traceIds.size(), videoUrls.size());
        return s3Service.deleteObject(videoUrls);
    }

    /**
     * 批量获取用户视频列表
     *
     * @param request
     * @return
     */
    public List<UserLibraryViewDO> selectLibrary(LibraryRequest request) {
        if (StringUtils.isNotEmpty(request.getVideoEventKey()) && ObjectUtils.compare(request.getVideoEvent(), 0L) <= 0) {
            request.setVideoEvent(Long.valueOf(request.getVideoEventKey()));
        }

        if (ObjectUtils.compare(request.getVideoEvent(), 0L) > 0
                && ObjectUtils.compare(request.getStartTimestamp(), 0L) <= 0) {
            request.setStartTimestamp((request.getVideoEvent() - 3600000) / 1000);
        }
        if (ObjectUtils.compare(request.getVideoEvent(), 0L) > 0
                && ObjectUtils.compare(request.getEndTimestamp(), 0L) <= 0) {
            request.setEndTimestamp((request.getVideoEvent() + 3600000) / 1000);
        }

        long t1 = System.currentTimeMillis();
        List<LibraryStatusTb> libraryStatusList = libraryStatusService.queryLibraryStatusList(request);
        long costTime1 = System.currentTimeMillis() - t1;
        LOGGER.info("selectTimeLineLibrary queryLibraryStatusList! userId={},list={},costTime={}", request.getUserId(), libraryStatusList.size(), costTime1);
        PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
            it.labels(PrometheusMetricsUtil.getHostName(), "queryLibraryStatusList", "0").observe(costTime1);
        }));
        if (CollectionUtils.isEmpty(libraryStatusList)) {
            return new ArrayList<>();
        }

        long t2 = System.currentTimeMillis();
        Map<String, LibraryStatusTb> traceIdToStatus = libraryStatusList.stream().collect(Collectors.toMap(LibraryStatusTb::getTraceId, LibraryStatusTb -> LibraryStatusTb));
        Map<String, Set<String>> serialNumberMap = libraryStatusList.stream().map(LibraryStatusTb::getSerialNumber).collect(Collectors.toSet()).stream()
                .collect(Collectors.toMap(serialNumber -> serialNumber, serialNumber -> deviceModelEventService.queryRowDeviceModelEvent(deviceManualService.getModelNoBySerialNumber(serialNumber))));
        long costTime2 = System.currentTimeMillis() - t2;
        LOGGER.info("selectTimeLineLibrary queryRowDeviceModelEvent! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime2);
        PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
            it.labels(PrometheusMetricsUtil.getHostName(), "queryRowDeviceModelEvent", "0").observe(costTime2);
        }));

        List<UserLibraryViewDO> listUserLibraryViewDO = Collections.emptyList();
        List<UserLibraryViewDO> subLibraryViewList = Collections.emptyList();
        List<UserLibraryViewDO> multiResolutionLibraryViewList = Collections.emptyList();
        Set<String> traceIdSet = libraryStatusList.stream().map(LibraryStatusTb::getTraceId).filter(obj -> obj != null).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(traceIdSet)) {
            long t3 = System.currentTimeMillis();
            Set<Integer> adminIds = libraryStatusList.stream().map(LibraryStatusTb::getAdminId).collect(Collectors.toSet());
            listUserLibraryViewDO = shardingLibraryDAO.selectLibraryByUserIdsAndTraceIds(adminIds, traceIdSet);
            long costTime3 = System.currentTimeMillis() - t3;
            LOGGER.info("selectTimeLineLibrary selectLibraryByUserIdsAndTraceIds! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime3);
            PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                it.labels(PrometheusMetricsUtil.getHostName(), "selectLibraryByUserIdsAndTraceIds", "0").observe(costTime3);
            }));
            if (Optional.ofNullable(request.getNeedSubVideos()).orElse(true)) {
                long t4 = System.currentTimeMillis();
                subLibraryViewList = shardingLibraryDAO.selectLibraryByUserIdsAndMainTraceIds(adminIds, traceIdSet, Arrays.asList(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW.getCode()), null);
                long costTime4 = System.currentTimeMillis() - t4;
                LOGGER.info("selectTimeLineLibrary selectLibraryByUserIdsAndMainTraceIds! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime4);
                PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                    it.labels(PrometheusMetricsUtil.getHostName(), "selectLibraryByUserIdsAndMainTraceIds", "0").observe(costTime4);
                }));
            }
            if (Optional.ofNullable(request.getNeedMultiResolutionVideos()).orElse(false)) {
                long t6 = System.currentTimeMillis();
                multiResolutionLibraryViewList = shardingLibraryDAO.selectLibraryByUserIdsAndMainTraceIds(adminIds, traceIdSet, Arrays.asList(VideoTypeEnum.EVNET_RECORDING_MULTI_RESOLUTION.getCode()), null);
                long costTime6 = System.currentTimeMillis() - t6;
                LOGGER.info("selectTimeLineLibrary selectMultiResolutionLibraryByUserIdsAndMainTraceIds! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime6);
                PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                    it.labels(PrometheusMetricsUtil.getHostName(), "selectMultiResolutionLibraryByUserIdsAndMainTraceIds", "0").observe(costTime6);
                }));
            }
        }
        Map<String, List<UserLibraryViewDO>> traceId2SubLibraryList = subLibraryViewList.stream().collect(Collectors.groupingBy(it -> it.getMainTraceId()));
        Map<String, List<UserLibraryViewDO>> traceId2MultiResolutionLibraryList = multiResolutionLibraryViewList.stream().collect(Collectors.groupingBy(it -> it.getMainTraceId()));

        long t5 = System.currentTimeMillis();
        listUserLibraryViewDO = listUserLibraryViewDO.stream().map(userLibraryViewDO -> {
            userLibraryViewDO.setSubVideos(traceId2SubLibraryList.getOrDefault(userLibraryViewDO.getTraceId(), new ArrayList<>()));
            userLibraryViewDO.setMultiResolutionVideos(traceId2MultiResolutionLibraryList.getOrDefault(userLibraryViewDO.getTraceId(), new ArrayList<>()));
            userLibraryViewDO.setDeviceAiEventList(new ArrayList<>(serialNumberMap.get(userLibraryViewDO.getSerialNumber())));
            List<SubcategoryInfo> subcategoryInfoList = new ArrayList<>();
            if (traceIdToStatus.get(userLibraryViewDO.getTraceId()).getBirdName() != null) {
                List<AiEvent> events = JSON.parseArray(userLibraryViewDO.getEventInfo(), AiEvent.class);
                for (AiEvent event : events) {
                    if (Objects.equals(event.getEventObject().getName(), AiObjectEnum.BIRD.getName().toLowerCase())) {
                        for (PossibleSubcategory subcategory : event.getPossibleSubcategory()) {
                            if (Objects.equals(traceIdToStatus.get(userLibraryViewDO.getTraceId()).getBirdName(), subcategory.getName())) {
                                List<BirdName> birdNames = birdLoversService.searchBirdNameByStdName(subcategory.getName());
                                String birdName = birdNames.get(0).getLang2Name().containsKey(request.getLanguage()) ? birdNames.get(0).getLang2Name().get(request.getLanguage()) : birdNames.get(0).getStdName();
                                subcategoryInfoList.add(new SubcategoryInfo(event.getEventObject().getName(), birdName, subcategory.getName(), s3Service.preSignUrl(event.getSummaryUrl()), subcategory.getConfidence()));
                                break;
                            }
                        }
                        if (!subcategoryInfoList.isEmpty()) {
                            break;
                        }
                    }
                }
            }
            userLibraryViewDO.setSubcategoryInfoList(subcategoryInfoList);
            return userLibraryViewDO;
        }).collect(Collectors.toList());
        long costTime5 = System.currentTimeMillis() - t5;
        LOGGER.info("selectTimeLineLibrary searchBirdNameByStdName! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime5);
        PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
            it.labels(PrometheusMetricsUtil.getHostName(), "searchBirdNameByStdName", "0").observe(costTime5);
        }));

        long t6 = System.currentTimeMillis();
        //填充视频用户信息
        initListUserLibraryViewDO(listUserLibraryViewDO, traceIdToStatus);
        long costTime6 = System.currentTimeMillis() - t6;
        LOGGER.info("selectTimeLineLibrary initListUserLibraryViewDO! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime6);
        PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
            it.labels(PrometheusMetricsUtil.getHostName(), "initListUserLibraryViewDO", "0").observe(costTime6);
        }));

        if (ObjectUtils.compare(request.getVideoEvent(), 0L) > 0) {
            listUserLibraryViewDO = new LinkedList<>(listUserLibraryViewDO);
            Collections.sort(listUserLibraryViewDO, new Comparator<UserLibraryViewDO>() {
                public int compare(UserLibraryViewDO userLibraryViewDO, UserLibraryViewDO userLibraryViewDO2) {
                    return ObjectUtils.compare(userLibraryViewDO.getTimestamp(), userLibraryViewDO2.getTimestamp());
                }

                ;
            });
        }
        long t7 = System.currentTimeMillis();
        Map<String, List<VideoSliceDO>> traceId2SliceList = queryTraceId2SliceList(listUserLibraryViewDO, request.getHasSliceList(), request.getStartTimestamp(), request.getEndTimestamp());
        inflateSliceList(listUserLibraryViewDO, traceId2SliceList);
        long costTime7 = System.currentTimeMillis() - t7;
        LOGGER.info("selectTimeLineLibrary inflateSliceList! userId={},list={},costTime={}", request.getUserId(), serialNumberMap.size(), costTime7);
        PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
            it.labels(PrometheusMetricsUtil.getHostName(), "searchBirdNameByStdName", "0").observe(costTime7);
        }));

        // 多分辨率信息
        if (CollectionUtils.isNotEmpty(listUserLibraryViewDO)) {
            listUserLibraryViewDO.forEach((e) -> {
                if (StringUtils.isEmpty(e.getResolution()) || e.getAdminId() == null || e.getTraceId() == null) return;
                List<String> traceIds = shardingLibraryDAO.selectMultiResolutionTraceIds(e.getAdminId(), e.getTraceId());
                if(CollectionUtils.isEmpty(traceIds) || traceIds.size() == 1) return;
                List<VideoSliceDO> videoSliceDOList = shardingVideoSliceDAO.selectResolutionInfoByTraceIds(e.getAdminId(), traceIds);
                if (CollectionUtils.isEmpty(videoSliceDOList)) return;
                e.setMultiResolutionInfo(new MultiResolutionInfo().setResolution().setBandwidth(MasterPlaylistGenerator.calculateBandwidth(videoSliceDOList, e.getResolution())));
            });
        }

        return listUserLibraryViewDO;
    }

    private Set<Integer> activityZoneIdSet(Collection<LibraryStatusTb> libraryStatuses) {
        Set<Integer> activityIdSet = Sets.newHashSet();
        for (LibraryStatusTb libraryStatusTb : libraryStatuses) {
            if (StringUtils.isEmpty(libraryStatusTb.getActivityZoneId())) {
                continue;
            }
            String[] activityIdArray = libraryStatusTb.getActivityZoneId().split(",");
            for (String activityId : activityIdArray) {

                if (StringUtils.isEmpty(activityId)) {
                    continue;
                }
                Integer activity = Integer.valueOf(activityId);
                if (activityIdSet.contains(activity)) {
                    continue;
                }
                activityIdSet.add(activity);
            }
        }
        return activityIdSet;
    }

    /**
     * 批量获取用户视频列表
     *
     * @param request
     * @return
     */
    public QueryLibraryResult selectLibraryEvent(LibraryRequest request) {
        List<LibraryEventDO> libraryEventList = libraryStatusService.queryLibraryEventList(request);

        List<LibraryEventView> libraryEventViewList = this.buildLibraryEventList(request.getUserId(), libraryEventList);

        // 填充视频关键帧信息
        if (Boolean.TRUE.equals(request.getQueryKeyshot())) {
            this.fillKeyshot(libraryEventViewList, request);
        }

        final VideoAndEventCount videoAndEventCount = libraryStatusService.selectVideoAndEventCount(request);
        return QueryLibraryResult.builder()
                .list(libraryEventViewList)
                .eventCount(videoAndEventCount.getEventCount())
                .libraryCount(videoAndEventCount.getVideoCount())   // 返回视频数据 不含image （no plan）
                .keyshotCount(Boolean.TRUE.equals(request.getQueryKeyshot()) ? selectKeyshotCount(request) : 0)
                .build();
    }

    private Integer selectKeyshotCount(LibraryRequest request) {
        if (!Boolean.TRUE.equals(request.getQueryKeyshot())
                || request.getObjectNames() == null || request.getObjectNames().isEmpty()) {
            return 0;
        }
        LibraryRequest libraryRequest = new LibraryRequest();
        libraryRequest.setUserId(request.getUserId());
        libraryRequest.setObjectNames(request.getObjectNames());
        List<LibraryStatusTb> libraryStatusTbList = shardingLibraryStatusDAO.selectLibraryStatusList(libraryRequest);
        Set<Integer> adminIdSet = libraryStatusTbList.stream().map(LibraryStatusTb::getAdminId).collect(Collectors.toSet());
        if (adminIdSet.isEmpty()) {
            return 0;
        }
        List<UserLibraryViewDO> userLibraryViewDOList = shardingLibraryDAO.selectLibraryByUserIdsAndTraceIds(adminIdSet, libraryStatusTbList.stream().map(LibraryStatusTb::getTraceId).collect(Collectors.toSet()));
        return (int) userLibraryViewDOList.stream().flatMap(it -> it.getKeyshots().stream())
                .filter(it -> request.getObjectNames().contains(it.getSubCategoryName()))
                .count();

    }

    private void fillKeyshot(List<LibraryEventView> libraryEventViewList, LibraryRequest request) {
        if (libraryEventViewList == null || libraryEventViewList.isEmpty()) {
            return;
        }
        List<String> objectNames = request.getObjectNames();
        Set<String> traceIds = libraryEventViewList.stream().map(LibraryEventView::getTraceIds).map(str -> Arrays.asList(str.split(","))).flatMap(List::stream).collect(Collectors.toSet());
        Set<Integer> adminIdSet = libraryEventViewList.stream().map(LibraryEventView::getAdminId).collect(Collectors.toSet());
        List<UserLibraryViewDO> userLibraryViewDOList = shardingLibraryDAO.selectLibraryByUserIdsAndTraceIds(adminIdSet, traceIds);
        Map<String, List<KeyShot>> traceId2KeyShot = userLibraryViewDOList.stream().collect(Collectors.toMap(UserLibraryViewDO::getTraceId, view -> view.getKeyshots() != null ? view.getKeyshots() : new ArrayList<>(),
                (existing, replacement) -> existing));
        libraryEventViewList.forEach(libraryEventView -> {
            Set<String> traceIdSet = Arrays.stream(libraryEventView.getTraceIds().split(",")).collect(Collectors.toSet());
            traceIdSet.forEach(traceId -> {
                List<KeyShot> keyShots = traceId2KeyShot.get(traceId);
                if (keyShots != null && !keyShots.isEmpty()) {
                    libraryEventView.addKeyshots(keyShots.stream()
                            .filter(keyShot -> objectNames == null || objectNames.isEmpty() || objectNames.contains(keyShot.getSubCategoryName()))
                            .peek(it -> it.setImageUrl(s3Service.preSignUrl(it.getImageUrl()))).collect(Collectors.toList())
                    );
                }
            });
        });

    }

    /**
     * 视频事件列表
     *
     * @param libraryEventList
     * @return
     */
    private List<LibraryEventView> buildLibraryEventList(Integer userId, List<LibraryEventDO> libraryEventList) {
        if (CollectionUtils.isEmpty(libraryEventList)) return new LinkedList<>();

        Set<Integer> adminIdSet = libraryEventList.stream().map(LibraryEventDO::getAdminIds).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());
        Set<String> traceIdSet = libraryEventList.stream().map(LibraryEventDO::getTraceIds).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());

        // 事件内libraryId 列表
        List<LibraryTb> libraryTbList = new LinkedList<>();
        Map<String, List<LibraryTb>> traceId2SubLibraryList = new LinkedHashMap<>();
        Map<String, List<LibraryTb>> traceId2MultiResolutionLibraryList = new LinkedHashMap<>();
        if (!CollectionUtils.isEmpty(traceIdSet)) {
            libraryTbList = shardingLibraryDAO.selectLibraryInfoByUserIdsAndBatchTraceId(adminIdSet, traceIdSet);
            // 查询双画面子视频
            List<Integer> subVideoTypes = Arrays.asList(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW.getCode());
            List<LibraryTb> subViewLibraryList = shardingLibraryDAO.selectLibraryInfoByUserIdsAndBatchMainTraceId(adminIdSet, traceIdSet, subVideoTypes, null);
            traceId2SubLibraryList = subViewLibraryList.stream().collect(Collectors.groupingBy(it -> it.getMainTraceId()));

            // 查询多分辨率子视频
            List<Integer> multiResolutionVideoTypes = Arrays.asList(VideoTypeEnum.EVNET_RECORDING_MULTI_RESOLUTION.getCode());
            List<LibraryTb> multiResolutionLibraryList = shardingLibraryDAO.selectLibraryInfoByUserIdsAndBatchMainTraceId(adminIdSet, traceIdSet, multiResolutionVideoTypes, null);
            traceId2MultiResolutionLibraryList = multiResolutionLibraryList.stream().collect(Collectors.groupingBy(it -> it.getMainTraceId()));
        }

        Map<String, LibraryTb> traceIdToLibraryTb = libraryTbList.stream()
                .collect(Collectors.toMap(LibraryTb::getTraceId, Function.identity()));

        List<LibraryEventView> libraryEventViewList = Lists.newArrayList();
        for (LibraryEventDO libraryEventDO : libraryEventList) {

            LibraryEventView libraryEventView = LibraryEventView.builder()
                    .serialNumber(libraryEventDO.getSerialNumber())
                    .videoEvent(libraryEventDO.getVideoEvent())
                    .videoEventKey(String.valueOf(libraryEventDO.getVideoEvent()))
                    .startTime(libraryEventDO.getStartTime())
                    .endTime(libraryEventDO.getEndTime())
                    .libraryCount(libraryEventDO.getLibraryCount())
                    .marked(libraryEventDO.getMarked())
                    .missing(libraryEventDO.getMissing())
                    .libraryIds(libraryEventDO.getLibraryIds())
                    .traceIds(StringUtils.join(libraryEventDO.getTraceIds(), ","))
                    .supportMagicPix(libraryEventDO.getSupportMagicPix())
                    .build();
            libraryEventView.setMediaType(libraryEventDO.getMediaType());

            getLibraryEventSummary(userId, libraryEventView, libraryEventDO, traceIdToLibraryTb, traceId2SubLibraryList, traceId2MultiResolutionLibraryList);
            libraryEventViewList.add(libraryEventView);

        }
        return libraryEventViewList;
    }

    /**
     * 视频事件统计
     *
     * @param userId
     * @param libraryEventView
     * @param libraryEventDO
     * @param traceIdToLibraryTb
     */
    void getLibraryEventSummary(Integer userId, LibraryEventView libraryEventView, LibraryEventDO libraryEventDO, Map<String, LibraryTb> traceIdToLibraryTb, Map<String, List<LibraryTb>> traceId2SubLibraryList, Map<String, List<LibraryTb>> traceId2MultiResolutionLibraryList) {
        // doorbell tags计算
        if (StringUtils.isNotEmpty(libraryEventDO.getDoorbellTags())) {
            libraryEventView.setDoorbellTags(new LinkedList<>(TextUtil.splitToNotBlankSet(libraryEventDO.getDoorbellTags(), ',')));
        }
        if (StringUtils.isNotEmpty(libraryEventDO.getDeviceCallEventTags())) {
            libraryEventView.setDeviceCallEventTag(StringUtils.join(TextUtil.splitToNotBlankSet(libraryEventDO.getDeviceCallEventTags(), ','), ","));
        }

        libraryEventDO.setTags(VideoLibraryTagUtil.mergeTags(libraryEventDO.getTags(), libraryEventDO.getAiEdgeTags(), libraryEventDO.getDoorbellTags(), libraryEventDO.getDeviceCallEventTags()));

        // tags 数量计算
        Map<String, Integer> tagsCountMap = this.deviceLibraryTag(userId, libraryEventDO.getTags());
        libraryEventView.setTags(tagsCountMap);

        libraryEventView.setHasSubVideos(libraryEventDO.getTraceIds().stream().anyMatch(traceId2SubLibraryList::containsKey));
        libraryEventView.setHasMultiResolutionVideos(libraryEventDO.getTraceIds().stream().anyMatch(traceId2MultiResolutionLibraryList::containsKey));

        List<LibraryTb> libraryTbList = libraryEventDO.getTraceIds().stream().flatMap(traceId -> {
            LibraryTb libraryTb = traceIdToLibraryTb.get(traceId);
            if (libraryTb != null) return Stream.of(libraryTb);
            LOGGER.info("视频在library_status 中存在，但是在video_library 中不存在 {}", traceId);
            return Stream.empty();
        }).sorted(Comparator.comparingInt(LibraryTb::getTimestamp).reversed()).collect(Collectors.toList()); // 从后往前排序

        libraryEventView.setServiceName(libraryTbList.stream().map(LibraryTb::getServiceName).filter(Objects::nonNull).findFirst().orElse(""));

        libraryEventView.setStartTime(FuncUtil.reduce(libraryTbList, LibraryTb::getTimestamp, Math::min).orElseGet(libraryEventDO::getStartTime));
        libraryEventView.setEndTime(FuncUtil.reduce(libraryTbList, it -> Optional.ofNullable(it.getEndTimestamp()).orElseGet(() -> it.getTimestamp() + it.getPeriod().intValue()), Math::max).orElseGet(libraryEventDO::getEndTime));
        libraryEventView.setPeriod(libraryTbList.stream().map(LibraryTb::getPeriod).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        libraryEventView.setIsShowPeriod(libraryTbList.stream().allMatch(LibraryTb::getIsShowPeriod));
        libraryEventView.setEnableDelete(libraryTbList.stream().allMatch(it -> Objects.equals(userId, it.getAdminId())));

        libraryEventView.setDeviceName(libraryTbList.stream().map(LibraryTb::getDeviceName).filter(StringUtils::isNotBlank).findFirst().orElse(""));
        libraryEventView.setImageUrl(libraryTbList.stream().map(LibraryTb::getImageUrl).map(s3Service::preSignUrl).filter(StringUtils::isNotBlank).findFirst().orElse(""));
        libraryEventView.setVideoUrl(libraryTbList.stream().map(LibraryTb::getVideoUrl).map(s3Service::preSignUrl).filter(StringUtils::isNotBlank).findFirst().orElse(""));
        libraryEventView.setAdminId(libraryTbList.stream().map(LibraryTb::getAdminId).filter(Objects::nonNull).findFirst().orElse(userId));
    }

    /**
     * 视频tag 次数统计
     *
     * @param tags
     * @return
     */
    private Map<String, Integer> deviceLibraryTag(Integer userId, String tags) {
        Map<String, Integer> tagCountMap = Maps.newHashMap();
        if (StringUtils.isEmpty(tags)) {
            return tagCountMap;
        }

        Set<String> parentTagSet = new LinkedHashSet<>();

        String[] tagArray = tags.split(",");
        for (String tag : tagArray) {
            if (StringUtils.isEmpty(tag)) {
                continue;
            }

            if (modelAiEventConfig.getParentEvent().containsKey(tag)) {
                //父级标签，不计算数量  但是safemo除外
                parentTagSet.add(tag);
                continue;
            }

            tag = modelAiEventConfig.eventSummary.containsKey(tag) ? modelAiEventConfig.getEventSummary().get(tag) : tag;
            int count = tagCountMap.containsKey(tag) ? (tagCountMap.get(tag) + 1) : 1;
            tagCountMap.put(tag, count);
        }

        if (AppConstants.TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))) {
            for (String tag : parentTagSet) {
                tagCountMap.put(tag, tagCountMap.getOrDefault(tag, 1)); // 如果父tag不在map中，则加入到mapg
            }
        }

        // sort tag count map
        Map<String, Integer> sortedTagCountMap = new LinkedHashMap<>();
        tagCountMap.entrySet()
                .stream()
                .sorted(VideoSearchService.createVideoTagComparator(Map.Entry::getKey))
                .forEach(tagCountEntry -> sortedTagCountMap.put(tagCountEntry.getKey(), tagCountEntry.getValue()));
        return sortedTagCountMap;
    }

    /**
     * 初始化视频中用户相关信息
     *
     * @param listUserLibraryViewDO
     */
    void initListUserLibraryViewDO(List<UserLibraryViewDO> listUserLibraryViewDO, Map<String, LibraryStatusTb> traceIdToStatus) {
        Set<Integer> userIdSet = Sets.newHashSet();
        listUserLibraryViewDO.forEach(view -> {
            if (!userIdSet.contains(view.getAdminId())) {
                userIdSet.add(view.getAdminId());
            }
        });

        traceIdToStatus.forEach((key, value) -> {
            if (!userIdSet.contains(value.getUserId())) {
                userIdSet.add(value.getUserId());
            }
        });


        List<User> userList = iUserDAO.selectUserByUserIds(StringUtils.join(userIdSet, ","));
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        Map<Integer, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, User -> User));

        final Map<Integer, ActivityZoneDO> activityMap = getActivityMap(traceIdToStatus.values());
        listUserLibraryViewDO.forEach(view -> {
            view.setAdminName(userMap.containsKey(view.getAdminId()) ? userMap.get(view.getAdminId()).getName() : "");
            view.setUserName(userMap.containsKey(view.getUserId()) ? userMap.get(view.getUserId()).getName() : "");

            LibraryStatusTb libraryStatus = traceIdToStatus.get(view.getTraceId());
            view.setId(libraryStatus.getLibraryId());
            view.setMarked(libraryStatus.getMarked());
            view.setMissing(libraryStatus.getMissing());
            view.setUserId(libraryStatus.getUserId());
            assemblePushInfo(view, libraryStatus.getUserId());
            view.setActivityZoneName(this.activityName(libraryStatus.getActivityZoneId(), activityMap));
            view.setEventInfo(null);
            view.setDoorbellTags(libraryStatus.getDoorbellTags());

            //处理门铃tags
            view.setTags(VideoLibraryTagUtil.mergeTags(view.getTags(), view.getAiEdgeTags(), view.getDoorbellTags(), view.getDeviceCallEventTag()));

            view.setSupportMagicPix(libraryStatus.getSupportMagicPix());
            view.setEnableDelete(Objects.equals(view.getUserId(), view.getAdminId()));

            view.setVideoEvent(libraryStatus.getVideoEvent());
        });
    }

    private String activityName(String activityStr, Map<Integer, ActivityZoneDO> activityZoneDOMap) {
        if (StringUtils.isEmpty(activityStr)) {
            return "";
        }
        String[] activityArr = activityStr.split(",");
        StringJoiner activityName = new StringJoiner(",");
        for (String activity : activityArr) {
            if (StringUtils.isEmpty(activity)) {
                continue;
            }
            final ActivityZoneDO activityZoneDO = activityZoneDOMap.get(Integer.valueOf(activity));
            if (activityZoneDO == null || StringUtils.isBlank(activityZoneDO.getZoneName())) {
                continue;
            }
            activityName.add(activityZoneDO.getZoneName());
        }
        return activityName.toString();
    }

    private Map<Integer, ActivityZoneDO> getActivityMap(Collection<LibraryStatusTb> libraryStatuses) {
        Map<Integer, ActivityZoneDO> activityZoneDOMap = Maps.newHashMap();
        Set<Integer> activityZoneId = this.activityZoneIdSet(libraryStatuses);
        if (activityZoneId.isEmpty()) {
            return activityZoneDOMap;
        }

        for (Integer activityId : activityZoneId) {
            activityZoneDOMap.put(activityId, activityZoneService.queryActivityZoneById(activityId));
        }
        return activityZoneDOMap;
    }


    /**
     * 获取单个视频信息
     *
     * @param id
     * @param userId
     * @return
     */
    public UserLibraryViewDO selectSingleLibrary(Integer id, String traceId, Integer userId) {
        String queryTraceId = StringUtils.defaultString(traceId, ShardingJdbcUtil.queryTraceIdByUserIdAndLibraryId(userId, id));
        LibraryStatusTb libraryStatusTb = StringUtils.isEmpty(queryTraceId) ? null : libraryStatusService.selectLibraryStatusByTraceIdAndUserId(queryTraceId, userId);
        if (libraryStatusTb == null) {
            throw new BaseException(NO_LIBRARY_ACCESS, NO_LIBRARY_ACCESS.getMsg());
        }

        Integer adminId = libraryStatusService.queryAdminIdByUserIdAndTraceId(userId, traceId);
        if (adminId == null) {
            throw new BaseException(ResultCollection.NO_LIBRARY_ACCESS, "视频不存在");
        }
        UserLibraryViewDO userLibraryViewDO = shardingLibraryDAO.selectSingleLibraryByUserIdAndTraceId(adminId, queryTraceId);
        userLibraryViewDO.setVideoEvent(libraryStatusTb.getVideoEvent());
        return this.initUserLibraryViewDO(userLibraryViewDO, userId);
    }

    private UserLibraryViewDO initUserLibraryViewDO(UserLibraryViewDO userLibraryViewDO, Integer userId) {

        userLibraryViewDO.setUserId(userId);

        if (userLibraryViewDO.getMissing() == null || userLibraryViewDO.getMarked() == null) {
            LibraryStatusTb libraryStatusTb = libraryStatusService.selectLibraryStatusByTraceIdAndUserId(userLibraryViewDO.getTraceId(), userId);
            if (libraryStatusTb != null) {
                userLibraryViewDO.setMissing(libraryStatusTb.getMissing());
                userLibraryViewDO.setMarked(libraryStatusTb.getMarked());
            }
        }

        User user = userService.queryUserById(userId);
        if (user != null) {
            userLibraryViewDO.setUserName(user.getName());
        }

        User adminUser = userService.queryUserById(userLibraryViewDO.getAdminId());
        if (adminUser != null) {
            userLibraryViewDO.setAdminName(adminUser.getName());
        }

        /**
         * 推送的标记信息
         */
        assemblePushInfo(userLibraryViewDO, userId);
        /**
         * 视频所属用户是否vip
         */
        userLibraryViewDO.setAdminIsVip(vipService.isVipDevice(userLibraryViewDO.getAdminId(), userLibraryViewDO.getSerialNumber()));

        // 处理门铃tag
        userLibraryViewDO.setTags(VideoLibraryTagUtil.mergeTags(userLibraryViewDO.getTags(), userLibraryViewDO.getAiEdgeTags(), userLibraryViewDO.getDoorbellTags(), userLibraryViewDO.getDeviceCallEventTag()));

        return userLibraryViewDO;
    }


    /**
     * 如果有推送，则展示在视频详情页
     *
     * @param view
     * @param userId
     * @return
     */
    public void assemblePushInfo(UserLibraryViewDO view, Integer userId) {
        final String eventInfo = view.getEventInfo();
        final String aiEdgeEventInfo = view.getAiEdgeEventInfo();
        final String doorbellTags = view.getDoorbellTags();
        final String deviceCallEventTag = view.getDeviceCallEventTag();
        final Integer adminId = view.getAdminId();

        Map<String, String> tag2Desc = new LinkedHashMap<>();

        List<AiEvent> events = new LinkedList<>();

        if (StringUtils.isNotEmpty(doorbellTags)) {
            List<EReportEvent> doorbellEventList = VideoLibraryTagUtil.getDoorbellEventInfoFromTags(doorbellTags);
            doorbellEventList.forEach(reportType -> {
                String content = notificationService.getVideoReportEventMsgContent(userId, reportType);
                if (StringUtils.isNotEmpty(content)) {
                    tag2Desc.put(reportType.name(), content);
                }
            });
        }

        if (StringUtils.isNotEmpty(deviceCallEventTag)) {
            String content = notificationService.getVideoReportEventMsgContent(userId, EReportEvent.DEVICE_CALL);
            if (StringUtils.isNotEmpty(content)) {
                tag2Desc.put(EReportEvent.DEVICE_CALL.name(), content);
            }
        }

        Gson gson = new Gson();
        if (JSON.isValidArray(eventInfo)) {
            JSONArray jsonArray = new JSONArray(eventInfo);
            for (int i = 0; i < jsonArray.length(); i++) {
                events.add(gson.fromJson(jsonArray.get(i).toString(), AiEvent.class));
            }
        } else if (StringUtils.isNotEmpty(eventInfo)) {
            events.add(gson.fromJson(eventInfo, AiEvent.class));
        }

        if (JSON.isValidArray(aiEdgeEventInfo)) {
            String[] aiEdgeTagStrs = StringUtils.defaultIfEmpty(view.getAiEdgeTags(), "").split(",");
            Set<AiObjectEnum> aiEdgeEventObjects = Arrays.asList(aiEdgeTagStrs).stream().map(aiEdgeTagStr -> StringUtils.isNotEmpty(aiEdgeTagStr) ? EnumFindUtil.findByName(aiEdgeTagStr, AiObjectEnum.class) : null)
                    .filter(obj -> obj != null).collect(Collectors.toSet());
            Map<AiObjectEnum, AiEvent> aiEdgeEventMap = new HashMap<>();
            JSONArray jsonArray = new JSONArray(aiEdgeEventInfo);
            for (int i = 0; i < jsonArray.length(); i++) {
                AiEvent event = gson.fromJson(jsonArray.get(i).toString(), AiEvent.class);
                if (aiEdgeEventObjects.contains(event.getEventObject())) {
                    aiEdgeEventMap.put(event.getEventObject(), event);
                }
            }
            events.forEach(event -> {
                aiEdgeEventMap.remove(event.getEventObject());
            });

            aiEdgeEventMap.entrySet().forEach(entry -> events.add(entry.getValue()));
        }
        boolean hasPossibleSubcategory = false;
        User user = userService.queryUserById(userId);
        for (AiEvent event : events) {
            String message = notificationService.getEventDescribe(new NotificationService.PushContext()
                    .setUser(user).setAdminId(adminId).setShowPossibleSubcategoryText(false), event);
            VideoTagEnumUtil.event2Tags(event).ifPresent(tag -> tag2Desc.putIfAbsent(tag, message));
            hasPossibleSubcategory |= !CollectionUtils.isEmpty(event.getPossibleSubcategory());
        }
        view.setHasPossibleSubcategory(hasPossibleSubcategory);

        // tag、describe 重新根据tag排序
        List<Map.Entry<String, String>> tagAndDescList = tag2Desc.entrySet().stream()
                .sorted(VideoSearchService.createVideoTagComparator(Map.Entry::getKey))
                .collect(Collectors.toList());
        view.setTags(tagAndDescList.stream().map(Map.Entry::getKey).collect(Collectors.joining(",")));
        view.setEventInfoList(tagAndDescList.stream().map(Map.Entry::getValue).collect(Collectors.toList()));
        view.setPushInfo(tagAndDescList.size() > 0 ? tagAndDescList.get(0).getValue() : "");
    }

    @Setter
    @Autowired
    private StaleDataTransferDAO staleDataTransferDAO;

    // 转移video_library表数据到oss中
    public void syncLibrary(int limitNum) {
        staleDataTransferService.transferStaleData(StaleDataTransferService.StaleData.<Integer>builder()
                .dbName("camera").tableName("video_library").primaryKeyName("id")
                .queryByLastIdAndLimitNum(staleDataTransferDAO::queryExpiredOrDeletedLibraryByLastIdAndLimitNum)
                .limitNum(limitNum).build());
    }

    /**
     * 查询视频捐献原因
     * 如果是用户选中的，status置为1
     *
     * @param lang
     * @param libraryId
     * @return
     */
    public List<LibraryDonateDO> queryLibraryDonateList(Integer userId, String lang, Long libraryId) {
        //get 配置列表
        List<LibraryDonateDO> libraryDonateDOList = libraryDonateConfig.getReason().get(lang);
        if (CollectionUtils.isEmpty(libraryDonateDOList)) {
            LOGGER.info("queryLibraryDonateList empty:libraryId:{},lang:{}", libraryId, lang);
            return libraryDonateDOList;
        }

        DeviceLibraryViewDO view = null;
        Set<Integer> userIds = shardingLibraryStatusDAO.selectAdminsByUserId(userId);
        DeviceLibraryViewDO view2 = view != null ? shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(userIds, view.getTraceId()) : null;
        view = view2;

        if (view != null && StringUtils.isEmpty(view.getTags())) {
            libraryDonateDOList = libraryDonateDOList.stream().filter(s -> s.getShowNoTag()).collect(Collectors.toList());
        }

        //查询捐献原因
        List<LibraryDonateDO> libraryDonateDOSelectList = libraryDonateDAO.queryLibraryDonateList(libraryId);
        if (CollectionUtils.isEmpty(libraryDonateDOSelectList)) {
            //如果用户未标记过视频，则只展示特定选项
            return libraryDonateDOList;
        }

        Map<Integer, LibraryDonateDO> donateDOMap = libraryDonateDOSelectList.stream()
                .collect(Collectors.toMap(LibraryDonateDO::getCode, donate -> donate));


        List<LibraryDonateDO> result = Lists.newArrayList();
        for (LibraryDonateDO libraryDonateDO : libraryDonateDOList) {
            LibraryDonateDO nonate = new LibraryDonateDO();
            nonate.setCode(libraryDonateDO.getCode());
            nonate.setTitle(libraryDonateDO.getTitle());
            if (donateDOMap.containsKey(libraryDonateDO.getCode())) {
                nonate.setStatus(1);
                nonate.setRemark(donateDOMap.get(libraryDonateDO.getCode()).getRemark());
            }
            result.add(nonate);
        }


        return result;
    }

    /**
     * 保存视频标注
     *
     * @param request
     * @param userId
     * @return
     */
    public boolean saveLibraryDonate(LibraryDonateRequest request, Integer userId) {
        String traceId = StringUtils.defaultString(request.getTraceId(), ShardingJdbcUtil.queryTraceIdByUserIdAndLibraryId(userId, request.getId().intValue()));
        Set<Integer> userIds = shardingLibraryStatusDAO.selectAdminsByUserId(userId);
        DeviceLibraryViewDO libraryViewDO = StringUtils.isEmpty(traceId) ? null : shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(userIds, traceId);
        if (libraryViewDO == null) {
            throw new ParamException(NO_LIBRARY_ACCESS.getCode(), "视频不存在");
        }

        UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(userId, libraryViewDO.getSerialNumber());
        if (userRoleDO == null) {
            throw new BaseException(ResultCollection.DEVICE_NO_ROLE, "无权限更新");
        }

        Set<Integer> codeSet = request.getCode();
        //查询捐献原因列表
        List<LibraryDonateDO> libraryDonateDOSelectList = libraryDonateDAO.queryLibraryDonateList(request.getId());
        if (!CollectionUtils.isEmpty(libraryDonateDOSelectList)) {
            for (LibraryDonateDO donate : libraryDonateDOSelectList) {
                //因为0为其他原因，可能会更改备注
                if (codeSet.contains(donate.getCode()) && donate.getCode() != 0) {
                    codeSet.remove(donate.getCode());
                } else {
                    //原提交的原因删除
                    libraryDonateDAO.deleteLibraryDonate(request.getId(), donate.getCode());
                    LOGGER.info("saveLibraryDonate delete 之前提交的原因,libraryId:{},code:{}", request.getId(), donate.getCode());
                }
            }
        }
        if (codeSet.isEmpty()) {
            return true;
        }

        return libraryDonateDAO.insertOrUpdate(request.getId(), codeSet, request.getRemark()) > 0;
    }


    public List<LibraryDonate> queryLibraryDonateList(LibraryDonateParam request) {
        //查询符合条件的捐献的视频
        List<LibraryDonate> libraryDonateList = libraryDonateDAO.queryLibraryDonateListByTime(request.getTimeStart(),
                request.getTimeEnd(), request.getLimitNumber());
        if (!CollectionUtils.isEmpty(libraryDonateList)) {
            List<Integer> libraryIdList = libraryDonateList.stream().map(donate -> donate.getLibraryId().intValue())
                    .collect(Collectors.toList());
            //查询视频地址
            Map<Long, String> libraryUrlMap = Collections.emptyMap();

            for (LibraryDonate donate : libraryDonateList) {
                if (!libraryUrlMap.containsKey(donate.getLibraryId())) {
                    continue;
                }
                //计算地址
                donate.setVideoUrl(s3Service.preSignUrl(libraryUrlMap.get(donate.getLibraryId())));
            }

        }
        return libraryDonateList;
    }

    /**
     * 按天获取是否有视频
     *
     * @param libraryRequest
     * @return
     */
    public List<LibraryCountDay> queryLibraryCountDay(LibraryRequest libraryRequest) {
        List<LibraryCountDay> result = Lists.newArrayList();
        List<LibraryRequest> libraryCountDayRequestList = this.initLibraryCountRequest(libraryRequest);
        if (CollectionUtils.isEmpty(libraryCountDayRequestList)) {
            LOGGER.info("按天查询，划分时间");
            return result;
        }
        int threadPoolSize = Math.min(THREAD_POOL_SIZE, libraryCountDayRequestList.size());

        String requestId = MDC.get(MDCKeys.REQUEST_ID);

        //创建线程池
        ExecutorService exc = Executors.newFixedThreadPool(threadPoolSize);
        try {
            List<Future<LibraryCountDay>> futures = Lists.newArrayList();
            for (LibraryRequest request : libraryCountDayRequestList) {

                Future<LibraryCountDay> future = exc.submit(new Callable() {
                    public Object call() throws Exception {
                        MDC.put(MDCKeys.REQUEST_ID, requestId);
                        MDC.put(MDCKeys.USER_ID, String.valueOf(libraryRequest.getUserId()));
                        return libraryStatusService.selectLibraryCountDay(request);
                    }
                });
                //将每个线程放入线程集合， 这里如果任何一个线程的执行结果没有回调，线程都会自动堵塞
                futures.add(future);
            }
            //所有线程执行完毕之后会执行下面的循环，然后通过循环每个个线程后执行线程的get()方法每个线程执行的结果
            for (Future<LibraryCountDay> future : futures) {
                LibraryCountDay libraryCountDay = future.get();
                if (libraryCountDay.getCount() == 0) {
                    continue;
                }
                result.add(libraryCountDay);
            }
        } catch (Exception e) {
            LOGGER.info("按天查询是否有视频异常", e);
        } finally {
            exc.shutdown();
        }
        return result;
    }

    /**
     * 和queryLibraryCountDay功能上一致，但是使用sql进行分组
     */
    public List<LibraryCountDay> queryLibraryCountDayGroup(LibraryRequest libraryRequest) {
        return libraryStatusService.selectLibraryCountDayGroup(libraryRequest);
    }

    /**
     * 参数划分时间区间
     *
     * @return
     */
    public static List<LibraryRequest> initLibraryCountRequest(LibraryRequest request) {
        long dateTimeStart = request.getStartTimestamp();
        long dateTimeEnd;
        List<LibraryRequest> libraryCountDayList = Lists.newArrayList();
        // 将时间按天划分区间
        while (dateTimeStart <= request.getEndTimestamp()) {
            if (StringUtils.isNotEmpty(request.getApp() != null ? request.getApp().getTimeZone() : null)) {
                // 使用指定时区
                Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(request.getApp().getTimeZone()));
                calendar.setTimeInMillis(dateTimeStart * 1000L);
                // 设置为当天开始时间
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                dateTimeStart = calendar.getTimeInMillis() / 1000L;

                // 设置为当天结束时间
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                dateTimeEnd = calendar.getTimeInMillis() / 1000L;
            } else {
                dateTimeEnd = dateTimeStart + SECONDS_PER_DAY;
            }

            LibraryRequest libraryRequest = new LibraryRequest();
            BeanUtils.copyProperties(request, libraryRequest);
            // 每一个分段的开始、结束时间
            libraryRequest.setStartTimestamp(dateTimeStart);
            libraryRequest.setEndTimestamp(dateTimeEnd);

            libraryCountDayList.add(libraryRequest);
            dateTimeStart = dateTimeEnd;
        }
        return libraryCountDayList;
    }

    public DeviceLibraryViewDO queryUserLibraryViewByIdOrTraceId(Integer userId, String traceId, Integer libraryId) {
        Set<Integer> userIds = shardingLibraryStatusDAO.selectAdminsByUserId(userId);
        String queryTraceId = StringUtils.defaultString(traceId, ShardingJdbcUtil.queryTraceIdByUserIdAndLibraryId(userId, libraryId));
        DeviceLibraryViewDO view = StringUtils.isEmpty(queryTraceId) ? null : shardingLibraryDAO.selectLibraryViewByUserIdsAndTraceId(userIds, queryTraceId);
        return view;
    }

    public Set<String> getOtherQueryLibraryTagNameList(Integer userId) {
        Set<String> otherTagNames = new HashSet<>();

        List<BindOperationTb> bindOperationTbList = deviceService.queryBindHistory(userId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(bindOperationTbList)) {
            for (BindOperationTb bindOperationTb : bindOperationTbList) {
                CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(bindOperationTb.getSerialNumber());
                if (cloudDeviceSupport != null && ObjectUtils.equals(cloudDeviceSupport.getSupportDeviceCall(), 1)) {
                    otherTagNames.add(EReportEvent.DEVICE_CALL.name());
                }

                Integer deviceModelCategory = deviceModelService.queryDeviceModelCategoryBySerialNumber(bindOperationTb.getSerialNumber());
                if (deviceModelCategory != null && deviceModelCategory.intValue() == DeviceModelCategoryEnums.DOORBELL.getCode()) {
                    otherTagNames.add(EReportEvent.DOORBELL_PRESS.name());
                    otherTagNames.add(EReportEvent.DOORBELL_REMOVE.name());
                }
            }
        } else {
            // do nothing
        }

        List<ShareStatusDO> shareStatusDOList = shareDAO.getDeviceShareeSuccessHistory(userId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shareStatusDOList)) {
            for (ShareStatusDO shareStatusDO : shareStatusDOList) {
                CloudDeviceSupport cloudDeviceSupport1 = deviceInfoService.getDeviceSupport(shareStatusDO.getSerialNumber());
                if (cloudDeviceSupport1 != null && ObjectUtils.equals(cloudDeviceSupport1.getSupportDeviceCall(), 1)) {
                    otherTagNames.add(EReportEvent.DEVICE_CALL.name());
                }

                Integer deviceModelCategory = deviceModelService.queryDeviceModelCategoryBySerialNumber(shareStatusDO.getSerialNumber());
                if (deviceModelCategory != null && deviceModelCategory.intValue() == DeviceModelCategoryEnums.DOORBELL.getCode()) {
                    otherTagNames.add(EReportEvent.DOORBELL_PRESS.name());
                    otherTagNames.add(EReportEvent.DOORBELL_REMOVE.name());
                }
            }
        } else {
            // do nothing
        }

        return otherTagNames;
    }

    public List<UserLibraryViewDO> querySliceList(QuerySliceListVO input) {
        LibraryRequest request = new LibraryRequest();
        request.setUserId(input.getUserId());
        request.setTraceIds(input.getTraceIds());
        request.setVideoEvent(0L);
        request.setNeedSubVideos(false);
        request.setHasSliceList(input.getHasSliceList());
        List<UserLibraryViewDO> libraryList = selectLibrary(request);
        return libraryList;
    }

    public Map<String, List<VideoSliceDO>> queryTraceId2SliceList(List<UserLibraryViewDO> listUserLibraryViewDO, Set<Integer> hasSliceList, Long startTimestamp, Long endTimestamp) {
        Map<String, List<VideoSliceDO>> traceId2SliceList = new HashMap<>();
        List<UserLibraryViewDO> libraryList = new LinkedList<>();
        AtomicInteger totalSliceNum = new AtomicInteger(0);
        try {
            for (UserLibraryViewDO library : listUserLibraryViewDO) {
                libraryList.add(library);
                if (CollectionUtils.isNotEmpty(library.getSubVideos())) {
                    libraryList.addAll(library.getSubVideos());
                }
            }
            Map<Integer, List<UserLibraryViewDO>> adminId2Libraries = libraryList.stream().collect(Collectors.groupingBy(it -> it.getAdminId()));
            for (Integer adminId : adminId2Libraries.keySet()) {
                List<UserLibraryViewDO> libraryList21 = adminId2Libraries.get(adminId).stream().filter(it -> hasSliceList.contains(it.getVideoType())).collect(Collectors.toList());
                Observable.fromIterable(libraryList21).map(it -> it.getTraceId()).buffer(100).forEach(traceIds -> {
                    for (VideoSliceDO slice : shardingVideoSliceDAO.querySliceByAdminUserIdAndTraceIdsAndTimestampRange(adminId, traceIds, DateUtils.toMillis(startTimestamp), DateUtils.toMillis(endTimestamp))) {
                        traceId2SliceList.computeIfAbsent(slice.getTraceId(), k -> new ArrayList<>()).add(slice);
                        totalSliceNum.incrementAndGet();
                    }
                });
            }
            log.info("queryTraceId2SliceList end! libraryListSize={},hasSliceList={},totalSliceNum={},traceId2SliceListSize={}", libraryList.size(), hasSliceList, totalSliceNum, traceId2SliceList.size());
        } catch (Throwable e) {
            log.error("queryTraceId2SliceList error! libraryListSize={},hasSliceList={},totalSliceNum={},resultSize={}", libraryList.size(), hasSliceList, totalSliceNum, traceId2SliceList.size());
        }
        return traceId2SliceList;
    }

    public void inflateSliceList(List<UserLibraryViewDO> libraryList, Map<String, List<VideoSliceDO>> traceId2SliceList) {
        if (CollectionUtils.isEmpty(libraryList)) return;
        try {
            for (UserLibraryViewDO library : libraryList) {
                inflateSliceList(library.getSubVideos(), traceId2SliceList); // 递归填充子画面视频

                List<VideoSliceDO> rawSliceList = traceId2SliceList.get(library.getTraceId());
                if (CollectionUtils.isEmpty(rawSliceList)) continue;

                List<SliceDetailVO> sliceList = rawSliceList.stream()
                        .sorted(Comparator.comparingInt(VideoSliceDO::getOrder))
                        .map(it -> {
                            SliceDetailVO sliceView = new SliceDetailVO();
//                            sliceView.setVideoUrl(s3Service.preSignSliceUrl(it.getVideoUrl(), safeRtcRootUrl));
                            sliceView.setVideoUrl(it.getVideoUrl());
                            sliceView.setTimestamp(it.getS3EventTime());
                            sliceView.setPeriod(VideoSliceDO.computeSliceMillis(it.getPeriod()));
                            sliceView.setOrder(it.getOrder());
                            sliceView.setIsLast(it.getIsLast());
                            sliceView.setFileSize(it.getFileSize());
                            return sliceView;
                        })
                        .collect(Collectors.toList());
                library.setSliceList(sliceList);
            }
        } catch (Throwable e) {
            log.error("inflateSliceList error!", e);
        }
    }

}