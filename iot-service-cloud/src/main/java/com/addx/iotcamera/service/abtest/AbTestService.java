package com.addx.iotcamera.service.abtest;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.FreeLicenseABTestResult;
import com.addx.iotcamera.bean.response.user.Product4GABTestResult;
import com.addx.iotcamera.bean.response.vip.FreeLicenseResponse;
import com.addx.iotcamera.dao.IProductDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.factory.DeviceBindInfoDao;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.vip.IUserDeviceFreeTierDao;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.ReportLogService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.abtest.model.AbFeatureSimpleResult;
import com.addx.iotcamera.service.abtest.model.UserTag;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import growthbook.sdk.java.FeatureResult;
import growthbook.sdk.java.GBContext;
import growthbook.sdk.java.GrowthBook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.AbTestConstants.*;
import static com.addx.iotcamera.constants.PayConstants.TIER_ID_SETS_4G;
import static com.addx.iotcamera.constants.ReportLogConstants.REPORTER_SYS;
import static com.addx.iotcamera.constants.UserConstants.USER_ABTEST_REPORT_TYPE;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/9/8 18:04
 */
@Service
@Slf4j
public class AbTestService {
    public static final String DEFAULT_JSON = "{}";
    public static final String DATE_NEW_USER = "2023-11-09 00:00:00";
    private static final int UNSET = -1;
    public static final String ABTEST_FEATURES_KEY = "abtestFeatures";

    private static final int  ABTEST_FEATURES_TIMEOUT = 300;
    @Value("${abtest.server.url:null}")
    public String serverUrl;
    @Autowired
    IOrderDAO iOrderDAO;
    @Autowired
    IUserDAO iUserDAO;

    @Autowired
    IProductDAO iProductDAO;

    @Resource
    private ReportLogService reportLogService;

    @Autowired
    private RedisService redisService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private FactoryDataQueryService factoryDataQueryService;
    @Qualifier("deviceBindInfoDao")
    @Autowired
    private DeviceBindInfoDao deviceBindInfoDao;
    @Autowired
    private FreeLicenseService freeLicenseService;
    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private DeviceModelService deviceModelService;
    @Autowired
    private DeviceManualService deviceManualService;
    @Autowired
    private IUserDeviceFreeTierDao userDeviceFreeTierDao;

    @Autowired
    private IUserVipDAO iUserVipDAO;
    @Autowired
    private UserVipService userVipService;

    //透传所有特性
    public String getFeatures() {

        String result = this.getAbFeatureByHttpWithCache();

        if (StringUtils.isEmpty(result)) {
            return DEFAULT_JSON;
        }
        return result;
    }


    //看所有生效特性的命中情况
    public List<AbFeatureSimpleResult> allFeatureCheck(String userId) throws URISyntaxException, IOException {

        String httpResult = this.getAbFeatureByHttpWithCache();

        if (StringUtils.isEmpty(httpResult)) {
            return new ArrayList<>();
        }

        String featuresJson = JSON.parseObject(httpResult, Map.class).get("features").toString();


        GBContext context = GBContext.builder()
                .featuresJson(featuresJson)
                .build();

        JSONObject userAttributesObj = new JSONObject();
        userAttributesObj.put("userId", Integer.parseInt(userId));

        context.setAttributesJson(userAttributesObj.toString()); // Optiona
        GrowthBook growthBook = new GrowthBook(context);

        List<AbFeatureSimpleResult> list = new ArrayList<>();


        Map featuresMap = JSON.parseObject(context.getFeaturesJson(), Map.class);
        for (Object key : featuresMap.keySet()) {
            //本地评估
            FeatureResult<Object> result = growthBook.evalFeature((String) key, Object.class);
            AbFeatureSimpleResult abFeatureSimpleResult = new AbFeatureSimpleResult();
            abFeatureSimpleResult.setFeatureId((String) key);
            abFeatureSimpleResult.setValue(result.getValue() != null ? result.getValue().toString() : null);

            if (result.getExperimentResult() != null) {
                abFeatureSimpleResult.setVariationId(result.getExperimentResult().getVariationId());
            } else {
                abFeatureSimpleResult.setVariationId(UNSET);
            }

            list.add(abFeatureSimpleResult);
        }

        return list;
    }


    //加上cache，减少growthbook服务器压力
    //service内部调用，无法用cache注解
    private String getAbFeatureByHttpWithCache(){
        String cacheResult = redisService.get(ABTEST_FEATURES_KEY);
        if (StringUtils.isNotEmpty(cacheResult)) {
            return cacheResult;
        }

        //暂时不用sdkKey
        String httpResult =   HttpUtils.httpGet(serverUrl, new HashMap<>());
        if (StringUtils.isEmpty(httpResult)) {
            return null;
        }

        redisService.set(ABTEST_FEATURES_KEY, httpResult, ABTEST_FEATURES_TIMEOUT);
        return httpResult;
    }

    //看是否命中某个特性
    public AbFeatureSimpleResult singleFeatureCheck(String userId, String featureId, String appType, String language,String tenantId, String appVersion) {
        String httpResult = this.getAbFeatureByHttpWithCache();
        log.debug("singleFeatureCheck httpResult is :{}", httpResult);

        if (StringUtils.isEmpty(httpResult)) {
            return new AbFeatureSimpleResult();
        }

        String featuresJson = JSON.parseObject(httpResult, Map.class).get("features").toString();

        GBContext context = GBContext.builder()
                .featuresJson(featuresJson)
                .build();

        //筛选条件
        JSONObject userAttributesObj = new JSONObject();
        userAttributesObj.put("userId", Integer.parseInt(userId));
        if(StringUtils.isNoneBlank(appType)){
            userAttributesObj.put("appType", appType);
        }
        if(StringUtils.isNotBlank(language)){
            userAttributesObj.put("language", language);
        }
        if(StringUtils.isNotBlank(tenantId)){
            userAttributesObj.put("tenantId", tenantId);
        }
        if (appVersion != null) {
            try {
                Integer versionCode = extractVersionAsInteger(appVersion);
                log.info("abtest appVersion is: {}", versionCode);
                userAttributesObj.put("version", versionCode);
            } catch (Exception e) {
                log.warn("Failed to process appVersion: {}", appVersion, e);
            }
        }

        //用户是否只绑定这两种型号的设备
        List<String> deviceList = userRoleService.getSerialNumbersByUserId(Integer.valueOf(userId));
        Boolean isOnlyAwarenessFreeTrialModelDevices = CollectionUtils.isNotEmpty(deviceList) && deviceList
                .stream().allMatch(sn -> AWARENESS_FREE_TRAIL_MODEL_NO.equals(deviceManualService.getModelNoBySerialNumber(sn)));

        Boolean isOnlyOneDayFreeTrialModelDevices = CollectionUtils.isNotEmpty(deviceList) && deviceList
                .stream().allMatch(sn -> ONE_DAY_FREE_TRAIL_MODEL_NO.equals(deviceManualService.getModelNoBySerialNumber(sn)));
        userAttributesObj.put("isOnlyAwarenessFreeTrialModelDevices", isOnlyAwarenessFreeTrialModelDevices);
        userAttributesObj.put("isOnlyOneDayFreeTrialModelDevices", isOnlyOneDayFreeTrialModelDevices);

        Boolean isOnlyAwarenessFreeLicenseId = getAwarenessFreeLicenseId(userId, isOnlyAwarenessFreeTrialModelDevices, deviceList);
        userAttributesObj.put("isOnlyAwarenessFreeLicenseId", isOnlyAwarenessFreeLicenseId);

        Boolean isOnlyOneDayFreeLicenseId = getOnlyOneDayFreeLicenseId(userId, isOnlyOneDayFreeTrialModelDevices, deviceList);
        userAttributesObj.put("isOnlyOneDayFreeLicenseId", isOnlyOneDayFreeLicenseId);


        log.info("isOnlyAwarenessFreeTrialModelDevices is: {}, isOnlyOneDayFreeTrialModelDevices is:{}, isOnlyAwarenessFreeLicenseId is:{}, isOnlyOneDayFreeLicenseId is: {}"
                , isOnlyAwarenessFreeTrialModelDevices,isOnlyOneDayFreeTrialModelDevices, isOnlyAwarenessFreeLicenseId, isOnlyOneDayFreeLicenseId);

        UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(Integer.valueOf(userId));
        if (userSettingsDO != null && userSettingsDO.getSupportFreeLicense() != null) {
            userAttributesObj.put("isSupportedFreeLicense", userSettingsDO.getSupportFreeLicense());
        }

        List<UserVipDO> userVipDOList = iUserVipDAO.queryUserVipInfo(Integer.valueOf(userId), 0, TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
        userAttributesObj.put("isPurchasedCloudVip", userVipDOList.stream().anyMatch(userVipDO -> !TIER_ID_SETS_4G.contains(userVipDO.getTierId()) && userVipDO.getTierId() % 10 > 0));

        context.setAttributesJson(userAttributesObj.toString()); // Optiona
        GrowthBook growthBook = new GrowthBook(context);

        //本地评估
        FeatureResult<Object> result = growthBook.evalFeature(featureId, Object.class);
        AbFeatureSimpleResult abFeatureSimpleResult = new AbFeatureSimpleResult();
        abFeatureSimpleResult.setFeatureId(featureId);
        abFeatureSimpleResult.setValue(result.getValue() != null ? result.getValue().toString() : null);
        abFeatureSimpleResult.setExperimentKey(result.getExperiment() != null && result.getExperiment().getKey() != null
                ? result.getExperiment().getKey() : null);

        if (result.getExperimentResult() != null) {
            abFeatureSimpleResult.setVariationId(result.getExperimentResult().getVariationId());
        } else {
            abFeatureSimpleResult.setVariationId(UNSET);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("variationId",abFeatureSimpleResult.getVariationId());
        jsonObject.put("featureId",abFeatureSimpleResult.getFeatureId());
        jsonObject.put("value",abFeatureSimpleResult.getValue());
        jsonObject.put("userId",userId);
        jsonObject.put("key",USER_ABTEST_REPORT_TYPE);
        reportLogService.reportLog(USER_ABTEST_REPORT_TYPE,REPORTER_SYS,REPORTER_SYS,jsonObject);

        return abFeatureSimpleResult;
    }

    private @Nullable Boolean getOnlyOneDayFreeLicenseId(String userId, Boolean isOnlyOneDayFreeTrialModelDevices, List<String> deviceList) {
        Boolean isOnlyOneDayFreeLicenseId = null;
        if (isOnlyOneDayFreeTrialModelDevices) {
            boolean hasNonOneDayFreeLicenseDevice = deviceList.stream()
                    .anyMatch(serialNumber -> Optional.ofNullable(userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(Integer.parseInt(userId), serialNumber))
                            .map(userDeviceFreeTierDO -> !ONE_DAY_FREE_LICENSE_ID.equals(userDeviceFreeTierDO.getTierId()))
                            .orElse(false));
            if (hasNonOneDayFreeLicenseDevice) {
                isOnlyOneDayFreeLicenseId = false;
            } else {
                FreeLicenseResponse freeLicenseData = freeLicenseService.getFreeLicenseData(Integer.valueOf(userId),
                        deviceList.stream().filter(serialNumber -> userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(Integer.parseInt(userId), serialNumber) == null).collect(Collectors.toList()));
                isOnlyOneDayFreeLicenseId = freeLicenseData.getDeviceList().stream()
                        .allMatch(device -> ONE_DAY_FREE_LICENSE_ID.equals(device.getFreeLicenseId()));
            }
        }
        return isOnlyOneDayFreeLicenseId;
    }


    private @Nullable Boolean getAwarenessFreeLicenseId(String userId, Boolean isOnlyAwarenessFreeTrialModelDevices, List<String> deviceList) {
        //用户所绑定设备此时能领取的freelicense id
        Boolean isOnlyAwarenessFreeLicenseId = null;
        if (isOnlyAwarenessFreeTrialModelDevices) {
            boolean hasNonAwarenessFreeLicenseDevice = deviceList.stream()
                    .anyMatch(serialNumber -> Optional.ofNullable(userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(Integer.parseInt(userId), serialNumber))
                            .map(userDeviceFreeTierDO -> !AWARENESS_FREE_LICENSE_ID.equals(userDeviceFreeTierDO.getTierId()))
                            .orElse(false));

            // 如果存在不满足条件的设备，直接设置为 false
            if (hasNonAwarenessFreeLicenseDevice) {
                isOnlyAwarenessFreeLicenseId = false;
            } else {
                // 如果所有设备都满足条件，进一步调用 freeLicenseService
                FreeLicenseResponse freeLicenseData = freeLicenseService.getFreeLicenseData(Integer.valueOf(userId),
                        deviceList.stream().filter(serialNumber -> userDeviceFreeTierDao.selectDeviceFreeTierByUserIdAndSerialNumber(Integer.parseInt(userId), serialNumber) == null).collect(Collectors.toList()));
                // 检查返回的设备列表中是否所有设备的 FreeLicenseId 都是 AWARENESS_FREE_LICENSE_ID
                isOnlyAwarenessFreeLicenseId = freeLicenseData.getDeviceList().stream()
                        .allMatch(device -> AWARENESS_FREE_LICENSE_ID.equals(device.getFreeLicenseId()));
            }
        }
        return isOnlyAwarenessFreeLicenseId;
    }

    //提取appVersion括号前的版本号并重新计算
    public static Integer extractVersionAsInteger(String versionName) {
        // 去掉括号中的内容
        int index = versionName.indexOf('(');
        String cleanVersionName = index > 0 ? versionName.substring(0, index) : versionName;

        // 分割版本号
        String[] versionParts = cleanVersionName.split("\\.");
        if (versionParts.length != 3) {
            throw new IllegalArgumentException("Invalid versionName format. Expected format: x.y.z");
        }

        // 提取 major, minor, patch 部分并检查范围
        int major = Integer.parseInt(versionParts[0]);
        int minor = Integer.parseInt(versionParts[1]);
        int patch = Integer.parseInt(versionParts[2]);

        if (major > 99 || minor > 99 || patch > 99) {
            throw new IllegalArgumentException("Each version part must be <= 99");
        }

        // 按固定长度拼接
        String versionCodeStr = String.format("%02d%02d%02d", major, minor, patch);

        // 转换为整数
        return Integer.parseInt(versionCodeStr);
    }

    //查询用户的ab属性，返回给客户端
    public UserTag queryUserTag(Integer userId) {
        UserTag userTag = new UserTag();
        List<OrderDO> orderList = iOrderDAO.queryUserTestOrderList(userId);
        userTag.setTested(CollectionUtils.isNotEmpty(orderList));

        //注册时间大于 指定日期
        User user = new User();
        user.setId(userId);
        user = iUserDAO.getUserById(user);
        String dateString = DATE_NEW_USER;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date date = null;
        try {
            date = dateFormat.parse(dateString);
        } catch (ParseException e) {
            throw new RuntimeException("日期解析错误");
        }

        long timestamp = date.getTime()/1000;
        userTag.setNewUser(user.getRegistTime() >= timestamp);

        int oldCount = iProductDAO.selectOldPackCount(userId);
        userTag.setPurchasedOld(oldCount > 0);

        int newCount = iProductDAO.selectNewPackCount(userId);
        userTag.setPurchasedNew(newCount > 0);

        return userTag;
    }

    public FreeLicenseABTestResult getFreeLicenseAbResult(Integer userId, AppRequestBase requestBase) {
        Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
        //获取两组ab命中结果
        AbFeatureSimpleResult awarenessAbResult = this.singleFeatureCheck(String.valueOf(userId), AWARENESS_FREE_TRAIL, requestBase.getApp().getAppType(), requestBase.getLanguage(), requestBase.getApp().getTenantId(), requestBase.getApp().getVersionName());
        AbFeatureSimpleResult oneDayAbResult = this.singleFeatureCheck(String.valueOf(userId), ONE_DAY_FREE_TRAIL, requestBase.getApp().getAppType(), requestBase.getLanguage(), requestBase.getApp().getTenantId(), requestBase.getApp().getVersionName());
        log.info("awarenessAbResult is: {}, oneDayAbResult is: {}, userId is:{}", awarenessAbResult, oneDayAbResult, userId);
        //是否命中实验组
        boolean notFreeTrial = (awarenessAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(awarenessAbResult.getValue(), EXPERIMENT_GROUP))
                || (oneDayAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(oneDayAbResult.getValue(), EXPERIMENT_GROUP));
        //是否命中对照组
        boolean isFreeTrial = (awarenessAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(awarenessAbResult.getValue(), CONTROL_GROUP))
                || (oneDayAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(oneDayAbResult.getValue(), CONTROL_GROUP));
        //先存入map，后续aop写入header
        if (awarenessAbResult.getExperimentKey() != null) {
            abFeatureSimpleResultList.put(awarenessAbResult.getExperimentKey(), awarenessAbResult.getVariationId());
        }
        if (oneDayAbResult.getExperimentKey() != null) {
            abFeatureSimpleResultList.put(oneDayAbResult.getExperimentKey(), oneDayAbResult.getVariationId());
        }
        return FreeLicenseABTestResult.builder()
                .abFeatureSimpleResultList(abFeatureSimpleResultList)
                .isFreeTrial(isFreeTrial)
                .isNotFreeTrial(notFreeTrial)
                .build();
    }

    public ABTestResult getAwarenessFreeTrailDayAbResult(Integer userId, AppRequestBase requestBase) {
        AbFeatureSimpleResult awarenessFreeTrialAbResult = this.singleFeatureCheck(String.valueOf(userId), AWARENESS_FREE_TRIAL_DAY, requestBase.getApp().getAppType(), requestBase.getLanguage(), requestBase.getApp().getTenantId(), requestBase.getApp().getVersionName());
        boolean hit0DayExperimentGroup = awarenessFreeTrialAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(awarenessFreeTrialAbResult.getValue(), ZERO_DAYS_EXPERIMENT_GROUP);
        boolean hitControlGroup = awarenessFreeTrialAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(awarenessFreeTrialAbResult.getValue(), AWARENESS_CONTROL_GROUP);
        boolean hit7DayExperimentGroup = awarenessFreeTrialAbResult.experimentSuccessful() && NumberUtil.isIntegerOrDecimalEqualTo(awarenessFreeTrialAbResult.getValue(), SEVEN_DAYS_EXPERIMENT_GROUP);
        Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
        if (awarenessFreeTrialAbResult.getExperimentKey() != null) {
            abFeatureSimpleResultList.put(awarenessFreeTrialAbResult.getExperimentKey(), awarenessFreeTrialAbResult.getVariationId());
        }
        return ABTestResult.builder()
                .experimentSuccessful(awarenessFreeTrialAbResult.experimentSuccessful())
                .hit0DayExperimentGroup(hit0DayExperimentGroup)
                .hit7DayExperimentGroup(hit7DayExperimentGroup)
                .hitControlGroup(hitControlGroup)
                .value(parseValue(awarenessFreeTrialAbResult.getValue()))
                .abFeatureSimpleResultList(abFeatureSimpleResultList)
                .build();
    }

    public Product4GABTestResult getProduct4GABTestResult(Integer userId, AppRequestBase requestBase) {
        AbFeatureSimpleResult dataPlanAbResult = this.singleFeatureCheck(String.valueOf(userId), DATA_PLAN_SKU_OFFER_TEST, requestBase.getApp().getAppType(), requestBase.getLanguage(), requestBase.getApp().getTenantId(), requestBase.getApp().getVersionName());
        log.debug("dataPlanAbResult is: {}, userId is:{}", dataPlanAbResult, userId);
        Integer billingCycleDuration = -1;

        Map<String, Integer> abFeatureSimpleResultList = new HashMap<>();
        if (dataPlanAbResult.getExperimentKey() != null) {
            billingCycleDuration = parseValue(dataPlanAbResult.getValue());
            abFeatureSimpleResultList.put(dataPlanAbResult.getExperimentKey(), dataPlanAbResult.getVariationId());
        }
        return Product4GABTestResult.builder()
                .experimentSuccessful(dataPlanAbResult.getExperimentKey() != null)
                .billingCycleDuration(billingCycleDuration)
                .abFeatureSimpleResultList(abFeatureSimpleResultList)
                .build();
    }

    public Integer parseValue(String valueStr) {
        try {
            return (valueStr != null && !valueStr.isEmpty()) ? (int) Double.parseDouble(valueStr) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
