package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.bird.*;
import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.domain.BirdNameFeedbackDO;
import com.addx.iotcamera.bean.openapi.PossibleSubcategoryVO;
import com.addx.iotcamera.config.EsConfig;
import com.addx.iotcamera.dao.BirdNameFeedbackDAO;
import com.addx.iotcamera.helper.ConfigHelper;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.model.PossibleSubcategory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.codehaus.plexus.util.IOUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@Slf4j
@Component
public class BirdLoversService {

    @Autowired
    private DeviceAiSettingsService deviceAiSettingsService;
    @Autowired
    private MessageNotificationSettingsService messageNotificationSettingsService;
    @Autowired
    private DeviceAuthService deviceAuthService;
    @Autowired
    private LibraryService libraryService;
    @Autowired
    private LibraryStatusService libraryStatusService;
    @Autowired
    private S3Service s3Service;
    @Autowired
    private AiAssistService aiAssistService;

    @Setter
    @Autowired
    private BirdNameFeedbackDAO birdNameFeedbackDAO;

    private static final String INDEX_BIRD_NAME = "birdName";
    private static final int EXCEL_LANG_START_INDEX = 4;

    @Setter
    @Autowired
    private EsConfig esConfig;

    @Getter
    private Map<String, String> langName2Lang;
    @Getter
    private Set<String> langSet;

    @PostConstruct
    public void init() {
        langName2Lang = ImmutableMap.copyOf(readLangSimpleNameFromFile());
        langSet = ImmutableSet.copyOf(langName2Lang.values());
    }

    /**
     * 读取 语言名称 和 语言名称简写 的映射关系
     * 纯文本 符号 '\t' 隔开
     *
     * @param is
     * @return
     */
    public static Map<String, String> readLangSimpleName(InputStream is) {
        Map<String, String> langNameMap = new BufferedReader(new InputStreamReader(is)).lines()
                .map(it -> it.split("\t")).collect(Collectors.toMap(it -> it[0], it -> it[1]));
        log.info("readLangSimpleName:{},{}", langNameMap.size(), JSON.toJSONString(langNameMap, true));
        Map<String, List<String>> lang2LangName = langNameMap.entrySet().stream()
                .collect(Collectors.groupingBy(it -> it.getValue(), Collectors.mapping(it -> it.getKey(), Collectors.toList())));
        lang2LangName.forEach((lang, langNames) -> {
            if (langNames.size() > 1) {
                com.addx.iotcamera.util.LogUtil.error(log, "readLangSimpleName 发现重复的语言名称简写映射！lang={},langNames={}", lang, langNames);
            }
        });
        return langNameMap;
    }


    /**
     * 读取鸟类名称
     * xlsx文件
     *
     * @param is
     * @param langName2Lang 语言英文名称 -> 语言英文简称
     * @return
     */
    @SneakyThrows
    public static BirdNameDict readBirdNameDict(final InputStream is, Map<String, String> langName2Lang) {
        log.info("readBirdNameDict begin!");
        final XSSFWorkbook book = new XSSFWorkbook(is);
        final XSSFSheet sheet = book.getSheetAt(0);
        /** 1.读取首行 **/
        final XSSFRow firstRow = sheet.getRow(0);
        final int cellCount = firstRow.getPhysicalNumberOfCells();
        final String[] columnLangName = new String[cellCount]; // 列索引对应的语言
        final String[] columnLang = new String[cellCount]; // 列索引对应的语言简写
        for (int j = EXCEL_LANG_START_INDEX; j < cellCount; j++) {
            XSSFCell cell = firstRow.getCell(j);
            String langName = cell.getStringCellValue(); // English
            String lang = langName2Lang.get(langName); // en
            if (lang == null) {
                throw new RuntimeException("找不到语言名称的英文缩写! langName=" + langName);
            }
            log.info("readBirdNameDict 找到语言名称的英文缩写 langName={},lang={}", langName, lang);
            columnLangName[j] = langName;
            columnLang[j] = lang;
        }
        /** 2.读取各语言中的鸟类名称 **/
        final int[] columnNotValueCount = new int[cellCount]; // 列索引对应的语言的鸟类名称缺失数量
        Arrays.fill(columnNotValueCount, 0);
        final int rowCount = sheet.getPhysicalNumberOfRows();
        ImmutableList.Builder<BirdName> birdNamesBuilder = ImmutableList.builder();
        for (int i = 1; i < rowCount; i++) {
            XSSFRow row = sheet.getRow(i);
            BirdName birdCategory = new BirdName();
            birdCategory.setSeq(new BigDecimal(row.getCell(0).toString()).setScale(0).toString());
            birdCategory.setOrder(row.getCell(1).getStringCellValue());
            birdCategory.setFamily(row.getCell(2).getStringCellValue());
            birdCategory.setIoc_12_1(row.getCell(3).getStringCellValue());
            Map<String, String> lang2Value = new LinkedHashMap<>(cellCount - EXCEL_LANG_START_INDEX);
            for (int j = EXCEL_LANG_START_INDEX; j < cellCount; j++) {
                String value = null;
                if (j < row.getPhysicalNumberOfCells()) {
                    value = row.getCell(j).getStringCellValue();
                }
                if (StringUtils.isBlank(value)) {
                    columnNotValueCount[j]++;
                    com.addx.iotcamera.util.LogUtil.error(log, "readBirdNameDict 找不到鸟类在某语言中的名称！seq={},langName={},lang={},en={},zh={}", birdCategory.getSeq(), columnLangName[j], columnLang[j], lang2Value.get("en"), lang2Value.get("zh"));
                } else if (value != null) {
                    lang2Value.put(columnLang[j], value);
                }
            }
            birdCategory.setZhName(lang2Value.get("zh"));
            birdCategory.setEnName(lang2Value.get("en"));
            birdCategory.setLang2Name(ImmutableMap.copyOf(lang2Value));
            birdNamesBuilder.add(birdCategory);
        }
        Map<String, Integer> notValueCount = new LinkedHashMap<>();
        for (int j = EXCEL_LANG_START_INDEX; j < cellCount; j++) {
            if (columnNotValueCount[j] <= 0) continue;
            notValueCount.put(columnLang[j], columnNotValueCount[j]);
        }
        ImmutableList<BirdName> birdNames = birdNamesBuilder.build();
        log.info("readBirdNameDict end! 鸟类总数={},各语言的鸟类名称缺失数量={}", birdNames.size(), JSON.toJSONString(notValueCount, true));
        BirdNameDict birdNameDict = new BirdNameDict();
        birdNameDict.setContent(birdNames);
        birdNameDict.setLangList(ImmutableList.copyOf(Arrays.copyOfRange(columnLang, EXCEL_LANG_START_INDEX, columnLang.length)));
        birdNameDict.setLangNameList(ImmutableList.copyOf(Arrays.copyOfRange(columnLangName, EXCEL_LANG_START_INDEX, columnLang.length)));
        return birdNameDict;
    }

    public static Map<String, String> readLangSimpleNameFromFile() {
        return readLangSimpleName(ConfigHelper.loadConfig("classpath:bird/lang_simple_name.txt"));
    }

    public static BirdNameDict readBirdNameDictFromFile() {
        final Map<String, String> langName2Lang = readLangSimpleNameFromFile();
        return readBirdNameDict(ConfigHelper.loadConfig("classpath:bird/Multiling_IOC_12_1c.xlsx"), langName2Lang);
    }

    @SneakyThrows
    public boolean putEsMapping(CloseableHttpClient client, String indexName, List<String> langList) {
        JSONObject mapping = BirdName.buildMapping(langList);
        String mappingStr = JSON.toJSONString(mapping, true);
        log.info("putEsMapping begin:{}", mappingStr);
        final String esEndpoint = esConfig.getEndpoints().get(mappingStr.hashCode() % esConfig.getEndpoints().size());
        HttpPut putMapping = new HttpPut(esEndpoint + "/" + indexName);
        putMapping.addHeader("Content-Type", "application/json;charset=UTF-8");
        putMapping.setEntity(new ByteArrayEntity(mappingStr.getBytes(StandardCharsets.UTF_8)));
        try (CloseableHttpResponse resp = client.execute(putMapping)) {
            String respBodyStr = IOUtil.toString(resp.getEntity().getContent(), "UTF-8");
            log.info("putEsMapping statusLine={},respBody={}", resp.getStatusLine(), respBodyStr);
            int respCode = resp.getStatusLine().getStatusCode();
            if (respCode == HttpStatus.SC_OK || respCode == HttpStatus.SC_CREATED) {
                return true;
            }
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "putEsMapping error!", e);
        }
        return false;
    }

    @SneakyThrows
    public boolean deleteEsIndex(String indexName) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            final String esEndpoint = esConfig.getEndpoints().get(indexName.hashCode() % esConfig.getEndpoints().size());
            HttpDelete delete = new HttpDelete(esEndpoint + "/" + indexName);
            try (CloseableHttpResponse resp = client.execute(delete)) {
                String respBodyStr = IOUtil.toString(resp.getEntity().getContent(), "UTF-8");
                log.info("deleteEsIndex statusLine={},respBody={}", resp.getStatusLine(), respBodyStr);
                int respCode = resp.getStatusLine().getStatusCode();
                if (respCode == HttpStatus.SC_OK) {
                    return true;
                }
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "deleteEsIndex error!", e);
            }
        }
        return false;
    }

    @SneakyThrows
    public boolean putEsData(CloseableHttpClient client, String indexName, BirdName birdName) {
        String birdNameStr = JSON.toJSONString(birdName, true);
        log.info("putEsData begin:{}", birdNameStr);
        final String esEndpoint = esConfig.getEndpoints().get(birdNameStr.hashCode() % esConfig.getEndpoints().size());
        HttpPut putData = new HttpPut(esEndpoint + "/" + indexName + "/_doc/" + birdName.getSeq());
        putData.addHeader("Content-Type", "application/json;charset=UTF-8");
        putData.setEntity(new ByteArrayEntity(birdNameStr.getBytes(StandardCharsets.UTF_8)));
        try (CloseableHttpResponse resp = client.execute(putData)) {
            String respBodyStr = IOUtil.toString(resp.getEntity().getContent(), "UTF-8");
            log.info("putEsData statusLine={},respBody={}", resp.getStatusLine(), respBodyStr);
            int respCode = resp.getStatusLine().getStatusCode();
            if (respCode == HttpStatus.SC_OK || respCode == HttpStatus.SC_CREATED) {
                return true;
            }
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "putEsData error!", e);
        }
        return false;
    }

    @SneakyThrows
    public Result<JSONObject> createBirdNameIndex(String indexName, InputStream is1) {
        InputStream is2 = ConfigHelper.loadConfig("classpath:es_mappings/birdName.json");
        try (CloseableHttpClient client = HttpClients.createDefault();
             BufferedReader reader = new BufferedReader(new InputStreamReader(is1));
             BufferedReader reader2 = new BufferedReader(new InputStreamReader(is2))) {

            JSONObject mapping = JSON.parseObject(reader2.lines().collect(Collectors.joining()));
            JSONObject lang2Name = mapping.getJSONObject("mappings").getJSONObject("properties")
                    .getJSONObject("lang2Name").getJSONObject("properties");
            if (!putEsMapping(client, indexName, new ArrayList<>(lang2Name.keySet()))) {
                return Result.Failure("create index fail!");
            }
            AtomicInteger count = new AtomicInteger(0);
            reader.lines().forEach(line -> {
                if (!JSON.isValidObject(line)) return;
                BirdName birdName = JSON.parseObject(line, BirdName.class);
                if (putEsData(client, indexName, birdName)) {
                    count.incrementAndGet();
                }
            });
            log.info("put_birdName_to_es:{}", count.get());
            return new Result<>(new JSONObject().fluentPut("count", count.get()));
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "createBirdNameIndex fail!", e);
            return Result.Failure(e.getMessage());
        }
    }

    public List<BirdName> searchBirdNameByStdName(String inputName) {
        JSONObject term = new JSONObject().fluentPut("ioc_12_1", inputName);
        JSONObject reqBody = new JSONObject()
                .fluentPut("query", new JSONObject().fluentPut("term", term));
        return searchBirdName(reqBody);
    }

    public List<BirdNameVO> searchBirdName(BirdNameSearch input) {
        JSONObject match = new JSONObject().fluentPut("lang2Name." + input.getLanguage(), new JSONObject()
                .fluentPut("query", input.getInputName()).fluentPut("fuzziness", "AUTO"));
        JSONObject reqBody = new JSONObject()
                .fluentPut("query", new JSONObject().fluentPut("match", match))
                .fluentPut("size", input.getLimitNum());
        List<BirdName> birdNames = searchBirdName(reqBody);
        return birdNames.stream().map(it -> BirdNameVO.from(it, input.getLanguage())).collect(Collectors.toList());
    }

    @SneakyThrows
    public List<BirdName> searchBirdName(JSONObject reqBody) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            String reqBodyStr = JSON.toJSONString(reqBody, true);
            log.info("searchBirdName begin:{}", reqBodyStr);
            final String esEndpoint = esConfig.getEndpoints().get(reqBodyStr.hashCode() % esConfig.getEndpoints().size());
            HttpPost search = new HttpPost(esEndpoint + "/" + esConfig.getIndexes().get(INDEX_BIRD_NAME) + "/_search");
            search.addHeader("Content-Type", "application/json;charset=UTF-8");
            search.setEntity(new ByteArrayEntity(reqBodyStr.getBytes(StandardCharsets.UTF_8)));
            try (CloseableHttpResponse resp = client.execute(search)) {
                String respBodyStr = IOUtil.toString(resp.getEntity().getContent(), "UTF-8");
                log.info("searchBirdName statusLine={},respBody={}", resp.getStatusLine(), respBodyStr);
                int respCode = resp.getStatusLine().getStatusCode();
                if (respCode == HttpStatus.SC_OK) {
                    JSONObject respBody = JSON.parseObject(respBodyStr);
                    return respBody.getJSONObject("hits").getJSONArray("hits").stream()
                            .map(it -> {
                                JSONObject hit = (JSONObject) it;
                                BirdName birdName = hit.getObject("_source", BirdName.class);
                                return birdName.setScore(hit.getBigDecimal("_score"));
                            })
                            .collect(Collectors.toList());
                }
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "searchBirdName error!", e);
            }
        }
        return null;
    }

    @SneakyThrows
    public BirdName getBirdName(String seq) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            log.info("getBirdName begin:{}", seq);
            final String esEndpoint = esConfig.getEndpoints().get(seq.hashCode() % esConfig.getEndpoints().size());
            HttpGet get = new HttpGet(esEndpoint + "/" + esConfig.getIndexes().get(INDEX_BIRD_NAME) + "/_doc/" + seq);
            get.addHeader("Content-Type", "application/json;charset=UTF-8");
            try (CloseableHttpResponse resp = client.execute(get)) {
                String respBodyStr = IOUtil.toString(resp.getEntity().getContent(), "UTF-8");
                log.info("getBirdName statusLine={},respBody={}", resp.getStatusLine(), respBodyStr);
                int respCode = resp.getStatusLine().getStatusCode();
                if (respCode == HttpStatus.SC_OK) {
                    JSONObject respBody = JSON.parseObject(respBodyStr);
                    return respBody.getObject("_source", BirdName.class);
                }
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "getBirdName error!", e);
            }
        }
        return null;
    }


    public Result feedbackBirdName(BirdNameFeedback input) {
        if (StringUtils.isNotBlank(input.getSelectStdName())) {
            List<BirdName> birdNames = searchBirdNameByStdName(input.getSelectStdName());
            if (birdNames.isEmpty()) {
                return Result.Error(ResultCollection.INVALID_PARAMS, "selectStdName取值错误!");
            }
        }
        BirdNameFeedbackDO birdNameFeedbackDO = new BirdNameFeedbackDO()
                .setTraceId(input.getTraceId())
                .setUserId(input.getUserId())
                .setLanguage(input.getLanguage())
                .setInputName(input.getInputName())
                .setSelectStdName(input.getSelectStdName())
                .setPossibleStdName(input.getPossibleStdName());
        int saveNum = birdNameFeedbackDAO.save(birdNameFeedbackDO);
        return new Result(new JSONObject().fluentPut("saveNum", saveNum));
    }

    public List<BirdNameFeedbackDO> queryBirdNameFeedback(BirdNameFeedback feedback) {
        return birdNameFeedbackDAO.queryByTraceIdAndUserIdAndLanguage(feedback.getTraceId(), feedback.getUserId(), feedback.getLanguage());
    }

    // 处理AI事件结果中的鸟类子类
    public List<PossibleSubcategory> handleAiEventPossibleSubcategory(List<PossibleSubcategory> possibleSubcategory) {
        if (CollectionUtils.isEmpty(possibleSubcategory)) return Collections.emptyList();
        List<PossibleSubcategory> subcategoryList = new LinkedList<>();
        for (PossibleSubcategory subcategory : possibleSubcategory) {
            List<BirdName> birdNames = searchBirdNameByStdName(subcategory.getName());
            if (birdNames.isEmpty()) {
                com.addx.iotcamera.util.LogUtil.error(log, "算法识别到学名未知的鸟类! possibleSubcategory={}", JSON.toJSONString(subcategory));
                continue;
            }
            subcategoryList.add(subcategory);
        }
        return subcategoryList;
    }


    public Result<JSONObject> queryBirdAiSetting(Integer userId, String sn) {
        if (SUCCESS.getCode() != deviceAuthService.checkActivatedAccess(userId, sn)) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "账号不是设备管理员!");
        }
        final Set<AiObjectEnum> aiAnalyzeEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, sn);
        final MessageNotificationSetting setting = messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId);
        final boolean aiNotifySwitch;
        if (setting != null) {
            Set<String> aiNotifyEventObjects = TextUtil.splitToNotBlankSet(setting.getEventObjects(), ',');
            aiNotifySwitch = aiNotifyEventObjects.contains(AiObjectEnum.BIRD.getName());
        } else {
            aiNotifySwitch = true; // 默认打开
        }
        JSONObject data = new JSONObject()
                .fluentPut("aiAnalyzeSwitch", aiAnalyzeEventObjects.contains(AiObjectEnum.BIRD))
                .fluentPut("aiNotifySwitch", aiNotifySwitch);
        return new Result(data);
    }

    public Result updateBirdAiSetting(Integer userId, String sn, JSONObject input) {
        if (SUCCESS.getCode() != deviceAuthService.checkActivatedAccess(userId, sn)) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "账号不是设备管理员!");
        }
        final Boolean aiAnalyzeSwitch = input.getBoolean("aiAnalyzeSwitch");
        if (aiAnalyzeSwitch != null) {
            if (aiAnalyzeSwitch) {
                deviceAiSettingsService.updateEnableEventObjectsPartly(userId, sn, Arrays.asList(AiObjectEnum.BIRD), Arrays.asList());
                // 如果启用鸟类检测，也需要更新 detectBirdAi 属性
                try {
                    aiAssistService.triggerAiEdgeSettingUpdate(userId, sn, null,null, null, null, null, null, null, null, true, null);
                } catch (Exception e) {
                    log.error("更新detectBirdAi设置失败", e);
                }
            } else {
                deviceAiSettingsService.updateEnableEventObjectsPartly(userId, sn, Arrays.asList(), Arrays.asList(AiObjectEnum.BIRD));
                // 如果禁用鸟类检测，也需要更新 detectBirdAi 属性
                try {
                    aiAssistService.triggerAiEdgeSettingUpdate(userId, sn, null, null, null,null, null, null, null, null, false, null);
                } catch (Exception e) {
                    log.error("更新detectBirdAi设置失败", e);
                }
            }
        }
        final Boolean aiNotifySwitch = input.getBoolean("aiNotifySwitch");
        if (aiNotifySwitch != null) {
            MessageNotificationSetting setting = messageNotificationSettingsService.queryMessageNotificationSetting(sn, userId);
            if (setting == null) {
                setting = messageNotificationSettingsService.buildMessageNotificationSetting(userId, sn);
            }
            final Set<String> aiNotifyEventObjects = TextUtil.splitToNotBlankSet(setting.getEventObjects(), ',');
            if (aiNotifySwitch) {
                if (aiNotifyEventObjects.add(AiObjectEnum.BIRD.getName())) {
                    setting.setEventObjects(StringUtils.join(aiNotifyEventObjects, ','));
                    setting.setPackageEventType(addBirdNotification(setting.getPackageEventType()));
                    messageNotificationSettingsService.updateMessageNotificationSettings(setting);
                }
            } else {
                if (aiNotifyEventObjects.remove(AiObjectEnum.BIRD.getName())) {
                    setting.setEventObjects(StringUtils.join(aiNotifyEventObjects, ','));
                    setting.setPackageEventType(removeBirdNotification(setting.getPackageEventType()));
                    messageNotificationSettingsService.updateMessageNotificationSettings(setting);
                }
            }
        }
        return Result.Success();
    }

    private static String addBirdNotification(String eventTypes){
        if(eventTypes == null){
            return "bird_any";
        }
        Set<String> newEventTypes = new HashSet<>(List.of(eventTypes.split(",")));
        newEventTypes.add("bird_any");
        return StringUtils.join(newEventTypes, ",");
    }

    private static String removeBirdNotification(String eventTypes){
        if(eventTypes == null){
            return null;
        }
        Set<String> newEventTypes = new HashSet<>(List.of(eventTypes.split(",")));
        newEventTypes.remove("bird_any");
        return StringUtils.join(newEventTypes, ",");
    }

    @SentinelResource("queryVideoPossibleSubcategory")
    public Result<List<PossibleSubcategoryVO>> queryVideoPossibleSubcategory(Integer userId, String traceId, String language) {
        LibraryStatusTb libraryStatus = libraryStatusService.selectLibraryStatusByTraceIdAndUserId(traceId, userId);
        if (libraryStatus == null) {
            return Result.Error(ResultCollection.NO_LIBRARY_ACCESS, "账号无权访问此视频!");
        }
        List<PossibleSubcategoryVO> voList = new LinkedList<>();
        DeviceLibraryViewDO library = libraryService.selectLibraryViewByTraceId(userId, traceId);
        if (library == null || !JSON.isValidArray(library.getEventInfo())) {
            return new Result<>(voList);
        }
        List<AiEvent> events = JSON.parseArray(library.getEventInfo(), AiEvent.class);
        for (AiEvent event : events) {
            if (CollectionUtils.isEmpty(event.getPossibleSubcategory())) continue;
            final String signedImageUrl = s3Service.preSignUrl(event.getSummaryUrl());
            for (PossibleSubcategory subcategory : event.getPossibleSubcategory()) {
                List<BirdName> birdNames = searchBirdNameByStdName(subcategory.getName());
                if (birdNames.isEmpty()) {
                    com.addx.iotcamera.util.LogUtil.error(log, "queryVideoPossibleSubcategory 发现学名未知的鸟类! possibleSubcategory={}", JSON.toJSONString(subcategory));
                    continue;
                }
                PossibleSubcategoryVO vo = PossibleSubcategoryVO.from(birdNames.get(0), language, subcategory.getConfidence(), signedImageUrl);
                if (vo.getMatchName() == null) {
                    com.addx.iotcamera.util.LogUtil.error(log, "queryVideoPossibleSubcategory 发现无法翻译的鸟类! birdName={},language={}", JSON.toJSONString(birdNames.get(0)), language);
                    continue;
                }
                voList.add(vo);
            }
        }
        return new Result(voList);
    }

}
