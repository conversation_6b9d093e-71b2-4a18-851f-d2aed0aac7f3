package com.addx.iotcamera.service.deviceplatform.alexa;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.device.DeviceStatusDO;
import com.addx.iotcamera.config.AlexaWhiteConfig;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.deviceplatform.OAuth2Service;
import com.addx.iotcamera.service.deviceplatform.alexa.safemo.AlexaSafemoRequestService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.snowplow.alexa.AlexaSafemoSnowPlowTracker;
import com.addx.iotcamera.util.DeviceCodecUtil;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.RedisUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.addx.iotcamera.constants.AlexaSafemoEventType.ACCOUNT_LINK_SUCCESS;

/**
 * <AUTHOR>
 * <p>
 * alexa service
 * 获取账户link状态、设备接入alexa权限相关
 */
@Service
@Slf4j
public class AlexaService {

    @Autowired(required = false)
    private volatile AlexaWhiteConfig alexaWhiteConfig;

    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    @Autowired(required = false)
    private RedisUtil redisUtil;

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired(required = false)
    private DeviceInfoService deviceInfoService;

    @Autowired(required = false)
    private DeviceConfigService deviceConfigService;

    @Autowired(required = false)
    private DeviceStatusService deviceStatusService;

    @Autowired(required = false)
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired(required = false)
    private UserService userService;

    @Autowired(required = false)
    private OAuth2Service oauth2Service;

    @Autowired
    private AlexaSafemoRequestService alexaSafemoRequestService;

    private final static String REDIS_DEVICE_ACCESS_ALEXA_LOCK_KEY_PATTERN = "device_can_access_alexa_lock_{serialNumber}";

    private final static String REDIS_DEVICE_ACCESS_ALEXA_PATTERN = "device_can_access_alexa_{serialNumber}";

    private final static String REDIS_USER_LINKED_LOCK_KEY_PATTERN = "user_linked_alexa_lock_{userId}";

    private final static String REDIS_USER_LINKED_PATTERN = "user_linked_alexa_{userId}";

    private final static String REDIS_USER_AMAZON_TOKEN_LOCK_KEY_PATTERN = "amazon_token_lock_{userId}";

    public static final String KEY_USER_ID = "userId";
    public static final String KEY_SERIAL_NUMBER = "serialNumber";

    public static final String KEY_CLIENT_ID = "client_id";
    public static final String KEY_CLIENT_SECRET = "client_secret";
    public static final String KEY_TOKEN_SERVER = "token_server";

    public static final String KEY_ACCESS_TOKEN = "access_token";
    public static final String KEY_REFRESH_TOKEN = "refresh_token";

    public static final String KEY_GRANT_TYPE = "grant_type";
    public static final String KEY_TOKEN_TYPE = "token_type";

    public static final String KEY_EXPIRES_IN = "expires_in";
    public static final String KEY_EXPIRES = "expires";

    public static final String KEY_AMAZON_TOKEN = "amazon_token";

    public static final String KEY_EVENT_GATEWAY_ENDPOINT = "alexa-eventgateway-endpoint";

    /**
     * 判断设备是否可以接入alexa
     * <p>
     * 固件支持
     * sn号有权限
     *
     * @param deviceDO
     * @return
     */
    public boolean canAccessAlexa(DeviceDO deviceDO) {
        return canFirmwareSupport(deviceDO) && canSnSupport(deviceDO.getSerialNumber(), deviceDO.getUserSn());
    }

    public boolean canAccessAlexaUseCache(DeviceDO deviceDO) {
        return canFirmwareSupport(deviceDO) && canSnSupportUseCache(deviceDO.getSerialNumber(), deviceDO.getUserSn());
    }

    public boolean canFirmwareSupport(DeviceDO deviceDO) {
        return deviceDO.getDeviceSupport() != null && deviceDO.getDeviceSupport().getSupportAlexaWebrtc().intValue() == 1;
    }

    public boolean canSnSupport(String sn, String userSn) {
        // 先从缓存获取
        String redisCanAccessAlexaKey = REDIS_DEVICE_ACCESS_ALEXA_PATTERN.replace("{serialNumber}", sn);

        boolean deviceCanAccessAlexa = false;

        // 没有则执行计算，采用悲观锁避免单设备并发执行
        String redisCanAccessAlexaLockKey = REDIS_DEVICE_ACCESS_ALEXA_LOCK_KEY_PATTERN.replace("{serialNumber}", sn);
        long getLockTime = redisUtil.tryLock(redisCanAccessAlexaLockKey);
        try {
            deviceCanAccessAlexa = getDeviceCanAccessAlexa(sn, userSn);
            redisService.set(redisCanAccessAlexaKey, deviceCanAccessAlexa ? "1" : "0", 600);
        } finally {
            redisUtil.unlock(redisCanAccessAlexaLockKey, getLockTime);
        }
        return deviceCanAccessAlexa;
    }

    public boolean canSnSupportUseCache(String sn, String userSn) {
        // 先从缓存获取
        String redisCanAccessAlexaKey = REDIS_DEVICE_ACCESS_ALEXA_PATTERN.replace("{serialNumber}", sn);
        if (redisService.containsKey(redisCanAccessAlexaKey)) {
            return "1".equalsIgnoreCase(redisService.get(redisCanAccessAlexaKey));
        }

        boolean deviceCanAccessAlexa = false;

        // 没有则执行计算，采用悲观锁避免单设备并发执行
        String redisCanAccessAlexaLockKey = REDIS_DEVICE_ACCESS_ALEXA_LOCK_KEY_PATTERN.replace("{serialNumber}", sn);
        long getLockTime = redisUtil.tryLock(redisCanAccessAlexaLockKey);
        try {
            if (redisService.containsKey(redisCanAccessAlexaKey)) {
                deviceCanAccessAlexa = "1".equalsIgnoreCase(redisService.get(redisCanAccessAlexaKey));
            } else {
                deviceCanAccessAlexa = getDeviceCanAccessAlexa(sn, userSn);
            }
            redisService.set(redisCanAccessAlexaKey, deviceCanAccessAlexa ? "1" : "0", 600);
        } finally {
            redisUtil.unlock(redisCanAccessAlexaLockKey, getLockTime);
        }
        return deviceCanAccessAlexa;
    }

    public boolean getDeviceCanAccessAlexa(String sn, String userSn) {
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(sn, userSn);
        boolean deviceCanAccessAlexa = deviceManufactureTableDO != null ? (Objects.equals(deviceManufactureTableDO.getSupportAlexa(), 1)
                || alexaWhiteConfig.getWhiteSnSet().contains(deviceManufactureTableDO.getSerialNumber())
                || alexaWhiteConfig.getWhiteSnSet().contains(deviceManufactureTableDO.getUserSn())
                || (deviceManufactureTableDO.getRegisterModelNo() != null && alexaWhiteConfig.getWhiteModelNoSet().contains(deviceManufactureTableDO.getRegisterModelNo())) // 衍生型号 eg:SS1211W1
                || (deviceManufactureTableDO.getModelNo() != null && alexaWhiteConfig.getWhiteModelNoSet().contains(deviceManufactureTableDO.getModelNo())) // 源型号 eg：SS121
        ) : (alexaWhiteConfig.getWhiteSnSet().contains(sn) || alexaWhiteConfig.getWhiteSnSet().contains(userSn));
        log.debug("getDeviceCanAccessAlexa end! sn={},userSn={},deviceCanAccessAlexa={},deviceManufactureTableDO={}", sn, userSn, deviceCanAccessAlexa, JSON.toJSONString(deviceManufactureTableDO));
        return deviceCanAccessAlexa;
    }

    /**
     * 判断用户是否link至alexa
     *
     * @param userId
     * @return
     */
    public boolean isUserLinked(String tenantId, Integer userId) {
        // 先判断是否支持tenantId
        if (!isSupportTenantId(tenantId)) {
            return false;
        }

        // 先从缓存获取
        String redisUserLinkedKey = REDIS_USER_LINKED_PATTERN.replace("{userId}", String.valueOf(userId));

        boolean userLinked = false;

        // 没有则执行计算，采用悲观锁避免单用户并发执行
        String redisUserLinkedLockKey = REDIS_USER_LINKED_LOCK_KEY_PATTERN.replace("{userId}", String.valueOf(userId));
        long getLockTime = redisUtil.tryLock(redisUserLinkedLockKey);
        try {
            Map<String, Object> amazonTokenAndEventGatewayMap = getAmazonTokenAndEventGateway(String.valueOf(userId));
            if (!CollectionUtils.isEmpty(amazonTokenAndEventGatewayMap)) {
                String eventGatewayEndpoint = amazonTokenAndEventGatewayMap.get(KEY_EVENT_GATEWAY_ENDPOINT).toString();
                if (eventGatewayEndpoint == null) return false;
                String accessToken = amazonTokenAndEventGatewayMap.containsKey(KEY_ACCESS_TOKEN) ? amazonTokenAndEventGatewayMap.get(KEY_ACCESS_TOKEN).toString() : null;
                if (accessToken == null) return false;
                Map<String, String> headersMap = new HashMap<>();
                headersMap.put("Authorization", "Bearer " + accessToken);
                headersMap.put("Content-Type", "application/json;charset=utf-8");

                ResponseEntity response = HttpUtils.httpsPost(headersMap, "{}", eventGatewayEndpoint);
                if (response.getStatusCodeValue() != 401) {
                    userLinked = true;
                }
            }

            redisService.set(redisUserLinkedKey, userLinked ? "1" : "0", 600);
        } finally {
            redisUtil.unlock(redisUserLinkedLockKey, getLockTime);
        }
        return userLinked;
    }

    public boolean isUserLinkedUseCache(String tenantId, Integer userId) {
        // 先判断是否支持tenantId
        if (!isSupportTenantId(tenantId)) {
            return false;
        }

        // 先从缓存获取
        String redisUserLinkedKey = REDIS_USER_LINKED_PATTERN.replace("{userId}", String.valueOf(userId));
        if (redisService.containsKey(redisUserLinkedKey)) {
            return "1".equalsIgnoreCase(redisService.get(redisUserLinkedKey));
        }

        boolean userLinked = false;

        // 没有则执行计算，采用悲观锁避免单用户并发执行
        String redisUserLinkedLockKey = REDIS_USER_LINKED_LOCK_KEY_PATTERN.replace("{userId}", String.valueOf(userId));
        long getLockTime = redisUtil.tryLock(redisUserLinkedLockKey);
        try {
            if (redisService.containsKey(redisUserLinkedKey)) {
                userLinked = "1".equalsIgnoreCase(redisService.get(redisUserLinkedKey));
            } else {
                Map<String, Object> amazonTokenAndEventGatewayMap = getAmazonTokenAndEventGateway(String.valueOf(userId));
                if (!CollectionUtils.isEmpty(amazonTokenAndEventGatewayMap)) {
                    String eventGatewayEndpoint = amazonTokenAndEventGatewayMap.get(KEY_EVENT_GATEWAY_ENDPOINT).toString();
                    if (eventGatewayEndpoint == null) return false;
                    String accessToken = amazonTokenAndEventGatewayMap.containsKey(KEY_ACCESS_TOKEN) ? amazonTokenAndEventGatewayMap.get(KEY_ACCESS_TOKEN).toString() : null;
                    if (accessToken == null) return false;
                    Map<String, String> headersMap = new HashMap<>();
                    headersMap.put("Authorization", "Bearer " + accessToken);
                    headersMap.put("Content-Type", "application/json;charset=utf-8");

                    ResponseEntity response = HttpUtils.httpsPost(headersMap, "{}", eventGatewayEndpoint);
                    if (response.getStatusCodeValue() != 401) {
                        userLinked = true;
                    }
                }
            }

            redisService.set(redisUserLinkedKey, userLinked ? "1" : "0", 600);
        } finally {
            redisUtil.unlock(redisUserLinkedLockKey, getLockTime);
        }
        return userLinked;
    }

    public boolean isSupportTenantId(String tenantId) {
        return StringUtils.isEmpty(tenantId) ? true : alexaWhiteConfig.getWhiteTenantIdSet().contains(tenantId);
    }

    /**
     * 获取user 调用amazon api的token信息
     *
     * @param oauthClientId
     * @return
     */
    public Map<String, Object> getAmazonTokenAndEventGateway(String oauthClientId) {
        String additionalInformation = oauth2Service.queryAdditionalInfo(oauthClientId);

        if (StringUtils.isEmpty(additionalInformation)) {
            return null;
        }

        JSONObject additionalInformationJSON = JSON.parseObject(additionalInformation);
        if (!additionalInformationJSON.containsKey(KEY_AMAZON_TOKEN)) {
            return null;
        }

        if (!additionalInformationJSON.containsKey(KEY_EVENT_GATEWAY_ENDPOINT)) {
            return null;
        }

        JSONObject currentTokenJSON = additionalInformationJSON.getJSONObject(KEY_AMAZON_TOKEN);
        long expires = currentTokenJSON.getLong(KEY_EXPIRES);

        // 如果token未过期，则返回
        if (System.currentTimeMillis() < expires) {
            Map<String, Object> queryTokenMap = new HashMap<>();
            queryTokenMap.put(KEY_ACCESS_TOKEN, currentTokenJSON.getString(KEY_ACCESS_TOKEN));
            queryTokenMap.put(KEY_REFRESH_TOKEN, currentTokenJSON.getString(KEY_REFRESH_TOKEN));
            queryTokenMap.put(KEY_EXPIRES_IN, currentTokenJSON.getString(KEY_EXPIRES_IN));
            queryTokenMap.put(KEY_TOKEN_TYPE, currentTokenJSON.getString(KEY_TOKEN_TYPE));
            queryTokenMap.put(KEY_EXPIRES, currentTokenJSON.getString(KEY_EXPIRES));
            queryTokenMap.put(KEY_EVENT_GATEWAY_ENDPOINT, additionalInformationJSON.getString(KEY_EVENT_GATEWAY_ENDPOINT));
            return queryTokenMap;
        }

        // 不用锁避免单用户并发执行
        String redisUserAmazonTokenLockKey = REDIS_USER_AMAZON_TOKEN_LOCK_KEY_PATTERN.replace("{userId}", String.valueOf(oauthClientId));
        if (!redisService.setIfAbsent(redisUserAmazonTokenLockKey, String.valueOf(System.currentTimeMillis()), 10, TimeUnit.SECONDS)) {
            com.addx.iotcamera.util.LogUtil.warn(log, "Other thread is updating oauth token, return null");
            return null;
        }

        Map<String, Object> queryTokenMap = new HashMap<>();
        try {
            String amazonTokenServer = currentTokenJSON.getString(KEY_TOKEN_SERVER);
            String amazonClientId = currentTokenJSON.getString(KEY_CLIENT_ID);
            String amazonClientSecret = currentTokenJSON.getString(KEY_CLIENT_SECRET);
            String refreshToken = currentTokenJSON.getString(KEY_REFRESH_TOKEN);

            //构造请求body参数
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put(KEY_GRANT_TYPE, KEY_REFRESH_TOKEN);
            requestBodyMap.put(KEY_REFRESH_TOKEN, refreshToken);
            requestBodyMap.put(KEY_CLIENT_ID, amazonClientId);
            requestBodyMap.put(KEY_CLIENT_SECRET, amazonClientSecret);

            log.info("start get access_token with refresh_token oauthClientId:{}", oauthClientId);
            String response = HttpUtils.httpsPost(JsonUtil.toJson(requestBodyMap), amazonTokenServer);
            queryTokenMap = JsonUtil.fromJson(response, HashMap.class);
            log.info("get access_token oauthClientId:{} queryTokenMap:{}", oauthClientId, queryTokenMap);

            if (!MapUtils.isEmpty(queryTokenMap)) {
                //更新到数据库
                Map<String, Object> saveTokenMap = new HashMap<>(queryTokenMap);
                saveTokenMap.put(KEY_TOKEN_SERVER, amazonTokenServer);
                saveTokenMap.put(KEY_CLIENT_ID, amazonClientId);
                saveTokenMap.put(KEY_CLIENT_SECRET, amazonClientSecret);
                long expiresInSeconds = Long.valueOf(saveTokenMap.get(KEY_EXPIRES_IN).toString());
                saveTokenMap.put(KEY_EXPIRES, System.currentTimeMillis() + (expiresInSeconds - 60 * 10) * 1000);

                additionalInformationJSON.put(KEY_AMAZON_TOKEN, saveTokenMap);

                additionalInformation = JsonUtil.toJson(additionalInformationJSON);

                log.info("save new access_token oauthClientId:{}", oauthClientId);
                Connection connection = jdbcTemplate.getDataSource().getConnection();
                if (!connection.getAutoCommit()) {
                    connection.setAutoCommit(true);
                }
                jdbcTemplate.update("UPDATE `oauth2`.`oauth_client_details` SET `additional_information`=? WHERE `client_id`=?", new String[]{additionalInformation, oauthClientId});
                connection.close();
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(log, "get access_token failed oauthClientId:{}", oauthClientId);
        }

        // access_token 获取失败
        if (MapUtils.isEmpty(queryTokenMap)) {
            return null;
        }

        queryTokenMap.put(KEY_EVENT_GATEWAY_ENDPOINT, additionalInformationJSON.getString(KEY_EVENT_GATEWAY_ENDPOINT));

        return queryTokenMap;
    }

    /**
     * accountLinkSuccess触发设备编码切换
     *
     * @param userId
     */
    public void accountLinkSuccess(Integer userId) {
        String tenantId = userService.queryTenantIdById(userId);
        if (AppConstants.TENANTID_SAFEMO.equals(tenantId)) {
            AlexaSafemoSnowPlowTracker.eventTrack(ACCOUNT_LINK_SUCCESS, userId.toString(), null);
            log.info("AlexaSafemo accountLinkSuccess safemo userId:{}", userId);
            alexaSafemoRequestService.sendSwitchDeviceCodecRequest(userId);
            AlexaSafemoSnowPlowTracker.successTrack(ACCOUNT_LINK_SUCCESS, userId.toString(), null);
            return;
        }
        String redisUserLinkedKey = REDIS_USER_LINKED_PATTERN.replace("{userId}", String.valueOf(userId));
        redisService.set(redisUserLinkedKey, "1", 600);

        List<DeviceDO> deviceDOList = deviceInfoService.listDevicesByUserId(userId);
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList.stream().filter(deviceDO -> deviceDO.getDeviceSupport() != null && deviceDO.getDeviceSupport().getSupportChangeCodec() != null && deviceDO.getDeviceSupport().getSupportChangeCodec().intValue() == 1).filter(deviceDO -> canAccessAlexa(deviceDO)).forEach(deviceDO -> {
                DeviceConfigRequest deviceConfigRequest = new DeviceConfigRequest().setSerialNumber(deviceDO.getSerialNumber()).setUserId(userId).setDefaultCodec(DeviceCodecUtil.H264);
                log.info("accountLinkSuccess updateDefaultCodec userId:{} serialNumber:{}", userId, deviceDO.getSerialNumber());
                deviceConfigService.updateDefaultCodec(deviceConfigRequest);
            });
            deviceDOList.stream().filter(deviceDO -> deviceDO.getDeviceSupport() == null || deviceDO.getDeviceSupport().getSupportChangeCodec() == null || deviceDO.getDeviceSupport().getSupportChangeCodec().intValue() != 1).filter(deviceDO -> canAccessAlexa(deviceDO) && DeviceCodecUtil.isMatchH264(deviceDO.getCodec())).forEach(deviceDO -> {
                DeviceStatusDO deviceStatusDO = deviceStatusService.queryDeviceStatusBySerialNumber(deviceDO.getSerialNumber());
                if (deviceStatusDO != null && !org.apache.commons.lang3.StringUtils.contains(deviceStatusDO.getLinkedPlatforms(), "alexa")) {
                    String newLinkedPlatforms = StringUtils.isEmpty(deviceStatusDO.getLinkedPlatforms()) ? "alexa" : String.join(",", deviceStatusDO.getLinkedPlatforms(), "alexa");
                    deviceStatusDO.setLinkedPlatforms(newLinkedPlatforms);
                    log.info("accountLinkSuccess update newLinkedPlatforms:{} userId:{} serialNumber:{}", newLinkedPlatforms, userId, deviceDO.getSerialNumber());
                    deviceStatusService.saveDeviceStatus(deviceStatusDO);
                }
            });
        }
    }
}
