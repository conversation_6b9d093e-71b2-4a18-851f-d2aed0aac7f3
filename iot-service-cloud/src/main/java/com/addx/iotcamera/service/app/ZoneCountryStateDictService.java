package com.addx.iotcamera.service.app;

import com.addx.iotcamera.bean.db.manage.ZoneCountryStateDictDO;
import com.addx.iotcamera.dao.manage.ZoneCountryStateDictDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("appZoneCountryStateDictService")
public class ZoneCountryStateDictService {
    @Autowired
    private ZoneCountryStateDictDAO zoneCountryStateDictDAO;


    @Cacheable(value = "countryStates", key = "#language")
    public Map<String, List<ZoneCountryStateDictDO>> getStatesByCountryAndLanguage(String language) {
        return zoneCountryStateDictDAO.queryCountryState(language)
                .stream()
                .collect(Collectors.groupingBy(ZoneCountryStateDictDO::getCountryNo));
    }
}
