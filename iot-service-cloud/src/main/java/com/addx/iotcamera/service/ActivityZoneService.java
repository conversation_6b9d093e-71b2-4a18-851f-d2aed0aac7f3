package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.ActivityZoneBean;
import com.addx.iotcamera.bean.app.ActivityZoneListRequest;
import com.addx.iotcamera.bean.app.ActivityZoneRequest;
import com.addx.iotcamera.bean.db.ActivityZoneDO;
import com.addx.iotcamera.bean.domain.library.ActivityZone;
import com.addx.iotcamera.dao.ActivityZoneDAO;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.ai.service.IActivityZoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@Service
@Slf4j
public class ActivityZoneService implements IActivityZoneService {

    @SuppressWarnings("all")
    @Autowired
    private ActivityZoneDAO activityZoneDAO;

    @Autowired
    private DeviceAuthService deviceAuthService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private VideoSearchService videoSearchService;

    @Autowired
    private OpenApiConfigService openApiConfigService;

    public Result getActivityZones(@NotNull ActivityZoneRequest request, @NotNull Integer userId) {
        String serialNumber = request.getSerialNumber();
        // 不需要admin权限
        Integer adminCheck = deviceAuthService.checkDeviceCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        List<ActivityZoneDO> res = activityZoneDAO.getActivityZonesBySerailNumber(serialNumber);
        return Result.ListResult(res);
    }

    public List<ActivityZoneDO> getActivityZones(String serialNumber) {
        return activityZoneDAO.getActivityZonesBySerailNumber(serialNumber);
    }

    @CacheEvict(value = "activityZone", key = "#request.serialNumber")
    public Result updateActivityZone(@NotNull ActivityZoneRequest request, @NotNull Integer userId) {
        String serialNumber = request.getSerialNumber();

        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        ActivityZoneDO activityZoneDO = ActivityZoneDO.builder()
                .id(request.getId())
                .serialNumber(request.getSerialNumber())
                .zoneName(request.getZoneName())
                .vertices(request.getVertices())
                .needAlarm(request.getNeedAlarm())
                .needPush(request.getNeedPush())
                .needRecord(request.getNeedRecord())
                .deleted(false)
                .build();
        Integer res = activityZoneDAO.updateActivityZone(activityZoneDO);
        videoSearchService.clearSearchOptionCache(serialNumber);
        openApiConfigService.publishDeviceConfigAsync(serialNumber,null, userId);
        return Result.SqlOperationResult(res);
    }

    @CacheEvict(value = "activityZone", key = "#request.serialNumber")
    public Result insertActivityZone(@NotNull ActivityZoneRequest request, @NotNull Integer userId) {
        String serialNumber = request.getSerialNumber();

        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        ActivityZoneDO activityZoneDO = ActivityZoneDO.builder()
                .serialNumber(request.getSerialNumber())
                .zoneName(request.getZoneName())
                .vertices(request.getVertices())
                .needAlarm(request.getNeedAlarm())
                .needPush(request.getNeedPush())
                .needRecord(request.getNeedRecord())
                .deleted(false)
                .build();
        Integer res = activityZoneDAO.insertActivityZone(activityZoneDO);
        videoSearchService.clearSearchOptionCache(serialNumber);
        return Result.KVResult("id", activityZoneDO.getId());
    }

    @CacheEvict(value = "activityZone", key = "#activityZoneListRequest.serialNumber")
    public Result batchInsertActivityZone(ActivityZoneListRequest activityZoneListRequest, Integer userId) {

        String serialNumber = activityZoneListRequest.getSerialNumber();
        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        //简单处理，直接删除之前的zone，再插入新的， 使用不DAO不适用service 防止重复发送设备更行配置通知
        activityZoneDAO.deleteActivityZoneBySerialNumber(serialNumber);

        List<Integer> ids = new ArrayList<>();
        for (ActivityZoneBean request: activityZoneListRequest.getList()) {
            ActivityZoneDO activityZoneDO = ActivityZoneDO.builder()
                    .id(request.getId())
                    .serialNumber(activityZoneListRequest.getSerialNumber())
                    .zoneName(request.getZoneName())
                    .vertices(request.getVertices())
                    .needAlarm(request.getNeedAlarm())
                    .needPush(request.getNeedPush())
                    .needRecord(request.getNeedRecord())
                    .deleted(false)
                    .build();
            if(request.getId() == null) {
                activityZoneDAO.insertActivityZone(activityZoneDO);
            }else {
                activityZoneDAO.updateActivityZone(activityZoneDO);
            }
            ids.add(activityZoneDO.getId());
        }
        videoSearchService.clearSearchOptionCache(serialNumber);
        openApiConfigService.publishDeviceConfigAsync(serialNumber,null, userId);
        return Result.KVResult("ids", ids);

    }


    @CacheEvict(value = "activityZone", key = "#request.serialNumber")
    public Result deleteActivityZone(@NotNull ActivityZoneRequest request, @NotNull Integer userId) {
        String serialNumber = request.getSerialNumber();

        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        ActivityZoneDO activityZoneDO = ActivityZoneDO.builder()
                .id(request.getId())
                .serialNumber(request.getSerialNumber())
                .build();
        Integer res = activityZoneDAO.deleteActivityZone(activityZoneDO);
        videoSearchService.clearSearchOptionCache(serialNumber);
        openApiConfigService.publishDeviceConfigAsync(serialNumber,null, userId);
        return Result.SqlOperationResult(res);
    }

    @CacheEvict(value = "activityZone", key = "#serialNumber")
    public Result deleteActivityZoneBySerialNumber(String serialNumber) {
        activityZoneDAO.deleteActivityZoneBySerialNumber(serialNumber);
        videoSearchService.clearSearchOptionCache(serialNumber);
        openApiConfigService.publishDeviceConfigAsync(serialNumber,null, null);
        return Result.Success();
    }

    /**
     * 获取activityZone-ai
     *
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "activityZone", key = "#serialNumber", unless = "#result==null")
    public List<ActivityZone> queryActivityZone(String serialNumber) {
        log.info("query serialNumber activity zone {}", serialNumber);
        return activityZoneDAO.getActivityZonesBySerialNumberForAi(serialNumber);
    }

    /**
     * 获取activityZone

     * @return
     */
    @Cacheable(value = "activityZoneById", key = "#activityId", unless = "#result==null")
    public ActivityZoneDO queryActivityZoneById(Integer activityId) {
        return activityZoneDAO.getActivityZonesById(activityId);
    }

    /**
     * 获取用户所属的activityZone
     *
     * @param userId
     * @return
     */
    public List<ActivityZoneDO> queryUserActivityZone(Integer userId) {
        List<String> serialNumberList = userRoleService.getSerialNumbersByUserId(userId);
        if (CollectionUtils.isEmpty(serialNumberList)) {
            return Lists.newArrayList();
        }
        return activityZoneDAO.getActivityZonesByUserId(serialNumberList);
    }
}
