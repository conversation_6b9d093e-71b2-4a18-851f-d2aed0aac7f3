package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.VideoReportEvent;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.dynamic.CopyWrite;
import com.addx.iotcamera.bean.msg.VideoMsgEntity;
import com.addx.iotcamera.bean.msg.VideoReportEventMsgEntity;
import com.addx.iotcamera.bean.msg.xinge.VideoCustomerContentMessage;
import com.addx.iotcamera.bean.video.PirAlarmDelayDO;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.enums.PushTypeEnums;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.publishers.notification.XingePushArgs;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.message.MessagePushManageService;
import com.addx.iotcamera.service.xinge.PushXingeService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.tencent.xinge.bean.Message;
import com.tencent.xinge.bean.MessageAndroid;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.LanguageBase;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.VideoTagEnumUtil;
import org.addx.iot.domain.extension.ai.model.AiEvent;
import org.addx.iot.domain.extension.ai.vo.AiTaskResult;
import org.addx.iot.domain.extension.entity.DeviceAiSwitch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.video.PushProcessRecorder.EVENT_MOTION;
import static com.addx.iotcamera.bean.video.PushProcessRecorder.ORDER_MOTION;
import static com.addx.iotcamera.constants.DeviceInfoConstants.DOORBELL_EVENT;
import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_PIR_START_TO_FIRST_PUSH_AI_MSG;
import static com.addx.iotcamera.service.video.VideoCache.Flag.LIBRARY_CREATED;
import static org.addx.iot.common.constant.AppConstants.TENANTID_SAFEMO;

@Slf4j(topic = "videoGenerate")
@Component
public class VideoNotifyService {

    @Autowired
    private VideoCacheService videoCacheService;
    @Autowired
    private S3Service s3Service;
    @Autowired
    @Lazy
    private NotificationService notificationService;
    @Autowired
    private MessagePushManageService messagePushManageService;
    @Autowired(required = false)
    private DevicePlatformEventPublisher devicePlatformEventPublisher;
    @Autowired
    @Lazy
    private PushXingeService pushXingeService;
    @Autowired
    @Lazy
    private PushService pushService;
    @Autowired
    @Lazy
    private CopyWrite copyWrite;
    @Autowired
    private TimeTicker timeTicker;
    @Autowired
    private VideoReportLogService videoReportLogService;
    @Autowired
    private AiAssistService aiAssistService;
    @Autowired
    private CenterNotifyConfig centerNotifyConfig;
    @Setter
    @Autowired
    private Gson gson;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Value("${checkPushIntent:false}")
    private Boolean checkPushIntent;

    @Value("${notifyCheckEnableOtherSwicth:true}")
    private Boolean notifyCheckEnableOtherSwicth = true;

    @Value("${notification.notCheckVideoCreatedUserIds}")
    private String notCheckVideoCreatedUserIds;

    @Resource
    @Lazy
    private UserRoleService userRoleService;

    @Resource
    private PushInfoService pushInfoService;

    @Resource
    @Lazy
    private DeviceService deviceService;
    @Resource
    @Lazy
    private UserService userService;

    @Resource
    @Lazy
    private DeviceManualService deviceManualService;

    @Resource
    @Lazy
    private FactoryDataQueryService factoryDataQueryService;

    private boolean getNeedCheckVideoCreated(Integer userId){
        return userId == null || notCheckVideoCreatedUserIds == null || !(new HashSet<>(Arrays.asList(notCheckVideoCreatedUserIds.split(","))).contains(userId.toString()));
    }

    public void sendMotionNotify(VideoCache video) {
        log.debug("sendMotionNotify video {}", JSON.toJSONString(video));
        if (!video.isEventRecordingMainView()) {
            recordMotionPushMetric(video, "skip_not_main_view");
            return;
        }
        if(!video.getFlag(LIBRARY_CREATED) && getNeedCheckVideoCreated(video.getAdminId())) { // 视频没有创建成功的Ai汇总事件结果丢弃
            log.info("sendMotionNotify video {} not create success, drop message", JSON.toJSONString(video));
            recordMotionPushMetric(video, "skip_no_library");
            return;
        }
        if(!video.getPirNotifyPushedKeys().isEmpty()){
            log.info("sendMotionNotify video {} have pushed other notification before, drop this motion message", JSON.toJSONString(video));
            recordMotionPushMetric(video, "skip_motion_as_other_push");
            return;
        }
        if (!checkEnableOtherPass(video)) {
            video.getPushProcessRecorder().recordNotPushResult(ORDER_MOTION, "未推送，未打开enableOther开关");
            log.debug("sendMotionNotify not open enableOther switch video {}", JSON.toJSONString(video));
            recordMotionPushMetric(video, "skip_no_enable_other");
            return;
        }
        if (!video.getIsNotify()) {
            video.getPushProcessRecorder().recordNotPushResult(ORDER_MOTION, "非vip且pushIgnored为true");
            log.debug("sendMotionNotify not vip push ignored is true video {}", JSON.toJSONString(video));
            recordMotionPushMetric(video, "skip_notify_off");
            return; // 非vip时pushIgnore字段才生效
        }
        if (StringUtils.isBlank(video.getImageUrl())) {
            video.getPushProcessRecorder().recordNotPushResult(ORDER_MOTION, "推送时封面图url为空");
            log.debug("sendMotionNotify summary is null video {}", JSON.toJSONString(video));
            recordMotionPushMetric(video, "skip_no_image");
            return; // 如果先收到切片再收到图片，s3ImageUrl为空
        }
        final String signedImageUrl = s3Service.preSignUrl(video.getImageUrl());
        // 如果只推送普通运动， 那么发通知
        log.debug("准备推送PIR检测到运动 {} {}", video.getSerialNumber(), video.getTraceId());
        for (VideoCommonCache.UserSimple user : video.getUsers()) {
            final Integer userId = user.getId();
            final String pushedKey = userId + ":motion";
            if (video.getPirNotifyPushedKeys().contains(pushedKey)) { // 检查 userId:motion 是否推送过
//                video.getPushProcessRecorder().recordUserNotPushEvent(userId, ORDER_MOTION, EVENT_MOTION, "eventObject已经推送过了");
                log.debug("sendMotionNotify already pushed video {}", JSON.toJSONString(video));
                recordMotionPushMetric(video, "skip_already_pushed");
                return; // 1个分享用户 + 1个eventObject 只推送一次
            }
            final VideoCache.ExeStep isPushShiedStep = video.loggingStep("isPushShied");
            boolean pushShield = messagePushManageService.queryUserPushSwitch(userId,video.getSerialNumber());
            if (pushShield) {
                isPushShiedStep.exeEnd();
                video.getPushProcessRecorder().recordUserNotPushEvent(userId, ORDER_MOTION, EVENT_MOTION, "用户设置了消息免打扰");
                log.debug("用户{}设置了消息免打扰,traceId:{}", userId, video.getTraceId());
                recordMotionPushMetric(video, "skip_push_shield");
                return;
            }
            isPushShiedStep.exeEnd();
            if (devicePlatformEventPublisher != null && video.getAlexPushedUserIds().add(userId)) {
                Map<String, Object> deviceEventParamMap = new HashMap<>();
                deviceEventParamMap.put("userId", String.valueOf(userId));
                deviceEventParamMap.put("serialNumber", video.getSerialNumber());
                final VideoCache.ExeStep sendKafkaStep = video.loggingStep("sendKafka:MOTION_PIR_DETECTED");
                devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.MOTION_PIR_DETECTED, deviceEventParamMap);
                sendKafkaStep.exeEnd();
            }
            final VideoCache.ExeStep getPushInfoStep = video.loggingStep("getPushInfo");
            final PushInfo pushInfo = videoCacheService.getPushInfo(video, userId);
            if (pushInfo == null) {
                getPushInfoStep.exeEnd();
                video.getPushProcessRecorder().recordUserNotPushEvent(userId, ORDER_MOTION, EVENT_MOTION, "找不到用户的pushInfo");
                log.debug("sendMotionNotify push Info is null video {}", JSON.toJSONString(video));
                recordMotionPushMetric(video, "skip_no_push_info");
                continue; // 没有推送参数，跳过
            }
            getPushInfoStep.exeEnd();
            video.getPushProcessRecorder().recordUserPushEventValidSuccess(userId, ORDER_MOTION, EVENT_MOTION);
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setDeviceName(video.getDeviceName());
            deviceDO.setSerialNumber(video.getSerialNumber());
            log.debug("sendMotionNotify start push video {} pushInfo {} ", video, pushInfo);
            notificationService.pushPirNotify(pushInfo, user.toUserDO(), deviceDO, video.getTraceId(), signedImageUrl, "", video.getVideoUrl());
            recordMotionPushMetric(video, "success");
            video.getPirNotifyPushedKeys().add(pushedKey); // 设置 userId:motion 推送过
        }
    }

    /**
     * 发送4G设备通知x
     *
     * @param serialNumber
     * @param traceId
     */
    public void sendMotionNotify4G(String serialNumber, String traceId) {
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO.getPushIgnored()) {
            return;
        }

        List<Integer> userIds = userRoleService.findAllUsersForDevice(serialNumber);
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        for (Integer userId : userIds) {
            boolean pushShield = messagePushManageService.queryUserPushSwitch(userId,serialNumber);
            User user = userService.queryUserById(userId);

            if (pushShield) {
                log.debug("用户{}设置了消息免打扰,traceId:{}", userId, traceId);
                record4GMotionPushMetric(user.getTenantId(),serialNumber,"skip_push_shield");
                continue;
            }

            PushInfo pushInfo = pushInfoService.getPushInfo(userId);
            if (pushInfo == null) {
                record4GMotionPushMetric(user.getTenantId(),serialNumber,"skip_no_push_info");
                // 没有推送参数，跳过
                continue;
            }

            notificationService.pushPirNotify(pushInfo, user, deviceDO, traceId, "", "");
            record4GMotionPushMetric(user.getTenantId(),serialNumber,"success");
        }
    }

    private void recordMotionPushMetric(VideoCache video, String pushResult) {
        PrometheusMetricsUtil.getPushMsgCountOptional()
                .ifPresent(it -> it.labels(
                        PrometheusMetricsUtil.getHostName(),
                        video.getTenantId(),
                        "MOTION",
                        pushResult).inc());
    }

    private void record4GMotionPushMetric(String tenantId,String serialNumber, String pushResult) {
        PrometheusMetricsUtil.getPushMsgCountOptional()
                .ifPresent(it -> it.labels(
                        PrometheusMetricsUtil.getHostName(),
                        tenantId,
                        "MOTION",
                        pushResult).inc());
    }

    public void sendAIResultNotify(VideoCache video, AiTaskResult aiTaskResult) {
        log.debug("sendAIResultNotify video {} aiTaskResult {}", JSON.toJSONString(video), JSON.toJSONString(aiTaskResult));
        final Integer order = aiTaskResult.getOrder();
        if (CollectionUtils.isEmpty(aiTaskResult.getEvents())) {
            video.getPushProcessRecorder().recordNotPushResult(order, "没识别到事件");
            log.debug("sendAIResultNotify no event recognized aiTAskResult {}", JSON.toJSONString(aiTaskResult));
            return; // 没识别到事件
        }

        if(!video.getFlag(LIBRARY_CREATED) && getNeedCheckVideoCreated(video.getAdminId())
                && ( aiTaskResult.getAiEdgeResult() == null || !aiTaskResult.getAiEdgeResult())) { // 视频没有创建成功的Ai汇总事件结果丢弃
            log.info("sendMotionNotify video {} not create success, drop message", JSON.toJSONString(video));
            return;
        }

        if (BooleanUtils.isNotTrue(aiTaskResult.getAiEdgeResult()) && CollectionUtils.isNotEmpty(video.getAiEdgeEvents())) {
            // merge with ai edge events
            Map<AiObjectEnum, List<AiEvent>> aiEdgeEventObjectMap = new HashMap<>(video.getAiEdgeEvents().stream().collect(Collectors.groupingBy(AiEvent::getEventObject)));
            aiTaskResult.getEvents().forEach(event -> {
                aiEdgeEventObjectMap.remove(event.getEventObject());
            });
            aiEdgeEventObjectMap.entrySet().forEach(entry -> {
                //fix UnsupportedOperationException: null addAll
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    aiTaskResult.getEvents().addAll(entry.getValue());
                }
            });
        }

        for (AiEvent event : aiTaskResult.getEvents()) {
            if (!video.isMatchAnyActivityZone(Arrays.asList(event))) {
                video.getPushProcessRecorder().recordNotPushEvent(order, event, "不在activeZone中");
                log.debug("sendAIResultNotify not in activeZone video {} event {}", JSON.toJSONString(video), JSON.toJSONString(event));
                continue; // 识别到的事件 不在activeZone中
            }
            final String eventObjectName = event.getEventObject().getName();
            if (!video.getNotifyEventObjects().contains(eventObjectName)) {
                video.getPushProcessRecorder().recordNotPushEvent(order, event, "不在通知设置的eventObject范围中");
                log.debug(" sendAIResultNotify not in the eventType range of the notification 1 settings video {} pushedKey {}",
                        JSON.toJSONString(video), JSON.toJSONString(eventObjectName));
                continue; // 识别到的事件 不在通知设置的eventObject范围中
            }
            final String eventTypeName = event.getEventType().getName();
            final Set<String> allEventTypes = VideoTagEnumUtil.getAllEventTypesByEventObject(eventObjectName, video.getTenantId());
            if ((CollectionUtils.isNotEmpty(allEventTypes) && !video.getNotifyEventTypes().contains(eventTypeName))
                || (CollectionUtils.isEmpty(allEventTypes) && !video.getNotifyEventTypes().contains(String.format("%s_any",eventObjectName)))) {
                video.getPushProcessRecorder().recordNotPushEvent(order, event, "不在通知设置的eventType范围中");
                log.debug("sendAIResultNotify not in the eventType range of the notification 2 settings allEventTypes {} video {} eventTypeName {}",
                        JSON.toJSONString(allEventTypes), JSON.toJSONString(video), JSON.toJSONString(eventTypeName));
                continue; // 识别到的事件 不在通知设置的eventType范围中
            }
            if (AiObjectEnum.VEHICLE.equals(event.getEventObject()) && !video.getIsNotifyLabelId(event.getLabelId())) {
                video.getPushProcessRecorder().recordNotPushEvent(order, event, "不在需要通知的labelId范围中");
                log.debug("sendAIResultNotify not in the labelId range that needs to be notified video {} pushedKey {}", JSON.toJSONString(video), JSON.toJSONString(event));
                continue; // 识别到的车辆事件 不在需要通知的labelId范围中
            }
            final String videoEvent = video.getIsMergeMessage() ? notificationService.libraryEventLog(aiTaskResult) : null;
            for (VideoCommonCache.UserSimple user : video.getUsers()) {
                final Integer userId = user.getId();
                final String pushedKey = userId + ":" + eventObjectName + ":" + eventTypeName;
                if (video.getPirNotifyPushedKeys().contains(pushedKey)) { // 检查 userId:eventObject:eventType 是否推送过
//                    video.getPushProcessRecorder().recordUserNotPushEvent(userId, order, event, "eventObject已经推送过了");
                    log.debug("sendAIResultNotify pirNotifyPushedKeys contains pushedKey video {} pushedKey {}", JSON.toJSONString(video), pushedKey);
                    continue; // 1个分享用户 + 1个eventObject 只推送一次
                }
                final VideoCache.ExeStep isPushShiedStep = video.loggingStep("isPushShied");
                boolean pushShield = messagePushManageService.queryUserPushSwitch(userId,video.getSerialNumber());
                if (pushShield) {
                    isPushShiedStep.exeEnd();
                    video.getPushProcessRecorder().recordUserNotPushEvent(userId, order, event, "用户设置了消息免打扰");
                    log.debug("sendAIResultNotify 用户{}设置了消息免打扰,traceId:{}", userId, video.getTraceId());
                    continue;
                }
                isPushShiedStep.exeEnd();
                if (devicePlatformEventPublisher != null && video.getAlexPushedUserIds().add(userId)) {
                    Map<String, Object> deviceEventParamMap = new HashMap<>();
                    deviceEventParamMap.put("userId", String.valueOf(userId));
                    deviceEventParamMap.put("serialNumber", video.getSerialNumber());
                    devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.MOTION_PIR_DETECTED, deviceEventParamMap);
                }
                final VideoCache.ExeStep getPushInfoStep = video.loggingStep("getPushInfo");
                final PushInfo pushInfo = videoCacheService.getPushInfo(video, userId);
                if (pushInfo == null) {
                    getPushInfoStep.exeEnd();
                    video.getPushProcessRecorder().recordUserNotPushEvent(userId, order, event, "找不到用户的pushInfo");
                    log.debug("sendAIResultNotify no push arg video {}", JSON.toJSONString(video));
                    continue; // 没有推送参数，跳过
                }
                getPushInfoStep.exeEnd();
                video.getPushProcessRecorder().recordUserPushEventValidSuccess(userId, order, event);
                NotificationService.PushContext pushCtx = new NotificationService.PushContext().setUser(user.toUserDO())
                        .setAdminId(video.getAdminId()).setPushInfo(pushInfo);
                log.debug("sendAIResultNotify start push message aiTaskResult {}, videoEvent {}",
                        JSON.toJSONString(aiTaskResult), JSON.toJSONString(videoEvent));

                boolean pushSuccess = this.pushAiMessage(video, pushCtx, aiTaskResult, videoEvent);
                PrometheusMetricsUtil.getPushMsgCountOptional()
                        .ifPresent(it -> it.labels(
                                PrometheusMetricsUtil.getHostName(),
                                video.getTenantId(),
                                event.getEventObject() != null ? event.getEventObject().getName() : "UNKNOWN_EVENT",
                                pushSuccess ? "success" : "fail").inc());

                video.getPirNotifyPushedKeys().add(pushedKey); // 设置 userId:eventObject:eventType 推送过
                videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_PUSH_AI_MSG, aiTaskResult.getOrder(), timeTicker.readMillis());
            }
        }
    }

    public boolean pushAiMessage(VideoCache video, NotificationService.PushContext pushCtx, AiTaskResult aiTaskResult, String videoEvent) {
        Integer userId = pushCtx.getUser().getId();
        try {
            PushInfo pushInfo = pushCtx.getPushInfo();
            if (PushTypeEnums.PUSH_XINGE.getCode() == pushInfo.getMsgType()) {
                Message message = this.buildXingeVideoMessage(video, pushCtx, aiTaskResult, videoEvent);
                XingePushArgs xingePushArgs = XingePushArgs.builder()
                        .userId(userId)
                        .serialNumber(aiTaskResult.getSerialNumber())
                        .bundleName(pushInfo.getBundleName())
                        .traceId(aiTaskResult.getTraceId())
                        .build();
                pushXingeService.pushMessage(xingePushArgs, message);
            } else {
                VideoMsgEntity message = buildVideoMessage(video, pushCtx, aiTaskResult, videoEvent);
                pushService.pushVideoMessage(pushInfo, message);
            }
            return true;
        } catch (Throwable e) {
            video.getPushProcessRecorder().recordPushError(userId, aiTaskResult.getOrder(), aiTaskResult.getEvents(), e);
            com.addx.iotcamera.util.LogUtil.error(log, "pushAiMessage error! userId={},sn={},traceId={}", userId, aiTaskResult.getSerialNumber(), aiTaskResult.getTraceId(), e);
        }
        return false;
    }

    private Message buildXingeVideoMessage(VideoCache video, NotificationService.PushContext pushCtx, AiTaskResult aiTaskResult, String videoEvent) {
        Message message = new Message();

        VideoCustomerContentMessage videoCustomerContentMessage = new VideoCustomerContentMessage();

        String s3ImageUrl = s3Service.preSignUrl(aiTaskResult.getImageUrl());
        message.setXgMediaResources(s3ImageUrl);
        videoCustomerContentMessage.getLibrary().setImageUrl(s3ImageUrl);
        videoCustomerContentMessage.getLibrary().setLibraryId(null);
        String mediaUrl = s3Service.preSignUrl(video.getVideoUrl());
        videoCustomerContentMessage.getLibrary().setVideoUrl(mediaUrl);
        message.setXgMediaAudioResources(mediaUrl);

        User user = pushCtx.getUser();
        message.setTitle(this.queryVideoTitle(video, user, aiTaskResult, video.getDeviceName()));

        PirAlarmDelayDO pirAlarmDelayDO = notificationService.queryPirMessageTitle(user,video.getDeviceName(),video.getSerialNumber(),aiTaskResult.getTraceId());
        log.debug("查询推送延时信息 sn {} traceId {} pirAlarmDelayDO {}",video.getSerialNumber(),video.getTraceId(),pirAlarmDelayDO);
        if(pirAlarmDelayDO.getAlarmType()>0){
            message.setTitle(pirAlarmDelayDO.getMessageTitle());
            videoCustomerContentMessage.setType(pirAlarmDelayDO.getAlarmType());
            videoCustomerContentMessage.setAlarmDelayTimeStamp(pirAlarmDelayDO.getAlarmDelayTime());
            videoCustomerContentMessage.setAlarmDuration(pirAlarmDelayDO.getAlarmDuration());
        }

        AiEvent event = aiTaskResult.getEvents().get(0);
        message.setContent(notificationService.getVideoEventDescribe(pushCtx, event, videoEvent));

        MessageAndroid messageAndroid = new MessageAndroid();
        messageAndroid.setIcon_res(s3ImageUrl);

        videoCustomerContentMessage.getDevice().setDeviceName(video.getDeviceName());
        videoCustomerContentMessage.getDevice().setSerialNumber(video.getSerialNumber());
        videoCustomerContentMessage.setTraceId(aiTaskResult.getTraceId());
        videoCustomerContentMessage.setVideoEvent(videoEvent);
        videoCustomerContentMessage.setCheckPushIntent(checkPushIntent);
        videoCustomerContentMessage.setUserId(user.getId());
        videoCustomerContentMessage.setTenantId(user.getTenantId());
        videoCustomerContentMessage.setNode(activeProfile);

        Integer magicPix = factoryDataQueryService.queryMagicPixByModelNo(deviceManualService.getModelNoBySerialNumber(video.getSerialNumber()));
        Boolean supportMagicPix = FactoryDataQueryService.supportMagicPix(magicPix);

        videoCustomerContentMessage.setSupportMagicPix(supportMagicPix);

        messageAndroid.setCustom_content(gson.toJson(videoCustomerContentMessage)); // gson会对url中的特殊字符进行转义
        message.setAndroid(messageAndroid);

        return message;
    }

    private String queryVideoTitle(VideoCache video, User user, AiTaskResult aiTaskResult, String deviceName) {
        final List<String> zoneNames = videoCacheService.getActivityZoneNames(video, aiTaskResult.getEvents());
        return zoneNames.size() == 0 ? deviceName :
                LanguageBase.MotionTitleForActivityZone(deviceName, zoneNames,
                        this.getUserNotifyMessage(user, CopyWriteConstans.motionTitleForActivityZone));
    }

    public String getUserNotifyMessage(User user, String key) {
        return centerNotifyConfig.getMessage().get(user.getTenantId()).get(user.getLanguage()).get(key);
    }

    private VideoMsgEntity buildVideoMessage(VideoCache video, NotificationService.PushContext pushCtx, AiTaskResult aiTaskResult, String videoEventKey) {
        VideoMsgEntity entity = new VideoMsgEntity();

        // 这两个本来就没值，预留字段?
        entity.getLocation().setLocationId(0);
        entity.getLocation().setLocationName("");

        entity.getDevice().setDeviceName(video.getDeviceName());
        entity.getDevice().setSerialNumber(video.getSerialNumber());

        entity.getLibrary().setLibraryId(null);
        entity.getLibrary().setVideoUrl(s3Service.preSignUrl(video.getVideoUrl()));

        entity.setTraceId(aiTaskResult.getTraceId());
        entity.setVideoEvent(videoEventKey);
        AiEvent event = aiTaskResult.getEvents().get(0);

        final List<String> zoneNames = videoCacheService.getActivityZoneNames(video, aiTaskResult.getEvents());
        User user = pushCtx.getUser();
        if (zoneNames.size() == 0) {
            entity.setTitle(video.getDeviceName());
        } else {
            String allZones = this.getUserNotifyMessage(user, CopyWriteConstans.motionTitleForActivityZone);
            entity.setTitle(LanguageBase.MotionTitleForActivityZone(video.getDeviceName(), zoneNames, allZones));
        }

        PirAlarmDelayDO pirAlarmDelayDO = notificationService.queryPirMessageTitle(user,video.getDeviceName(),video.getSerialNumber(),aiTaskResult.getTraceId());
        log.debug("查询推送延时信息 sn {} traceId {} pirAlarmDelayDO {}",video.getSerialNumber(),video.getTraceId(),pirAlarmDelayDO);
        if(pirAlarmDelayDO.getAlarmType()>0){
            entity.setTitle(pirAlarmDelayDO.getMessageTitle());
            entity.setType(pirAlarmDelayDO.getAlarmType());
            entity.setAlarmDelayTimeStamp(pirAlarmDelayDO.getAlarmDelayTime());
            entity.setAlarmDuration(pirAlarmDelayDO.getAlarmDuration());
        }


        entity.setBody(notificationService.getVideoEventDescribe(pushCtx, event, videoEventKey));
        entity.setThumbnailUrl(s3Service.preSignUrl(aiTaskResult.getImageUrl()));
        entity.setVideoEvent(org.springframework.util.StringUtils.isEmpty(videoEventKey) ? null : videoEventKey);

        entity.setCheckPushIntent(checkPushIntent);
        entity.setUserId(user.getId());
        entity.setTenantId(user.getTenantId());
        entity.setNode(activeProfile);

        Integer magicPix = factoryDataQueryService.queryMagicPixByModelNo(deviceManualService.getModelNoBySerialNumber(video.getSerialNumber()));
        Boolean supportMagicPix = FactoryDataQueryService.supportMagicPix(magicPix);

        entity.setSupportMagicPix(supportMagicPix);
        return entity;
    }


    public void notifyVideoReportEvent(VideoCache video, EReportEvent reportEvent, Integer msgType) {
        final VideoCache.ExeStep notifyVideoReportEventStep = video.loggingStep("notifyVideoReportEvent:" + reportEvent);
        final String traceId = video.getTraceId();
        final String serialNumber = video.getSerialNumber();
        final String videoEvent = videoCacheService.getVideoEvent(video);
        final String deviceName = video.getDeviceName();
        final String s3ImageUrl;
        final String pushKeyPostfix;
        // 无图片和有图片时各推一次。只会从无图到有图，不用担心先有图后无图的情况。
        if (StringUtils.isNotBlank(video.getImageUrl())) {
            s3ImageUrl = s3Service.preSignUrl(video.getImageUrl());
            pushKeyPostfix = "hasImage";
        } else {
            s3ImageUrl = null;
            pushKeyPostfix = "noImage";
        }
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        for (VideoCommonCache.UserSimple user : video.getUsers()) {
            final Integer userId = user.getId();
            final String pushedKey = userId + ":" + reportEvent.name() + ":" + pushKeyPostfix;
            if (video.getPirNotifyPushedKeys().contains(pushedKey)) { // 检查 userId:reportEvent:(noImage|hasImage) 是否推送过
                continue; // 一个分享用户 + 一个reportEvent 各推一次
            }
            final VideoCache.ExeStep isPushShiedStep = video.loggingStep("isPushShied");
            boolean pushShield = DOORBELL_EVENT.contains(reportEvent.getEventId()) ?
                    messagePushManageService.isPushShied(userId) : messagePushManageService.queryUserPushSwitch(userId,deviceDO);
            if (pushShield) {
                isPushShiedStep.exeEnd();
                log.debug("用户{}设置了消息免打扰,traceId:{}", userId, traceId);
                continue;
            }
            isPushShiedStep.exeEnd();
            // 拼接推送消息
            String content = getVideoReportEventMsgContent(user, reportEvent);
            if (StringUtils.isBlank(content)) {
                com.addx.iotcamera.util.LogUtil.warn(log, " no push content configured, traceId:{} reportEvent:{}", userId, traceId, reportEvent);
                continue;
            }
            final VideoCache.ExeStep getPushInfoStep = video.loggingStep("getPushInfo");
            final PushInfo pushInfo = videoCacheService.getPushInfo(video, userId);
            if (pushInfo == null) {
                getPushInfoStep.exeEnd();
                video.getPushProcessRecorder().recordUserNotPushEvent(userId, -2, null, "找不到用户的pushInfo");
                continue; // 没有推送参数，跳过
            }
            getPushInfoStep.exeEnd();
            if (PushTypeEnums.PUSH_XINGE.getCode() == pushInfo.getMsgType()) {
                Message message = new Message();
                message.setTitle(deviceName);
                message.setContent(content);
                message.setXgMediaResources(s3ImageUrl);

                VideoCustomerContentMessage videoCustomerContentMessage = new VideoCustomerContentMessage();
                videoCustomerContentMessage.setType(msgType);
                videoCustomerContentMessage.getDevice().setDeviceName(deviceName);
                videoCustomerContentMessage.getDevice().setSerialNumber(serialNumber);
                videoCustomerContentMessage.getLibrary().setImageUrl(s3ImageUrl);
                videoCustomerContentMessage.setTraceId(traceId);
                videoCustomerContentMessage.setVideoEvent(videoEvent);
                videoCustomerContentMessage.setCheckPushIntent(checkPushIntent);
                videoCustomerContentMessage.setUserId(user.getId());
                videoCustomerContentMessage.setTenantId(user.getTenantId());
                videoCustomerContentMessage.setNode(activeProfile);

                Integer magicPix = factoryDataQueryService.queryMagicPixByModelNo(deviceManualService.getModelNoBySerialNumber(serialNumber));
                Boolean supportMagicPix = FactoryDataQueryService.supportMagicPix(magicPix);

                videoCustomerContentMessage.setSupportMagicPix(supportMagicPix);


                MessageAndroid messageAndroid = new MessageAndroid();
                messageAndroid.setCustom_content(gson.toJson(videoCustomerContentMessage)); // gson会对url中的特殊字符进行转义
                messageAndroid.setIcon_res(s3ImageUrl);

                String tenantId = user.getTenantId();

                pushXingeService.initAndroidMessageChannel(tenantId, messageAndroid);
                message.setAndroid(messageAndroid);

                XingePushArgs xingePushArgs = XingePushArgs.builder()
                        .userId(userId)
                        .traceId(traceId)
                        .bundleName(pushInfo.getBundleName())
                        .build();
                final VideoCache.ExeStep pushMessageStep = video.loggingStep("pushMessage:" + pushInfo.getMsgType());
                pushXingeService.pushMessage(xingePushArgs, message);
                pushMessageStep.exeEnd();
            } else {
                VideoReportEventMsgEntity videoReportEventMsgEntity = new VideoReportEventMsgEntity();
                videoReportEventMsgEntity.setTitle(deviceName);
                videoReportEventMsgEntity.setBody(content);
                videoReportEventMsgEntity.setTraceId(traceId);
                videoReportEventMsgEntity.setType(msgType);
                videoReportEventMsgEntity.getDevice().setDeviceName(deviceName);
                videoReportEventMsgEntity.getDevice().setSerialNumber(serialNumber);
                videoReportEventMsgEntity.setThumbnailUrl(s3ImageUrl);
                videoReportEventMsgEntity.setVideoEvent(videoEvent);
                videoReportEventMsgEntity.setCheckPushIntent(checkPushIntent);
                videoReportEventMsgEntity.setUserId(user.getId());
                videoReportEventMsgEntity.setTenantId(user.getTenantId());
                videoReportEventMsgEntity.setNode(activeProfile);

                Integer magicPix = factoryDataQueryService.queryMagicPixByModelNo(deviceManualService.getModelNoBySerialNumber(serialNumber));
                Boolean supportMagicPix = FactoryDataQueryService.supportMagicPix(magicPix);

                videoReportEventMsgEntity.setSupportMagicPix(supportMagicPix);

                final VideoCache.ExeStep pushMessageStep = video.loggingStep("pushMessage:" + pushInfo.getMsgType());
                pushService.pushVideoMessage(pushInfo, videoReportEventMsgEntity);
                pushMessageStep.exeEnd();
            }
            video.getPirNotifyPushedKeys().add(pushedKey); // 设置 userId:reportEvent:(noImage|hasImage) 推送过
        }
        notifyVideoReportEventStep.exeEnd();
    }

    public String getVideoReportEventMsgContent(VideoCommonCache.UserSimple user, EReportEvent reportEvent) {
        String configKey = VideoReportEvent.reportEvent2ConfigKey.get(reportEvent);
        String content = copyWrite.getConfig().containsKey(configKey) ?
                copyWrite.getConfig().get(configKey).containsKey(user.getLanguage()) ? copyWrite.getConfig().get(configKey).get(user.getLanguage())
                        : copyWrite.getConfig().get(configKey).get("en")
                : null;
        return content;
    }

    public boolean checkEnableOtherPass(VideoCache video) {
        DeviceAiSwitch deviceAiSwitch = aiAssistService.queryEventObjectSwitch(video.getAdminId(), video.getSerialNumber()).getData();
        boolean needCheckEnableOther = notifyCheckEnableOtherSwicth && !deviceAiSwitch.getList().isEmpty();
        if (needCheckEnableOther) {
            return BooleanUtils.isTrue(video.getEnableOther());
        }
        return true;
    }

    public void sendMotionNotifyToAlexa(VideoCache video) {
        if (!TENANTID_SAFEMO.equals(video.getTenantId())) return;
        if (!video.isEventRecordingMainView()) return;
        if (!video.getIsNotify()) {
            return; // 非vip时pushIgnore字段才生效
        }
        // 给每个分享者发送alexa pir推送
        for (VideoCommonCache.UserSimple user : video.getUsers()) {
            if (devicePlatformEventPublisher != null && video.getAlexPushedUserIds().add(user.getId())) {
                Map<String, Object> deviceEventParamMap = new HashMap<>();
                deviceEventParamMap.put("userId", user.getId());
                deviceEventParamMap.put("serialNumber", video.getSerialNumber());
                devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.MOTION_PIR_DETECTED, deviceEventParamMap);
            }
        }
    }

}
