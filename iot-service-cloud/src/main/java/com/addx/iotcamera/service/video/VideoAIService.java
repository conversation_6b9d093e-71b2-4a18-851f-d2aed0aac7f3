package com.addx.iotcamera.service.video;

import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.domain.AITask;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.RecognitionObjectCategory;
import com.addx.iotcamera.bean.openapi.SaasAITaskIM;
import com.addx.iotcamera.config.AiTaskParamsConfig;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.config.opanapi.SaasAiTaskConfig;
import com.addx.iotcamera.enums.VipAiAnalyzeType;
import com.addx.iotcamera.enums.VipCountType;
import com.addx.iotcamera.helper.TimeTicker;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_PIR_START_TO_FIRST_NOTIFY_AI;
import static com.addx.iotcamera.service.openapi.SaasAIService.buildOutParams;
import static com.addx.iotcamera.service.video.VideoCache.Flag.LAST_AI_TASK_SEND;

@Slf4j(topic = "videoGenerate")
@Component
public class VideoAIService {

    @Autowired
    private S3Service s3Service;
    @Autowired
    private SaasAiTaskConfig aiTaskConfig;
    @Autowired
    @Lazy
    private StorageAllocateService storageAllocateService;
    @Autowired
    private AiTaskParamsConfig aiTaskParamsConfig;
    @Autowired
    private ActivityZoneService activityZoneService;
    @Autowired
    private VipService vipService;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceSupportService deviceSupportService;
    @Resource
    private MqSender mqSender;
    @Autowired
    private TimeTicker timeTicker;
    @Autowired
    private VideoReportLogService videoReportLogService;
    @Autowired
    private UserService userService;
    @Autowired
    private PaasTenantConfig paasTenantConfig;

    @Value("${spring.kafka.topics.saas-ai-task}")
    private String saasAiTaskTopic;
    @Value("${spring.kafka.topics.video-generate}")
    private String videoGenerateTopic;

    @Value("${ai-cloud.enable}")
    private Boolean enableAiCloudImageFeature;

    @Value("${ai-cloud.blackUserIds}")
    private String blackUserIds;


    private boolean userInBlackList(String userId) {
        return (new HashSet<>(Arrays.asList(blackUserIds.split(",")))).contains(userId);
    }

    /**
     * 构建ai任务的通用参数
     * 这个方法要在video的admin和vip查询完毕后调用
     */
    public SaasAITaskIM createDefaultSaasAITask(VideoCommonCache video, Collection<String> extraAiDetectEvents) {
        final SaasAITaskIM saasAITask = new SaasAITaskIM().setRecognitionObjects(new LinkedList<>()).setIdBox(new SaasAITaskIM.IdBox());
        if (!video.getHasAiAbility()) {
            return saasAITask; // 不是vip，则不查后续信息
        }
        final VipAiAnalyzeType vipTierType = VipAiAnalyzeType.getVipTierType(video.getHasAiAbility(), video.getIsBirdVip());
        List<String> aiDetectNotifyEvents = deviceInfoService.pushAiList(video.getSerialNumber(), video.getAdminId(), vipTierType.getSupportEventObjectNames());
        if (CollectionUtils.isNotEmpty(extraAiDetectEvents)) {
            aiDetectNotifyEvents = ImmutableList.<String>builder().addAll(aiDetectNotifyEvents).addAll(extraAiDetectEvents).build();
        }
        if (CollectionUtils.isEmpty(aiDetectNotifyEvents)) {
            return saasAITask; // AI分析开关为空，则不查后续信息
        }
        final AITask.IdentificationBox identificationBox = aiTaskParamsConfig.getIdentificationBoxByTenantId(video.getTenantId());
        for (String event : FuncUtil.iterable(aiDetectNotifyEvents)) {
            RecognitionObjectCategory category = RecognitionObjectCategory.eventObjectNameOf(event);
            saasAITask.getRecognitionObjects().add(new SaasAITaskIM.RecognitionObject().setCategory(category)
                    .setFunctions(category.getPaasOwnedBizFunctions()));
            saasAITask.getIdBox().addVisualizeRecognition(category);
        }
        if (identificationBox != null) {
            for (AITask.Color color : FuncUtil.iterable(identificationBox.getColors())) {
                saasAITask.getIdBox().addIdBoxColor(new SaasAITaskIM.IdBoxColor().setColor(color.getColor())
                        .setName(RecognitionObjectCategory.eventObjectNameOf(color.getName())));
            }
        }
        saasAITask.setActivityZoneList(activityZoneService.queryActivityZone(video.getSerialNumber()));
        saasAITask.setDeveloperId(Arrays.asList("a4x_sdk"));
        VipCountType vipCountType = vipService.queryVipCountType(video.getTenantId(), video.getAdminId(), video.getSerialNumber());
        saasAITask.getContext().setVipCountType(vipCountType != null ? vipCountType.getCode() : 0);
        return saasAITask;
    }

    public boolean sendSaasAiTask(VideoCache video, VideoSliceDO slice) {
        if (!video.isEventRecordingMainView()) return false;
        CloudDeviceSupport cloudDeviceSupport = deviceSupportService.queryDeviceSupportBySn(video.getSerialNumber());
        if (cloudDeviceSupport != null && Boolean.TRUE.equals(cloudDeviceSupport.getSupportSendAiImageDirect())
                && enableAiCloudImageFeature && !userInBlackList(video.getAdminId().toString())) {
            log.info("sendSaasAiTask deviceSupport supportSendAiImageDirect is true, need not send ai kafka task!, traceId: {}", video.getTraceId());
            return true;
        }
        if(AppConstants.TENANTID_SAFEMO.equals(userService.queryUserById(video.getAdminId()).getTenantId())){
            log.info("sendSaasAiTask: safemo tenantId need not send ai kafka task!");
            return true;
        }
        if (video.getDefaultSaasAITask().getRecognitionObjects().isEmpty()) {
            return true; // 没有任何需要分析的对象，直接返回true
        }
        final VideoCache.ExeStep sendSaasAiTaskStep = video.loggingStep("sendSaasAiTask");
        try {
            SaasAITaskIM input = new SaasAITaskIM()
                    .setInputType(SaasAITaskIM.InputType.SLICE)
                    .setOutputType(SaasAITaskIM.OutputType.KAFKA)
                    .setTraceId(video.getTraceId())
                    .setDeviceSn(video.getSerialNumber())
                    .setOwnerId(video.getAdminId() + "")
                    .setVideoUrl(slice != null ? s3Service.preSignUrl(slice.getVideoUrl()) : "")
                    .setImages(Collections.EMPTY_LIST)
                    .setOrder(slice != null ? slice.getOrder() : -1)
                    .setIsLast(slice != null && !slice.getIsLast() ? 0 : 1)
                    .setTimeout(aiTaskConfig.getTimeout())
                    .setOutStorage(storageAllocateService.createOutStorage(aiTaskConfig.getOutStorage(), video.getStoreBucket()))
                    .setTaskId(OpenApiUtil.shortUUID())
                    .setTaskSendTime(timeTicker.readSeconds())
                    .setActivityZoneList(video.getDefaultSaasAITask().getActivityZoneList())
                    .setRecognitionObjects(video.getDefaultSaasAITask().getRecognitionObjects())
                    .setTenantId(video.getTenantId())
                    .setIdBox(video.getDefaultSaasAITask().getIdBox())
                    .setDeveloperId(video.getDefaultSaasAITask().getDeveloperId())
                    .setDeviceIp(slice != null ? slice.getDeviceIp() : video.getDeviceIp())
                    .setCountryNo(video.getCountryNo())
                    .setModelNo(video.getDeviceModelNo())
                    .setOutputTopic(videoGenerateTopic); // 不走webhook转发，iotService直接接收
            input.setSliceTotalNum(slice != null ? -1 : video.getSliceNum()); // 避免设备端先收到汇总消息，抛弃调后续切片消息
            input.setContext(video.getDefaultSaasAITask().getContext());
            input.setOutParams(buildOutParams(input));
            log.info("sendSaasAiTask end! input={}", JSON.toJSONString(input));
            mqSender.send(saasAiTaskTopic, input.getTraceId(), input);
            videoReportLogService.reportPirProcessDuration(video, REPORT_TYPE_PIR_START_TO_FIRST_NOTIFY_AI, input.getOrder(), timeTicker.readMillis());
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getSendSaasAiTaskCountOptional()
                    .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName()).inc()));
            return true;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "sendSaasAiTask error! video={}", JSON.toJSONString(video), e);
            return false;
        } finally {
            sendSaasAiTaskStep.exeEnd();
        }
    }

    public SaasAITaskIM getAiTaskConfig(AITask aiTask, boolean isLast) {

        User user = userService.queryUserById(Integer.valueOf(aiTask.getUserId()));
        List<SaasAITaskIM.RecognitionObject> recognitionObjects = new LinkedList<>();
        SaasAITaskIM.IdBox idBox = new SaasAITaskIM.IdBox();
        for (String event : FuncUtil.iterable(aiTask.getEvents())) {
            RecognitionObjectCategory category = RecognitionObjectCategory.eventObjectNameOf(event);
            recognitionObjects.add(new SaasAITaskIM.RecognitionObject().setCategory(category)
                    .setFunctions(category.getPaasOwnedBizFunctions()));
            idBox.addVisualizeRecognition(category);
        }
        if (aiTask.getIdentificationBox() != null) {
            for (AITask.Color color : FuncUtil.iterable(aiTask.getIdentificationBox().getColors())) {
                idBox.addIdBoxColor(new SaasAITaskIM.IdBoxColor().setColor(color.getColor())
                        .setName(RecognitionObjectCategory.eventObjectNameOf(color.getName())));
            }
        }
        SaasAITaskIM input = new SaasAITaskIM()
                .setInputType(SaasAITaskIM.InputType.SLICE)
                .setOutputType(SaasAITaskIM.OutputType.KAFKA)
                .setTraceId(aiTask.getTraceId())
                .setCountryNo(user.getCountryNo())
                .setDeviceSn(aiTask.getSerialNumber())
                .setOwnerId(aiTask.getUserId() + "")
                .setVideoUrl(aiTask.getVideoUrl())
                .setImages(Collections.EMPTY_LIST)
                .setOrder(aiTask.getOrder())
                .setIsLast(isLast ? 1 : 0)
                .setTimeout(aiTaskConfig.getTimeout())
                .setOutStorage(aiTaskConfig.getOutStorage())
                .setTaskId(aiTask.getTaskId())
                .setTaskSendTime(aiTask.getTaskSendTime())
                .setActivityZoneList(aiTask.getActivityZoneList())
                .setRecognitionObjects(recognitionObjects)
                .setTenantId(user.getTenantId())
                .setIdBox(idBox)
                .setModelNo(aiTask.getModelNo())
                .setOutputTopic(videoGenerateTopic); // 不走webhook转发，iotService直接接收
        input.setSliceTotalNum(Optional.ofNullable(aiTask.getSliceTotalNum()).orElse(-1)); // 避免设备端先收到汇总消息，抛弃调后续切片消息
        VipCountType vipCountType = vipService.queryVipCountType(user.getTenantId(), Integer.valueOf(aiTask.getUserId()), aiTask.getSerialNumber());
        input.getContext().setVipCountType(vipCountType != null ? vipCountType.getCode() : 0);
        input.setOutParams(buildOutParams(input));
        return input;
    }

    public void sendLastSaasAiTask(VideoCache video) {
        if (video.getFlag(LAST_AI_TASK_SEND)) return;
        if (!sendSaasAiTask(video, null)) return;
        video.setFlag(LAST_AI_TASK_SEND);
    }

    /*
    public String buildOutParams(VideoCache video) {
        JSONObject params = new JSONObject().fluentPut("outEncodeType", 0); // URL(0), BINARY(1), BASE64(2);
        if (paasTenantConfig.getPaasTenantInfo(video.getTenantId()).getEnableAiResultToThird()) {
            // 用于webhook匹配回调地址
            params.put("accountId", video.getAccountId());
            params.put("tenantId", video.getTenantId());
        }
        return params.toJSONString();
    }

    public String buildOutputTopic(VideoCache video) {
        if (paasTenantConfig.getPaasTenantInfo(video.getTenantId()).getEnableAiResultToThird()) {
            return saasAiTaskResultTopic; // webhook转发给第三方平台
        }
        return videoGenerateTopic; // 不走webhook转发，iotService直接接收
    }
    */

}
