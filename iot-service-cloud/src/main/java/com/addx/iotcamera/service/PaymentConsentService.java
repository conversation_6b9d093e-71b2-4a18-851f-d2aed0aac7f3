package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.userorder.BoughtProductInfo;
import com.addx.iotcamera.bean.db.AirwallexCustomer;
import com.addx.iotcamera.bean.db.PaymentConsent;
import com.addx.iotcamera.bean.db.SubscriptionPaymentTask;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.pay.PaymentConfig;
import com.addx.iotcamera.bean.request.DeletePaymentConsentRequest;
import com.addx.iotcamera.bean.response.PaymentConsentInfoList;
import com.addx.iotcamera.config.CardBrandUrlConfig;
import com.addx.iotcamera.config.pay.PaymentCenterConfig;
import com.addx.iotcamera.config.pay.ThreePayBlacklistConfig;
import com.addx.iotcamera.dao.AirwallexCustomerDAO;
import com.addx.iotcamera.dao.PaymentConsentDAO;
import com.addx.iotcamera.dao.SubscriptionPaymentTaskDAO;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.SubscriptionPaymentTaskStatusEnum;
import com.addx.iotcamera.service.pay.AirwallexPayService;
import com.addx.iotcamera.util.AirwallexUrlSelector;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PaymentConsentService {

    @Autowired
    private PaymentConsentDAO paymentConsentDAO;


    @Autowired
    private AirwallexCustomerDAO airwallexCustomerDAO;

    @Autowired
    private SubscriptionPaymentTaskDAO subscriptionPaymentTaskDAO;

    @Resource
    private CardBrandUrlConfig cardBrandUrlConfig;

    @Autowired
    private PaymentCenterConfig paymentCenterConfig;


    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private AirwallexUrlSelector airwallexUrlSelector;


    @Autowired
    private AirwallexPayService airwallexPayService;

    @Autowired
    private UserService userService;

    public List<PaymentConsent> queryPaymentFlowNoRefundBatch(List<String> payConsentIds) {
        if (CollectionUtils.isEmpty(payConsentIds)) {
            return Collections.emptyList();
        }
        return paymentConsentDAO.queryPaymentFlowNoRefundBatch(payConsentIds);
    }

    public PaymentConsent getByPaymentConsentId(String paymentConsentId) {
        if (paymentConsentId == null || paymentConsentId.isEmpty()) {
            return  new PaymentConsent();
        }
        return paymentConsentDAO.getByPaymentConsentId(paymentConsentId);
    }


    public Result getPaymentConsentIdList(Integer userId) {

        User user = userService.queryUserById(userId);
        if (user == null || user.getEmail() == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "用户不存在或已经被禁用");
        }

        PaymentConsentInfoList paymentConsentInfoListResult = new PaymentConsentInfoList();

        if (airwallexPayService.isUserInThreePayBlacklist(userId)) {
            log.info("提审账号不走卡管理 userid={}", userId);
            return new Result(paymentConsentInfoListResult);
        }

        List<PaymentConsent> paymentConsentList = paymentConsentDAO.getByUserId(userId);
        if (CollectionUtils.isEmpty(paymentConsentList)) {
            return new Result(paymentConsentInfoListResult);
        }

        List<SubscriptionPaymentTask> tasksByUserIdAndStatus = subscriptionPaymentTaskDAO.getTasksByUserIdAndStatus(userId, Arrays.asList(SubscriptionPaymentTaskStatusEnum.PENDING.getValue(),
                SubscriptionPaymentTaskStatusEnum.PROCESSING.getValue()));
        List<String> constentIdList;
        if (!CollectionUtils.isEmpty(tasksByUserIdAndStatus)) {
            constentIdList = tasksByUserIdAndStatus.stream().map(SubscriptionPaymentTask::getPaymentConsentId).collect(Collectors.toList());
        } else {
            constentIdList = new ArrayList<>();
        }

        List<PaymentConsentInfoList.PaymentConsentInfo> paymentConsentInfoList = new ArrayList<>();
        //过滤Apple pay的卡
        paymentConsentList = paymentConsentList.stream().filter(e -> e.getPaymentMethod() != null && !e.getPaymentMethod().contains("applepay")).collect(Collectors.toList());
        paymentConsentList.forEach(paymentConsent -> {
            PaymentConsentInfoList.PaymentConsentInfo paymentConsentInfo = new PaymentConsentInfoList.PaymentConsentInfo();
            PaymentConsentInfoList.PaymentMethod paymentMethod = new PaymentConsentInfoList.PaymentMethod();

            paymentConsentInfo.setId(paymentConsent.getId().intValue());
            paymentConsentInfo.setDefaultCard(Objects.equals(paymentConsent.getIsDefault(), 1));
            paymentConsentInfo.setCustomerId(paymentConsent.getCustomerId());
            paymentConsentInfo.setPaymentConsentId(paymentConsent.getPaymentConsentId());
            paymentConsentInfo.setUsed(constentIdList.contains(paymentConsent.getPaymentConsentId()));

            String paymentMethodJsonString = paymentConsent.getPaymentMethod();
            if (paymentMethodJsonString != null) {
                log.info("payment method={}", paymentMethodJsonString);
                try {
                    // 从paymentMethod中提取卡信息
                    JSONObject paymentMethodJson = JSON.parseObject(paymentMethodJsonString);
                    if (paymentMethodJson.containsKey("card")) {
                        JSONObject cardJson = paymentMethodJson.getJSONObject("card");

                        // 创建CardInfo对象
                        paymentMethod.setLast4(cardJson.getString("last4"));
                        paymentMethod.setBin(cardJson.getString("bin"));
                        paymentMethod.setExpiryYear(cardJson.getString("expiry_year"));
                        paymentMethod.setExpiryMonth(cardJson.getString("expiry_month"));
                        paymentMethod.setCardType(cardJson.getString("card_type"));

                        // 设置品牌图片URL
                        if (StringUtils.hasLength(paymentConsent.getBrand())) {
                            String brandImageUrl = cardBrandUrlConfig.getUrlByBrand(paymentConsent.getBrand());
                            paymentMethod.setBrand(paymentConsent.getBrand());
                            paymentMethod.setIcon(brandImageUrl);
                        }
                        paymentMethod.setType(PaymentTypeEnums.CARD.name());

                        log.info("getPaymentConsentIdList 获取支付方式信息成功, userId={},  brand={}", userId, paymentConsent.getBrand());
                    }

                    String type = paymentMethodJson.getString("type");
                    if ("applepay".equals(type)) {
                        paymentMethod.setType(PaymentTypeEnums.APPLEPAY.name());
                    }

                } catch (Exception e) {
                    log.error("getPaymentConsentIdList 获取支付方式信息失败, userId={}, error={}",
                            userId, e.getMessage(), e);
                }
            }

            paymentConsentInfo.setPaymentMethod(paymentMethod);
            paymentConsentInfoList.add(paymentConsentInfo);
        });
        paymentConsentInfoListResult.setPaymentConsentInfoList(paymentConsentInfoList);


        return new Result(paymentConsentInfoListResult);
    }

    @Transactional
    public Result deleteConsent(Integer userId, @Valid DeletePaymentConsentRequest request) {

        PaymentConsent paymentConsent = getByPaymentConsentId(request.getConsentId());
        if (paymentConsent == null) {
            return Result.Failure("查询不到卡片信息");
        }

        List<SubscriptionPaymentTask> tasksByUserIdAndStatus = subscriptionPaymentTaskDAO.getTasksByUserIdAndStatus(userId, Arrays.asList(SubscriptionPaymentTaskStatusEnum.PENDING.getValue(),
                SubscriptionPaymentTaskStatusEnum.PROCESSING.getValue()));
        List<String> constentIdList = tasksByUserIdAndStatus.stream().map(SubscriptionPaymentTask::getPaymentConsentId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(constentIdList) && constentIdList.contains(request.getConsentId())) {
            return Result.Error(ResultCollection.NOT_DELETE_CONSENT_USED);
        }

        JSONObject jsonObject = deletePaymentConsentId(userId, request.getConsentId());
        log.info("Airwallex deleteConsent return jsonObject={}", jsonObject);

        String status = jsonObject.getString("status");
        log.info("deleteConsent userId={}, status={}, isDefault={}", userId, status, paymentConsent.getIsDefault());

        if (status.equals("DISABLED")) {
            paymentConsentDAO.setDeleteByPaymentConsentId(request.getConsentId());
            //如果删除的是默认卡要将最新添加的卡设置为默认卡
            if (Objects.equals(paymentConsent.getIsDefault(), 1)) {
                List<PaymentConsent> paymentConsents = paymentConsentDAO.queryPaymentConsentListByUserId(userId);
                paymentConsents = paymentConsents.stream().filter(e -> e.getPaymentMethod() != null && !e.getPaymentMethod().contains("applepay")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(paymentConsents)) {
                    PaymentConsent paymentConsentLast = paymentConsents.get(0);
                    paymentConsentDAO.clearDefaultByUserId(userId);
                    log.info("deleteConsent paymentConsentLast={}", paymentConsentLast);
                    paymentConsentDAO.setDefault(paymentConsentLast.getId());
                }
            }
            return Result.Success();
        }
        return Result.Failure("delete failed");
    }


    private JSONObject deletePaymentConsentId(Integer userId, String paymentConsentId) {
        // 获取用户的客户ID
        AirwallexCustomer customer = airwallexCustomerDAO.getByUserId(userId);
        if (customer == null || org.apache.commons.lang3.StringUtils.isEmpty(customer.getCustomerId())) {
            throw new RuntimeException("用户没有关联的Airwallex客户");
        }

        String customerId = customer.getCustomerId();

        // 确保有token
        String authToken = airwallexPayService.ensureValidToken();

        // 准备请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(authToken);

        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("request_id", UUID.randomUUID().toString());

        HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

        // 获取Vicoo的支付配置
        PaymentConfig paymentConfig = paymentCenterConfig.getConfig().get("vicoo");
        if (paymentConfig == null) {
            throw new RuntimeException("找不到Vicoo支付配置");
        }

        try {
            // 创建支付意向
            String apiUrl = airwallexUrlSelector.getApiUrl(userId, paymentConfig);
            String url = apiUrl + "/api/v1/pa/payment_consents/" + paymentConsentId + "/disable";
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    String.class
            );
            log.info("deletePaymentConsentId response code={}", response.getStatusCode());

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject responseJson = JSON.parseObject(response.getBody());
                log.info("禁用卡号成功，paymentIntentId={}", responseJson.getString("id"));
                return responseJson;
            } else {
                log.error("禁用卡号失败，状态码：{}, 响应：{}", response.getStatusCode(), response.getBody());
                throw new RuntimeException("禁用卡号失败");
            }
        } catch (Exception e) {
            log.error("Airwallex 禁用卡号 异常", e);
            throw new RuntimeException("禁用卡号失败异常: " + e.getMessage());
        }
    }
}
