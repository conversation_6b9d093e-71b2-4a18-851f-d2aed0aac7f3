package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.apollo.Transfer;
import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.release.AndroidVersionInfo;
import com.addx.iotcamera.bean.app.release.IosVersion;
import com.addx.iotcamera.bean.db.ApkInfoDO;
import com.addx.iotcamera.bean.db.app.IosVersionDO;
import com.addx.iotcamera.bean.db.app.IosVersionReleaseDO;
import com.addx.iotcamera.bean.domain.manage.AppTransfer;
import com.addx.iotcamera.bean.domain.manage.Country;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.openapi.OpenSdkInit;
import com.addx.iotcamera.bean.response.ServerNode;
import com.addx.iotcamera.bean.response.ZendeskItem;
import com.addx.iotcamera.bean.response.ZendeskSupportResponse;
import com.addx.iotcamera.config.*;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.CountryNodeConfig;
import com.addx.iotcamera.config.apollo.CountryNodeV1Config;
import com.addx.iotcamera.config.app.AndroidReleaseConfig;
import com.addx.iotcamera.config.app.AppMenuConfig;
import com.addx.iotcamera.config.app.TenantServerNodeConfig;
import com.addx.iotcamera.config.app.ZendeskConfig;
import com.addx.iotcamera.dao.AppInfoDAO;
import com.addx.iotcamera.dao.manage.ICountryDAO;
import com.addx.iotcamera.dao.user.IEuUserDao;
import com.addx.iotcamera.enums.AppType;
import com.addx.iotcamera.enums.EvnEnums;
import com.addx.iotcamera.enums.ServerNodeEnums;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.google.api.client.util.Lists;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.ResponseEntity.BodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.*;

import static org.addx.iot.common.constant.AppConstants.*;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

/**
 * <AUTHOR>
 */
@Service
public class AppInfoService {
    @Autowired
    private AppInfoDAO appInfoDAO;

    @Autowired
    private CenterNotifyConfig centerNotifyConfig;

    @Autowired
    private CountryNodeConfig countryNodeConfig;
    @Autowired
    private CountryNodeV1Config countryNodeV1Config;

    @Autowired
    private EuCountryConfig euCountryConfig;
    @Autowired
    private ServerNodeConfig serverNodeConfig;

    @Autowired
    private IEuUserDao iEuUserDao;

    @Autowired
    private AppUpgradeConfig appUpgradeConfig;

    @Autowired
    private AndroidReleaseConfig androidReleaseConfig;

    @Autowired
    private CountlyConfig countlyConfig;

    @Value("${release.evn}")
    private String reeleaseEvn;

    @Autowired
    private ICountryDAO iCountryDAO;

    @Autowired
    private AppMenuConfig appMenuConfig;

    @Autowired
    private AppFileS3Config appFileS3Config;

    @Autowired
    private TenantSettingService tenantSettingService;

    private volatile AmazonS3 appFileS3;

    private final static Logger logegr = LoggerFactory.getLogger(AppInfoService.class);

    @Value("${servernode}")
    private String serverNode;

    private final static String serverNodeUs = "US";
    private final static String serverNodeCn = "CN";

    private final static String appTypeIos = "iOS";

    @Resource
    private ZendeskConfig zendeskConfig;

    @Resource
    private TenantServerNodeConfig tenantServerNodeConfig;

    /**
     * 获取安卓最新APK信息
     *
     * @return
     */
    public ApkInfoDO getLatestApk(AppRequestBase request) {
        String bundle = Optional.ofNullable(request).map(AppRequestBase::getApp).map(AppInfo::getBundle).orElse("");
        String tenantId = this.isTenantIdVicohome(bundle) ? tenantIdVicoHome : request.getApp().getTenantId();
        Map<String, AndroidVersionInfo> config = this.getEvnAndroidConfig();
        if (config == null) {
            logegr.info("androidReleaseConfig null");
            return null;
        }

        if (!config.containsKey(tenantId)) {
            logegr.info("androidReleaseConfig not contains {}", tenantId);
            return null;
        }

        AndroidVersionInfo androidVersionInfo = config.get(tenantId);
        ApkInfoDO apkInfoDO = ApkInfoDO.builder()
                .apkId(androidVersionInfo.getVersionCode())
                .apkName(androidVersionInfo.getVersionName())
                .apkSize(androidVersionInfo.getFileSize())
                .minSupport(androidVersionInfo.getMinimalVersion())
                .apkUrl(androidVersionInfo.getS3Url().get(serverNode))
                .shouldJumpToPlaystore(androidVersionInfo.getShouldJumpToPlaystore())
                .tenantId(request.getApp().getTenantId())
                .releaseNote(androidReleaseConfig.getReleaseNotes().get(tenantId).get(request.getLanguage()))
                .customizedMenu(this.getAppCustomizedMenuUrl(request))
                .checkVersionCode(androidVersionInfo.getCheckVersionCode())
                .build();

        return apkInfoDO;
    }

    /**
     * 根据环境获取不同节点
     *
     * @return
     */
    private Map<String, AndroidVersionInfo> getEvnAndroidConfig() {
        Map<String, AndroidVersionInfo> map;
        EvnEnums evnEnums = EvnEnums.queryEnums(reeleaseEvn);
        switch (evnEnums) {
            case TEST:
                map = androidReleaseConfig.getTest();
                break;
            case STAGING:
                map = androidReleaseConfig.getStaging();
                break;
            case RELEASE:
                map = androidReleaseConfig.getRelease();
                break;
            default:
                map = null;
                break;
        }
        return map;
    }

    /**
     * 是否vicoHome
     *
     * @param packageName
     * @return
     */
    public boolean isTenantIdVicohome(String packageName) {
        return packageName!= null && packageName.contains(VICOHOME_PACKAGE);
    }

    /**
     * 获取IOS最新版本信息
     *
     * @return
     */
    public IosVersionDO getLatestIosVersion(AppRequestBase request) {
        String tenantId = request.getApp().getTenantId();

        IosVersion iosVersion = appInfoDAO.selectLatestIosVersion(tenantId,null);
        if(iosVersion == null){
            return IosVersionDO.builder()
                    .customizedMenu(this.getAppCustomizedMenuUrl(request))
                    .build();
        }
        List<String> notes = appInfoDAO.getIosVersionDescriptions(iosVersion.getBuild(),request.getLanguage());
        return this.initIosVersionDO(iosVersion, notes, request);
    }

    /**
     * 初始化APP版本
     *
     * @param iosVersion
     * @param notes
     * @param request
     * @return
     */
    private IosVersionDO initIosVersionDO(IosVersion iosVersion, List<String> notes, AppRequestBase request) {
        String tenantId = request.getApp().getTenantId();
        String downloadUrl = appUpgradeConfig.getConfig().containsKey(tenantId) ?
                appUpgradeConfig.getConfig().get(tenantId) : "";
        return IosVersionDO.builder()
                .version(iosVersion.getBuild())
                .minSupport(iosVersion.getMinBuild())
                .versionName(iosVersion.getVersion())
                .downloadUrl(downloadUrl)
                .releaseNote(notes)
                .packageSize(iosVersion.getPackageSize())
                .tenantId(request.getApp().getTenantId())
                .customizedMenu(this.getAppCustomizedMenuUrl(request))
                .checkVersionCode(iosVersion.getCheckVersionCode())
                .build();
    }

    /**
     * 设置安卓最新APK信息
     *
     * @return
     */
    public Result setLatestApk(ApkInfoDO apkInfoDO) {
        if (!apkInfoDO.IsValid()) {
            return INVALID_PARAMS.getResult();
        }
        appInfoDAO.insertApk(apkInfoDO);
        apkInfoDO.getReleaseNote().forEach(description -> appInfoDAO.insertApkDescription(apkInfoDO.getApkId(), description));
        return Result.Success();
    }

    /**
     * 设置IOS最新版本信息
     *
     * @return
     */
    public Result setLatestIosVersion(IosVersionReleaseDO iosVersionDO) {
        IosVersion iosVersion = appInfoDAO.selectLatestIosVersion(iosVersionDO.getTenantId(), iosVersionDO.getBuild());

        appInfoDAO.insertIosVersion(iosVersionDO);
        if( iosVersion == null && !CollectionUtils.isEmpty(iosVersionDO.getReleaseNote())){
            for(String language : iosVersionDO.getReleaseNote().keySet()){
                List<String> releaseNote = iosVersionDO.getReleaseNote().getOrDefault(language, Lists.newArrayList());
                releaseNote.forEach(
                        description -> appInfoDAO.insertIosVersionDescription(iosVersionDO.getBuild(),language, description)
                );
            }
        }
        return Result.Success();
    }

    /**
     * 获取指定key 的文案-区分语言
     *
     * @param request
     * @return
     */
    public String getDescription(DescriptionRequest request) {
        if (!centerNotifyConfig.getMessage().containsKey(request.getApp().getTenantId())) {
            logegr.info("getDescription no tenantId,tenantId:{}", request.getApp().getTenantId());
            return "";
        }


        if (!centerNotifyConfig.getMessage().get(request.getApp().getTenantId()).containsKey(request.getLanguage())) {
            logegr.info("getDescription no language,language:{}", request.getLanguage());

            return "";
        }
        return centerNotifyConfig.getMessage().get(request.getApp().getTenantId()).get(request.getLanguage()).get(request.getDescriptionKey());
    }

    /**
     * 根据国家过去节点地址
     *
     * @param request
     * @return
     */
    public String queryNode(LoginRequest request) {
        String version = request.getApp().getApiVersion();
        // app 默认节点
        Map<String, String> defaultNode = version.equals(AppConstants.defaultVersion) ? countryNodeConfig.getDefaultNode() :
                countryNodeV1Config.getDefaultNode();
        if (this.verifyEuCountryUser(request)) {
            logegr.info("欧洲用户走美国节点");
            return defaultNode.get(request.getApp().getTenantId());
        }

        if (StringUtils.isEmpty(request.getCountryNo())) {
            logegr.info("国家不能为空");
            throw new BaseException(INVALID_PARAMS, "国家不能为空");
        }

        // tenantId下国家no对应域名映射关系,
        Map<String, Map<String, String>> countryConfig = version.equals(AppConstants.defaultVersion) ? countryNodeConfig.getConfig() :
                countryNodeV1Config.getConfig();
        if (countryConfig == null) {
            logegr.info("国家配置不能为空");
            throw new BaseException(INVALID_PARAMS, "国家配置不能为空");
        }

        if (!countryConfig.containsKey(request.getApp().getTenantId())) {
            logegr.info("queryNode config has no tenantId:{}", request.getApp().getTenantId());
            return defaultNode.get(request.getApp().getTenantId());
        }

        String countNo = getAppCountryNo(request.getCountryNo(), request.getApp().getAppType(), request.getApp().getApiVersion());
        if (!countryConfig.get(request.getApp().getTenantId()).containsKey(countNo)) {
            logegr.info("queryNode config has no country:{}", countNo);
            return defaultNode.get(request.getApp().getTenantId());
        }

        //判断是否需要走迁移后的节点，是：走迁移后的节点映射查询，否：按国家正常查询节点
        boolean needTransfer = this.verifyTransferNode(request);
        if (needTransfer) {
            countNo = countryNodeConfig.getTransferNode().get(request.getApp().getTenantId()).get(countNo);
        }
        return countryConfig.get(request.getApp().getTenantId()).get(countNo);
    }

    /**
     * 根据国家代码选择节点地址
     *
     * @param request
     * @return
     */
    public ServerNode queryNodeV1(LoginRequest request) {
        String version = request.getApp().getApiVersion();
        // app 默认节点
        Map<String, String> defaultNode = version.equals(AppConstants.defaultVersion) ? countryNodeConfig.getDefaultNode() :
                countryNodeV1Config.getDefaultNode();
        ZendeskItem zendeskItem = null;
        if (this.verifyEuCountryUser(request)) {
            logegr.info("欧洲用户走美国节点");
            zendeskItem = zendeskConfig.queryZendeskItem(request.getApp().getTenantId(),ZENDESK_NODE_US);
            //如果租户需要根据平台区分节点，则把平台连接到key上
            String nodeUrl = getNodeUrlInDefaultNode(request.getApp().getTenantId(),
                    AppConstants.ZENDESK_NODE_US, request.getApp().getAppType(),
                    countryNodeV1Config.getAppTypeNodeUrlConfig(), defaultNode);

            return ServerNode.builder()
                    .nodeUrl(nodeUrl)
                    .zendeskHost(zendeskItem.getHost())
                    .appId(zendeskItem.getAppId())
                    .clientId(zendeskItem.getClientId())
                    .build();
        }

        if (StringUtils.isEmpty(request.getCountryNo())) {
            logegr.info("国家不能为空");
            throw new BaseException(INVALID_PARAMS, "国家不能为空");
        }

        // tenantId下国家no对应域名映射关系,
        Map<String, Map<String, String>> countryConfig = version.equals(AppConstants.defaultVersion) ? countryNodeConfig.getConfig() :
                countryNodeV1Config.getConfig();
        if (countryConfig == null) {
            logegr.info("国家配置不能为空");
            throw new BaseException(INVALID_PARAMS, "国家配置不能为空");
        }

        if (!countryConfig.containsKey(request.getApp().getTenantId())) {
            zendeskItem = zendeskConfig.queryZendeskItem(request.getApp().getTenantId(),ZENDESK_NODE_US);

            logegr.info("queryNode config has no tenantId:{}", request.getApp().getTenantId());
            return ServerNode.builder()
                    .nodeUrl(defaultNode.get(request.getApp().getTenantId()))
                    .zendeskHost(zendeskItem.getHost())
                    .clientId(zendeskItem.getClientId())
                    .appId(zendeskItem.getAppId())
                    .build();
        }

        // 有特殊规则的国家需要指定节点
        String countNo = mappingCountryNoToEurope(request.getApp().getTenantId(),getAppCountryNo(request.getCountryNo(), request.getApp().getAppType(), request.getApp().getApiVersion()));

        if (!countryConfig.get(request.getApp().getTenantId()).containsKey(countNo) ) {
            logegr.info("queryNode config has no country:{}", countNo);

            //如果租户需要根据平台区分节点，则把平台连接到key上
            String nodeUrl = getNodeUrlInDefaultNode(request.getApp().getTenantId(),
                    AppConstants.ZENDESK_NODE_US, request.getApp().getAppType(),
                    countryNodeV1Config.getAppTypeNodeUrlConfig(), defaultNode);
            zendeskItem = zendeskConfig.queryZendeskItem(request.getApp().getTenantId(),ZENDESK_NODE_US);

            return ServerNode.builder()
                    .nodeUrl(nodeUrl)
                    .zendeskHost(zendeskItem.getHost())
                    .appId(zendeskItem.getAppId())
                    .clientId(zendeskItem.getClientId())
                    .build();
        }

        //判断是否需要走迁移后的节点，是：走迁移后的节点映射查询，否：按国家正常查询节点
        boolean needTransfer = this.verifyTransferNode(request);
        if (needTransfer) {
            // 获取迁移后指向的节点
            countNo = countryNodeConfig.getTransferNode().get(request.getApp().getTenantId()).get(countNo);
        }

        logegr.info("queryNode countNo {}", countNo);
        //如果租户需要根据平台区分节点，则把平台连接到key上
        String nodeUrl = getNodeUrlInCountry(request.getApp().getTenantId(),
                countNo, request.getApp().getAppType(),
                countryNodeV1Config.getAppTypeNodeUrlConfig(), countryConfig);
        zendeskItem = zendeskConfig.queryZendeskItem(request.getApp().getTenantId(),countNo);

        return ServerNode.builder()
                .nodeUrl(nodeUrl)
                .zendeskHost(zendeskItem.getHost())
                .appId(zendeskItem.getAppId())
                .clientId(zendeskItem.getClientId())
                .build();
    }

    private String getNodeUrlInCountry(String tenantId, String countNo, String appType, Map<String, Map<String, Map<String, String>>> appTypeNodeUrlConfig, Map<String, Map<String, String>> countryConfig) {
        try {
            boolean tenantIdUseAppType = countryNodeV1Config.getAppTypeNodeUrlConfig().containsKey(tenantId);
            if (tenantIdUseAppType) {
                return appTypeNodeUrlConfig.get(tenantId).get(countNo).get(appType);
            } else {
                return countryConfig.get(tenantId).get(countNo);
            }
        } catch (Exception e) {
            logegr.error("getNodeUrlInCountry error, tenant {} , country {} , appType {} ", tenantId, countNo, appType, e);
            return countryConfig.get(tenantId).get(countNo);
        }
    }

    private String getNodeUrlInDefaultNode(String tenantId, String countryNo, String appType, Map<String, Map<String, Map<String, String>>> appTypeNodeUrlConfig, Map<String, String> defaultNode) {
        try {
            boolean tenantIdUseAppType = countryNodeV1Config.getAppTypeNodeUrlConfig().containsKey(tenantId);

            if (tenantIdUseAppType) {
                return appTypeNodeUrlConfig.get(tenantId).get(countryNo).get(appType);
            } else {
                return defaultNode.get(tenantId);
            }
        } catch (Exception e) {
            logegr.error("getNodeUrlInDefaultNode error, tenant {} , country {} , appType {} ", tenantId, countryNo, appType, e);
            return defaultNode.get(tenantId);
        }
    }


    /**
     * 根据国家代码选择节点地址
     * 只给APP-SDK使用，不考虑部分欧洲用户在美国节点的逻辑，不考虑节点迁移的逻辑
     *
     * @param request
     * @return
     */
    public Result<JSONObject> queryNodeForAppSdk(OpenSdkInit request) {
        final String countryNo = request.getCountryNo();
        if (StringUtils.isEmpty(countryNo)) {
            return Result.Error(INVALID_PARAMS, "countryNo不能为空");
        }
        AppInfo app = request.getApp();
        if (StringUtils.isEmpty(app.getTenantId())) {
            return Result.Error(INVALID_PARAMS, "tenantId不能为空");
        }
        if (AppType.nameOf(app.getAppType()) == null) {
            return Result.Error(INVALID_PARAMS, "appType参数错误");
        }
        if (StringUtils.isEmpty(app.getApiVersion())) {
            return Result.Error(INVALID_PARAMS, "apiVersion不能为空");
        }
        ServerNodeEnums serverNode;
        if (request.getServerNode() != null) {
            serverNode = ServerNodeEnums.valueOf(request.getServerNode().toUpperCase());
            if (serverNode == null) {
                return Result.Error(INVALID_PARAMS, "serverNo参数错误");
            }
        } else {
            serverNode = queryServerNodeByCountryNo(countryNo.toUpperCase());
        }
        String rootUrl = serverNodeConfig.getRootUrl().get(serverNode.name());
        return buildServerNode(app, serverNode.name(), rootUrl);
    }

    public ServerNodeEnums queryServerNodeByCountryNo(String countryNo) {
        if (countryNo.equals("CN")) {
            return ServerNodeEnums.CN;
        } else if (euCountryConfig.getConfig().containsKey(countryNo)) {
            return ServerNodeEnums.EU;
        } else {
            return ServerNodeEnums.US;
        }
    }

    // 需要 app.appType,app.tenantId
    public Result<JSONObject> buildServerNode(AppInfo app, String countNo, String nodeUrl) {
        logegr.info("queryNode countNo={},nodeUrl={}", countNo, nodeUrl);
        ZendeskItem zendeskItem = zendeskConfig.queryZendeskItem(app.getTenantId(),countNo);
        if (zendeskItem == null) {
            logegr.info("queryNode config has no zendeskItem countNo={}", countNo);
            return Result.Failure("zendeskItem配置缺失");
        }
        final String trackerUrl = tenantSettingService.getTenantSettingByTenantId(app.getTenantId()).getTrackerUrl();
        ServerNode serverNode = ServerNode.builder()
                .nodeUrl(nodeUrl)
                .serverNode(countNo)
                .zendeskHost(zendeskItem.getHost())
                .appId(zendeskItem.getAppId())
                .clientId(zendeskItem.getClientId())
                .trackerUrl(trackerUrl)
                .build();
        JSONObject json = (JSONObject) JSON.toJSON(serverNode);
        json.putAll(countlyConfig.getParamsByAppInfo(app));
        return new Result<>(json);
    }

    /**
     * 简化欧洲节点
     *
     * @param countryNo
     * @return
     */
    private String mappingCountryNoToEurope(String tenantId,String countryNo) {
        if (euCountryConfig.getConfig().containsKey(countryNo)) {
            //欧洲节点
            return AppConstants.ZENDESK_NODE_EU;
        }

        //判断是否指定国家对应节点
        return tenantServerNodeConfig.queryTenantServerNode(tenantId,countryNo);
    }

    /**
     * vicoHome 的域名处理，--v1 表示vicHome，国外节点国家选择中国则返回美国节点
     *
     * @param countryNo
     * @param appType
     * @param apiVersion
     * @return
     */
    private String getAppCountryNo(String countryNo, String appType, String apiVersion) {
        if (apiVersion.equals(AppConstants.defaultVersion)) {
            return countryNo;
        }
        if (appTypeIos.equals(appType)) {
            return countryNo;
        }


        return serverNode.equals(serverNodeCn) ? countryNo :
                countryNo.equals(serverNodeCn) ? serverNodeUs : countryNo;
    }

    /**
     * 是否需要走迁移后的节点
     *
     * @param request
     * @return
     */
    private boolean verifyTransferNode(LoginRequest request) {
        if (!needAppTransfer(request)) {
            return false;
        }

        if (countryNodeConfig.getAppTransfer() == null) {
            logegr.info("配置迁移说明为Null");
            throw new BaseException(INVALID_PARAMS, "未配置迁移说明");
        }
        Transfer transfer = countryNodeConfig.getAppTransfer().get(request.getApp().getTenantId());
        if (transfer == null) {
            logegr.info("未配置迁移说明:{}", request.getApp().getTenantId());
            throw new BaseException(INVALID_PARAMS, "未配置迁移说明");
        }

        if (!transfer.isVerify()) {
            logegr.info("是否开启迁移逻辑,{}", transfer);
            return false;
        }

        logegr.info("verifyTransferNode 过期时间:{}", transfer.getExpirationTime());
        long time = System.currentTimeMillis();
        return time > transfer.getExpirationTime();
    }

    /**
     * 判断是否配置迁移
     *
     * @param request
     * @return
     */
    private boolean needAppTransfer(AppRequestBase request) {
        String version = request.getApp().getApiVersion();

        if (VICOHOME_PACKAGE.equals(request.getApp().getBundle())) {
            //vicohome 不做校验(因为vicohome 与 vicoo 目前使用的相同的 tenantId)
            return false;
        }
        // 简化欧洲节点配置
        String countNo = mappingCountryNoToEurope(request.getApp().getTenantId(),getAppCountryNo(request.getCountryNo(), request.getApp().getAppType(), version));
        Map<String, Map<String, String>> transferNodeMap = version.equals(AppConstants.defaultVersion) ? countryNodeConfig.getTransferNode() :
                countryNodeV1Config.getTransferNode();
        if (transferNodeMap == null) {
            logegr.info("verifyTransferNode transferNodeMap null:{}", request.getApp().getTenantId());
            return false;
        }
        if (!transferNodeMap.containsKey(request.getApp().getTenantId())) {
            logegr.info("verifyTransferNode no tenantId:{}", request.getApp().getTenantId());
            //该tenantId 无对应需要迁移的节点
            return false;
        }

        if (!transferNodeMap.get(request.getApp().getTenantId()).containsKey(countNo)) {
            //tenantId 下 国家 无对应需要迁移的节点
            logegr.info("verifyTransferNode no country:{}", countNo);
            return false;
        }
        return true;
    }

    /**
     * 验证是否欧洲且走美国节点用户
     *
     * @param request
     * @return
     */
    private boolean verifyEuCountryUser(LoginRequest request) {
        boolean result = false;
        if (StringUtils.isEmpty(request.getUserName())) {
            return false;
        }
        if (euCountryConfig.getConfig().containsKey(request.getCountryNo())) {
            //请求欧洲节点
            String euUserName = iEuUserDao.getEuUser(request.getUserName());
            result = !StringUtils.isEmpty(euUserName);
        }
        return result;
    }

    /**
     * 验证迁移通告是否展示
     *
     * @param request
     * @return
     */
    public AppTransfer verifyTransfer(AppRequestBase request) {
        AppTransfer appTransfer;
        boolean needTransfer = this.needAppTransfer(request);
        if (!needTransfer) {
            appTransfer = AppTransfer.builder()
                    .shouldShow(false)
                    .shouldLogout(false)
                    .build();
            logegr.info("no need TransferNode");
            return appTransfer;
        }

        Transfer transfer = countryNodeConfig.getAppTransfer().get(request.getApp().getTenantId());
        long time = System.currentTimeMillis();
        if (transfer.isVerify()) {
            boolean shouldShow = ((time < transfer.getExpirationTime()) || (time > transfer.getExpirationTime() && transfer.isTransferAfterExpiration())) ? true : false;
            boolean shouldLogout = (shouldShow && (time > transfer.getExpirationTime())) ? true : false;
            String countNo = mappingCountryNoToEurope(request.getApp().getTenantId(),getAppCountryNo(request.getCountryNo(), request.getApp().getAppType(), request.getApp().getApiVersion()));

            if (!serverNode.equals(countNo) && shouldLogout) {
                // 迁移期已过，且不是被迁移的节点，不需要显示迁移声明(此时域名直接返回要迁移到的节点)

                logegr.info("迁移期已过");
                return AppTransfer.builder()
                        .shouldShow(false)
                        .shouldLogout(false)
                        .build();
            }
            String moreDetailUrl = "zh".endsWith(request.getLanguage()) ? (shouldLogout ? transfer.getMoreDetailUrlAfter().get("zh") : transfer.getMoreDetailUrlBefor().get("zh"))
                    : (shouldLogout ? transfer.getMoreDetailUrlAfter().get("en") : transfer.getMoreDetailUrlBefor().get("en"));
            appTransfer = AppTransfer.builder()
                    .shouldShow(shouldShow)
                    .shouldLogout(shouldLogout)
                    .downloadUrl(shouldShow ? transfer.getDownloadUrl().get(request.getApp().getAppType()) : "")
                    .moreDetailUrl(shouldShow ? moreDetailUrl : "")
                    .expirationTime(shouldShow ? transfer.getExpirationTime() : 0)
                    .build();
        } else {
            logegr.info("transfer 开关未开启");
            appTransfer = AppTransfer.builder()
                    .shouldShow(false)
                    .shouldLogout(false)
                    .build();
        }
        return appTransfer;
    }

    /**
     * 是否在迁移期
     *
     * @return
     */
    public boolean registerVerify(AppRequestBase requestBase) {
        AppTransfer appTransfer = this.verifyTransfer(requestBase);
        if (appTransfer.isShouldShow()) {
            return true;
        }
        return false;
    }

    /**
     * 获取新app下载地址
     *
     * @param tenantId
     * @return
     */
    public String queryDownloadUrl(String tenantId, String appType) {
        if (!countryNodeConfig.getAppTransfer().containsKey(tenantId)) {
            return "";
        }
        return countryNodeConfig.getAppTransfer().get(tenantId).getDownloadUrl().get(appType);
    }

    /**
     * 查询国家列表
     *
     * @param language
     * @return
     */
    @Cacheable(value = "countryList", key = "#language", unless = "#result==null")
    public List<Country> queryCountryList(String language) {
        return iCountryDAO.queryCountryList(language);
    }


    /**
     * 查询国家列表--模糊查询
     *
     * @return
     */
    public List<Country> queryCountry(CountryRequest request) {
        return iCountryDAO.queryCountry(request.getLanguage(), request.getCountry());
    }

    // 查询所有国家编码
    @Cacheable(value = "allCountryNo")
    public Set<String> queryAllCountryNo() {
        return iCountryDAO.queryAllCountryNo();
    }

    /**
     * 查询zendesk 支持
     *
     * @return
     */
    public ZendeskSupportResponse queryZendeskSupport(String language) {
        return ZendeskSupportResponse.builder()
                .zendeskChatSupport(this.zendeskChatSupport(language))
                .build();
    }

    /**
     * 是否支持zendesk chat
     *
     * @param language
     * @return
     */
    private boolean zendeskChatSupport(String language) {
        if (!serverNode.equals(AppConstants.ZENDESK_NODE_US)) {
            return false;
        }
        return APP_LANGUAGE_EN.equals(language);
    }

    /**
     * 返回定制模块地址
     *
     * @param request
     * @return
     */
    private List<String> getAppCustomizedMenuUrl(AppRequestBase request) {
        if (!appMenuConfig.getConfig().containsKey(request.getApp().getTenantId())) {
            return new ArrayList<>();
        }
        Map<String, List<String>> tenantIdMap = appMenuConfig.getConfig().get(request.getApp().getTenantId());
        // 未配置了相应语言，返回默认地址
        return tenantIdMap.get(tenantIdMap.containsKey(request.getLanguage()) ? request.getLanguage() : APP_LANGUAGE_EN);
    }
    
    public ResponseEntity getOrDownloadAppFile(String requestDomain, String tenantId, String templateCode, String templateLang, String fileName, Boolean responseFileUrl, Boolean download) {
        if(appFileS3 == null) {
            appFileS3 = AmazonS3ClientBuilder.standard().withRegion(appFileS3Config.getClientRegion()).withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(appFileS3Config.getS3AccessKey(), appFileS3Config.getS3SecretKey()))).withAccelerateModeEnabled(false).build();
        }

        String tenantIdFinal = org.apache.commons.lang3.StringUtils.lowerCase(tenantId);

        // get specified language app file key
        String bucketName = String.join("", appFileS3Config.getBucketPrefix(), "-", tenantIdFinal);
        String appFileHtmlPath = String.join("", tenantIdFinal, "/", templateCode, "/", templateLang, "/");
        String defaultAppFileHtmlPath = String.join("", tenantIdFinal, "/", templateCode, "/", "default");

        String appFileKey = null;
        ListObjectsV2Result listAppFileResult = appFileS3.listObjectsV2(bucketName, appFileHtmlPath);
        if(listAppFileResult.getKeyCount() == 0) {
            listAppFileResult = appFileS3.listObjectsV2(bucketName, defaultAppFileHtmlPath);
        }

        // get app file key if requestDomain match
        if(listAppFileResult.getKeyCount() != 0) {
            appFileKey = listAppFileResult.getObjectSummaries().stream().filter(summary -> StringUtils.isEmpty(requestDomain) || summary.getKey().contains(requestDomain.toLowerCase().replace(".", "_").replace("-", "_"))).map(S3ObjectSummary::getKey).findAny().orElse(null);
            if(StringUtils.isEmpty(appFileKey)) {
                appFileKey = listAppFileResult.getObjectSummaries().get(0).getKey();
            }
        }

        if(StringUtils.isEmpty(appFileKey)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(JsonUtil.toJson(Result.Failure("no such app file")));
        }

        ResponseEntity responseEntity = null;
        if(BooleanUtils.isTrue(responseFileUrl)) {
            URL appFileURL = appFileS3.generatePresignedUrl(bucketName, appFileKey, new Date(System.currentTimeMillis() + 12L * 60 * 60 * 1000), HttpMethod.GET);
            responseEntity = ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(Result.KVResult("url", appFileURL.toString()));
        } else {
            String appFileHtmlContent = null;
            try (InputStream htmlInputStream = appFileS3.getObject(new GetObjectRequest(bucketName, appFileKey)).getObjectContent()){
                appFileHtmlContent = IOUtils.toString(htmlInputStream);
            } catch(Exception e) {
                logegr.error("get appFileHtmlContent failed ", e);
            }

            BodyBuilder bodyBuilder = ResponseEntity.ok().contentType(MediaType.TEXT_HTML);
            if(BooleanUtils.isTrue(download)) {
                bodyBuilder.header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            };
            responseEntity = bodyBuilder.body(appFileHtmlContent);
        }
        return responseEntity;
    }
}
