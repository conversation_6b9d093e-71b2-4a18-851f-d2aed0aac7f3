package com.addx.iotcamera.service.device.model;

import com.addx.iotcamera.bean.device.model.DeviceModelAdminConfigDO;
import com.addx.iotcamera.dao.model.IDeviceModelAdminConfigDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 设备型号管理后台配置服务
 */
@Service
@Slf4j
public class DeviceModelAdminConfigService {

    @Resource
    private IDeviceModelAdminConfigDAO deviceModelAdminConfigDAO;

    /**
     * 根据型号查询管理后台配置
     */
    @Cacheable(value = "deviceModelAdminConfig", key = "#modelNo", unless = "#result==null")
    public DeviceModelAdminConfigDO queryByModelNo(String modelNo) {
        return deviceModelAdminConfigDAO.queryByModelNo(modelNo);
    }

    /**
     * 保存或更新管理后台配置
     */
    @CacheEvict(value = "deviceModelAdminConfig", key = "#config.modelNo")
    public int saveOrUpdate(DeviceModelAdminConfigDO config) {
        return deviceModelAdminConfigDAO.saveOrUpdate(config);
    }
} 