package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.UserTierDeviceRequest;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.GooglePaymentRequest;
import com.addx.iotcamera.bean.app.vip.TierInfo;
import com.addx.iotcamera.bean.app.vip.UserTierDeviceInfo;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.db.TierActiveConditionDO;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.uservip.DeviceExchangecodeTierDO;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.openapi.DeviceVipLog;
import com.addx.iotcamera.bean.openapi.PaasVipLevel;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.dao.UserTierDeviceDAO;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.device.home.DeviceHomeModeService;
import com.addx.iotcamera.service.pay.PurchaseSelectDeviceService;
import com.addx.iotcamera.service.vip.DeviceExchangecodeTierService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.service.vip.TierActiveConditionService;
import com.addx.iotcamera.service.vip.TierService;
import com.addx.iotcamera.util.TierIdUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.PayConstants.*;
import static com.addx.iotcamera.constants.UserConstants.SHARE_MSG_TYPE_KEY;
import static com.addx.iotcamera.enums.pay.TierTypeEnums.TIER_FREE_LICENSE;
import static com.addx.iotcamera.enums.user.ShareMessageTypeEnum.USER_TIER_DEVICE;
import static org.addx.iot.common.constant.AppConstants.TIER_DEVICE_LIMIT_APP;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.TIER_DEVICE_NUM_ERROE;

/**
 * 用户套餐设备service
 */
@Service
@Slf4j
public class UserTierDeviceService {

    @Autowired
    @Lazy
    private TierService tierService;

    @Autowired
    private UserVipService userVipService;

    @Autowired
    @Lazy
    private AdditionalUserTierService additionalUserTierService;

    @Autowired
    private VipService vipService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private TierActiveConditionService tierActiveConditionService;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserTierDeviceDAO userTierDeviceDAO;

    @Autowired
    private CenterNotifyConfig centerNotifyConfig;

    @Autowired
    private UserRoleService userRoleService;
    @Resource
    private IOrderDAO iOrderDAO;
    @Resource
    private IOrderProductDAO iOrderProductDAO;
    @Resource
    private PurchaseSelectDeviceService purchaseSelectDeviceService;

    @Autowired
    private DeviceSettingService deviceSettingService;

    @Resource
    private MqSender mqSender;

    @Value("${spring.kafka.topics.user-tier-device}")
    private String userTierDeviceTopic;

    private static Integer DEVICE_TIER_CACHE_TIME_SECONDS = 10 * 60;

    @Resource
    private Device4GService device4GService;

    @Resource
    @Lazy
    private FreeLicenseService freeLicenseService;

    @Resource
    @Lazy
    private UserVipActivateService userVipActivateService;

    @Autowired
    private DeviceExchangecodeTierService deviceExchangecodeTierService;

    @Resource
    @Lazy
    private DeviceAttributeService deviceAttributeService;


    @Resource
    @Lazy
    private DeviceHomeModeService deviceHomeModeService;

    @Resource
    @Lazy
    private DeviceService deviceService;

    public List<UserTierDeviceInfo> getManageUserTierDeviceInfoList(AppRequestBase request, Integer userId) {
        //当前用户套餐信息
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, request.getLanguage(), request.getApp().getTenantId());
        //当前生效的套餐id
        List<Integer> activeTierIdList = userVipTier.getTierIdList();
        if (CollectionUtils.isEmpty(activeTierIdList)) {
            return Collections.emptyList();
        }

        List<TierInfo> tenantIdTierInfoList = tierService.queryTierList(request.getApp().getTenantId(), request.getLanguage());

        //获取当前用户作为admin的设备-过去device_vip的类型
        final List<DeviceDO> deviceDOList = new ArrayList(filterOutDeviceVip(request.getApp().getTenantId(), filterOutShareDevice(userId, deviceInfoService.listDevicesByUserId(userId))));
        Collections.reverse(deviceDOList);

        //计算每个套餐下设备list
        Map<Integer, List<DeviceDO>> deviceActiveTierIdMap = new HashMap<>();
        deviceDOList.forEach(deviceDO -> {
            // 查询设备所属套餐id
            Integer deviceActiveTierId = getDeviceCurrentTier(userId, deviceDO.getSerialNumber());
            //设备所属套餐id不在用户当前生效的套餐id 范围内,刷新套餐下设备
            if(!activeTierIdList.contains(deviceActiveTierId)) {
                refreshUserDeviceTierCache(userId,true);
                deviceActiveTierId = getDeviceCurrentTier(userId, deviceDO.getSerialNumber());
            }

            if (!deviceActiveTierIdMap.containsKey(deviceActiveTierId)) {
                deviceActiveTierIdMap.put(deviceActiveTierId, new LinkedList<>());
            }
            deviceActiveTierIdMap.get(deviceActiveTierId).add(deviceDO);
        });

        // 根据生效tierId 计算
        List<UserTierDeviceInfo> userTierDeviceInfoList = activeTierIdList.stream().map(activeTierId -> {
            Tier tier = tierService.queryTierById(activeTierId);
            String tierName = null;
            Integer maxDeviceNum = tier.getMaxDeviceNum();
            if (ObjectUtils.equals(TierIdUtil.getTierLevelFromTierId(activeTierId), 0)) {
                tierName = centerNotifyConfig.getMessage().get(request.getApp().getTenantId()).get(request.getLanguage()).get("freeMessage");
            } else {
                TierInfo tierInfo = CollectionUtils.isEmpty(tenantIdTierInfoList) ? null : tenantIdTierInfoList.stream().filter(tenantIdTierInfo -> ObjectUtils.equals(tenantIdTierInfo.getId(), activeTierId)).findAny().orElse(null);
                if (tierInfo != null) {
                    tierName = tierInfo.getName();
                }
            }

            List<String> supportDeviceSnList = new LinkedList<>();
            TierActiveConditionDO tierActiveConditionDO = tierActiveConditionService.getById(request.getApp().getTenantId(), activeTierId, null);
            if (tierActiveConditionDO == null || StringUtils.isEmpty(tierActiveConditionDO.getSupportModelNo())) {
                supportDeviceSnList = deviceDOList.stream().filter(obj -> obj != null).map(DeviceDO::getSerialNumber).collect(Collectors.toList());
            } else if (tierActiveConditionDO != null && StringUtils.isNotEmpty(tierActiveConditionDO.getSupportModelNo())) {
                supportDeviceSnList = deviceDOList.stream().filter(deviceDO -> deviceDO != null && StringUtils.contains(tierActiveConditionDO.getSupportModelNo(), deviceDO.getModelNo())).map(DeviceDO::getSerialNumber).collect(Collectors.toList());
            }

            List<DeviceDO> tierDeviceList = deviceActiveTierIdMap.getOrDefault(activeTierId, Collections.emptyList());

            UserTierDeviceInfo userTierDeviceInfo = new UserTierDeviceInfo();
            userTierDeviceInfo.setTierId(activeTierId);
            userTierDeviceInfo.setTierGroupId(tier.getTierGroupId());
            userTierDeviceInfo.setTierName(tierName);
            userTierDeviceInfo.setMaxDeviceNum(maxDeviceNum);
            userTierDeviceInfo.setSupportDeviceSnList(supportDeviceSnList);
            userTierDeviceInfo.setActiveDeviceList(tierDeviceList);
            return userTierDeviceInfo;

        }).collect(Collectors.toList());

        // 未分配套餐的设备
        List<DeviceDO> noTierDeviceDOList = deviceActiveTierIdMap.get(null);

        if (CollectionUtils.isNotEmpty(noTierDeviceDOList)) {
            UserTierDeviceInfo userNoTierDeviceInfo = new UserTierDeviceInfo();
            userNoTierDeviceInfo.setTierId(-1);
            userNoTierDeviceInfo.setActiveDeviceList(noTierDeviceDOList);
            userNoTierDeviceInfo.setSupportDeviceSnList(deviceDOList.stream().map(DeviceDO::getSerialNumber).collect(Collectors.toList()));
            userTierDeviceInfoList.add(userNoTierDeviceInfo);
        }

        return userTierDeviceInfoList;
    }

    public List<UserTierDeviceInfo> getCurrentTierDeviceInfoList(AppRequestBase request, Integer userId) {
        List<UserTierDeviceInfo> currentTierDeviceInfoList = new LinkedList<>();
        // 生效的tierId 对应的生效设备、支持设备list，按tierId id 优先级排序
        List<UserTierDeviceInfo> tierDeviceInfoList = getManageUserTierDeviceInfoList(request, userId);
        if (CollectionUtils.isEmpty(tierDeviceInfoList)) {
            return Collections.emptyList();
        }

        currentTierDeviceInfoList.add(tierDeviceInfoList.get(0));

        // query additional tier device info
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, request.getLanguage(), request.getApp().getTenantId());
        final List<DeviceDO> deviceDOList = tierDeviceInfoList.get(0).getActiveDeviceList();
        Collections.reverse(deviceDOList);

        List<AdditionalUserTierInfo> currentAdditionalUserTierList = MapUtils.isEmpty(userVipTier.getAdditionalTierInfo()) ? Collections.emptyList() : (List<AdditionalUserTierInfo>) (userVipTier.getAdditionalTierInfo().get("activeList"));
        if (CollectionUtils.isNotEmpty(currentAdditionalUserTierList)) {
            List<AdditionalTierInfo> additionalTierInfoList = tierService.queryAdditionalTierList(request, userId);
            currentAdditionalUserTierList.forEach(currentAdditionalUserTier -> {
                AdditionalTierInfo additionalTierInfo = additionalTierInfoList.stream().filter(additionalTierInfoObj -> ObjectUtils.equals(additionalTierInfoObj.getTierUid(), currentAdditionalUserTier.getTierUid())).findAny().orElse(null);
                if (additionalTierInfo == null) {
                    return;
                }

                Set<String> tierSupportSns = CollectionUtils.isEmpty(additionalTierInfo.getSupportDeviceList()) ? Collections.emptySet() : additionalTierInfo.getSupportDeviceList().stream().map(DeviceDO::getSerialNumber).collect(Collectors.toSet());

                List<DeviceDO> currentAdditionalTierDeviceList = new LinkedList<>();
                int j = 0;
                while (j < deviceDOList.size()) {
                    DeviceDO deviceDO = deviceDOList.get(j++);
                    // check device support
                    if (tierSupportSns.contains(deviceDO.getSerialNumber())) {
                        currentAdditionalTierDeviceList.add(deviceDO);
                    }
                }

                UserTierDeviceInfo currentUserAdditionalTierDeviceInfo = new UserTierDeviceInfo();
                currentUserAdditionalTierDeviceInfo.setTierUid(additionalTierInfo.getTierUid());
                currentUserAdditionalTierDeviceInfo.setActiveDeviceList(currentAdditionalTierDeviceList);
                currentUserAdditionalTierDeviceInfo.setEndTime(currentAdditionalUserTier.getEffectiveTime());
                currentUserAdditionalTierDeviceInfo.setEndTime(currentAdditionalUserTier.getEndTime());
                currentTierDeviceInfoList.add(currentUserAdditionalTierDeviceInfo);
            });
        }

        return currentTierDeviceInfoList;
    }

    /**
     * 用户名下鸟类套餐
     * @param request
     * @param userId
     * @param deviceDOList
     * @return
     */
    public List<UserTierDeviceInfo> initAdditionalUserTierInfo(AppRequestBase request, Integer userId,List<DeviceDO> deviceDOList){
        if(CollectionUtils.isEmpty(deviceDOList)){
            return Lists.newArrayList();
        }
        
        List<UserTierDeviceInfo> currentTierDeviceInfoList = Lists.newArrayList();
        Collections.reverse(deviceDOList);
        //用户鸟类套餐信息
        List<AdditionalUserTierInfo> currentAdditionalUserTierList = additionalUserTierService.getActiveAdditionalUserTierInfo(userId, request.getApp().getTenantId(), request.getLanguage());

        if (CollectionUtils.isNotEmpty(currentAdditionalUserTierList)) {
            List<AdditionalTierInfo> additionalTierInfoList = tierService.queryAdditionalTierList(request, userId);
            currentAdditionalUserTierList.forEach(currentAdditionalUserTier -> {
                //匹配用户已有的鸟类套餐信息
                AdditionalTierInfo additionalTierInfo = additionalTierInfoList.stream()
                        .filter(additionalTierInfoObj -> ObjectUtils.equals(additionalTierInfoObj.getTierUid(), currentAdditionalUserTier.getTierUid())).findAny().orElse(null);
                if (additionalTierInfo == null) {
                    return;
                }

                Set<String> tierSupportSns = CollectionUtils.isEmpty(additionalTierInfo.getSupportDeviceList()) ? Collections.emptySet() : additionalTierInfo.getSupportDeviceList().stream().map(DeviceDO::getSerialNumber).collect(Collectors.toSet());

                List<DeviceDO> currentAdditionalTierDeviceList = deviceDOList.stream()
                        .filter(d -> CollectionUtils.isNotEmpty(tierSupportSns) && tierSupportSns.contains(d.getSerialNumber()))
                        .collect(Collectors.toList());

                UserTierDeviceInfo currentUserAdditionalTierDeviceInfo = new UserTierDeviceInfo();
                currentUserAdditionalTierDeviceInfo.setTierUid(additionalTierInfo.getTierUid());
                currentUserAdditionalTierDeviceInfo.setActiveDeviceList(currentAdditionalTierDeviceList);
                currentUserAdditionalTierDeviceInfo.setEndTime(currentAdditionalUserTier.getEffectiveTime());
                currentUserAdditionalTierDeviceInfo.setEndTime(currentAdditionalUserTier.getEndTime());
                currentTierDeviceInfoList.add(currentUserAdditionalTierDeviceInfo);
            });
        }
        return currentTierDeviceInfoList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserTierDeviceInfo(Integer userId, UserTierDeviceRequest userTierDeviceRequest) {
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, userTierDeviceRequest.getLanguage(), userTierDeviceRequest.getApp().getTenantId());
        List<Integer> activeTierIdList = this.initUserCurrentTierIdList(userVipTier);
        if (CollectionUtils.isEmpty(activeTierIdList)) {
            return;
        }

        boolean checkTierIdResult = userTierDeviceRequest.getList().stream()
                .allMatch(userTierDeviceInfo -> ObjectUtils.equals(userTierDeviceInfo.getTierId(), -1)
                        || activeTierIdList.contains(userTierDeviceInfo.getTierId()));
        if (!checkTierIdResult) {
            throw new BaseException(INVALID_PARAMS);
        }

        String tenantId = userTierDeviceRequest.getApp().getTenantId();

        //当前 套餐->设备set
        Map<Integer,Set<String>> userTierDeviceDOMap = this.queryUserTierDeviceDOListByUser(userId,tenantId).stream()
                .collect(Collectors.groupingBy(UserTierDeviceDO::getTierId,Collectors.mapping(UserTierDeviceDO::getSerialNumber,Collectors.toSet())));

        userTierDeviceRequest.getList().forEach(userTierDeviceInfo -> {
            Tier tier = tierService.queryTierById(userTierDeviceInfo.getTierId());
            if(tier.getLevel().equals(0)){
                // 免费套餐在后面的刷新操作里做
                return;
            }


            if(Optional.ofNullable(tier.getMaxDeviceNum()).orElse(0) > 0){
                //自营的app 有次限制
                if(TIER_DEVICE_LIMIT_APP.contains(tenantId)){
                    // 当前套餐支持的设备
                    List<DeviceDO> notInTierDeviceList = this.queryUserAdminDevice(userId,tenantId).stream()
                            .filter(deviceDO -> Optional.ofNullable(deviceDO.getSimThirdParty()).orElse(0) == 0)
                            .filter(deviceDO -> {
                                boolean device4G = deviceInfoService.checkIfDeviceUse4G(deviceDO.getSerialNumber());
                                if(device4G){
                                    return TierServiceTypeEnums.TIER_4G.getCode().equals(tier.getTierServiceType()) &&
                                            (!com.alipay.api.internal.util.StringUtils.isEmpty(deviceDO.getIccid()));
                                }else{
                                    return TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode().equals(tier.getTierServiceType());
                                }
                            }).collect(Collectors.toList());

                    if(tier.getMaxDeviceNum() > userTierDeviceInfo.getActiveDeviceSnList().size()
                            && notInTierDeviceList.size() > userTierDeviceInfo.getActiveDeviceSnList().size()){
                        //还有设备未设置在套餐下
                        throw new BaseException(TIER_DEVICE_NUM_ERROE);
                    }
                }
                if(tier.getMaxDeviceNum() < userTierDeviceInfo.getActiveDeviceSnList().size()){
                    //超过套餐限制
                    throw new BaseException(TIER_DEVICE_NUM_ERROE);
                }
            }

            userTierDeviceInfo.getActiveDeviceSnList().forEach(serialNumber -> {
                setDeviceActiveTier(userTierDeviceRequest.getApp().getTenantId(), userId, serialNumber, tier.getTierId(), tier.getTierGroupId());
            });
            log.debug("updateUserTierDeviceInfo userTierDeviceDOMap {}",userTierDeviceDOMap);
            if(userTierDeviceDOMap.containsKey(userTierDeviceInfo.getTierId())){
                Set<String> insertSns = new HashSet<>(userTierDeviceInfo.getActiveDeviceSnList());

                // 移除sn
                userTierDeviceDOMap.get(userTierDeviceInfo.getTierId()).stream()
                        .filter(existsn -> !insertSns.contains(existsn))
                        .forEach( serialNumber -> {
                            this.deleteUserTierDevice(userId,serialNumber,tier.getTierId(),tenantId);
                        });
            }
         });

        //刷新套餐下设备
        refreshUserDeviceTierCache(userId,userVipTier);
    }

    public void refreshUserDevice(Integer userId,String tenantId) {
        Map<String,Object> dataMap = Maps.newHashMap();
        dataMap.put("userId",userId);
        dataMap.put("tenantId", org.springframework.util.StringUtils.hasLength(tenantId) ? tenantId : userService.queryTenantIdById(userId));
        dataMap.put(SHARE_MSG_TYPE_KEY,USER_TIER_DEVICE.getCode());
        mqSender.send(userTierDeviceTopic,userId,dataMap);
    }

    public void onRemoveUserDevice(Integer userId, String serialNumber) {
        try {
            String tenantId = userService.queryTenantIdById(userId);
            this.deleteUserTierDevice(userId,serialNumber,null,tenantId);
            this.refreshUserDevice(userId,tenantId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "onRemoveUserDevice failed", e);
        }
    }

    /**
     * 刷新计算套餐下生效设备
     * @param userId
     */
    public void refreshUserDeviceTierCache(Integer userId,boolean readOnly) {
        this.refreshUserDeviceTierCache(userId,null,readOnly);
    }
    public void refreshUserDeviceTierCache(Integer userId,UserVipTier userVipTier) {
        this.refreshUserDeviceTierCache(userId,userVipTier,true);
    }
    public void refreshUserDeviceTierCache(Integer userId, UserVipTier userVipTier,boolean readOnly) {
        try {
            log.debug("refreshUserDeviceTierCache userId {} ", userId);

            String tenantId = userService.queryTenantIdById(userId);
            log.debug("tenantid is: {}", tenantId);
            if(StringUtils.isEmpty(tenantId)) {
                return;
            }

            // 避免重新查询数据
            if(userVipTier == null){
                userVipTier = userVipService.queryUserVipInfo(userId, "en", tenantId);
            }

            List<Integer> freeTierIdList = freeLicenseService.queryUserFreeTierIdList(userId);
            log.debug("userVipTier is {}, and freeTierIdList is: {}", userVipTier, freeTierIdList);
            //用户可指定套餐的设备列表->过滤分享设备->过滤设备vip
            List<DeviceDO> deviceDOList = filterOutDeviceVip(tenantId, userRoleService.queryUserDeviceAdmin(userId,readOnly));
            Collections.reverse(deviceDOList);
            //是否存在兑换码设备维度vip
            boolean hasExchangecodeDevice = deviceDOList.stream()
                    .map(DeviceDO::getSerialNumber)
                    .anyMatch(serialNumber -> deviceExchangecodeTierService.queryDeviceExchangecodeTierBySerialNumber(serialNumber) != null);
            // 云服务套餐组内各组正在生效的套餐
            if (!userVipService.hasUserVipActive(userVipTier) && CollectionUtils.isEmpty(freeTierIdList) && !hasExchangecodeDevice) {
                //删除已经失效的套餐对应的设备
                log.debug("删除已经失效的套餐对应的设备");
                this.deleteUserTierDeviceDeactivatedevice(userId,tenantId,Lists.newArrayList());
                return;
            }

            List<Integer> allActiveTierIdList = this.deleteTierDeviceExpire(userId,freeTierIdList,userVipTier);
            log.debug("allActiveTierIdList is: {}", allActiveTierIdList);
            // 清除已解绑的记录
            this.deleteUserTierDeviceDeactivatedevice(userId,tenantId,deviceDOList);
            //套餐切换时刷新套餐下设备
            //需要指定对比用户
            this.refrashUserTierDeviceVicoo(userId,allActiveTierIdList,tenantId,deviceDOList);

            //处理ai喂鸟器兑换码套餐
            this.refreshDeviceExchangecodeTier(userId, tenantId, deviceDOList, allActiveTierIdList);
            //vip 用户会初始化home mode数据
            deviceHomeModeService.initDeviceHomeMode(userId,vipService.isVipUser(userId));

            this.sendDeviceTierConfig(userId,allActiveTierIdList);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "refreshUserDeviceTierCache failed userId " + userId, e);
        }
    }

    private void refreshDeviceExchangecodeTier(Integer userId, String tenantId, List<DeviceDO> deviceDOList, List<Integer> allActiveTierIdList) {
        log.debug("refreshDeviceExchangecodeTier userId {}, deviceDOList is:{} ", userId, deviceDOList);
        deviceDOList.stream()
                .map(DeviceDO::getSerialNumber)
                .filter(serialNumber -> {
                    DeviceExchangecodeTierDO deviceExchangecodeTierDO = deviceExchangecodeTierService.queryDeviceExchangecodeTierBySerialNumber(serialNumber);
                    log.debug("deviceExchangecodeTierDO is:{}", deviceExchangecodeTierDO);
                    UserTierDeviceDO userTierDeviceDO = this.queryFromPrimaryByUserIdAndSerialNumber(userId, serialNumber);
                    log.debug("userTierDeviceDO is:{}", userTierDeviceDO);
                    return deviceExchangecodeTierDO != null && (userTierDeviceDO == null || FREE_LICENSE_TIER.contains(userTierDeviceDO.getTierId()));
                })
                .forEach(serialNumber -> {
                    allActiveTierIdList.clear();
                    allActiveTierIdList.add(AI_BIRD_DEVICE_TIER);
                    this.deleteUserTierDevice(userId, serialNumber, null, tenantId);
                    this.setDeviceActiveTier(tenantId, userId, serialNumber, AI_BIRD_DEVICE_TIER, null);
                });
    }

    public UserTierDeviceDO queryFromPrimaryByUserIdAndSerialNumber(Integer userId, String serialNumber) {
        return userTierDeviceDAO.selectFromPrimaryByUserIdAndSerialNumber(userId, serialNumber);
    }

    /**
     * 清除已解绑的设备（防止解绑时未处理的）
     */
    private void deleteUserTierDeviceDeactivatedevice(Integer userId,String tenantId,List<DeviceDO> deviceDOList){
        Set<String> userDeviceSet = deviceDOList.stream().map(DeviceDO::getSerialNumber).collect(Collectors.toSet());
        userTierDeviceDAO.getByUserIdReadOnly(tenantId,userId).stream()
                .filter(userTierDeviceDO -> !userDeviceSet.contains(userTierDeviceDO.getSerialNumber()))
                .forEach(userTierDeviceDO -> this.deleteUserTierDevice(userId,userTierDeviceDO.getSerialNumber(),null,tenantId));
    }

    /**
     * 下发设备config
     * @param userId
     * @param activeTierIdList
     */
    private void sendDeviceTierConfig(Integer userId,List<Integer> activeTierIdList){
        // 下发套餐参数给设备
        if(!CollectionUtils.isEmpty(activeTierIdList)){
            for(Integer tierId : activeTierIdList){
                userVipActivateService.sendTierParam2DeviceConfig(userId, tierId);
            }
        }

        userVipActivateService.sendTierParam2DeviceConfig(userId, null);
    }


    private List<Integer> deleteTierDeviceExpire(Integer userId,
                                                 List<Integer> deviceTierList,
                                                 UserVipTier userVipTier){

        List<Integer> allActiveTierIdList = this.initUserCurrentTierIdList(userVipTier);
        if(!CollectionUtils.isEmpty(allActiveTierIdList) && !CollectionUtils.isEmpty(deviceTierList)){
            //删除已经失效的套餐对应的设备
            userTierDeviceDAO.deleteByUserIdAndTierList(userId,allActiveTierIdList);
            allActiveTierIdList.addAll(deviceTierList);
        }else{
            allActiveTierIdList.addAll(deviceTierList);
            userTierDeviceDAO.deleteByUserIdAndTierList(userId,allActiveTierIdList);
        }
        return allActiveTierIdList;
    }

    public void verifyUserBirdAdditional(Integer userId,Integer tierId) {
        if(TierIdUtil.isFreeTier1(tierId) || TierIdUtil.isFreeTier2(tierId) || tierId <= 0){
            //只有免费套餐，不支持鸟类
            return;
        }

        try{
            UserVipDO userVipDO = userVipService.queryUserCurrentVip(userId,TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
            if(userVipDO == null){
                //当前无套餐
                return;
            }

            Long orderId = Optional.ofNullable(userVipDO.getOrderId()).orElse(0L);
            if(orderId.equals(0L)){
                // 手动增加的vip记录
                return;
            }

            OrderDO orderDO = iOrderDAO.queryByOrderId(userVipDO.getOrderId());
            if(orderDO == null){
                // 手动增加的vip记录
                return;
            }
            OrderProductDo orderProductDo = iOrderProductDAO.selectOrderProductByOrderId(orderDO.getId());
            if(orderProductDo == null){
                // 不存在商品记录
                return;
            }

            //用户设备支持鸟类（如果支付时指定设备则只判断指定的设备）
            List<String> buyTierDevice = this.initUserTierDeviceList(orderDO,tierId,null);
            // 赠送鸟类套餐
            additionalUserTierService.verifyAdditionalTierFree(orderDO,orderProductDo,buyTierDevice);
        }catch (Exception e){
            log.error("验证增加鸟类套餐异常",e);
        }
    }

    /**
     * 初始化设备tier id
     * @param userVipTier
     * @return
     */

    private List<Integer> initUserCurrentTierIdList(UserVipTier userVipTier){
        List<Integer> activeTierIdList = userVipTier.getTierIdList();
        List<Integer> tier4GList = userVipService.queryTierId4GList(userVipTier);

        List<Integer> allActiveTierIdList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(tier4GList)){
            allActiveTierIdList.addAll(tier4GList);
        }
        if(!CollectionUtils.isEmpty(activeTierIdList)){
            allActiveTierIdList.addAll(activeTierIdList);
        }
        return allActiveTierIdList;
    }

    /**
     * vicoo 套餐2.0 设备数量限制的套餐刷新指定设备逻辑(套餐切换)
     */
    private void refrashUserTierDeviceVicoo(Integer userId,List<Integer> activeTierIdList,String tenantId,List<DeviceDO> deviceDOList){
        for(Integer activeTierId : activeTierIdList)  {
            Tier tier = tierService.queryTierById(activeTierId);
            this.deleteDeviceTierServiceType(userId,tenantId,tier);

            Set<String> serialNumberSet ;
            if(tier.getLevel().equals(0)){
                //免费套餐，无设备数量限制
                serialNumberSet = this.initUserFreeTierDevice(userId,tenantId,deviceDOList,tier);
            }else{
                //当前生效的套餐
                UserVipDO userVipDO = userVipService.queryUserVipByActiveTierId(userId,activeTierId);
                if(userVipDO == null){
                    log.error("refrashUserTierDeviceVicoo userVip null {}",activeTierId );
                    continue;
                }

                //查询购买订单信息，为了下一步获取购买时指定的设备
                Long orderId = Optional.ofNullable(userVipDO.getOrderId()).orElse(0L);
                OrderDO orderDO = iOrderDAO.queryByOrderId(orderId);
                if(orderDO == null){
                    log.error("refrashUserTierDeviceVicoo order null {}",userVipDO );
                    continue;
                }

                serialNumberSet = new HashSet<>(this.initUserTierDeviceList(orderDO,userVipDO.getTierId(),deviceDOList,userVipDO.getTradeNo()));
            }

            log.debug("refrashUserTierDeviceVicoo 套餐刷新 tierId {} sn {}",tier.getTierId(),serialNumberSet);
            if(CollectionUtils.isEmpty(serialNumberSet)){
                log.debug("刷新用户设备 用户 {} 指定设备已不在用户名下 {}",userId,serialNumberSet);
                continue;
            }

            deviceDOList.stream().filter(deviceDO -> serialNumberSet.contains(deviceDO.getSerialNumber()))
                .forEach(deviceDO -> {
                    setDeviceActiveTier(tenantId, userId, deviceDO.getSerialNumber(), tier.getTierId(), tier.getTierGroupId());
                });
        }
    }

    /**
     * 计算免费套餐设备
     * @param userId
     * @param tenantId
     * @param deviceDOList
     * @param tier
     * @return
     */
    public Set<String> initUserFreeTierDevice(Integer userId,String tenantId,List<DeviceDO> deviceDOList,Tier tier){
        Set<String> serialNumberSet;
        Integer currentTime = (int) Instant.now().getEpochSecond();
        // 已指定到非免费套餐的设备--原有属于付费套餐的设备
        Set<String> existDeviceSnSet = this.queryUserTierDeviceDOListByUser(userId,tenantId).stream()
                .filter(userTier -> userTier.getTierId()%10 > 0)
                .map(UserTierDeviceDO::getSerialNumber)
                .collect(Collectors.toSet());
        if(tier.getTierType().equals(TIER_FREE_LICENSE.getCode())){
            List<UserDeviceFreeTierDO> userDeviceFreeTierDOS = freeLicenseService.queryUserDeviceByFreeTierId(userId,tier.getTierId());
            serialNumberSet = userDeviceFreeTierDOS.stream()
                    .filter(userDeviceFreeTierDO -> userDeviceFreeTierDO.getEndTime() > currentTime && !existDeviceSnSet.contains(userDeviceFreeTierDO.getSerialNumber()))
                    .map(UserDeviceFreeTierDO::getSerialNumber)
                    .collect(Collectors.toSet());

            // 清除已过期的设备
            userDeviceFreeTierDOS.stream()
                    .filter(userDeviceFreeTierDO -> userDeviceFreeTierDO.getEndTime() < currentTime)
                    .forEach(userDeviceFreeTierDO -> this.deleteUserTierDevice(userId,userDeviceFreeTierDO.getSerialNumber(),tier.getTierId(),tenantId));
        }else {

            if(!CollectionUtils.isEmpty(existDeviceSnSet)){
                existDeviceSnSet.forEach(
                        //删除已存在的非免费套餐设备
                        sn -> this.deleteUserTierDevice(userId,sn,tier.getTierId(),tenantId)
                );
            }

            //需要设置免费套餐的设备
            serialNumberSet = deviceDOList.stream()
                    .map(DeviceDO::getSerialNumber)
                    .filter( serialNumber -> !existDeviceSnSet.contains(serialNumber) && !deviceInfoService.checkIfDeviceUse4G(serialNumber))
                    .collect(Collectors.toSet());
        }
        return serialNumberSet;
    }

    public void setDeviceActiveTier(String tenantId, Integer userId, String serialNumber, Integer tierId, Integer tierGroupId) {
        String deviceTierCacheKey = getDeviceTierCacheKey(userId, serialNumber);
        redisService.set(deviceTierCacheKey, String.join("", String.valueOf(tierId), "_", String.valueOf(tierGroupId)), DEVICE_TIER_CACHE_TIME_SECONDS);
        log.debug("setDeviceActiveTier tenantId {} userId {} serialNumber {} tierId {} tierGroupId {}", tenantId, userId, serialNumber, tierId, tierGroupId);

        if (deviceInfoService.checkIf4GDeviceHasOfficialSimCard(serialNumber)) {
            deviceSettingService.pushSettingMsg(serialNumber);
        }

        if(userTierDeviceDAO.queryByUserTierIdAndSerialNumber(userId, tierId, serialNumber) != null){
            return;
        }

        userTierDeviceDAO.deleteByUserIdAndSerialNumber(tenantId, userId, serialNumber,null,null);
        UserTierDeviceDO userTierDeviceDO = new UserTierDeviceDO();
        userTierDeviceDO.setTenantId(tenantId);
        userTierDeviceDO.setUserId(userId);
        userTierDeviceDO.setSerialNumber(serialNumber);
        userTierDeviceDO.setTierId(tierId);
        userTierDeviceDAO.batchUpdateUserTierDevice(Collections.singletonList(userTierDeviceDO));

        //设备变成vip 设备时，将pushIgnored 关闭
        // 临时逻辑，等app home mode 发版后可以去掉
        this.devicePushIgnoredAfterVip(tierId,serialNumber);
        //vip状态变更引起设备设置变化
        this.modifyDeviceSettingAfterTierChange(serialNumber,tierId);
    }


    public void deleteUserTierDevice(Integer userId,String serialNumber,Integer tierId,String tenantId){
        String deviceTierCacheKey = getDeviceTierCacheKey(userId, serialNumber);
        redisService.delete(deviceTierCacheKey);
        userTierDeviceDAO.deleteByUserIdAndSerialNumber(tenantId, userId, serialNumber,tierId,null);
    }

    public Integer getDeviceCurrentTier(Integer userId, String serialNumber) {
        Tuple2<Integer, Integer> deviceCurrentTierWithTierGroupId = getDeviceCurrentTierWithTierGroupId(userId, serialNumber);
        return deviceCurrentTierWithTierGroupId != null ? deviceCurrentTierWithTierGroupId.v0() : null;
    }

    /*
    套餐组需求文档：https://a4x-paas.feishu.cn/wiki/wikcn9fAr0m9xw1RrXCHeAO5n4b
    实现逻辑参考：com.addx.iotcamera.service.UserService.getAppFormOptions
    无套餐、免费套餐2、免费套餐3不能设置一分钟以下的拍摄间隔
    */
    public boolean getIsNoVipOrFreeTier2(Integer userId, String serialNumber) {
        if (vipService.isVipDevice(userId, serialNumber)) return false;
        Integer currentTierId = getDeviceCurrentTier(userId, serialNumber);
        if(currentTierId == null){
            return true;
        }
        Tier tier = tierService.queryTierById(currentTierId);
        return TierIdUtil.getTierLevelFromTierId(currentTierId) < 0 || TierIdUtil.isFreeTier2(currentTierId) ||
                tier.getTierType().equals(TIER_FREE_LICENSE.getCode());
    }

    public Tuple2<Integer, Integer> getDeviceCurrentTierWithTierGroupId(Integer userId, String serialNumber) {
        String deviceTierCacheKey = getDeviceTierCacheKey(userId, serialNumber);
        String tierIdStr = redisService.getFromSlave(deviceTierCacheKey);
        if (StringUtils.equals(tierIdStr, "NULL")) {
            return null;
        }

        String[] tierIdStrs = StringUtils.isEmpty(tierIdStr) ? new String[0] : tierIdStr.split("_");
        Integer currentTierId = (ArrayUtils.getLength(tierIdStrs) < 1 || !NumberUtils.isNumber(tierIdStrs[0])) ? null : Integer.valueOf(tierIdStrs[0]);
        Integer tierGroupId = (ArrayUtils.getLength(tierIdStrs) < 2 || !NumberUtils.isNumber(tierIdStrs[1])) ? null : Integer.valueOf(tierIdStrs[1]);
        log.debug("getDeviceCurrentTierWithTierGroupId userId {} serialNumber {} query from redis, currentTierId {}", userId, serialNumber, currentTierId);

        if (currentTierId == null) {
            String tenantId = userService.queryTenantIdById(userId);
            List<UserTierDeviceDO> userTierDeviceDOList = userTierDeviceDAO.getByUserId(tenantId, userId);
            currentTierId = userTierDeviceDOList.stream()
                    .filter(userTierDeviceDO -> Objects.equals(userTierDeviceDO.getSerialNumber(), serialNumber))
                    .map(UserTierDeviceDO::getTierId).findAny().orElse(null);
            log.debug("getDeviceCurrentTierWithTierGroupId userId {} serialNumber {} query from database, currentTierId {}", userId, serialNumber, currentTierId);

            if (currentTierId == null || currentTierId <= 0) {
                log.debug("no currentTierId set null to redis");
                redisService.set(deviceTierCacheKey, "NULL", 600);
                return null;
            }

            Tier tier = tierService.queryTierById(currentTierId);
            tierIdStr = tier.getTierId() + "_" + tier.getTierGroupId();
            redisService.set(deviceTierCacheKey, tierIdStr, 600);
        }


        return new Tuple2(currentTierId, tierGroupId);
    }

    /**
     * 判断设备是否在购买指定的设备中
     * @param userId
     * @param serialNumber
     * @return
     */
    public Tier queryUserDeviceCurrentTier(Integer userId, String serialNumber){
        boolean device4G = deviceInfoService.checkIfDeviceUse4G(serialNumber);
        UserVipDO userVipDO = userVipService.queryUserCurrentVip(userId , device4G ? TierServiceTypeEnums.TIER_4G.getCode() : TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode());
        if(userVipDO == null){
            return null;
        }

        Tier tier =  tierService.queryTierById(userVipDO.getTierId());
        int maxDeviceLimit = Optional.ofNullable(tier.getMaxDeviceNum()).orElse(0);
        if(maxDeviceLimit == 0){
            return tier;
        }

        OrderDO orderDO = iOrderDAO.queryByOrderId(userVipDO.getOrderId());
        if(orderDO == null){
            return null;
        }

        if(StringUtils.isBlank(orderDO.getExtend())){
            return null;
        }

        List<String> tierDeviceList = StringUtils.isEmpty(userVipDO.getTradeNo()) ? Lists.newArrayList() :
                purchaseSelectDeviceService.queryPurchaseSelectDevice(userVipDO.getTradeNo());
        if(CollectionUtils.isEmpty(tierDeviceList)){
            tierDeviceList = this.initPaymentDeviceSn(orderDO);
        }
        Set<String> deviceSn = new HashSet<>(tierDeviceList);

        return deviceSn.contains(serialNumber) ? tierService.queryTierById(userVipDO.getTierId()) : null;
    }

    public List<DeviceDO> filterOutDeviceVip(String tenantId, List<DeviceDO> deviceDOList) {
        List<DeviceDO> filteredDeviceDOList = deviceDOList.stream().map(deviceDO -> {
            DeviceVipLog deviceVipLog = vipService.queryLastDeviceVip(tenantId, deviceDO.getSerialNumber());
            return (deviceVipLog == null || (PaasVipLevel.codeOf(deviceVipLog.getVipLevel()) == PaasVipLevel.NO_VIP)) ? deviceDO : null;
        }).filter(obj -> obj != null).collect(Collectors.toList());
        return filteredDeviceDOList;
    }

    /**
     * 过滤分享设备
     * @param userId
     * @param deviceDOList
     * @return
     */
    public List<DeviceDO> filterOutShareDevice(Integer userId, List<DeviceDO> deviceDOList) {
        if(CollectionUtils.isEmpty(deviceDOList)) {
            return deviceDOList;
        }

        Set<String> adminDeviceSns = Sets.newHashSet();
        if(deviceDOList.get(0).getAdminId() == null){
            List<UserRoleDO> adminUserRoleDOList = userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode());
            if(CollectionUtils.isEmpty(adminUserRoleDOList)) {
                return Collections.emptyList();
            }
            adminDeviceSns = adminUserRoleDOList.stream().map(UserRoleDO::getSerialNumber).collect(Collectors.toSet());
        }

        Set<String> finalAdminDeviceSns = adminDeviceSns;
        return deviceDOList.stream().filter(deviceDO -> {
            if(deviceDO.getAdminId() == null){
                return finalAdminDeviceSns.contains(deviceDO.getSerialNumber());
            }else{
                return deviceDO.getAdminId().equals(userId);
            }
        }).collect(Collectors.toList());
    }

    public boolean isDeviceAdditionalTierActive(Integer userId, String serialNumber, String tierUid) {
        return redisService.containsKey(getDeviceAdditionalTierCacheKey(userId, serialNumber, tierUid));
    }

    private String getDeviceTierCacheKey(Integer userId, String serialNumber) {
        return String.join("", "deviceTier#", String.valueOf(userId), "_", String.valueOf(serialNumber));
    }

    private String getDeviceAdditionalTierCacheKey(Integer userId, String serialNumber, String tierUid) {
        return String.join("", "deviceAdditionalTier#", String.valueOf(userId), "_", String.valueOf(serialNumber), "_", tierUid);
    }

    /**
     * 获取已设置套餐设备list
     * @param userId
     * @param tier
     * @return
     */
    public Set<String> getActiveTierDeviceList(Integer userId,Tier tier){
        return userTierDeviceDAO.getByUserTierId(userId,tier.getTierId()).stream().map(UserTierDeviceDO::getSerialNumber).collect(Collectors.toSet());
    }

    public UserTierDeviceDO queryByUserIdAndSerialNumber(Integer userId, String serialNumber) {
        return userTierDeviceDAO.selectByUserIdAndSerialNumber(userId, serialNumber);
    }

    public boolean isDeviceCanTriggerPir(Integer userId, String serialNUmber) {
        return queryByUserIdAndSerialNumber(userId, serialNUmber) != null;
    }

    /**
     * 获取用户名下绑定的非设备vip的设备
     * @param userId
     * @param tenantId
     * @return
     */
    public List<DeviceDO> queryUserAdminDevice(Integer userId,String tenantId){
        List<DeviceDO> userDeviceList = deviceInfoService.listDevicesByUserId(userId);
        //过滤分享设备
        //过滤有设备vip的设备
        return filterOutDeviceVip(tenantId, filterOutShareDevice(userId, userDeviceList));
    }

    public List<UserTierDeviceDO> queryUserTierDeviceDOListByUser(Integer userId,String tenantId){
        return userTierDeviceDAO.getByUserId(tenantId,userId);
    }

    /**
     * 套餐生效的设备
     * @param bugTierId
     * @param orderDO
     */
    public List<String> initUserTierDeviceList(OrderDO orderDO,Integer bugTierId,List<DeviceDO> deviceDOList){
        return this.initUserTierDeviceList(orderDO,bugTierId,deviceDOList,null);
    }

    public List<String> initUserTierDeviceList(OrderDO orderDO,Integer bugTierId,List<DeviceDO> deviceDOList,String tradeNo){
        if(CollectionUtils.isEmpty(deviceDOList)){
            deviceDOList = this.queryUserAdminDevice(orderDO.getUserId(),orderDO.getTenantId());
        }

        Tier tier = tierService.queryTierById(bugTierId);
        if(tier.getMaxDeviceNum() == null || tier.getMaxDeviceNum().equals(0)){
            return deviceDOList
                    .stream()
                    // 云服套餐只能指定wifi设备，4G套餐只能指定4G设备
                    .filter(deviceDO -> {
                        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(deviceDO.getSerialNumber());
                        if(device4GSimDO != null){
                            return TierServiceTypeEnums.TIER_4G.getCode().equals(tier.getTierServiceType())
                                    && (!StringUtils.isEmpty(device4GSimDO.getIccid())
                                    && device4GSimDO.getSimThirdParty().equals(0));
                        }else{
                            return TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode().equals(tier.getTierServiceType());
                        }
                    })
                    .map(DeviceDO::getSerialNumber)
                    .collect(Collectors.toList());
        }

        return this.queryDeviceFromUser(orderDO,tradeNo,tier,true,deviceDOList);
    }
    /**
     * 套餐购买时指定的设备
     * @param orderDO
     * @return
     */
    public List<String> initPaymentDeviceSn(OrderDO orderDO){
        List<String> tierDeviceList = Lists.newArrayList();

        //查询支付时指定的设备sn
        if(orderDO.getOrderType().equals(PaymentTypeEnums.APPLE.getCode())){
            ApplePaymentRequest request = JSONObject.parseObject(orderDO.getExtend(),ApplePaymentRequest.class);
            tierDeviceList = request.getTierDeviceList();
        }else if(orderDO.getOrderType().equals(PaymentTypeEnums.GOOGLE.getCode())){
            GooglePaymentRequest request = JSONObject.parseObject(orderDO.getExtend(),GooglePaymentRequest.class);
            tierDeviceList = request.getTierDeviceList();
        }
        return CollectionUtils.isEmpty(tierDeviceList) ? Lists.newArrayList() : tierDeviceList;
    }


    private List<String> queryDeviceFromUser(OrderDO orderDO,String tradeNo,Tier tier,boolean filterFree,List<DeviceDO> deviceDOList){
        deviceDOList = deviceDOList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Set<String> userAdminSn = deviceDOList.stream()
                .map(DeviceDO::getSerialNumber).collect(Collectors.toSet());

        //已指定套餐的设备
        List<UserTierDeviceDO> userTierDeviceDOList = this.queryUserTierDeviceDOListByUser(orderDO.getUserId(),orderDO.getTenantId());
        if(filterFree){
            userTierDeviceDOList = userTierDeviceDOList.stream().filter(ud -> ud.getTierId()%10 > 0).collect(Collectors.toList());
        }
        // 当前套餐已指定的设备
        Set<String> existDeviceSnSet = userTierDeviceDOList.stream()
                .filter(ud -> ud.getTierId().equals(tier.getTierId()))
                .map(UserTierDeviceDO::getSerialNumber)
                .collect(Collectors.toSet());

        // 已指定套餐的设备.homeguard 可能存在多个同时生效的套餐
        Set<String> finalAllExistDeviceSnSet = userTierDeviceDOList.stream()
                .filter(ud -> TierIdUtil.getTierLevelFromTierId(ud.getTierId()) > 0)
                .map(UserTierDeviceDO::getSerialNumber)
                .collect(Collectors.toSet());
        log.debug("计算套餐下设备 tierId {} userTierDeviceDOList 1 套餐已指定的设备 {}",tier.getTierId(),existDeviceSnSet);


        if(tier.getMaxDeviceNum() == existDeviceSnSet.size()){
            return new ArrayList<>(existDeviceSnSet);
        }else if(tier.getMaxDeviceNum() < existDeviceSnSet.size()){
            List<String> existDeviceSnList = new ArrayList<>(existDeviceSnSet);
            // 多分配了设备
            for(int i = tier.getMaxDeviceNum() ; i < existDeviceSnList.size() ; i++){
                deleteUserTierDevice(orderDO.getUserId(), existDeviceSnList.get(i),tier.getTierId(),orderDO.getTenantId());
            }

            return existDeviceSnList.subList(0,tier.getMaxDeviceNum());
        }

        //套餐下无指定设备，则使用购买时指定的
        if(CollectionUtils.isEmpty(existDeviceSnSet)){
            List<String> purchaseSelectDeviceList = StringUtils.isEmpty(tradeNo) ? Lists.newArrayList() :
                    purchaseSelectDeviceService.queryPurchaseSelectDevice(tradeNo);
            if(CollectionUtils.isEmpty(purchaseSelectDeviceList)){
                purchaseSelectDeviceList = this.initPaymentDeviceSn(orderDO);
            }
            purchaseSelectDeviceList = purchaseSelectDeviceList.stream()
                    .filter(userAdminSn::contains)
                    .filter(sn -> !finalAllExistDeviceSnSet.contains(sn))
                    .filter(sn -> {
                        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(sn);
                        if(device4GSimDO != null){
                            return TierServiceTypeEnums.TIER_4G.getCode().equals(tier.getTierServiceType())
                                    && (org.springframework.util.StringUtils.hasLength(device4GSimDO.getIccid())
                                    && device4GSimDO.getSimThirdParty().equals(0));
                        }else{
                            return TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode().equals(tier.getTierServiceType());
                        }
                    })
                    .collect(Collectors.toList());
            if(tier.getMaxDeviceNum() == purchaseSelectDeviceList.size()){
                return new ArrayList<>(purchaseSelectDeviceList);
            }

            existDeviceSnSet = new HashSet<>(purchaseSelectDeviceList);
        }


        //返回未指定套餐的设备，数量=套餐数量限制
        Set<String> finalExistDeviceSnSet = existDeviceSnSet;
        List<String> deviceSnList = deviceDOList.stream()
                // 云服套餐只能指定wifi设备，4G套餐只能指定4G设备
                .map(DeviceDO::getSerialNumber)
                .filter(userAdminSn::contains)
                .filter(sn -> {
                    Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(sn);
                    if(device4GSimDO != null){
                        return TierServiceTypeEnums.TIER_4G.getCode().equals(tier.getTierServiceType())
                                && (org.springframework.util.StringUtils.hasLength(device4GSimDO.getIccid())
                                && device4GSimDO.getSimThirdParty().equals(0));
                    }else{
                        return TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode().equals(tier.getTierServiceType());
                    }
                })
                .filter(serialNumber -> !finalAllExistDeviceSnSet.contains(serialNumber) && !finalExistDeviceSnSet.contains(serialNumber))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deviceSnList)){
            return new ArrayList<>(existDeviceSnSet);
        }

        List<String> newAddSerialNumberList = tier.getMaxDeviceNum() == null ? deviceSnList :
                tier.getMaxDeviceNum() <= existDeviceSnSet.size() ? new ArrayList<>(existDeviceSnSet) :
                deviceSnList.subList(0,Math.min(tier.getMaxDeviceNum()-existDeviceSnSet.size(),deviceSnList.size()));
        newAddSerialNumberList.addAll(existDeviceSnSet);
        return newAddSerialNumberList;
    }


    public void checkDevice4GTierServiceType(String serialNumber){
        MDC.put(MDCKeys.SERIAL_NUMBER, serialNumber);
        Integer adminId = userRoleService.getDeviceAdminUser(serialNumber);
        if(adminId.equals(0)){
            log.info("设备非绑定状态 {}",serialNumber);
            return;
        }
        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(serialNumber);
        if(device4GSimDO == null){
            log.info("不是4G设备 {}",serialNumber);
            return;
        }


        String tenantId = userService.queryTenantIdById(adminId);

        //刷新套餐下设备
        this.refreshUserDevice(adminId,tenantId);
    }

    /**
     * 清除用户套餐下不符合条件的设备
     * @param userId
     * @param tenantId
     * @param tier
     */
    public void deleteDeviceTierServiceType(Integer userId,String tenantId,Tier tier){
        List<UserTierDeviceDO> userTierDeviceDOList = userTierDeviceDAO.getByUserTierId(userId,tier.getTierId());
        if(CollectionUtils.isEmpty(userTierDeviceDOList)){
            return;
        }

        for(UserTierDeviceDO userTierDeviceDO : userTierDeviceDOList){
            Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(userTierDeviceDO.getSerialNumber());
            if(device4GSimDO == null){
                //wifi 设备
                if(tier.getTierServiceType().equals(TierServiceTypeEnums.TIER_4G.getCode())){
                    this.deleteUserTierDevice(userId,userTierDeviceDO.getSerialNumber(), tier.getTierId(),tenantId);
                }
            }else {
                //4G设备
                if(tier.getTierServiceType().equals(TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode())
                        || !org.springframework.util.StringUtils.hasLength(device4GSimDO.getIccid())
                        || Optional.ofNullable(device4GSimDO.getSimThirdParty()).orElse(0) == 1){
                    this.deleteUserTierDevice(userId,userTierDeviceDO.getSerialNumber(), tier.getTierId(),tenantId);
                }
            }
        }
    }


    /**
     * 有生效的free license vip设备
     * @param userId
     * @param tenantId
     * @return
     */
    public boolean hasActiveFreeLicenceDeviceVip(Integer userId,String tenantId){
        return userTierDeviceDAO.getByUserIdReadOnly(tenantId,userId)
                .stream()
                .anyMatch(userTierDeviceDO -> userTierDeviceDO.getTierId().equals(FREE_LICENSE_7_DAY));
    }


    /**
     * 非vip变为vip,变更参数
     * @param serialNumber
     * @param tierId
     */
    public void modifyDeviceSettingAfterTierChange(String serialNumber,Integer tierId){
        if(TierIdUtil.getTierLevelFromTierId(tierId) <= 0 || FREE_LICENSE_TIER_NOT_VIP.contains(tierId)){
            log.debug("redeemFreeLicense not vip sn {} tierId {}",serialNumber,tierId);
            //非vip
            return;
        }
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if(deviceSettingsDO == null){
            log.debug("redeemFreeLicense deviceSettingsDO null sn {}",serialNumber);
            return;
        }

        //
        DeviceAttributeService.DeviceAttributeSource src = deviceAttributeService.getAttributeSource(serialNumber);
        if(!src.getSupport().getPirRecordTimeOptions().contains(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_180.getEnumName())
            && !src.getSupport().getPirRecordTimeOptions().contains(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_AUTO.getEnumName())){
            log.debug("redeemFreeLicense deviceSettingsDO support RecordTimeOptions {} sn {}",src.getSupport().getPirRecordTimeOptions(),serialNumber);
            return;
        }
        log.debug("redeemFreeLicense deviceSetting PirRecordTime {}",deviceSettingsDO.getPirRecordTime());
        if(src.getSupport().getPirRecordTimeOptions().contains(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_180.getEnumName())){
            deviceSettingsDO.setPirRecordTime(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_180.getEnumName());
            deviceSettingsDO.setRecLen(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_180.getValue());
        }else {
            deviceSettingsDO.setPirRecordTime(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_AUTO.getEnumName());
            deviceSettingsDO.setRecLen(AppFormOptionsDO.VideoSecondOptionValue.SECONDS_AUTO.getValue());
        }

        deviceSettingService.updateDeviceSetting(deviceSettingsDO);
    }

    /**
     * 没有套餐、设备记录的用户
     * @param userIds
     * @return
     */
    public Set<Integer> queryEmptyTierDeviceList(List<Integer> userIds){
        Set<Integer> hasRecordUserSet = userTierDeviceDAO.queryUserTierDeviceCounts(userIds)
                .stream()
                .map(UserTierDeviceDO::getUserId).collect(Collectors.toSet());


        return userIds.stream()
                .filter(userId -> !hasRecordUserSet.contains(userId))
                .collect(Collectors.toSet());
    }

    /**
     * 设备变成vip 设备时，将pushIgnored 关闭
     * @param tierId
     * @param serialNumber
     */
    private void devicePushIgnoredAfterVip(Integer tierId, String serialNumber) {
        // 检查套餐是否为付费套餐(tier_id=1001 或 tier.level>0)
        boolean isPaidTier = tierId != null && (tierId.equals(FREE_LICENSE_7_DAY) || tierService.queryTierLevelById(tierId) > 0);
        if (!isPaidTier) {
            return;
        }

        // 获取设备信息
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO == null || deviceDO.getPushIgnored() == null || !deviceDO.getPushIgnored()) {
            return;
        }

        // 更新设备push_ignored为0
        DeviceDO updateDeviceDO = DeviceDO.builder()
                .serialNumber(serialNumber)
                .pushIgnored(false)
                .build();
        deviceService.updateDeviceInfo(updateDeviceDO);
        log.debug("devicePushIgnoredAfterVip: updated push_ignored to 0 for device {} with tier {}", serialNumber, tierId);
    }
}
