package com.addx.iotcamera.service.device_msg;

import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.device_msg.*;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.exception.ResultException;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.bean.KissApiResult;
import com.addx.iotcamera.mqtt.MqttPayload;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.deviceplatform.alexa.safemo.AlexaSafemoOnMessageService;
import com.addx.iotcamera.util.AlexaSafemoUtil;
import com.addx.iotcamera.util.OpenApiUtil;
import com.addx.tracking.interceptor.impl.WebSocketInterceptor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.opentelemetry.api.OpenTelemetry;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.DeviceCodecUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.function.Consumer;
import java.util.function.Function;

import static com.addx.iotcamera.service.DeviceAuthService.USER_ID_SYSTEM;

@Component
@Slf4j
public class KissWsService extends BaseWsService {

    @Autowired
    private DeviceMsgService deviceMsgService;
    // 设备sn -> 设备在所在kiss节点
    @Getter
    @Autowired
    private KissDeviceNodeManager sn2KissDeviceNode;
    @Autowired
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Autowired
    @Lazy
    private UserRoleService userRoleService;
    @Autowired
    @Lazy
    private UserService userService;
    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;
    @Autowired
    @Lazy
    private DeviceConfigService deviceConfigService;
    @Autowired
    private OpenTelemetry openTelemetry;
    @Autowired
    WebSocketInterceptor webSocketInterceptor;

    @Getter
    private KissWsClientManager ip2KissWsClient = new KissWsClientManager().setKissWsClientFactory(this::createKissWsClient);

    @Setter
    public Function<URI, KissWsClient> kissWsClientFactory = KissWsClient::new; // 方便单元测试
    @Autowired
    @Lazy
    private KissFinder kissFinder;
    @Autowired
    @Lazy
    private AlexaSafemoOnMessageService alexaSafemoOnMessageService;

    private ConcurrentLinkedQueue<Consumer<KissWsClient>> kissWsClientOpenListeners = new ConcurrentLinkedQueue<>();

    public void addKissWsClientOpenListeners(Consumer<KissWsClient> listener) {
        if (listener == null) return;
        this.kissWsClientOpenListeners.add(listener);
    }

    // 创建连接kiss的websocket客户端
    KissWsClient createKissWsClient(URI uri) {
        final KissWsClient wsClient = kissWsClientFactory.apply(uri);
        wsClient.setOpenListener(() -> {
            for (Consumer<KissWsClient> listener : kissWsClientOpenListeners) {
                try {
                    listener.accept(wsClient);
                } catch (Exception e) {
                    log.error("kissWsClient open listener accept error! wsClient={}", wsClient, e);
                }
            }
        });
        wsClient.setRawMsgListener(rawMsg -> {
            if (AlexaSafemoUtil.isAlexaMsg(rawMsg)) {
                alexaSafemoOnMessageService.onMessage(rawMsg);
                return true; // 回调返回true，则其它listener不生效
            }
            return false;
        });
        wsClient.setKissDeviceNodesListener(nodes -> {
            nodes.forEach(node -> node.setKissIp(uri.getHost()));
            sn2KissDeviceNode.putAll(nodes);
        });
        wsClient.setCmdAckListener(ack -> {
            deviceMsgService.onCmdAck(ack);
        });
        wsClient.setRetainedMsgAckListener(ack -> {
            deviceMsgService.onRetainedMsgAck(ack);
        });
        wsClient.setOpenTelemetry(openTelemetry);
        wsClient.setReqListener(req -> {
            final IotWsReqName name = EnumUtils.getEnum(IotWsReqName.class, req.getName());
            if (IotWsReqName.queryClientCountryNo == name) {
                final JSONObject value = (JSONObject) JSON.toJSON(req.getValue());
                final Map<String, String> clientId2CountryNo = getCountryNoByClientIds(value.getObject("clientIds", List.class));
                if (!clientId2CountryNo.isEmpty()) {
                    wsClient.send(JSON.toJSONString(new IotWsResp<>(req).setValue(new JSONObject().fluentPut("clientId2CountryNo", clientId2CountryNo))));
                }

            } else if (IotWsReqName.deviceReportEvent == name) {
                final DeviceReportEventDO reportEventDO = JSON.parseObject(JSON.toJSONString(req.getValue()), DeviceReportEventDO.class);
                deviceInfoService.handleEventReport(reportEventDO); // ws上报
            } else if (IotWsReqName.switchDeviceCodecLive == name) {
                // {"method":"IOT_WS_REQ","name":"switchDeviceCodecLive","id":"799e8cb89086476daeea1c6f4bc6a6ce","time":1736143542467,"value":{"serialNumber":"165e7e3287ccaa3e3e2472c15c506a86"}}
                final JSONObject value = (JSONObject) JSON.toJSON(req.getValue());
                final String serialNumber = value.getString("serialNumber");
                Result result = deviceConfigService.updateDefaultCodec(new DeviceConfigRequest().setUserId(USER_ID_SYSTEM)
                        .setSerialNumber(serialNumber).setDefaultCodec(DeviceCodecUtil.H264));
                wsClient.send(JSON.toJSONString(new IotWsResp<>(req).setValue(new JSONObject()
                        .fluentPutAll(value).fluentPut("modifyResult", result))));
            }
        });
        wsClient.setRespListener(resp -> {
            final IotWsReqName name = IotWsReqName.valueOf(resp.getName());
        });
        wsClient.setWebSocketInterceptor(webSocketInterceptor);
        wsClient.setKissFinder(kissFinder);
        return wsClient;
    }

    // 把kiss中的设备状态转换成状态机状态
    public DeviceStateDO getDeviceState(String sn) {
        final KissDeviceNode kissDeviceNode = sn2KissDeviceNode.get(sn);
        if (kissDeviceNode == null) return null;
        final DeviceStateDO state = DeviceStateDO.builder().sn(sn)
                .stateId(kissDeviceNode.getDeviceStatus().toDeviceStateMachineEnums().getCode())
                .updatedAtTime(kissDeviceNode.getLastUpdateTime())
                .updatedAt(new Date(kissDeviceNode.getLastUpdateTime()))
                .onlineStatus(kissDeviceNode.getDeviceStatus().toDeviceOnlineStatusEnums())
                .build();
        log.info("getDeviceState from kiss end! sn={},state={}", sn, JSON.toJSONString(state));
        return state;
    }

    public KissApiResult wakeup(String sn, String traceId, String wakeup, Boolean isWsKeepAlive) {
        final JSONObject msg = new JSONObject().fluentPut("method", "WAKEUP").fluentPut("recipientClientId", sn)
                .fluentPut("traceId", traceId).fluentPut("wakeup", wakeup).fluentPut("isWsKeepAlive", isWsKeepAlive);
        return sendDeviceMsg(sn, msg.toJSONString());
    }

    public KissApiResult wakeupByKissIp(String kissIp, String sn, String traceId, String wakeup, Boolean isWsKeepAlive) {
        final JSONObject msg = new JSONObject().fluentPut("method", "WAKEUP").fluentPut("recipientClientId", sn)
                .fluentPut("traceId", traceId).fluentPut("wakeup", wakeup).fluentPut("isWsKeepAlive", isWsKeepAlive);
        return sendDeviceMsgByKissIp(kissIp, sn, msg.toJSONString());
    }

//    public KissDeviceNode queryKissDeviceNode(String sn) {
//        return sn2KissDeviceNode.get(sn);
//    }


    KissApiResult sendDeviceMsgByKissIp(String kissIp, String sn, String msg) {
        try {
            final KissWsClient wsClient = ip2KissWsClient.get(kissIp);
            if (wsClient == null || !wsClient.isOpen()) {
                throw new ResultException().setCode(-200).setMsg("无法访问设备所在的kiss节点!");
            }
            wsClient.send(msg);
            log.info("sendDeviceMsgByKissIp end! sn={},kissIp={},msg={}", sn, kissIp, msg);
            return new KissApiResult().setCode(0);
        } catch (ResultException e) {
            log.info("sendDeviceMsgByKissIp fail! sn={},kissIp={},msg={},failCode={},failMsg={}", sn, kissIp, msg, e.getCode(), e.getMsg());
            return new KissApiResult().setCode(e.getCode()).setMsg(e.getMsg());
        } catch (Throwable e) {
            log.error("sendDeviceMsgByKissIp error! sn={},kissIp={},msg={}", sn, kissIp, msg, e);
            return new KissApiResult().setCode(-500).setMsg("访问设备所在的kiss节点发生异常!");
        }
    }

    KissApiResult sendDeviceMsgToAny(Collection<KissWsClient> kissWsClients, String msg) {
        for (KissWsClient client : kissWsClients) {
            try {
                if (!client.isOpen()) continue;
                client.send(msg);
                log.info("sendDeviceMsgToAny end! kissUri={},msg={}", client.getURI(), msg);
                return new KissApiResult().setCode(0);
            } catch (Throwable e) {
                log.error("sendDeviceMsgToAny error! kissUri={},msg={}", client.getURI(), msg, e);
            }
        }
        log.error("sendDeviceMsgToAny fail! msg={}", msg);
        return new KissApiResult().setCode(-200).setMsg("发送RETAINED_MSG到任意一个kiss节点失败!");
    }

    KissApiResult sendDeviceMsgToAll(Collection<KissWsClient> kissWsClients, String msg) {
        int sendNum = 0;
        for (KissWsClient client : kissWsClients) {
            try {
                if (!client.isOpen()) continue;
                client.send(msg);
                sendNum++;
            } catch (Throwable e) {
                log.error("sendDeviceMsgToAll error! kissUri={},msg={}", client.getURI(), msg, e);
            }
        }
        log.info("sendDeviceMsgToAll end! kissNodeNum={},sendNum={},msg={}", kissWsClients.size(), sendNum, msg);
        return new KissApiResult().setCode(0).setMsg("发送消息到所有kiss节点完成!");
    }

    public KissApiResult sendDeviceMsgToAll(String msg) {
        return sendDeviceMsgToAll(ip2KissWsClient.values(), msg);
    }

    public static DeviceRetainedMsg buildRetainedMsg(String name, String sn, JSONObject value) {
        // 如果原始消息内容的最外层有id和time，就直接复用
        final String id = Optional.ofNullable(value).map(it -> it.getString("id")).orElseGet(OpenApiUtil::shortUUID);
        final Integer time = Optional.ofNullable(value).map(it -> it.getInteger("time")).orElseGet(PhosUtils::getUTCStamp);
        return new DeviceRetainedMsg().setRecipientClientId(sn).setName(name).setTime(time).setId(id).setValue(value);
    }

    public KissApiResult sendRetainedMsgToDevice(String name, String sn, MqttPayload payload) {
        final DeviceRetainedMsg retainedMsg = buildRetainedMsg(name, sn, JSON.parseObject(payload.getTextData()))
                .setBinaryData(MqttPayload.encodeBase64(payload.getBinaryData()));
        final String msg = retainedMsg.toSendString();
        final KissApiResult result = sendDeviceMsg(sn, msg);
        if (result.success()) return result;
        return sendDeviceMsgToAny(ip2KissWsClient.values(), msg); // 如果往当前节点发送失败了，往任意kiss节点发送
    }

    public KissApiResult sendCmdToDevice(String sn, MqttPayload payload) {
        // {"id":"cmd:34e6c6e814ee40ef9b495e97a95a9942","name":"deviceDormancy","serialNumber":"2065a8582cebbf7efee5f9c5931169fa","time":1687849342,"value":{"dormancy":0}}
        final JSONObject root = JSON.parseObject(payload.getTextData());
        final DeviceCmd cmd = new DeviceCmd().setRecipientClientId(sn).setName(root.getString("name"))
                .setTime(root.getInteger("time")).setId(root.getString("id")).setValue(root.get("value"))
                .setBinaryData(MqttPayload.encodeBase64(payload.getBinaryData()));
        return sendDeviceMsg(sn, JSON.toJSONString(cmd));
    }

    public boolean updateKeepAliveParams(String sn, KeepAliveParams keepAliveParams) {
        final JSONObject msg = new JSONObject().fluentPut("method", IotMsgMethod.KEEP_ALIVE_PARAMS.name())
                .fluentPut("recipientClientId", sn).fluentPut("timestamp", System.currentTimeMillis())
                .fluentPutAll((JSONObject) JSON.toJSON(keepAliveParams));
        return sendDeviceMsg(sn, JSON.toJSONString(msg)).success();
    }

    public boolean closeOldGroup(String sn) {
        final JSONObject msg = new JSONObject().fluentPut("method", "CLOSE_OLD_GROUP")
                .fluentPut("recipientClientId", sn).fluentPut("requestId", MDC.get(MDCKeys.REQUEST_ID));
        return sendDeviceMsgToAll(ip2KissWsClient.values(), msg.toJSONString()).success();
    }

    public boolean closeGroup(String sn) {
        final JSONObject msg = new JSONObject().fluentPut("method", "CLOSE_GROUP")
                .fluentPut("recipientClientId", sn).fluentPut("requestId", MDC.get(MDCKeys.REQUEST_ID));
        return sendDeviceMsgToAll(ip2KissWsClient.values(), msg.toJSONString()).success();
    }

    public Map<String, String> getCountryNoByClientIds(Collection<String> clientIds) {
        final Map<String, String> clientId2CountryNo = new LinkedHashMap<>();
        for (String clientId : clientIds) {
            try {
                String countryNo = userRoleService.getCountryNoBySerialNumber(clientId);
                if (countryNo == null) {
                    Integer userId = null;
                    try {
                        userId = Integer.parseInt(clientId, 10);
                    } catch (Throwable e) {
                    }
                    countryNo = Optional.ofNullable(userId).map(userService::queryUserById).map(it -> it.getCountryNo()).orElse(null);
                }
                if (countryNo != null) {
                    clientId2CountryNo.put(clientId, countryNo);
                }
            } catch (Throwable e) {
                log.error("getCountryNoByClientIds error! clientId={}", clientId, e);
            }
        }
        log.info("getCountryNoByClientIds end! clientIds={},clientId2CountryNo={}", JSON.toJSONString(clientIds), JSON.toJSONString(clientId2CountryNo));
        return clientId2CountryNo;
    }

    public Result<Map<String, String>> broadcast(JSONObject root) {
        Map<String, String> kissIp2Result = new HashMap<>();
        // {"id":"cmd:34e6c6e814ee40ef9b495e97a95a9942","name":"deviceDormancy","serialNumber":"2065a8582cebbf7efee5f9c5931169fa","time":1687849342,"value":{"dormancy":0}}
        final JSONObject msgObj = new JSONObject().fluentPut("method", "BROADCAST")
                .fluentPut("id", OpenApiUtil.shortUUID()).fluentPut("time", PhosUtils.getUTCStamp())
                .fluentPut("value", root);
        final String msg = JSON.toJSONString(msgObj);
        for (Map.Entry<String, KissWsClient> entry : getIp2KissWsClient().entrySet()) {
            String kissIp = entry.getKey();
            KissWsClient wsClient = entry.getValue();
            if (!wsClient.isOpen()) {
                log.info("broadcast fail! kissIp={},wsClient={},msg=notOpen", kissIp, wsClient);
                kissIp2Result.put(kissIp, "notOpen");
                continue;
            }
            try {
                wsClient.send(msg);
                log.info("broadcast end! kissIp={},wsClient={},msg={}", kissIp, wsClient, msg);
                kissIp2Result.put(kissIp, "success");
            } catch (Exception e) {
                log.error("broadcast error! kissIp={},wsClient={},msg={}", kissIp, wsClient, msg, e);
                kissIp2Result.put(kissIp, "error");
            }
        }
        return new Result<>(kissIp2Result);
    }

}
