package com.addx.iotcamera.service.factory;

import com.addx.iotcamera.dao.device.IDeviceManualDAO;
import com.addx.iotcamera.dao.factory.DeviceModelDao;
import com.addx.iotcamera.service.RedisService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.addx.iotcamera.enums.device.DeviceModelCategoryEnums.CAMERA;

@Service
@Slf4j
public class DeviceModelService {
    @Setter
    @Resource
    private DeviceModelDao deviceModelDao;

    @Setter
    @Resource
    private IDeviceManualDAO iDeviceManualDAO;

    @Autowired
    @Lazy
    private RedisService redisService;

    /**
     * 请求型号安装引导页
     * @param modelNo
     * @return
     */
    @Cacheable(value = "deviceModelInstallBoot", key = "#modelNo", unless = "#result==null")
    public String queryDeviceModelInstallBoot(String modelNo){
        log.info("queryDeviceModelInstallBoot {}",modelNo);
        return deviceModelDao.selectInstallBoot(modelNo);
    }

    /**
     * 查询型号所属类别-by modelNo
     * @param modelNo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class},propagation = Propagation.NOT_SUPPORTED)
    public Integer queryDeviceModelCategory(String modelNo) {
        final String redisKey = "modelCategory::" + modelNo;
        String value = redisService.get(redisKey);
        Integer categoryId;
        if (value == null) {
            categoryId = deviceModelDao.queryModelCategory(modelNo);
            if (categoryId != null) {
                log.info("query DeviceModelCategoryEnums modelNo={},categoryId={}", modelNo, categoryId);
            } else {
                com.addx.iotcamera.util.LogUtil.error(log, "query DeviceModelCategoryEnums modelNo={},categoryId={}", modelNo, categoryId);
                categoryId = CAMERA.getCode();
            }
            redisService.set(redisKey, categoryId + "");
        } else {
            try {
                categoryId = Integer.parseInt(value);
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, "query DeviceModelCategoryEnums parseIntError modelNo={}", modelNo, e);
                categoryId = CAMERA.getCode();
            }
        }
        return categoryId;
    }

    /**
     * 查询型号所属类别-by serialNumber
     * @param serialNumber
     * @return
     */
    @Cacheable(value = "modelCategory", key = "#serialNumber", unless = "#result==null")
    public Integer queryDeviceModelCategoryBySerialNumber(String serialNumber){
        log.info("query DeviceModelCategoryEnums {}",serialNumber);
        String modelNo = iDeviceManualDAO.getModelNoBySerialNumber(serialNumber);
        return queryDeviceModelCategory(modelNo);
    }
}
