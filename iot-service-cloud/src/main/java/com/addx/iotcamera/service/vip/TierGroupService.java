package com.addx.iotcamera.service.vip;

import static com.addx.iotcamera.constants.PayConstants.SUB_TIER_PRODUCT_SET;

import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.TierActiveConditionDO;
import com.addx.iotcamera.bean.db.TierGroupDO;
import com.addx.iotcamera.bean.db.pay.UserVipDO;
import com.addx.iotcamera.bean.domain.Tier;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.dao.IUserVipDAO;
import com.addx.iotcamera.dao.TierGroupDao;
import com.addx.iotcamera.enums.ProductTypeEnums;
import com.addx.iotcamera.util.DateUtils;
import com.addx.iotcamera.util.TierIdUtil;
import com.google.common.collect.Maps;
import java.time.Instant;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TierGroupService {

    @Autowired
    private TierActiveConditionService tierActiveConditionService;

    @Autowired
    private TierService tierService;

    @Autowired
    private TierGroupDao tierGroupDao;

    @Autowired
    private IUserVipDAO iUserVipDAO;

    public TierGroupDO getById(String tenantId, Integer id) {
        TierGroupDO tierGroupDO = id == null ? null : tierGroupDao.getById(tenantId, id);
        return tierGroupDO == null ?
                TierGroupDO.builder()
                        .id(0)
                        .name("")
                        .tenantId(tenantId)
                        .weight(0)
                        .tierActiveOrder(3)
                        .build()
                : tierGroupDO;
    }

    public List<UserVipDO> addUserVipDO(List<UserVipDO> currentUserVipDOList, UserVipDO addUserVipDO, ProductDO productDO,Integer freeTrialDay) {
        int currentTime = (int) (Instant.now().getEpochSecond());
        addUserVipDO.setEffectiveTime(currentTime);
        addUserVipDO.setEndTime(DateUtils.computeTime(addUserVipDO.getFreeTrial().equals(1) ? null : productDO, addUserVipDO.getFreeTrial().equals(1) ? (freeTrialDay+1) : 1, currentTime));

        if (CollectionUtils.isEmpty(currentUserVipDOList)) {
            return Collections.singletonList(addUserVipDO);
        }

        // 升级套餐立即生效
        List<UserVipDO> newUserVipDOList = new LinkedList<>();
        if (productDO.getType().equals(ProductTypeEnums.PURCHASE.getCode()) || SUB_TIER_PRODUCT_SET.contains(productDO.getType())) {
            log.info("套餐购买:userId{},productId:{}", addUserVipDO.getUserId(), productDO.getId());
            //除了新套餐比当前套餐都高的情况，其余处于插入，不需要计算保护期时间
            this.newTierParams(currentUserVipDOList, currentTime, addUserVipDO, productDO,freeTrialDay);
            newUserVipDOList.add(addUserVipDO);
        } else {
            log.info("套餐升级:userId{},productId:{}", addUserVipDO.getUserId(), productDO.getId());

            newUserVipDOList.add(0, addUserVipDO);
        }

        return newUserVipDOList;
    }

    /**
     * 根据套餐组规则和套餐生效条件,对套餐生效顺序进行排序
     * @param currentUserVipDOList
     * @return
     */
    public List<UserVipDO> sortUserVipDO(List<UserVipDO> currentUserVipDOList) {
        if (CollectionUtils.isEmpty(currentUserVipDOList)) {
            return currentUserVipDOList;
        }

        Integer tierGroupId = tierService.queryTierById(currentUserVipDOList.get(0).getTierId()).getTierGroupId();
        TierGroupDO tierGroupDO = getById(null, tierGroupId);

        Map<Integer, TierActiveConditionDO> tierActiveConditionDOMap = new HashMap<>();
        if (StringUtils.isNotEmpty(tierGroupDO.getTenantId())) {
            currentUserVipDOList.forEach(userVipDO -> tierActiveConditionDOMap.computeIfAbsent(userVipDO.getTierId(), (tierIdKey) -> tierActiveConditionService.getById(tierGroupDO.getTenantId(), tierIdKey, null)));
        }

        // 按照生效顺序和套餐结束时间排序
        Integer tierActiveOrder = tierGroupDO.getTierActiveOrder();
        if (ObjectUtils.equals(tierActiveOrder, 3)) {
            // 先比较生效顺序然后比较时间
            currentUserVipDOList.sort((userVipDO1, userVipDO2) -> {
                Integer activeOrder1 = ObjectUtils.defaultIfNull(tierActiveConditionDOMap.containsKey(userVipDO1.getTierId()) ? tierActiveConditionDOMap.get(userVipDO1.getTierId()).getActiveOrder() : null, -1 * TierIdUtil.getTierLevelFromTierId(userVipDO1.getTierId()));
                Integer activeOrder2 = ObjectUtils.defaultIfNull(tierActiveConditionDOMap.containsKey(userVipDO2.getTierId()) ? tierActiveConditionDOMap.get(userVipDO2.getTierId()).getActiveOrder() : null, -1 * TierIdUtil.getTierLevelFromTierId(userVipDO2.getTierId()));
                return ObjectUtils.compare(activeOrder1, activeOrder2) != 0 ? ObjectUtils.compare(activeOrder1, activeOrder2) : ObjectUtils.compare(userVipDO1.getEndTime(), userVipDO2.getEndTime());
            });
        } else if (ObjectUtils.equals(tierActiveOrder, 2)) { // 按照套餐等级排序
            // 比较生效顺序
            currentUserVipDOList.sort((userVipDO1, userVipDO2) -> {
                Integer activeOrder1 = ObjectUtils.defaultIfNull(tierActiveConditionDOMap.containsKey(userVipDO1.getTierId()) ? tierActiveConditionDOMap.get(userVipDO1.getTierId()).getActiveOrder() : null, -1 * TierIdUtil.getTierLevelFromTierId(userVipDO1.getTierId()));
                Integer activeOrder2 = ObjectUtils.defaultIfNull(tierActiveConditionDOMap.containsKey(userVipDO2.getTierId()) ? tierActiveConditionDOMap.get(userVipDO2.getTierId()).getActiveOrder() : null, -1 * TierIdUtil.getTierLevelFromTierId(userVipDO2.getTierId()));
                return ObjectUtils.compare(activeOrder1, activeOrder2);
            });
        } else if (ObjectUtils.equals(tierActiveOrder, 1)) { // 按照套餐结束时间排序
            // 比较时间
            currentUserVipDOList.sort((userVipDO1, userVipDO2) -> ObjectUtils.compare(userVipDO1.getEndTime(), userVipDO2.getEndTime()));
        }
        return currentUserVipDOList;
    }


    /**
     * 计算结束日期的开始时间和赠送天数
     *
     * @param time
     * @param addUserVipDO
     * @param productDO
     * @return
     */
    private void newTierParams(List<UserVipDO> userVipDOList, int time, UserVipDO addUserVipDO, ProductDO productDO,Integer freeTrialDay) {
        boolean complete = false;
        int tierId = addUserVipDO.getTierId();
        //赠送天数
        int protection = 0;
        Integer effectiveTime = time;

        int lastEndTime = time;
        for (int i = 0; i < userVipDOList.size(); i++) {
            UserVipDO vip = userVipDOList.get(i);

            //当前套餐优先级大于等于新购买套餐
            boolean existTierPriority = vip.getTierId().equals(tierId) || this.verifyTierGroupPriority(vip.getTierId(),tierId);
            if (existTierPriority) {
                //当前套餐比要购买套餐等级高或者相等，当前套餐不处理,新套餐插入位置后移
                lastEndTime = vip.getEndTime();
                continue;
            }

            // 免费套餐不延期
            int existTierLevel = vip.getTierId() % 10;
            if (existTierLevel == 0) {
                continue;
            }

            //第一个小于新套餐就是该插入的地方
            //新套餐插入是否已结束，如结束，余下只需将剩余低等级套餐后延
            if (!complete) {
                if (i == 0) {
                    //新购买的套餐等级最高，直接生效，第二天才算做新套餐第一天
                    protection = 1;
                }
                complete = true;
                effectiveTime = lastEndTime;

                // 需要后延的套餐，肯定是正在套餐期间内，所以需要重新计算开始时间
                vip.setEffectiveTime(DateUtils.computeTime(addUserVipDO.getFreeTrial().equals(1) ? null : productDO, addUserVipDO.getFreeTrial().equals(1) ? (freeTrialDay+protection) : protection, effectiveTime));
            } else {
                vip.setEffectiveTime(lastEndTime);
            }

            //更新套餐的生效、结束时间
            vip.setEndTime(DateUtils.computeTime(addUserVipDO.getFreeTrial().equals(1) ? null :productDO, addUserVipDO.getFreeTrial().equals(1) ? (freeTrialDay+protection) : protection, vip.getEndTime()));

            iUserVipDAO.updateUserVipTime(vip);

            lastEndTime = vip.getEndTime();
            log.info("计算套餐生效时间 index {} vip {} lastEndTime {}",i,vip,lastEndTime);
        }

        if (!complete) {
            //已有套餐均大于或等于当前套餐
            effectiveTime = lastEndTime;
        }

        //新购买套餐开始时间
        addUserVipDO.setEffectiveTime(effectiveTime);

        //新购买套餐结束时间
        Integer endTime = DateUtils.computeTime(addUserVipDO.getFreeTrial().equals(1) ? null :productDO,addUserVipDO.getFreeTrial().equals(1) ? (freeTrialDay+protection) : protection, effectiveTime);
        addUserVipDO.setEndTime(endTime);
    }

    /**
     * 判断套餐组优先级
     * @param tierId
     * @param currentTierId
     * @return
     */
    public boolean verifyTierGroupPriority(Integer tierId,Integer currentTierId){
        Tier buyTier = tierService.queryTierById(tierId);
        Tier currentTier = tierService.queryTierById(currentTierId);
        if(buyTier == null || currentTier == null){
            throw new BaseException("套餐信息未设置");
        }

        if(!buyTier.getTierType().equals(currentTier.getTierType())){
            //套餐组高优先级
            return buyTier.getTierType() > currentTier.getTierType();
        }

        TierGroupDO buyTierGroup = this.getById(null,buyTier.getTierGroupId());
        TierGroupDO currentTierGroup = this.getById(null,currentTier.getTierGroupId());

        if(!buyTierGroup.getWeight().equals(currentTierGroup.getWeight())){
            //套餐组高优先级
            return buyTierGroup.getWeight() > currentTierGroup.getWeight();
        }

        return buyTier.getLevel() > currentTier.getLevel();
    }
}
