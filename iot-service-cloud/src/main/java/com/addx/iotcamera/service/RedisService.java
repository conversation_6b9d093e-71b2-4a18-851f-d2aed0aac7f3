package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.domain.DeviceOperationDO;
import com.addx.iotcamera.bean.domain.ShareCacheDO;
import com.addx.iotcamera.constants.VideoConstants;
import com.addx.iotcamera.service.lettuce.service.LettuceDataTransClient;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.DTIMKeyMigrationUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.gson.Gson;
import io.reactivex.Observable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.addx.iot.common.constant.AppConstants.REDIS_PREFIX_CACHE_NAME;

@Slf4j
@Component
public class RedisService {

    private final static long increNumber = 1L;
    private final static long DEVICE_OPERATION_KEY_EXPIRE_TIME = 60;

    @Resource(name = "businessRedisTemplateClusterClient")
    private StringRedisTemplate businessRedisTemplateClusterClient;

    @Autowired
    LettuceDataTransClient lettuceDataTransClient;

    @Autowired
    private RedisLockRegistry redisLockRegistry;

    
    public DeviceOperationDO getDeviceOperationDo(String key) {
        if (!businessRedisTemplateClusterClient.hasKey(key)) {
            return null;
        }
        Gson gson = new Gson();
        return gson.fromJson(businessRedisTemplateClusterClient.opsForValue().get(key), DeviceOperationDO.class);
    }

    public void setDeviceOperationDOWithEmpty(String key) {
        businessRedisTemplateClusterClient.opsForValue().set(key, "", DEVICE_OPERATION_KEY_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    
    public boolean hasDeviceOperationDo(String key) {
        return businessRedisTemplateClusterClient.hasKey(key);
    }

    public void dropDeviceOperationDo(String key) {
        businessRedisTemplateClusterClient.delete(key);
    }

    public void setDeviceOperationDo(String key, DeviceOperationDO value) {
        Gson gson = new Gson();

        businessRedisTemplateClusterClient.opsForValue().set(key, gson.toJson(value), DEVICE_OPERATION_KEY_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    
    public ShareCacheDO getShareCacheDO(String key) {
        if (!businessRedisTemplateClusterClient.hasKey(key)) {
            return null;
        }
        Gson gson = new Gson();
        return gson.fromJson(businessRedisTemplateClusterClient.opsForValue().get(key), ShareCacheDO.class);
    }

    public void setShareCacheDO(String key, ShareCacheDO value, int expire) {
        Gson gson = new Gson();

        businessRedisTemplateClusterClient.opsForValue().set(key, gson.toJson(value), expire, TimeUnit.SECONDS);
    }

    public boolean hasKey(String key) {
        return businessRedisTemplateClusterClient.hasKey(key);
    }

    /**
     * redis 记录
     *
     * @param key
     * @param value
     * @param timeOut
     */
    public void set(String key, String value, Integer timeOut) {
        businessRedisTemplateClusterClient.opsForValue().set(key, value, timeOut, TimeUnit.SECONDS);
    }

    /**
     * redis 记录-无过期时间
     *
     * @param key
     * @param value
     */
    public void set(String key, String value) {
        businessRedisTemplateClusterClient.opsForValue().set(key, value);
    }

    /**
     * 查询缓存
     *
     * @param key
     * @return
     */
    
    public String get(String key) {
        return businessRedisTemplateClusterClient.opsForValue().get(key);
    }


    
    public Object get(String key, String field) {
        return businessRedisTemplateClusterClient.opsForHash().get(key, field);
    }

    
    public boolean containsKey(String key) {
        return businessRedisTemplateClusterClient.hasKey(key);
    }

    /**
     * 是否存在指定key(从库中读取)
     *
     * @param key
     * @return
     */
    
    public String getFromSlave(String key) {
        return businessRedisTemplateClusterClient.opsForValue().get(key);
    }

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    public boolean delete(String key) {
        return BooleanUtils.isTrue(businessRedisTemplateClusterClient.delete(key));
    }

    // 手动删除通过@Cacheable注解创建的环境。带前缀或无前缀的都删除，避免遗漏。使用redisCluster不用批量delete方法
    public Long deleteCacheableAnnotationCache(String key) {
        return deleteCacheableAnnotationCache(Arrays.asList(key));
    }

    // 手动删除通过@Cacheable注解创建的环境
    public Long deleteCacheableAnnotationCache(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) return 0L;
        List<String> deleteKeys = keys.stream().map(key -> REDIS_PREFIX_CACHE_NAME + key).collect(Collectors.toList());
        long deleteNum = deleteKeys.stream().filter(this::delete).count();
        log.debug("deleteCacheableAnnotationCache end! deleteNum={},deleteKeys={}", deleteNum, JSON.toJSONString(deleteKeys));
        return deleteNum;
    }

    /**
     * 设置过期时间
     *
     * @param key
     * @return
     */
    
    public boolean setExpired(String key, Integer timeOut) {
        return businessRedisTemplateClusterClient.expire(key, timeOut, TimeUnit.SECONDS);
    }

    
    public long increNum(String key) {
        return businessRedisTemplateClusterClient.opsForValue().increment(key, 1);
    }

    
    public long incrBy(String key, long delta) {
        return businessRedisTemplateClusterClient.opsForValue().increment(key, delta);
    }

    
    public void setHashFieldValue(String key, String field, String value) {
        businessRedisTemplateClusterClient.opsForHash().put(key, field, value);
    }

    
    public void setHashFieldValueAll(String key, Map<String, Object> param) {
        businessRedisTemplateClusterClient.opsForHash().putAll(key, param);
    }

    
    public void setHashFieldValueMap(String key, Map<Object, Object> param, Long expire) {
        businessRedisTemplateClusterClient.opsForHash().putAll(key, param);
        businessRedisTemplateClusterClient.expire(key, expire, TimeUnit.SECONDS);
    }

    /**
     * hashget 使用的 slave节点
     *
     * @param key
     * @param field
     * @return
     */
    
    public Object getHashFieldValue(String key, String field) {
        return businessRedisTemplateClusterClient.opsForHash().get(key, field);
    }

    
    public Map<Object, Object> getHashEntries(String key) {
        return businessRedisTemplateClusterClient.opsForHash().entries(key);
    }

    /**
     * hash filed 自增
     *
     * @param key
     * @param field
     */
    
    public void increHashFieldValue(String key, String field) {
        businessRedisTemplateClusterClient.opsForHash().increment(key, field, increNumber);
    }

    /**
     * hash filed 自增一定数值
     *
     * @param key
     * @param field
     */
    
    public void increHashFieldValue(String key, String field, long number) {
        businessRedisTemplateClusterClient.opsForHash().increment(key, field, number);
    }

    /**
     * 删除hash filed
     *
     * @param key
     * @param field
     */
    
    public void deleteHashField(String key, String field) {
        businessRedisTemplateClusterClient.opsForHash().delete(key, field);
    }

    public boolean hasHashKey(String key, String field){
        return businessRedisTemplateClusterClient.opsForHash().hasKey(key, field);
    }

    public Long deleteHashFields(String key, Collection<String> fields) {
        if (CollectionUtils.isEmpty(fields)) return 0L;
        return businessRedisTemplateClusterClient.opsForHash().delete(key, fields.toArray());
    }

    
    public boolean hasFieldKey(String key, String field) {
        return businessRedisTemplateClusterClient.opsForHash().hasKey(key, field);
    }

    
    public Map<Object, Object> getDeviceBattery(String key) {
        return businessRedisTemplateClusterClient.opsForHash().entries(key);
    }

    
    public int hashIncrementInt(String key, String hkey, int num) {
        return businessRedisTemplateClusterClient.opsForHash().increment(key, hkey, num).intValue();
    }

    
    public BigDecimal hashIncrementBigDecimal(String key, String hKey, BigDecimal num) {
        Double doubleValue = businessRedisTemplateClusterClient.opsForHash().increment(key, hKey, num.doubleValue());
        return new BigDecimal(doubleValue);
    }

    
    public void hashIncrementDouble(String key, String hKey, Double num, long expireTime) {
        businessRedisTemplateClusterClient.opsForHash().increment(key, hKey, num != null ? num : 0);
        businessRedisTemplateClusterClient.expire(key, expireTime, TimeUnit.SECONDS);
    }

    
    public Integer hashGetInt(String key, String hKey, Integer defaultValue) {
        Object value = businessRedisTemplateClusterClient.opsForHash().get(key, hKey);
        return value != null ? Integer.valueOf(value + "") : defaultValue;
    }

    
    public String hashGetString(String key, String hKey) {
        Object value = businessRedisTemplateClusterClient.opsForHash().get(key, hKey);
        return value != null ? value.toString() : null;
    }

    
    public JSONObject hashMultiGet(String key, List<String> hKeys) {
        if (CollectionUtils.isEmpty(hKeys)) return null;
        List list = businessRedisTemplateClusterClient.opsForHash().multiGet(key, (Collection) hKeys);
        int notNullNum = 0;
        JSONObject data = new JSONObject();
        for (int i = 0; i < hKeys.size(); i++) {
            Object value = list.get(i);
            if (value != null) notNullNum++;
            data.put(hKeys.get(i), value);
        }
        return notNullNum > 0 ? data : null;
    }

    
    public JSONObject hashGetAll(String key) {
        Map<Object, Object> map = businessRedisTemplateClusterClient.opsForHash().entries(key);
        return map != null ? new JSONObject((Map) map) : null;
    }

    
    public JSONObject hashEntries(String key) {
        Map<Object, Object> map = businessRedisTemplateClusterClient.opsForHash().entries(key);
        return map != null ? new JSONObject((Map) map) : new JSONObject();
    }

    
    public void hashPut(String key, String hKey, Object value) {
        if (value == null) return;
        businessRedisTemplateClusterClient.opsForHash().put(key, hKey, value + "");
    }

    
    public void hashPutAll(String key, Map<String, Object> map) {
        if (CollectionUtils.isEmpty(map)) return;
        Map<String, String> strMap = map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey
                , en -> (en.getValue() != null) ? en.getValue() + "" : null));
        businessRedisTemplateClusterClient.opsForHash().putAll(key, strMap);
    }

    
    public int hashIncrementInt(String key, String hkey, int num, int timeoutSecond) {
        int num2 = businessRedisTemplateClusterClient.opsForHash().increment(key, hkey, num).intValue();
        if (num2 == num) businessRedisTemplateClusterClient.expire(key, timeoutSecond, TimeUnit.SECONDS);
        return num2;
    }

    
    public Long hashMembersCount(String key) {
        return businessRedisTemplateClusterClient.opsForHash().size(key);
    }

    
    public <T> int listRightPushAll(String key, List<T> list, int timeoutSecond) {
        if (CollectionUtils.isEmpty(list)) return 0;
        List<String> sliceJsonList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
        int num2 = businessRedisTemplateClusterClient.opsForList().rightPushAll(key, sliceJsonList).intValue();
        if (num2 == list.size()) businessRedisTemplateClusterClient.expire(key, timeoutSecond, TimeUnit.SECONDS);
        return num2;
    }


    
    public void zadd(String key, String value, double score) {
        businessRedisTemplateClusterClient.opsForZSet().add(key, value, score);
    }

    
    public boolean zremove(String key, String value) {
        return businessRedisTemplateClusterClient.opsForZSet().remove(key, value) > 0;
    }

    
    public long zremove(String key, String[] values) {
        return businessRedisTemplateClusterClient.opsForZSet().remove(key, values);
    }

    
    // 获取所有 score 值介于 min 和 max 之间(包括等于 min 或 max )的成员。
    public Set<String> zrangeByScore(String key, double min, double max, long offset, long count) {
        return businessRedisTemplateClusterClient.opsForZSet().rangeByScore(key, min, max, offset, count);
    }

    
    public Long zremoveByScore(String key, double min, double max) {
        return businessRedisTemplateClusterClient.opsForZSet().removeRangeByScore(key, min, max);
    }

    
    public Long zCard(String key) {
        return businessRedisTemplateClusterClient.opsForZSet().zCard(key);
    }

    
    public Double zScore(String key, Object value) {
        return businessRedisTemplateClusterClient.opsForZSet().score(key, value);
    }

    
    public void addSetValue(String key, String[] value) {
        businessRedisTemplateClusterClient.opsForSet().add(key, value);
        businessRedisTemplateClusterClient.expire(key, VideoConstants.LIBRARY_ACTIVITY_TRACE_EXPIRE, TimeUnit.SECONDS);
    }

    
    public Set<String> getSetValue(String key) {
        return businessRedisTemplateClusterClient.opsForSet().members(key);
    }

    
    public boolean setIfAbsent(String key, String value, long timeout, TimeUnit timeUnit) {
//        return businessRedisTemplateClusterClient.opsForValue().setIfAbsent(key, value, timeout, timeUnit);
        if (businessRedisTemplateClusterClient.opsForValue().setIfAbsent(key, value)) {
            businessRedisTemplateClusterClient.expire(key, timeout, timeUnit);
            return true;
        }
        return false;
    }

    
    public String getAndSet(String key, String value) {
        return businessRedisTemplateClusterClient.opsForValue().getAndSet(key, value);
    }

    /**
     * @param keys
     * @return
     */
    public List<String> multiGet(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) return Collections.emptyList();
        /**
         * 若要使用this调用生效aop，需要使用@EnableAspectJAutoProxy(exposeProxy = true)；
         * https://codeleading.com/article/***********/
         * 这里先用获取代理类的方式解决
         */
        return keys.stream().map(this::get).collect(Collectors.toList());
    }

    
    public List<String> rangeList(String key) {
        return businessRedisTemplateClusterClient.opsForList().range(key, 0, -1);
    }

    public String lpop(String key) {
        return businessRedisTemplateClusterClient.opsForList().leftPop(key);
    }

    
    public void saveList(String key, String value, long expireTime) {
        businessRedisTemplateClusterClient.opsForList().leftPush(key, value);
        if(expireTime > 0){
            businessRedisTemplateClusterClient.expire(key, expireTime, TimeUnit.SECONDS);
        }
    }

    
    public void deleteListValue(String key, String value) {
        businessRedisTemplateClusterClient.opsForList().remove(key, 0, value);
    }

    public JSONObject clearRedisCache(List<String> names) {
        // return lettuceDataTransClient.clearRedisCache(names);
        log.info("clearRedisCache begin! names={}", JSON.toJSONString(names));
        JSONObject results = new JSONObject();
        if (CollectionUtils.isEmpty(names)) return results;
        for (String name : names) {
            Integer deleteNum = businessRedisTemplateClusterClient.execute((RedisCallback<Integer>) conn -> {
                ScanOptions scanOptions = ScanOptions.scanOptions().count(100L).match(name + "::*").build();
                Cursor<byte[]> cursor = conn.scan(scanOptions);
                int count = 0;
                while (cursor.hasNext()) {
                    conn.del(cursor.next());
                    count++;
                }
                return count;
            });
            JSONObject result = new JSONObject().fluentPut("deleteNum", deleteNum);
            log.info("clearRedisCache delete end! name={},result={}", name, JSON.toJSONString(result));
            results.put(name, result);
        }
        log.info("clearRedisCache end! results={}", JSON.toJSONString(results));
        return results;
    }

    public Long delete(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) return 0L;
        keys.forEach(this::delete);
        return (long) keys.size();
    }

    
    public Set<String> setMembers(String key) {
        return businessRedisTemplateClusterClient.opsForSet().members(key);
    }

    
    public Long setAdd(String key, Collection<String> values) {
        return businessRedisTemplateClusterClient.opsForSet().add(key, values.stream().toArray(String[]::new));
    }

    public Lock obtain(String key) {
        return redisLockRegistry.obtain(key);
    }

    
    public Long ttl(String key) {
        return businessRedisTemplateClusterClient.getExpire(key);
    }

    public int generateLibraryId(Integer userId) {
        try {
            return (int) this.incrBy(VideoStoreService.REDIS_KEY_VIDEO_GENERATE_LIBRARY_ID + userId, -1);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "generateLibraryId error! userId={}", userId, e);
            // 使用0作为libraryId。有10%的老app使用libraryId去查看详情和删除。
            return VideoStoreService.DEFAULT_LIBRARY_ID;
        }
    }

    
    public Observable<List<String>> setScan(String key, String match, int count) {
        final ScanOptions scanOptions = ScanOptions.scanOptions().match(match).count(count).build();
        final Cursor<String> cursor = businessRedisTemplateClusterClient.opsForSet().scan(key, scanOptions);
        return Observable.<String>create(emitter -> {
            while (cursor.hasNext()) emitter.onNext(cursor.next());
            emitter.onComplete();
        }).buffer(count);
    }

    
    public Long setRemove(String key, Collection<String> values) {
        return businessRedisTemplateClusterClient.opsForSet().remove(key, values.stream().toArray(String[]::new));
    }

    public Boolean getBit(String key,Long offset) {
        return businessRedisTemplateClusterClient.opsForValue().getBit(key, offset);
    }

    public Boolean setBit(String key,Long offset, Boolean flag) {
        return businessRedisTemplateClusterClient.opsForValue().setBit(key, offset, flag);
    }

    private final Cache<String, Optional<String>> localCache = Caffeine.newBuilder()
            .expireAfterWrite(1L, TimeUnit.MINUTES)
            .build();

    public Optional<String> getFromLocalCache(String key) {
        if (StringUtils.isBlank(key)) return Optional.empty();
        try {
            return localCache.get(key, k -> {
                try {
                    return Optional.ofNullable(get(key));
                } catch (Throwable e) {
                    log.error("getFromLocalCache read redis error! key={}", key, e);
                    return Optional.empty();
                }
            });
        } catch (Throwable e) {
            log.error("getFromLocalCache read cache error! key={}", key, e);
            return Optional.empty();
        }
    }

    /**
     * 读取DTIM疲劳值
     */
    public Object getDTIMValue(String serialNumber) {
        return DTIMKeyMigrationUtil.getDTIMFatigue(businessRedisTemplateClusterClient, serialNumber);
    }
    
    /**
     * 设置DTIM疲劳值
     */
    public void setDTIMValue(String serialNumber, Object value) {
        DTIMKeyMigrationUtil.setDTIMFatigue(businessRedisTemplateClusterClient, serialNumber, value);
    }

}
