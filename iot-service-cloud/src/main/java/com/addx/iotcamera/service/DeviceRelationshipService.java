package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.ShareQueryRequest;
import com.addx.iotcamera.bean.device.share.ShareUserInfoQueryResponseDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.RoleDefinitionDO;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.device.ShareApprovalLocalDAO;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.enums.device.DeviceShareTypeEnum;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
public class DeviceRelationshipService {

    @Autowired
    private IShareDAO shareDAO;

    @Autowired
    private VideoSearchService videoSearchService;

    @Autowired
    private UserService userService;
    @Autowired
    @Lazy
    private OpenApiWebhookService openApiWebhookService;

    @Autowired
    private RoleDefinitionService roleDefinitionService;

    @Autowired(required = false)
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    ShareApprovalLocalDAO shareApprovalLocalDAO;

    @Resource
    private StateMachineService stateMachineService;

    @Resource
    private DeviceManualService deviceManualService;

    @Resource
    private DeviceService deviceService;

    public Integer undoShare(ShareApprovalDO approvalDO) {
        approvalDO.setTargetId(Optional.ofNullable(approvalDO.getTargetId()).orElse(0));
        User targetUser = approvalDO.getTargetId().equals(0) ?
                userService.getUserByEmailAndTenantId(approvalDO.getTargetEmail(), null,approvalDO.getTenantId()) :
                userService.queryUserById(approvalDO.getTargetId());
        Integer res = 0;
        if(targetUser != null){
            // 解除已完成的分享记录
            approvalDO.setTargetId(targetUser.getId());
            res = this.undoShareDevice(approvalDO);
        }


        //分享中的记录
        Integer resing = this.undoShareingDevice(targetUser,approvalDO);
        return Math.max(res,resing);
    }

    /**
     * 解除已分享完成的设备
     * @param approvalDO
     * @return
     */
    private int  undoShareDevice(ShareApprovalDO approvalDO){
        Integer res = 0;
        UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(approvalDO.getTargetId(),approvalDO.getSerialNumber());
        if(userRoleDO != null){
            res = userRoleService.cleanShareUserRole(approvalDO);
            videoSearchService.clearSearchOptionCache(Arrays.asList(approvalDO.getTargetId()));
            openApiWebhookService.callWebhookForDeviceShareCancel(approvalDO.getSerialNumber(), approvalDO.getTargetId(), 1);

            //send device unshared event
            if(devicePlatformEventPublisher != null) {
                Map<String, Object> deviceEventParamMap = new HashMap<>();
                deviceEventParamMap.put("userId", String.valueOf(approvalDO.getTargetId()));
                deviceEventParamMap.put("serialNumber", approvalDO.getSerialNumber());
                devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DEVICE_BEUNSHARED, deviceEventParamMap);
            }
        }
        return res;
    }

    /**
     * 分享未完成的记录
     * @param targetUser
     * @param approvalDO
     * @return
     */
    private int  undoShareingDevice(User targetUser,ShareApprovalDO approvalDO){
        String targetEmail = targetUser == null ? approvalDO.getTargetEmail() : targetUser.getEmail();
        if(!StringUtils.hasLength(targetEmail)){
            return 0;
        }
        List<ShareStatusDO> shareStatusDOList = shareApprovalLocalDAO.queryDeviceShareingList(approvalDO.getSerialNumber(),approvalDO.getTargetEmail(), DeviceShareTypeEnum.CLOUD.getCode());
        if(CollectionUtils.isEmpty(shareStatusDOList)){
            return 0;
        }

        for(ShareStatusDO shareStatusDO : shareStatusDOList){
            ShareApprovalDO shareApprovalDO = ShareApprovalDO.builder()
                    .id(shareStatusDO.getId())
                    .status(-2)
                    .targetId(targetUser == null ? 0 : targetUser.getId())
                    .build();
            shareApprovalLocalDAO.setApprovalStatus(shareApprovalDO);
        }
        return shareStatusDOList.size();
    }


    public Integer undoShareSelf(ShareApprovalDO approvalDO) {
        Integer result = userRoleService.cleanShareUserRole(approvalDO);
        videoSearchService.clearSearchOptionCache(Arrays.asList(approvalDO.getTargetId()));
        openApiWebhookService.callWebhookForDeviceShareCancel(approvalDO.getSerialNumber(), approvalDO.getTargetId(), 2);

        //send device unshared event
        if(devicePlatformEventPublisher != null) {
            Map<String, Object> deviceEventParamMap = new HashMap<>();
            deviceEventParamMap.put("userId", String.valueOf(approvalDO.getTargetId()));
            deviceEventParamMap.put("serialNumber", approvalDO.getSerialNumber());
            devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DEVICE_BEUNSHARED, deviceEventParamMap);
        }
        return result;
    }

    public List<ShareStatusDO> getDeviceShareStatus(ShareQueryRequest requestDO) {
        List<ShareStatusDO> shareStatusDOList = shareDAO.getDeviceShareStatus(requestDO);
        if (CollectionUtils.isEmpty(shareStatusDOList)) {
            return Lists.newArrayList();
        }
        List<Integer> userId = shareStatusDOList.stream().map(ShareStatusDO::getUserId).collect(Collectors.toList());
        Map<Integer, User> userMap = userService.queryUserMap(userId);

        for (ShareStatusDO shareStatusDO : shareStatusDOList) {
            RoleDefinitionDO roleDefinitionDO = roleDefinitionService.quRoleDefinitionDO(shareStatusDO.getRoleId());
            if (roleDefinitionDO != null) {
                shareStatusDO.setRole(roleDefinitionDO.getRole());
            }

            if (userMap.containsKey(shareStatusDO.getUserId())) {
                shareStatusDO.setUserName(userMap.get(shareStatusDO.getUserId()).getName());
                shareStatusDO.setUserEmail(userService.userEmailProtect(userMap.get(shareStatusDO.getUserId()).getEmail()));
                shareStatusDO.setUserPhone(userService.userPhoneProtect(userMap.get(shareStatusDO.getUserId()).getPhone()));
            }
        }
        return shareStatusDOList;
    }

    public Integer insertApproval(Integer userId, ShareCacheDO cacheDO) {

        ShareApprovalDO shareApprovalDO = new ShareApprovalDO();
        shareApprovalDO.setAdminId(cacheDO.getAdminId());
        shareApprovalDO.setSerialNumber(cacheDO.getSerialNumber());
        shareApprovalDO.setRoleId(cacheDO.getRoleId());
        shareApprovalDO.setSerialNumber(cacheDO.getSerialNumber());
        shareApprovalDO.setTargetId(userId);
        shareApprovalDO.setShareId(cacheDO.getShareId());
        return shareDAO.insertApproval(shareApprovalDO);
    }

    /**
     * 用户名下未处理的分享
     * @param requestDO
     * @return
     */
    public List<ShareQueryResponseDO> getRecentApprovals(ShareQueryRequest requestDO) {
        List<ShareQueryResponseDO> shareQueryResponseDOList = shareDAO.getRecentApprovals(requestDO, DeviceShareService.SHARE_TIME_EXPIRE);
        if (CollectionUtils.isEmpty(shareQueryResponseDOList)) {
            return Lists.newArrayList();
        }
        shareQueryResponseDOList.forEach(share -> {
            share.setTargetEmail(userService.userEmailProtect(share.getTargetEmail()));
            share.setTargetPhone(userService.userPhoneProtect(share.getTargetPhone()));
        });
        return shareQueryResponseDOList;
    }


    /**
     * 用户名下未处理的分享--基站
     * @param requestDO
     * @return
     */
    public List<ShareQueryResponseDO> queryRecentApprovalsLocal(ShareApprovalDO requestDO) {
        User targetUser = userService.queryUserById(requestDO.getTargetId());
        requestDO.setTargetEmail(targetUser.getEmail());

        //未处理的分享记录
        List<ShareApprovalDO> shareApprovalList = shareApprovalLocalDAO.queryShareApprovalList(requestDO);
        if (CollectionUtils.isEmpty(shareApprovalList)) {
            return Lists.newArrayList();
        }

        List<Integer> adminIds = shareApprovalList.stream().map(ShareApprovalDO::getAdminId).collect(Collectors.toList());
        Map<Integer,User> userMap = userService.queryUserMap(adminIds);

        return shareApprovalList.stream()
            .map(
                shareApprovalDO -> {
                  User adminUser = userMap.get(shareApprovalDO.getAdminId());
                  if (adminUser == null || adminUser.getStatus() == 1) {
                    // 不存在或者已注销的用户
                    return null;
                  }

                  ShareQueryResponseDO responseDO = new ShareQueryResponseDO();
                  BeanUtils.copyProperties(shareApprovalDO, responseDO);

                  responseDO.setAdminEmail(adminUser.getEmail());
                  responseDO.setShareDeviceType(shareApprovalDO.getDeviceType());
                  return responseDO;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }


    /**
     * 查询设备已分享的用户
     * @param request
     * @return
     */
    public List<ShareStatusDO> getDeviceShareUserList(ShareQueryRequest request) {
        List<ShareStatusDO>  responseList = com.google.common.collect.Lists.newArrayList();

        //已分享完成的用户
        responseList.addAll(this.querySharedUserList(request));

        Set<String> email = responseList.stream()
                .map(ShareStatusDO::getUserEmail)
                .collect(Collectors.toSet());
        //未分享完成的用户
        responseList.addAll(this.queryShareingUserList(request,email));
        return responseList;
    }


    /**
     * 已分享的用户
     * @param request
     * @return
     */
    private List<ShareStatusDO> querySharedUserList(ShareQueryRequest request){
        // 查询设备已分享的用户
        List<Integer> shareUserIdList = userRoleService.findAllUsersForDevice(request.getSerialNumber()).stream()
                .filter(userId -> !userId.equals(request.getAdminId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(shareUserIdList)){
            return Lists.newArrayList();
        }

        //组装用户信息
        Map<Integer, User> userMap = userService.queryUserMap(shareUserIdList);
        List<ShareStatusDO> responseList = com.google.common.collect.Lists.newArrayList();
        for (Integer userId : shareUserIdList) {
            if(!userMap.containsKey(userId)){
                continue;
            }
            User user = userMap.get(userId);
            ShareStatusDO shareStatusDO = ShareStatusDO.builder()
                    .userId(userId)
                    .userName(user.getName())
                    .userEmail(user.getEmail())
                    .status(1)
                    .shareDeviceNum(1)
                    .build();
            responseList.add(shareStatusDO);
        }
        return responseList;
    }


    /**
     * 分享中的用户
     * @param request
     * @return
     */
    private List<ShareStatusDO> queryShareingUserList(ShareQueryRequest request,Set<String> email){
        // 查询设备所属用户，过滤掉admin
        return shareApprovalLocalDAO.queryDeviceShareingList(request.getSerialNumber(),null, DeviceShareTypeEnum.CLOUD.getCode())
                .stream()
                .filter(s -> !email.contains(s.getUserEmail()))
                .collect(Collectors.toList());
    }



    /**
     * 查询被分享者信息
     * @param request
     * @return
     */
    public ShareUserInfoQueryResponseDO queryShareUserInfo(ShareQueryRequest request){
        User user = request.getUserId() != null ? userService.queryUserById(request.getUserId()) :
                userService.getUserByEmailAndTenantId(request.getTargetEmail(),null,request.getApp().getTenantId());

        ShareUserInfoQueryResponseDO responseDO = new ShareUserInfoQueryResponseDO();
        responseDO.setTargetEmail(request.getTargetEmail());
        //被分享者信息
        if(user == null){
            //被邀请者未创建账号
            responseDO.setTargetId(0);
            responseDO.setShareStatus(0);
        }else{
            responseDO.setTargetId(user.getId());
            UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(user.getId(),request.getSerialNumber());
            responseDO.setShareStatus(userRoleDO == null ? 0 : 1);
        }


        DeviceManualDO deviceManualDO = deviceManualService.getDeviceManualBySerialNumber(request.getSerialNumber());
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(request.getSerialNumber());

        //组装bx设备信息
        ShareUserInfoQueryResponseDO.UserShareDeviceDO userShareDevice = new ShareUserInfoQueryResponseDO.UserShareDeviceDO();
        userShareDevice.setSerialNumber(request.getSerialNumber());

        //查询设备在线状态
        DeviceStateDO deviceState;
        try {
            deviceState = stateMachineService.getDeviceState(request.getSerialNumber());
        } catch (Exception e) {
            deviceState = null;
            com.addx.iotcamera.util.LogUtil.warn(log, "failed getDeviceState serialNumber {}", request.getSerialNumber());
        }
        userShareDevice.setOnline(deviceState == null ? 0 : deviceState.initDeviceOnlineStatus());
        userShareDevice.setModelNo(deviceManualDO.getModelNo());
        userShareDevice.setDeviceName(deviceDO.getDeviceName());
        userShareDevice.setShared(responseDO.getShareStatus().equals(1));

        responseDO.setUserShareDevice(userShareDevice);
        return responseDO;
    }
}
