package com.addx.iotcamera.service.app;

import com.addx.iotcamera.config.app.TencentCodeTemplateConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.helper.ConcurrentSingleFactory;
import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TencentSmsService {

    public static final String PHONE_COUNTRY_CODE_CN = "+86";

    @Autowired
    @Lazy
    private TencentCodeTemplateConfig config;

    private final ConcurrentSingleFactory<SmsClient> smsClientFactory = new ConcurrentSingleFactory<>(this::createSmsClient);

    public SmsClient getSmsClient() {
        return smsClientFactory.get();
    }

    @PostConstruct
    public void init() {
        CompletableFuture.runAsync(this::getSmsClient);
    }

    public SmsClient createSmsClient() {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId，SecretKey。
            // 为了保护密钥安全，建议将密钥设置在环境变量中或者配置文件中，请参考本文凭证管理章节。
            // 硬编码密钥到代码中有可能随代码泄露而暴露，有安全隐患，并不推荐。
            // Credential cred = new Credential("SecretId", "SecretKey");
            Credential cred = new Credential(config.getSecretId(), config.getSecretKey());

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            // 从3.0.96版本开始, 单独设置 HTTP 代理
            // httpProfile.setProxyHost("真实代理ip");
            // httpProfile.setProxyPort(真实代理端口);
            httpProfile.setReqMethod("POST"); // get请求(默认为post请求)
            httpProfile.setConnTimeout(30); // 请求连接超时时间，单位为秒(默认60秒)
            httpProfile.setWriteTimeout(30);  // 设置写入超时时间，单位为秒(默认0秒)
            httpProfile.setReadTimeout(30);  // 设置读取超时时间，单位为秒(默认0秒)
            httpProfile.setEndpoint(config.getEndpoint()); // 指定接入地域域名(默认就近接入)

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod("HmacSHA256"); // 指定签名算法(默认为HmacSHA256)
            // 自3.1.80版本开始，SDK 支持打印日志。
            clientProfile.setHttpProfile(httpProfile);
            clientProfile.setDebug(true);
            // 从3.1.16版本开始，支持设置公共参数 Language, 默认不传，选择(ZH_CN or EN_US)
            clientProfile.setLanguage(Language.ZH_CN);
            // 实例化要请求产品(以cvm为例)的client对象,clientProfile是可选的
            SmsClient client = new SmsClient(cred, config.getRegion(), clientProfile);
            log.info("createSmsClient end! endpoint={},region={}", config.getEndpoint(), config.getRegion());
            return client;
        } catch (Exception e) {
            log.error("createSmsClient error! endpoint={},region={}", config.getEndpoint(), config.getRegion(), e);
            return null;
        }
    }

    public Set<String> sendSms(String templateId, String signName, String[] templateParamSet, String[] phoneNumberSet) {
        SmsClient smsClient = getSmsClient();
        if (smsClient == null) return Collections.emptySet();
        try {
            SendSmsRequest req = new SendSmsRequest();
            // 下发手机号码，采用 E.164 标准，格式为+[国家或地区码][手机号]，单次请求最多支持200个手机号且要求全为境内手机号或全为境外手机号。
            req.setPhoneNumberSet(phoneNumberSet);
            // 短信 SdkAppId，在 短信控制台 添加应用后生成的实际 SdkAppId，示例如1400006666。
            req.setSmsSdkAppId(config.getSdkAppId());
            // 模板 ID，必须填写已审核通过的模板 ID。
            req.setTemplateId(templateId);
            // 短信签名内容，使用 UTF-8 编码，必须填写已审核通过的签名。
            req.setSignName(signName);
            // 模板参数，若无模板参数，则设置为空。模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致。
            req.setTemplateParamSet(templateParamSet);
            // 用户的 session 内容，可以携带用户侧 ID 等上下文信息，server 会原样返回。注意长度需小于512字节。
            req.setSessionContext(MDC.get(MDCKeys.REQUEST_ID));

            SendSmsResponse resp = smsClient.SendSms(req);
            log.info("sendSms end! templateId={},signName={},templateParamSet={},phoneNumberSet={},resp={}", templateId, signName, JSON.toJSONString(templateParamSet), JSON.toJSONString(phoneNumberSet), JSON.toJSONString(resp));
            return Arrays.stream(resp.getSendStatusSet()).filter(it -> "Ok".equals(it.getCode()))
                    .map(SendStatus::getPhoneNumber).collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("sendSms error! templateId={},signName={},templateParamSet={},phoneNumberSet={}", templateId, signName, JSON.toJSONString(templateParamSet), JSON.toJSONString(phoneNumberSet), e);
        }
        return Collections.emptySet();
    }

    /**
     * 发送短信验证码
     *
     * @param language 语言
     * @param tenantId 租户id
     * @param code     验证码
     * @param phone    手机号
     * @return <0:表示不符合条件，0:表示发送失败，1:表示发送成功
     */
    public int sendSmsCode(String language, String tenantId, String code, String phone) {
        try {
            if (!config.getEnable()) return -1;
            String templateId = config.getTemplate().get(language);
            if (StringUtils.isBlank(templateId)) return -2;
            String signName = config.getSignName().get(tenantId);
            if (StringUtils.isBlank(signName)) return -3;
            if (StringUtils.isNotBlank(config.getValidPhonePattern())) {
                if (!Pattern.matches(config.getValidPhonePattern(), phone)) return -4;
            }
            String totalPhone = PHONE_COUNTRY_CODE_CN + phone;
            Set<String> okPhones = sendSms(templateId, signName, new String[]{code}, new String[]{totalPhone});
            return okPhones.contains(totalPhone) ? 1 : 0;
        } catch (Exception e) {
            log.error("sendSmsCode error! language={},tenantId={},code={},phone={}", language, tenantId, code, phone, e);
            return -99;
        }
    }

}
