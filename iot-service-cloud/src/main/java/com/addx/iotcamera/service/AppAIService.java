package com.addx.iotcamera.service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.addx.iotcamera.bean.app.AppAiConfigRequest;
import com.addx.iotcamera.bean.db.AppAiConfigDO;
import com.addx.iotcamera.bean.db.AppAiConfigGlobalDO;
import com.addx.iotcamera.dao.AppAiConfigDAO;
import com.addx.iotcamera.dao.AppAiConfigGlobalDAO;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * app端 ai设置 service
 */
@Slf4j
@Service
public class AppAIService {

    @Autowired
    private AppAiConfigDAO appAiConfigDAO;

    @Autowired
    private AppAiConfigGlobalDAO appAiConfigGlobalDAO;

    @Value("${app.aiConfig.returnEmptyValueRatio:100}")
    private Integer returnEmptyValueRatio = 100;

    /**
     * app端上报更新ai capability
     * @param userId
     * @param request
     */
    @SentinelResource("updateAiCapability")
    @Transactional
    public void updateAiCapability(Integer userId, AppAiConfigRequest request) {
        AppAiConfigDO appAiConfigDO = appAiConfigDAO.getByUserAndDeivceCode(userId, request.getDeviceCode(), request.getA4xAiLibVersion());
        if(appAiConfigDO == null) {
            appAiConfigDO = new AppAiConfigDO();
            appAiConfigDO.setApiVersion(request.getA4xAiLibVersion());
            appAiConfigDO.setDeviceCode(request.getDeviceCode());
            appAiConfigDO.setVideo("unkonwn");
            appAiConfigDO.setImage("unkonwn");
            appAiConfigDO.setUserId(userId);
        }

        String image = appAiConfigDO.getImage();
        String video = appAiConfigDO.getVideo();
        if(request.getMagicPix() != null) {
            image = request.getMagicPix().getImage();
            video = request.getMagicPix().getVideo();
            //修复labels数量错误问题
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
                if(StringUtils.isNotEmpty(request.getMagicPix().getImage())) {
                    PrometheusMetricsUtil.getAppAiConfigCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "magic_pix_image", "1").inc();;
                }
                if(StringUtils.isNotEmpty(request.getMagicPix().getVideo())) {
                    PrometheusMetricsUtil.getAppAiConfigCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "magic_pix_video", "1").inc();;
                }
            });
        }
        appAiConfigDO.setImage(image);
        appAiConfigDO.setVideo(video);

        if(appAiConfigDO.getId() == null) {
            appAiConfigDAO.insert(appAiConfigDO);
        } else {
            appAiConfigDAO.updateById(appAiConfigDO);
        }
    }

    /**
     * app端获取 ai capability
     * @param userId
     * @param deviceCode
     * @param a4xAiLibVersion
     * @return
     * @throws Exception
     */
    public Map queryAiCapability(Integer userId, String deviceCode, String a4xAiLibVersion) throws Exception {
        AppAiConfigGlobalDO globalAppAiConfig = appAiConfigGlobalDAO.getByDeivceCode(deviceCode, a4xAiLibVersion);
        if(globalAppAiConfig == null || StringUtils.isEmpty(globalAppAiConfig.getTemplate())) {
            if((Math.random() * returnEmptyValueRatio) < 5) {
                globalAppAiConfig = appAiConfigGlobalDAO.getByDeivceCode("DEFAULT", a4xAiLibVersion);
            }
        }

        if(globalAppAiConfig == null || StringUtils.isEmpty(globalAppAiConfig.getTemplate())) {
            return Collections.emptyMap();
        }

        // 配置模版
        String template = globalAppAiConfig.getTemplate();

        // param replace
        if(StringUtils.isNotEmpty(globalAppAiConfig.getParamList())) {
            List<Map> paramList = JsonUtil.fromJson(globalAppAiConfig.getParamList(), List.class);
            for(Map paramMap : paramList) {
                String name = (String)paramMap.get("name");
                String value = (String)paramMap.get("value");
                if(StringUtils.isEmpty(name)) {
                    continue;
                }
                String nameKey = String.join("", "${", name, "}");
                template = template.replace(nameKey, value);
            }
        }

        Map resultMap = JsonUtil.fromJson(template, HashMap.class);
        resultMap.put("deviceCode", deviceCode);
        resultMap.put("a4xAiLibVersion", a4xAiLibVersion);
        return resultMap;
    }
}
