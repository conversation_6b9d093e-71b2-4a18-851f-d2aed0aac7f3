package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.device.DeviceWhiteListDO;
import com.addx.iotcamera.dao.device.DeviceWhiteListDAO;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class DeviceWhiteListService {

    @Autowired
    private DeviceWhiteListDAO deviceWhiteListDAO;

    public DeviceWhiteListDO queryBySn(String sn) {
        return deviceWhiteListDAO.queryBySn(sn);
    }

    public int update(DeviceWhiteListDO item) {
        if (item == null || StringUtils.isBlank(item.getSerialNumber())) return 0;
        return deviceWhiteListDAO.update(item);
    }

    public int insert(DeviceWhiteListDO item) {
        if (item == null || StringUtils.isBlank(item.getSerialNumber())) return 0;
        return deviceWhiteListDAO.insert(item);
    }

    public PirServiceName getPirVideoStorageServiceBySn(String sn) {
        return Optional.ofNullable(queryBySn(sn)).map(it -> EnumUtils.getEnum(PirServiceName.class, it.getPirVideoStorageService())).orElse(null);
    }

    public List<DeviceWhiteListDO> queryByCondition(DeviceWhiteListDO condition, Integer offset, Integer size) {
        return deviceWhiteListDAO.queryByCondition(condition, offset, size);
    }

    public long countByCondition(DeviceWhiteListDO condition) {
        return deviceWhiteListDAO.countByCondition(condition);
    }

    public List<DeviceWhiteListDO> queryBySns(List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) return Collections.emptyList();
        return deviceWhiteListDAO.queryBySns(sns);
    }
}
