package com.addx.iotcamera.service.app;

import com.addx.iotcamera.bean.response.ExtensionAbTestResponse;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.VipService;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.abtest.model.AbFeatureSimpleResult;
import com.addx.iotcamera.service.vip.TierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ExtensionAbTestService {

    @Autowired
    private UserRoleService userRoleService;
    
    @Autowired
    private TierService tierService;
    
    @Autowired
    private VipService vipService;
    
    @Autowired
    private DeviceInfoService deviceInfoService;
    
    private static final Map<String,String> EXTENSION_TO_AB_KEY = new HashMap<>();

    static {
        EXTENSION_TO_AB_KEY.put("smartAlertsPro", "short_summary");
    }
    @Autowired
    private AbTestService abTestService;

    /**
     * 获取用户的扩展AB测试信息
     * @param userId 用户ID
     * @param extensionName 扩展名称
     * @return 扩展AB测试响应
     */
    public ExtensionAbTestResponse getExtensionAbTestInfo(Integer userId, String extensionName) {
        if (userId == null || extensionName == null || extensionName.isEmpty()) {
            return ExtensionAbTestResponse.builder()
                    .enable(false)
                    .devices(new HashMap<>())
                    .build();
        }
        
        // 获取用户所有设备
        List<String> userSerialNumbers = userRoleService.getUserSerialNumberByUserId(userId);
        if (userSerialNumbers == null || userSerialNumbers.isEmpty()) {
            return ExtensionAbTestResponse.builder()
                    .enable(false)
                    .devices(new HashMap<>())
                    .build();
        }
        
        // 检查用户是否有非4G设备和非喂鸟器设备

        boolean hasNonBirdOr4GDevice = userSerialNumbers.stream()
                .anyMatch(serialNumber -> !tierService.isBirdDevice(serialNumber) &&  !deviceInfoService.checkIfDeviceUse4G(serialNumber));
        
        // 用户级别的AB测试逻辑
        boolean userEnabled = isExtensionEnabledForUser(userId, extensionName, hasNonBirdOr4GDevice);
        
        // 设备级别的AB测试逻辑
        Map<String, Boolean> deviceMap = new HashMap<>();
        
        for (String serialNumber : userSerialNumbers) {
            boolean isEnabled = isExtensionEnabledForDevice(serialNumber, extensionName, userId);
            deviceMap.put(serialNumber, isEnabled);
        }
        
        return ExtensionAbTestResponse.builder()
                .enable(userEnabled)
                .devices(deviceMap)
                .build();
    }
    
    /**
     * 判断扩展是否对用户启用
     * @param userId 用户ID
     * @param extensionName 扩展名称
     * @param hasNonBirdOr4GDevice 是否有喂鸟器设备
     * @return 是否启用
     */
    private boolean isExtensionEnabledForUser(Integer userId, String extensionName, boolean hasNonBirdOr4GDevice) {
        // 针对不同的扩展实现不同的AB测试逻辑
        try {
            List<AbFeatureSimpleResult> features = abTestService.allFeatureCheck(userId.toString());

            if (features.stream().anyMatch(f -> f.getFeatureId().equals(EXTENSION_TO_AB_KEY.get(extensionName)) && f.experimentSuccessful())) {
                // smartAlertsPro扩展的AB测试逻辑:
                // 1. 用户需要同时拥有喂鸟器设备和非4G设备
                return hasNonBirdOr4GDevice;
            }
        }catch (Exception ex){
            log.warn("ExtensionAbTestService.isExtensionEnabledForUser error", ex);
            return false;
        }
        
        // 默认不启用
        return false;
    }
    
    /**
     * 判断扩展是否对设备启用
     * @param serialNumber 设备序列号
     * @param extensionName 扩展名称
     * @param userId 用户ID
     * @return 是否启用
     */
    private boolean isExtensionEnabledForDevice(String serialNumber, String extensionName, Integer userId) {
        // 针对不同的扩展实现不同的设备级别AB测试逻辑
        // 1. 如果是喂鸟器设备，则启用
        // 2. 如果是非4G设备，则启用
        // 3. 如果是VIP设备，则启用
        return tierService.isBirdDevice(serialNumber) ||
                !deviceInfoService.checkIfDeviceUse4G(serialNumber) ||
                vipService.isVipDevice(userId, serialNumber);

    }
} 