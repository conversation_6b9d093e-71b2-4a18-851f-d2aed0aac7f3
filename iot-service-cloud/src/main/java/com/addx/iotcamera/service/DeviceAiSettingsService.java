package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.DeviceAiSettingsDO;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.config.device.DeviceSettingConfig;
import com.addx.iotcamera.dao.DeviceAiSettingsDAO;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.domain.extension.ai.enums.AiObjectActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.addx.iot.domain.extension.ai.enums.utils.AiObjectEnumUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Builder
@Component
public class DeviceAiSettingsService {

    @Autowired
    private DeviceAiSettingsDAO deviceAiSettingsDAO;
    @Lazy
    @Autowired
    private VipService vipService;
    @Lazy
    @Autowired
    private DeviceModelEventService deviceModelEventService;
    @Lazy
    @Autowired
    private DeviceManualService deviceManualService;

    @Autowired
    @Lazy
    private DeviceModelConfigService deviceModelConfigService;


    // AI分析初始化值
    public static final ImmutableSet<AiObjectEnum> INIT_ENABLE_EVENT_OBJECTS = ImmutableSet.of(AiObjectEnum.PERSON, AiObjectEnum.PET, AiObjectEnum.VEHICLE, AiObjectEnum.BIRD);

    // AI分析被打开后默认打开的事件推送eventType
    private static final ImmutableMap<String, ImmutableSet<String>> INIT_NOTIFY_EVENT_TYPES = ImmutableMap.<String, ImmutableSet<String>>builder()
            .put(AiObjectEnum.PERSON.getName(), ImmutableSet.of(AiObjectActionEnum.PERSON_ANY.getName()))
            .put(AiObjectEnum.PET.getName(), ImmutableSet.of(AiObjectActionEnum.PET_ANY.getName()))
            .put(AiObjectEnum.VEHICLE.getName(), ImmutableSet.of(AiObjectActionEnum.VEHICLE_ANY.getName(),AiObjectActionEnum.VEHICLE_ENTER.getName(), AiObjectActionEnum.VEHICLE_OUT.getName()))
            .put(AiObjectEnum.BIRD.getName(), ImmutableSet.of(AiObjectActionEnum.BIRD_ANY.getName()))
            .put(AiObjectEnum.NUISANCE_ANIMAL.getName(), ImmutableSet.of(AiObjectActionEnum.NUISANCE_ANIMAL_ANY.getName()))
            .build();

    private static final ImmutableMap<String, ImmutableSet<String>> SAFEMO_INIT_NOTIFY_EVENT_TYPES = ImmutableMap.<String, ImmutableSet<String>>builder()
            .put(AiObjectEnum.PERSON.getName(), ImmutableSet.of(AiObjectActionEnum.PERSON_EXIST.getName()))
            .put(AiObjectEnum.PET.getName(), ImmutableSet.of(AiObjectActionEnum.PET_EXIST.getName()))
            .put(AiObjectEnum.VEHICLE.getName(), ImmutableSet.of(AiObjectActionEnum.VEHICLE_APPEAR.getName()))
            .put(AiObjectEnum.BIRD.getName(), ImmutableSet.of())
            .put(AiObjectEnum.PACKAGE.getName(), ImmutableSet.of(AiObjectActionEnum.PACKAGE_EXIST.getName()))
            .build();
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private DeviceSettingConfig deviceSettingConfig;
    @Autowired
    private UserService userService;

    public static ImmutableMap<String, ImmutableSet<String>> getInitNotifyEventTypes(String tenantId){
        if(AppConstants.TENANTID_SAFEMO.equals(tenantId)){
            return SAFEMO_INIT_NOTIFY_EVENT_TYPES;
        }
        return INIT_NOTIFY_EVENT_TYPES;
    }

    @CacheEvict(value = {"deviceAiSettings"}, key = "#userId+'-'+#serialNumber")
    public DeviceAiSettingsDO saveDeviceAiSettings(Integer userId, String serialNumber, Collection<AiObjectEnum> eventObjects) {
        log.info("saveDeviceAiSettings userId={},sn={},eventObjects={}", userId, serialNumber, eventObjects);
        int bits = AiObjectEnumUtil.bitsOfEventObjects(eventObjects);
        DeviceAiSettingsDO model = DeviceAiSettingsDO.builder().userId(userId).serialNumber(serialNumber).eventObjectsSwitch(bits).build();
        Integer saveNum = deviceAiSettingsDAO.save(model);
        log.info("saveDeviceAiSettings model={},saveNum={}", JSON.toJSONString(model), saveNum);
        return model;
    }

    @CacheEvict(value = {"deviceAiSettings"}, key = "#userId+'-'+#serialNumber")
    public DeviceAiSettingsDO initDeviceAiSettings(Integer userId, String serialNumber) {
        final ImmutableSet<AiObjectEnum> enableEventObjects = INIT_ENABLE_EVENT_OBJECTS;
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo != null) {
            DeviceModel deviceModel= deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo);
            //常电设备默认不开开发过滤
            if(!deviceModel.isCanStandby()){
                return saveDeviceAiSettings(userId, serialNumber, Set.of());
            }
            try {
                Set<AiObjectEnum> aiSaasEventObjects = deviceModelEventService.queryDeviceModelEvent(modelNo).stream()
                        .map(eventName -> {
                            try {
                                if(AiObjectEnum.PACKAGE.getName().equals(eventName)){
                                    if(!AppConstants.TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))){
                                        return null;
                                    }
                                }
                                return AiObjectEnum.fromObjectName(eventName);
                            } catch (IllegalArgumentException e) {
                                log.warn("Invalid AiObjectEnum value: {}, skipping it", eventName);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if(deviceSettingConfig.queryEnableOtherMotionAi(modelNo)){
                    aiSaasEventObjects.add(AiObjectEnum.All_OTHER_MOTIONS);
                }
                return saveDeviceAiSettings(userId, serialNumber, aiSaasEventObjects);
            } catch (Exception e) {
                log.error("Error processing AI settings for model {}: {}", modelNo, e.getMessage(), e);
            }
        }
        log.info("initDeviceAiSettings userId={},sn={},enableEventObjects={}", userId, serialNumber, enableEventObjects);
        return saveDeviceAiSettings(userId, serialNumber, enableEventObjects);
    }

    // 用户查询不经过缓存，防止缓存刷新慢的问题
    @Cacheable(value = "deviceAiSettings", key = "#userId+'-'+#serialNumber", unless = "#result==null")
    public Set<AiObjectEnum> queryEnableEventObjects(Integer userId, String serialNumber) {
        DeviceAiSettingsDO model = deviceAiSettingsDAO.queryBySerialNumber(userId, serialNumber);
        if (model == null) { // 存量用户需要初始化这个表
            model = initDeviceAiSettings(userId, serialNumber);
        }
        Set<AiObjectEnum> eventObjects = AiObjectEnumUtil.eventObjectsOfBits(model.getEventObjectsSwitch());
        log.info("queryEnableEventObjects userId={},sn={},model={},eventObjects={}", userId, serialNumber, JSON.toJSONString(model), eventObjects);
        return eventObjects;
    }

    public Set<AiObjectEnum> queryEnableEventObjects(String serialNumber){
        return queryEnableEventObjects(userRoleService.getDeviceAdminUser(serialNumber), serialNumber);
    }

    // 全量更新
    @CacheEvict(value = {"deviceAiSettings"}, key = "#userId+'-'+#serialNumber")
    public Integer updateEnableEventObjects(Integer userId, String serialNumber, Collection<AiObjectEnum> eventObjects) {
        log.info("updateEnableEventObjects userId={},sn={},eventObjects={}", userId, serialNumber, eventObjects);
        int bits = AiObjectEnumUtil.bitsOfEventObjects(eventObjects);
        return deviceAiSettingsDAO.updateEventObjectsSwitch(userId, serialNumber, bits);
    }

    // 部分更新
    @CacheEvict(value = {"deviceAiSettings"}, key = "#userId+'-'+#serialNumber")
    public Integer updateEnableEventObjectsPartly(Integer userId, String serialNumber, Collection<AiObjectEnum> enableList, Collection<AiObjectEnum> disableList) {
        log.info("updateEnableEventObjectsPartly userId={},sn={},enableList={},disableList={}", userId, serialNumber, enableList, disableList);
        int enableBits = AiObjectEnumUtil.bitsOfEventObjects(enableList);
        int disableBits = AiObjectEnumUtil.bitsOfEventObjects(disableList);
        return deviceAiSettingsDAO.updateEventObjectsSwitchPartly(userId, serialNumber, enableBits, disableBits);
    }
}
