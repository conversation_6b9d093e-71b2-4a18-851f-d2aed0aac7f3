package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.LoginRequest;
import com.addx.iotcamera.bean.app.MailConfirmRequest;
import com.addx.iotcamera.bean.app.RegisterRequest;
import com.addx.iotcamera.bean.app.user.LoginOpenRequest;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.user.UserPayEmailDO;
import com.addx.iotcamera.bean.domain.user.UserTrustDevice;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.config.AppUserConfig;
import com.addx.iotcamera.config.app.EmailPayConfig;
import com.addx.iotcamera.config.app.TenantFreeTierConfig;
import com.addx.iotcamera.constants.PrefixConstants;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.enums.user.UserAppScoreMomentEnum;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.publishers.zendesk.ZendeskClient;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.user.UserTrustDeviceService;
import com.addx.iotcamera.util.MessageUtil;
import com.addx.iotcamera.util.ZendeskUtil;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.exceptions.ClientException;
import lombok.Setter;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.controller.auth.AuthController.SALT_LENGTH;
import static org.addx.iot.common.constant.AppConstants.*;
import static org.addx.iot.common.enums.ResultCollection.*;


/**
 * <AUTHOR>
 * @date 2019/6/5
 */
@Service
public class LoginService {
    private static Logger LOGGER = LoggerFactory.getLogger(LoginService.class);

    //用户登录错误次数
    private static final String USER_LOGIN_ERROR_COUNT = "user:login:{user}";
    //用户登录错误次数超过限制后发送的验证码
    private static final String USER_LOGIN_RETRY_CODE = "user:login:retry:code:{user}";
    //登录错误超过5次发送验证码标记
    private static final String USER_LOGIN_CODE_SEND_EXPIRE = "user:login:retry:expire:{user}";

    private static int USER_LOGIN_CODE_SEND_EXPIRE_TIME = 60;

    private static long ERROR_RETRY_COUNT = 5;

    //快捷登录验证码
    private static final String USER_PHONE_CODE = "user:phone:code:{phone}:{tenantId}";
    //快捷登录验证码失败次数
    private static final String USER_PHONE_CODE_COUNT = "user:phone:code:count:{phone}:{tenantId}";

    private static int USER_CODE_EXPIRE = 15 * 60;
    private static final String PWD_TEST = "567d36915080015f1f58ec6e2b5c74a781ca44dadc508ef1f71d06cb655b769b";
    private static final String SALT_TEST = "aiTest";

    @Autowired
    private UserTrustDeviceService userTrustDeviceService;

    @Autowired
    private UserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private PushService pushService;

    @Autowired
    private MailConfirmService mailConfirmService;

    @Autowired
    private MqSender mqSender;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private AppUserConfig appUserConfig;

    @Autowired
    private AppInfoService appInfoService;

    @Autowired
    private UserSettingService userSettingService;

    @Autowired
    private UserVipActivateService userVipActivateService;

    @Autowired(required = false)
    private ZendeskClient zendeskClient;

    @Autowired
    private EmailPayConfig emailPayConfig;

    @Resource
    private UserAppScoreService userAppScoreService;
    @Resource
    private TenantFreeTierConfig tenantFreeTierConfig;

    @Autowired
    @Lazy
    private ForceChangePwdService forceChangePwdService;

    @Setter
    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 登录
     *
     * @param loginRequest
     * @return
     */
    public Result login(LoginRequest loginRequest) throws ClientException {
        LOGGER.info("login start request:{}", JSON.toJSONString(loginRequest));
        //读取登录请求中的Email
        String email = loginRequest.getEmail();
        String phone = loginRequest.getPhone();
        //打印Email
        LOGGER.info("Login for email:{},phone:{}", email, phone);

        //验证参数
        if (!verifyLoginParam(loginRequest)) {
            return Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        }
        //校验登录失败次数
        virifyLoginTimes(loginRequest);

        //根据Email读取用户类
        User stored = userService.getUserByEmailAndTenantId(email, phone, loginRequest.getApp().getTenantId());

        //账户不存在
        if (stored == null) {
            //账户未注册
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }
        //密码错误
        else if (!StringUtils.isEmpty(loginRequest.getPassword()) && !stored.getHashedPassword().equals(PhosUtils.CalcSalt(loginRequest.getPassword(), stored.getSalt()))) {
            //密码错误
            return userRetryLogin(loginRequest);
        } else if (forceChangePwdService.getForceResetPwdFlag(stored.getId() + "")) { // 拒绝登录
            return Result.Error(NOT_LOGGED_IN); // 返回密码错误，引导用户去重置密码
        }
        //通过验证
        else {
            loginRequest.setId(stored.getId());
            int resultCode = checkCode(loginRequest);
            if (resultCode != 0) {
                return Result.Error(resultCode, "error");
            }

            //登录时更新语言
            stored.setLanguage(loginRequest.getLanguage());
            stored.setAppType(loginRequest.getApp().getAppType());
            stored.setAppVersion(loginRequest.getApp().getVersion());
            stored.setAppVersionName(loginRequest.getApp().getVersionName());
            userService.updateUserLanguage(stored);

            //生成随机种子
            String seed = PhosUtils.getUUID();

            LoginResponseDO loginResponseDO = this.generateLoginOrRegisterResponse(stored, seed, loginRequest, 1);

            checkAppEnvMatchServerProfile(loginRequest);

            //清除用户相关的缓存
            this.cleanUserCode(stored);

            LOGGER.info("Login success ! loginResponseDO:{}:", JSON.toJSONString(loginResponseDO));
            //返回给用户登录成功的response
            return new Result(loginResponseDO);
        }
    }

    public void checkAppEnvMatchServerProfile(LoginRequest loginRequest) {
        if(!StringUtils.isEmpty(loginRequest.getApp().getEnv())) {
            try {
                String env = activeProfile.split("-")[0];
                if(!org.apache.commons.lang3.StringUtils.containsIgnoreCase(loginRequest.getApp().getEnv(), env)) {
                    com.addx.iotcamera.util.LogUtil.warn(LOGGER, "not match server profile {} and request env {}", activeProfile, loginRequest.getApp().getEnv());
                }
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "failed to compute match env and node", e);
            }
        }
    }

    public Result loginOpen(LoginOpenRequest loginRequest,String tenantId) throws ClientException {
        //读取登录请求中的Email
        String email = loginRequest.getEmail();
        //打印Email
        LOGGER.info("Login for email:{}", email);


        //根据Email读取用户类
        User stored = userService.getUserByEmailAndTenantId(email, "", tenantId);

        //账户不存在
        if (stored == null) {
            //账户未注册
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }

        //密码错误
         if (!stored.getHashedPassword().equals(PhosUtils.CalcSalt(loginRequest.getPassword(), stored.getSalt()))) {
            //密码错误
             return ResultCollection.getResult(NOT_LOGGED_IN.getCode());
        }
        //通过验证

        //生成随机种子
        String seed = PhosUtils.getUUID();

        HttpTokenDO tokenDO  = tokenService.generateMsgToken(seed, stored);

        LOGGER.info("Login success ! loginResponseDO:{}:", JSON.toJSONString(tokenDO));
        //返回给用户登录成功的response
        return new Result(tokenDO);

    }

    private boolean verifyLoginParam(LoginRequest loginRequest) {
        if (StringUtils.isEmpty(loginRequest.getCode()) && StringUtils.isEmpty(loginRequest.getPassword())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "用户验证码和密码不能同时为空");
            return false;
        }

        if (StringUtils.isEmpty(loginRequest.getPhone()) && StringUtils.isEmpty(loginRequest.getEmail())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "用户手机号和邮箱不能同时为空");
            return false;
        }

        if (loginRequest.getVerifyVersion() == 1 && StringUtils.isEmpty(loginRequest.getUserDeviceId())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备ID不能为空");
            return false;
        }
        return true;
    }

    /**
     * 验证登录次数
     *
     * @param loginRequest
     */
    public boolean virifyLoginTimes(LoginRequest loginRequest) {
        if (!needLoginStatistics(loginRequest)) {
            return true;
        }
        String loginKey = queryLoginKey(loginRequest);
        String key = USER_LOGIN_ERROR_COUNT.replace("{user}", loginKey);
        if (!redisService.hasDeviceOperationDo(key)) {
            //未登录失败过
            return true;
        }

        //登录失败次数
        int loginFailTimes = Integer.parseInt(redisService.get(key));

        // 验证是否需要返回引导忘记密码
        this.verifyGuideForgetPassword(loginRequest,loginFailTimes);

        if (loginFailTimes >= ERROR_RETRY_COUNT && loginRequest.getLoginType().equals(LoginTypeEnums.NORMAL.getCode())) {
            throw new BaseException(TOO_MAY_ERROR, "TOO_MAY_ERROR");
        }
        //登录失败发送验证码后再失败次数
        if (loginFailTimes >= (ERROR_RETRY_COUNT + ERROR_RETRY_COUNT) && !loginRequest.getLoginType().equals(LoginTypeEnums.NORMAL.getCode())) {
            throw new BaseException(RETRY_TOO_MAY_ERROR, "RETRY_TOO_MAY_ERROR");
        }
        return true;
    }

    /**
     * 验证密码失败超过次数是否需要返回引导忘记密码
     * @param loginRequest
     * @param loginFailTimes
     */
    private void verifyGuideForgetPassword(LoginRequest loginRequest,int loginFailTimes){
        if(!loginRequest.getGuideForgetPassword()){
            //不需要返回引导忘记密码
            return;
        }

        if(loginFailTimes < LOGIN_ERROR_GUIDE_PASSWORD_COUNT){
            //失败 超过3次才会返回引导忘记密码
            return;
        }

        throw new BaseException(TOO_MAY_ERROR_GUIDE_PASSWORD, "TOO_MAY_ERROR_GUIDE_PASSWORD");
    }

    /**
     * @return
     */
    private boolean isQuickLogin(LoginRequest loginRequest) {
        //是快捷登录条件，
        //1.是普通、历史登录方式
        //2.验证码不为空
        //3.密码为空
        //4.手机号不为空
        if ((LoginTypeEnums.NORMAL.getCode().equals(loginRequest.getLoginType()) || LoginTypeEnums.OLD.getCode().equals(loginRequest.getLoginType()))
                && !StringUtils.isEmpty(loginRequest.getCode())
                && StringUtils.isEmpty(loginRequest.getPassword())
                && !StringUtils.isEmpty(loginRequest.getPhone())) {
            //原登录方式、使用手机号登录且有验证码-快捷登录
            return true;
        }
        return false;
    }

    /**
     * 获取登录标记(判断之前登录失败次数)
     *
     * @param loginRequest
     * @return
     */
    private String queryLoginKey(LoginRequest loginRequest) {
        return StringUtils.isEmpty(loginRequest.getEmail()) ? loginRequest.getPhone() : loginRequest.getEmail();
    }

    private int checkCode(LoginRequest loginRequest) {
        if (!StringUtils.hasLength(loginRequest.getCode())) {
            //必须传递code场景已经校验过code必传
            LOGGER.info("无需验证code");
            return 0;
        }

        int result;
        switch (LoginTypeEnums.queryByCode(loginRequest.getLoginType())) {
            case OLD:
            case NORMAL:
                result = resultCode(loginRequest);
                break;
            case EMAILCODE:
            case PHONECODE:
                result = resultCodeWithRetry(loginRequest);
                break;
            default:
                result = INVALID_PARAMS.getCode();
                break;

        }
        return result;
    }

    /**
     * 正常登录的校验
     *
     * @param loginRequest
     * @return
     */
    private int resultCode(LoginRequest loginRequest) {
        if (this.isQuickLogin(loginRequest)) {
            String tenantId = loginRequest.getApp().getTenantId();
            //快捷登录
            String key = USER_PHONE_CODE.replace("{phone}", loginRequest.getPhone()).
                    replace("{tenantId}", tenantId);
            String storeCode = redisService.get(key);
            if (StringUtils.isEmpty(storeCode)) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "用户验证码获取为null");
                return -1036;
            }

            String countKey = USER_PHONE_CODE_COUNT.replace("{phone}", loginRequest.getPhone()).
                    replace("{tenantId}", tenantId);
            long nun = redisService.increNum(countKey);

            if (!storeCode.equals(loginRequest.getCode())) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "用户验证码不相等,storeCode:{},code:{}", storeCode, loginRequest.getCode());
                return -1031;
            }

            if (nun >= 6) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "用户验证码尝试次数太多,countKey:{},nun:{}", countKey, nun);
                return -1035;
            }
        }
        return 0;
    }

    /**
     * 校验验证码
     *
     * @param loginRequest
     * @return
     */
    private int resultCodeWithRetry(LoginRequest loginRequest) {
        if (StringUtils.isEmpty(loginRequest.getCode())) {
            LOGGER.info("login code empty,param:{}", loginRequest.toString());
            return INVALID_PARAMS.getCode();
        }

        String loginKey = queryLoginKey(loginRequest);
        //是否有验证码，没有表示验证码已过期
        String codeKey = USER_LOGIN_RETRY_CODE.replace("{user}", loginKey);
        if (!redisService.hasDeviceOperationDo(codeKey)) {
            return CONFIRM_EXPIRED.getCode();
        }
        //验证验证码是否正确
        String code = redisService.get(codeKey);
        if (loginRequest.getCode().equals(code)) {
            return SUCCESS.getCode();
        }
        //验证次数是否超过限制
        this.loginReTry(loginRequest);
        return PASSWORD_OR_CODE_ERROR.getCode();
    }

    /**
     * 用户实际注册
     *
     * @param registerRequest
     * @return
     */
    @Transactional
    public Result register(RegisterRequest registerRequest) {
        String email = registerRequest.getEmail();
        //打印Email
        String phone = registerRequest.getPhone();
        //打印Email
        LOGGER.info("Register for email:{},phone:{}", email, phone);
        if (StringUtils.isEmpty(email) && StringUtils.isEmpty(phone)) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "手机号、邮箱不能同时为空,email:{},phone:{}", email, phone);
            return Result.Error(NO_CONTACT, "ACCOUNT_NOT_REGISTERED");
        }

        if (registerRequest.getVerifyVersion() == 1 && StringUtils.isEmpty(registerRequest.getUserDeviceId())) {
            return Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        }

        //该邮箱、手机号已经被使用已经使用，不能再进行注册
        if (userService.getUserByEmailAndTenantId(email, phone, registerRequest.getApp().getTenantId()) != null) {
            return Result.Error(-1002, "ACCOUNT_IN_USE");
        }
        /*
         * 不合理
         */
        //用户未注册，验证密码合法性
        else if (PhosUtils.pswdInvalid(registerRequest.getPassword())) {
            //密码格式不合法
            return Result.Error(-1012, "INVALID_PASSWORD");
        }
        //注册新用户
        else {
            //验证邮箱
            return mailConfirmService.mailConfirm(registerRequest, email, null);
        }
    }

    /**
     * 用户实际重置密码
     *
     * @param registerRequest
     * @return
     */
    public Result resetPassword(RegisterRequest registerRequest) {
        String email = registerRequest.getEmail();
        String phone = registerRequest.getPhone();
        //打印Email
        LOGGER.info("Reset password for: " + email);

        User stored = userService.getUserByEmailAndTenantId(email, phone, registerRequest.getApp().getTenantId());
        //该账户未注册
        if (stored == null) {
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }
        //验证密码合法性
        else if (PhosUtils.pswdInvalid(registerRequest.getPassword())) {
            //密码格式不合法
            return Result.Error(-1012, "INVALID_PASSWORD");
        }
        // 验证是否需要强制重置密码
        else if (forceChangePwdService.newPwdNotChange(stored, registerRequest.getPassword())) {
            forceChangePwdService.sendEmail(ForceChangePwdService.EmailType.NEW_PWD_NOT_CHANGE, stored, registerRequest);
            return Result.Error(NOT_LOGGED_IN);
        }
        //重置密码
        else {
            //验证邮箱
            return mailConfirmService.mailConfirm(registerRequest, email, stored);
        }
    }

    /**
     * 注册成功
     *
     * @param registerRequest
     * @param storedConfirm
     * @param email
     * @return
     */
    public Result registerSuccess(RegisterRequest registerRequest, MailConfirmRequest storedConfirm, String
            email) {

        String countryNo = Objects.toString(storedConfirm.getCountryNo(), "");

        //生成盐
        String salt = PhosUtils.randomString(SALT_LENGTH);
        //生成随机种子
        String seed = PhosUtils.getUUID();
        User user = new User();
        user.setEmail(email);
        user.setPhone(registerRequest.getPhone());
        user.setSalt(salt);
        if (StringUtils.isEmpty(registerRequest.getName())) {
            user.setName(appUserConfig.getNamePreByTenantId(registerRequest.getApp().getTenantId()) + PhosUtils.randomNumString(9));
        } else {
            user.setName(registerRequest.getName());
        }
        user.setHashedPassword(PhosUtils.CalcSalt(registerRequest.getPassword(), salt));
        user.setTenantId(registerRequest.getApp().getTenantId());
        user.setLanguage(registerRequest.getLanguage());
        user.setCountryNo(countryNo);
        user.setType(UserType.REGISTER.getCode());
        user.setAppType(registerRequest.getApp().getAppType());
        user.setAppVersion(registerRequest.getApp().getVersion());
        user.setAppVersionName(registerRequest.getApp().getVersionName());
        //将用户数据写入数据库
        userService.insertUser(user);
        Boolean supportFreeLicense = Optional.ofNullable(registerRequest.getSupportFreeLicense()).orElse(false);

        if(Boolean.FALSE.equals(supportFreeLicense)){
            //添加免费套餐2
            userVipActivateService.createFreeUserTier(user.getId(), tenantFreeTierConfig.queryFreeTier(registerRequest.getApp().getTenantId()));
        }

        //登记邮箱为已使用
        mailConfirmService.setMailConfirmUsed(storedConfirm);

        //注册时默认该设备时可信任的
        if (registerRequest.getVerifyVersion() == 1) {
            UserTrustDevice userTrustDevice = new UserTrustDevice();
            userTrustDevice.setUserId(user.getId());
            userTrustDevice.setDeviceId(registerRequest.getUserDeviceId() == null ? "" : registerRequest.getUserDeviceId());
            userTrustDevice.setDeviceName(registerRequest.getUserDeviceName() == null ? "" : registerRequest.getUserDeviceName());
            userTrustDeviceService.createUserTrustDevice(userTrustDevice);
        }


        //发送 welcome email
        this.sendWelcomeEmail(user,registerRequest);
        //source 调用来源2-测试
        LoginResponseDO loginResponseDO = this.generateLoginOrRegisterResponse(user, seed, registerRequest,2);

        this.initUserSetting(user.getId(),supportFreeLicense);

        userAppScoreService.userAppScoreMoment(user.getId(), UserAppScoreMomentEnum.USER_CONTINUOUSUSE);
        //返回给用户注册成功的response
        return new Result(loginResponseDO);
    }

    /**
     * 注册后发送欢迎邮件
     * @param user
     * @param registerRequest
     */
    private void sendWelcomeEmail(User user,RegisterRequest registerRequest){
        if(TENANTID_NO_NEED_WELCOME.contains(registerRequest.getApp().getTenantId())){
            // 客户要求不发送欢迎邮件
            return;
        }
        if(emailPayConfig.needSendPayEmail(registerRequest.getApp().getTenantId())){
            // 发送付费邮件
            UserPayEmailDO userPayEmailDO = UserPayEmailDO.builder()
                    .userId(user.getId())
                    .sendEmailTypeEnums(SendEmailTypeEnums.SIGNIN_PAY_EMAIL)
                    .build();
            mqSender.sendToUserEmail(userPayEmailDO);
        }else{
            // 发送普通welcome 邮件
            // create or update zendesk user
            // create zendesk welcome ticket with tag "registration" "${tenantId}" "welcome_group_n"
            Map<String, Object> zendeskUser = new HashMap<>();
            zendeskUser.put("userId", user.getId());
            zendeskUser.put("email", ZendeskUtil.generateZendeskEmail(user));
            zendeskUser.put("tenantId", registerRequest.getApp().getTenantId());
            zendeskUser.put("name", user.getName());
            zendeskUser.put("language", user.getLanguage());
            zendeskUser.put("countryNo", user.getCountryNo());
            zendeskUser.put("appName", registerRequest.getApp().getAppName());
            if (zendeskClient != null) {
                zendeskClient.actionAsync(ZendeskActionEnums.USER_CREATE, zendeskUser);
                zendeskClient.actionAsync(ZendeskActionEnums.WELCOME_TICKET_CREATE, new HashMap(zendeskUser));
            }
        }
    }

    public Integer initUser(Integer fmtId, Integer startNo, Integer endNo) {
        User user = userService.queryUserById(fmtId);
        if (user == null) {
            LOGGER.warn("initUser user is null, not process");
            return 0;
        }

        UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(user.getId());
        if (userSettingsDO == null) {
            LOGGER.warn("initUser userSetting is null, not process");
            return 0;
        }
        int count = 0;
        String nameFmt = "aiTest%s";
        String emailFmt = "<EMAIL>";

        for (int i = startNo; i <= endNo; i++) {
            String email = String.format(emailFmt, i);
            User userByEmail = userService.getUserByEmailAndTenantId(email, "", user.getTenantId());
            if (userByEmail != null) {
                LOGGER.info("email is exist, not process, email :{}", email);
                continue;
            }

            user.setEmail(email);
            user.setName(String.format(nameFmt, i));
            user.setHashedPassword(PWD_TEST);
            user.setSalt(SALT_TEST);
            userService.insertUser(user);

            userSettingsDO.setUserId(user.getId());
            userSettingService.insertUserSetting(userSettingsDO);
            count++;
        }

        return count;
    }

    /**
     * 用户推送合并开关
     * @param userId
     */
    public void initUserSetting(Integer userId){
        this.initUserSetting(userId,false);
    }

    /**
     * 用户推送合并开关
     * @param userId
     */
    public void initUserSetting(Integer userId,Boolean supportFreeLicense){
        UserSettingsDO userSettingsDO = UserSettingsDO.builder()
                .userId(userId)
                .messageMergeSwitch(UserMessageMergeSwitchEnums.WAKE.getCode())
                .supportFreeLicense(supportFreeLicense ? 1 : 0)
                .build();
        userSettingService.insertUserSetting(userSettingsDO);
    }

    /**
     * 重置密码成功
     *
     * @param registerRequest
     * @param storedConfirm
     * @param stored
     * @param email
     * @return
     */
    public Result resetPasswordSuccess(RegisterRequest registerRequest, MailConfirmRequest storedConfirm, User
            stored, String email) {
        //生成盐
        String salt = PhosUtils.randomString(SALT_LENGTH);
        User user = new User();
        user.setId(stored.getId());
        user.setSalt(salt);
        user.setName(stored.getName());
        user.setHashedPassword(PhosUtils.CalcSalt(registerRequest.getPassword(), salt));
        user.setTenantId(registerRequest.getApp().getTenantId());
        final String reason = forceChangePwdService.getForceResetPwdFlag(stored.getId() + "") ? "1" : "0"; // 修改密码原因
        userService.insertUserPwdChangeLog(stored, reason);
        Integer res = userService.updateUserPassword(user);
        if (res > 0) {
            forceChangePwdService.deleteForceChangePwdFlag(stored.getId());
        }
        mailConfirmService.setMailConfirmUsed(storedConfirm);

        //重置密码成功后，清除用户历史登录错误次数统计
        this.cleanUserCode(stored);

        return Result.SqlOperationResult(res);
    }

    /**
     * 注册确认
     *
     * @param confirmRequest
     * @return
     */
    public Result registerConfirm(MailConfirmRequest confirmRequest) throws ClientException {
        String email = confirmRequest.getEmail();
        String phone = confirmRequest.getPhone();
        if (StringUtils.isEmpty(email) && StringUtils.isEmpty(phone)) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "手机号、邮箱不能同时为空,email:{},phone:{}", email, phone);
            return Result.Error(NO_CONTACT, "ACCOUNT_NOT_REGISTERED");
        }

        if (appInfoService.registerVerify(confirmRequest)) {
            LOGGER.info("迁移期内不能注册,email:{},phone:{},tenantId:{}", email, phone, confirmRequest.getApp().getTenantId());
            return new Result(USER_ACCOUNT_TRANSFER.getCode(), "USER_ACCOUNT_TRANSFER", appInfoService.queryDownloadUrl(confirmRequest.getApp().getTenantId(), confirmRequest.getApp().getAppType()));
        }

        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        //打印Email
        LOGGER.info("Register confirm for,email:{},phone:{} ", email, phone);
        //打印调用的方法名
        LOGGER.info("method name:" + methodName);
        //发送验证邮件
        return mailConfirmService.sendConfirmEmail(confirmRequest, methodName);
    }

    /**
     * 发送登录前操作验证码
     * @param confirmRequest
     * @return
     * @throws ClientException
     */
    public Result sendConfirmBeforLogin(MailConfirmRequest confirmRequest) throws ClientException {
        String email = confirmRequest.getEmail();
        String phone = confirmRequest.getPhone();
        if (StringUtils.isEmpty(email) && StringUtils.isEmpty(phone)) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "手机号、邮箱不能同时为空,email:{},phone:{}", email, phone);
            return Result.Error(NO_CONTACT, "ACCOUNT_NOT_REGISTERED");
        }

        //打印Email
        LOGGER.info("sendConfirmBeforLogin confirm for email {} phone {} ", email, phone);
        //发送验证邮件
        return mailConfirmService.sendConfirmEmail(confirmRequest, "");
    }

    /**
     * 重置确认
     *
     * @param confirmRequest
     * @return
     */
    public Result resetConfirm(MailConfirmRequest confirmRequest) throws ClientException {
        String email = confirmRequest.getEmail();
        String phone = confirmRequest.getPhone();
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        //打印Email
        LOGGER.info("Reset confirm for,email:{},phone:{}", email, phone);
        //打印调用的方法名
        LOGGER.info("method name:" + methodName);
        //发送验证邮件
        return mailConfirmService.sendConfirmEmail(confirmRequest, methodName);
    }

    /**
     * 用户登出
     *
     * @param userId
     * @param request
     * @return
     */
    public Result logout(Integer userId, AppRequestBase request) {


        //删除用户token相关信息
        tokenService.deleteUserToken(String.valueOf(userId));

        PushInfo pushInfo = PushInfo.builder()
                .userId(userId)
                .msgType(0)
                .msgToken("")
                .iosVoipToken("")
                .appType(request.getApp().getAppType())
                .bundleName(request.getApp().getBundle())
                .build();
        pushService.setPushInfo(pushInfo);
        return Result.Success();
    }

    /**
     * 生成用户注册或者登录成功后的response
     *
     * @param user
     * @param seed
     * @param registerOrLoginRequest
     * @param source 调用来源 1-登录 2-注册
     * @return
     */
    public LoginResponseDO generateLoginOrRegisterResponse(User user, String seed, LoginRequest
            registerOrLoginRequest, Integer source) {
        boolean invalidMsgInfo = StringUtils.isEmpty(registerOrLoginRequest.getMsgToken()) || null == registerOrLoginRequest.getMsgType();
        PushInfo pushInfo = PushInfo.builder()
                .userId(user.getId())
                .msgType(invalidMsgInfo ? 0 : registerOrLoginRequest.getMsgType())
                .msgToken(invalidMsgInfo ? "" : registerOrLoginRequest.getMsgToken())
                .iosVoipToken(invalidMsgInfo ? "" : registerOrLoginRequest.getIosVoipToken())
                .appType(registerOrLoginRequest.getApp().getAppType())
                .bundleName(registerOrLoginRequest.getApp().getBundle())
                .build();

        //更新用户信息
        pushService.setPushInfo(pushInfo);

        LOGGER.info("generateLoginOrRegisterResponse login verify {}", registerOrLoginRequest.getVerifyVersion());
        //测试账号不进行1fa验证 internal == 1为测试账号
        //兼容版本，只有带了verifyVersion==1的版本才需要走以下逻辑
        if(!Objects.equals(user.getInternal(), 1) && source == 1 && (registerOrLoginRequest.getVerifyVersion() == 1 && registerOrLoginRequest.getUserDeviceId() != null)) {
            //获取当前登录用户所有已经信任的设备
            List<UserTrustDevice> userTrustDevices = userTrustDeviceService.queryUserTrustDeviceByUserId(user.getId());
            List<String> deviceIdList = userTrustDevices.stream()
                    .filter(Objects::nonNull)
                    .map(UserTrustDevice::getDeviceId)
                    .filter(e -> e != null && !e.trim().isEmpty())
                    .collect(Collectors.toList());
            LOGGER.info("deviceIdList:{}", JSON.toJSONString(deviceIdList));

            if (CollectionUtils.isEmpty(deviceIdList) || !deviceIdList.contains(registerOrLoginRequest.getUserDeviceId())) {
                final HttpTokenDO httpTokenDO = tokenService.generateMsgToken(PrefixConstants.TEMP_TOKEN_PREFIX,seed, user);
                LoginResponseDO responseDO = userService.buildLoginResponseDO(user, httpTokenDO);
                //目前临时方案默认用主邮箱账号 1 - 主邮箱认证，2-2FA邮箱，3-2FA电话
                responseDO.setAuthType(1);
                responseDO.setNeedAuthType(1);
                responseDO.setAuthInfo(AuthInfo.builder().email(user.getEmail()).build());
                responseDO.setToken(httpTokenDO);
                tokenService.setUserSeed(PrefixConstants.TEMP_TOKEN_PREFIX + user.getId(), seed);
                return  responseDO;
            }
        }

        final HttpTokenDO httpTokenDO = tokenService.generateMsgToken(seed, user);
        final LoginResponseDO responseDO = userService.buildLoginResponseDO(user, httpTokenDO);
        tokenService.setUserSeed(String.valueOf(user.getId()), seed);
        return responseDO;
    }

    public Result quickLoginCode(LoginRequest loginRequest) throws ClientException {
        if (StringUtils.isEmpty(loginRequest.getPhone())) {
            return Result.Error(INVALID_PHONE, "phone not empty");
        }

        if (!PhosUtils.phoneInvalid(loginRequest.getPhone())) {
            //打印手机号
            LOGGER.info("INVALID_PHONE: " + loginRequest.getPhone());
            //邮箱格式不合法
            return Result.Error(INVALID_PHONE, "INVALID_PHONE");
        }
        //根据手机号得到用户
        User storedUser = userService.getUserByEmailAndTenantId("", loginRequest.getPhone(), loginRequest.getApp().getTenantId());
        //重置
        if (storedUser == null) {
            //用户不存在
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }

        String code = PhosUtils.randomNumString(6);
        //需要区分不同公司的APP用户
        String key = USER_PHONE_CODE.replace("{phone}", loginRequest.getPhone()).
                replace("{tenantId}", loginRequest.getApp().getTenantId());
        LOGGER.info("用户快捷登录,key:{},code:{}", key, code);
        redisService.set(key, code, USER_CODE_EXPIRE);
        String countKey = USER_PHONE_CODE_COUNT.replace("{phone}", loginRequest.getPhone()).
                replace("{tenantId}", loginRequest.getApp().getTenantId());
        redisService.set(countKey, "0", USER_CODE_EXPIRE);
        boolean result = messageUtil.sendCodePhone(loginRequest.getPhone(), code, loginRequest.getLanguage(), loginRequest.getApp().getTenantId());
        return result ? Result.Success() : Result.Failure("send error");
    }

    /**
     * 错误次数限制
     *
     * @param loginRequest
     * @return
     */
    private Result userRetryLogin(LoginRequest loginRequest) throws ClientException {
        if (!needLoginStatistics(loginRequest)) {
            LOGGER.info("历史版本、非vicoo 无需发送验证码,{}", loginRequest.toString());
            return Result.Error(-1021, "WRONG_PASSWORD");
        }
        Result result;
        String loginKey = queryLoginKey(loginRequest);
        String userLoginErrorCountKey = USER_LOGIN_ERROR_COUNT.replace("{user}", loginKey);
        String value = redisService.get(userLoginErrorCountKey);
        long count = Long.valueOf(StringUtils.isEmpty(value) ? "0" : value);

        switch (LoginTypeEnums.queryByCode(loginRequest.getLoginType())) {
            case NORMAL:
                //普通登录校验
                result = nomalLoginError(count, userLoginErrorCountKey, loginRequest);
                break;
            case EMAILCODE:
            case PHONECODE:
                //加验证码登录校验
                result = retryLoginError(count, userLoginErrorCountKey);
                break;
            default:
                result = Result.Error(-1021, "WRONG_PASSWORD");
                break;
        }

        //密码错误
        return result;
    }

    /**
     * 判断是否需要登录错误次数限制
     *
     * @param loginRequest
     * @return
     */
    private boolean needLoginStatistics(LoginRequest loginRequest) {
        if (LoginTypeEnums.OLD.getCode().equals(loginRequest.getLoginType())) {
            //三种情况不走登录验证次数的逻辑：历史版本、
            LOGGER.info("用户登录无需验证失败次数:历史版本");
            return false;
        }
        if (!TENANTID_VICOO.equals(loginRequest.getApp().getTenantId()) &&
                !TENANTID_GUARD.equals(loginRequest.getApp().getTenantId())) {
            //三种情况不走登录验证次数的逻辑：客户APP
            LOGGER.info("用户登录无需验证失败次数:客户APP");
            return false;
        }

        if (isQuickLogin(loginRequest)) {
            //三种情况不走登录验证次数的逻辑：手机号快捷登录---其实需要要争，后续调整逻辑
            LOGGER.info("用户登录无需验证失败次数:快捷登录");
            return false;
        }


        return true;
    }

    /**
     * 普通登录密码错误返回
     *
     * @param errorCount
     * @param key
     * @return
     */
    private Result nomalLoginError(long errorCount, String key, LoginRequest loginRequest) throws
            ClientException {
        if (errorCount == 0) {
            //设置缓存时间
            redisService.set(key, "0", USER_CODE_EXPIRE);
        }
        //错误次数超过5次，需要验证码登录
        if (errorCount >= ERROR_RETRY_COUNT) {
            return Result.Error(TOO_MAY_ERROR, "TOO_MAY_ERROR");
        }
        //累加错误次数
        redisService.increNum(key);
        //只默认发一次验证码
        if (errorCount == (ERROR_RETRY_COUNT - 1)) {
            sendCode(loginRequest, true);
        }
        return Result.Error(-1021, "WRONG_PASSWORD");
    }

    /**
     * 使用密码+验证码登录密码错误返回
     *
     * @param errorCount
     * @param key
     * @return
     */
    private Result retryLoginError(long errorCount, String key) throws ClientException {
        //密码+验证码错误次数超过
        if (errorCount >= (ERROR_RETRY_COUNT + ERROR_RETRY_COUNT)) {
            return Result.Error(RETRY_TOO_MAY_ERROR, "RETRY_TOO_MAY_ERROR");
        }

        redisService.increNum(key);
        return Result.Error(PASSWORD_OR_CODE_ERROR, "PASSWORD_OR_CODE_ERROR");
    }


    private void sendCode(LoginRequest loginRequest, boolean verify) throws ClientException {
        String loginKey = queryLoginKey(loginRequest);
        String userLoginKeyKey = USER_LOGIN_RETRY_CODE.replace("{user}", loginKey);
        String userLoginExpireKey = USER_LOGIN_CODE_SEND_EXPIRE.replace("{user}", loginKey);
        if (verify) {
            if (redisService.hasDeviceOperationDo(userLoginExpireKey)) {
                LOGGER.info("sendCode 120s 内不重新发送,email:{},phone:{}", loginRequest.getEmail(), loginRequest.getPhone());
                return;
            }
        }

        String code = PhosUtils.randomNumString(6);
        //redis 维护  验证码、发送标记
        redisService.set(userLoginKeyKey, code, USER_CODE_EXPIRE);
        redisService.set(userLoginExpireKey, code, USER_LOGIN_CODE_SEND_EXPIRE_TIME);

        if (!StringUtils.isEmpty(loginRequest.getEmail())) {
            mailConfirmService.sendEmail(loginRequest.getEmail(), null, code, loginRequest.getLanguage(), loginRequest.getApp().getTenantId(), SendEmailTypeEnums.SIGNIN, loginRequest.getApp());
        } else {
            mailConfirmService.sendEmail(null, loginRequest.getPhone(), code, loginRequest.getLanguage(), loginRequest.getApp().getTenantId(), SendEmailTypeEnums.SIGNIN, loginRequest.getApp());
        }
    }

    /**
     * 登录成功后清除用户验证码的缓存
     */
    private void cleanUserCode(User user) {
        LOGGER.info("clean user cache,userId:{},user:{}", user.getId(), user.toString());
        //清除邮箱对应的缓存
        if (!StringUtils.isEmpty(user.getEmail())) {
            //登录错误次数
            cleanLoginErrorCount(user.getEmail());
        }
        //清除手机号对应的缓存记录
        if (!StringUtils.isEmpty(user.getPhone())) {
            //登录错误次数
            cleanLoginErrorCount(user.getPhone());
            //快捷登录错误次数
            cleanQuickLoginErrorCount(user.getPhone(), user.getTenantId());
        }
    }

    /**
     * 将记录的错误统计次数删除
     *
     * @param loginKey
     */
    private void cleanLoginErrorCount(String loginKey) {
        //登录后清除登录错误次数
        String keyErrorCount = USER_LOGIN_ERROR_COUNT.replace("{user}", loginKey);
        if (redisService.hasDeviceOperationDo(keyErrorCount)) {
            redisService.dropDeviceOperationDo(keyErrorCount);
        }

        //登录后清除登录错误验证码
        String keyRetryCode = USER_LOGIN_RETRY_CODE.replace("{user}", loginKey);
        if (redisService.hasDeviceOperationDo(keyRetryCode)) {
            redisService.dropDeviceOperationDo(keyRetryCode);
        }

        //登录后清除验证码2分钟限制
        String userLoginExpireKey = USER_LOGIN_CODE_SEND_EXPIRE.replace("{user}", loginKey);
        if (redisService.hasDeviceOperationDo(userLoginExpireKey)) {
            redisService.dropDeviceOperationDo(userLoginExpireKey);
        }
    }

    /**
     * 将快捷登录记录的错误统计次数删除
     */
    private void cleanQuickLoginErrorCount(String phone, String tenantId) {
        //快捷登录key
        String key = USER_PHONE_CODE.replace("{phone}", phone).
                replace("{tenantId}", tenantId);
        String countKey = USER_PHONE_CODE_COUNT.replace("{phone}", phone).
                replace("{tenantId}", tenantId);
        if (redisService.hasDeviceOperationDo(key)) {
            redisService.dropDeviceOperationDo(key);
        }
        if (redisService.hasDeviceOperationDo(countKey)) {
            redisService.dropDeviceOperationDo(countKey);
        }
    }


    public Result retryLoginCode(LoginRequest loginRequest) throws ClientException {
        if (loginRequest.getLoginType().equals(LoginTypeEnums.NORMAL.getCode())) {
            return Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        }
        if (StringUtils.isEmpty(loginRequest.getPhone()) && StringUtils.isEmpty(loginRequest.getEmail())) {
            return Result.Error(INVALID_PHONE, "手机、邮箱不能同时为空");
        }

        if (!StringUtils.isEmpty(loginRequest.getPhone()) && !PhosUtils.phoneInvalid(loginRequest.getPhone())) {
            //打印手机号
            LOGGER.info("INVALID_PHONE: " + loginRequest.getPhone());
            //格式不合法
            return Result.Error(INVALID_PHONE, "INVALID_PHONE");
        }

        if (!StringUtils.isEmpty(loginRequest.getEmail()) && PhosUtils.mailInvalid(loginRequest.getEmail())) {
            //打印邮箱
            LOGGER.info("INVALID_email: " + loginRequest.getEmail());
            //格式不合法
            return Result.Error(INVALID_PHONE, "邮箱格式不正确");
        }

        //验证失败次数是否超过限制
        this.loginReTry(loginRequest);

        //根据手机号得到用户
        User storedUser = userService.getUserByEmailAndTenantId(loginRequest.getEmail(), loginRequest.getPhone(), loginRequest.getApp().getTenantId());
        //重置
        if (storedUser == null) {
            //用户不存在
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }

        sendCode(loginRequest, true);
        return Result.Success();
    }

    /**
     * 验证登录次数是否超过限制
     *
     * @param loginRequest
     */
    private void loginReTry(LoginRequest loginRequest) {
        String loginKey = queryLoginKey(loginRequest);
        //重试失败次数,验证是否超过限制
        String codeKeyRetryErrorCount = USER_LOGIN_ERROR_COUNT.replace("{user}", loginKey);
        long count = redisService.increNum(codeKeyRetryErrorCount);
        if (count > (ERROR_RETRY_COUNT + ERROR_RETRY_COUNT)) {
            throw new BaseException(CONFIRM_BEYOND_LIMIT, "CONFIRM_BEYOND_LIMIT");
        }
    }
}
