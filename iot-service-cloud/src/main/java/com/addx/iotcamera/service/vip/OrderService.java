package com.addx.iotcamera.service.vip;

import com.addx.iotcamera.bean.app.vip.GoogleHistoryRequest;
import com.addx.iotcamera.bean.db.PaymentFlow;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.db.pay.OrderProductDo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.serve.ServeConfig;
import com.addx.iotcamera.dao.pay.IOrderDAO;
import com.addx.iotcamera.dao.pay.IOrderProductDAO;
import com.addx.iotcamera.dao.pay.IPaymentFlowDAO;
import com.addx.iotcamera.enums.OrderSubTypeEnums;
import com.addx.iotcamera.enums.pay.GoogleHistoryNotifyEnums;
import com.addx.iotcamera.service.ProductService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.util.DateUtils;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class OrderService {
    @Autowired
    private IOrderDAO iOrderDAO;

    @Resource
    private IOrderProductDAO iOrderProductDAO;

    @Resource
    private ProductService productService;

    @Resource
    @Lazy
    private ApplePayService applePayService;

    @Resource
    private IPaymentFlowDAO iPaymentFlowDAO;

    @Resource
    @Lazy
    private UserService userService;

    @Resource
    private ServeConfig serveConfig;

    /**
     * 是否订阅订单
     */
    @Cacheable(value = "orderSub", key = "#orderId", unless = "#result==null")
    public boolean verifyOrderSub(Long orderId) {
        if (orderId.equals(0)) {
            return false;
        }

        OrderDO orderDO = iOrderDAO.queryByOrderId(orderId);
        if (orderDO == null) {
            return false;
        }
        return orderDO.getSubType().equals(OrderSubTypeEnums.SUBSCRIBE.getCode());
    }

    /**
     * 查询订单信息-包含定制验证信息
     * @param orderId
     * @return
     */
    public OrderDO queryOrderInfoById(Long orderId) {
        return iOrderDAO.queryByOrderId(orderId);
    }

    public OrderDO queryOrderInfoByIdReadOnly(Long orderId) {
        return iOrderDAO.queryByOrderIdReadOnly(orderId);
    }

    // camer.order表的orderType不会变化，建立缓存
    private ConcurrentHashMap<Long, Integer> orderId2Type = new ConcurrentHashMap<>();

    public Integer queryOrderTypeByOrderId(Long orderId) {
        return orderId2Type.computeIfAbsent(orderId, iOrderDAO::queryOrderTypeByOrderId);
    }

    public OrderProductDo queryOrderProductDO(Long orderId){
        return iOrderProductDAO.selectOrderProductByOrderId(orderId);
    }

    /**
     * 批量获取订单与商品的关系
     * @param orderIds
     * @return
     */
    public Map<Long, ProductDO> queryProductBatch(List<Long> orderIds){
        if(CollectionUtils.isEmpty(orderIds)){
            return Maps.newHashMap();
        }
        List<OrderProductDo> orderProductDoList = iOrderProductDAO.selectOrderProductByOrderIdBatch(orderIds);
        if(CollectionUtils.isEmpty(orderIds)){
            return Maps.newHashMap();
        }
        Map<Long,ProductDO> productDOMap = Maps.newHashMap();
        for(OrderProductDo orderProductDo : orderProductDoList){
            if(productDOMap.containsKey(orderProductDo.getOrderId())){
                continue;
            }
            productDOMap.put(orderProductDo.getOrderId(),productService.queryProductById(orderProductDo.getProductId()));
        }
        return productDOMap;
    }


    public List<OrderDO> queryUserOrderList(Integer userId){
        return iOrderDAO.queryUserOrderList(userId);
    }

    /**
     * 查询用户自己购买的订单数
     * @param orderIds
     * @return
     */
    public int userOrderPaymentByUser(List<Long> orderIds){
        if(CollectionUtils.isEmpty(orderIds)){
            return 0;
        }
        return iOrderDAO.queryUserPaymentOrderCount(orderIds);
    }

    public OrderDO querySubOrder(String tradeNo){
        if(!StringUtils.hasLength(tradeNo)){
            return null;
        }
        return iOrderDAO.queryBytradeNo(tradeNo);
    }

    public List<OrderDO> queryBytradeNoBatch(String tradeNo){
        if(!StringUtils.hasLength(tradeNo)){
            return Lists.newArrayList();
        }
        return iOrderDAO.queryBytradeNoBatch(tradeNo);
    }

    /**
     * 更新订单取消时间
     * @param orderDO
     */
    public void updateOrderCancel(OrderDO orderDO){
        iOrderDAO.updateOrderCancelTime(orderDO);
    }


    /**
     * 根据订单号查询订单
     * @param orderSn
     * @return
     */
    public OrderDO queryOrderDOByOrderSn(String orderSn){
        return iOrderDAO.queryByOrderSn(orderSn);
    }

    /**
     * 更新谷歌订单历史事件
     * @param request
     * @return
     */
    public boolean notifyGoogleOrder(GoogleHistoryRequest request){

        OrderDO orderDO = null;

        Integer expiredTime = 0;
        for(GoogleHistoryRequest.GoogleOrderNotifyEven notifyType : request.getNotifyTypeList()){
            GoogleHistoryNotifyEnums enums = GoogleHistoryNotifyEnums.codeOf(notifyType.getNotifyType());

            PaymentFlow paymentFlow = iPaymentFlowDAO.queryPaymentFlow(request.getTransactionId());
            if(paymentFlow == null){

                int indexOrder = request.getTransactionId().lastIndexOf("..");
                if(indexOrder < 1){
                    return false;
                }
                String tradeNo = request.getTransactionId().substring(0,indexOrder);
                orderDO = this.querySubOrder(tradeNo);
                if(orderDO == null){
                    return false;
                }
                // 谷歌订单取消、退款是顺序往下递增一个订单，实际在数据库中不存在
                paymentFlow = iPaymentFlowDAO.queryPaymentFlowLast(tradeNo);
                if(paymentFlow == null){
                    return false;
                }
             }

            orderDO = orderDO == null ? this.queryOrderDOByOrderSn(paymentFlow.getOutTradeNo()) : orderDO;
            if(orderDO == null){
                return false;
            }

            switch (enums){
                case FREE_TRIAL:
                    paymentFlow.setFreeTrial(1);
                    break;
                case REFUND:
                    paymentFlow.setRefund(1);
                    paymentFlow.setRefundReason(notifyType.getNotifyReason());
                    paymentFlow.setRefundTime(notifyType.getNotifyTime());

                    User user = userService.queryUserById(orderDO.getUserId());
                    if(user != null){
                        paymentFlow.setRefundAppVersionName(user.getAppVersionName());

                    }
                    paymentFlow.setRefundServeVersion(serveConfig.getServeReleaseTag());
                    break;
                case CANCEL_AUTO_RENEW:
                    orderDO.setOrderCancel(1);
                    orderDO.setOrderCancelReason(notifyType.getNotifyReason());
                    orderDO.setOrderCancelTime(notifyType.getNotifyTime());
                    iOrderDAO.updateOrderCancelTime(orderDO);

                    break;
            }
            iPaymentFlowDAO.updateRefundOrderInfo(paymentFlow);
        }
        return true;
    }

    /**
     * 批量查询订单
     * @param orderIds
     * @return
     */
    public List<OrderDO> queryOrderBatch(List<Long> orderIds){
        if(CollectionUtils.isEmpty(orderIds)){
            return Lists.newArrayList();
        }

        return iOrderDAO.queryOrderBatch(orderIds);
    }


    /**
     * 验证订单是否还在续订中
     * @param orderId
     * @return
     */
    public boolean verifyOrderSubStatus(Long orderId){
        if(orderId == null){
            return false;
        }
        OrderDO orderDO = this.queryOrderInfoById(orderId);
        return orderDO != null && orderDO.getSubType().equals(1) && orderDO.getOrderCancel().equals(0);
    }
}
