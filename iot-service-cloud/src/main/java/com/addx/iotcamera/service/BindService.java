package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.alexa.result.DeviceEvent;
import com.addx.iotcamera.bean.app.device.home.DeviceHomeModeRequest;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.FirmwareTableDO;
import com.addx.iotcamera.bean.db.device.DeviceBindCode;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.db.user.UserShowFreeLicenseDO;
import com.addx.iotcamera.bean.device.CoolDownDO;
import com.addx.iotcamera.bean.device.DeviceConnectReport;
import com.addx.iotcamera.bean.device.DeviceModelSettingDO;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.device.model.DeviceModelVolumeDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.DeviceBindStep;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.mqtt.Request.MqttDeviceBindRequest;
import com.addx.iotcamera.config.DeviceAntiflickerConfig;
import com.addx.iotcamera.config.apollo.DeviceLanguageConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.device.*;
import com.addx.iotcamera.config.opanapi.PaasTenantConfig;
import com.addx.iotcamera.constants.DeviceReportKeyConstants;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.IShareDAO;
import com.addx.iotcamera.dao.device.IDeviceBindErrorDAO;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.SetParameterRequest;
import com.addx.iotcamera.publishers.vernemq.requests.SetRetainParamRequest;
import com.addx.iotcamera.publishers.vernemq.responses.MqttDeviceBindResponse;
import com.addx.iotcamera.publishers.vernemq.responses.MqttResponseCode;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.home.DeviceHomeModeService;
import com.addx.iotcamera.service.device.home.DeviceHomeService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.service.device.model.DeviceModelTenantService;
import com.addx.iotcamera.service.device.model.DeviceModelVoiceService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.NodeMatcherAgency;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.user.UserShowFreeLicenseService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.MapUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.addx.iotcamera.util.QRcodeUtil;
import com.addx.tracking.config.snowplow.SnowPlowManager;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.zxing.WriterException;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.thingmodel.ThingModel;
import org.addx.iot.common.thingmodel.ThingModelConfig;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.core.IExtensionService;
import org.apache.commons.lang.RandomStringUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.*;
import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.service.deviceplatform.alexa.safemo.AlexaSafemoLiveService.getRealCxSerialNumber;
import static com.addx.iotcamera.service.security_mode.biz.SecurityModeBiz.NO_MODE_ID;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_UNACTIVATED;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.vo.Result.successFlag;

@Component
public class BindService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BindService.class);
    private static final String PACKAGE_FACTORY = "factory";
    private static final String API_QUERY_SN = "querysn";
    private static final int DEFAULT_ANTIFLICKER = 60;
    public static final int DEVICE_CONNECT_TIMEOUT = 3600;

    private static final int defaultBindType = 0;
    private static final int RADIX = 16;

    public static final String AFTER_BINDING_POPUP_KEY = "deviceCache::afterBindingPopup:";

    public static final Integer MANUFACTURE_CONFIRMED = 1;


    @Autowired
    private ActivityZoneService activityZoneService;

    @Autowired
    private IDeviceDAO deviceDAO;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private VideoSearchService videoSearchService;

    @Autowired
    private DeviceRelationshipService deviceRelationshipService;

    @Autowired
    private IShareDAO shareDAO;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private DeviceAttributeService deviceAttributeService;

    @Autowired
    private FirmwareService firmwareService;

    @Autowired
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Autowired
    private UserVipService userVipService;
    @Autowired
    private VipService vipService;

    @Autowired
    private IDeviceBindErrorDAO iDeviceBindErrorDAO;

    @Autowired
    private ReportLogService reportLogService;

    @Autowired
    private LocationInfoService locationInfoService;

    @Autowired
    private DeviceSettingService deviceSettingService;

    @Value("${codebind.node}")
    private String profilesActive;

    @Autowired
    private DeviceAntiflickerConfig deviceAntiflickerConfig;

    @Autowired
    private GeoIpService geoIpService;

    @Autowired
    private DeviceLanguageConfig deviceLanguageConfig;
    @Autowired
    private UserService userService;

    @Autowired
    private DeviceNameConfig deviceNameConfig;

    @Autowired
    private RotationPointService rotationPointService;

    @Autowired
    private DeviceModelTenantService deviceModelTenantService;

    @Resource
    private DeviceHomeService deviceHomeService;

    @Resource
    private DeviceManualService deviceManualService;

    @Autowired
    private NodeMatcherAgency nodeMatcherAgency;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DeviceBindCodeService deviceBindCodeService;

    @Autowired
    private DeviceAiSettingsService deviceAiSettingsService;

    @Autowired
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceStatusService deviceStatusService;
    @Autowired
    private DeviceModelVoiceService deviceModelVoiceService;
    @Autowired
    private AppAccountConfig appAccountConfig;
    @Autowired
    private OpenApiWebhookService openApiWebhookService;
    @Autowired
    private PaasTenantConfig paasTenantConfig;

    @Resource
    private DeviceModelConfigService deviceModelConfigService;

    @Autowired(required = false)
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Autowired
    private DeviceCameraNameConfig deviceCameraNameConfig;

    @Resource
    private DeviceSettingConfig deviceSettingConfig;

    @Autowired
    private DeviceDisplayModelNoCorrectConfig deviceDisplayModelNoCorrectConfig;


    private static final String deviceZhLanguage = "zh";
    private static final String deviceCnLanguage = "cn";
    private static final String deviceEnLanguage = "en";

    @Autowired
    private DeviceModelService deviceModelService;

    @Resource
    private UserAppScoreService userAppScoreService;

    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private DeviceShareService deviceShareService;

    @Autowired @Lazy
    private ThingModelConfig thingModelConfig;
    @Resource
    @Lazy
    private VideoStoreService videoStoreService;

    @Autowired
    IExtensionService extensionService;
    @Autowired
    private DeviceModeService deviceModeService;

    @Resource
    @Lazy
    private DeviceHomeModeService deviceHomeModeService;
    @Autowired
    private DeviceDormancyPlanConfig deviceDormancyPlanConfig;

    @Resource
    @Lazy
    private UserSettingService userSettingService;

    @Resource
    @Lazy
    private UserShowFreeLicenseService userShowFreeLicenseService;


    public Result deactivateOwnDeviceBySerialNumber(String serialNumber, Integer userId) {
        UserRoleService.UserRoles userRoles = userRoleService.queryUserRolesBySn(serialNumber, false);
        Integer adminId = userRoles.getAdminId();
        if (adminId == null || !adminId.equals(userId) ) {
            LOGGER.info("用户无权限操作此设备,{}", serialNumber);
            return Result.Error(DEVICE_UNACTIVATED);
        }
        Result result = deactivateDevice(serialNumber, userRoles, adminId);
        analyticDeleteDeviceResult(serialNumber, userRoles.getAdminId(), DELETE_DEVICE_REASON_MANAGER, result);
        return result;
    }

    public Result deactivateDeviceBySerialNumber(String serialNumber, int reason) {
        UserRoleService.UserRoles userRoles = userRoleService.queryUserRolesBySn(serialNumber, false);
        Integer adminId = userRoles.getAdminId();
        if (adminId == null) {
            LOGGER.info("用户无权限操作此设备,{}", serialNumber);
            return Result.Error(DEVICE_UNACTIVATED);
        }
        Result result = deactivateDevice(serialNumber, userRoles, adminId);
        analyticDeleteDeviceResult(serialNumber, userRoles.getAdminId(), reason, result);
        return result;
    }


    private Result deactivateDevice(String serialNumber, UserRoleService.UserRoles userRoles, Integer adminId) {
        //清除home mode数据
        this.cleanDeviceHomeMode(adminId,serialNumber);

        // 清除用户角色表
        userRoleService.cleanUserRole(serialNumber, adminId);
        videoSearchService.clearSearchOptionCache(userRoles.getUserIds());
        userTierDeviceService.onRemoveUserDevice(adminId, serialNumber);

        // 现在解绑设备后录像不删除
        // 此处注释代码仅供参考，不应当实际执行
        // -- libraryService.deleteDeviceLibrariesBySerialNumber(serialNumber);

        // 清空Activity Zone
        activityZoneService.deleteActivityZoneBySerialNumber(serialNumber);

        //将解绑设备的分享记录取消
        deviceShareService.cancelSerialNumberShare(serialNumber);
        // 重置设备绑定信息
        Integer res = deviceInfoService.deactivateDevice(serialNumber);

        DeviceDO storedDevice = deviceService.getAllDeviceInfo(serialNumber);

        try {
            //删除该设备人型检测设置
            notificationService.deleteMessageNotificationSettings(serialNumber, adminId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备删除设置,serialNumber:{}", serialNumber);
        }

        if (res < 1) {
            return Result.Error(-101, "Failed to deactivate device");
        }

        videoStoreService.allLocalVideoDeletedAsync(serialNumber, adminId); // 解绑设备时，异步清空cxs视频

        //send device deactivate event
        if (devicePlatformEventPublisher != null) {
            Map<String, Object> deviceEventParamMap = new HashMap<>();
            deviceEventParamMap.put("userId", String.valueOf(adminId));
            deviceEventParamMap.put("serialNumber", serialNumber);
            devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DEVICE_DEACTIVATE, deviceEventParamMap);
        }

        LOGGER.info(String.format("Deactivated device %s successfully.", serialNumber));
        openApiWebhookService.callWebhookForDeviceUnbind(adminId, serialNumber, userRoles.getShareUserIds());
        return Result.Success();
    }

    public Result bindDeviceFromApp(Integer userId, BindOperationRequest request, IpInfo ipInfo) {
        String operationId = this.bindOperation(userId, request, ipInfo);
        if (request.getApp() != null && AppConstants.TENANTID_SAFEMO.equals(request.getApp().getTenantId())) {
            DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureByUserSn(request.getUserSn());
            if (deviceManufactureTableDO != null)
                extensionService.clearExtensionEnabledCameraWhenBinding(deviceManufactureTableDO.getSerialNumber());
        }
        return Result.KVResult("operationId", operationId);
    }

    private static String createBindOperationId(Integer userId) {
        final String zeros = "000000";
        String prefix = TraceIdHelper.toAlphanumeric(userId); // max: '1BCkl2'(<=6位)
        String postfix = RandomStringUtils.randomAlphanumeric(10); // =16位
        return zeros.substring(6 - prefix.length()) + postfix; // =16位
    }

    private String bindOperation(Integer userId, BindOperationRequest request, IpInfo ipInfo) {
        Integer locationId = request.getLocationId();
        Long homeId = deviceHomeService.initHomeId(userId,request.getLanguage(), request.getApp().getTenantId());
        // 绑定流程重构后，绑定请求中的locationId设置为-1来表示不合法的情况。没有新增接口的原因，是我实在不想再和App浪费口舌在和这种争论上了
        if (locationId == null || locationId == -1) {
            locationId = locationInfoService.queryDefaultLocationId(userId,request,homeId);
        }


        //获取绑定码生成时间
        String bindCodeTimestamp = StringUtils.isEmpty(request.getBindCode()) ? "" : redisService.get(DeviceReportKeyConstants.DEVICE_BIND_CODE_KEY.replace("{bindCode}", request.getBindCode()));
        BindOperationTb operationTb = BindOperationTb.builder()
                .userId(userId)
                .locationId(locationId)
                .homeId(homeId)
                .deviceLanguage(getAppDeviceLanguage(request.getLanguage()))
                .timeZone(request.getTimeZone())
                .ip(ipInfo.getIp())
                .bindCode(request.getBindCode())
                .tenantId(request.getApp().getTenantId())
                .appType(request.getApp().getAppType())
                .version(request.getApp().getVersion().toString())
                .bindType(request.getType())
                .bindContentSrc(request.getBindContentSrc())
                .deviceNetType(request.getDeviceNetType())
                .bindCodeTimestamp(StringUtils.isEmpty(bindCodeTimestamp) ? 0 : Long.valueOf(bindCodeTimestamp))
                .build();

        LOGGER.info(String.format("Binding device for user %d to location: %d", userId, locationId));

        // 更新绑定操作信息表
        for (int i = 0; true; i++) {
            try {
                operationTb.setOperationId(createBindOperationId(userId));
                deviceDAO.initBindOperation(operationTb);
                break;
            } catch (DuplicateKeyException e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "bindOperationId duplicated! traceId={},i={}", operationTb.getOperationId(), i, e);
                if (i >= 2) throw new RuntimeException("bindOperationId duplicated!", e);
            }
        }
        reportLogService.sysReportBind(REPORT_TYPE_GENERATE_BIND_OPERATION,
                MapUtil.builder()
                        .put("bindOperationId", operationTb.getOperationId())
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("bindCode", request.getBindCode())
                        .put("tenantId", request.getApp().getTenantId())
                        .build()
        );
        return operationTb.getOperationId();
    }

    private String getAppDeviceLanguage(String language) {
        //设备不支持的默认显示英语
        String deviceLanguage = !deviceLanguageConfig.getConfig().containsKey(language) ? deviceEnLanguage : language;

        //设备语言 zh 转 cn
        return deviceZhLanguage.equals(deviceLanguage) ? deviceCnLanguage : deviceLanguage;
    }


    public Result checkBindOperation(String bindOperationId, int userId, IpInfo ipInfo) {

        BindOperationTb operationTb = deviceDAO.selectOperation(bindOperationId);

        reportLogService.sysReportBind(REPORT_TYPE_CHECK_BIND_OPERATION,
                MapUtil.builder()
                        .put("bindOperationId", bindOperationId)
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("answered", operationTb.getAnswered())
                        .put("statusCode", operationTb.getStatusCode())
                        .put("tenantId", operationTb.getTenantId())
                        .build()
        );

        if (operationTb.getAnswered() == 0) {
            return ResultCollection.REQUEST_NOT_HANDLED.getResult();
        } else if (operationTb.getStatusCode() != 0) {
            return ResultCollection.getResult(operationTb.getStatusCode());
        } else {
            return Result.KVResult("serialNumber", operationTb.getSerialNumber());
        }
    }

    /**
     * 绑定check v1
     *
     * @param bindOperationIds
     * @param userId
     * @param ipInfo
     * @return
     */
    public Result<DeviceBindStep> checkBindOperationStep(String bindOperationIds, int userId, IpInfo ipInfo) {
        if (StringUtils.isEmpty(bindOperationIds)) {
            throw new BaseException(INVALID_PARAMS, "bindOperationIds 不能为空");
        }
        String[] operationArray = bindOperationIds.split(",");

        DeviceBindStep deviceBindStepTemp = null;
        for (String bindOperationId : operationArray) {
            DeviceBindStep deviceBindStep = this.getDeviceBindSep(bindOperationId, userId, ipInfo);

            if (deviceBindStepTemp == null) {
                deviceBindStepTemp = deviceBindStep;
            } else if (deviceBindStep.getDeviceBindStep() <= DeviceBindStatusEnums.REFUSE_OVERWRITE_BINDING.getCode()) { // 服务端拒绝绑定
                if (deviceBindStepTemp.getDeviceBindStep() < DeviceBindStatusEnums.INIT.getCode()) {
                    deviceBindStepTemp = deviceBindStep;
                }
            } else if (deviceBindStepTemp.getDeviceBindStep() < deviceBindStep.getDeviceBindStep()) {
                deviceBindStepTemp = deviceBindStep;
            }
        }

        return new Result(deviceBindStepTemp);
    }

    private DeviceBindStep getDeviceBindSep(String bindOperationId, int userId, IpInfo ipInfo) {
        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", bindOperationId);
        String step = redisService.get(key);
        reportLogService.sysReportBind(REPORT_TYPE_CHECK_BIND_OPERATION_STEP,
                MapUtil.builder()
                        .put("bindOperationId", bindOperationId)
                        .put("userId", userId)
                        .put("deviceIp", ipInfo.getIp())
                        .put("step", step)
                        .build()
        );
        Integer deviceBindStep = StringUtils.isEmpty(step) ? DeviceBindStatusEnums.PREPARE.getCode() : Integer.valueOf(step);
        String serialNumber = "";
        if (deviceBindStep == DeviceBindStatusEnums.INIT.getCode()) {
            BindOperationTb operationTb = deviceDAO.selectOperation(bindOperationId);
            if (StringUtils.isEmpty(operationTb.getSerialNumber())) {
                //未查询到设备序列号，有可能设备绑定操作还未完成（事务）,需要等到整个操作完成才算绑定完成
                deviceBindStep = DeviceBindStatusEnums.BIND.getCode();
                LOGGER.info("设备序列号为空:{}", bindOperationId);
            } else {
                serialNumber = operationTb.getSerialNumber();
                BindOperationTb operationTbOriginal = BindOperationTb.builder()
                        .operationId(bindOperationId)
                        .appRequestBindComplete(1)
                        .appRequestBindCompleteTimestamp(Instant.now().getEpochSecond())
                        .build();
                deviceDAO.updateBindOperationBindInfo(operationTbOriginal);
            }
        }
        return DeviceBindStep.builder()
                .serialNumber(serialNumber)
                .deviceBindStep(deviceBindStep)
                .opretionId(bindOperationId)
                .build();
    }

    private void sendBindResponse(MqttDeviceBindResponse mqttResponse, String serialNumber, int statusCode) {
        mqttResponse.setCode(statusCode);
        try {
            // value.pirResponseType: pir响应的数据结构类型。0或null：当前数据结构；1：对接第三方平台的数据结构
            Optional.ofNullable(mqttResponse.getValue()).map(it -> it.getUserId())
                    .map(userService::queryUserById).map(it -> it.getTenantId())
                    .ifPresent(mqttResponse.getValue()::setTenantId);
        } catch (Throwable e) {
            LOGGER.error("sendBindResponse queryTenantId error! mqttResponse={}", JSON.toJSONString(mqttResponse));
        }
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
            PrometheusMetricsUtil.getBindMqttReponseCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), statusCode + "").inc();
        });
        try {
            VernemqPublisher.bindOperationResponse(serialNumber, mqttResponse);
        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to reply bind request from camera " + serialNumber, ex);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<BindDeviceResult> bindDeviceFromCamera(MqttDeviceBindRequest request) throws MqttException, IdNotSetException {
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
            PrometheusMetricsUtil.getBindFromCameraRequestCounterOptional().get().labels(PrometheusMetricsUtil.getHostName()).inc();
        });
        
        String serialNumber = request.getSerialNumber();
        String bindOperationId = request.getValue().getRid();
        // 记录绑定步骤
        this.updateDeviceBindStep(bindOperationId, DeviceBindStatusEnums.BIND.getCode());
        MqttDeviceBindResponse mqttResponse = new MqttDeviceBindResponse();
        mqttResponse.setId(request.getId());
        mqttResponse.setTime(PhosUtils.getUTCStamp());

        LOGGER.info(String.format("Activating device %s for bind operation: %s", serialNumber, bindOperationId));

        reportLogService.sysReportBind(REPORT_TYPE_RECEIVE_BIND_REQUEST_FROM_DEVICE,
                MapUtil.builder()
                        .put("bindOperationId", bindOperationId)
                        .put("serialNumber", serialNumber)
                        .build()
        );

        // 初始化将要写入绑定操作表的实体
        final BindOperationTb operationTb = deviceDAO.selectOperation(bindOperationId);

        // 根据操作ID判断是否收到app端发起的合法绑定请求
        if (operationTb == null) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Didn't find valid bind operation: {} for serialNumber: {}", bindOperationId, serialNumber);
            sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_REQUEST);
            return ResultCollection.INVALID_REQUEST_ID.getResult();
        }

        mqttResponse.getValue().setUserId(operationTb.getUserId());

        // 补全写回数据库信息
        operationTb.setSerialNumber(serialNumber);
        operationTb.setAnswered(1);
        // 缺省操作结果为成功
        operationTb.setStatusCode(ResultCollection.SUCCESS.getCode());


        // 请求已过期，结果入库，通知通知设备，并返回
        if ((PhosUtils.getUTCStamp() - operationTb.getRequestTime()) > DEVICE_CONNECT_TIMEOUT) {
            LOGGER.info(String.format("This bind operation: %s is expired", bindOperationId));
            operationTb.setStatusCode(ResultCollection.REQUEST_EXPIRED.getCode());
            deviceDAO.updateBindOperation(operationTb);
            sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.EXPIRED_REQUEST);
            return ResultCollection.REQUEST_EXPIRED.getResult();
        }
        // 如果bindContent是通过nodeMatcher传递过去的，则删除nodeMather上的绑定信息
        if (BindContentSrc.NODE_MATCHER.getCode() == operationTb.getBindContentSrc()) {
            nodeMatcherAgency.deleteBindContent(null, serialNumber);
        }

        UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(serialNumber);

        // 如果设备已被激活
        if ((null != userRoleDO)) {
            if (operationTb.getUserId().equals(userRoleDO.getAdminId())) {
                //验证是否可以绑定
                Result allowBindResult = this.deviceModelAllowBind(serialNumber,operationTb,mqttResponse);
                if(!allowBindResult.getResult().equals(successFlag)){
                    // 不符合绑定条件,型号不支持
                    return allowBindResult;
                }

                // 该用户已拥有此设备，直接返回成功
                LOGGER.info(String.format("User %d try to re-activate device %s", operationTb.getUserId(), serialNumber));
                // 完成绑定初始化
                this.updateDeviceBindComplete(bindOperationId);

                this.deviceBound(operationTb, serialNumber, mqttResponse);

                // 重复绑定也发事件通知
                openApiWebhookService.callWebhookForDeviceBind(operationTb.getUserId(), serialNumber, operationTb.getTimeZone(), null);
                sendDeviceBindToAlexa(bindOperationId); // 重复绑定

                PrometheusMetricsUtil.getRepeatBindCounterOptional().ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), "sameUser").inc());
                return new Result<>(new BindDeviceResult().setOldAdminId(userRoleDO.getAdminId()).setAdminId(userRoleDO.getAdminId()));
            } else {
                final User oldUser = userService.queryUserById(userRoleDO.getAdminId()); // enableRefuseOverwriteBinding按现有管理员的tenant的配置
                if (oldUser != null && paasTenantConfig.getPaasTenantInfo(oldUser.getTenantId()).getEnableRefuseOverwriteBinding()) {
                    // 除非原用户主动解绑，否则拒绝覆盖绑定
                    operationTb.setStatusCode(ResultCollection.REFUSE_OVERWRITE_BINDING.getCode());
                    deviceDAO.updateBindOperation(operationTb);
                    sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.REFUSE_OVERWRITE_BINDING);
                    return ResultCollection.REFUSE_OVERWRITE_BINDING.getResult();
                }

                //校验设备sn、mac
                boolean verifyDeviceSnInfo = this.verifyDeviceSnInfo(request.getValue().getUserSn(),serialNumber,request.getValue().getMacAddress());
                if(!verifyDeviceSnInfo){
                    sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_REQUEST);
                    LOGGER.error("exist different device info sn {}",request.getValue().getUserSn());
                    PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getGenerateDeviceBindErrorCountOptional()
                            .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName()).inc()));
                    return Result.Failure("exist different device info");
                }
                // 设备属于同一节点其他用户，调用解绑函数
                deactivateDeviceBySerialNumber(serialNumber, BindService.DELETE_DEVICE_REASON_OVERRIDE);

                PrometheusMetricsUtil.getRepeatBindCounterOptional().ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), "otherUser").inc());
            }
        }

        boolean needPostFactoryApi = this.needFactoryApi(request);
        LOGGER.info("检查是否需要访问工厂服务的接口: " + needPostFactoryApi);
        String modelNo = null;
        DeviceManufactureTableDO deviceManufactureTableDO = null;
        // TODO 这里的函数应该封装一下
        if (needPostFactoryApi) {
            // 确认SN有效性
            deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureBySn(serialNumber);
            if (deviceManufactureTableDO == null) {
                // 无效的序列号
                sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_SN);
                return Result.Failure("Invalid SN");
            }
            modelNo = StringUtils.isEmpty(deviceManufactureTableDO.getRegisterModelNo()) ? deviceManufactureTableDO.getModelNo()
                    : deviceManufactureTableDO.getRegisterModelNo();

            MqttDeviceBindRequest.MqttDeviceBindRequestValue requestValue = request.getValue();
            // 因某些型号固件原因 displayModelNo一定需要从工厂库里拿
            requestValue.setDisplayModelNo(Optional.ofNullable(deviceManufactureTableDO.getDisplayModelNo()).orElse(""));

            if (StringUtils.isEmpty(requestValue.getUserSn())) {
                requestValue.setUserSn(deviceManufactureTableDO.getUserSn());
            }
            if (StringUtils.isEmpty(requestValue.getSerialNumber())) {
                requestValue.setSerialNumber(deviceManufactureTableDO.getSerialNumber());
            }
            if (StringUtils.isEmpty(requestValue.getOriginModelNo())) {
                requestValue.setOriginModelNo(requestValue.getOriginModelNo());
            }
            if (StringUtils.isEmpty(requestValue.getModelNo())) {
                requestValue.setModelNo(modelNo);
            }
            if (StringUtils.isEmpty(requestValue.getMacAddress())) {
                requestValue.setMacAddress(Optional.ofNullable(deviceManufactureTableDO.getMacAddress()).orElse(""));
            }
            if (StringUtils.isEmpty(requestValue.getMcuNumber())) {
                requestValue.setMcuNumber(Optional.ofNullable(deviceManufactureTableDO.getMcuNumber()).orElse(""));
            }
            // 如果没上传版本号， 再用工厂数据设置， 防止工厂数据库里的固件版本低于设备本身版本
            if (StringUtils.isEmpty(requestValue.getFirmwareId())) {
                requestValue.setFirmwareId(Optional.ofNullable(deviceManufactureTableDO.getFirmwareId()).orElse(""));
            }
        }else{
            // 根据配置更新客户型号
            // 此处需求jira http://************:8080/browse/G0-31743
            deviceDisplayModelNoCorrectConfig.correctDisplayModelNo(request);
        }

        // 判断sn/serialNumber /model 是否符合绑定条件
        Result deviceExist = this.verifyDeviceModelInfo(
                deviceManufactureTableDO,
                request.getValue().getUserSn(),
                request.getValue().getSerialNumber(),
                operationTb.getTenantId(),
                request.getValue().getModelNo()
        );
        if(!deviceExist.getResult().equals(successFlag)){
            LOGGER.error("绑定验证不符合条件");
            return Result.Failure("Invalid SN");
        }

        // 检查一下参数，别出岔子哇
        if (!request.getValue().hasDeviceManualInfo()) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "bind device info is not valid!");
            sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_REQUEST);
            return Result.Failure("Invalid device info");
        }

        // 拷贝硬件信息到对应节点的库
        DeviceManualDO deviceManual = new DeviceManualDO()
                .setUserSn(request.getValue().getUserSn())
                .setSerialNumber(request.getValue().getSerialNumber())
                .setModelNo(request.getValue().getModelNo())
                .setOriginalModelNo(request.getValue().getOriginModelNo())
                .setDisplayModelNo(request.getValue().getDisplayModelNo())
                .setMacAddress(request.getValue().getMacAddress())
                .setMcuNumber(request.getValue().getMcuNumber());
        deviceManualService.addDeviceManual(deviceManual);
        LOGGER.info(String.format("Copy factory hardware info for device", serialNumber));

        // 经过上面一系列检查，现在可以正式进行绑定操作了
        //重新绑定时删除之前的收藏点
        rotationPointService.deleteRotationPointBySerialNumber(serialNumber);
        initDeviceBinding(operationTb.getUserId(), serialNumber);
        
        // 更新数据库，确认绑定操作
        deviceDAO.updateBindOperation(operationTb);
        LOGGER.info(String.format("Updated bind operation info for: %s", bindOperationId));

        // 初始化设备状态表
        deviceStatusService.initDeviceStatusForBind(serialNumber, operationTb);
        LOGGER.info(String.format("Updated Device status for device: %s", serialNumber));

        // 更新用户设备关系表
        UserRoleDO userRole = new UserRoleDO();
        userRole.setUserId(operationTb.getUserId());
        userRole.setAdminId(operationTb.getUserId());
        userRole.setSerialNumber(serialNumber);
        userRole.setRoleId("1");
        userRoleService.saveUserRole(userRole);
        LOGGER.info(String.format("Init admin relationship for device: %s with user : %d", serialNumber, operationTb.getUserId()));

        // 在当前节点的最后才去修改device表
        DeviceDO device = new DeviceDO();
        device.setSerialNumber(serialNumber);
        device.setUserId(operationTb.getUserId());
        device.setDeviceName(initDeviceName(operationTb.getUserId(),request.getValue().getModelNo()));
        device.setLocationId(operationTb.getLocationId());
        device.setHomeId(operationTb.getHomeId());
        device.setFirmwareId(request.getValue().getFirmwareId());
        device.setDisplayModelNo(request.getValue().getDisplayModelNo());

        device.setDormancyPlanSwitch(deviceDormancyPlanConfig.queryDeviceDormancyPlanValue(deviceManual.getModelNo()));
        deviceService.activateDevice(device);
        LOGGER.info(String.format("Updated Device info after activating device: %s", serialNumber));

        // 更新OTA状态
        FirmwareViewDO firmwareViewDO = new FirmwareViewDO();
        firmwareViewDO.setSerialNumber(serialNumber);
        firmwareViewDO.setTargetFirmware(queryDeviceFirmware(serialNumber));
        firmwareService.insertDeviceOTA(firmwareViewDO);
        LOGGER.info(String.format("Init OTA info for device", serialNumber));

        // 初始化设备维度AI分析设置
        deviceAiSettingsService.initDeviceAiSettings(operationTb.getUserId(), serialNumber);
        //更新设备人型检测
        initMessageNotificationSettings(operationTb.getUserId(), serialNumber);



        this.operationAfterDeviceBind(operationTb);

        // 通知摄像头初始化设置。cooldown、deviceAudio、doorBell都在initDeviceConfig里面有，不用单独去初始化。
        DeviceSettingsDO deviceSetting = initDeviceConfig(operationTb, request.getValue().getModelNo());

        final String tenantId = userService.getUserTenantId(operationTb.getUserId());
        if (AppConstants.TENANTID_SAFEMO.equals(tenantId)) {
            Integer securityMode = deviceModeService.getSecurityMode(operationTb.getUserId());
            LOGGER.debug("bind start to setSecurityMode,userId:{}, sn:{}, mode:{}", operationTb.getUserId(), operationTb.getSerialNumber(), securityMode);
            if (securityMode != null && securityMode != NO_MODE_ID) {
                deviceModeService.setSecurityMode(operationTb.getUserId(), securityMode);
            }
        }

        sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.SUCCESS);

        // 完成绑定初始化
        this.updateDeviceBindComplete(bindOperationId);

        // 绑定发送事件通知
        openApiWebhookService.callWebhookForDeviceBind(operationTb.getUserId(), serialNumber, operationTb.getTimeZone(), deviceManual);
        sendDeviceBindToAlexa(bindOperationId); // 首次绑定
        return new Result<>(new BindDeviceResult().setOldAdminId(userRoleDO != null ? userRoleDO.getAdminId() : null).setAdminId(userRole.getAdminId()));
    }


    /**
     * 设备绑定之后做的操作
     * @param operationTb
     */
    public void operationAfterDeviceBind(BindOperationTb operationTb){
        // 计算绑定后评分引导
        userAppScoreService.userAppScoreMomentDeviceBind(operationTb.getUserId(),operationTb.getSerialNumber());
    }

    /**
     * 设备绑定时做的初始化操作
     */
    private void initDeviceBinding(Integer userId, String serialNumber) {
        try {
            userVipService.deleteUserReminder(userId);

            // 设备新绑定去除所有休眠计划
            deviceDormancyPlanService.deleteDeviceDormancyPlan(serialNumber);

        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "用户绑定初始化失败" + userId, e);
        }
    }


    public String initDeviceName(Integer userId,String modelNo) {
        User user = userService.queryUserById(userId);
        if (user == null) {
            return "";
        }

        if(deviceCameraNameConfig.getConfig().containsKey(modelNo)){
            //指定型号设备 cameraName
            return deviceCameraNameConfig.getConfig().get(modelNo);
        }

        Integer deviceModelCategory = deviceModelService.queryDeviceModelCategory(modelNo);
        return deviceNameConfig.queryDeviceName(user.getLanguage(),deviceModelCategory);
    }

    /**
     * 设备重新绑定情况时回复
     *
     * @param operationTb
     * @param serialNumber
     * @param mqttResponse
     */
    private void deviceBound(BindOperationTb operationTb, String serialNumber, MqttDeviceBindResponse mqttResponse) throws MqttException, IdNotSetException {
        //更新绑定操作记录表状态
        operationTb.setStatusCode(ResultCollection.SUCCESS.getCode());
        deviceDAO.updateBindOperation(operationTb);

        //更新设备语言
        DeviceSettingsDO deviceSettingsDO = DeviceSettingsDO.builder()
                .serialNumber(serialNumber)
                .language(operationTb.getDeviceLanguage())
                .deviceSupportLanguage(null)
                .build();
        deviceSettingService.updateDeviceSetting(deviceSettingsDO);

        //更新设备设置
        DeviceSettingsDO storeDeviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        LOGGER.info("设备重新绑定将数据库配置下发,serialNumber:{},deviceSettingsDO:{}", serialNumber, storeDeviceSettingsDO);
        deviceSettingService.resetVernemqSettingMessage(serialNumber, storeDeviceSettingsDO);

        //回复设备绑定结果
        sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.SUCCESS);
    }

    private DeviceSettingsDO initDeviceConfig(BindOperationTb operationTb, String modelNo) {
        try {
            operationTb.setBindRequestApproved(1);
            operationTb.setBindRequestApprovedTimestamp(Instant.now().getEpochSecond());

            DeviceModel deviceModel = deviceModelConfigService.queryRowDeviceModelByModelNo(modelNo);

            DeviceModelVolumeDO deviceModelVoiceDO = deviceModelVoiceService.queryDeviceModelVoice(modelNo);
            DeviceModelSettingDO deviceModelSettingDO = DeviceModelSettingDO.builder()
                    .deviceModelVoiceDO(deviceModelVoiceDO)
                    .mirrorFlip(deviceModel == null ? MIRROR_FLIP_VALUE : deviceModel.getMirrorFlip())
                    .motionSensitivity(deviceSettingConfig.queryMotionSensitivity(modelNo,deviceModel))
                    .chargeAutoPowerOnSwitch(deviceSettingConfig.queryModelChargeAutoPowerOnSwitch(modelNo))
                    .build();

            DeviceAppSettingsDO appSettingsDO = DeviceAppSettingsDO.defaultSettings(deviceModelSettingDO,deviceModel);
            appSettingsDO.setClearDefaultCodec(true);
            appSettingsDO.setTimeZone(operationTb.getTimeZone());
            appSettingsDO.setDeviceLanguage(operationTb.getDeviceLanguage());
            appSettingsDO.setSerialNumber(operationTb.getSerialNumber());
            appSettingsDO.setAntiflickerSwitch(deviceSettingConfig.queryModelAntiflickerSwitch(modelNo));
            String country = geoIpService.getCountryCode(operationTb.getIp());
            appSettingsDO.setRecLamp(deviceSettingConfig.queryRecLamp(modelNo));
            appSettingsDO.setEnableOtherMotionAi(deviceSettingConfig.queryEnableOtherMotionAi(modelNo));
            if (deviceAntiflickerConfig.getConfig().containsKey(country)) {
                appSettingsDO.setAntiflicker(deviceAntiflickerConfig.getConfig().get(country));
            } else {
                appSettingsDO.setAntiflicker(DEFAULT_ANTIFLICKER);
            }
            // 根据套餐判断是否打开拍摄间隔开关
            try {
                final boolean isNoVipOrFreeTier2 = userTierDeviceService.getIsNoVipOrFreeTier2(operationTb.getUserId(), operationTb.getSerialNumber());
                if (isNoVipOrFreeTier2) {
                    User user = userService.queryUserById(operationTb.getUserId());
                    Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(operationTb.getUserId(), operationTb.getSerialNumber());
                    if(currentTierId == null){
                        currentTierId = userVipService.queryUserFreeTierId(user.getId());
                    }

                    UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(operationTb.getUserId());
                    boolean promotionPeriod ;
                    if(userSettingsDO != null && userSettingsDO.getSupportFreeLicense().equals(1)){
                        UserShowFreeLicenseDO userShowFreeLicenseDO = userShowFreeLicenseService.queryUserShowFreeLicenseByUserId(user.getId());
                        promotionPeriod = userShowFreeLicenseService.userDevicePromotionPeriod(userShowFreeLicenseDO,deviceModel);
                    }else {
                        promotionPeriod = false;
                    }
                    LOGGER.debug("bindDevice currentTierId {} registertime {} promotionPeriod {}",currentTierId,user.getRegistTime(),promotionPeriod);
                    // 推广期设备固定30s
                    int coolDownTime = promotionPeriod ? AppFormOptionsDO.CooldownOptionValue.SECONDS_30.getValue() :
                            FreeUserVipTier2Config.getCooldownOptionValueList(currentTierId, user.getRegistTime(), modelNo, operationTb.getSerialNumber()).get(0).getValue();
                    appSettingsDO.setCooldown(new CoolDownDO().setValue(coolDownTime).setUserEnable(true));
                }
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceConfig failed set cooldown", e);
            }

            DeviceSettingsDO settingsDO = DeviceSettingsDO.ParseFrom(appSettingsDO, null);
            LOGGER.debug("bindDevice settingsDO init {}",JSON.toJSONString(settingsDO));
            String rawSettingsDOJSON = null;
            try {
                rawSettingsDOJSON = JSON.toJSONString(settingsDO);
                final DeviceAttributeService.DeviceAttributeSource attrSrc = deviceAttributeService.getAttributeSource(operationTb.getSerialNumber());
                attrSrc.setSettingDefaultEnumValue(settingsDO); // 初始化枚举值字段
                LOGGER.info("initDeviceConfig setSettingDefaultEnumValue end! sn={},rawSettingsDO={},settingsDO={}", operationTb.getSerialNumber(), rawSettingsDOJSON, JSON.toJSONString(settingsDO));
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceConfig setSettingDefaultEnumValue error! sn={},rawSettingsDO={},settingsDO={}", operationTb.getSerialNumber(), rawSettingsDOJSON, JSON.toJSONString(settingsDO), e);
            }

            this.pushMqttMessage(settingsDO); // 发送 setting mqtt 消息
            // 不需要设置设备支持语言
            settingsDO.setDeviceSupportLanguage(null);
            // 设备支持门铃铃音需要设备上报
            settingsDO.setSupportDoorBellRingKey("");
            appSettingsDO.setDoorbellPressNotifySwitch(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH);
            appSettingsDO.setDoorbellPressNotifyType(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE);
            appSettingsDO.setPowerSource(OptionEnumMapping.OPTION_ENUM_UNSELECTED);

            //物模型里的默认值赋值
            for (ThingModel.ThingEntity entity : thingModelConfig.getThingModelByModelNo(modelNo).getProperties()) {
                // thing_model.yml 的默认值
                if(entity.getDefaultValue()!=null) {
                    settingsDO.addToPropertyJson(entity.getIdentifier(), entity.getDefaultValue());
                }
            }

            deviceSettingService.setDeviceSettings(settingsDO);

            return settingsDO;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "绑定重置设备设置失败:serialNumber:{},{}", operationTb.getSerialNumber(), e);
            return null;
        }
    }

    private void pushMqttMessage(DeviceSettingsDO settingsDO) throws MqttException, IdNotSetException {
        //发送普通设置set消息
        SetParameterRequest setParameterRequest = settingsDO.ToRequest(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        VernemqPublisher.setParameter(settingsDO.getSerialNumber(), setParameterRequest);

        //发送retain message消息
        SetRetainParamRequest retainParamter = deviceSettingService.initSetParameterRequest(setParameterRequest.getId(), settingsDO, null);
        VernemqPublisher.setParameterRetain(settingsDO.getSerialNumber(), retainParamter);
    }

    private void initMessageNotificationSettings(Integer userId, String serialNumber) {
        try {
            // 初始化AI消息设置表。无论当前设备是否是VIP
            notificationService.initMessageNotificationSettings(serialNumber, userId);
            if (vipService.isVipDevice(userId, serialNumber)) {
                LOGGER.info("绑定AI人型识别:{}", serialNumber);
                // 开启人型检测
                deviceInfoService.updatePersonDetect(serialNumber, PersonDetectEnum.OPENPERSONDETECT.getCode());
            } else {
                //防止解绑时未关闭的情况
                LOGGER.info("关闭AI人型检测:{}", serialNumber);
                deviceInfoService.updatePersonDetect(serialNumber, PersonDetectEnum.CLOSEPERSONDETECT.getCode());
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "绑定AI人型识别error:serialNumber:{},{}", serialNumber, e);
        }
    }

    /**
     * 获取同型号最新的固件id
     *
     * @param serialNumber
     * @return
     */
    private String queryDeviceFirmware(String serialNumber) {
        String firewareId = "";
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if (modelNo != null) {
            FirmwareTableDO firmwareTableDO = firmwareService.getLatestActivatedFirmwareBySerialNumber(serialNumber);
            firewareId = firmwareTableDO == null ? firewareId : firmwareTableDO.getFirmwareId();
        }
        return firewareId;
    }

    /**
     * 上传设备绑定错误原因
     *
     * @param request
     * @return
     */
    public boolean bindErrorReason(BindErrorRequest request) {
        int time = (int) (System.currentTimeMillis() / 1000);
        request.setCdate(time);
        return iDeviceBindErrorDAO.insertOrUpdate(request) > 0;
    }

    public String queryModelBySerialNumber(String userSn) {
        //现在型号逻辑待确认，等确认后再补逻辑，已跟产品沟通

        return StringUtils.isEmpty(userSn) ? "" : "G";
    }

    public Result queryDeviceBindCode(Integer userId, BindOperationRequest request, IpInfo ipInfo) throws IOException, WriterException {
        String operation = bindOperation(userId, request, ipInfo);
        String contents;
        String image;
        if (DeviceBindTypeEnums.JSON.getCode().equals(request.getCodeType())) {
            contents = this.getCodeValueJson(operation, request);
            image = QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight());
        } else {
            contents = this.getCodeValue(operation, request);
            image = QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight());
        }
        if (StringUtils.isEmpty(image)) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "queryDeviceBindCode error,{}", request.toString());
            throw new BaseException(INVALID_PARAMS, "INVALID_PARAMS");
        }

        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("contents", contents);
        map.put("image", image);
        return new Result(map);
    }

    public Result bindCableDevice(Integer userId, BindOperationRequest request, IpInfo ipInfo) throws Exception {
        DeviceManufactureTableDO deviceManu = factoryDataQueryService.queryDeviceManufactureByUserSn(request.getUserSn());
        if (deviceManu == null) {
            return Result.Error(INVALID_PARAMS, "userSn无效");
        }
        request.setBindContentSrc(BindContentSrc.NODE_MATCHER.getCode());
        request.setDeviceNetType(DeviceNetType.CABLE.getCode());
        String operation = bindOperation(userId, request, ipInfo);
        String contents = this.getCodeValue(operation, request);
        // 设置绑定内容到node-matcher
        if (!nodeMatcherAgency.setBindContent(request.getUserSn(), deviceManu.getSerialNumber(), contents, DEVICE_CONNECT_TIMEOUT)) {
            return Result.Failure("发送绑定信息失败");
        }
        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("contents", contents);
        map.put("image", QRcodeUtil.genBarcode(contents, request.getWidth(), request.getHeight()));
        return new Result(map);
    }

    public Result queryDeviceBindByApTextResult(Integer userId, BindByApOperationRequest request, IpInfo ipInfo) throws IOException, WriterException {
        BindOperationRequest bindOperationRequest = new BindOperationRequest();
        BeanUtils.copyProperties(request, bindOperationRequest);
        bindOperationRequest.setBindContentSrc(BindContentSrc.QRCODE.getCode());
        String operation = bindOperation(userId, bindOperationRequest, ipInfo);

        String bindText = getCodeValue(operation, bindOperationRequest);
        String bindJson = getCodeValueJson(operation, bindOperationRequest);

        Map<String, String> map = Maps.newHashMap();
        map.put("operationId", operation);
        map.put("bindText", bindText);
        map.put("bindJson", bindJson);
        return new Result(map);
    }

    private String getCodeValueJson(String operation, BindOperationRequest request) {
        JSONObject codeValue = new JSONObject();
        codeValue.put("r", operation);
        codeValue.put("l", request.getDeviceLanguage());
        codeValue.put("n", request.getNetworkName());
        codeValue.put("e", profilesActive);
        codeValue.put("p", request.getPassword());
        // 目前前入职只支持 0 （绑定）
        codeValue.put("c", defaultBindType);
        // 添加 绑定流程追踪ID
        final String traceId = AppRequestBase.getValidTraceId(request);
        if (traceId != null) codeValue.put("t", traceId);

        return codeValue.toString();
    }

    private String getCodeValue(String operation, BindOperationRequest request) throws UnsupportedEncodingException {
        StringBuilder codeStringBuilder = new StringBuilder();
        Integer language = deviceLanguageConfig.getConfig().containsKey(request.getDeviceLanguage()) ?
                deviceLanguageConfig.getConfig().get(request.getDeviceLanguage()) : deviceLanguageConfig.getConfig().get(request.getLanguage());
        if (language == null) {
            //默认英语
            language = 0;
        }
        final DeviceNetType deviceNetType = DeviceNetType.codeOf(request.getDeviceNetType());
        LOGGER.info("getCodeValueByte language:{},deviceNetType={}", language, deviceNetType);
        appendValue(CodeKeyEnums.QR_ID_REQUEST_ID.getCode(), operation, codeStringBuilder);
        appendValueInt(CodeKeyEnums.QR_ID_LANGUAGE.getCode(), language, codeStringBuilder);
        if (deviceNetType == DeviceNetType.WIFI) { // 防止影响老设备，不改变键值对顺序
            appendValue(CodeKeyEnums.QR_ID_WIFI_SSID.getCode(), request.getNetworkName(), codeStringBuilder);
        }
        appendValue(CodeKeyEnums.QR_ID_SERVER_ENV.getCode(), profilesActive, codeStringBuilder);
        if (deviceNetType == DeviceNetType.WIFI) { // 防止影响老设备，不改变键值对顺序
            appendValue(CodeKeyEnums.QR_ID_WIFI_PASSWORD.getCode(), request.getPassword(), codeStringBuilder);
        }
        // 防止影响老设备，新的键值对放在最后
        appendValueInt(CodeKeyEnums.QR_ID_DEVICE_NET_TYPE.getCode(), deviceNetType.getCode(), codeStringBuilder);

        //4G设备新增
        if (deviceNetType == DeviceNetType.MOBILE) {
            appendValue(CodeKeyEnums.QR_ID_DEVICE_SIM_APN.getCode(), request.getApn(), codeStringBuilder);
            appendValue(CodeKeyEnums.QR_ID_DEVICE_SIM_MCC.getCode(), request.getMcc(), codeStringBuilder);
            appendValue(CodeKeyEnums.QR_ID_DEVICE_SIM_MNC.getCode(), request.getMnc(), codeStringBuilder);
            appendValueInt(CodeKeyEnums.QR_ID_DEVICE_SIM_APNV.getCode(), request.getApnv(), codeStringBuilder);
        }
        // 添加 绑定流程追踪ID
        final String traceId = AppRequestBase.getValidTraceId(request);
        if (traceId != null) appendValue(CodeKeyEnums.QR_ID_TRACE_ID.getCode(), traceId, codeStringBuilder);

        return codeStringBuilder.toString();
    }

    /**
     * 拼接文本,key+value.length+value
     *
     * @param key
     * @param value
     */
    private void appendValue(int key, String value, StringBuilder codeStringBuilder) throws UnsupportedEncodingException {
        codeStringBuilder.append((char) key);
        codeStringBuilder.append((char) (StringUtils.isEmpty(value) ? 0 : value.getBytes().length));
        if (!StringUtils.isEmpty(value)) {
            codeStringBuilder.append(value);
        }
    }


    /**
     * 拼接文本,key+value.length+value
     *
     * @param key
     * @param value
     */
    private void appendValueInt(int key, Integer value, StringBuilder codeStringBuilder) {
        codeStringBuilder.append((char) key);
        codeStringBuilder.append((char) 1);
        codeStringBuilder.append((char) value.intValue());
    }

    /**
     * 用户进入绑定页面获取绑定码，会随着后续bind_operation 传入
     *
     * @param userId
     * @return
     */
    public String getBindCode(int userId) {
        StringBuilder code = new StringBuilder();
        code.append(userId);
        code.append("-");
        code.append(System.currentTimeMillis());
        String result = code.toString();
        reportLogService.sysReportBind(REPORT_TYPE_GENERATE_BIND_CODE,
                MapUtil.builder()
                        .put("userId", userId)
                        .put("code", result)
                        .build()
        );

        Long time = Instant.now().getEpochSecond();
        // 记录绑定码生成时间
        redisService.set(DeviceReportKeyConstants.DEVICE_BIND_CODE_KEY.replace("{bindCode}", result),
                String.valueOf(Instant.now().getEpochSecond()),
                DEVICE_CONNECT_TIMEOUT);

        // 记录绑定码
        DeviceBindCode deviceBindCode = DeviceBindCode.builder()
                .userId(userId)
                .bindCode(result)
                .cdate(time.intValue())
                .build();
        deviceBindCodeService.insertDeviceBindCode(deviceBindCode);
        return result;
    }

    private Result deviceModelAllowBind(String serialNumber, BindOperationTb operationTb,MqttDeviceBindResponse mqttResponse) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        if(!StringUtils.hasLength(modelNo)){
            LOGGER.error("有绑定关系，型号不存在");
            operationTb.setStatusCode(ResultCollection.INVALID_PARAMS.getCode());
            deviceDAO.updateBindOperation(operationTb);
            sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_REQUEST);
            return Result.Failure("有绑定关系，型号不存在");
        }
        if(!deviceModelTenantService.queryDeviceModelTenantModelList(operationTb.getTenantId()).contains(modelNo)){
            LOGGER.error("tenant 不支持 nodelNo ");
            operationTb.setStatusCode(ResultCollection.DEVICE_MODEL_BIND_SUPPORT.getCode());
            deviceDAO.updateBindOperation(operationTb);
            sendBindResponse(mqttResponse, serialNumber, MqttResponseCode.INVALID_REQUEST);
            return Result.Failure("有绑定关系，型号不存在");
        }
        return Result.Success();
    }

    /**
     * 设备上报连接http状态
     *
     * @param deviceConnectReport
     */
    public void deviceReportConnectStatus(DeviceConnectReport deviceConnectReport) {
        // 固件未保证顺讯性,暂时不更新步骤
//        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", deviceConnectReport.getOperationId());
//        // 连接http成功
//        redisService.set(key, String.valueOf(DeviceBindStatusEnums.CONNECT.getCode()), DEVICE_CONNECT_TIMEOUT);
        reportLogService.deviceEventReport(REPORT_GROUP_BIND, REPORT_TYPE_DEVICE_HTTP_CONNECT, JSON.parseObject(JSON.toJSONString(deviceConnectReport)));

        LOGGER.info("deviceReportConnectStatus mark redis,operationId:{}", deviceConnectReport.getOperationId());
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(deviceConnectReport.getOperationId())
                .deviceHttpRequest(1)
                .deviceHttpRequestTimestamp(Instant.now().getEpochSecond())
                .build();

        deviceDAO.updateBindOperationBindInfo(bindOperationTb);
    }

    /**
     * 更新设备绑定步骤
     *
     * @param operationId
     * @param step
     */
    public void updateDeviceBindStep(String operationId, int step) {
        LOGGER.info("设备绑定步骤更新,operationId:{},step:{}", operationId, step);
        String key = DeviceReportKeyConstants.DEVICE_CONNECT_KEY.replace("{operationId}", operationId);
        // 更新 operationId 对应绑定步骤
        redisService.set(key, String.valueOf(step), DEVICE_CONNECT_TIMEOUT);
    }

    private void updateDeviceBindComplete(String operationId) {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(operationId)
                .bindRequestComplete(1)
                .bindRequestCompleteTimestamp(Instant.now().getEpochSecond())
                .build();
        deviceDAO.updateBindOperationBindInfo(bindOperationTb);
    }

    /**
     * 设备绑定-接收到设备mqtt消息
     *
     * @param operationId
     */
    public void receiveDeviceBindMqtt(String operationId) {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(operationId)
                .deviceMqttRequest(1)
                .deviceMqttRequestTimestamp(Instant.now().getEpochSecond())
                .build();

        deviceDAO.updateBindOperationBindInfo(bindOperationTb);
    }

    /**
     * APP上报绑定步骤
     *
     * @param operationId
     * @return
     */
    public boolean deviceBindComplete(String operationId, int bindStep, String appName) {
        BindOperationTb bindOperationTb = BindOperationTb.builder()
                .operationId(operationId)
                .build();
        Long time = Instant.now().getEpochSecond();
        DeviceBindStatusEnums bindStatusEnums = DeviceBindStatusEnums.queryByCode(bindStep);
        switch (bindStatusEnums) {
            case WIFI:
                bindOperationTb.setAppBindStepWifi(1);
                bindOperationTb.setAppBindStepWifiTimestamp(time);
                break;
            case CONNECT:
                bindOperationTb.setAppBindStepHttp(1);
                bindOperationTb.setAppBindStepHttpTimestamp(time);
                break;
            case BIND:
                bindOperationTb.setAppBindStepApproved(1);
                bindOperationTb.setAppBindStepApprovedTimestamp(time);
                break;
            case INIT:
                bindOperationTb.setAppBindStepComplete(1);
                bindOperationTb.setAppBindStepCompleteTimestamp(time);

                //send device bind event
                sendDeviceBindToAlexa(operationId); // app上报绑定完成
                break;
        }

        int result = deviceDAO.updateBindOperationBindInfo(bindOperationTb);
        return result > 0;
    }

    public void sendDeviceBindToAlexa(String operationId) {
        if (devicePlatformEventPublisher != null) {
            Map<String, Object> deviceEventParamMap = new HashMap<>();
            deviceEventParamMap.put("operationId", operationId);
            devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DEVICE_BIND, deviceEventParamMap);
        }
    }


    public void updateOperationScanUserSn(BindOperationTb bindOperationTb) {
        deviceDAO.updateBindOperationScanUserSn(bindOperationTb);
    }

    /**
     * 判断是否需要调用厂测api
     *
     * @param request
     * @return
     */
    public boolean needFactoryApi(MqttDeviceBindRequest request) {
        if (StringUtils.isEmpty(request.getValue().getUserSn())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备绑定验证无userSn【{}】", request.getSerialNumber());
            return true;
        }
        if (StringUtils.isEmpty(request.getValue().getOriginModelNo())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备绑定验证无originModelNo【{}】", request.getSerialNumber());
            return true;
        }
        if (StringUtils.isEmpty(request.getValue().getModelNo())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备绑定验证无modelNo【{}】", request.getSerialNumber());
            return true;
        }
        if (StringUtils.isEmpty(request.getValue().getMacAddress())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备绑定验证无macAddress【{}】", request.getSerialNumber());
            return true;
        }

        if (StringUtils.isEmpty(request.getValue().getFirmwareId())) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "设备绑定验证无firmwareId【{}】", request.getSerialNumber());
            return true;
        }

        return false;
    }

    /**
     * 校验设备是否符合绑定条件
     * @param userSn
     * @param serialNumber
     * @return
     */
    public Result verifyDeviceModelInfo(DeviceManufactureTableDO deviceManufactureTableDO, String userSn,String serialNumber,String tenantId,String modelNo){
        if (deviceManufactureTableDO == null) {
            deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureByUserSn(userSn);
        }
        if (deviceManufactureTableDO == null) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备绑定时校验,设备序列号无效，deviceManufactureTableDO null");
            // 无效的序列号
            return Result.Failure("Invalid SN");
        }

        if(deviceManufactureTableDO.getSerialNumber() == null ||
                !deviceManufactureTableDO.getSerialNumber().equals(serialNumber)){
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备绑定时校验,设备序列号无效，deviceManufactureTableDO serialNumber {},device serialNumber {}",deviceManufactureTableDO.getSerialNumber(),serialNumber);
            return Result.Failure("UUID not exist");
        }

        if (!MANUFACTURE_CONFIRMED.equals(deviceManufactureTableDO.getManufactureConfirmed())) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "设备绑定时校验,设备处于未出厂状态，deviceManufactureTableDO serialNumber {}",deviceManufactureTableDO.getSerialNumber());
            return Result.Failure("UUID not exist");
        }

        Set<String> supportModelNoSet = deviceModelTenantService.queryDeviceModelTenantModelList(tenantId);
        return supportModelNoSet.contains(modelNo) ? Result.Success() :  Result.Failure("modelNo not support");
    }

    public String getLatestBindModelNo(int userId) {
        if (userId <= 0) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        BindOperationTb bindOperationTb = deviceDAO.queryBindHistoryCompleted(userId);
        if (bindOperationTb == null || org.apache.commons.lang3.StringUtils.isBlank(bindOperationTb.getSerialNumber())) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        return deviceManualService.getModelNoBySerialNumber(bindOperationTb.getSerialNumber());
    }

    /**
     * 绑定时校验不同设备相同sn 的情况
     * @param userSn
     * @param serialNumber
     * @param macAddress
     * @return
     */
    public boolean verifyDeviceSnInfo(String userSn,String serialNumber,String macAddress){
        DeviceManualDO deviceManualDO = deviceManualService.getDeviceManualBySerialNumber(serialNumber);
        if(deviceManualDO == null){
            // 存在绑定关系，但是没有型号信息,
            LOGGER.error("存在绑定关系，但是没有型号信息 设备号 {}",serialNumber);
            return false;
        }

        if(!deviceManualDO.getUserSn().equals(userSn)){
            LOGGER.error("已存在userSn 与 本次userSn 不一致 {} ",deviceManualDO.getUserSn());
            return false;
        }

        return Optional.ofNullable(macAddress).orElse("")
                .equals(Optional.ofNullable(deviceManualDO.getMacAddress()).orElse(""));
    }


    /**
     * 绑定成功后，加一个afterBinding弹窗缓存
     *
     * @param userId
     */
    public void cacheUserIdAfterBinding(Integer userId) {
        String key = AFTER_BINDING_POPUP_KEY + userId;
        redisService.set(key, userId.toString(), 172800);
    }

    public Integer getCachedUserIdAfterBinding(Integer userId) {
        String key = AFTER_BINDING_POPUP_KEY + userId;
        String cachedValue = redisService.getFromSlave(key);

        return cachedValue != null ? Integer.valueOf(cachedValue) : null;
    }

    // 当camera绑定到bx时，自动给它在云端解绑
    public void deactivateDeviceForCameraBindToBx(DeviceEvent deviceEvent) {
        try {
//           "cxSerialNumber": "67a3f6003442e063ec58d3b09c0bd70c-44466d76b9d150deb04d2d8b7172e106",
            final String cxSn = getRealCxSerialNumber(deviceEvent.getCxSerialNumber());
            final UserRoleService.UserRoles userRoles = userRoleService.queryUserRolesBySn(cxSn, true);
            if (userRoles.getAdminId() == null || !userRoles.getAdminId().equals(deviceEvent.getAdminUserId())) {
                LOGGER.debug("deactivateDeviceForCameraBindToBx cloud not bind! deviceEvent={}", JSON.toJSONString(deviceEvent));
                return;
            }
            Result result = deactivateDevice(cxSn, userRoles, deviceEvent.getAdminUserId());
            analyticDeleteDeviceResult(cxSn, userRoles.getAdminId(), DELETE_DEVICE_REASON_BIND_TO_BX, result);
            LOGGER.info("deactivateDeviceForCameraBindToBx end! deviceEvent={},result={}", JSON.toJSONString(deviceEvent), JSON.toJSONString(result));
        } catch (Throwable e) {
            LOGGER.error("deactivateDeviceForCameraBindToBx error! deviceEvent={}", JSON.toJSONString(deviceEvent), e);
        }
    }

    public static final int DELETE_DEVICE_REASON_MANAGER = 1;
    // 2-设备被其它账号绑定时,自动与上一个账号解绑。
    public static final int DELETE_DEVICE_REASON_OVERRIDE = 2;
    // 3-设备被bx绑定时,自动与上一个账号解绑。
    public static final int DELETE_DEVICE_REASON_BIND_TO_BX = 3;
    // 4-设备被a4x内部解绑
    public static final int DELETE_DEVICE_REASON_INNER = 4;

    public void analyticDeleteDeviceResult(String sn, Integer userId, Integer reason, Result<?> result) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("sn", sn);
            map.put("user_id", userId);
            map.put("reason", reason);
            map.put("result", successFlag.equals(result.getResult()));
            map.put("error_code", result.getResult());
            SnowPlowManager.getInstance().analyticSelfEvent("delete_device_result", map);
        } catch (Exception e) {
            LOGGER.error("analyticDeleteDeviceResult error, sn:{}, userId:{} reason:{} result:{}", sn, userId, reason, JSON.toJSONString(result));
        }
    }

    /**
     * 清除deviceHomeMode 数据
     * 重新计算设备解绑后是否展示home mode
     * @param adminId
     * @param serialNumber
     */
    public void cleanDeviceHomeMode(Integer adminId,String serialNumber){
        boolean supporHomeMode = deviceHomeModeService.querySupportHomeMode(adminId);
        if(!supporHomeMode){
            return;
        }
        deviceHomeModeService.deactivateOwnDevice(adminId,serialNumber);
        User user = userService.queryUserById(adminId);
        DeviceHomeModeRequest request = new DeviceHomeModeRequest();
        AppInfo app = new AppInfo();
        app.setTenantId(user.getTenantId());
        request.setApp(app);
        deviceHomeModeService.queryDeviceHomeMode(adminId,request);
    }
}
