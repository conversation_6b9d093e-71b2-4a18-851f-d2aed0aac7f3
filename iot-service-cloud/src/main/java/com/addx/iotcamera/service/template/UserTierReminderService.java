package com.addx.iotcamera.service.template;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.db.BindOperationTb;
import com.addx.iotcamera.bean.db.UserTierDeviceDO;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.bean.domain.library.TierFreeNotifyPeriodDO;
import com.addx.iotcamera.bean.domain.library.TierFreeUserExpireDO;
import com.addx.iotcamera.config.TierReminderConfig;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.service.Device4GService;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserTierDeviceService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.VipService;
import com.addx.iotcamera.service.user.UserSettingService;
import com.addx.iotcamera.service.vip.TierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.addx.iotcamera.enums.template.FunctionType.TIER_RECEIVE_REMINDER;

@Slf4j
@Service
public class UserTierReminderService extends FunctionExperimentService{

    @Resource
    @Lazy
    private UserVipService userVipService;

    @Resource
    @Lazy
    private UserRoleService userRoleService;

    @Resource
    private TierReminderConfig tierReminderConfig;
    @Autowired
    private VipService vipService;
    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private Device4GService device4GService;

    @Override
    public String queryFunctionName() {
        return TIER_RECEIVE_REMINDER.getName();
    }

    @Resource
    @Lazy
    private DeviceInfoService deviceInfoService;

    @Resource
    @Lazy
    private UserTierDeviceService userTierDeviceService;

    @Resource
    @Lazy
    private TierService tierService;

    @Resource
    @Lazy
    private IDeviceDAO deviceDAO;

    @Override
    public TierFreeUserExpireDO eligible(Integer userId, AppRequestBase request) {
        BindOperationTb bindOperationTb = deviceDAO.queryBindHistoryCompleted(userId);
        Set<String> device4gList = userRoleService.getUserRoleByUserId(userId, UserRoleEnums.ADMIN.getCode()).stream()
                .map(UserRoleDO::getSerialNumber)
                .filter(serialNumber -> deviceInfoService.checkIfDeviceUse4G(serialNumber))
                .collect(Collectors.toSet());
        UserSettingsDO userSettingsDO = userSettingService.queryUserSetting(userId);
        boolean oldUser = userSettingsDO.getSupportFreeLicense() == 0;
        if(bindOperationTb == null || StringUtils.isBlank(bindOperationTb.getSerialNumber())  ||
                !deviceInfoService.checkIfDeviceUse4G(bindOperationTb.getSerialNumber())){
            //没有4G设备
            boolean receiveVip = userVipService.isTierReceived(userId,request.getApp().getTenantId());
            if(oldUser && receiveVip){
                //之前领取或者购买过套餐
                return TierFreeUserExpireDO.builder()
                        .notify(false)
                        .build();
            }

            List<String> serialNumList = userRoleService.getUserSerialNumberByUserId(userId).stream().filter(sn ->
                    device4GService.queryDevice4GSimDO(sn) == null
            ).collect(Collectors.toList());
            log.debug("serialNumList is: {}", CollectionUtils.isEmpty(serialNumList));
            boolean existDeviceWithOutVip = !CollectionUtils.isEmpty(serialNumList) &&
                    serialNumList.stream().anyMatch(serialNum -> !vipService.isVipDevice(userId, serialNum));

            //未领取套餐且有设备绑定的才需要提醒
            return TierFreeUserExpireDO.builder()
                    .notify((oldUser && !CollectionUtils.isEmpty(serialNumList))
                            || (!oldUser && !CollectionUtils.isEmpty(serialNumList) && (!receiveVip || existDeviceWithOutVip)))
                    .hasDevice4G(false)
                    .recommendProductType(0)
                    .build();
        }else{
            //有4G设备套餐
            Set<Integer> tierId4GSet = tierService.getTierIdByTierServiceType(request.getApp().getTenantId(), TierServiceTypeEnums.TIER_4G.getCode());

            // 指定套餐的设备
            Set<String> inTier4gDeviceSet = userTierDeviceService.queryUserTierDeviceDOListByUser(userId,request.getApp().getTenantId()).stream()
                    .filter(userTierDeviceDO -> tierId4GSet.contains(userTierDeviceDO.getTierId()))
                    .map(UserTierDeviceDO::getSerialNumber).collect(Collectors.toSet());


            return TierFreeUserExpireDO.builder()
                    .notify(!device4gList.equals(inTier4gDeviceSet))
                    .hasDevice4G(true)
                    .recommendProductType(1)
                    .operationId(bindOperationTb.getOperationId())
                    .containsOfficialSim(deviceInfoService.containsOfficialSim(userId))
                    .build();
        }
    }

    @Override
    public TierFreeNotifyPeriodDO inProtectionPeriod(Integer userId) {
        return null;
    }

    @Override
    public void updateProtectionPeriod(Integer userId) {

    }

    /**
     * 返回弹出次数时间配置
     * @param userId
     * @return
     */
    public Map<Integer,Integer> initNotifyConfig(Integer userId){
        //新老config 切换灰度
        boolean satisfyRule = this.verifyGrayscale(userId);
        return satisfyRule ? tierReminderConfig.getConfigV1() : tierReminderConfig.getConfig();
    }


}
