package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.AliveRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.WowconfigCache;
import com.addx.iotcamera.config.Wowconfig;
import com.addx.iotcamera.config.device.DeviceDtimConfig;
import com.addx.iotcamera.config.device.DeviceGlobalConfig;
import com.addx.iotcamera.constants.DeviceReportKeyConstants;
import com.addx.iotcamera.constants.ReportLogConstants;
import com.addx.iotcamera.enums.EProtocol;
import com.addx.iotcamera.kiss.bean.KissParams;
import com.addx.iotcamera.kiss.enums.EHeartbeatReplyMode;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.KeepAliveRequest;
import com.addx.iotcamera.publishers.vernemq.responses.WowConfigResponse;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceWowConfigParamService;
import com.addx.iotcamera.service.device.model.DeviceModelConfigService;
import com.addx.iotcamera.util.BeanUtil;
import com.addx.iotcamera.util.MapUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.collect.ImmutableRangeMap;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.gson.Gson;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.constants.DeviceInfoConstants.DEVICE_KEEP_ALIVE_PREFIX;
import static com.addx.iotcamera.util.DTIMUtil.calculateDTIMFatigue;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_UNACTIVATED;


@Component
public class UdpService {
    @Autowired
    private RedisService redisService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Autowired
    private ReportLogService reportLogService;

    @Resource
    private IKissService kissService;

    @Resource
    private DeviceManualService deviceManualService;

    @Autowired
    private Wowconfig wowconfig;

    @Autowired
    private DeviceGlobalConfig deviceGlobalConfig;

    @Resource
    private StateMachineService stateMachineService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceDtimConfig deviceDtimConfig;

    @Resource
    private DeviceWowConfigParamService deviceWowConfigParamService;

    @Autowired
    @Lazy
    private DeviceModelConfigService deviceModelConfigService;

    private static final Logger LOGGER = LoggerFactory.getLogger(UdpService.class);

    private static final int KEEP_ALIVE_INTERVAL_BUFFER = 5;
    private static final String DEFAULT_DEVICE_GLOBAL_MODEL = "CG1";
    public static final RangeMap<Double, Integer> DYNAMIC_DTIM_RANGE = ImmutableRangeMap.<Double, Integer>builder()
            .put(Range.greaterThan(40.0), 1)
            .put(Range.closed(30.0, 40.0), 3)
            .put(Range.closedOpen(20.0,30.0), 5)
            .put(Range.lessThan(20.0), 7)
            .build();

    @SentinelResource("keepalive")
    public Result keepAlive(Integer userId, AliveRequest request) {
        if (request.getSeconds() < 0) {
            return Result.Error(-101, "Can not keep alive for less than 0 seconds");
        }

        String serialNumber = request.getSerialNumber();

        Map<String, Object> commonInfoMap = MapUtil.builder()
                .put("userId", userId)
                .put("serialNumber", serialNumber)
                .put("modelNo", deviceManualService.getModelNoBySerialNumber(serialNumber))
                .put("seconds", request.getSeconds())
                .build();
        // 记录准备发送保活请求
        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_PREPARE_KEEPALIVE,
                MapUtil.builder().putAll(commonInfoMap).put("msg", "准备发送保活请求").build());

        String keepAliveRequestId = CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID();

        LOGGER.info(String.format("=========== Try to awake %s for %d seconds. KeepAliveRequestId ID: %s", serialNumber, request.getSeconds(), keepAliveRequestId));

        KeepAliveRequest keepAliveRequest = KeepAliveRequest.builder()
                .id(keepAliveRequestId)
                .time(PhosUtils.getUTCStamp())
                .duration(request.getSeconds())
                .build();
        redisService.setDeviceOperationDOWithEmpty(keepAliveRequest.getId());
        LOGGER.info("Stored keep alive request {} to redis", keepAliveRequest.getId());

        DeviceOperationDO storedOperation;

        // 调用微服务接口 唤醒设备
        kissService.wakeupDevice(serialNumber, "");

        boolean verify = hasDeviceRecord(serialNumber);
        if(verify){
            LOGGER.info("过滤指定设备{}",serialNumber);
            return Result.Success();
        }

        int awakeTimout = 12; // 唤醒最多等12秒

        try {
            for (int i = 0; i < awakeTimout * 10; i++) {
                if (i % 10 == 0) { // 每秒钟重试KeepAlive
                    VernemqPublisher.keepAlive(serialNumber, keepAliveRequest);
                    LOGGER.info("Sending keep alive request {} to device {}", keepAliveRequestId, serialNumber);

                    // 记录发送keepalive唤醒设备
                    reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_KEEPALIVE_LOOP_SEND,
                            MapUtil.builder()
                                    .putAll(commonInfoMap)
                                    .put("msg", "发送保活信息，等待回复")
                                    .put("stateId", stateMachineService.getStateIdBySn(serialNumber))
                                    .build());
                }
                // 每0.1 秒检查 keepAliveRequestId
                storedOperation = redisService.getDeviceOperationDo(keepAliveRequestId);
                if (storedOperation != null) {
                    LOGGER.info(String.format("=========== Device %s has received keepAlive request with ID : %s.", serialNumber, keepAliveRequestId));
                    redisService.dropDeviceOperationDo(keepAliveRequestId);

                    // 记录设备回复keepalive
                    reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_KEEPALIVE_LOOP_ANSWER,
                            MapUtil.builder().putAll(commonInfoMap).put("msg", "成功收到回执，保活完成").build());
                    return Result.Success();
                }
                Thread.sleep(100L);
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to wakeup device {}", serialNumber, e);
            return Result.Error(ResultCollection.FATAL_ERROR, "Unable to wakeup device");
        }

        // 记录设备没有回复keepalive
        reportLogService.sysReportLive(ReportLogConstants.REPORT_TYPE_KEEPALIVE_LOOP_NO_ANSWER,
                MapUtil.builder().putAll(commonInfoMap).put("msg", "没有收到保活回执").build());

        return ResultCollection.DEVICE_NO_RESPONSE.getResult();
    }

    public Result handleWowConfigRequest(DeviceRequestDO sleepRequest) throws MqttException, IdNotSetException {
        String serialNumber = sleepRequest.getSerialNumber();

        WowConfigResponse rsp = new WowConfigResponse();
        rsp.setId(sleepRequest.getRequestId());
        rsp.setTime(PhosUtils.getUTCStamp());
        rsp.setRetryInterval(wowconfig.getRetryInterval());
        rsp.setRetryCount(wowconfig.getRetryCount());

        rsp.setBan1Ref(wowconfig.getBan1Ref());
        rsp.setBan2Ref(wowconfig.getBan2Ref());
        rsp.setBan3Ref(wowconfig.getBan3Ref());


        // TODO 这个方法有问题， 返回的信息之后device表里的， 但是DeviceDO里还有额外的很多字段 在这个方法里没有被初始化
        // TODO 应该放到service层，真正的成为"all info"
        DeviceDO storedDevice = deviceService.getAllDeviceInfo(serialNumber);
        if (null == storedDevice || 0 == storedDevice.getActivated()) {
            // 2019.11.25 重做绑定逻辑后，不再向G0摄像头返回的MQTT消息中设置错误的状态码
            rsp.setDeviceStatus(DEVICE_UNACTIVATED.getCode());
            VernemqPublisher.wowConfigResponse(serialNumber, rsp);
            return Result.Error(ResultCollection.DEVICE_NO_ACCESS);
        }

        // 根据配置构造udp信息
        // 这些信息用来返回给设备，设备会保存对应信息以供后续操作
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        EProtocol protocol = EProtocol.findByStrValue(cloudDeviceSupport.getSupportKeepAliveProtocol());

        rsp.setDtim(calculateDynamicDTIM(serialNumber,modelNo));
        // 目前允许的协议是 udp和tcp
        if (protocol != null) {
            try {
                KissParams kissParams = kissService.getKissParams(serialNumber, protocol,
                        EHeartbeatReplyMode.findByValue(sleepRequest.getHeartbeatReplyMode()));
                rsp.setAddr(kissParams.getAddrList().get(0).getIp());
                rsp.setPort(kissParams.getAddrList().get(0).getPort());
                rsp.setKeepalive(kissParams.getHeartbeat());
                rsp.setWakeup(kissParams.getWakeup());
                rsp.setKissParams(kissParams);
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, e.getMessage(), e);
            }

        } else {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "错误的协议内容: {}, sn:{}", cloudDeviceSupport.getSupportKeepAliveProtocol(), serialNumber);
        }

        final DeviceGlobal deviceGlobal = getDeviceGlobalByModelNo(modelNo);
        rsp.setInterval(deviceGlobal.getInterval());
        rsp.setTimeout(deviceGlobal.getTimeout());
        rsp.setMinInterval(deviceGlobal.getMinInterval());
        rsp.setMaxInterval(deviceGlobal.getMaxInterval());

        // 发送信息给设备
        boolean operationRes = false;
        try {
            this.changeDeviceConfigWhiteList(serialNumber, rsp);
            validateWowConfigParams(rsp, deviceGlobal);
            operationRes = VernemqPublisher.wowConfigResponse(serialNumber, rsp);
        } catch (Exception ex) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "Failed to send message to MQTT for sleep request from devices", ex);
        }

        return Result.OperationResult(operationRes);
    }

    /**
     * 白名单内设备更改参数
     *
     * @param serialNumber
     * @param rsp
     */
    private void changeDeviceConfigWhiteList(String serialNumber, WowConfigResponse rsp) {
        //先验证sn 有无设置wowconfig
        Set<String> supportWowConfigCache = deviceWowConfigParamService.querySupportWowConfigCache();
        if(CollectionUtils.isEmpty(supportWowConfigCache) || !supportWowConfigCache.contains(serialNumber)){
            return ;
        }

        //从apollo配置改成管理后台设置，客户成功同事可以自己操作
        WowconfigCache wowconfigCache = deviceWowConfigParamService.queryWowConfigCache(serialNumber);
        if (wowconfigCache == null) {
            return;
        }

        Optional.ofNullable(wowconfigCache.getRetryInterval()).ifPresent(rsp::setRetryInterval);
        Optional.ofNullable(wowconfigCache.getRetryCount()).ifPresent(rsp::setRetryCount);
        Optional.ofNullable(wowconfigCache.getDtim()).ifPresent(rsp::setDtim);
        Optional.ofNullable(wowconfigCache.getInterval()).ifPresent(rsp::setInterval);
        Optional.ofNullable(wowconfigCache.getTimeout()).ifPresent(rsp::setTimeout);
    }

    /**
     * 计算返回的dtim 映射值
     * @param serialNumber
     * @param modelNo
     * @return
     */
    public int calculateDynamicDTIM(String serialNumber,String modelNo) {
        try {
            Object storedDeviceDTIMFatigue = redisService.getDTIMValue(serialNumber);
            if (storedDeviceDTIMFatigue == null) {
                return this.getDeviceDtim(modelNo);
            } else {
                Gson gson = new Gson();
                DeviceDTIMFatigue deviceDTIMFatigue = gson.fromJson(storedDeviceDTIMFatigue.toString(), DeviceDTIMFatigue.class);
                LOGGER.debug("Current dtim fatigue: {} for serialNumber: {}", deviceDTIMFatigue.getValue(), serialNumber);
                return DYNAMIC_DTIM_RANGE.get(calculateDTIMFatigue(deviceDTIMFatigue));
            }
        } catch (Exception ex) {
            return this.getDeviceDtim(modelNo);
        }
    }

    /**
     * 从配置中获取dtim 配置
     * @param modelNo
     * @return
     */
    private Integer getDeviceDtim(String modelNo){
       return deviceDtimConfig.queryDeviceDtimValue(modelNo);
    }

    private void validateWowConfigParams(WowConfigResponse wowConfigResponse, DeviceGlobal defaultDeviceGlobal) {
        // 当前是为了保证，设备的心跳发送间隔 > 设备的重试间隔 * 重试次数 + buffer，防止设备在重试阶段异常
        if (wowConfigResponse.getInterval() < wowConfigResponse.getRetryInterval() * wowConfigResponse.getRetryCount() + KEEP_ALIVE_INTERVAL_BUFFER) {
            wowConfigResponse.setInterval(defaultDeviceGlobal.getInterval());
            wowConfigResponse.setRetryCount(wowconfig.getRetryCount());
            wowConfigResponse.setRetryInterval(wowconfig.getRetryInterval());
        }
    }

    /**
     * 验证是否需要过滤设备
     * @param serialNumber
     */
    private boolean hasDeviceRecord(String serialNumber){
        String key = DEVICE_KEEP_ALIVE_PREFIX.replace("{serialNumber}",serialNumber);
        if(redisService.hasDeviceOperationDo(key)){
            //直接返回
            return true;
        }

        redisService.set(key,"1",10);
        return false;
    }

    public DeviceGlobal getDeviceGlobalByModelNo(String modelNo) {
        final DeviceGlobal rawDeviceGlobal = Optional.ofNullable(modelNo).map(deviceGlobalConfig.getConfig()::get)
                .orElseGet(() -> deviceGlobalConfig.getConfig().get(DEFAULT_DEVICE_GLOBAL_MODEL));
        final DeviceGlobal deviceGlobal = BeanUtil.override(rawDeviceGlobal, new DeviceGlobal()); // 不修改原对象
        final DeviceModel deviceModel = Optional.ofNullable(modelNo).filter(StringUtils::isNotBlank)
                .map(deviceModelConfigService::queryRowDeviceModelByModelNo).orElse(null);
        if (deviceModel != null) {
            if (deviceModel.getKeepAliveInterval() != null && deviceModel.getKeepAliveTimeout() != null) {
                deviceGlobal.setInterval(deviceModel.getKeepAliveInterval()).setTimeout(deviceModel.getKeepAliveTimeout());
            }
            if (DeviceModel.validateIntervalRange(deviceModel.getMinKeepAliveInterval(), deviceModel.getMaxKeepAliveInterval())) {
                deviceGlobal.setMinInterval(deviceModel.getMinKeepAliveInterval()).setMaxInterval(deviceModel.getMaxKeepAliveInterval());
            }
        }
        // 如果配置的interval范围不合法，则使用现有的interval值
        if (!DeviceModel.validateIntervalRange(deviceGlobal.getMinInterval(), deviceGlobal.getMaxInterval())) {
            deviceGlobal.setMinInterval(deviceGlobal.getInterval()).setMaxInterval(deviceGlobal.getInterval());
        }
        return deviceGlobal;
    }

}
