package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.helper.DeviceOperationHelper;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.AlarmRequest;
import com.addx.iotcamera.service.NotificationService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.PhosUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.service.device.DoorbellService.getNotifyMsgType;
import static com.addx.iotcamera.service.device.DoorbellService.getNotifySwitch;

/**
 * 设备打电话service
 */
@Service
@Slf4j
public class DeviceCallService {

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Autowired
    @Lazy
    private VideoService videoService;
    @Autowired
    private DeviceSettingService deviceSettingService;

    @Resource
    private RedisService redisService;

    @Resource
    private IKissService kissService;

    @Resource
    private DeviceOperationHelper deviceOperationHelper;

    /**
     * 按压门铃
     *
     * @param traceId
     * @param sn
     */
    public void call(String traceId, String sn) {
        videoService.updateVideoReportEvents(sn, traceId, Arrays.asList(EReportEvent.DEVICE_CALL));
        // 推送
        final DeviceSettingsDO settings = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
        if (getNotifySwitch(settings)) {
            notificationService.notifyVideoReportEvent(traceId, sn, EReportEvent.DEVICE_CALL, getNotifyMsgType(settings));
        }
    }

    /**
     * 设备下发 cancelAlarm
     * @param serialNumber
     * @return
     * @throws IdNotSetException
     */
    public boolean deviceCancelAlarm(String serialNumber) throws IdNotSetException {
        kissService.wakeupDevice(serialNumber, "");

        boolean deviceWakeup = deviceOperationHelper.waitDeviceWake(serialNumber);
        if(!deviceWakeup){
            com.addx.iotcamera.util.LogUtil.warn(log, "设备{}唤醒失败",serialNumber);
            return false;
        }

        AlarmRequest alarmRequest = new AlarmRequest();
        alarmRequest.setId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        alarmRequest.setTime(PhosUtils.getUTCStamp());
        alarmRequest.setName("cancelAlarm");
        redisService.setDeviceOperationDOWithEmpty(alarmRequest.getId());
        VernemqPublisher.alarm(serialNumber, alarmRequest);
        return true;
    }
}