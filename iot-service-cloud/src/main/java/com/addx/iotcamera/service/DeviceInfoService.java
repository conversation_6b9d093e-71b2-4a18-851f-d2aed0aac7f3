package com.addx.iotcamera.service;


import com.addx.iotcamera.bean.app.DeviceRequest;
import com.addx.iotcamera.bean.app.LibraryRequest;
import com.addx.iotcamera.bean.app.ListUserDevicesRequest;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalUserTierInfo;
import com.addx.iotcamera.bean.app.device.DeviceConfigRequest;
import com.addx.iotcamera.bean.app.device.DeviceLocationRequest;
import com.addx.iotcamera.bean.app.device.DeviceModelMatchResult;
import com.addx.iotcamera.bean.app.device.home.DeviceHomeRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.db.MessageNotificationSetting;
import com.addx.iotcamera.bean.db.SwitchWifiOperation;
import com.addx.iotcamera.bean.db.device.DeviceHomeModeDO;
import com.addx.iotcamera.bean.device.*;
import com.addx.iotcamera.bean.device.attributes.DeviceDormancyInfo;
import com.addx.iotcamera.bean.device.attributes.DeviceOnlineInfo;
import com.addx.iotcamera.bean.device.attributes.OptionEnumMapping;
import com.addx.iotcamera.bean.device.model.DeviceModelIconDO;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.device.*;
import com.addx.iotcamera.bean.domain.library.LibraryCountDay;
import com.addx.iotcamera.bean.domain.report.ReportInfo;
import com.addx.iotcamera.bean.domain.uservip.AiBirdDevice;
import com.addx.iotcamera.bean.domain.uservip.UserDeviceFreeTierDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.bean.openapi.OpenApiDeviceConfig;
import com.addx.iotcamera.bean.response.device.DevicePushImage;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.bean.video.UploadVideoBeginRequest;
import com.addx.iotcamera.bean.video.VideoMsgType;
import com.addx.iotcamera.config.PowerStatConfig;
import com.addx.iotcamera.config.apollo.CenterNotifyConfig;
import com.addx.iotcamera.config.apollo.DnsServerConfig;
import com.addx.iotcamera.config.device.DeviceBindFirmwareConfig;
import com.addx.iotcamera.constants.CopyWriteConstans;
import com.addx.iotcamera.constants.DeviceInfoConstants;
import com.addx.iotcamera.constants.DeviceReportKeyConstants;
import com.addx.iotcamera.dao.IDeviceDAO;
import com.addx.iotcamera.dao.MessageNotificationSettingsDao;
import com.addx.iotcamera.dao.SwitchWifiOperationDAO;
import com.addx.iotcamera.dao.device.IDeviceApInfoDAO;
import com.addx.iotcamera.dao.device.IDeviceSettingDAO;
import com.addx.iotcamera.dao.factory.DeviceBindInfoDao;
import com.addx.iotcamera.enums.*;
import com.addx.iotcamera.enums.device.DeviceModelCategoryEnums;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.helper.DeviceDetectHelper;
import com.addx.iotcamera.helper.ProcessLogHelper;
import com.addx.iotcamera.helper.TraceIdHelper;
import com.addx.iotcamera.helper.aws.AwsHelper;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.publishers.vernemq.MqttResponsePackage;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.responses.WakeupResponse;
import com.addx.iotcamera.service.device.*;
import com.addx.iotcamera.service.device.home.DeviceHomeModeService;
import com.addx.iotcamera.service.device.home.DeviceHomeModeSettingService;
import com.addx.iotcamera.service.device.home.DeviceHomeService;
import com.addx.iotcamera.service.device.model.*;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService;
import com.addx.iotcamera.service.factory.DeviceModelService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.firmware.FirmwareService;
import com.addx.iotcamera.service.openapi.OpenApiWebhookService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoNotifyService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.service.vip.DeviceExchangecodeTierService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.util.*;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.DeviceDisconnectedReasonEnums;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffset;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffsetResponse;
import org.addx.iot.common.utils.EnumFindUtil;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.service.IDeviceInfoService;
import org.addx.iot.domain.extension.ai.enums.AiActionEnum;
import org.addx.iot.domain.extension.ai.enums.AiObjectEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.addx.iotcamera.bean.device.attributes.OptionEnumMapping.getEnumNames;
import static com.addx.iotcamera.constants.DeviceReportKeyConstants.DEVICE_AWAKE_109_ERROR_KEY;
import static com.addx.iotcamera.constants.PayConstants.FREE_LICENSE_7_DAY;
import static com.addx.iotcamera.constants.PayConstants.TIER_NAME_KEY_AI_BIRD_DEVICE;
import static com.addx.iotcamera.constants.ReportLogConstants.*;
import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.util.DTIMUtil.calculateDTIMFatigue;
import static org.addx.iot.common.enums.ResultCollection.*;

@Component
@Slf4j
public class DeviceInfoService implements IDeviceInfoService {

    private static Logger LOGGER = LoggerFactory.getLogger(DeviceInfoService.class);
    public final static String DEVICE_SUPPORT_KEY_PRE = "deviceSupport:{serialNumber}";
    private final static String DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE = "deviceSupportPirSliceReport:{serialNumber}";
    public final static String EVENT_TRACKING_SWITCH_KEY_PRE = "eventTrackingSwitch:{serialNumber}";

    private final static double BASE_HALF_LIFE_VALUE = 1.0;
    /**
     * 设备固件构建号-过期时间
     */
    private final static int DEVICE_FIRMWARE_BUILDER_EXPIRE = 2 * 12 * 30 * 24 * 60 * 60;

    @Value("${oldDeviceSSlTimeOffset:0}")
    private long oldDeviceSSlTimeOffset;

    @Value("${oldDeviceResponseTimestamp:0}")
    private long oldDeviceResponseTimestamp;

    @Autowired
    private IDeviceDAO deviceDAO;

    @Autowired
    private IDeviceSettingDAO deviceSettingDAO;

    @Autowired
    private WowzaService wowzaService;

    @Lazy
    @Autowired
    private VideoService videoService;

    @Autowired
    private OpenApiWebhookService openApiWebhookService;
    @Autowired
    private PushService pushService;
    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserService userService;

    @Autowired
    private DeviceStatusService deviceStatusService;

    @Autowired
    private LocationInfoService locationInfoService;

    @Autowired
    private DeviceOTAService deviceOTAService;

    @Autowired
    private FirmwareService firmwareService;

    @Autowired
    private RoleDefinitionService roleDefinitionService;

    @Autowired
    private DeviceSdCardStatusService deviceSdCardStatusService;

    @Autowired
    private SwitchWifiOperationDAO switchWifiOperationDAO;

    @Autowired
    private DeviceModelEventService deviceModelEventService;

    @Autowired
    private AdditionalUserTierService additionalUserTierService;

    @Value("${packagePushSwitch:false}")
    private boolean packagePushSwitch;

    @Autowired
    private ReportLogService reportLogService;

    @Autowired
    private DeviceSettingService deviceSettingService;

    @Autowired
    private DeviceAiSettingsService deviceAiSettingsService;

    @Autowired
    private StateMachineService stateMachineService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PersistentRedisService persistentRedisService;

    @Autowired
    private DeviceSupportService deviceSupportService;

    @Autowired
    private VernemqPublisher vernemqPublisher;

    @Autowired
    private DeviceModelIconService deviceModelIconService;

    @Resource
    private DeviceManualService deviceManualService;

    @Autowired
    private TenantSettingService tenantSettingService;

    @Setter
    @Autowired
    private Gson gson;

    @Autowired
    private DnsServerConfig dnsServerConfig;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceDetectHelper deviceDetectHelper;

    @Autowired
    private IKissService kissService;

    @Autowired
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired
    private CenterNotifyConfig centerNotifyConfig;

    @Autowired
    private TraceIdHelper traceIdHelper;
    @Resource
    private DeviceModelConfigService deviceModelConfigService;
    @Autowired
    private VipService vipService;

    @Autowired
    private DeviceExchangecodeTierService deviceExchangecodeTierService;
    @Autowired
    private DeviceBindInfoDao deviceBindInfoDao;

    @Autowired
    private ProductExchangeCodeService productExchangeCodeService;

    @Autowired
    private DeviceConfigService deviceConfigService;

    @Autowired
    private FactoryDataQueryService factoryDataQueryService;

    @Autowired
    @Lazy
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Autowired(required = false)
    private AlexaService alexaService;

    @Autowired(required = false)
    private GoogleHomeService googleHomeService;

    @Resource
    private DeviceBindFirmwareConfig deviceBindFirmwareConfig;

    @Autowired
    private IDeviceApInfoDAO deviceApInfoDAO;

    @Autowired
    private DoorbellService doorbellService;

    @Resource
    private DeviceModelService deviceModelService;

    @Autowired
    private DeviceEnumMappingService deviceEnumMappingService;

    @Autowired
    private DeviceCallService deviceCallService;

    @Autowired
    private PowerStatConfig powerStatConfig;

    @Autowired
    private UserVipService userVipService;

    @Autowired
    private VideoGenerateService videoGenerateService;

    @Resource
    private DeviceModelTenantService deviceModelTenantService;

    public static ThreadLocal<EReportEventSource> reportEventSourceThreadLocal = new ThreadLocal<>();
    public static ThreadLocal<String> reportEventSourceIpThreadLocal = new ThreadLocal<>();


    @Autowired
    private Device4GService device4GService;

    @Resource
    @Lazy
    private VideoNotifyService videoNotifyService;
    @Autowired
    private DeviceFloodLightService deviceFloodLightService;

    @Resource
    private UserTierDeviceService userTierDeviceService;
    @Resource
    private FreeLicenseService freeLicenseService;
    @Autowired
    private LibraryService libraryService;
    @Autowired
    @Lazy
    private VideoStoreService videoStoreService;

    @Resource
    private DeviceHomeService deviceHomeService;

    @Resource
    @Lazy
    private NotificationService notificationService;

    @Resource
    private DeviceHomeModeService deviceHomeModeService;

    @Resource
    private DeviceHomeModeSettingService deviceHomeModeSettingService;
    @Qualifier("messageNotificationSettingsDao")
    @Autowired
    private MessageNotificationSettingsDao messageNotificationSettingsDao;

    public boolean getEventTrackingSwitch(String serialNumber) {
        String key = EVENT_TRACKING_SWITCH_KEY_PRE.replace("{serialNumber}", serialNumber);
        return "false".equals(redisService.get(key)) ? false : true;
    }

    public DeviceDO getSingleDevice(DeviceDO queryDeviceDO,Integer userId) {
        DeviceDO device = deviceService.getAllDeviceInfo(queryDeviceDO.getSerialNumber());
        if (device == null) {
            return null;
        }
        List<DeviceDO> deviceDOListParam = Lists.newArrayList(device);
        List<DeviceDO> deviceDOListResult = listDevicesByUserId(queryDeviceDO.getUserId(), deviceDOListParam);
        if (!CollectionUtils.isEmpty(deviceDOListResult)) {
            //带有序列号的情况，最多只会返回一条
            device = deviceDOListResult.get(0);
            device.setDisplayGitSha(this.getDeviceFirmwareBuilder(device.getSerialNumber()));

            getDeviceInfoFromSetting(device);

            if (StringUtils.isEmpty(device.getDefaultCodec()) && !StringUtils.isEmpty(device.getCodec())) {
                device.setDefaultCodec(device.getCodec().contains(DeviceCodecUtil.H265) ? DeviceCodecUtil.H265 : DeviceCodecUtil.H264);
            }
            if ((device.getShowCodecChange() != null && device.getShowCodecChange().booleanValue() == true) && StringUtils.isEmpty(device.getDefaultCodec())) {
                device.setDefaultCodec(DeviceCodecUtil.H265);
            }
        } else {
            return null;
        }

        device.setDeviceModel(processDeviceModelForWebrtc(device.getSerialNumber(), device.getDeviceSupport()));
        device.setSdCard(deviceSdCardStatusService.querySdCard(queryDeviceDO.getSerialNumber()));

        device.setShowCodecChange(getSupportChangeCodec(device));
        return device;
    }

    public boolean getSupportChangeCodec(DeviceDO device) {
        if (device.getDeviceSupport() != null && device.getDeviceSupport().getSupportChangeCodec().intValue() == 1) {
            if (alexaService != null && alexaService.canAccessAlexa(device)) {
                return true;
            } else if (googleHomeService != null && googleHomeService.canAccessGoogleHome(device)) {
                return true;
            }
        }
        return false;
    }

    public void setDeviceSupport(DeviceDO deviceDO) {
        CloudDeviceSupport cloudDeviceSupport = getDeviceSupport(deviceDO.getSerialNumber());
        if (cloudDeviceSupport != null) {
            /*
            后端用supportSdCardCooldown判断是否应该展示sdCardCooldownSwitch开关
            app端用supportSdCardCooldown判断是否应该使用新的sd卡回看协议
            产品希望无论app使用新老sdk回看协议，都可以有sdCardCooldownSwitch开关
            后端涉及supportSdCardCooldown的判断逻辑不变，给app端下发固定值null
             */
            // 新版sd卡需求，如果SupportSdCardCooldown为0或者1的时候下发固定值null，
            // 为2的时候不下发null,目前没有大于2的情况
            if (cloudDeviceSupport.getSupportSdCardCooldown() != null && cloudDeviceSupport.getSupportSdCardCooldown() < 2) {
                cloudDeviceSupport.setSupportSdCardCooldown(null);
            }
            deviceDO.setDeviceSupport(cloudDeviceSupport);
        }
    }

    // 获取原始的设备支持项,不包含其他表的信息
    // (deviceModel会填充deviceSupport的信息，有可能死循环)
    public CloudDeviceSupport getRowDeviceSupport(String serialNumber) {
        String key = DEVICE_SUPPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        String value = redisService.getFromSlave(key);
        final CloudDeviceSupport cloudDeviceSupport;
        if (value == null || !JSON.isValidObject(value)) {
            cloudDeviceSupport = deviceSupportService.queryDeviceSupportBySn(serialNumber);
            if (cloudDeviceSupport != null) {
                // 把mysql数据写入redis时，有小概率会覆盖mqtt上传的新值。因此设置一个缓存时间，下次查mysql查到的就是最新值。
                redisService.set(key, gson.toJson(cloudDeviceSupport), 60 * 10);
            }
        } else {
            //默认会把jsonjbect属性里的 int转成float，需要处理下
            Gson gson = new GsonBuilder()
                    .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
                    .create();
            cloudDeviceSupport = gson.fromJson(value, CloudDeviceSupport.class);
        }
        return cloudDeviceSupport;
    }

    // 获取设备支持项，填充了其他表的信息
    public CloudDeviceSupport getDeviceSupport(String serialNumber) {
        CloudDeviceSupport cloudDeviceSupport = getRowDeviceSupport(serialNumber);
        if (cloudDeviceSupport == null) {
            cloudDeviceSupport = new CloudDeviceSupport(); // 创建一个空的deviceSupport对象
        }
        inflateDeviceSupport(serialNumber, cloudDeviceSupport); // 填充其它对象中的信息
        return cloudDeviceSupport;
    }

    
    public Boolean checkIfDeviceUse4G(String serialNumber) {
        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(serialNumber);
        return device4GSimDO != null;
    }



    /**
     * 给deviceSupport填充其它对象中的信息（deviceStatus,deviceModel)
     */
    public void inflateDeviceSupport(final String serialNumber, final CloudDeviceSupport cloudDeviceSupport) {
        if (cloudDeviceSupport.getSupportPirSliceReport() == null) {
            cloudDeviceSupport.setSupportPirSliceReport(getDeviceSupportPirSliceReport(serialNumber));
        }
        /* todo deviceStatus中部分字段转移到deviceSupport中，老的deviceSupport中没有，需要兼容，下次上线去掉 */
        final Supplier<Optional<DeviceStatusDO>> deviceStatusOnce = FuncUtil.getOnce(() -> deviceStatusService.queryDeviceStatusBySerialNumber(serialNumber));
        if (cloudDeviceSupport.getQuantityCharge() == null) {
            cloudDeviceSupport.setQuantityCharge(deviceStatusOnce.get().map(it -> it.getQuantityCharge()).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getAntiflickerSupport() == null) {
            // deviceStatus.antiflicker的命名有问题，其实就是来自于remoteDO.antiflickerSupport
            cloudDeviceSupport.setAntiflickerSupport(deviceStatusOnce.get().map(it -> it.getAntiflicker()).orElse(0));
        }
        /*** 如果固件没有上报，填充deviceModel中的数据  开始*/
        final Supplier<Optional<String>> modelNoOnce = FuncUtil.getOnce(() -> {
            return deviceManualService.getModelNoBySerialNumber(serialNumber);
        });
        final Supplier<Optional<DeviceModel>> deviceModelOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(deviceModelConfigService::queryRowDeviceModelByModelNo).orElse(null);
        });
        final Supplier<Optional<Integer>> modelCategoryOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(deviceModelService::queryDeviceModelCategory).orElse(null);
        });
        final Supplier<Optional<OptionEnumMapping>> enumMappingOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(deviceEnumMappingService::getEnumMappingByModelNo).orElse(null);
        });
        final Supplier<Optional<TenantSetting>> tenantSettingOnce = FuncUtil.getOnce(() -> {
            final String tenantId = Optional.ofNullable(userRoleService.getAdminUserBySn(serialNumber))
                    .filter(it -> it.getResult() == Result.successFlag).map(it -> it.getData().getTenantId()).orElse("");
            return tenantSettingService.getTenantSettingByTenantId(tenantId);
        });
        final Supplier<Optional<Integer>> magicPixOnce = FuncUtil.getOnce(() -> {
            return modelNoOnce.get().map(factoryDataQueryService::queryMagicPixByModelNo).orElse(null);
        });
        if (org.apache.commons.lang3.StringUtils.isBlank(cloudDeviceSupport.getSupportStreamProtocol())) {
            cloudDeviceSupport.setSupportStreamProtocol(deviceModelOnce.get().map(DeviceModel::getStreamProtocol).orElse(""));
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(cloudDeviceSupport.getSupportAudioCodectype())) {
            cloudDeviceSupport.setSupportAudioCodectype(deviceModelOnce.get().map(DeviceModel::getAudioCodectype).orElse(""));
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(cloudDeviceSupport.getSupportKeepAliveProtocol())) {
            cloudDeviceSupport.setSupportKeepAliveProtocol(deviceModelOnce.get().map(DeviceModel::getKeepAliveProtocol).orElse(""));
        }
        if (cloudDeviceSupport.getSupportCanStandby() == null) {
            cloudDeviceSupport.setSupportCanStandby(deviceModelOnce.get().map(DeviceModel::isCanStandby).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getSupportWhiteLight() == null) {
            cloudDeviceSupport.setSupportWhiteLight(deviceModelOnce.get().map(DeviceModel::isWhiteLight).orElse(false));
        }
        if (cloudDeviceSupport.getSupportDevicePersonDetect() == null) {
            cloudDeviceSupport.setSupportDevicePersonDetect(deviceModelOnce.get().map(DeviceModel::isDevicePersonDetect).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getCanRotate() == null) {
            cloudDeviceSupport.setCanRotate(deviceModelOnce.get().map(DeviceModel::isCanRotate).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getSupportMotionTrack() == null) {
            cloudDeviceSupport.setSupportMotionTrack(deviceModelOnce.get().map(DeviceModel::isSupportMotionTrack).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getSupportFrequency() == null) {
            cloudDeviceSupport.setSupportFrequency(deviceModelOnce.get().map(DeviceModel::isSupportFrequency).orElse(false) ? 1 : 0);
        }
        if (cloudDeviceSupport.getAntiDisassemblyAlarm() == null) {
            cloudDeviceSupport.setAntiDisassemblyAlarm(deviceModelOnce.get().map(DeviceModel::isAntiDisassemblyAlarm).orElse(false) ? 1 : 0);
        }
        /*** 以下是设备设置通用化一期 */
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getPirSensitivityOptions())) {
            cloudDeviceSupport.setPirSensitivityOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirSensitivityOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getPirRecordTimeOptions())) {
            cloudDeviceSupport.setPirRecordTimeOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirRecordTimeOptions())).orElseGet(Collections::emptyList));
        }
        // 云端拍摄时长(无论是设备上报还是后端默认值) 过滤掉tenantId维度配置的禁用选项
        tenantSettingOnce.get().map(it -> it.getPirRecordTimeDisabledOptions()).ifPresent(it -> {
            cloudDeviceSupport.setPirRecordTimeOptions(FuncUtil.subtractToList(cloudDeviceSupport.getPirRecordTimeOptions(), it));
        });
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getSdCardPirRecordTimeOptions())) {
            cloudDeviceSupport.setSdCardPirRecordTimeOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getSdCardPirRecordTimeOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getPirCooldownTimeOptions())) {
            cloudDeviceSupport.setPirCooldownTimeOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getPirCooldownTimeOptions())).orElseGet(Collections::emptyList));
        }
        // 云端拍摄间隔(无论是设备上报还是后端默认值) 过滤掉tenantId维度配置的禁用选项
        tenantSettingOnce.get().map(it -> it.getPirCooldownTimeDisabledOptions()).ifPresent(it -> {
            cloudDeviceSupport.setPirCooldownTimeOptions(FuncUtil.subtractToList(cloudDeviceSupport.getPirCooldownTimeOptions(), it));
        });
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getVideoResolutionOptions())) {
            cloudDeviceSupport.setVideoResolutionOptions(CloudDeviceSupport.getVideoResolutionOptions(cloudDeviceSupport.getDeviceSupportResolution()));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getVideoAntiFlickerFrequencyOptions())) {
            cloudDeviceSupport.setVideoAntiFlickerFrequencyOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getVideoAntiFlickerFrequencyOptions())).orElseGet(Collections::emptyList));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getLiveStreamCodecOptions())) {
            cloudDeviceSupport.setLiveStreamCodecOptions(DeviceCodecUtil.getCodecOptions(cloudDeviceSupport.getSupportChangeCodec()));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getNightVisionSensitivityOptions())) {
            cloudDeviceSupport.setNightVisionSensitivityOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getNightVisionSensitivityOptions())).orElseGet(Collections::emptyList));
        }
        if (cloudDeviceSupport.getSupportDoorbellPressNotifySwitch() == null) {
            cloudDeviceSupport.setSupportDoorbellPressNotifySwitch(modelCategoryOnce.get().map(it -> it == DeviceModelCategoryEnums.DOORBELL.getCode()).orElse(false));
        }
        /*** 如果固件没有上报，填充deviceModel中的数据 结束*/
        if (cloudDeviceSupport.getSupportLocalVideoLookBack() == null) {
            cloudDeviceSupport.setSupportLocalVideoLookBack(false); // 默认值不写入数据库
        }
        if (cloudDeviceSupport.getLocalVideoStorageType() == null) {
            cloudDeviceSupport.setLocalVideoStorageType(0);  // 默认值不写入数据库
        }
        if (DeviceAttributeIntRange.isEmpty(cloudDeviceSupport.getMechanicalDingDongDurationRange())) {
            cloudDeviceSupport.setMechanicalDingDongDurationRange(new DeviceAttributeIntRange(200, 1000, 100));
        }
        if (cloudDeviceSupport.getSupportSdCardFormat() == null) {
            cloudDeviceSupport.setSupportSdCardFormat(false);  // 默认值不写入数据库
        }
        if (cloudDeviceSupport.getSupportNightVisionSwitch() == null) {
            cloudDeviceSupport.setSupportNightVisionSwitch(true); // 必然有红外灯，不一定有白光灯
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getNightVisionModeOptions())) {
            cloudDeviceSupport.setNightVisionModeOptions(cloudDeviceSupport.getSupportWhiteLight() ? Arrays.asList("infrared", "white") : Arrays.asList("infrared"));
        }
        if (CollectionUtils.isEmpty(cloudDeviceSupport.getAlarmDurationOptions())) {
            cloudDeviceSupport.setAlarmDurationOptions(enumMappingOnce.get().map(it -> getEnumNames(it.getAlarmDurationOptions())).orElseGet(Collections::emptyList));
        }
        if (DeviceAttributeIntRange.isEmpty(cloudDeviceSupport.getAlarmVolumeRange())) {
            cloudDeviceSupport.setAlarmVolumeRange(new DeviceAttributeIntRange(10, 100, 1));
        }
        if (DeviceAttributeIntRange.isEmpty(cloudDeviceSupport.getVoiceVolumeRange())) {
            cloudDeviceSupport.setVoiceVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        }
        if (DeviceAttributeIntRange.isEmpty(cloudDeviceSupport.getLiveSpeakerVolumeRange())) {
            cloudDeviceSupport.setLiveSpeakerVolumeRange(new DeviceAttributeIntRange(0, 100, 1));
        }
        if (cloudDeviceSupport.getSupportAlarmFlashLight() == null) {
            cloudDeviceSupport.setSupportAlarmFlashLight(cloudDeviceSupport.getSupportWhiteLight()); // 支持白光灯就支持报警闪光灯
        }
        if (cloudDeviceSupport.getSupportIndoor() == null) {
            cloudDeviceSupport.setSupportIndoor(modelCategoryOnce.get().map(it -> it == DeviceModelCategoryEnums.DOORBELL.getCode()).orElse(false));
        }
        if (cloudDeviceSupport.getSupportStarlightSensor() == null) {
            cloudDeviceSupport.setSupportStarlightSensor(false);  // 默认值不写入数据库
        }
        if (cloudDeviceSupport.getSupportUnlimitedWebsocket() == null) {
            cloudDeviceSupport.setSupportUnlimitedWebsocket(false);  // 默认值不写入数据库
        }
        if (cloudDeviceSupport.getSupportMagicPix() == null) {
            magicPixOnce.get().map(FactoryDataQueryService::supportMagicPix).ifPresent(cloudDeviceSupport::setSupportMagicPix);
        }
        if (cloudDeviceSupport.getSupportPirRecordTime() == null) {
            cloudDeviceSupport.setSupportPirRecordTime(true); // 设备默认支持云端pir。4g设备不支持传false
        }
    }

    /**
     * 用户设备列表-不限定序列号
     *
     * @param userId
     * @return
     */
    public List<DeviceDO> listDevicesByUserId(Integer userId) {
        List<DeviceDO> devices = listDevicesByUserId(userId, null, null);
        devices.forEach(device -> {
            if(StringUtils.isNotBlank(device.getSerialNumber())){
                device.setDeviceModel(processDeviceModelForWebrtc(device.getSerialNumber(), device.getDeviceSupport()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(device.getModelNo())) {
                final Set<String> supportEvents = deviceModelEventService.queryRowDeviceModelEvent(device.getModelNo());
                device.setSupportBirdVip(supportEvents.contains(AiObjectEnum.BIRD.getName()));
            } else {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "listuserdevices modelNo is empty! userId={},sn={}", userId, device.getSerialNumber());
            }
        });
        return devices;
    }

    public List<DeviceDO> listDevicesByUserId(Integer userId, List<DeviceDO> deviceDOListParam) {
        return listDevicesByUserId(userId, deviceDOListParam, null);
    }

    /**
     * 用户设备列表
     *
     * @param userId
     * @return
     */
    public List<DeviceDO> listDevicesByUserId(Integer userId, List<DeviceDO> deviceDOListParam, Integer roleId) {
        User user = userService.queryUserById(userId);
        if (user == null) {
            //用户不存在
            return Lists.newArrayList();
        }
        List<DeviceDO> deviceDOList = Lists.newArrayList();
        List<UserRoleDO> userRoleDOList = userRoleService.getUserRoleByUserId(userId, roleId);
        if (CollectionUtils.isEmpty(userRoleDOList)) {
            return deviceDOList;
        }

        //获取设备序列号列表
        List<String> serialNumberList = Lists.newArrayList();
        //设备相关用户
        Set<Integer> userIdSet = Sets.newHashSet();
        //设备与用户关系map
        Map<String, UserRoleDO> mapSerialNumberToUserRole = Maps.newHashMap();
        //初始化需要关联其他表的参数
        userRoleDOList.forEach(userRoleDO -> {
            serialNumberList.add(userRoleDO.getSerialNumber());

            if (!userIdSet.contains(userRoleDO.getAdminId())) {
                userIdSet.add(userRoleDO.getAdminId());
            }
            if (!userIdSet.contains(userRoleDO.getUserId())) {
                userIdSet.add(userRoleDO.getUserId());
            }

            mapSerialNumberToUserRole.put(userRoleDO.getSerialNumber(), userRoleDO);
        });
        //设备记录,
        deviceDOList = CollectionUtils.isEmpty(deviceDOListParam) ? queDeviceBySerialNumbers(serialNumberList) : deviceDOListParam;
        if (CollectionUtils.isEmpty(deviceDOList)) {
            return deviceDOList;
        }
        // 对设备按绑定时间排序
        deviceDOList = this.deviceDOListSort(deviceDOList, userRoleDOList);

        //设备关联位置
        Set<Integer> locationSet = Sets.newHashSet();
        deviceDOList.forEach(deviceDO -> {
            if (!locationSet.contains(deviceDO.getLocationId())) {
                locationSet.add(deviceDO.getLocationId());
            }
        });

        Map<Long,String> deviceHomeNameMap = deviceHomeService.queryHomeNameBatch(deviceDOList.stream().map(DeviceDO::getHomeId).collect(Collectors.toList()));

        Map<Integer, LocationDO> locationDOMap = locationInfoService.queryLocationDOs(locationSet);
        //获取用户相关信息
        Map<Integer, User> mapUser = userService.queryUserMap(new ArrayList<>(userIdSet));
        //设备状态
        Map<String, DeviceStatusDO> mapDeviceStatusDO = deviceStatusService.queryDeviceStatus(serialNumberList);
        Map<String, DeviceStateDO> deviceStateDOMap = Collections.emptyMap();
        try {
            deviceStateDOMap = stateMachineService.batchGetDeviceState(serialNumberList);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "failed batchGetDeviceState serialNumberList:{}", serialNumberList);
        }

        //device_ota
        Map<String, DeviceOTADO> mapDeviceOta = deviceOTAService.queryDeviceOtaBySerialNumber(serialNumberList);
        // 租户设置
        final TenantSetting tenantSetting = tenantSettingService.getTenantSettingByTenantId(user.getTenantId());

        Map<String, DeviceFloodLightMsgModel> sn2FloodLightMsgMap = deviceFloodLightService.getFloodLightMsgMap(serialNumberList);

        // 获取所有支持的型号集合
        Set<String> allModelNos = productExchangeCodeService.getAllModelNos();

        //组装各部分信息
        int time = (int) (System.currentTimeMillis() / 1000);
        Iterator<DeviceDO> iterator = deviceDOList.iterator();
        while (iterator.hasNext()) {
            try {
                DeviceDO deviceDO = iterator.next();
                UserRoleDO userRoleDO = mapSerialNumberToUserRole.get(deviceDO.getSerialNumber());
                if (userRoleDO == null) {
                    // 当 deviceDOListParam 不为空时，deviceDOList有可能和mapSerialNumberToUserRole不一致
                    iterator.remove();
                    continue;
                }
                //设备所处位置
                fillDeviceLocation(deviceDO, locationDOMap,deviceHomeNameMap);
                //用户相关信息
                fillDeviceUserInfo(userId, deviceDO, userRoleDO, mapUser);
                //设备状态相关
                fillDeviceStatus(deviceDO, mapDeviceStatusDO, deviceStateDOMap, time, user);

                fillDeviceManufacture(deviceDO);
                //设备ota
                dillDeviceOta(deviceDO, mapDeviceOta);

                //初始化设备图标
                fillDeviceInfo(deviceDO);

                //设置设备维度vip等级
                deviceDO.setDeviceVipLevel(vipService.queryLastDeviceVip(user.getTenantId(), deviceDO.getSerialNumber()).getVipLevel());
                //设备4G流量套餐
                deviceDO.setDevice4GDataVip(vipService.deviceHas4GDataVip(userRoleDO.getAdminId(),checkIfDeviceUse4G(deviceDO.getSerialNumber()),deviceDO.getSerialNumber()));
                DeviceModelMatchResult deviceOrRelatedDevicesInModelSet = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(deviceDO.getSerialNumber(), allModelNos);
                deviceDO.setAiBirdDevice(AiBirdDevice.builder()
                        .isAiBirdDevice(deviceOrRelatedDevicesInModelSet.isMatched())
                        .hasExchanged(deviceExchangecodeTierService.queryDeviceExchangecodeTierBySerialNumber(deviceDO.getSerialNumber()) != null)
                        .tierNameKey(TIER_NAME_KEY_AI_BIRD_DEVICE).build());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(deviceDO.getModelNo())) {
                    final Set<String> supportEvents = deviceModelEventService.queryRowDeviceModelEvent(deviceDO.getModelNo());
                    deviceDO.setSupportBirdVip(supportEvents.contains(AiObjectEnum.BIRD.getName()));
                }
                this.initDeviceVip(deviceDO);

                // 查询设备音频设置
                if (deviceDO.getLiveAudioToggleOn() == null) {
                    DeviceConfigRequest.DeviceAudio deviceAudio = deviceConfigService.queryDeviceAudio(userId, deviceDO.getSerialNumber());
                    if (deviceAudio != null) {
                        deviceDO.setLiveAudioToggleOn(deviceAudio.getLiveAudioToggleOn());
                        deviceDO.setRecordingAudioToggleOn(deviceAudio.getRecordingAudioToggleOn());
                        deviceDO.setLiveSpeakerVolume(deviceAudio.getLiveSpeakerVolume());
                    }
                }

                deviceDO.setSdCard(deviceSdCardStatusService.querySdCard(deviceDO.getSerialNumber()));
                deviceDO.setCustomerId(factoryDataQueryService.queryDeviceCustomerIdByUserSn(deviceDO.getUserSn()));
                deviceDO.setEnableLiveChat(tenantSetting.getEnableLiveChatByCustomerId(deviceDO.getCustomerId()));

                if (Objects.nonNull(deviceDO.getDeviceSupport())
                        && BooleanUtils.isTrue(deviceDO.getDeviceSupport().getSupportManualFloodlightSwitch())) {
                    DeviceFloodLightMsgModel deviceFloodLightMsgModel = sn2FloodLightMsgMap.get(deviceDO.getSerialNumber());
                    if (Objects.nonNull(deviceFloodLightMsgModel)) {
                        deviceDO.setManualFloodlightSwitch(deviceFloodLightMsgModel.isSwitchOn());
                    }
                }
                if (Objects.nonNull(deviceDO.getDeviceSupport())
                        && BooleanUtils.isTrue(deviceDO.getDeviceSupport().getSupportManualFloodlightLuminance())) {
                    DeviceFloodLightMsgModel deviceFloodLightMsgModel = sn2FloodLightMsgMap.get(deviceDO.getSerialNumber());
                    if (Objects.nonNull(deviceFloodLightMsgModel)) {
                        deviceDO.setManualFloodlightLuminance(deviceFloodLightMsgModel.getLuminance());
                    }
                }
            }catch (Exception e){
                //避免出异常接口返回null
                log.error("listDevicesByUserId error, {}, ", userId, e);
            }
        }

        return deviceDOList;
    }

    /**
     * 设备列表按绑定时间排序
     *
     * @param sourceDeviceDOList
     * @param userRoleDOList
     * @return
     */
    private List<DeviceDO> deviceDOListSort(List<DeviceDO> sourceDeviceDOList, List<UserRoleDO> userRoleDOList) {
        Map<String, DeviceDO> deviceDOMap = sourceDeviceDOList.stream().collect(
                Collectors.toMap(DeviceDO::getSerialNumber, DeviceDO -> DeviceDO)
        );
        List<DeviceDO> newDeviceDOList = Lists.newArrayList();
        for (UserRoleDO userRoleDO : userRoleDOList) {
            if (!deviceDOMap.containsKey(userRoleDO.getSerialNumber())) {
                continue;
            }
            newDeviceDOList.add(deviceDOMap.get(userRoleDO.getSerialNumber()));
        }
        return newDeviceDOList;
    }

    /**
     * 获取设备图标
     *
     * @param deviceDO
     */
    private void fillDeviceInfo(DeviceDO deviceDO) {
        if(!StringUtils.isNotBlank(deviceDO.getModelNo())){
            return;
        }
        DeviceModelIconDO deviceModelIconDO = deviceModelIconService.queryDeviceModelIcon(deviceDO.getModelNo());
        deviceDO.setIcon(deviceModelIconDO == null ?
                 "" : deviceModelIconDO.getIconUrl());
        deviceDO.setSmallIcon(deviceModelIconDO == null ?
                "" : deviceModelIconDO.getSmallIconUrl());
    }

    /**
     * 设备ota相关信息组装
     *
     * @param deviceDO
     * @param mapDeviceOta
     */
    private void dillDeviceOta(DeviceDO deviceDO, Map<String, DeviceOTADO> mapDeviceOta) {
        if (!mapDeviceOta.containsKey(deviceDO.getSerialNumber())) {
            LOGGER.info("listDevicesByUserId mapDeviceOta not contains SerialNumber:{}", deviceDO.getSerialNumber());
            return;
        }

        DeviceOTADO deviceOTADO = mapDeviceOta.get(deviceDO.getSerialNumber());
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView(deviceDO, deviceOTADO);
        if (firmwareViewDO != null) {
            deviceDO.setFirmwareStatus(firmwareViewDO.getFirmwareStatus());
            deviceDO.setNewestFirmwareId(firmwareViewDO.getTargetFirmware());
        }
    }

    private void fillDeviceManufacture(DeviceDO deviceDO) {
        DeviceManualDO deviceManufactureDO = deviceManualService.getDeviceManualBySerialNumber(deviceDO.getSerialNumber());
        if(deviceManufactureDO == null){
            return;
        }

        deviceDO.setUserSn(deviceManufactureDO.getUserSn());
        // 这个hack是因为最开始App组没有做model no和display model no的区分，为了兼容不得不做
        deviceDO.setModelNo("SP10".equals(deviceManufactureDO.getDisplayModelNo()) ? "SP10" : deviceManufactureDO.getModelNo());
        deviceDO.setMacAddress(deviceManufactureDO.getMacAddress());
        deviceDO.setMcuNumber(deviceManufactureDO.getMcuNumber());
        deviceDO.setDisplayModelNo(StringUtils.isEmpty(deviceManufactureDO.getDisplayModelNo()) ? deviceDO.getModelNo() : deviceManufactureDO.getDisplayModelNo());
        //类别
        deviceDO.setModelCategory(deviceModelService.queryDeviceModelCategory(deviceDO.getModelNo()));

    }


    /**
     * 填充设备状态相关信息
     *
     * @param deviceDO
     * @param mapDeviceStatusDO
     * @param time
     */
    public void fillDeviceStatus(DeviceDO deviceDO, Map<String, DeviceStatusDO> mapDeviceStatusDO, Map<String, DeviceStateDO> deviceStateDOMap, int time, User user) {
        if (mapDeviceStatusDO.containsKey(deviceDO.getSerialNumber())) {
            DeviceStatusDO deviceStatusDO = mapDeviceStatusDO.get(deviceDO.getSerialNumber());
            final DeviceOnlineInfo onlineInfo = deviceStatusService.getDeviceOnlineInfo(deviceDO.getSerialNumber(), time, deviceStatusDO, !MapUtils.isEmpty(deviceStateDOMap) && deviceStateDOMap.containsKey(deviceDO.getSerialNumber()) ? deviceStateDOMap.get(deviceDO.getSerialNumber()) : null);
            deviceDO.setOnline(onlineInfo.getOnline());
            deviceDO.setAwake(onlineInfo.getAwake());
            deviceDO.setOfflineTime(onlineInfo.getOfflineTime());
            deviceDO.setBatteryLevel(deviceStatusDO.getBatteryLevel());
            deviceDO.setIsCharging(deviceStatusDO.getIsCharging());
            deviceDO.setSignalStrength(deviceStatusDO.getSignalStrength());
            deviceDO.setSignalLevel(deviceStatusDO.getSignalLevel());

            deviceDO.setWifiPowerLevel(deviceStatusDO.getWifiPowerLevel());
            deviceDO.setNetworkName(deviceStatusDO.getNetworkName());
            deviceDO.setIp(deviceStatusDO.getIp());
            deviceDO.setWifiChannel(deviceStatusDO.getWifiChannel());
            final CloudDeviceSupport cloudDeviceSupport = getDeviceSupport(deviceDO.getSerialNumber());
            deviceDO.setQuantityCharge(Optional.ofNullable(cloudDeviceSupport).map(it -> it.getQuantityCharge()).orElse(0) > 0);
            deviceDO.setAntiflickerSupport(Optional.ofNullable(cloudDeviceSupport).map(it -> it.getAntiflickerSupport()).orElse(0) > 0);

            //init sim info
            setDeviceSimInfoIfHasIccid(deviceDO);

            deviceDO.setCodec(deviceStatusDO.getCodec());
            deviceDO.setLinkedPlatforms(deviceStatusDO.getLinkedPlatforms());
            deviceDO.setLiveAudioToggleOn(deviceStatusDO.getLiveAudioToggleOn());
            deviceDO.setRecordingAudioToggleOn(deviceStatusDO.getRecordingAudioToggleOn());
            deviceDO.setLiveSpeakerVolume(deviceStatusDO.getLiveSpeakerVolume());
            deviceDO.setAlarmWhenRemoveToggleOn(deviceStatusDO.getAlarmWhenRemoveToggleOn());
            deviceDO.setChargingMode(deviceStatusDO.getChargingMode());
            deviceDO.setDeviceNetType(Optional.ofNullable(deviceStatusDO.getDeviceNetType()).orElse(DeviceNetType.WIFI.getCode()));
            final DeviceDormancyInfo dormancyInfo = getDeviceDormancyInfo(deviceDO.getSerialNumber(), deviceDO.getUserId(), deviceDO.getOnline());
            deviceDO.setDeviceStatus(dormancyInfo.getDeviceStatus());
            deviceDO.setDeviceDormancyMessage(dormancyInfo.getDeviceDormancyMessage());
            deviceDO.setDeviceDormancyWakeTime(dormancyInfo.getDeviceDormancyWakeTime());

            deviceDO.setSolarModelNo(deviceStatusDO.getSolarModelNo());
            deviceDO.setSolarSn(deviceStatusDO.getSolarSn());
            deviceDO.setSolarOriginalModelNo(deviceStatusDO.getSolarOriginalModelNo());
            deviceDO.setBatterySn(deviceStatusDO.getBatterySn());
            deviceDO.setBatteryModelNo(deviceStatusDO.getBatteryModelNo());
            deviceDO.setBatteryRemoval(deviceStatusDO.getBatteryRemoval());
            deviceDO.setVideoHasCodecBeginTime(videoStoreService.getVideoHasCodecBeginTime(deviceDO.getAdminId(), deviceDO.getSerialNumber()));

            log.debug("fillDeviceStatus debug deviceDo:{}, onlineInfo:{}", deviceDO, onlineInfo);
            fillDeviceStatusReason(onlineInfo, deviceDO, user);
        }

        setDeviceSupport(deviceDO);
    }

    public static final String REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS = "FILL_DEVICE_STATUS_REASON_TENANT_IDS";

    public void fillDeviceStatusReason(DeviceOnlineInfo onlineInfo, DeviceDO deviceDO, User user) {
        try {
            if (deviceDO == null || onlineInfo == null || user == null || user.getTenantId() == null) {
                return;
            }
            if (!DeviceOnlineStatusEnums.OFFLINE.getCode().equals(deviceDO.getOnline())) {
                return;
            }
            final Integer reason = onlineInfo.getReasonFromData();
            if (reason == null) {
                return;
            }
            final Optional<Set<String>> tenantIds = redisService.getFromLocalCache(REDIS_KEY_FILL_DEVICE_STATUS_REASON_TENANT_IDS)
                    .map(it -> TextUtil.splitToNotBlankSet(it, ','));
            if (!tenantIds.isPresent() || !tenantIds.get().contains(user.getTenantId())) {
                return;
            }
            final DeviceDisconnectedReasonEnums disconnectedReason = DeviceDisconnectedReasonEnums.codeOf(reason);
            if (disconnectedReason != null && disconnectedReason.getAppEnum() != null) {
                deviceDO.setDeviceStatus(disconnectedReason.getAppEnum().getCode());
            }
        } catch (Throwable e) {
            log.error("fillDeviceStatusReason error! sn={}", deviceDO.getSerialNumber(), e);
        }
    }

    public void setDeviceSimInfoIfHasIccid(DeviceDO deviceDO) {
        boolean isVip = vipService.isVipDevice(deviceDO.getUserId(), deviceDO.getSerialNumber());
        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(deviceDO.getSerialNumber());
        //标记设备类型，云存还是4G
        deviceDO.setDeviceStorageType(device4GSimDO == null ? TierServiceTypeEnums.TIER_CLOID_SERVICE.getCode() : TierServiceTypeEnums.TIER_4G.getCode());
        deviceDO.setIccid(device4GSimDO == null ? "" : device4GSimDO.getIccid());
        deviceDO.setImei(device4GSimDO == null ? "" : device4GSimDO.getImei());
        deviceDO.setSimThirdParty(device4GSimDO == null ? 0 :device4GSimDO.getSimThirdParty());
        deviceDO.setSimStatus(device4GSimDO == null ? 0 :device4GSimDO.getSimStatus());
        deviceDO.setInBlackList(device4GSimDO != null && !isVip && device4GSimDO.getSimThirdParty() == 1?
                device4GSimDO.getInBlackList() : false);
    }

    public DeviceDormancyInfo getDeviceDormancyInfo(String sn, Integer userId, Integer online) {
        // 此状态后续会扩展，3表示在线且休眠，11表示低电量关机，12表示按键关机，0是默认值无特殊含义
        final DeviceDormancyInfo deviceDO = new DeviceDormancyInfo().setDeviceStatus(0);
        if (DeviceOnlineStatusEnums.ONLINE.getCode().equals(online)) {
            DeviceDormancyStatus deviceDormancyStatus = deviceDormancyPlanService.queryDeviceDormancyStatus(sn);
            if (deviceDormancyStatus != null && deviceDormancyStatus.getStatus() != null &&
                    (deviceDormancyStatus.getStatus().equals(DeviceDormancyStatusEnums.DORMANCY_PLAN_DORMANCY.getCode()) ||
                    deviceDormancyStatus.getStatus().equals(DeviceDormancyStatusEnums.APP_DORMANCY.getCode()))) {
                // 表示处于休眠状态
                deviceDO.setDeviceStatus(3);
                // 这段文案后续由APP维护
                deviceDO.setDeviceDormancyMessage(this.getDeviceDormancyMessage(deviceDormancyStatus.getWakeupTime(),
                        deviceDormancyStatus.getStatus(), userId));
                //设备退出休眠计划时间戳
                deviceDO.setDeviceDormancyWakeTime(deviceDormancyStatus.getWakeupTime());
            }
        }
        return deviceDO;
    }

    /**
     * 返回唤醒时间
     *
     * @param expireTime
     * @param deviceDormancyStatusType
     * @param userId
     * @return
     */
    private String getDeviceDormancyMessage(Long expireTime, Integer deviceDormancyStatusType, Integer userId) {
        LOGGER.info("device dormancy status expireTime:{},deviceDormancyStatusType:{}", expireTime, deviceDormancyStatusType);
        long currentTime = Instant.now().getEpochSecond();
        User user = userService.queryUserById(userId);
        if (DeviceDormancyStatusEnums.APP_DORMANCY.getCode().equals(deviceDormancyStatusType) || expireTime < currentTime) {
            return centerNotifyConfig.queryValueByKey(user.getTenantId(),user.getLanguage(),CopyWriteConstans.DEVICE_DORMANCY);
        }

        Long tomorrowTime = DateUtils.getDateAfter(new Date(), 1).getTime() / 1000;
        if (expireTime < tomorrowTime) {
            // 当天唤醒
            String timeFormatter = DateTimeFormatter.ofPattern("HH:mm").format(
                    LocalDateTime.ofEpochSecond(expireTime, 0, ZoneOffset.ofHours(8)));
            String message = centerNotifyConfig.getMessage().get(user.getTenantId())
                    .get(user.getLanguage()).get(CopyWriteConstans.DEVICE_DORMANCY_TODAY);
            if (StringUtils.isEmpty(message)) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyToday empty");
                return "";
            }
            return message.replace("{wakeTime}", timeFormatter);
        }
        Long afterOfTomorrowTime = DateUtils.getDateAfter(new Date(), 2).getTime() / 1000;
        if (expireTime < afterOfTomorrowTime) {
            //第二天唤醒
            String timeFormatter = DateTimeFormatter.ofPattern("HH:mm").format(
                    LocalDateTime.ofEpochSecond(expireTime, 0, ZoneOffset.ofHours(8)));
            String message = centerNotifyConfig.getMessage().get(user.getTenantId())
                    .get(user.getLanguage()).get(CopyWriteConstans.DEVICE_DORMANCY_TOMORROW);
            if (StringUtils.isEmpty(message)) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyTomorrow empty");
                return "";
            }
            return message.replace("{wakeTime}", timeFormatter);
        }
        //两天后唤醒
        String weekDay = Instant.ofEpochSecond(expireTime).atZone(ZoneId.systemDefault()).getDayOfWeek().toString();
        String message = centerNotifyConfig.getMessage().get(user.getTenantId())
                .get(user.getLanguage()).get(CopyWriteConstans.DEVICE_DORMANCY_MONDAY);
        if (StringUtils.isEmpty(message)) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "getDeviceDormancyMessage deviceDormancyWeekday empty");
            return "";
        }
        return message.replace("{weekday}", weekDay);
    }

    /**
     * 填充设备用户相关信息
     *
     * @param deviceDO
     * @param mapUser
     */
    private void fillDeviceUserInfo(Integer userId, DeviceDO deviceDO, UserRoleDO userRoleDO, Map<Integer, User> mapUser) {
        /* 不一定
    private void fillDeviceUserInfo(Integer userId, DeviceDO deviceDO, Map<String, UserRoleDO> mapSerialNumberToUserRole, Map<Integer, User> mapUser) {
        //值一定会存在
        UserRoleDO userRoleDO = mapSerialNumberToUserRole.get(deviceDO.getSerialNumber());
        */
        deviceDO.setUserId(userRoleDO.getUserId());

        deviceDO.setAdminId(userRoleDO.getAdminId());
        if (mapUser.containsKey(userRoleDO.getAdminId())) {
            if (userId.equals(userRoleDO.getAdminId())) {
                deviceDO.setAdminEmail(mapUser.get(userRoleDO.getAdminId()).getEmail());
                deviceDO.setAdminPhone(mapUser.get(userRoleDO.getAdminId()).getPhone());
            } else {
                deviceDO.setAdminEmail(userService.userEmailProtect(mapUser.get(userRoleDO.getAdminId()).getEmail()));
                deviceDO.setAdminPhone(userService.userPhoneProtect(mapUser.get(userRoleDO.getAdminId()).getPhone()));
            }

            deviceDO.setAdminName(mapUser.get(userRoleDO.getAdminId()).getName());

            RoleDefinitionDO roleDefinitionDO = roleDefinitionService.quRoleDefinitionDO(Integer.valueOf(userRoleDO.getRoleId()));
            deviceDO.setRole(roleDefinitionDO.getRole());
            deviceDO.setRoleName(roleDefinitionDO.getRoleName());
        }
    }


    /**
     * 填充位置名称
     *
     * @param deviceDO
     * @param locationDOMap
     */
    private void fillDeviceLocation(DeviceDO deviceDO, Map<Integer, LocationDO> locationDOMap,Map<Long,String> deviceHomeNameMap) {
        if (locationDOMap.containsKey(deviceDO.getLocationId())) {
            deviceDO.setLocationName(locationDOMap.get(deviceDO.getLocationId()).getLocationName());
        }

        deviceDO.setHomeName(deviceHomeNameMap.getOrDefault(deviceDO.getHomeId(),""));
    }

    /**
     * 根据序列号查询设备列表
     *
     * @param serialNumbers
     * @return
     */
    public List<DeviceDO> queDeviceBySerialNumbers(List<String> serialNumbers) {
        //获取设备列表
        return deviceDAO.queryDeviceBynSerialNumbers(serialNumbers);
    }

    public List<MsgResponseDO> getAllPushDetailBySerialNumber(String serialNumber) {
        // 如果已经忽略推送，则直接在此处查询是就不带出推送信息
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(serialNumber);
        if (deviceDO.getPushIgnored()) {
            return new ArrayList<>();
        }

        // 不需要忽略推送，则正常查询所有应该收到推送的用户
        return deviceDAO.getPushDetailBySerialNumber(serialNumber);
    }

    /**
     * 查询应该受到推送的用户
     *
     * @param serialNumber
     * @return
     */
    public List<MsgResponseDO> getAllPushDetailBySerialNumberNoFollowPushIgnored(String serialNumber) {
        // 不需要忽略推送，正常查询所有应该收到推送的用户
        return deviceDAO.getPushDetailBySerialNumber(serialNumber);
    }

    /**
     * 更新设备firmware
     *
     * @param serialNumber
     * @param firmwareId
     */
    public void updateFirmwareVersionBySerialNumber(String serialNumber, String firmwareId) {
        String originalFirmwareId = deviceService.queryDeviceFirmware(serialNumber);
        if (StringUtils.isEmpty(firmwareId)) {
            return;
        }
        if (firmwareId.equals(originalFirmwareId)) {
            // 要更新的值与数据库中的值相同则不更新
            return;
        }

        deviceService.updateFirmwareVersionBySerialNumber(serialNumber, firmwareId);
    }

    /**
     * 更新设备mcuVersion
     */
    public void updateDeviceMcuVersion(String serialNumber, String mcuNumber) {
        if (StringUtils.isEmpty(mcuNumber)) {
            return;
        }
        String originalMcuVersion = deviceManualService.queryMcuVersionBySerialNumber(serialNumber);
        if (StringUtils.isEmpty(originalMcuVersion)) {
            // 未查询出结果表示数据表中无此记录，绑定还未完成，status 就已经上报了
            return;
        }
        if (originalMcuVersion.equals(mcuNumber)) {
            return;
        }
        deviceManualService.updateMcuVersionBySerialNumber(serialNumber, mcuNumber);
    }

    /**
     * 设备status 上报电量记录
     *
     * @param device
     */
    public void deviceStatusLog(DeviceDO device) {
        if (device.getBatteryLevel() == null) {
            return;
        }

        String key = DEVICE_STATUS_BATTERY_KEY.replace("{serialNumber}", device.getSerialNumber());
        //电量值去重，可以在status 上报频率调整后去掉
        if (!needDeviceStatusBatteryLog(key, device.getBatteryLevel())) {
            return;
        }

        Map<String, Object> map = MapUtil.builder()
                .put("serialNumber", device.getSerialNumber())
                .put("deviceStatusBattery", device.getBatteryLevel())
                .build();
        reportLogService.reportLog(REPORT_TYPE_DEVICE_STATUS_BATTERY, REPORT_GROUP_DEVICE_STATUS, REPORTER_DEVICE, map);
        redisService.set(key, String.valueOf(device.getBatteryLevel()), DEVICE_STATUS_BATTERY_EXPIRE);
    }

    /**
     * 判断是否需要记录电量变化Log
     *
     * @param key
     * @param batteryLevel
     * @return
     */
    private boolean needDeviceStatusBatteryLog(String key, Integer batteryLevel) {
        if (!redisService.hasDeviceOperationDo(key)) {
            return true;
        }

        String batteryValue = redisService.get(key);
        if (StringUtils.isEmpty(batteryValue)) {
            return true;
        }

        Integer storeBattery = Integer.valueOf(batteryValue);
        if (storeBattery.equals(batteryLevel)) {
            return false;
        }
        return true;
    }


    public void updateHardwareStatus(DeviceDO device) {
        if (device.getWhiteLight() == null) {
            device.setWhiteLight(WhiteLightEnums.NOSUPPORT.getCode());
        }
        if (device.getDeviceSupport() != null) {
            this.initDeviceSupport(device.getSerialNumber(), device.getDeviceSupport());
        }

        this.deviceFirmwareBuilder(device);

        // 设备status上报 status信息缓存 手动解决保留一些原有字段
        DeviceStatusDO newDeviceStatusDO = DeviceStatusDO.parseFrom(device);
        try {
            DeviceStatusDO existDeviceStatusDO = deviceStatusService.queryDeviceStatusBySerialNumber(device.getSerialNumber());
            if (existDeviceStatusDO != null) {
                //目前设备未上报，保留绑定时的设置
                if(newDeviceStatusDO.getDeviceNetType() == null){
                    newDeviceStatusDO.setDeviceNetType(existDeviceStatusDO.getDeviceNetType());
                }

                newDeviceStatusDO.setLinkedPlatforms(existDeviceStatusDO.getLinkedPlatforms());

                // 判断设备是否link至alexa
                if (!org.apache.commons.lang3.StringUtils.equalsIgnoreCase(existDeviceStatusDO.getCodec(), newDeviceStatusDO.getCodec())) {
                    if (alexaService != null && alexaService.canSnSupport(device.getSerialNumber(), device.getUserSn())
                            && !org.apache.commons.lang3.StringUtils.contains(newDeviceStatusDO.getLinkedPlatforms(), "alexa")) {
                        String newLinkedPlatforms = StringUtils.isEmpty(newDeviceStatusDO.getLinkedPlatforms()) ? "alexa" : String.join(",", newDeviceStatusDO.getLinkedPlatforms(), "alexa");
                        newDeviceStatusDO.setLinkedPlatforms(newLinkedPlatforms);
                    }
                    if (googleHomeService != null && googleHomeService.canSnSupport(device.getSerialNumber(), device.getUserSn())
                            && !org.apache.commons.lang3.StringUtils.contains(newDeviceStatusDO.getLinkedPlatforms(), "googlehome")) {
                        String newLinkedPlatforms = StringUtils.isEmpty(newDeviceStatusDO.getLinkedPlatforms()) ? "googlehome" : String.join(",", newDeviceStatusDO.getLinkedPlatforms(), "googlehome");
                        newDeviceStatusDO.setLinkedPlatforms(newLinkedPlatforms);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("setLinkedPlatforms error ", e);
        }
        deviceStatusService.saveDeviceStatus(newDeviceStatusDO);
    }


    /**
     * 固件构建号
     *
     * @param device
     */
    public void deviceFirmwareBuilder(DeviceDO device) {
        if (StringUtils.isEmpty(device.getDisplayGitSha())) {
            return;
        }
        String key = DeviceInfoConstants.DEVICE_FIRMWARE_BUILDER.replace("{serialNumber}", device.getSerialNumber());
        redisService.set(key, device.getDisplayGitSha(), DEVICE_FIRMWARE_BUILDER_EXPIRE);
    }

    /**
     * 获取设备构建号
     *
     * @param serialNumber
     * @return
     */
    public String getDeviceFirmwareBuilder(String serialNumber) {
        String key = DeviceInfoConstants.DEVICE_FIRMWARE_BUILDER.replace("{serialNumber}", serialNumber);
        String deviceFirmwareBuilder = redisService.get(key);
        return StringUtils.isEmpty(deviceFirmwareBuilder) ? "" : deviceFirmwareBuilder;
    }

    /**
     * redis存储固件支持的属性
     *
     * @param serialNumber
     * @param cloudDeviceSupport
     */
    public void initDeviceSupport(String serialNumber, CloudDeviceSupport cloudDeviceSupport) {
        String key = DEVICE_SUPPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        Gson gson = new Gson();
        redisService.set(key, gson.toJson(cloudDeviceSupport));
    }

    public void setDeviceSupportPirSliceReport(String serialNumber, Integer value) {
        String key = DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        redisService.set(key, value != null ? (value + "") : "0");
    }

    private Integer getDeviceSupportPirSliceReport(String serialNumber) {
        String key = DEVICE_SUPPORT_PIR_SLICE_REPORT_KEY_PRE.replace("{serialNumber}", serialNumber);
        return "1".equals(redisService.getFromSlave(key)) ? 1 : 0;
    }

    /**
     * 查询设备是否支持 killKeepAlive
     *
     * @param serialNumber
     * @return
     */
    public boolean deviceKillKeepAliveSupport(String serialNumber) {
        CloudDeviceSupport cloudDeviceSupport = getDeviceSupport(serialNumber);
        return cloudDeviceSupport == null ? false : cloudDeviceSupport.isKillKeepAlive();
    }



    /**
     * 校验是否通知ai
     */
    public List<String> pushAiList(String serialNumber, Integer userId, Collection<String> vipSupportEventObjects) {
        if (CollectionUtils.isEmpty(vipSupportEventObjects)) return Collections.emptyList();
        // 用户设置的需要ai分析的事件对象
        Set<String> userEventObjects = deviceAiSettingsService.queryEnableEventObjects(userId, serialNumber)
                .stream().map(AiObjectEnum::getName).collect(Collectors.toSet());
        // 开拍过滤对象 + tag对象
        List<MessageNotificationSetting> messageNotificationSettingList = messageNotificationSettingsDao.getMessageNotificationSettings(userId, serialNumber);
        if(!messageNotificationSettingList.isEmpty() && messageNotificationSettingList.get(0).getEventObjects() != null) {
            userEventObjects.addAll(Set.of(messageNotificationSettingList.get(0).getEventObjects().split(",")));
        }
        // 设备型号的需要ai分析的事件对象
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        Set<String> modelEventObjects = deviceModelEventService.queryRowDeviceModelEvent(modelNo);
        // 取交集
        List<String> retainEventObjects = FuncUtil.retainToList(modelEventObjects, userEventObjects, vipSupportEventObjects);
        LOGGER.info("pushAiList sn={},userId={},userEventObjects={},modelEventObjects={},vipSupportEventObjects={},retainEventObjects={}", serialNumber, userId, userEventObjects, modelEventObjects, vipSupportEventObjects, retainEventObjects);
        return retainEventObjects;
    }

    public List<String> queryEnabledObjectCategories(String serialNumber, Integer userId) {
        try {
            final boolean isVip = vipService.isVipDevice(userId, serialNumber);
            final boolean isBirdVip = vipService.isBirdVipDevice(userId, serialNumber);
            final VipAiAnalyzeType vipTierType = VipAiAnalyzeType.getVipTierType(isVip, isBirdVip);
            return pushAiList(serialNumber, userId, vipTierType.getSupportEventObjectNames());
        } catch (Throwable e) {
            LOGGER.error("queryEnabledObjectCategories error! sn={},userId={}", serialNumber, userId, e);
            return new ArrayList<>();
        }
    }

    // 当前是否是鸟类vip
    @Deprecated // replace by com.addx.iotcamera.service.openapi.PaasVipService.isBirdVipDevice
    public boolean isBirdVip(Integer adminId) {
        try {
            User user = userService.queryUserById(adminId);
            List<AdditionalUserTierInfo> list = additionalUserTierService.getActiveAdditionalUserTierInfo(adminId, user.getTenantId(), user.getLanguage());
            return list.stream().anyMatch(it -> it.getType() == 1);
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "isBirdVip error! adminId={}", adminId, e);
            return false;
        }
    }

    public Result handleDeviceAwake(DeviceRequestDO awakeDO) throws MqttException, IdNotSetException {
        WakeupResponse response = new WakeupResponse();
        response.setId(awakeDO.getRequestId());
        response.setTime(PhosUtils.getUTCStamp());
        String serialNumber = awakeDO.getSerialNumber();
        DeviceDO storedDevice = deviceService.getAllDeviceInfo(serialNumber);

        //判断是否需要手动设置设备的对时, 避免因为mqtt ssl证书过期导致设备连不上
        if(oldDeviceSSlTimeOffset !=0 || oldDeviceResponseTimestamp != 0) {
            //修改response time
            try {
                String firmwareId = deviceService.queryDeviceFirmware(serialNumber);
                String modelNo = StringUtils.isEmpty(storedDevice.getModelNo()) ? deviceManualService.getModelNoBySerialNumber(serialNumber) : storedDevice.getModelNo();
                response.setTime(OldDeviceWakeupResponseTimeUtil.getResponseTime(modelNo, firmwareId, oldDeviceSSlTimeOffset, oldDeviceResponseTimestamp));
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "set oldDeviceSSlTimeOffset failed, use current time", e);
            }
        }

        WakeupResponse.WakeupResponseValue wakeupResponseValue = response.new WakeupResponseValue();

        if (null == storedDevice || 0 == storedDevice.getActivated()) {
            // 2019.11.25 重做绑定逻辑后，不再向G0摄像头返回的MQTT消息中设置错误的状态码
            // response.setCode(MqttResponseCode.NOT_ACTIVATED);
            wakeupResponseValue.setDeviceStatus(DEVICE_UNACTIVATED.getCode());
            response.setValue(wakeupResponseValue);
            VernemqPublisher.wakeupResponse(serialNumber, response);
            return Result.Error(ResultCollection.DEVICE_NO_ACCESS);
        }

        if (shouldPushForLowBattery(awakeDO)) {
            pushService.pushMessageForLowBatteryEvent(serialNumber);
        }

        // 设备还没有升级到最新版本，需要进一步升级
        // 不向摄像头回复response，如果摄像头由于种种原因没有收到OTA指令，还可以通过重发的awake消息来更新
        if (storedDevice.getOtaOnAwake()) {
            LOGGER.info("Device {} should OTA to a further version.", serialNumber);
            FirmwareViewDO otaTargetFirmware = firmwareService.buildFirmwareView(serialNumber, "");
            firmwareService.startOtaFromApp(serialNumber, otaTargetFirmware, false);
        }

        deviceStatusService.updateLastActBySerialNumber(serialNumber);

        int adminId = userRoleService.getDeviceAdminUser(serialNumber);
        if(!userVipService.isNoUserTier(adminId) || vipService.isVipDevice(adminId, serialNumber)) {
            StreamServerDO streamServerDO = wowzaService.getRecWowzaServerWithLeastLoad(serialNumber);
            wakeupResponseValue.setSize(streamServerDO.getSize());
            wakeupResponseValue.setDuration(streamServerDO.getDuration());
            wakeupResponseValue.setProtocol(streamServerDO.getProtocol());
            wakeupResponseValue.setHost(streamServerDO.getIp());
            wakeupResponseValue.setApp(streamServerDO.getAppName());
        }

        // traceId 必须具有唯一性
        String traceId = traceIdHelper.genTraceIdForRecordVideoV2(serialNumber, ERecordingTrigger.PIR);
        wakeupResponseValue.setTraceId(traceId);

        wakeupResponseValue.setDnsServers(dnsServerConfig.getConfig());
        // 新增timezone offset信息为了让设备自动更新夏令时状态
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if (deviceSettingsDO != null) {
            String timezone = deviceSettingsDO.getTimeZone();
            wakeupResponseValue.setStandardOffset(DateUtils.getStandardOffset(timezone));
            wakeupResponseValue.setDstOffset(DateUtils.getOffset(timezone));
        }
        // 耗电统计参数
        wakeupResponseValue.setPowerStatConfig(powerStatConfig);

        response.setValue(wakeupResponseValue);

        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);

        // 设备唤醒后根据唤醒原因分类操作
        this.deviceAwakeReport(awakeDO, traceId, modelNo);

        boolean res = VernemqPublisher.wakeupResponse(serialNumber, response);

        return Result.OperationResult(res);
    }

    public PbSyncTimeZoneOffsetResponse syncTimeZoneOffsetByProto(String sn, PbSyncTimeZoneOffset req) {
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(sn);
        final String timezone = Optional.ofNullable(deviceSettingsDO).map(DeviceSettingsDO::getTimeZone).orElse("");

        PbSyncTimeZoneOffsetResponse.PbData.PbValue.Builder valueBuilder = PbSyncTimeZoneOffsetResponse.PbData.PbValue.newBuilder()
                .setStandardOffset(DateUtils.getStandardOffset(timezone))
                .setDstOffset(DateUtils.getOffset(timezone));
        if (req.hasValue() && req.getValue().hasTriggerDeviceMsgId()) {
            valueBuilder.setTriggerDeviceMsgId(req.getValue().getTriggerDeviceMsgId());
        }
        return PbSyncTimeZoneOffsetResponse.newBuilder().setResult(0).setMsg("Success")
                .setData(PbSyncTimeZoneOffsetResponse.PbData.newBuilder()
                        .setCode(0).setTime(PhosUtils.getUTCStamp()).setName(req.getName()).setId(req.getId())
                        .setValue(valueBuilder.build())
                        .build())
                .build();
    }

    /**
     * @param awakeDO
     * @param traceId
     */
    public void deviceAwakeReport(DeviceRequestDO awakeDO, String traceId, String modelNo) {
        boolean needComputeDtiM = !CollectionUtils.isEmpty(awakeDO.getReason().getWifiWakeInfo());
        if(needComputeDtiM){
            //使用新计算逻辑
            this.deviceComputeDtimByWifiWakeInfo(awakeDO,modelNo);
        }

        Integer por = awakeDO.getReason().getPor();
        String serialNumber = awakeDO.getSerialNumber();

        PorEnums porEnums = PorEnums.queryEnums(por);
        if (porEnums != null) {
            switch (porEnums) {
                case PIR:
                    this.deviceAwakePir(serialNumber, traceId);
                    break;
                case TCP_UDP_KEEP_ALIVE:
                case WIFI_KEEP_ALIVE_DISCONNECTION:
                    if(!needComputeDtiM){
                        this.deviceAwakeTcpOrUdpKeepalive(serialNumber, modelNo);
                    }
                    break;
                default:
                    break;
            }
        }

    }



    private void deviceAwakeTcpOrUdpKeepalive(String serialNumber, String modelNo) {
        DeviceDTIMFatigue deviceDTIMFatigue;
        // 将直接访问DEVICE_AWAKE_109_KEY替换为使用getDTIMValue方法
        Object storedDeviceDTIMFatigue = redisService.getDTIMValue(serialNumber);

        if (storedDeviceDTIMFatigue == null) {
            deviceDTIMFatigue = DeviceDTIMFatigue.builder()
                    .timestamp(0)
                    .value(0.0)
                    .build();
        } else {
            deviceDTIMFatigue = gson.fromJson(storedDeviceDTIMFatigue.toString(), DeviceDTIMFatigue.class);
        }
        deviceDTIMFatigue.setModelNo(modelNo);
        long currentTime = Instant.now().getEpochSecond();
        if (deviceDTIMFatigue.getTimestamp() == 0) {
            deviceDTIMFatigue.setValue(1.0);
        } else {
            deviceDTIMFatigue.setValue(calculateDTIMFatigue(deviceDTIMFatigue) + BASE_HALF_LIFE_VALUE);
        }
        deviceDTIMFatigue.setTimestamp(currentTime);
        Gson gson = new Gson();
        String deviceDtimStr = gson.toJson(deviceDTIMFatigue);
        // 将直接写入DEVICE_AWAKE_109_KEY替换为使用setDTIMValue方法
        redisService.setDTIMValue(serialNumber, deviceDtimStr);
        LOGGER.info("deviceAwakeTcpOrUdpKeepalive {} 新值:{}", serialNumber, deviceDtimStr);
    }

    /**
     * 计算dtim
     * @param awakeDo
     * @param modelNo
     */
    public Double deviceComputeDtimByWifiWakeInfo(DeviceRequestDO awakeDo, String modelNo) {

        if(CollectionUtils.isEmpty(awakeDo.getReason().getWifiWakeInfo())){
            // 未上报wifiWakeInfo (老固件)
            return 0D;
        }
        List<DeviceRequestDO.WifiWakeInfo> filter109ErrorList = awakeDo.getReason().getWifiWakeInfo().stream()
                .filter(wifi -> !wifi.getCode().equals(DEVICE_AWAKE_109_ERROR_KEY))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filter109ErrorList)){
            // 过滤109 错误
            return 0D;
        }

        int cnt = 0;
        for(DeviceRequestDO.WifiWakeInfo wifiWakeInfo : filter109ErrorList){
            cnt += wifiWakeInfo.getCnt();
        }

        if(cnt < 1){
            // wifi错误次数为0
            return 0D;
        }
        Gson gson = new Gson();
        //上一次存储的记录
        // 将直接访问DEVICE_AWAKE_109_KEY替换为使用getDTIMValue方法
        Object storedDeviceDTIMFatigue = redisService.getDTIMValue(awakeDo.getSerialNumber());

        DeviceDTIMFatigue deviceDTIMFatigue = storedDeviceDTIMFatigue == null ? DeviceDTIMFatigue.builder().timestamp(0).modelNo(modelNo).value(Double.valueOf(cnt)).build()
                : gson.fromJson(storedDeviceDTIMFatigue.toString(), DeviceDTIMFatigue.class);

        if (deviceDTIMFatigue.getTimestamp() > 0) {
            deviceDTIMFatigue.setValue(calculateDTIMFatigue(deviceDTIMFatigue) + cnt);
        }
        deviceDTIMFatigue.setTimestamp(Instant.now().getEpochSecond());

        String deviceDtimStr = gson.toJson(deviceDTIMFatigue);
        // 将直接写入DEVICE_AWAKE_109_KEY替换为使用setDTIMValue方法
        redisService.setDTIMValue(awakeDo.getSerialNumber(), deviceDtimStr);
        LOGGER.info("deviceComputeDtimByWifiWakeInfo {} 新值:{}", awakeDo.getSerialNumber(), deviceDtimStr);
        return deviceDTIMFatigue.getValue();
    }

    /**
     * 设备pir唤醒后操作
     *
     * @param serialNumber
     * @param traceId
     */
    private void deviceAwakePir(String serialNumber, String traceId) {
        LOGGER.debug("Send awake response to device with serial number: {} awoke by PIR with trace id {}",
                serialNumber,
                traceId);

        //设备唤醒时同步设备配置信息
        try {
            deviceSettingService.syncDeviceSetting(serialNumber);
        } catch (Exception e) {
            LOGGER.info("wakeup serialNumber device setting error: {}", serialNumber, e);
        }
    }

    /**
     * 处理设备上报的事件信息
     *
     * @param eventDO
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    public Result handleEventReport(DeviceReportEventDO eventDO) {
        EReportEvent reportEvent = EReportEvent.findByEventId(eventDO.getValue().getEvent());
        if (reportEvent == null) return Result.Success(); // 遇到不认识的event，直接return，避免空指针异常
        final JSONObject responseValue = new JSONObject().fluentPut("event", eventDO.getValue().getEvent());
        Integer userId = userRoleService.getDeviceAdminUser(eventDO.getSerialNumber());
        switch (reportEvent) {
            case PIR:
                return respondStreamServerParams(userId, eventDO, ERecordingTrigger.PIR);
            case LOW_BATTERY:
                return respondLowBatteryEvent(eventDO);
            case BIND_STATUS:
                return respondBindStatusEvent(eventDO);
            /*
            case POWER_PLUG_IN:
                break;
            case POWER_OFF:
                break;
            case LIVE_BEGIN:
                //直播开始
                break;
            case LIVE_END:
                // 直播结束
                break;
            case INFRARED_LIGHT_ON:
                //红外开始
                break;
            case INFRARED_LIGHT_OFF:
                // 红外灯结束
                break;
            case WHITE_LIGHT_ON:
                //白光灯开始
                break;
            case WHITE_LIGHT_OFF:
                //白光灯结束
                break;
            case BIDIRECTIONAL_SPEECH_ON:
                //双向语音开始
                break;
            case BIDIRECTIONAL_SPEECH_OFF:
                //双向语音结束
                break;
            case CRY: // 设备上报哭声检测
                return respondStreamServerParams(userId, eventDO, ERecordingTrigger.CRY);
            case DETECT_RESULT: // 设备识别
                return respondDetectResult(eventDO);
            */
            case DEVICE_CALL:
                openApiWebhookService.callWebhookForDeviceCall(eventDO.getSerialNumber(), eventDO.getValue().getTraceId());
                if (videoGenerateService.isEnable(eventDO.getSerialNumber(), eventDO.getValue().getTraceId())) {
                    videoGenerateService.sendVideoMsg(userId, VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                    break;
                }
                deviceCallService.call(eventDO.getValue().getTraceId(), eventDO.getSerialNumber());
                break;
            case DOORBELL_PRESS:
                // 按压门铃
                // 优先返回，避免设备端阻塞
                if (videoGenerateService.isEnable(eventDO.getSerialNumber(), eventDO.getValue().getTraceId())) {
                    // 发送alexa事件
                    devicePlatformEventPublisher.sendEventSync(DevicePlatformEventEnums.DOORBELL_PRESS, new HashMap<>(Collections.singletonMap("serialNumber", eventDO.getSerialNumber())));
                    eventDO.setAttachment(new JSONObject().fluentPut(S3_VIDEO_SLICE_INFO_NOTIFY_ALEXA, true));
                    videoGenerateService.sendVideoMsg(userId, VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                    break;
                }
                doorbellService.onPress(eventDO);
                break;
            case DOORBELL_REMOVE:
                // 拆除门铃
                // 优先返回，避免设备端阻塞
                if (videoGenerateService.isEnable(eventDO.getSerialNumber(), eventDO.getValue().getTraceId())) {
                    videoGenerateService.sendVideoMsg(userId, VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                    break;
                }
                doorbellService.onRemove(eventDO);
                break;
            case POWER_STATISTICS:
                // 耗电量统计
                // 优先返回，避免设备端阻塞
                if (DeviceInfoService.reportEventSourceThreadLocal.get() == EReportEventSource.KISS) {
                    String kissIp = DeviceInfoService.reportEventSourceIpThreadLocal.get();
                    if (!StringUtils.isEmpty(kissIp)) {
                        kissService.sendReportEventResponse(eventDO.getSerialNumber(), JsonUtil.toJson(MqttResponsePackage.of("reportEvent", new HashMap<>(), eventDO.getId())), kissIp);
                    }
                } else {
                    vernemqPublisher.reportEventResponse(eventDO.getSerialNumber(), new HashMap<>(), eventDO.getId());
                }
                break;
            case ROTATE_CALIBRATION:
                boolean calibrationFinished = BooleanUtils.isTrue(eventDO.getValue().getCalibrationFinished());
                redisService.set(RotateService.getRotateCalibrationKey(eventDO.getSerialNumber()), String.valueOf(calibrationFinished), 10);
                break;
            case AI_EDGE_EVENT:
                vernemqPublisher.reportEventResponse(eventDO.getSerialNumber(), responseValue, eventDO.getId());
                if (videoGenerateService.isEnable(eventDO.getSerialNumber(), eventDO.getValue().getTraceId())) {
                    videoGenerateService.sendVideoMsg(userId, VideoMsgType.REPORT_EVENT, eventDO.getValue().getTraceId(), eventDO);
                    break;
                }
                break;
            case PIR_ALARM:
                this.pirAlarmReportEvent(eventDO,responseValue);
                break;
            default:
                // throw new IllegalArgumentException("Illegal event!");
        }
        return Result.Success();
    }

    private void pirAlarmReportEvent(DeviceReportEventDO eventDO,JSONObject responseValue){
        log.debug("设备上报 PIR_ALARM {}",eventDO.getSerialNumber());
        boolean supportHomeMode = deviceHomeModeService.querySupportHomeMode(userRoleService.getDeviceAdminUser(eventDO.getSerialNumber()));
        if(!supportHomeMode){
            return;
        }

        String traceId = Optional.ofNullable(eventDO.getValue().getTraceId()).orElse("");
        Integer msgType = eventDO.getValue().getMsgType() != null ? eventDO.getValue().getMsgType() :
                Optional.ofNullable(eventDO.getValue().getAlarmDelayTime()).orElse(0) > 0 ?
                        MsgType.PIR_WARNING : MsgType.PIR_ALERT_ACTIVE;

        Integer triggerTime = eventDO.getValue().getTriggerTime() != null ? eventDO.getValue().getTriggerTime() : (int) Instant.now().getEpochSecond();
        if(msgType.equals(MsgType.PIR_WARNING)){
            AlarmDelayParam alarmDelayParam = AlarmDelayParam.builder()
                    .alarmType(msgType)
                    .alarmDelayTime(eventDO.getValue().getAlarmDelayTime())
                    .alarmDuration(eventDO.getValue().getAlarmDuration())
                    .deviceAlarmTime(triggerTime + eventDO.getValue().getAlarmDelayTime())
                    .traceId(traceId)
                    .hasPush(false)
                    .build();
            videoService.saveUserDeviceAlarmDelay(eventDO.getSerialNumber(),alarmDelayParam);
        }

        notificationService.deviceAlarmMessagePush(eventDO.getSerialNumber(),traceId,msgType, eventDO.getValue().getAlarmDelayTime(),eventDO.getValue().getAlarmDuration(),triggerTime,null,null);

        vernemqPublisher.reportEventResponse(eventDO.getSerialNumber(), responseValue, eventDO.getId());
    }

    /**
     * 更新人型检测
     *
     * @param serialNumber
     */
    public void updatePersonDetect(String serialNumber, Integer personDetect) {
        LOGGER.info("人型检测状态变更:serialNumber:【{}】,personDetect:【{}】", serialNumber, personDetect);
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setSerialNumber(serialNumber);
        deviceDO.setPersonDetect(personDetect);
        deviceService.updateDeviceInfo(deviceDO);
    }

    private boolean shouldPushForLowBattery(DeviceRequestDO awakeDO) {
        if (awakeDO.getReason() == null || awakeDO.getReason().getBatteryEvent() == null) {
            return false;
        }
        return awakeDO.getReason().getBatteryEvent() == 1;
    }

    /**
     * 查询指定位置绑定的设备列表
     *
     * @return
     */
    public List<DeviceDO> queryDeviceByLocationId(int userId, int locationId) {
        List<DeviceDO> deviceDOList = deviceDAO.queryDeviceByLocation(locationId);
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList = listDevicesByUserId(userId, deviceDOList, UserRoleEnums.ADMIN.getCode());
        }
        return deviceDOList;
    }


    public DeviceModel getDeviceModel(String serialNumber) {
        String modelNo = deviceManualService.getModelNoBySerialNumber(serialNumber);
        return getDeviceModelByModelNo(modelNo);
    }

    public DeviceModel getDeviceModelByModelNo(String modelNo) {
        return deviceModelConfigService.queryDeviceModelConfig(modelNo);
    }


    public String generateSwitchWifiOperation(DeviceRequest request, int userId) {
        String switchWifiOperationId = IDUtil.uuid().substring(0, 8);

        SwitchWifiOperation switchWifiOperation = SwitchWifiOperation.builder()
                .serialNumber(request.getSerialNumber())
                .operationId(switchWifiOperationId)
                .userId(userId)
                .build();

        switchWifiOperationDAO.insertSwitchWifiOperation(switchWifiOperation);

        return switchWifiOperationId;
    }

    /**
     * 设备包裹检测开关
     *
     * @param serialNumber
     * @return
     */
//    public void updateDevicePackagePush(Integer userId, String serialNumber, Integer packagePush) {
//
//        //开启包裹检测，需要验证用户当前是否购买套餐
//        boolean isVip = userVipService.isVipUser(userId);
//        if (!isVip) {
//            throw new BaseException(DEVICE_NO_ACCESS, DEVICE_NO_ACCESS.getResult().getMsg());
//        }
//
//        deviceDAO.updateDevicePackagePush(serialNumber, DevicePackagePushEnums.PACKAGEPUSHON.getCode().equals(packagePush) ? DevicePackagePushEnums.PACKAGEPUSHON.getCode() :
//                DevicePackagePushEnums.PACKAGEPUSHOFF.getCode());
//    }

    /**
     * 关闭用户包裹检测
     *
     * @param serialNumber
     */
//    public void closePackagePush(String serialNumber) {
//        deviceDAO.updateDevicePackagePush(serialNumber, DevicePackagePushEnums.PACKAGEPUSHOFF.getCode());
//    }

    /**
     * 更新用户配置
     *
     * @param serialNumber
     * @param userId
     * @param startTime
     */
    public void sysReportUpdateUserConfigStart(String serialNumber, Integer userId, long startTime) {
        reportLogService.sysReportUpdateUserConfig(REPORT_TYPE_UPDATE_USER_CONFIG_START, MapUtil.builder()
                .put("serialNumber", serialNumber)
                .put("userId", userId)
                .put("startTime", startTime)
                .put("modelNo", deviceManualService.getModelNoBySerialNumber(serialNumber))
                .build());
    }

    /**
     * 更新用户配置结果
     *
     * @param reportInfo
     * @param updateStatus
     */
    public void sysReportUpdateUserConfigEnd(ReportInfo reportInfo,
                                             boolean updateStatus, String reason) {
        long time = System.currentTimeMillis();
        final String modelNo = deviceManualService.getModelNoBySerialNumber(reportInfo.getSerialNumber());
        reportLogService.sysReportUpdateUserConfig(REPORT_TYPE_UPDATE_USER_CONFIG_END, MapUtil.builder()
                .put("serialNumber", reportInfo.getSerialNumber())
                .put("userId", reportInfo.getUserId())
                .put("endTime", time)
                .put("waitTime", time - reportInfo.getStartTime())
                .put("updateStatus", updateStatus)
                .put("reason", reason)
                .put("modelNo", modelNo)
                .build());
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getUpdateUserConfigEndCountOptional()
                .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName(), modelNo, updateStatus + "", reason).inc()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void bindDeviceLocation(DeviceLocationRequest request, Integer userId) {
        if (request.getLocationId() == -1 && StringUtils.isEmpty(request.getLocationName())) {
            LOGGER.info("bindDeviceLocation 参数异常,serialNumber:{},locationId:{},locationName:{}", request.getSerialNumber(), request.getLocationId(), request.getLocationName());
            throw new BaseException(INVALID_PARAMS, "INVALID_PARAMS");
        }

        // 获取设备当前信息
        DeviceDO currentDevice = deviceService.getAllDeviceInfo(request.getSerialNumber());
        if (currentDevice != null && currentDevice.getHomeId() != null && currentDevice.getHomeId() > 0) {
            // 获取原有home的mode记录
            List<DeviceHomeModeDO> oldHomeModes = deviceHomeModeService.queryDeviceHomeModeBatch(userId, currentDevice.getHomeId());
            if (!CollectionUtils.isEmpty(oldHomeModes)) {
                // 删除原有home的mode setting记录
                for (DeviceHomeModeDO oldMode : oldHomeModes) {
                    deviceHomeModeSettingService.deleteDeviceHomeModeSetting( oldMode.getId(),request.getSerialNumber());
                }
            }
        }

        DeviceDO deviceDO = DeviceDO.builder()
                .serialNumber(request.getSerialNumber())
                .deviceName(request.getDeviceName())
                .build();
        LOGGER.info("update device name success,serialNumber:{}", request.getSerialNumber());
        Long  homeId = request.getHomeId() != null ? request.getHomeId() : deviceHomeService.initHomeId(userId,request.getLanguage(),request.getApp().getTenantId());

        if (request.getLocationId() == -1 && !StringUtils.isEmpty(request.getLocationName())) {
            LocationDO location = new LocationDO();
            location.setAdminId(userId);
            location.setHomeId(homeId);
            location.setCity(request.getCity());
            location.setCountry(request.getCountry());
            location.setDistrict(request.getDistrict());
            location.setPostalCode(request.getPostalCode());
            location.setInsertTime(PhosUtils.getUTCStamp());
            location.setStreetAddress1(request.getStreetAddress1());
            location.setStreetAddress2(request.getStreetAddress2());
            location.setState(request.getState());
            location.setLocationName(request.getLocationName());
            location = locationInfoService.insertLocationReturnId(location);

            deviceDO.setLocationId(location.getId());
            deviceDO.setHomeId(homeId);
        } else {
            deviceDO.setLocationId(request.getLocationId());
            LocationDO locationDO = locationInfoService.selectSingleLocation(request.getLocationId());
            if(locationDO == null){
                LOGGER.info("bindDeviceLocation 参数异常 locationId 不存在 {}", request.getLocationId());
                throw new BaseException(INVALID_PARAMS, "INVALID_PARAMS");
            }
            deviceDO.setHomeId(locationDO.getHomeId());
        }

        deviceService.updateDeviceInfoAndCleanCache(deviceDO);
        LOGGER.info("update device location success,serialNumber:{},locationId:{}", request.getSerialNumber(), deviceDO.getLocationId());



        // 初始化用户Home mode
        deviceHomeModeService.initDeviceHomeMode(userId,vipService.isVipUser(userId),false);
    }

    public DeviceModel processDeviceModelForWebrtc(String serialNumber, CloudDeviceSupport cloudDeviceSupport) {

        DeviceModel model = new DeviceModel();
        DeviceModel modelConfig = deviceModelConfigService.queryDeviceModelConfig(serialNumber);
        if(modelConfig == null){
            // pm 还没发布配置
            return model;
        }
        BeanUtils.copyProperties(modelConfig, model);
        if (cloudDeviceSupport != null && Objects.equals(cloudDeviceSupport.getSupportWebrtc(), 1)) {
            model.setStreamProtocol("webrtc");
        }
        return model;
    }

    private Result respondLowBatteryEvent(DeviceReportEventDO eventDO) {
        String serialNumber = eventDO.getSerialNumber();
        pushService.pushMessageForLowBatteryEvent(serialNumber);
        openApiWebhookService.callWebhookForDeviceLowBattery(serialNumber);

        // 发送alexa low battery event
        devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.LOW_BATTERY, new HashMap<String, Object>() {{
            put("serialNumber", serialNumber);
            put("battery", eventDO.getValue().getBattery());
        }});

        Boolean res = null;
        if (DeviceInfoService.reportEventSourceThreadLocal.get() == EReportEventSource.KISS) {
            String kissIp = DeviceInfoService.reportEventSourceIpThreadLocal.get();
            if (!StringUtils.isEmpty(kissIp)) {
                kissService.sendReportEventResponse(serialNumber, JsonUtil.toJson(MqttResponsePackage.of("reportEvent", new HashMap<>(), eventDO.getId())), kissIp);
            }
            res = true;
        } else {
            res = vernemqPublisher.reportEventResponse(serialNumber, new HashMap<>(), eventDO.getId());
        }

        return Result.OperationResult(res);
    }

    private Result respondStreamServerParams(Integer userId, DeviceReportEventDO eventDO, ERecordingTrigger trigger) {
        long pirStartTimestamp = System.currentTimeMillis();
        String serialNumber = eventDO.getSerialNumber();
        // 设备端生成的traceId就用设备端的，否则由服务端生成
        String traceId = Optional.ofNullable(eventDO.getValue().getTraceId())
                .orElseGet(() -> traceIdHelper.genTraceIdForRecordVideoV2(serialNumber, trigger));
        String motionType = Optional.ofNullable(eventDO.getValue().getMotionType())
                .orElse(OpenApiDeviceConfig.MotionType.video.name());
        VideoUploadType videoUploadType = VideoUploadType.codeOf(eventDO.getValue().getVideoUploadType());

        //4G设备套餐无套餐
        this.device4gPir(serialNumber,traceId);

        final Map<String, Object> pirEventResponse;
        if (videoUploadType == VideoUploadType.WOWZA) {
            StreamServerDO streamServerDO = wowzaService.getRecWowzaServerWithLeastLoad(serialNumber);
            pirEventResponse = (JSONObject) JSON.toJSON(streamServerDO);
        } else if (videoUploadType == VideoUploadType.S3) {
            UploadVideoBeginRequest uploadVideoBeginRequest = UploadVideoBeginRequest.builder().serialNumber(eventDO.getSerialNumber())
                    .traceId(traceId).mqttEventTime(eventDO.getTime()).motionType(motionType).build();
            Result<JSONObject> result = videoService.videoUploadBegin(uploadVideoBeginRequest);
            if (result.getResult() != Result.successFlag) return result;
            pirEventResponse = result.getData();
        } else {
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "respondStreamServerParams 参数错误:{}", JSON.toJSONString(eventDO));
            return INVALID_PARAMS.getResult();
        }
        pirEventResponse.put("traceId", traceId);
        pirEventResponse.put("event", eventDO.getValue().getEvent());

        // 如果是哭声, 一会儿还要通知
        if (trigger == ERecordingTrigger.CRY) {
            deviceDetectHelper.add(serialNumber, traceId, AiActionEnum.CRY.getName(), AiActionEnum.CRY.getName());
        }
        // 先返回pir上传视频参数,再做后续操作,减小耗时
        Result result = Result.OperationResult(vernemqPublisher.reportEventResponse(serialNumber, pirEventResponse, eventDO.getId()));
        // 在mqtt响应发送后统计pir触发时间
        long pirEndTimestamp = System.currentTimeMillis();

        Boolean saveSuccess = null;
        if (videoUploadType == VideoUploadType.S3) {
            if (videoGenerateService.isEnable(serialNumber, traceId)) {
                eventDO.setAttachment(new JSONObject()
//                        .fluentPut(S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME, eventDO.getTime())
                        .fluentPut(S3_VIDEO_SLICE_INFO_MQTT_EVENT_TIME, System.currentTimeMillis()) // 以后端时间为准
                        .fluentPut(S3_VIDEO_SLICE_INFO_PIR_START_TIME, pirStartTimestamp)
                        .fluentPut(S3_VIDEO_SLICE_INFO_PIR_END_TIME, pirEndTimestamp));
                videoGenerateService.sendVideoMsg(userId, VideoMsgType.REPORT_EVENT, traceId, eventDO);
                saveSuccess = true;
            } else {
                saveSuccess = videoService.recordPirTriggerTime(eventDO.getSerialNumber(), traceId, eventDO.getTime(), pirStartTimestamp, pirEndTimestamp);
            }
        }
        reportLogService.sysReportEvent(REPORT_TYPE_DEVICE_REPORT_EVENT,
                MapUtil.builder()
                        .put("event", eventDO.value.getEvent())
                        .put("serialNumber", serialNumber)
                        .put("time", eventDO.getTime())
                        .put("traceId", traceId)
                        .put("modelNo", deviceManualService.getModelNoBySerialNumber(serialNumber))
                        .put(VideoUploadType.KEY_NAME, videoUploadType.getCode())
                        .put("pirStartTimestamp", pirStartTimestamp)
                        .put("pirEndTimestamp", pirEndTimestamp)
                        .put("time2PirStart", DateUtils.diffMillis(eventDO.getTime(), pirStartTimestamp))
                        .put("pirStart2PirEnd", DateUtils.diffMillis(pirStartTimestamp, pirEndTimestamp))
                        .put("saveSuccess", saveSuccess)
                        .build());

        // 刷新s3存储天数设置
        deviceConfigService.refreshS3VideoStorageDaysByDay(serialNumber);
        return result;
    }

    /**
     * 判断是否4G切无套餐的设备
     * @param serialNumber
     * @param traceId
     */
    public void device4gPir(String serialNumber,String traceId){
        boolean device4g =  this.checkIfDeviceUse4G(serialNumber);
        if(!device4g){
            return;
        }

        videoNotifyService.sendMotionNotify4G(serialNumber,traceId);
    }



    private Result respondDetectResult(DeviceReportEventDO eventDO) {
        JSONObject data = JSON.parseObject(eventDO.value.data);
        String traceId = data.getString("traceId");
        String eventObject = data.getString("eventObject");

        // 哭声上报结果
        if (Objects.requireNonNull(EnumFindUtil.findByName(eventObject, AiActionEnum.class)) == AiActionEnum.CRY) {
            deviceDetectHelper.add(eventDO.getSerialNumber(), traceId, eventObject, AiActionEnum.CRY.getName());
        }

        return Result.Success();
    }

    private Result respondBindStatusEvent(DeviceReportEventDO eventDO) {
        String serialNumber = eventDO.getSerialNumber();
        UserRoleDO userRoleDO = userRoleService.getDeviceAdminUserRole(serialNumber);
        Map<String, Object> value = MapUtil.builder().put("bindStatus", userRoleDO == null ? 0 : 1).build();
        value.put("event", eventDO.getValue().getEvents());

        // 新增timezone offset信息为了让设备自动更新夏令时状态
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if (deviceSettingsDO != null) {
            String timezone = deviceSettingsDO.getTimeZone();
            //标准时间
            value.put("standardOffset", DateUtils.getStandardOffset(timezone));
            //夏令时
            value.put("dstOffset", DateUtils.getOffset(timezone));
        }

        return Result.OperationResult(vernemqPublisher.reportEventResponse(serialNumber, value, eventDO.getId()));
    }

    /**
     * 将设备信息恢复到未绑定状态
     *
     * @param serialNumber
     */
    public int deactivateDevice(String serialNumber) {
        DeviceDO deviceDO = DeviceDO.builder()
                .serialNumber(serialNumber)
                .locationId(0)
                .activated(0)
                .activatedTime(0)
                .build();
        return deviceService.updateDeviceInfo(deviceDO);
    }


    /**
     * 导出设备 DeviceDTIMFatigue (半衰值)
     */
    public HSSFWorkbook exportDeviceDtimFatigue() throws Exception {
        Map<Object, Object> deviceDtimFatigueMap = redisService.getHashEntries(DeviceReportKeyConstants.DEVICE_AWAKE_109_KEY);

        String[] titleArray = new String[]{"设备序列号", "更新时间", "值", "modeNo"};
        List<String[]> list = Lists.newArrayList();
        for (Object key : deviceDtimFatigueMap.keySet()) {
            String[] item = new String[4];
            item[0] = key.toString();
            DeviceDTIMFatigue deviceDtimFatigue = gson.fromJson(deviceDtimFatigueMap.get(key).toString(), DeviceDTIMFatigue.class);
            item[1] = String.valueOf(deviceDtimFatigue.getTimestamp());
            item[2] = String.valueOf(deviceDtimFatigue.getValue());
            item[3] = deviceManualService.getModelNoBySerialNumber(key.toString());
            list.add(item);
        }
        return FileUtil.exportDrawData(titleArray, list);
    }

    /**
     * 返回设备绑定后初始化信息
     *
     * @param request
     * @return
     */
    public DeviceBind deviceBindInit(Integer userId,DeviceRequest request) {
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(request.getSerialNumber());
        if (deviceDO == null) {
            LOGGER.info("此序列号设备不存在");
            throw new BaseException(DEVICE_NO_ACCESS);
        }

        this.initDeviceFirmwareAfterBind(deviceDO);

        List<LocationDO> locationDOList = locationInfoService.listUserLocations(userId);

        // 获取所有支持的型号集合
        Set<String> allModelNos = productExchangeCodeService.getAllModelNos();
        DeviceModelMatchResult deviceOrRelatedDevicesInModelSet = productExchangeCodeService.isDeviceOrRelatedDevicesInModelSet(deviceDO.getSerialNumber(), allModelNos);
        boolean showRedeem = deviceOrRelatedDevicesInModelSet.isMatched() && deviceExchangecodeTierService.queryDeviceExchangecodeTierBySerialNumber(request.getSerialNumber()) == null;
        Integer tierId = productExchangeCodeService.queryRedeemTierIdByModelNo(deviceManualService.getModelNoBySerialNumber(request.getSerialNumber()), deviceOrRelatedDevicesInModelSet.getBindInfoList());
        DeviceBind deviceBind = DeviceBind.builder()
                .serialNumber(request.getSerialNumber())
                .deviceName(deviceDO.getDeviceName())
                .firmwareStatus(deviceDO.getFirmwareStatus())
                .newestFirmwareId(deviceDO.getNewestFirmwareId())
                .locationDOList(locationDOList)
                .locationId(deviceDO.getLocationId() == null ? 0 : deviceDO.getLocationId())
                .showRedeem(showRedeem)
                .redeemTierId(tierId)
                .build();
        return deviceBind;
    }
    public DeviceBind deviceBindInitV2(Integer userId,DeviceRequest request) {
        DeviceDO deviceDO = deviceService.getAllDeviceInfo(request.getSerialNumber());
        if (deviceDO == null) {
            LOGGER.info("此序列号设备不存在");
            throw new BaseException(DEVICE_NO_ACCESS, "DEVICE_NO_ACCESS");
        }

        this.initDeviceFirmwareAfterBind(deviceDO);

        return DeviceBind.builder()
                .serialNumber(request.getSerialNumber())
                .deviceName(deviceDO.getDeviceName())
                .firmwareStatus(deviceDO.getFirmwareStatus())
                .newestFirmwareId(deviceDO.getNewestFirmwareId())
                .locationId(deviceDO.getLocationId())
                .homeId(deviceDO.getHomeId())
                .homeList(deviceHomeService.queryUserHomeList(userId,new DeviceHomeRequest()))
                .build();
    }


    /**
     * 初始化设备绑定后固件升级信息
     * @param deviceDO
     */
    private void initDeviceFirmwareAfterBind(DeviceDO deviceDO){
        String modelNo = deviceManualService.getModelNoBySerialNumber(deviceDO.getSerialNumber());
        if(deviceBindFirmwareConfig.deviceNeedFilterOta(modelNo,deviceDO.getFirmwareId())){
            LOGGER.info("设备{}生产型号{}固件版本号{}绑定后不需要升级",deviceDO.getSerialNumber(),modelNo,deviceDO.getFirmwareId());
            deviceDO.setFirmwareStatus(0);
            return;
        }

        DeviceOTADO deviceOTADO = deviceOTAService.queryDeviceOtaBySerialNumber(deviceDO.getSerialNumber());
        FirmwareViewDO firmwareViewDO = firmwareService.buildFirmwareView(deviceDO, deviceOTADO);
        if (firmwareViewDO != null) {
            deviceDO.setFirmwareStatus(firmwareViewDO.getFirmwareStatus());
            deviceDO.setNewestFirmwareId(firmwareViewDO.getTargetFirmware());
        }
    }

    /**
     * 获取当前用户下设备推送image
     *
     * @param userId
     * @return
     */
    public List<DevicePushImage> queryUserDevicePushImage(Integer userId) {
        List<DevicePushImage> devicePushImageList = Lists.newArrayList();
        List<UserRoleDO> serialNumUserRoleList = userRoleService.getUserRoleByUserId(userId);

        for (UserRoleDO userRoleDO : serialNumUserRoleList) {
            final DevicePushImage pushImage = queryDevicePushImage(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW, userRoleDO.getSerialNumber(), userRoleDO.getAdminId());
            if (pushImage == null) {
                LOGGER.info("当前设备无message推送记录,serialNumber:{},adminId:{}", userRoleDO.getSerialNumber(), userRoleDO.getAdminId());
                continue;
            }
            // 重新计算image url(有过期时间)
            pushImage.setLastPushImageUrl(s3Service.preSignUrl(pushImage.getLastPushImageUrl()));
            pushImage.setSerialNumber(userRoleDO.getSerialNumber());
            final DevicePushImage subImage = queryDevicePushImage(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW, userRoleDO.getSerialNumber(), userRoleDO.getAdminId());
            if (subImage != null) {
                subImage.setLastPushImageUrl(s3Service.preSignUrl(subImage.getLastPushImageUrl()));
                subImage.setSerialNumber(userRoleDO.getSerialNumber());
                pushImage.setSubVideoImage(subImage);
            }
            devicePushImageList.add(pushImage);
        }
        return devicePushImageList;
    }

    public DevicePushImage queryDevicePushImage(VideoTypeEnum videoTypeEnum, String sn, Integer adminId) {
        if (videoTypeEnum == null || sn == null || adminId == null) return null;
        try {
            final String redisKey = videoTypeEnum.getLastImageKeyTemplate().replace("{serialNumber}", sn)
                    .replace("{userId}", String.valueOf(adminId));
            final String redisValue = persistentRedisService.get(redisKey);
            LOGGER.info("queryUserDevicePushImage serialNumber:{},key:{},value:{}", sn, redisKey, redisValue);
            if (StringUtils.isBlank(redisValue)) return null;
            Gson gson = new Gson();
            return gson.fromJson(redisValue, DevicePushImage.class);
        } catch (Exception e) {
            log.error("queryUserDevicePushImage error!", e);
            return null;
        }
    }

    /**
     * 唤醒设备
     *
     * @param serialNumber
     * @param traceId
     */
    @SentinelResource("wakeupDevice")
    public void wakeUpDevice(String serialNumber, String traceId) {
        // 唤醒设备
        kissService.wakeupDevice(serialNumber, traceId);
    }

    /**
     * 等待设备唤醒
     *
     * @param serialNumber
     * @return
     */
    public boolean waitDeviceWakeUp(String serialNumber) {
        LOGGER.info("waitDeviceWakeUp for {}", serialNumber);
        boolean supportKillKeepAlive = this.deviceKillKeepAliveSupport(serialNumber);
        if (!supportKillKeepAlive) {
            LOGGER.info("设备 {} 不支持killKeepAlive");
            // 老固件不支持killKeepAlive
            return true;
        }
        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                .retryIfResult(Predicates.equalTo(false))
                // 设备唤醒平均2-3秒，没必要100 毫秒就一次判断，太频繁
                .withWaitStrategy(WaitStrategies.fixedWait(200, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(50))
                .build();
        try {
            retryer.call(() -> deviceStatusService.deviceConnectionStatus(serialNumber));
            LOGGER.info("waitDeviceWakeUp {} succeeded!", serialNumber);
            return true;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "waitDeviceWakeUp {} failed!", serialNumber);
            return false;
        }
    }

    @Override
    public void triggerDeviceUploadSupport(String serialNumber) {

    }

    public DeviceApInfoDO queryDeviceApInfo(Integer userId, String serialNumber, String userSn, String apInfoVersion) {
        DeviceApInfoDO deviceApInfoDO = new DeviceApInfoDO();

        // 优先从厂测数据库获取设备信息
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(serialNumber, userSn);
        if (deviceManufactureTableDO == null) {
            throw new BaseException(ResultCollection.DEVICE_NO_EXIT, "device not exist");
        }

        String tenantId = userService.queryTenantIdById(userId);
        Set<String> supportModelNoSet = deviceModelTenantService.queryDeviceModelTenantModelList(tenantId);
        String modelNo = org.apache.commons.lang3.StringUtils.defaultString(deviceManufactureTableDO.getRegisterModelNo(),deviceManufactureTableDO.getModelNo());
        if(CollectionUtils.isEmpty(supportModelNoSet) || !supportModelNoSet.contains(modelNo)){
            log.info("app {} 不支持当前型号设备 {}",tenantId,modelNo);
            throw new BaseException(DEVICE_SN_NOT_SUPPORT_APP, "DEVICE_SN_NOT_SUPPORT_APP");
        }

        BeanUtils.copyProperties(deviceManufactureTableDO, deviceApInfoDO);

        // 其次再从数据库查询设备信息
        DeviceDO device = deviceService.getAllDeviceInfo(deviceManufactureTableDO.getSerialNumber());
        if (device == null) {
            device = new DeviceDO();
        }
        device.setModelNo(org.apache.commons.lang3.StringUtils.defaultString(deviceManufactureTableDO.getRegisterModelNo(), deviceManufactureTableDO.getModelNo()));
        fillDeviceInfo(device);
        deviceApInfoDO.setIcon(device.getIcon());
        deviceApInfoDO.setSmallIcon(device.getSmallIcon());

        DeviceDBApInfo deviceDBApInfo = deviceApInfoDAO.queryBySn(deviceManufactureTableDO.getSerialNumber());

        // 尝试从数据库获取
        String apInfo = null;
        if (deviceDBApInfo!=null && !StringUtils.isEmpty(deviceDBApInfo.getApInfo())) {
            String deviceDBApInfoVersion = deviceDBApInfo.getApInfoVersion();
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(deviceDBApInfoVersion, apInfoVersion)) {
                apInfo = deviceDBApInfo.getApInfo();
            }
        }

        // 尝试从厂测数据获取
        if (StringUtils.isEmpty(apInfo) && !StringUtils.isEmpty(deviceManufactureTableDO.getApInfo())) {
            apInfo = deviceManufactureTableDO.getApInfo();
        }

        // 都没获取到，则手动生成
        if (StringUtils.isEmpty(apInfo) && !"01".equals(apInfoVersion)) {
            apInfo = DeviceApInfoGenerateUtil.generateApInfo(tenantId, deviceManufactureTableDO.getUserSn(), apInfoVersion);
        }

        deviceApInfoDO.setApInfo(apInfo);
        return deviceApInfoDO;
    }

    /**
     * 从设备同步apInfo更新数据库里的apInfo
     *
     * @param serialNumber
     * @param syncApInfoVersion
     * @return
     */
    public String updateDeviceApInfoFromSync(String serialNumber, String syncApInfoVersion) {
        DeviceManufactureTableDO deviceManufactureTableDO = factoryDataQueryService.queryDeviceManufactureTableDOBySnOrUserSn(serialNumber, null);
        if (deviceManufactureTableDO == null) {
            return null;
        }

        String apInfo = null;
        if (!StringUtils.isEmpty(deviceManufactureTableDO.getApInfo())) { // 如果厂测数据库已生成ap信息则使用厂测数据库的
            apInfo = deviceManufactureTableDO.getApInfo();
        } else {
            // 如果连同步的ap信息没有，则只能服务端自己生成
            try {
                int adminId = userRoleService.getDeviceAdminUser(serialNumber);
                String tenantId = userService.queryTenantIdById(adminId);
                apInfo = DeviceApInfoGenerateUtil.generateApInfo(tenantId, deviceManufactureTableDO.getUserSn(), StringUtils.isEmpty(syncApInfoVersion) ? DeviceApInfoGenerateUtil.DEFAULT_AP_INFO_VERSION : syncApInfoVersion);
            } catch (Exception e) {
                com.addx.iotcamera.util.LogUtil.warn(LOGGER, "generateApInfo failed, will use default generateApInfo", e);
            }
        }

        if (StringUtils.isEmpty(apInfo)) {
            apInfo = DeviceApInfoGenerateUtil.generateDefaultApInfo(deviceManufactureTableDO.getUserSn());
        }

        DeviceDBApInfo deviceDBApInfo = new DeviceDBApInfo();
        deviceDBApInfo.setSerialNumber(serialNumber);
        deviceDBApInfo.setUserSn(deviceManufactureTableDO.getUserSn());
        deviceDBApInfo.setApInfoVersion(syncApInfoVersion);
        deviceDBApInfo.setApInfo(apInfo);
        deviceApInfoDAO.upsert(deviceDBApInfo);

        return apInfo;
    }

    public Result<FoundDeviceInfoResult> queryFoundDeviceInfo(FoundDeviceInfoQuery input) {
        Result<FoundDeviceInfoResult> result = new Result<>(new FoundDeviceInfoResult());
        List<DeviceManufactureTableDO> deviceManus = factoryDataQueryService.queryDeviceManufactureByUserSns(input.getUserSns());
        Map<String, DeviceManufactureTableDO> userSn2Manu = deviceManus.stream().collect(Collectors.toMap(it -> it.getUserSn(), it -> it));

        Map<String, DeviceModelIconDO> modelNo2Icon = new LinkedHashMap<>();
        for (String userSn : input.getUserSns()) {
            FoundDeviceInfoResult.DeviceItem deviceItem = new FoundDeviceInfoResult.DeviceItem().setUserSn(userSn);
            result.getData().getDevices().add(deviceItem);
            DeviceManufactureTableDO manu = userSn2Manu.get(userSn);
            if (manu == null) {
                deviceItem.setQueryStatus(-1);
                continue;
            }
            String modelNo = Optional.ofNullable(manu.getRegisterModelNo()).orElse(manu.getModelNo());
            if (modelNo == null) {
                deviceItem.setQueryStatus(-2);
                continue;
            }
            DeviceModelIconDO modelIcon = modelNo2Icon.computeIfAbsent(modelNo, deviceModelIconService::queryDeviceModelIcon);
            if (modelIcon == null) {
                deviceItem.setQueryStatus(-3);
                continue;
            }
            deviceItem.setQueryStatus(0).setIcon(modelIcon.getIconUrl()).setSmallIcon(modelIcon.getSmallIconUrl());
        }
        return result;
    }

    void getDeviceInfoFromSetting(DeviceDO device) {
        DeviceSettingsDO deviceSettingsDO = deviceSettingService.getDeviceSettingsBySerialNumber(device.getSerialNumber());
        if (deviceSettingsDO != null) {
            device.setDefaultCodec(deviceSettingsDO.getDefaultCodec());
            if (deviceSettingsDO.getLiveAudioToggleOn() != null) {
                device.setLiveAudioToggleOn(deviceSettingsDO.getLiveAudioToggleOn());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getRecordingAudioToggleOn() != null) {
                device.setRecordingAudioToggleOn(deviceSettingsDO.getRecordingAudioToggleOn());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getLiveSpeakerVolume() != null) {
                device.setLiveSpeakerVolume(deviceSettingsDO.getLiveSpeakerVolume());
            } else {
                // do nothing
            }
            if (deviceSettingsDO.getAlarmWhenRemoveToggleOn() != null) {
                device.setAlarmWhenRemoveToggleOn(deviceSettingsDO.getAlarmWhenRemoveToggleOn());
            } else {
                // do nothing
            }

            device.setTimeZone(deviceSettingsDO.getTimeZone());
        } else {
            // do nothing
        }
    }

    public Result initDeviceSupport() {
        AwsHelper.clearAllInstance();
        ProcessLogHelper processHelper = new ProcessLogHelper("initDeviceSupport", 2, RoundingMode.DOWN);
        processHelper.setTotalNum(userRoleService.queryAllAdminUserRoleNum());
        Iterator<UserRoleDO> iterator = userRoleService.queryAllAdminUserRoleIterator("initDeviceSupport", 1000);
        while (iterator.hasNext()) {
            UserRoleDO userRole = iterator.next();
            final String sn = userRole.getSerialNumber();
            try {
                final CloudDeviceSupport cloudDeviceSupport = this.getRowDeviceSupport(sn);
                if (cloudDeviceSupport == null) {
                    processHelper.onSkip();
                    continue;
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportLocalVideoLookBack()).orElse(false)) {
                    cloudDeviceSupport.setSupportLocalVideoLookBack(null);
                }
                if (Optional.ofNullable(cloudDeviceSupport.getLocalVideoStorageType()).orElse(0) != 1) {
                    cloudDeviceSupport.setLocalVideoStorageType(null);
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportSdCardFormat()).orElse(false)) {
                    cloudDeviceSupport.setSupportSdCardFormat(null);
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportNightVisionSwitch()).orElse(false)) {
                    cloudDeviceSupport.setSupportNightVisionSwitch(null);
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportWhiteLight()).orElse(false)) {
                    cloudDeviceSupport.setSupportWhiteLight(null);
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportAlarmFlashLight()).orElse(false)) {
                    cloudDeviceSupport.setSupportAlarmFlashLight(null);
                }
                if (!Optional.ofNullable(cloudDeviceSupport.getSupportStarlightSensor()).orElse(false)) {
                    cloudDeviceSupport.setSupportStarlightSensor(null);
                }
                this.initDeviceSupport(sn, cloudDeviceSupport);
                deviceSupportService.insertDeviceSupport(sn, cloudDeviceSupport);
                processHelper.onProcess(Result.Success());
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(LOGGER, "initDeviceSupport error!", e);
                processHelper.onProcess(Result.Failure(e.getMessage()));
            }
        }
        processHelper.onEnd();
        return new Result(0, processHelper.toString(), null);
    }


    /**
     * 判断用户是否拥有某类型设备
     * @param adminId
     * @param enums
     * @return
     */
    public boolean hasServiceTypeDevice(Integer adminId, TierServiceTypeEnums enums){
        List<UserRoleDO> userRoleDOList = userRoleService.getUserRoleByUserId(adminId,UserRoleEnums.ADMIN.getCode());
        if(CollectionUtils.isEmpty(userRoleDOList)){
            return false;
        }
        boolean result = false;
        switch (enums){
            case TIER_4G:
                result = userRoleDOList.stream()
                        .anyMatch(ur -> this.checkIfDeviceUse4G(ur.getSerialNumber()));
                break;
            case TIER_CLOID_SERVICE:
                result = userRoleDOList.stream()
                        .anyMatch(ur -> !this.checkIfDeviceUse4G(ur.getSerialNumber()));
                break;
        }
        return result;
    }

    public Boolean checkIf4GDeviceHasOfficialSimCard(String serialNumber) {
        Device4GSimDO device4GSimDO = device4GService.queryDevice4GSimDO(serialNumber);
        return device4GSimDO != null && device4GSimDO.getSimThirdParty() == 0;
    }

    /**
     * 判断4G设备列表是否有官方sim卡
     * @param adminId
     * @return
     */
    public boolean containsOfficialSim(Integer adminId){
        return userRoleService.getUserRoleByUserId(adminId, UserRoleEnums.ADMIN.getCode())
                .stream()
                .map(UserRoleDO::getSerialNumber)
                .map(sn -> Optional.ofNullable(device4GService.queryDevice4GSimDO(sn)))
                .map(opt -> opt.map(Device4GSimDO::getSimThirdParty).orElse(null))
                .anyMatch(simThirdParty -> simThirdParty != null && simThirdParty == 0);
    }

    //init 设备vip信息
    public void initDeviceVip(DeviceDO deviceDO){

        if(StringUtils.isNotBlank(deviceDO.getIccid())){
            deviceDO.setDeviceInVip(false);
        }else {
            deviceDO.setDeviceInVip(vipService.isVipDevice(deviceDO.getAdminId(),deviceDO.getSerialNumber()));
        }

        if(!deviceDO.getDeviceInVip()){
            return;
        }


        Integer currentTierId = userTierDeviceService.getDeviceCurrentTier(deviceDO.getAdminId(),deviceDO.getSerialNumber());
        if(!FREE_LICENSE_7_DAY.equals(currentTierId)){
            return;
        }
        UserDeviceFreeTierDO userDeviceFreeTierDO = freeLicenseService.queryUserDeviceFreeTierBySn(deviceDO.getAdminId(),deviceDO.getSerialNumber());

        deviceDO.setFreeLicenseId(userDeviceFreeTierDO != null && userDeviceFreeTierDO.getTierId().equals(currentTierId) ? currentTierId : 0);
        deviceDO.setFreeLicenseEndTime(deviceDO.getFreeLicenseId() > 0 ? userDeviceFreeTierDO.getEndTime() : 0);
    }

    public void fillListUserDeviceV3Params(List<DeviceDO> list, Integer userId, ListUserDevicesRequest request) {
        if (CollectionUtils.isEmpty(list)) {
            return ;
        }
        List<DevicePushImage> devicePushImages = this.queryUserDevicePushImage(userId);
        devicePushImages = devicePushImages == null ? new ArrayList<>() : devicePushImages;
        Map<String, DevicePushImage> map = new HashMap<>();
        for (DevicePushImage tmp : devicePushImages) {
            map.put(tmp.getSerialNumber(), tmp);
        }

        for (DeviceDO deviceDOJson : list) {
            LibraryRequest req = new LibraryRequest();
            req.setUserId(userId);
            req.setUse("day");
            if (request.getLibraryCountStartTimestamp() == null || request.getLibraryCountEndTimestamp() == null) {
                continue;
            }
            req.setStartTimestamp(request.getLibraryCountStartTimestamp());
            req.setEndTimestamp(request.getLibraryCountEndTimestamp());
            req.setSerialNumber(Arrays.asList(deviceDOJson.getSerialNumber()));
            List<LibraryCountDay> libraryCountDays = libraryService.queryLibraryCountDayGroup(req);

            deviceDOJson.setLibraryCountList(libraryCountDays);
            deviceDOJson.setDevicePushImage(map.get(deviceDOJson.getSerialNumber()));
        }
    }
}
