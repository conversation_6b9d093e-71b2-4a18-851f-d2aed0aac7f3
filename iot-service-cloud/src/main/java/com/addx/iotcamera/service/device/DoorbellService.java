package com.addx.iotcamera.service.device;

import com.addx.iotcamera.bean.domain.DeviceSettingsDO;
import com.addx.iotcamera.bean.domain.device.DeviceReportEventDO;
import com.addx.iotcamera.bean.msg.MsgType;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.enums.DoorbellPressNotifyType;
import com.addx.iotcamera.enums.EReportEvent;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.service.*;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.addx.iotcamera.constants.DeviceModelSettingConstants.DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH;
import static com.addx.iotcamera.constants.DeviceModelSettingConstants.DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE;

/**
 * <AUTHOR>
 * <p>
 * 门铃相关service
 */
@Service
@Slf4j
public class DoorbellService {

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Setter
    @Autowired(required = false)
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Autowired
    @Lazy
    private VideoService videoService;
    @Autowired
    private DeviceSettingService deviceSettingService;

    /**
     * 按压门铃
     *
     * @param deviceReportEventDO
     */
    public void onPress(DeviceReportEventDO deviceReportEventDO) {
        String traceId = deviceReportEventDO.getValue().getTraceId();
        String serialNumber = deviceReportEventDO.getSerialNumber();
        videoService.updateVideoReportEvents(serialNumber, traceId, Arrays.asList(EReportEvent.DOORBELL_PRESS));
        // 推送
        final DeviceSettingsDO settings = deviceSettingService.getDeviceSettingsBySerialNumber(serialNumber);
        if (getNotifySwitch(settings)) {
            notificationService.notifyVideoReportEvent(traceId, serialNumber, EReportEvent.DOORBELL_PRESS, getNotifyMsgType(settings));
            // 发送alexa事件
            if (devicePlatformEventPublisher != null) {
                devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DOORBELL_PRESS, new HashMap<>(Collections.singletonMap("serialNumber", deviceReportEventDO.getSerialNumber())));
            }
        }
    }

    public static Boolean getNotifySwitch(DeviceSettingsDO settings) {
        return Optional.ofNullable(settings).map(it -> it.getDoorbellPressNotifySwitch()).orElse(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_SWITCH);
    }

    public static Integer getNotifyMsgType(DeviceSettingsDO settings) {
        String notifyType = Optional.ofNullable(settings).map(it -> it.getDoorbellPressNotifyType()).orElse(DEFAULT_KEY_DOORBELL_PRESS_NOTIFY_TYPE);
        return DoorbellPressNotifyType.phone.name().equals(notifyType) ? MsgType.DEVICE_CALL_MSG : MsgType.NEW_VIDEO_MSG;
    }

    /**
     * 拆除门铃
     *
     * @param deviceReportEventDO
     */
    public void onRemove(DeviceReportEventDO deviceReportEventDO) {
        String traceId = deviceReportEventDO.getValue().getTraceId();
        String serialNumber = deviceReportEventDO.getSerialNumber();
        videoService.updateVideoReportEvents(serialNumber, traceId, Arrays.asList(EReportEvent.DOORBELL_REMOVE));
        // 推送
        notificationService.notifyVideoReportEvent(traceId, serialNumber, EReportEvent.DOORBELL_REMOVE, MsgType.NEW_VIDEO_MSG);
    }
}
