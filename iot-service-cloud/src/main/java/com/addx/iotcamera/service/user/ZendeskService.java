package com.addx.iotcamera.service.user;

import com.addx.iotcamera.bean.ZendeskActionVO;
import com.addx.iotcamera.bean.app.UpdateZendeskTicketVO;
import com.addx.iotcamera.bean.app.user.ZendeskUserTokenRequest;
import com.addx.iotcamera.bean.db.DeviceManufactureTableDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.app.ZendeskConfig;
import com.addx.iotcamera.publishers.zendesk.ZendeskClient;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.zendesk.client.v2.Zendesk;
import org.zendesk.client.v2.model.CustomFieldValue;
import org.zendesk.client.v2.model.Ticket;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ZendeskService {

    @Autowired
    @Lazy
    private UserService userService;
    @Autowired
    @Lazy
    private RedisService redisService;
    @Resource
    @Lazy
    private ZendeskConfig zendeskConfig;
    @Resource
    @Lazy
    private ZendeskClient zendeskClient;
    @Resource
    @Lazy
    private FactoryDataQueryService factoryDataQueryService;

    public Long getZendeskFieldIdByTenantIdAndKey(String tenantId, String key) {
        Map<String, Long> key2Id = Optional.ofNullable(zendeskConfig.getTenantId2Key2FieldId()).map(it -> it.get(tenantId)).orElse(null);
        if (MapUtils.isNotEmpty(key2Id)) return key2Id.get(key);
        return Optional.ofNullable(zendeskConfig.getTenantId2Key2FieldId()).map(it -> it.get("default")).map(it -> it.get(key)).orElse(null);
    }

    public Result updateTicket(UpdateZendeskTicketVO input) {
        final String tenantId = input.getTenantId();
        int fieldErrNum = 0;
        int idNotFoundNum = 0;
        List<CustomFieldValue> fieldValueList = new LinkedList<>();
        try {
            for (UpdateZendeskTicketVO.CustomField customField : input.getCustomFields()) {
                if (StringUtils.isBlank(customField.getKey())) {
                    fieldErrNum++;
                    continue;
                }
                if (ArrayUtils.isEmpty(customField.getValue())) {
                    continue;
                }
                Long fieldId = getZendeskFieldIdByTenantIdAndKey(tenantId, customField.getKey());
                if (fieldId == null) {
                    idNotFoundNum++;
                    PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getZendeskFieldIdNotFoundCounterOptional().ifPresent(it -> {
                        it.labels(PrometheusMetricsUtil.getHostName(), tenantId, customField.getKey()).inc();
                    }));
                    continue;
                }
                CustomFieldValue fieldValue = new CustomFieldValue();
                fieldValue.setId(fieldId);
                fieldValue.setValue(customField.getValue());
                fieldValueList.add(fieldValue);
            }
            buildCuidField(input).ifPresent(fieldValueList::add);
            if (fieldErrNum > 0) {
                return Result.Error(ResultCollection.INVALID_PARAMS.getCode(), "field key or value empty!");
            }
            if (fieldValueList.isEmpty()) {
                return Result.Success();
            }
            Ticket ticket = new Ticket();
            ticket.setId(Long.parseLong(input.getTicketId()));
            ticket.setCustomFields(fieldValueList);
            log.info("updateTicket begin! ticket={}", JSON.toJSONString(ticket));
            Zendesk client = zendeskConfig.queryZendesk(input.getTenantId());
            if (client == null) {
                return Result.Error(ResultCollection.ZENDESK_TICKET_UPDATE_FAIL, "not found zendesk client");
            }
            Ticket result = client.updateTicket(ticket);
            log.info("updateTicket end! fieldErrNum={},idNotFoundNum={},updateFieldValueNum={},result={}", fieldErrNum, idNotFoundNum, fieldValueList.size(), JSON.toJSONString(result));
            return Result.Success();
        } catch (Exception e) {
            log.error("updateTicket error! fieldErrNum={},idNotFoundNum={},updateFieldValueNum={}", fieldErrNum, idNotFoundNum, fieldValueList.size(), e);
            return Result.Error(ResultCollection.ZENDESK_TICKET_UPDATE_FAIL, "update ticket fail");
        }
    }

    private Optional<CustomFieldValue> buildCuidField(UpdateZendeskTicketVO input) {
        if (CollectionUtils.isEmpty(input.getUserSnList())) return Optional.empty();
        // CUID 48313488595225
        Long fieldId = getZendeskFieldIdByTenantIdAndKey(input.getTenantId(), "CUID");
        if (fieldId == null) return Optional.empty();
        CustomFieldValue fieldValue = new CustomFieldValue();
        fieldValue.setId(fieldId);
        fieldValue.setValue(input.getUserSnList().stream().map(factoryDataQueryService::queryDeviceCustomerIdByUserSn).toArray(String[]::new));
        return Optional.of(fieldValue);
    }

    public Result createZendeskUser(ZendeskUserTokenRequest request) {
        boolean isCreateNewZendeskUser = Optional.ofNullable(request.getIsCreateNewZendeskUser()).orElse(true);
        try {
            User user = userService.queryUserById(request.getUserId());
            if (user == null) {
                return Result.Error(ResultCollection.INVALID_PARAMS, "user not exist!");
            }
            String redisKey = "userZendesk:" + request.getUserId();
            String redisValue = redisService.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                return Result.Success();
            }
            if (!isCreateNewZendeskUser) {
                return Result.Error(ResultCollection.ZENDESK_USER_NOT_EXIST);
            }
            long t1 = System.currentTimeMillis();
            ZendeskActionVO zendeskActionVO = ZendeskClient.createZendeskActionVO(user, request);
            Long zdUserId = zendeskClient.createZendeskUser(zendeskActionVO);
            if (zdUserId == null) {
                return Result.Error(ResultCollection.ZENDESK_USER_CREATE_FAIL);
            }
            redisService.set(redisKey, zdUserId + "");
            long t2 = System.currentTimeMillis();
            log.info("createZendeskUser end! costTime={},zdUserId={},userId={}", (t2 - t1), zdUserId, request.getUserId());
            return Result.Success();
        } catch (Exception e) {
            log.error("createZendeskUser error! request={}", JSON.toJSONString(request), e);
            return Result.Failure("auth token error!");
        }
    }

}
