package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.apollo.MailSend;
import com.addx.iotcamera.bean.app.AppInfo;
import com.addx.iotcamera.bean.app.MailConfirmRequest;
import com.addx.iotcamera.bean.app.RegisterRequest;
import com.addx.iotcamera.bean.app.vip.UserVipTier;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.user.EmailPayUrlDO;
import com.addx.iotcamera.bean.domain.verfiy.VerifyCodeResponseDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.exception.ParamException;
import com.addx.iotcamera.bean.manage.EmailAccount;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.config.apollo.MailSendConfig;
import com.addx.iotcamera.config.app.AppAccountConfig;
import com.addx.iotcamera.config.app.EmailPayConfig;
import com.addx.iotcamera.dao.IMailConfirmDAO;
import com.addx.iotcamera.enums.SendEmailTypeEnums;
import com.addx.iotcamera.util.ConfigCenterUtil;
import com.addx.iotcamera.util.Mail;
import com.addx.iotcamera.util.MessageUtil;
import com.aliyuncs.exceptions.ClientException;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

import static com.addx.iotcamera.constants.UserConstants.*;
import static com.addx.iotcamera.controller.auth.AuthController.*;
import static com.addx.iotcamera.enums.SendEmailTypeEnums.TWO_TEMP_FA;
import static org.addx.iot.common.enums.ResultCollection.*;

@Component
public class MailConfirmService {

    private static Logger LOGGER = LoggerFactory.getLogger(MailConfirmService.class);

    /**
     * 验证码发送间隔120s
     */
    private static final Integer CONFIRM_SENDING_INTERVAL = 120;

    /**
     * 验证码有效期300s
     */
    private static final Integer CONFIRM_CODE_EXPIRED = 300;

    @Autowired
    private IMailConfirmDAO mailConfirmDAO;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UserService userService;

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private MailSendConfig mailSendConfig;

    @Autowired
    private ConfigCenterUtil configCenterUtil;

    @Autowired
    private TenantTierConfig tenantTierConfig;

    private final static String defaultTenantId = "Vicoo";

    @Autowired
    private AppAccountConfig appAccountConfig;

    @Resource
    private UserVipService userVipService;

    @Resource
    private EmailPayConfig emailPayConfig;

    /**
     * 查询指定邮箱、手机号近期是否有申请过验证码
     *
     * @param mailConfirmRequest
     * @return
     */
    public MailConfirmRequest getMailConfirm(MailConfirmRequest mailConfirmRequest) {
        return mailConfirmDAO.getMailConfirm(mailConfirmRequest);
    }

    /**
     * 查询指定用户收到的验证码是否和后端发送的匹配
     *
     * @param mailConfirmRequest
     * @return
     */
    public MailConfirmRequest getMailConfirmWithCode(MailConfirmRequest mailConfirmRequest, Integer userId) {
        mailConfirmRequest.setUserId(userId);
        return mailConfirmDAO.getMailConfirmWithCode(mailConfirmRequest);
    }

    /**
     * 查询是否存在邮箱或者手机号记录
     *
     * @param mailConfirmRequest
     * @return
     */
    public MailConfirmRequest queryMailConfirm(MailConfirmRequest mailConfirmRequest) {
        return mailConfirmDAO.queryMailConfirm(mailConfirmRequest);
    }

    public Integer setMailConfirm(MailConfirmRequest mailConfirmRequest) {
        MailConfirmRequest confirm = queryMailConfirm(mailConfirmRequest);
        int row = 0;
        if (confirm == null) {
            row = mailConfirmDAO.insertMailConfirm(mailConfirmRequest);
        } else {
            confirm.setCode(mailConfirmRequest.getCode());
            confirm.setUsed(0);
            confirm.setCount(mailConfirmRequest.getCount());
            confirm.setUserId(mailConfirmRequest.getUserId());
            row = mailConfirmDAO.setMailConfirm(confirm);
        }
        return row;
    }

    public Integer setMailConfirmUsed(MailConfirmRequest mailConfirmRequest) {
        return mailConfirmDAO.setMailConfirmUsed(mailConfirmRequest);
    }

    /**
     * 邮箱验证
     *
     * @param registerRequest
     * @param email
     * @return
     */
    public Result mailConfirm(RegisterRequest registerRequest, String email, User stored) {
        MailConfirmRequest tmpConfirm = new MailConfirmRequest();
        tmpConfirm.setEmail(email);
        tmpConfirm.setPhone(registerRequest.getPhone());
        tmpConfirm.setCode(registerRequest.getCode());
        tmpConfirm.getApp().setTenantId(registerRequest.getApp().getTenantId());
        //从数据库获取邮箱
        MailConfirmRequest storedConfirm = this.getMailConfirm(tmpConfirm);

        if (storedConfirm == null) {
            //该邮箱无效，没有有效的验证码信息
            return Result.Error(-1034, "CONFIRM_NOT_REQUESTED");
        } else if (storedConfirm.getCode().isEmpty()) {
            //请求无验证码信息
            return Result.Error(-1032, "CONFIRM_CODE_REQUIRED");
        } else if (!storedConfirm.getCode().equals(registerRequest.getCode())) {
            //可验证次数-1
            storedConfirm.setCount(storedConfirm.getCount() - 1);
            mailConfirmDAO.setMailConfirm(storedConfirm);
            //验证码与该邮箱应收到的不匹配
            return Result.Error(-1031, "WRONG_CONFIRM_CODE");
        } else if (PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() > CONFIRM_CODE_PERIOD) {
            //验证码已过期
            return Result.Error(-1036, "CONFIRM_EXPIRED");
        } else if (storedConfirm.getCount() <= 0) {
            //验证码试错次数过多
            return Result.Error(-1035, "CONFIRM_BEYOND_LIMIT");
        } else {
            storedConfirm.setCountryNo(registerRequest.getCountryNo());
            if (stored == null) {
                //注册成功
                return loginService.registerSuccess(registerRequest, storedConfirm, email);
            } else {
                //重置密码成功
                return loginService.resetPasswordSuccess(registerRequest, storedConfirm, stored, email);
            }
        }
    }

    /**
     * 发送验证邮件
     * 通过传入不同的方法名，发送不同的验证邮件或返回不同的错误。如果想传入没有预先定义的方法名，会产生UNKNOWN_ERROR
     * 可以继续重构
     *
     * @param confirmRequest
     * @param methodName
     * @return
     */
    public Result sendConfirmEmail(MailConfirmRequest confirmRequest, String methodName) throws ClientException {
        //预先定义方法名
        //根据方法名不同，执行不同语句
        String resetConfirm = "resetConfirm";
        String registerConfirm = "registerConfirm";
        String email = confirmRequest.getEmail();
        String phone = confirmRequest.getPhone();
        User user = new User();
        user.setEmail(email);
        //根据Email得到用户
        User storedUser = userService.getUserByEmailAndTenantId(email, phone, confirmRequest.getApp().getTenantId());
        // 重置或者登录
        if (storedUser == null && (resetConfirm.equals(methodName) || SendEmailTypeEnums.SIGNIN.getCode().equals(confirmRequest.getCodeType()))) {
            //用户不存在
            return Result.Error(-1001, "ACCOUNT_NOT_REGISTERED");
        }else if (storedUser != null && registerConfirm.equals(methodName)) {
            //该邮箱、手机号已经使用，不能再进行注册
            return Result.Error(-1002, "ACCOUNT_IN_USE");
        }else if (!StringUtils.isEmpty(email) && PhosUtils.mailInvalid(email)) {
            //打印邮箱非法
            LOGGER.info("INVALID_EMAIL: " + email);
            //邮箱格式不合法
            return Result.Error(-1011, "INVALID_EMAIL");
        }else if (!StringUtils.isEmpty(phone) && !PhosUtils.phoneInvalid(phone)) {
            //打印邮箱非法
            LOGGER.info("INVALID_PHONE: " + email);
            //邮箱格式不合法
            return Result.Error(INVALID_PHONE, "INVALID_PHONE");
        }

        SendEmailTypeEnums sendEmailTypeEnums =
                StringUtils.hasLength(methodName) ? resetConfirm.equals(methodName) ?  SendEmailTypeEnums.RESET : SendEmailTypeEnums.SIGNINTO
                : SendEmailTypeEnums.codeOf(confirmRequest.getCodeType());
        return  this.sendMailConfirmType(confirmRequest,sendEmailTypeEnums);
    }

    /**
     * 记录验证码记录并发送邮件
     * @param confirmRequest
     * @param sendEmailTypeEnums
     * @return
     * @throws ClientException
     */
    public Result sendMailConfirmType(MailConfirmRequest confirmRequest,SendEmailTypeEnums sendEmailTypeEnums) throws ClientException {
        MailConfirmRequest storedConfirm = this.getMailConfirm(confirmRequest);

        if (storedConfirm != null && PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() < CONFIRM_SEND_INTERVAL) {
            //验证码请求频繁,直接返回成功，不重新发送验证码
            LOGGER.info("sendConfirmEmail phone:{},email:{},重新进入验证码页面，不重发验证码", confirmRequest.getPhone(), confirmRequest.getEmail());
            return Result.Success();
        }

        if (storedConfirm == null || PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() >= CONFIRM_CODE_PERIOD) {
            //新生成6位随机验证码
            String code = PhosUtils.randomNumString(6);
            //打印验证码
            confirmRequest.setCode(code);
            //设置再次发送的时间间隔
            confirmRequest.setCount(RETRY_TIME);
        }else{
            storedConfirm.setLanguage(confirmRequest.getLanguage());
            storedConfirm.setApp(confirmRequest.getApp());
            confirmRequest = storedConfirm;
        }

        this.setMailConfirm(confirmRequest);
        //根据调用方法不同，发送不同的验证码
        boolean sendResult = this.sendEmail(confirmRequest.getEmail(), confirmRequest.getPhone(), confirmRequest.getCode(), confirmRequest.getLanguage(), confirmRequest.getApp().getTenantId(), sendEmailTypeEnums, confirmRequest.getApp());
        return sendResult ? Result.Success() : Result.Failure("send error");
    }



    /**
     * 发送用来将邮箱/手机关联到账号的邮件
     *
     * @param confirmRequest
     * @return
     */
    public Result sendBindContactCode(MailConfirmRequest confirmRequest, Integer userId) throws ClientException {
        String email = confirmRequest.getEmail();
        String phone = confirmRequest.getPhone();
        String tenantId = confirmRequest.getApp().getTenantId();
        // 校验入参是否合法
        if (StringUtils.isEmpty(email) && StringUtils.isEmpty(phone)) {
            return NO_CONTACT.getResult();
        } else if (!StringUtils.isEmpty(email) && !StringUtils.isEmpty(phone)) {
            return BIND_MULTI_CONTACTS.getResult();
        }

        User storedUser = userService.getUserByEmailAndTenantId(email, phone, confirmRequest.getApp().getTenantId());
        if (storedUser != null) {
            //该邮箱或手机号已经使用，不能再进行注册
            return Result.Error(-1002, "ACCOUNT_IN_USE");
        }

        MailConfirmRequest storedConfirm = this.getMailConfirm(confirmRequest);
        if (storedConfirm != null && (PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() < CONFIRM_SEND_INTERVAL)) {
            //验证码请求频繁
            LOGGER.debug("-1033: FREQUENT_CONFIRM");
            return Result.Error(-1033, "FREQUENT_CONFIRM");
        }

        // 验证无误，准备发送
        String code = PhosUtils.randomNumString(6);
        confirmRequest.setCode(code);
        confirmRequest.setCount(RETRY_TIME);
        confirmRequest.setUserId(userId);
        this.setMailConfirm(confirmRequest);

        boolean sendResult = this.sendEmail(email, phone, confirmRequest.getCode(), confirmRequest.getLanguage(), tenantId, SendEmailTypeEnums.BINDCONTACT, confirmRequest.getApp());
        return sendResult ? Result.Success() : Result.Failure("send error");

    }

    /**
     * 验证邮件验证码是否正确
     *
     * @param confirmRequest
     * @return
     */
    public Result checkConfirm(MailConfirmRequest confirmRequest) {
        MailConfirmRequest storedConfirm = this.getMailConfirm(confirmRequest);
        if (storedConfirm == null) {
            //该邮箱并没有有效的验证码信息
            return Result.Error(-1034, "CONFIRM_NOT_REQUESTED");
        }
        if (storedConfirm.getCount() <= 0) {
            //验证码试错次数过多
            return Result.Error(-1035, "CONFIRM_BEYOND_LIMIT");
        }
        if (PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() > CONFIRM_CODE_PERIOD) {
            //验证码已过期
            return Result.Error(-1036, "CONFIRM_EXPIRED");
        }
        VerifyCodeResponseDO verifyCodeResponseDO = new VerifyCodeResponseDO();

        //验证成功
        if (storedConfirm.getCode().equals(confirmRequest.getCode())) {
            Result res = Result.Success();
            verifyCodeResponseDO.setRemaining(storedConfirm.getCount());

            res.setData(verifyCodeResponseDO);

            return res;
        }
        //验证失败
        else {
            //可验证次数-1
            storedConfirm.setCount(storedConfirm.getCount() - 1);
            mailConfirmDAO.setMailConfirm(storedConfirm);
            //验证码与该邮箱应收到的不匹配
            Result res = Result.Error(-1031, "WRONG_CONFIRM_CODE");
            verifyCodeResponseDO.setRemaining(storedConfirm.getCount());
            res.setData(verifyCodeResponseDO);
            return res;
        }
    }

    /**
     * 验证邮件验证码是否正确
     *
     * @param confirmRequest
     * @return
     */
    public Result checkOneFAConfirmCode(MailConfirmRequest confirmRequest) {
        MailConfirmRequest storedConfirm = this.getMailConfirm(confirmRequest);
        if (storedConfirm == null) {
            //该邮箱并没有有效的验证码信息
            return Result.Error(-1034, "CONFIRM_NOT_REQUESTED");
        }
        if (storedConfirm.getCount() <= 0) {
            //验证码试错次数过多
            return Result.Error(-1035, "CONFIRM_BEYOND_LIMIT");
        }
        if (PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() > CONFIRM_CODE_EXPIRED) {
            //验证码已过期
            return Result.Error(-1036, "CONFIRM_EXPIRED");
        }
        VerifyCodeResponseDO verifyCodeResponseDO = new VerifyCodeResponseDO();

        //验证成功
        if (storedConfirm.getCode().equals(confirmRequest.getCode())) {
            Result res = Result.Success();
            verifyCodeResponseDO.setRemaining(storedConfirm.getCount());

            res.setData(verifyCodeResponseDO);

            //验证通过设置该验证码已经使用过了
            this.setMailConfirmUsed(storedConfirm);
            return res;
        }
        //验证失败
        else {
            //可验证次数-1
            storedConfirm.setCount(storedConfirm.getCount() - 1);
            mailConfirmDAO.setMailConfirm(storedConfirm);
            //验证码与该邮箱应收到的不匹配
            Result res = Result.Error(-1031, "WRONG_CONFIRM_CODE");
            verifyCodeResponseDO.setRemaining(storedConfirm.getCount());
            res.setData(verifyCodeResponseDO);
            return res;
        }
    }

    /**
     * 检查邮件/短信验证码是否正确，如果正确则绑定此种联系方式
     *
     * @param confirmRequest
     * @return
     */
    public Result bindContact(MailConfirmRequest confirmRequest, Integer userId) {
        MailConfirmRequest storedConfirm = this.getMailConfirmWithCode(confirmRequest, userId);
        if (storedConfirm == null) {
            //该邮箱并没有有效的验证码信息
            return Result.Error(-1034, "CONFIRM_NOT_REQUESTED");
        }
        if (storedConfirm.getCount() <= 0) {
            //验证码试错次数过多
            return Result.Error(-1035, "CONFIRM_BEYOND_LIMIT");
        }
        if (PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() > CONFIRM_CODE_PERIOD) {
            //验证码已过期
            return Result.Error(-1036, "CONFIRM_EXPIRED");
        }

        Map<String, Integer> limitMap = new HashMap<>();
        String remainingStr = "remaining";
        //验证成功
        if (storedConfirm.getCode().equals(confirmRequest.getCode())) {
            User user = userService.queryUserById(userId);

            if (!StringUtils.isEmpty(storedConfirm.getEmail())) {
                user.setEmail(storedConfirm.getEmail());
            } else if (!StringUtils.isEmpty((storedConfirm.getPhone()))) {
                user.setPhone(storedConfirm.getPhone());
            }

            userService.updateUserContact(user);

            Result res = Result.Success();
            limitMap.put(remainingStr, storedConfirm.getCount());
            res.setData(limitMap);
            return res;
        }
        //验证失败
        else {
            //可验证次数-1
            storedConfirm.setCount(storedConfirm.getCount() - 1);
            mailConfirmDAO.setMailConfirm(storedConfirm);
            //验证码与该邮箱应收到的不匹配
            Result res = Result.Error(-1031, "WRONG_CONFIRM_CODE");
            limitMap.put(remainingStr, storedConfirm.getCount());
            res.setData(limitMap);
            return res;
        }
    }

    /**
     * 将用户的注册记录删除
     *
     * @param user
     */
    public void deleteMailConfirm(User user) {
        if (StringUtils.isEmpty(user.getPhone()) && StringUtils.isEmpty(user.getEmail())) {
            return;
        }
        mailConfirmDAO.deleteMailConfirm(user);
    }

    /**
     * 发送验证码邮件/短信
     *
     * @param email
     * @param phone
     * @param code
     * @param language
     * @param tenantId
     * @param enums
     * @return
     * @throws ClientException
     */
    public boolean sendEmail(String email, String phone, String code, String language, String tenantId, SendEmailTypeEnums enums, AppInfo appRequestBase) throws ClientException {
        if (!StringUtils.isEmpty(email)) {
            //邮件
            if (!mailSendConfig.getConfig().containsKey(tenantId)) {
                LOGGER.info("getSendEmailTitle no tenantId config,tenantId:{}", tenantId);
                throw new ParamException(INVALID_PARAMS.getCode(), "no tenantId");
            }
            if (!mailSendConfig.getConfig().get(tenantId).containsKey(language)) {
                LOGGER.info("getSendEmailTitle no language config,tenantId:{},language:{}", tenantId, language);
                throw new ParamException(INVALID_PARAMS.getCode(), "no language");
            }
            MailSend mailSend = mailSendConfig.getConfig().get(tenantId).get(language);
            String title = this.getSendEmailTitle(mailSend, enums)
                    .replace(defaultTenantId, this.queryTenantName(tenantId, appRequestBase.getApiVersion()))
                    .replace("$APP_NAME", this.queryTenantName(tenantId, appRequestBase.getApiVersion()))
                    .replace("{Appname}", this.queryTenantName(tenantId, appRequestBase.getApiVersion()));
            String body = this.getSendEmailBody(email, code, enums, mailSend, tenantId, appRequestBase);
            EmailAccount emailAccount = appAccountConfig.queryEmailAccount(tenantId);
            body = body.replace("$SUPPORT_EMAIL",emailAccount.getAccount());

            return Mail.SendHtmlEmail(email, title, body,emailAccount);
        } else {
            //短信
            return messageUtil.sendCodePhone(phone, code, language, tenantId);
        }
    }

    public boolean sendEmail(User user, SendEmailTypeEnums enums) throws ClientException {
        AppInfo appRequestBase = new AppInfo();
        appRequestBase.setTenantId(user.getTenantId());
        appRequestBase.setApiVersion("v1");
        appRequestBase.setAppType("iOS");
        return this.sendEmail(user.getEmail(),user.getPhone(),"",user.getLanguage(),user.getTenantId(),enums,appRequestBase);
    }

    /**
     * 获取邮件title
     *
     * @param enums
     * @return
     */
    public String getSendEmailTitle(MailSend mailSend, SendEmailTypeEnums enums) {

        String title = null;
        switch (enums) {
            case SIGNINTO:
                title = mailSend.getSignInTitle();
                break;
            case RESET:
                title = mailSend.getResetTitle();
                break;
            case BINDCONTACT:
                title = mailSend.getBindContactTitle();
                break;
            case SIGNIN:
                title = mailSend.getLoginInTitle();
                break;
            case SIGNIN_PAY_EMAIL:
                title = mailSend.getSignInPayTitle();
                break;
            case FREE_TRIAL_EMAIL:
                title = mailSend.getFreeTrialTitle();
                break;
            case DUE_REMINDER_EMAIL:
                title = mailSend.getDueReminderTitle();
                break;
            case BUY_REMINDER_EMAIL:
                title = mailSend.getBuyReminderTitle();
                break;
            case SHARE_STATION_NEW:
            case SHARE_STATION:
                title = mailSend.getStationShareEmailTitle();
                break;
            case TWO_YEAR_FREE_TIER_30DAY_NOTIFY:
                title = mailSend.getTwoYearFree30DayEmailTitle();
                break;
            case TWO_YEAR_FREE_TIER_EXPIRE_NOTIFY:
                title = mailSend.getTwoYearFreeExpireEmailTitle();
                break;
            case TWO_YEAR_FREE_TIER_180DAY_NOTIFY:
                title = mailSend.getTwoYearFree180DayEmailTitle();
                break;
            case USER_FEEDBACK_4_KB_APP:
                title = mailSend.getUserFreeBack4KBAppTitle();
                break;
            case TWO_TEMP_FA:
                title = mailSend.getTwoTempFaEmailTitle();
                break;
            default:
                LOGGER.info("getSendEmailTitle no enums config,enums:{}", enums.getCode());
                throw new ParamException(INVALID_PARAMS.getCode(), "no enums");
        }
        return title.replace("{awarenessService}",mailSend.getAwarenessService());
    }

    /**
     * 获取邮件正文
     *
     * @param userMail
     * @param confirmCode
     * @param enums
     * @param mailSend
     * @return
     */
    public String getSendEmailBody(String userMail, String confirmCode, SendEmailTypeEnums enums, MailSend mailSend, String tenantId, AppInfo appRequestBase) {
        String body = null;
        User user = null;
        String vipExpireDay = null;
        switch (enums) {
            case SIGNINTO:
                body = mailSend.getSignInContent();
                break;
            case RESET:
                body = mailSend.getResetContent();
                break;
            case BINDCONTACT:
                body = mailSend.getBindContactContent();
                break;
            case SIGNIN:
                body = mailSend.getLoginInContent();
                break;
            case SHARE_STATION_NEW:
                body = mailSend.getStationShareEmailContentNew();
                break;
            case SHARE_STATION:
                body = mailSend.getStationShareEmailContent();
                break;
            case TWO_YEAR_FREE_TIER_30DAY_NOTIFY:
                body = mailSend.getTwoYearFree30DayEmailContent();
                body = this.queryEmailBodyTierExpire(body,userMail,tenantId);
                break;
            case TWO_YEAR_FREE_TIER_180DAY_NOTIFY:
                body = mailSend.getTwoYearFree180DayEmailContent();
                body = this.queryEmailBodyTierExpire(body,userMail,tenantId);
                break;
            case TWO_YEAR_FREE_TIER_EXPIRE_NOTIFY:
                body = mailSend.getTwoYearFreeExpireEmailContent();
                body = this.queryEmailBodyTierExpire(body,userMail,tenantId);
                break;
            case USER_FEEDBACK_4_KB_APP:
                body = mailSend.getUserFreeBack4KBAppContent();
                break;
            case TWO_TEMP_FA:
                body = mailSend.getTwoTempFaEmailContent();
                break;
            default:
                LOGGER.info("getSendEmailBody no enums config,enums:{}", enums.getCode());
                throw new ParamException(INVALID_PARAMS.getCode(), "no enums");
        }

        body = body.replace("$USER_MAIL", userMail).replace("$CONFIRM_CODE", confirmCode)
                .replace("{copywright}", configCenterUtil.queryCopyRight(tenantId, appRequestBase))
                .replace("{copyright}", configCenterUtil.queryCopyRight(tenantId, appRequestBase))
                .replace(defaultTenantId, this.queryTenantName(tenantId, appRequestBase.getApiVersion()))
                .replace("$APP_NAME",this.queryTenantName(tenantId, appRequestBase.getApiVersion()))
        ;
        return body;
    }

    /**
     * 替换到期文案变量
     * @param body
     * @param userMail
     * @param tenantId
     * @return
     */
    private String queryEmailBodyTierExpire(String body,String userMail,String tenantId){
        User user = userService.getUserByEmailAndTenantId(userMail,"",tenantId);
        if(user == null){
            return body;
        }
        body = body.replace("$USER_NAME",user.getName());
        String vipExpireDay = userVipService.queryCurrentFreeTierEndDay(user.getId(),user.getCountryNo(),user.getLanguage());
        if(StringUtils.hasLength(vipExpireDay)){
            body = body.replace("$EXPIRE_DAY",vipExpireDay);
        }
        return body;
    }

    /**
     * 获取邮件正文-支付邮件
     *
     * @param enums
     * @param mailSend
     * @return
     */
    private String getSendEmailPaymentBody(SendEmailTypeEnums enums, MailSend mailSend, User user,  String tenantId) {
        String body = null;
        StringBuilder bodyJoin  = new StringBuilder();
        bodyJoin.append("<html>");
        bodyJoin.append("<head>");
        bodyJoin.append("<title>");
        String title = this.getSendEmailTitle(mailSend,enums).replace(defaultTenantId, this.queryTenantName(tenantId, null));
        bodyJoin.append(title);
        bodyJoin.append("</title>");
        bodyJoin.append("</head>");
        bodyJoin.append("<body>");
        bodyJoin.append("<h1>").append(title).append("</h1>");
        switch (enums) {
            case SIGNIN_PAY_EMAIL:
                body = this.getEmailBodyWelcome(mailSend,user,tenantId);
                break;
            case FREE_TRIAL_EMAIL:
                body = this.getEmailBodyFreeTrial(mailSend,user,tenantId);
                break;
            case DUE_REMINDER_EMAIL:
                body = this.getEmailBodyDueReminder(mailSend,user,tenantId);
                break;
            case BUY_REMINDER_EMAIL:
                body = this.getEmailBodyBuyReminder(mailSend,user,tenantId);
                break;
            default:
                LOGGER.info("getSendEmailBody no enums config,enums:{}", enums.getCode());
                throw new ParamException(INVALID_PARAMS.getCode(), "no enums");
        }

        body = body.replace(defaultTenantId, this.queryTenantName(tenantId, null))
                .replace("$SUPPORT_EMAIL",emailPayConfig.getSupportEmail().get(tenantId));
        bodyJoin.append(body);
        bodyJoin.append("</body>");
        bodyJoin.append("</html>");
        return bodyJoin.toString();
    }

    /**
     * 获取需要展示的公司标记
     *
     * @param tenantId
     * @return
     */
    public String queryTenantName(String tenantId, String apiVersion) {
        //此处判断因为展示的是公司全名而不是tenantId
        String tenantName = tenantTierConfig.getConfig() != null && tenantTierConfig.getConfig().containsKey(tenantId) ?
                tenantTierConfig.getConfig().get(tenantId) : StringUtils.capitalize(tenantId);

        if (!tenantName.equals(AppConstants.tierNameVicoo)) {
            //tenantId非vicoo 的不处理
            return tenantName;
        }
        return AppConstants.defaultVersion.equals(apiVersion) ? tenantName : AppConstants.tierNameVicoHome;
    }


    /**
     * 向用户发送支付相关邮件
     * @param userId
     * @param enums
     */
    public void sendToUserPaymentEmail(Integer userId ,SendEmailTypeEnums enums){
        try {
            User user = userService.queryUserById(userId);
            if(StringUtils.isEmpty(user.getEmail())){
                return;
            }
            String tenantId = userService.queryTenantIdById(user.getId());
            MailSend mailSend = mailSendConfig.getConfig().get(tenantId).get(user.getLanguage());
            String title = this.getSendEmailTitle(mailSend, enums).replace(defaultTenantId, this.queryTenantName(tenantId, null));
            String body = this.getSendEmailPaymentBody(enums,mailSend,user,tenantId);

            EmailAccount emailAccount = appAccountConfig.queryPayEmailAccount(enums,tenantId);
            Mail.SendHtmlEmail(user.getEmail(), title, body,emailAccount);
        }catch (Exception e){
            com.addx.iotcamera.util.LogUtil.error(LOGGER, "sendToUserPaymentEmail error",e);
        }
    }

    /**
     * 注册后的welcome邮件
     * @param mailSend
     * @param user
     * @param tenantId
     * @return
     */
    private String getEmailBodyWelcome(MailSend mailSend,User user,String tenantId){
        StringJoiner email = new StringJoiner(PAY_EMAIL_BR);
        email.add("<p>");
        email.add(mailSend.getPayEmailWelcome().replace("{USER_MAIL}",user.getEmail()));
        email.add("</p>");
        email.add("<p>").add(mailSend.getPayEmailAd()).add("</p>");
        EmailPayUrlDO emailPayUrlDO = emailPayConfig.getUrlConfig().get(tenantId);
        if(emailPayUrlDO == null){
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "emailPayUrlDO config not contains {}" ,tenantId);
            return "";
        }
        email.add(PAY_EMAIL_IMAGE.replace("{emailPayImageUrl}",emailPayUrlDO.getImagUrl()));
        email.add(PAY_EMAIL_A_HREF.replace("{url}",emailPayUrlDO.getOfficialUrl()).replace("{content}",mailSend.getPayEmailLearnMore()));
        email.add("<p>").add(mailSend.getPayEmailWelcomeFeed()).add("</p>");
        email.add("<p>").add(mailSend.getPayEmailFrom()).add("</p>");
        return email.toString();
    }


    /**
     * 免费试用邮件
     * @param mailSend
     * @param user
     * @param tenantId
     * @return
     */
    private String getEmailBodyFreeTrial(MailSend mailSend,User user,String tenantId){
        StringJoiner email = new StringJoiner(PAY_EMAIL_BR);
        email.add("<p>").add(mailSend.getPayEmailHello().replace("{USER_MAIL}",user.getEmail())).add("</p>");
        email.add("<p>").add(mailSend.getPayEmailAd()).add("</p>");
        EmailPayUrlDO emailPayUrlDO = emailPayConfig.getUrlConfig().get(tenantId);
        if(emailPayUrlDO == null){
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "emailPayUrlDO config not contains {}" ,tenantId);
            return "";
        }
        email.add(PAY_EMAIL_IMAGE.replace("{emailPayImageUrl}",emailPayUrlDO.getImagUrl()));
        email.add(PAY_EMAIL_A_HREF.replace("{url}",emailPayUrlDO.getOfficialUrl()).replace("{content}",mailSend.getPayEmailLearnMore()));
        email.add("<p>").add(mailSend.getPayEmailFrom()).add("</p>");
        return email.toString();
    }

    /**
     * 到期提醒邮件
     * @param mailSend
     * @param user
     * @param tenantId
     * @return
     */
    private String getEmailBodyDueReminder(MailSend mailSend,User user,String tenantId){
        StringJoiner email = new StringJoiner(PAY_EMAIL_BR);
        email.add("<p>").add(mailSend.getPayEmailHello().replace("{USER_MAIL}",user.getEmail())).add("</p>");
        UserVipTier userVipTier = userVipService.queryUserVipInfo(user.getId(), user.getLanguage(),userService.queryTenantIdById(user.getId()));
        String tierName = userVipTier.getTierName();
        Integer notifyDay = userVipTier.getNotifyDay();

        email.add("<p>").add(mailSend.getPayEmailExpireTime().replace("{tierName}",tierName).replace("{endDate}",String.valueOf(notifyDay))).add("</p>");
        email.add("<p>").add(mailSend.getPayEmailExpireRemindMessage()).add("</p>");
        EmailPayUrlDO emailPayUrlDO = emailPayConfig.getUrlConfig().get(tenantId);
        if(emailPayUrlDO == null){
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "emailPayUrlDO config not contains {}" ,tenantId);
            return "";
        }
        email.add(PAY_EMAIL_IMAGE.replace("{emailPayImageUrl}",emailPayUrlDO.getImagUrl()));
        email.add(PAY_EMAIL_A_HREF.replace("{url}",emailPayUrlDO.getOfficialUrl()).replace("{content}",mailSend.getPayEmailLearnMore()));
        email.add("<p>").add(mailSend.getPayEmailFrom()).add("</p>");
        return email.toString();
    }


    /**
     * 购买提醒邮件
     * @param mailSend
     * @param user
     * @param tenantId
     * @return
     */
    private String getEmailBodyBuyReminder(MailSend mailSend,User user,String tenantId){
        StringJoiner email = new StringJoiner(PAY_EMAIL_BR);
        email.add("<p>").add(mailSend.getPayEmailHello().replace("{USER_MAIL}",user.getEmail())).add("</p>");
        email.add("<p>").add(mailSend.getPayEmailAd()).add("</p>");
        EmailPayUrlDO emailPayUrlDO = emailPayConfig.getUrlConfig().get(tenantId);
        if(emailPayUrlDO == null){
            com.addx.iotcamera.util.LogUtil.warn(LOGGER, "emailPayUrlDO config not contains {}" ,tenantId);
            return "";
        }
        email.add(PAY_EMAIL_IMAGE.replace("{emailPayImageUrl}",emailPayUrlDO.getImagUrl()));
        email.add(PAY_EMAIL_A_HREF.replace("{url}",emailPayUrlDO.getOfficialUrl()).replace("{content}",mailSend.getPayEmailLearnMore()));
        email.add("<p>").add(mailSend.getPayEmailFrom()).add("</p>");
        return email.toString();
    }


    /**
     * 发送验证码
     * @param request
     * @return
     * @throws ClientException
     */
    public Result sendConfirmCode(MailConfirmRequest request) throws ClientException {
        User user = userService.queryUserById(request.getUserId());
        if(user == null){
            //已注销
            throw new BaseException(USER_ACCOUNT_CANCEL);
        }

        SendEmailTypeEnums enums = SendEmailTypeEnums.codeOf(request.getCodeType());
        return this.sendMailConfirmType(request,enums);
    }

    public Result sendCode(Integer userId, MailConfirmRequest request) throws ClientException {
        request.setCodeType(TWO_TEMP_FA.getCode());
        // 直接处理验证码发送逻辑，而不是调用 sendConfirmCode
        User user = userService.queryUserById(userId);
        if(user == null){
            //已注销
            throw new BaseException(USER_ACCOUNT_CANCEL);
        }
        //设置主邮箱
        request.setEmail(user.getEmail());

        SendEmailTypeEnums enums = SendEmailTypeEnums.codeOf(request.getCodeType());
        MailConfirmRequest storedConfirm = this.getMailConfirm(request);

        if (storedConfirm != null && PhosUtils.getUTCStamp() - storedConfirm.getActiveTime() < CONFIRM_SENDING_INTERVAL) {
            //验证码请求频繁,直接返回成功，不重新发送验证码
            LOGGER.info("sendCode phone:{},email:{},重新进入验证码页面，不重发验证码", request.getPhone(), request.getEmail());
            return Result.Error(-1033, "FREQUENT_CONFIRM");
        }

        //新生成6位随机验证码
        String code = PhosUtils.randomNumString(6);
        //重置验证次数
        request.setCount(RETRY_TIME);
        //设置验证码
        request.setCode(code);

        this.setMailConfirm(request);
        boolean sendResult = this.sendEmail(request.getEmail(), request.getPhone(), request.getCode(),
                request.getLanguage(), request.getApp().getTenantId(), enums, request.getApp());
        return sendResult ? Result.Success() : Result.Failure("send error");
    }


}
