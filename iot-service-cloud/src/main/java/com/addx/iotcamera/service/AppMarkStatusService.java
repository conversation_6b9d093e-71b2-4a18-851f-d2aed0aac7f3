package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.app.StatusMarkRequest;
import com.addx.iotcamera.bean.app.StatusMarkResponse;
import com.addx.iotcamera.bean.db.AppMarkStatusDO;
import com.addx.iotcamera.dao.AppMarkStatusDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * App 标记状态服务接口
 */
@Slf4j
@Service
public class AppMarkStatusService {

    @Autowired
    private AppMarkStatusDAO appMarkStatusDAO;

    public StatusMarkResponse getStatusMark(StatusMarkRequest request) {
        log.debug("Querying status mark with userId={}, serialNumber={}, statusKey={}",
                request.getUserId(), request.getSerialNumber(), request.getStatusKey());
                
        // 查询状态记录
        AppMarkStatusDO statusDO = appMarkStatusDAO.getByUserIdAndSerialNumberAndStatusKey(
                request.getUserId(), 
                request.getSerialNumber(), 
                request.getStatusKey()
        );
        
        log.debug("Query result: {}", statusDO);
        
        // 构建响应
        return StatusMarkResponse.builder()
                .statusKey(request.getStatusKey())
                .clickTimes(statusDO != null ? statusDO.getClickTimes() : 0)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean clickStatusMark(StatusMarkRequest request) {
        log.debug("Clicking status mark with userId={}, serialNumber={}, statusKey={}",
                request.getUserId(), request.getSerialNumber(), request.getStatusKey());
                
        // 查询状态记录
        AppMarkStatusDO statusDO = appMarkStatusDAO.getByUserIdAndSerialNumberAndStatusKey(
                request.getUserId(), 
                request.getSerialNumber(), 
                request.getStatusKey()
        );
        
        log.debug("Found existing status record: {}", statusDO);
        
        int result;
        if (statusDO != null) {
            // 增加点击次数
            result = appMarkStatusDAO.increaseClickTimes(
                    request.getUserId(), 
                    request.getSerialNumber(), 
                    request.getStatusKey()
            );
            log.debug("Increased click times, result: {}", result);
        } else {
            // 插入新记录
            AppMarkStatusDO newStatusDO = AppMarkStatusDO.builder()
                    .userId(request.getUserId())
                    .serialNumber(request.getSerialNumber())
                    .statusKey(request.getStatusKey())
                    .clickTimes(1)
                    .build();
            result = appMarkStatusDAO.insertRecord(newStatusDO);
            log.debug("Inserted new status record, result: {}, id: {}", result, newStatusDO.getId());
        }
        
        return result > 0;
    }
} 