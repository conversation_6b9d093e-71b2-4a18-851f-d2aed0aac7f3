package com.addx.iotcamera.shardingjdbc.dao.library;

import com.addx.iotcamera.bean.app.LibraryRequest;
import com.addx.iotcamera.bean.app.LibraryStatusTbRequest;
import com.addx.iotcamera.bean.app.QuerySliceListVO;
import com.addx.iotcamera.bean.db.LibraryEventDO;
import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.domain.library.LibraryCountDay;
import com.addx.iotcamera.bean.domain.library.StorageClearLibraryInfo;
import com.addx.iotcamera.bean.video.VideoAndEventCount;
import com.addx.iotcamera.bean.video.VideoTagSummary;
import com.addx.iotcamera.shardingjdbc.readonly.ShardingReadOnly;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface IShardingLibraryStatusDAO {


    // mark in use
    @Insert("INSERT INTO `video-library-db`.`library_status` (`user_id`, `admin_id`, `library_id`, `trace_id`, `timestamp`,`serial_number`,`video_event`, `tier_id`, `tier_group_id`, `ttl_timestamp`, `support_magic_pix`, `extension`) " +
            "values (#{userId}, #{adminId}, #{libraryId}, #{traceId}, #{timestamp}, #{serialNumber}, #{eventKey}, #{deviceUseCurrentTierId}, #{deviceUseCurrentTierGroupId}, #{ttlTimestamp}, #{supportMagicPix}, #{extension})")
    Integer insertLibraryStatus(@Param("userId") Integer userId,
                                @Param("adminId") Integer adminId,
                                @Param("libraryId") Integer libraryId,
                                @Param("traceId") String traceId,
                                @Param("serialNumber") String serialNumber,
                                @Param("timestamp") Integer timestamp,
                                @Param("eventKey") String eventKey,
                                @Param("deviceUseCurrentTierId") Integer deviceUseCurrentTierId,
                                @Param("deviceUseCurrentTierGroupId") Integer deviceUseCurrentTierGroupId,
                                @Param("ttlTimestamp") Integer ttlTimestamp,
                                @Param("supportMagicPix") Boolean supportMagicPix,
                                @Param("extension") String extension);

    @Insert("<script>INSERT INTO `video-library-db`.`library_status` (`user_id`, `admin_id`, `library_id`, `trace_id`" +
            ", `timestamp`,`serial_number`,`video_event`, `tier_id`, `tier_group_id`, `ttl_timestamp`, `extension`" +
            " <if test=\"endTimestamp!=null\">,`end_timestamp`</if>" +
            " <if test=\"mainTraceId!=null\">,`main_trace_id`</if>" +
            " <if test=\"videoType!=null\">,`video_type`</if>" +
            " <if test=\"serviceName!=null\">,`service_name`</if>" +
            " <if test=\"resolution!=null\">,`resolution`</if>" +
            " <if test=\"tags!=null\">,`tags`</if>" +
            " <if test=\"aiEdgeTags!=null\">,`ai_edge_tags`</if>" +
            " <if test=\"activityZoneId!=null\">,`activity_zone_id`</if>" +
            " <if test=\"doorbellTags!=null\">,`doorbell_tags`</if>" +
            " <if test=\"deviceCallEventTag!=null\">,`device_call_event_tag`</if>" +
            " <if test=\"supportMagicPix!=null\">,`support_magic_pix`</if>" +
            ") values (#{userId}, #{adminId}, #{libraryId}, #{traceId}" +
            ", #{timestamp}, #{serialNumber}, #{videoEvent}, #{tierId}, #{tierGroupId}, #{ttlTimestamp}, #{extension}" +
            " <if test=\"endTimestamp!=null\">,#{endTimestamp}</if>" +
            " <if test=\"mainTraceId!=null\">,#{mainTraceId}</if>" +
            " <if test=\"videoType!=null\">,#{videoType}</if>" +
            " <if test=\"serviceName!=null\">,#{serviceName}</if>" +
            " <if test=\"resolution!=null\">,#{resolution}</if>" +
            " <if test=\"tags!=null\">,#{tags}</if>" +
            " <if test=\"aiEdgeTags!=null\">,#{aiEdgeTags}</if>" +
            " <if test=\"activityZoneId!=null\">,#{activityZoneId}</if>" +
            " <if test=\"doorbellTags!=null\">,#{doorbellTags}</if>" +
            " <if test=\"deviceCallEventTag!=null\">,#{deviceCallEventTag}</if>" +
            " <if test=\"supportMagicPix!=null\">,#{supportMagicPix}</if>" +
            ")</script>")
    Integer insertLibraryStatusV2(LibraryStatusTb libraryStatus);

    // mark in use
    @Update("<script>" +
            "UPDATE `video-library-db`.`library_status` SET `missing` = #{missing} " +
            "WHERE `user_id` = #{userId}" +
            "<if test = 'traceId != null'> AND `trace_id` = #{traceId}</if>" +
            "<if test = 'libraryId != null'> AND `library_id` = #{libraryId}</if>" +
            "</script>")
    Integer updateLibraryRead(LibraryStatusTbRequest status);

    // mark in use
    @Update("<script>" +
            "UPDATE `video-library-db`.`library_status` SET `missing` = #{missing} " +
            "WHERE `user_id` = #{userId}" +
            "<if test = 'traceIdList != null'>  AND `trace_id` in <foreach collection='traceIdList' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach></if>" +
            "<if test = 'libraryIds != null'> AND `library_id` in (${libraryIds})</if>" +
            "</script>")
    Integer updateLibraryReadByLibraryIds(LibraryStatusTbRequest status);

    // mark in use
    @Update("<script>" +
            "UPDATE `video-library-db`.`library_status` SET `marked` = #{marked} " +
            "WHERE  `user_id` = #{userId}" +
            "<if test = 'traceId != null'> AND `trace_id` = #{traceId}</if>" +
            "<if test = 'libraryId != null'> AND `library_id` = #{libraryId}</if>" +
            "</script>")
    Integer updateLibraryMarked(LibraryStatusTbRequest status);

    // mark in use
    @Update("<script>" +
            "UPDATE `video-library-db`.`library_status` SET `marked` = #{marked} " +
            "WHERE  `user_id` = #{userId}" +
            "<if test = 'traceIdList != null'>  AND `trace_id` in <foreach collection='traceIdList' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach></if>" +
            "<if test = 'libraryIds != null'> AND `library_id` in (${libraryIds})</if>" +
            "</script>")
    Integer updateLibraryMarkedByLibraryIds(LibraryStatusTbRequest status);

    // mark in use
    @Update("<script>" +
            " UPDATE `video-library-db`.`library_status` SET" +
            " `library_id`=`library_id`" +
            " <if test=\"libraryStatusTb.tags!=null\">,`tags`=#{libraryStatusTb.tags}</if>" +
            " <if test=\"libraryStatusTb.activityZoneId!=null\">,`activity_zone_id`=#{libraryStatusTb.activityZoneId}</if>" +
            " <if test=\"libraryStatusTb.doorbellTags!=null\">,`doorbell_tags`=#{libraryStatusTb.doorbellTags}</if>" +
            " <if test=\"libraryStatusTb.deviceCallEventTag!=null\">,`device_call_event_tag`=#{libraryStatusTb.deviceCallEventTag}</if>" +
            "<if test=\"libraryStatusTb.extension!=null\">,`extension`=#{libraryStatusTb.extension}</if>" +
            " WHERE `user_id`=#{libraryStatusTb.userId} AND `trace_id` = #{libraryStatusTb.traceId} " +
            "</script>")
    Integer updateLibraryTags(@Param("libraryStatusTb") LibraryStatusTb libraryStatusTb);

    @Update("<script>" +
            " UPDATE `video-library-db`.`library_status` SET" +
            " `library_id`=`library_id`" +
            " <if test=\"libraryStatus.tags!=null\">,`tags`=#{libraryStatus.tags}</if>" +
            " <if test=\"libraryStatus.aiEdgeTags!=null\">,`ai_edge_tags`=#{libraryStatus.aiEdgeTags}</if>" +
            " <if test=\"libraryStatus.activityZoneId!=null\">,`activity_zone_id`=#{libraryStatus.activityZoneId}</if>" +
            " <if test=\"libraryStatus.doorbellTags!=null\">,`doorbell_tags`=#{libraryStatus.doorbellTags}</if>" +
            " <if test=\"libraryStatus.deviceCallEventTag!=null\">,`device_call_event_tag`=#{libraryStatus.deviceCallEventTag}</if>" +
            "<if test=\"libraryStatus.extension!=null\">,`extension`=#{libraryStatus.extension}</if>" +
            "<if test=\"libraryStatus.timestamp!=null\">,`timestamp`=#{libraryStatus.timestamp}</if>" +
            "<if test=\"libraryStatus.endTimestamp!=null\">,`end_timestamp`=#{libraryStatus.endTimestamp}</if>" +
            " WHERE `user_id`=#{libraryStatus.userId} AND `trace_id` = #{libraryStatus.traceId} " +
            "</script>")
    Integer updateLibraryStatus(@Param("libraryStatus") LibraryStatusTb libraryStatusTb);

    // mark in use
    @Delete("<script>" +
            "DELETE FROM `video-library-db`.`library_status`" +
            "WHERE `user_id`=#{userId} AND `trace_id` = #{traceId}" +
            "</script>")
    Integer deleteOneLibraryStatus(Integer userId, Integer libraryId, String traceId);

    // mark in use
    @Delete({"<script>delete from `video-library-db`.library_status where user_id = #{userId} " +
            "<if test = 'traceIdList != null'>  AND `trace_id` in <foreach collection='traceIdList' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach></if>" +
            "</script>"})
    Integer deleteLibraryStatus(@Param("userId") Integer userId, @Param("traceIdList") List<String> traceIdList);

    // mark in use
    @ShardingReadOnly
    @SelectProvider(type = ShardinglibraryStatusProvider.class, method = "selectLibraryStatus")
    LibraryStatusTb selectLibraryStatus(LibraryStatusTb request);

    @ShardingReadOnly
    @Select("<script>" +
            " select library_id, admin_id, trace_id, main_trace_id, video_type, service_name, user_id, missing, marked, video_event, `timestamp`, extension from " +
            " `video-library-db`.`library_status`" +
            " where `user_id`=#{userId} and `trace_id`=#{traceId} " +
            "</script>")
    LibraryStatusTb selectLibraryStatusByTraceIdAndUserId(@Param("traceId") String traceId, @Param("userId") Integer userId);

    // mark in use
    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectLibrary")
    List<LibraryStatusTb> selectLibraryStatusList(LibraryRequest request);

    @ShardingReadOnly
    @Select("<script>" +
            " select * from `library_status`" +
            " FORCE INDEX(index_userid_video_event)" + // 强制使用 user_id & video_event 索引
            " where `user_id`=#{userId}" +
            " and `video_event` in" +
            " <foreach collection='videoEvents' item='videoEvent' open='(' separator=',' close=')'>#{videoEvent}</foreach>" +
            " and `video_type` in" +
            " <foreach collection='videoTypes' item='videoType' open='(' separator=',' close=')'>#{videoType}</foreach>" +
            "</script>")
    List<LibraryStatusTb> selectByUserIdAndVideoEventsAndVideoTypes(@Param("userId") Integer userId
            , @Param("videoEvents") Collection<Long> videoEvents, @Param("videoTypes") Collection<Integer> videoTypes);

    // mark in use
    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectLibraryEvent")
    List<LibraryEventDO> selectLibraryEventList(LibraryRequest request);

    // mark in use
    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectCountLibrary")
    Integer selectCountLibraryStatus(LibraryRequest request);

    // mark in use
    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectCountLibraryEvent")
    Integer selectCountLibraryEvent(LibraryRequest request);

    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectVideoAndEventCount")
    VideoAndEventCount selectVideoAndEventCount(LibraryRequest request);

    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectLibraryCountDay")
    LibraryCountDay selectLibraryCountDay(LibraryRequest request);

    @ShardingReadOnly
    @SelectProvider(type = ShardingSqlProvider.class, method = "selectLibraryCountDayGroup")
    List<LibraryCountDay> selectLibraryCountDayGroup(LibraryRequest request);

    // 分库分表里，每个库都会产生一条结果，需要在代码层面合并
    @ShardingReadOnly
    @Select("select serial_number sn\n" +
            ",group_concat(distinct tags) tags\n" +
            ",group_concat(distinct ai_edge_tags) aiEdgeTags\n" +
            ",group_concat(distinct doorbell_tags) doorbellTags\n" +
            ",group_concat(distinct device_call_event_tag) deviceCallEventTag\n" +
            "from `video-library-db`.library_status where user_id=#{userId} and deleted=0\n" +
            "group by serial_number;")
    @Results({
            @Result(property = "sn", column = "sn", jdbcType = JdbcType.VARCHAR, javaType = String.class),
            @Result(property = "tags", column = "tags", jdbcType = JdbcType.VARCHAR, javaType = String.class),
            @Result(property = "aiEdgeTags", column = "aiEdgeTags", jdbcType = JdbcType.VARCHAR, javaType = String.class),
            @Result(property = "doorbellTags", column = "doorbellTags", jdbcType = JdbcType.VARCHAR, javaType = String.class),
            @Result(property = "deviceCallEventTag", column = "deviceCallEventTag", jdbcType = JdbcType.VARCHAR, javaType = String.class)
    })
    List<VideoTagSummary> queryVideoTagSummaryByUserId(@Param("userId") Integer userId);

    @ShardingReadOnly
    @Select("<script>" +
            "SELECT min(`timestamp`) as minTimestamp, max(`timestamp`) as maxTimestamp, count(1) as libraryCount FROM `video-library-db`.library_status where user_id=#{adminId} AND deleted=0 AND `ttl_timestamp` > UNIX_TIMESTAMP(now()) " +
            " <if test=\"storageClearLibraryInfo!=null\"> " +
            "       <if test=\"storageClearLibraryInfo.serialNumberList!=null\"> AND serial_number IN <foreach collection=\"storageClearLibraryInfo.serialNumberList\" item=\"serialNumber\" index=\"index\" open=\"(\" close=\")\" separator=\",\">#{serialNumber}</foreach> </if>" +
            "       <choose> <when test=\"storageClearLibraryInfo.needClearOldUserLibrary\"> AND (tier_group_id=#{storageClearLibraryInfo.clearTierGroupId} OR tier_group_id IS NULL)</when><otherwise> AND tier_group_id=#{storageClearLibraryInfo.clearTierGroupId}</otherwise></choose>" +
            " </if>" +
            " HAVING count(1)>0" +
            "</script>")
    Map<String, Object> getAdminShardingLibraryInfo(@Param("adminId") Integer adminId, @Param("storageClearLibraryInfo") StorageClearLibraryInfo storageClearLibraryInfo);

    @ShardingReadOnly
    @Select("SELECT user_id, trace_id FROM `video-library-db`.library_status where user_id=#{userId} AND trace_id in (#{traceIdList}) AND deleted=0")
    List<LibraryStatusTb> getUserShardingLibraryByTraceIds(Integer userId, String traceIdList);



    @ShardingReadOnly
    @Select("SELECT count(*) FROM `video-library-db`.library_status where user_id=#{userId} AND deleted=0 and tier_id = #{tierId} and ttl_timestamp between UNIX_TIMESTAMP() and #{expireTime}")
    int getUserShardingLibraryExpireCount(@Param("userId") Integer userId,
                                                           @Param("expireTime") Integer expireTime,
                                                           @Param("tierId") Integer tierId);

    @ShardingReadOnly
    @Select("SELECT min(admin_id) FROM `video-library-db`.library_status where user_id = #{userId} AND trace_id = #{traceId} AND deleted=0")
    Integer selectAdminIdByUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    @ShardingReadOnly
    @Select("<script>" +
            "SELECT * FROM `video-library-db`.library_status " +
            "where user_id = #{userId} " +
            "<if test=\"traceIds != null\"> AND trace_id in <foreach collection=\"traceIds\" item=\"traceId\" index=\"index\" open=\"(\" close=\")\" separator=\",\">#{traceId}</foreach></if>" +
            "AND deleted=0" +
            "</script>")
    List<LibraryStatusTb> selectLibraryStatusByUserIdAndTraceIds(@Param("userId") Integer userId, @Param("traceIds") List<String> traceIds);


    @Select("select trace_id from `video-library-db`.library_status" +
            " where user_id=#{userId} and serial_number=#{sn} and deleted=0" +
            " order by timestamp desc limit 1;")
    String queryLastTraceIdByUserIdAndSn(@Param("userId") Integer userId, @Param("sn") String sn);

    @ShardingReadOnly
    @Select("SELECT admin_id FROM `video-library-db`.library_status where user_id=#{userId} AND deleted=0")
    Set<Integer> selectAdminsByUserId(Integer userId);

    @ShardingReadOnly
    @Select("<script>select * from `video-library-db`.`library_status`" +
            " where `user_id`=#{userId} and `trace_id` in " +
            "   <foreach collection='traceIds' item='item'  open='(' separator=',' close=')' >" +
            "       #{item}" +
            "   </foreach> " +
            "</script>")
    List<LibraryStatusTb> queryLibraryStatusByTraceIds(QuerySliceListVO traceIds);
}
