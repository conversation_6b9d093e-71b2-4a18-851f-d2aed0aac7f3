package com.addx.iotcamera.shardingjdbc.dao.library;

import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.device_msg.SliceCountDO;
import com.addx.iotcamera.shardingjdbc.readonly.ShardingReadOnly;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

import static com.addx.iotcamera.shardingjdbc.dao.library.IShardingVideoSliceInsertOptimazeDAO.SQL_INSERT_VIDEO_SLICE_COLUMNS;
import static com.addx.iotcamera.shardingjdbc.dao.library.IShardingVideoSliceInsertOptimazeDAO.SQL_INSERT_VIDEO_SLICE_VALUES;

@Repository
public interface IShardingVideoSliceDAO {

    String SQL_SELECT_VIDEO_SLICE_COLUMNS = "*";

    @ShardingReadOnly
    @Select("select " + SQL_SELECT_VIDEO_SLICE_COLUMNS + " from `video-library-db`.`video_slice`" +
            " where `user_id` = #{userId} and `trace_id`=#{traceId} order by `order` asc")
    List<VideoSliceDO> querySliceByAdminUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    @ShardingReadOnly
    @Select("select " + SQL_SELECT_VIDEO_SLICE_COLUMNS + " from `video-library-db`.`video_slice`" +
            " where `user_id` = #{userId} and `trace_id`=#{traceId} and `order`=#{order}")
    VideoSliceDO querySliceByAdminUserIdAndTraceIdAndOrder(@Param("userId") Integer userId, @Param("traceId") String traceId, @Param("order") Integer order);

    @ShardingReadOnly
    @Select("<script>" +
            "select " + SQL_SELECT_VIDEO_SLICE_COLUMNS + " from `video-library-db`.`video_slice`" +
            " where `user_id` = #{userId} and `trace_id` in" +
            " <foreach collection='traceIds' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach>" +
            "</script>")
    List<VideoSliceDO> querySliceByAdminUserIdAndTraceIds(@Param("userId") Integer userId, @Param("traceIds") List<String> traceIds);

    @ShardingReadOnly
    @Select("<script>" +
            "select " + SQL_SELECT_VIDEO_SLICE_COLUMNS + " from `video-library-db`.`video_slice`" +
            " where `user_id` = #{userId} and `trace_id` in" +
            " <foreach collection='traceIds' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach>" +
            " <if test='startTimestamp!=null'> AND `s3_event_time` &gt;= #{startTimestamp} </if>" +
            " <if test='endTimestamp!=null'> AND `s3_event_time` &lt;= #{endTimestamp} </if>" +
            "</script>")
    List<VideoSliceDO> querySliceByAdminUserIdAndTraceIdsAndTimestampRange(@Param("userId") Integer userId, @Param("traceIds") List<String> traceIds
            , @Param("startTimestamp") Long startTimestamp, @Param("endTimestamp") Long endTimestamp);

    @Insert("<script>insert into `video-library-db`.`video_slice` (" + SQL_INSERT_VIDEO_SLICE_COLUMNS + ") values " +
            " (" + SQL_INSERT_VIDEO_SLICE_VALUES + ") </script>")
    int insertSlice(@Param("slice") VideoSliceDO videoSliceDO);

    @Insert("<script>insert into `video-library-db`.`video_slice` (" + SQL_INSERT_VIDEO_SLICE_COLUMNS + ") values " +
            " <foreach collection='sliceList' item='slice' open='' separator=',' close=''> " +
            " (" + SQL_INSERT_VIDEO_SLICE_VALUES + ") </foreach>" + 
            " ON DUPLICATE KEY UPDATE `video_url` = `video_url` </script>")
    int insertSliceList(@Param("sliceList") List<VideoSliceDO> sliceList);

    @Delete("DELETE FROM `video-library-db`.`video_slice` WHERE `user_id` = #{userId} and `trace_id` = #{traceId}; ")
    Integer deleteSliceByAdminUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    @Delete("DELETE FROM `video-library-db`.`video_slice` WHERE `user_id` = #{userId} and `trace_id` = #{traceId} and `order` <= #{maxOrder}; ")
    Integer deleteSliceByAdminUserIdAndTraceIdAndMaxOrder(@Param("userId") Integer userId, @Param("traceId") String traceId,@Param("maxOrder") Integer maxOrder);

    @ShardingReadOnly
    @Select("<script>" +
            "SELECT `trace_id` as `traceId`" +
            ",count(*) as `num`" +
            ",max(`order`) as `maxOrder`" +
            ",max(`is_last`) as `hasLast`" +
            ",sum(`period`) as `period`" +
            ",sum(if(not isnull(`end_utc_timestamp_millis`),(`end_utc_timestamp_millis`-`s3_event_time`),0))/1000 as `duration`" +
            ",sum(`file_size`) as `fileSize`" +
            " FROM `video-library-db`.`video_slice`" +
            " WHERE `user_id` = #{userId} and `trace_id` in" +
            " <foreach collection='traceIds' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach>" +
            " group by `trace_id`" +
            "</script>")
    List<SliceCountDO> selectSliceCountByAdminUserIdAndTraceIds(@Param("userId") Integer userId, @Param("traceIds") Collection<String> traceIds);

    @Select("<script>" +
            "select `trace_id`, `file_size`, `peroid`, `resolution` from `video-library-db`.`video_slice` where user_id = #{userId} and trace_id in " +
            "<foreach collection='traceIds' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach> " +
            "</script>")
    List<VideoSliceDO> selectResolutionInfoByTraceIds(@Param("userId") Integer userId, @Param("traceIds") List<String> traceIds);
}
