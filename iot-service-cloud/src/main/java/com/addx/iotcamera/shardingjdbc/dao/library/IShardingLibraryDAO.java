package com.addx.iotcamera.shardingjdbc.dao.library;

import com.addx.iotcamera.bean.db.DeviceLibraryViewDO;
import com.addx.iotcamera.bean.db.LibraryTb;
import com.addx.iotcamera.bean.db.UserLibraryViewDO;
import com.addx.iotcamera.bean.domain.InsertLibraryRequest;
import com.addx.iotcamera.bean.domain.LibraryDonate;
import com.addx.iotcamera.bean.video.ResolutionInfo;
import com.addx.iotcamera.shardingjdbc.readonly.ShardingReadOnly;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface IShardingLibraryDAO {

    String SQL_INSERT_LIBRARY = "" +
            " insert into `video-library-db`.`video_library` (`timestamp`, `serial_number`, `image_url`, `video_url`" +
            " ,`period`, `deleted`, `expired`, `file_size`, `image_only`,`device_name`, `user_id`, `admin_id`, `share_user_ids`, `ttl_timestamp`, `extension`" +
            " <if test ='endTimestamp != null'>,`end_timestamp`</if>" +
            " <if test=\"type!=null\">,`type`</if>" +
            " <if test=\"traceId!=null\">,`trace_id`</if>" +
            " <if test=\"mainTraceId!=null\">,`main_trace_id`</if>" +
            " <if test=\"videoType!=null\">,`video_type`</if>" +
            " <if test=\"serviceName!=null\">,`service_name`</if>" +
            " <if test=\"codec!=null\">,`codec`</if>" +
            " <if test=\"resolution!=null\">,`resolution`</if>" +
            " <if test=\"receivedAllSlice!=null\">,`received_all_slice`</if>" +
            " <if test=\"tierId!=null\">,`tier_id`</if>" +
            " <if test=\"tierGroupId!=null\">,`tier_group_id`</if>" +
            " <if test=\"doorbellTags!=null\">,`doorbell_tags`</if>" +
            " <if test=\"doorbellEventInfo!=null\">,`doorbell_event_info`</if>" +
            " <if test=\"deviceCallEventTag!=null\">,`device_call_event_tag`</if>" +
            " <if test ='tags != null'>,`tags`</if>" +
            " <if test ='aiEdgeTags != null'>,`ai_edge_tags`</if>" +
            " <if test ='eventInfo != null'>,`event_info`</if>" +
            " <if test ='keyshot != null'>,`keyshot`</if>" +
            " <if test ='aiEdgeEventInfo != null'>,`ai_edge_event_info`</if>" +
            " <if test ='summaryDescription != null'>,`summary_description`</if>" +
            " ) VALUES (#{timestamp},#{serialNumber},#{imageUrl},#{videoUrl}," +
            "    #{period}, 0, #{expired},#{fileSize}, #{imageOnly}, #{deviceName}, #{userId}, #{adminId}, #{shareUserIds}, #{ttlTimestamp}, #{extension}" +
            " <if test ='endTimestamp != null'>,#{endTimestamp}</if>" +
            " <if test=\"type!=null\">,#{type}</if>" + // default 0
            " <if test=\"traceId!=null\">,#{traceId}</if>" + // default null,unique
            " <if test=\"mainTraceId!=null\">,#{mainTraceId}</if>" +
            " <if test=\"videoType!=null\">,#{videoType}</if>" +
            " <if test=\"serviceName!=null\">,#{serviceName}</if>" +
            " <if test=\"codec!=null\">,#{codec}</if>" +
            " <if test=\"resolution!=null\">,#{resolution}</if>" +
            " <if test=\"receivedAllSlice!=null\">,#{receivedAllSlice}</if>" + // default null
            " <if test=\"tierId!=null\">,#{tierId}</if>" + // default nul
            " <if test=\"tierGroupId!=null\">,#{tierGroupId}</if>" + // default nul
            " <if test=\"doorbellTags!=null\">,#{doorbellTags}</if>" +
            " <if test=\"doorbellEventInfo!=null\">,#{doorbellEventInfo}</if>" +
            " <if test=\"deviceCallEventTag!=null\">,#{deviceCallEventTag}</if>" +
            " <if test ='tags != null'>,#{tags}</if>" +
            " <if test ='aiEdgeTags != null'>,#{aiEdgeTags}</if>" +
            " <if test ='eventInfo != null'>,#{eventInfo}</if>" +
            " <if test ='keyshot != null'>,#{keyshot}</if>" +
            " <if test ='aiEdgeEventInfo != null'>,#{aiEdgeEventInfo}</if>" +
            " <if test ='summaryDescription != null'>,#{summaryDescription}</if>" +
            " )"; // default null

    // mark in use
    @Insert("<script>" + SQL_INSERT_LIBRARY + "</script>")
    Integer insertLibrary(InsertLibraryRequest library);

    String SELECT_LIBRARY_COLUMNS = " select 0 as `id`,date_format( from_unixtime( ( `timestamp` + 28800 ) ), '%Y%m%d' ) AS `date`," +
            "ifnull(`end_timestamp`,`timestamp`+ceil(`period`)) endTimestamp," +
            "timestamp,serial_number,image_url,video_url,type,received_all_slice,period,file_size,image_only," +
            "admin_id,share_user_ids,device_name,tags,ai_edge_tags,trace_id,main_trace_id,video_type,service_name,codec,resolution,event_info,ai_edge_event_info,doorbell_tags,doorbell_event_info,device_call_event_tag,keyshot,extension, summary_description " +
            " ";

            // mark in use
    @ShardingReadOnly
    @Select({"<script>" + SELECT_LIBRARY_COLUMNS +
            "from `video-library-db`.`video_library` where user_id in" +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            " <if test='traceIds != null and traceIds.size() > 0'> and trace_id in " +
            "<foreach collection='traceIds' item='traceId'  open='(' separator=',' close=')' >" +
            "#{traceId}" +
            " </foreach> " +
            " </if>" +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()) order by `timestamp` desc </script>"})
    List<UserLibraryViewDO> selectLibraryByUserIdsAndTraceIds(@Param("userIds") Set<Integer> userIds, @Param("traceIds") Set<String> traceIds);

    @ShardingReadOnly
    @Select({"<script>" + SELECT_LIBRARY_COLUMNS +
            "from `video-library-db`.`video_library` where user_id in" +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            SQL_WHIRE_MAIN_TRACE_ID_IN +
            SQL_WHIRE_VIDEO_TYPE_IN +
            " <if test=\"serviceName!=null\"> AND `service_name` = #{serviceName}</if>" +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()) order by `timestamp` desc </script>"})
    List<UserLibraryViewDO> selectLibraryByUserIdsAndMainTraceIds(@Param("userIds") Set<Integer> userIds
            , @Param("mainTraceIds") Set<String> mainTraceIds
            , @Param("videoTypes") List<Integer> videoTypes
            , @Param("serviceName") String serviceName);

    // mark in use
    @ShardingReadOnly
    @Select("<script>" +
            "select 0 as `id`,trace_id,main_trace_id,video_type,service_name,codec,resolution,image_url,period,device_name,admin_id,video_url,type,received_all_slice,timestamp" +
            ",ifnull(`end_timestamp`,ceil(`timestamp`+`period`)) as `end_timestamp`" +
            ",doorbell_tags,doorbell_event_info,device_call_event_tag,keyshot,extension,summary_description " +
            "from `video-library-db`.`video_library` where user_id in" +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            " <if test='traceIds != null and traceIds.size() > 0'> and trace_id in " +
            "<foreach collection='traceIds' item='traceId'  open='(' separator=',' close=')' >" +
            "#{traceId}" +
            " </foreach> " +
            " </if>" +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()) " +
            " order by `timestamp` desc" +
            " </script>")
    List<LibraryTb> selectLibraryInfoByUserIdsAndBatchTraceId(@Param("userIds") Set<Integer> userIds, @Param("traceIds") Set<String> traceIds);

    @ShardingReadOnly
    @Select("<script>" +
            "select 0 as `id`,trace_id,main_trace_id,video_type,service_name,codec,resolution,image_url,period,device_name,admin_id,video_url,type,received_all_slice,timestamp" +
            ",ifnull(`end_timestamp`,ceil(`timestamp`+`period`)) as `end_timestamp`" +
            ",doorbell_tags,doorbell_event_info,device_call_event_tag,keyshot,extension,summary_description " +
            "from `video-library-db`.`video_library` where user_id in" +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            SQL_WHIRE_MAIN_TRACE_ID_IN +
            SQL_WHIRE_VIDEO_TYPE_IN +
            " <if test=\"serviceName!=null\"> AND `service_name` = #{serviceName}</if>" +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()) " +
            " order by `timestamp` desc" +
            " </script>")
    List<LibraryTb> selectLibraryInfoByUserIdsAndBatchMainTraceId(@Param("userIds") Set<Integer> userIds
            , @Param("mainTraceIds") Set<String> mainTraceIds
            , @Param("videoTypes") List<Integer> videoTypes
            , @Param("serviceName") String serviceName
    );

    String SQL_WHIRE_MAIN_TRACE_ID_IN = "" +
            " <if test='mainTraceIds!=null and mainTraceIds.size()>0'> " +
            "   AND `main_trace_id` in " +
            "   <foreach collection='mainTraceIds' item='item'  open='(' separator=',' close=')' >" +
            "       #{item}" +
            "   </foreach> " +
            " </if>";

    String SQL_WHIRE_VIDEO_TYPE_IN = "" +
            " <if test='videoTypes!=null and videoTypes.size()>0'> " +
            "   AND `video_type` in " +
            "   <foreach collection='videoTypes' item='item'  open='(' separator=',' close=')' >" +
            "       #{item}" +
            "   </foreach> " +
            " </if>";

    // mark in use
    @ShardingReadOnly
    @Select("SELECT 0 as `id`, " +
            "   date_format( from_unixtime(`timestamp`), '%Y%m%d' ) AS date, " +
            "    `timestamp`, " +
            "    `end_timestamp`, " +
            "    `serial_number`, " +
            "    `image_url`, " +
            "    `video_url`, " +
            "    `type`, " +
            "    `trace_id`, " +
            "    `main_trace_id`, " +
            "    `video_type`, " +
            "    `service_name`, " +
            "    `codec`, " +
            "    `received_all_slice`, " +
            "    `period`, " +
            "    `file_size`, " +
            "    `image_only`, " +
            "    `device_name`, " +
            "    0 as location_id, " +
            "    ' ' as location_name, " +
            "    `admin_id`, " +
            "    `share_user_ids`, " +
            "    `tags`, `ai_edge_tags`, event_info, ai_edge_event_info," +
            "    `doorbell_tags` ,doorbell_event_info, " +
            "    device_call_event_tag, keyshot,extension, summary_description " +
            "FROM `video-library-db`.`video_library` " +
            "WHERE `user_id` = #{userId} and `trace_id` = #{traceId} and deleted = 0 and expired = 0 AND ttl_timestamp > UNIX_TIMESTAMP(now()); ")
    UserLibraryViewDO selectSingleLibraryByUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    // mark in use
    @ShardingReadOnly
    @Select("<script>" +
            "SELECT 0 as `id`, " +
            "   date_format( from_unixtime(`timestamp`), '%Y%m%d' ) AS date, " +
            "    `timestamp`, " +
            "    `end_timestamp`, " +
            "    `serial_number`, " +
            "    `image_url`, " +
            "    `video_url`, " +
            "    `type`, " +
            "    `trace_id`, " +
            "    `main_trace_id`, " +
            "    `video_type`, " +
            "    `service_name`, " +
            "    `codec`, " +
            "    `received_all_slice`, " +
            "    `period`, " +
            "    `file_size`, " +
            "    `image_only`, " +
            "    `device_name`, " +
            "    0 as location_id, " +
            "    ' ' as location_name, " +
            "    `admin_id`, " +
            "    `share_user_ids`, " +
            "    `tags`, `ai_edge_tags`, event_info, ai_edge_event_info," +
            "    `doorbell_tags` ,doorbell_event_info, " +
            "    device_call_event_tag,keyshot,extension,summary_description " +
            "FROM `video-library-db`.`video_library` " +
            "WHERE `user_id` = #{userId} " +
            "and `trace_id` = #{traceId} and deleted = 0 and expired = 0 " +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()); " +
            "</script>")
    UserLibraryViewDO selectLibraryViewByAdminIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    // mark in use
    @ShardingReadOnly
    @Select("<script>" +
            "SELECT " +
            "    0 AS `id`, " +
            "    date_format(from_unixtime(`timestamp`), '%Y%m%d' ) AS `date`, " +
            "    `video_library`.`timestamp`, " +
            "    `video_library`.`end_timestamp`, " +
            "    `video_library`.`serial_number`, " +
            "    `video_library`.`image_url`, " +
            "    `video_library`.`video_url`, " +
            "    `video_library`.`type`, " +
            "    `video_library`.`trace_id`, " +
            "    `video_library`.`main_trace_id`, " +
            "    `video_library`.`video_type`, " +
            "    `video_library`.`service_name`, " +
            "    `video_library`.`codec`, " +
            "    `video_library`.`received_all_slice`, " +
            "    `video_library`.`period`, " +
            "    `video_library`.`file_size`, " +
            "    `video_library`.`image_only`, " +
            "    `video_library`.`device_name`, " +
            "    `video_library`.`tags`, " +
            "    `video_library`.`event_info`, " +
            "    `video_library`.`keyshot`, " +
            "    `video_library`.`extension`, " +
            "    `video_library`.`summary_description`, " +
            "    0 AS `location_id`, " +
            "    ' ' AS location_name  " +
            "FROM `video-library-db`.`video_library` " +
            "WHERE `user_id` in " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            " </foreach> " +
            " and `trace_id` = #{traceId} and `deleted` = 0 and expired=0 AND ttl_timestamp > UNIX_TIMESTAMP(now()); " +
            "</script>")
    DeviceLibraryViewDO selectLibraryViewByUserIdsAndTraceId(@Param("userIds") Set<Integer> userIds, @Param("traceId") String traceId);

    // mark in use
    @Delete("DELETE FROM `video-library-db`.`video_library` " +
            "WHERE `user_id` = #{userId} and `trace_id` = #{traceId}; ")
    Integer deleteLibraryByUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    // mark in use
    @Delete("<script>" +
            "DELETE FROM `video-library-db`.`video_library` " +
            "WHERE `user_id` = #{userId} and `trace_id` IN <foreach collection='traceIdList' item='traceId'  open='(' separator=',' close=')'> #{traceId} </foreach>" +
            "</script>")
    Integer batchDeleteLibraryByUserIdsAndTraceIdList(@Param("userId") Integer userId, @Param("traceIdList") List<String> traceIdList);

    // mark in use
    @Update("<script>" +
            "UPDATE `video-library-db`.`video_library` SET `event_info` = #{libraryTb.eventInfo}" +
            "<if test = 'libraryTb.tags != null'>,tags=#{libraryTb.tags}</if>" +
            "<if test = 'libraryTb.aiEdgeTags != null'>,ai_edge_tags=#{libraryTb.aiEdgeTags}</if>" +
            "<if test = 'libraryTb.imageUrl != null'>,image_url=#{libraryTb.imageUrl} </if>" +
            "<if test = 'libraryTb.summaryDescription != null'>,summary_description=#{libraryTb.summaryDescription} </if>" +
            "WHERE `user_id` = #{libraryTb.userId} and `trace_id` = #{libraryTb.traceId} " +
            " <if test=\"startTimestamp!=null\"> AND `timestamp` &gt;= #{startTimestamp} </if>" +
            "</script>")
    Integer updateLibraryInfoByUserIdAndTraceId(@Param("libraryTb") LibraryTb libraryTb, @Param("startTimestamp") Integer startTimestamp);

    // mark in use
    @ShardingReadOnly
    @Select({"<script>select 0 as libraryId,video_url from `video-library-db`.video_library where user_id in " +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            " and trace_id in " +
            " <foreach collection='traceIds' item='traceId'  open='(' separator=',' close=')' >" +
            "#{traceId} " +
            " </foreach> AND ttl_timestamp > UNIX_TIMESTAMP(now())</script>"})
    List<LibraryDonate> selectLibraryByBatchUserIdsAndTraceId(@Param("userIds") Set<Integer> userIds, @Param("traceIds") List<String> traceIds);

    // mark in use
    @ShardingReadOnly
    @Select("<script>select " +
            "timestamp, serial_number, user_id, trace_id, update_time, image_url, video_url, period, deleted, expired, file_size, " +
            "image_only, tags, admin_id, share_user_ids, device_name, event_info, package_event_info, type, received_all_slice, " +
            "received_complete, complete_time, s3_address_received_timestamp, total_start_recording_timestamp, total_end_recording_timestamp, " +
            "package_image_url, doorbell_tags, doorbell_event_info, device_call_event_tag, ttl_timestamp, tier_id, tier_group_id, ai_edge_tags, ai_edge_event_info,keyshot, extension, summary_description" +
            " from `video-library-db`.`video_library` where `user_id` = #{userId} and `trace_id`=#{traceId} AND `expired`=0 and `deleted`=0 AND ttl_timestamp > UNIX_TIMESTAMP(now())" +
            "</script>")
    InsertLibraryRequest selectLibraryByUserIdAndTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    // mark in use
    // period 和 fileSize 不进行累加
    @Update("<script>" +
            " update `video-library-db`.`video_library` set" +
            " `trace_id` = trace_id" +
            " <if test=\"libraryModify.receivedAllSlice!=null\">,`received_all_slice`=#{libraryModify.receivedAllSlice}</if>" +
            " <if test=\"libraryModify.imageUrl!=null\">,`image_url`=ifnull(image_url,#{libraryModify.imageUrl})</if>" +
            " <if test=\"libraryModify.period!=null\">,`period`=#{libraryModify.period}</if>" +
            " <if test=\"libraryModify.fileSize!=null\">,`file_size`=#{libraryModify.fileSize}</if>" +
            " <if test=\"libraryModify.doorbellTags!=null\">,`doorbell_tags`=#{libraryModify.doorbellTags}</if>" +
            " <if test=\"libraryModify.doorbellEventInfo!=null\">,`doorbell_event_info`=#{libraryModify.doorbellEventInfo}</if>" +
            " <if test=\"libraryModify.extension!=null\">,`extension`=#{libraryModify.extension}</if>" +
            " <if test=\"libraryModify.deviceCallEventTag!=null\">,`device_call_event_tag`=#{libraryModify.deviceCallEventTag}</if>" +
            " <if test=\"libraryModify.summaryDescription!=null\">,`summary_description`=#{libraryModify.summaryDescription}</if>" +
            " where (`user_id` = #{libraryModify.userId} and `trace_id`=#{libraryModify.traceId} and `expired`=0 and `deleted`=0)" +
            "</script>")
    Integer updateLibraryByUserIdAndTraceId(@Param("libraryModify") LibraryTb libraryModify);


    // mark in use
    // imageUrl直接更新
    @Update("<script>" +
            " update `video-library-db`.`video_library` set" +
            " `trace_id` = trace_id" +
            " <if test=\"libraryModify.receivedAllSlice!=null\">,`received_all_slice`=#{libraryModify.receivedAllSlice}</if>" +
            " <if test=\"libraryModify.imageUrl!=null\">,`image_url`=#{libraryModify.imageUrl}</if>" +
            " <if test=\"libraryModify.period!=null\">,`period`=#{libraryModify.period}</if>" +
            " <if test=\"libraryModify.fileSize!=null\">,`file_size`=#{libraryModify.fileSize}</if>" +
            " <if test=\"libraryModify.doorbellTags!=null\">,`doorbell_tags`=#{libraryModify.doorbellTags}</if>" +
            " <if test=\"libraryModify.doorbellEventInfo!=null\">,`doorbell_event_info`=#{libraryModify.doorbellEventInfo}</if>" +
            " <if test=\"libraryModify.deviceCallEventTag!=null\">,`device_call_event_tag`=#{libraryModify.deviceCallEventTag}</if>" +
            " <if test=\"libraryModify.extension!=null\">,`extension`=#{libraryModify.extension}</if>" +
            " <if test ='libraryModify.tags != null'>,`tags`=#{libraryModify.tags}</if>" +
            " <if test ='libraryModify.aiEdgeTags != null'>,`ai_edge_tags`=#{libraryModify.aiEdgeTags}</if>" +
            " <if test ='libraryModify.eventInfo != null'>,`event_info`=#{libraryModify.eventInfo}</if>" +
            " <if test ='libraryModify.aiEdgeEventInfo != null'>,`ai_edge_event_info`=#{libraryModify.aiEdgeEventInfo}</if>" +
            " <if test ='libraryModify.keyshot != null'>,`keyshot`=#{libraryModify.keyshot}</if>" +
            " <if test ='libraryModify.timestamp != null'>,`timestamp`=#{libraryModify.timestamp}</if>" +
            " <if test ='libraryModify.endTimestamp != null'>,`end_timestamp`=#{libraryModify.endTimestamp}</if>" +
            " <if test ='libraryModify.summaryDescription != null'>,`summary_description`=#{libraryModify.summaryDescription}</if>" +
            " where (`user_id` = #{libraryModify.userId} and `trace_id`=#{libraryModify.traceId} and `expired`=0 and `deleted`=0)" +
            "</script>")
    Integer updateLibraryByUserIdAndTraceIdV2(@Param("libraryModify") LibraryTb libraryModify);

    // mark in use
    @ShardingReadOnly
    @Select({"<script>" +
            "select 0 as `id`, trace_id,admin_id,share_user_ids,file_size " +
            "from `video-library-db`.`video_library` where user_id in " +
            "<foreach collection='userIds' item='userId'  open='(' separator=',' close=')' >" +
            "#{userId}" +
            " </foreach> " +
            " and trace_id in " +
            "<foreach collection='traceIds' item='traceId'  open='(' separator=',' close=')' >" +
            "#{traceId}" +
            " </foreach> " +
            " AND ttl_timestamp > UNIX_TIMESTAMP(now()) " +
            " order by `timestamp` desc" +
            "</script>"})
    List<UserLibraryViewDO> selectLibraryStorageClearInfoList(@Param("userIds") Set<Integer> userIds, @Param("traceIds") Set<String> traceIds);

    // mark in use
    @ShardingReadOnly
    @Select("<script>" +
            "SELECT 0 as `id`, `timestamp`, `admin_id`, `share_user_ids`, ifnull(file_size, 0) as file_size  FROM `video-library-db`.video_library WHERE user_id = #{userId} and trace_id=#{traceId} AND deleted=0 AND ttl_timestamp > UNIX_TIMESTAMP(now()) " +
            "</script>")
    LibraryTb getAdminShardingLibraryByTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    @ShardingReadOnly
    @Select("<script>" +
            "SELECT `trace_id`" +
            " FROM `video-library-db`.`video_library`" +
            " WHERE `user_id` = #{userId} and `serial_number` = #{sn} and `trace_id` in" +
            " <foreach collection='traceIds' item='traceId' open='(' separator=',' close=')'>#{traceId}</foreach>" +
            " AND `service_name`=#{serviceName}" +
            "</script>")
    List<String> selectTraceIdsByAdminIdAndSnAndTraceIdsAndServiceName(@Param("userId") Integer userId, @Param("sn") String sn
            , @Param("traceIds") List<String> traceIds, @Param("serviceName") String serviceName);

    @ShardingReadOnly
    @Select("<script>" +
            "SELECT `trace_id`" +
            " FROM `video-library-db`.`video_library`" +
            " WHERE `user_id` = #{userId} and `serial_number` = #{sn} and `timestamp` &lt; #{maxTimestamp}" +
            " AND `service_name`=#{serviceName}" +
            "</script>")
    List<String> selectTraceIdsByAdminIdAndSnAndMaxTimestampAndServiceName(@Param("userId") Integer userId, @Param("sn") String sn
            , @Param("maxTimestamp") Long maxTimestamp, @Param("serviceName") String serviceName);

    @ShardingReadOnly
    @Select(" select ifnull(max(`timestamp`),0)*1000 from `video_library`  where `user_id`=#{adminId} and `serial_number` = #{sn} and `codec` is null;")
    Long selectMaxTimeByNotCodec(@Param("adminId") Integer adminId, @Param("sn") String sn);

    @Update("<script>" +
            " update `video_library` set `image_url`=#{imageUrl} " +
            " where `user_id` = #{userId} and `trace_id`=#{traceId}" +
            " <if test='!overrideImageUrl'>and not (`image_url` is null or `image_url`='')</if>" +
            "</script>")
    int updateImageUrlByTraceId(
            @Param("userId") Integer userId,
            @Param("traceId") String traceId,
            @Param("imageUrl") String imageUrl,
            @Param("overrideImageUrl") boolean overrideImageUrl
    );

    // 多分辨率支持：查询某个traceId下所有可用的分辨率
    @ShardingReadOnly
    @Select("<script>" +
            "select distinct `resolution`, `video_type` from `video_library`" +
            " where `user_id` = #{userId} and (" +
            "   (`trace_id`=#{traceId} and `video_type` in (1,3)) or " +  // 主视频
            "   (`main_trace_id`=#{traceId} and `video_type`=3)" +    // 多分辨率附属视频
            " ) and `resolution` is not null" +
            "</script>")
    List<ResolutionInfo> queryAvailableResolutionsByTraceId(@Param("userId") Integer userId, @Param("traceId") String traceId);

    @ShardingReadOnly
    @Select("select `trace_id` from `video_library` where (" +
            " (user_id = #{userId} and `trace_id` = #{traceId})" +
            " or (user_id = #{userId} and `main_trace_id` = #{traceId} and video_type = 3) )" +
            " and `resolution` = #{resolution} limit 1")
    String selectTraceIdByMainTraceIdAndResolution(@Param("userId") Integer userId, @Param("traceId") String traceId, @Param("resolution") String resolution);

    @ShardingReadOnly
    @Select("select codec from `video_library` where user_id = #{userId} and `trace_id` = #{traceId} limit 1")
    String selectCodec(@Param("userId") Integer userId, @Param("traceId") String traceId);
}
