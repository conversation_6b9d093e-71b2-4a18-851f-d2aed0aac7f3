
package com.addx.iotcamera.kiss.service.impl;

import com.addx.iotcamera.bean.db.device.DeviceKissFlagDO;
import com.addx.iotcamera.bean.device.DeviceCoturnDO;
import com.addx.iotcamera.bean.device_msg.IotMsgMethod;
import com.addx.iotcamera.bean.device_msg.KissDeviceNode;
import com.addx.iotcamera.bean.device_msg.KissDeviceStatus;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.tenant.TenantSetting;
import com.addx.iotcamera.config.LiveConfig;
import com.addx.iotcamera.config.device.KeepAliveParams;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.enums.EProtocol;
import com.addx.iotcamera.kiss.CoturnFinder;
import com.addx.iotcamera.kiss.DeviceWebrtcParamService;
import com.addx.iotcamera.kiss.KissFinder;
import com.addx.iotcamera.kiss.KissNodeType;
import com.addx.iotcamera.kiss.bean.*;
import com.addx.iotcamera.kiss.enums.EHeartbeatReplyMode;
import com.addx.iotcamera.kiss.helper.DeviceSleepHelper;
import com.addx.iotcamera.kiss.node.CoturnNode;
import com.addx.iotcamera.kiss.node.KissNode;
import com.addx.iotcamera.kiss.service.IKissAllocationService;
import com.addx.iotcamera.kiss.service.IKissService;
import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.device.DeviceServerAllocService;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import com.addx.iotcamera.service.device_msg.KissDeviceNodeManager;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.LiveLogUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.crypto.Mac;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KissServiceImpl implements IKissService {

    @Value("${spring.kafka.topics.device-wakeup:}")
    private String deviceWakeupTopic;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UserRoleService userRoleService;

    @Autowired
    @Lazy
    private DeviceServerAllocService deviceServerAllocService;

    @Resource
    private IKissAllocationService kissAllocationService;

    @Resource
    private KissFinder kissFinder;

    @Resource
    private CoturnFinder coturnFinder;

    @Autowired
    private MqSender mqSender;

    @Autowired
    private DeviceWebrtcParamService deviceWebrtcParamService;

    @Autowired
    private RedisService redisService;
    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;
    @Autowired
    @Lazy
    private KissWsService kissWsService;
    @Autowired
    @Lazy
    private KissDeviceNodeManager kissDeviceNodeManager;
    @Autowired
    @Lazy
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Autowired
    @Lazy
    private LiveConfig liveConfig;
    @Resource
    @Lazy
    private TenantSettingService tenantSettingService;

    private static final String REDIS_KEY_DEVICE_SIGNAL_SERVER = "device_signal_server:{sn}";

    @Override
    public KissParams getKissParams(String serialNumber, EProtocol protocol, EHeartbeatReplyMode replyMode) {
        // 先生成heartbeat和wakeup参数
        DeviceSleepHelper.SleepParams params = DeviceSleepHelper.genParams(serialNumber, replyMode);
        String countryNo = userRoleService.getCountryNoBySerialNumber(serialNumber);
        final ChooseNodeCondition condition = new ChooseNodeCondition().setCountryNo(countryNo)
                .setPurpose(deviceServerAllocService.queryBySn(serialNumber).getKissPurpose());
        List<KissNode> nodeList = kissFinder.chooseHeartbeatNodesBySNAndCountryNo(serialNumber, condition, 2);

        List<String> ip4List = new ArrayList<>();
        KissParams kissParams = new KissParams()
                .setWakeup(params.getWakeup())
                .setHeartbeat(params.getHeartbeat())
                .setAddrList(new ArrayList<>());

        nodeList.forEach(node -> {
            // 记录内网ip
            ip4List.add(node.getIntranetIp4());

            // 给设备的地址用公网ip
            kissParams.getAddrList().add(new KissParams.KissAddr()
                    .setIp(node.getExtranetIp4())
                    .setPort(protocol == EProtocol.TCP ? node.getTcpPort() : node.getUdpPort()));
        });
        if (ip4List.isEmpty()) {
            com.addx.iotcamera.util.LogUtil.error(log, "无法选到可用kiss节点 {} {}", serialNumber, protocol);
            return null;
        }

        // 记录到数据库
        KissServer currentKissServer = kissAllocationService.getKissServer(serialNumber);
        KissServer kissServer = new KissServer()
                .setSecret(params.getSecret())
                .setRealIp4(currentKissServer != null ? currentKissServer.getRealIp4() : null)
                .setUpdateTime(Instant.now().toEpochMilli());
        log.debug("看看是啥 {}", kissServer);
        kissAllocationService.setKissServer(serialNumber, kissServer);
        return kissParams;
    }

    /**
     * 为什么要让app端从缓存中取kiss节点?
     * 1、kiss节点数量变了，按规则分配的kiss节点可能会变。
     * 2、设备端是长链接，未异常断开的情况下，即使kiss节点数量变了，也不会去重新分配kiss地址。
     * 3、设备端每次连上kiss，kiss都会上报DEVICE_NODE_UPDATE到iot-service，所以给app分配这个设备上报的地址。
     */
    public KissNode getKissNodeFromSignalServerCache(String sn) {
        final KissDeviceNode kissDeviceNode = kissDeviceNodeManager.get(sn);
        if (KissDeviceStatus.offline.equals(kissDeviceNode.getDeviceStatus())) {
            if (liveConfig.getAppLive4gIgnoreWsOffline() && !deviceMsgSrcManager.isWsKeepAlive(sn)) {
                log.debug("signalServerCache appLive4gIgnoreWsOffline! sn={}", sn);
            } else {
                return null;
            }
        }
        final KissNode node = Optional.ofNullable(kissFinder.getNodeByKissIp(kissDeviceNode.getKissIp())).orElse(null);
        log.info("signalServerCache get end! sn={},kissDeviceNode={},kissNode={}", sn, kissDeviceNode, JSON.toJSONString(node));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(kissDeviceNode.getKissIp()) && node == null) {
            // 有可能出现kiss实例正常但从zk中短暂丢失的情况。app会被分配到不通kiss实例导致直播失败。处理方案：给设备端发close消息
            kissWsService.closeGroup(sn);
        }
        return node;
    }

    private void saveSignalServerCache(String serialNumber, SignalServer signalServer) {
        final String redisKey = REDIS_KEY_DEVICE_SIGNAL_SERVER.replace("{sn}", serialNumber);
        final String redisValue = JSON.toJSONString(signalServer);
        redisService.set(redisKey, redisValue);
        log.info("signalServerCache save end! redisKey={},signalServer={}", redisKey, redisValue);
    }

    @Override
    public SignalServer getSignalServerAddr(String serialNumber, boolean isCamera) {
        if (!isCamera) {
            final KissNode kissNode = getKissNodeFromSignalServerCache(serialNumber);
            if (kissNode != null) return SignalServer.from(kissNode);
        }
        final ChooseNodeCondition condition = getChooseNodeConditionBySn(serialNumber);
        KissNode node = kissFinder.chooseWebsocketNodeBNAndCountryNo(serialNumber, condition);
        return SignalServer.from(node);
    }

    // 根据sn获取kiss节点查询条件
    public ChooseNodeCondition getChooseNodeConditionBySn(String sn) {
        ChooseNodeCondition condition = new ChooseNodeCondition();
        try {
            condition.setPurpose(deviceServerAllocService.queryBySn(sn).getKissPurpose());
            final Result<User> adminResult = userRoleService.getAdminUserBySn(sn);
            if (Result.successFlag.equals(adminResult.getResult())) {
                final User admin = adminResult.getData();
                final TenantSetting tenantSetting = tenantSettingService.getTenantSettingByTenantId(admin.getTenantId());
                condition.setCountryNo(admin.getCountryNo());
                condition.setNodeType(KissNodeType.nameOf(tenantSetting.getLiveNodeType()));
            }
        } catch (Throwable e) {
            log.error("getChooseNodeConditionBySn error! sn={},condition={}", sn, JSON.toJSONString(condition), e);
        }
        return condition;
    }

    @Override
    public void wakeupDevice(String serialNumber, String traceId) {
        String wakeup = DeviceSleepHelper.genWakeup(serialNumber);

        final DeviceKissFlagDO kissFlagDO = deviceMsgSrcManager.queryKissFlag(serialNumber);
        final Boolean isWsKeepAlive = kissFlagDO.getDeviceIsWsKeepAlive();
        if (kissFlagDO.getDeviceSupportKissReplaceMqtt()) { // wakeupDevice
            if (!isWsKeepAlive) { // 新版固件不支持ws保活的，调用kissWs唤醒接口，走tcp唤醒
                final KissServer kissServer = kissAllocationService.getKissServer(serialNumber);
                final KissNode node = Optional.ofNullable(kissServer).map(it -> it.getRealIp4()).map(kissFinder::getNodeByKissIp).orElse(null);
                log.info("wakeupDevice getKissServer end! isWsKeepAlive={},sn={},kissServer={},kissNode={}", isWsKeepAlive, serialNumber, JSON.toJSONString(kissServer), JSON.toJSONString(node));
                if (node != null) {
                    final KissApiResult wakeupResult = kissWsService.wakeupByKissIp(node.getIntranetIp4(), serialNumber, traceId, wakeup, isWsKeepAlive);
                    if (wakeupResult.success()) return;
                }
                wakeupDevice(serialNumber, traceId, wakeup, node, isWsKeepAlive); // 唤醒不成功，走异步唤醒
                return;
            }
            final KissApiResult wakeupResult = kissWsService.wakeup(serialNumber, traceId, wakeup, isWsKeepAlive);
            if (wakeupResult.success()) return;
        }
        final CloudDeviceSupport cloudDeviceSupport = deviceInfoService.getDeviceSupport(serialNumber);
        final KissNode node;
        if (cloudDeviceSupport.getSupportUnlimitedWebsocket()) { // 设备支持websocket长链接，从新缓存里取
            node = getKissNodeFromSignalServerCache(serialNumber);
            wakeupDevice(serialNumber, traceId, wakeup, node, true);
        } else {    // 老版设备，从老缓存里取
            final KissServer kissServer = kissAllocationService.getKissServer(serialNumber);
            node = Optional.ofNullable(kissServer).map(it -> it.getRealIp4()).map(kissFinder::getNodeByKissIp).orElse(null);
            log.info("wakeupDevice getKissServer end! isWsKeepAlive={},sn={},kissServer={},kissNode={}", isWsKeepAlive, serialNumber, JSON.toJSONString(kissServer), JSON.toJSONString(node));
            wakeupDevice(serialNumber, traceId, wakeup, node, false);
        }
    }

    public void wakeupDevice(String serialNumber, String traceId, String wakeup, KissNode node, Boolean isWsKeepAlive) {
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceWakeupCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "COUNT").inc());

        if (node != null) {
            LiveLogUtil.log(traceId, "call kiss node wakeup api");
            String uri = node.getInnerApiAddr() + "/wakeup";
            MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
            paramMap.add("sn", serialNumber);
            paramMap.add("wakeup", wakeup);
            paramMap.add("traceId", traceId);
            paramMap.add("isWsKeepAlive", isWsKeepAlive);
            post(uri, paramMap);
        } else {
            LiveLogUtil.log(traceId, "设备未连接到kiss服务 {} {} 尝试异步唤醒", serialNumber, traceId);
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getAsyncWakeupDeviceCounterOptional()
                    .ifPresent(it -> it.labels(PrometheusMetricsUtil.getHostName()).inc()));

            if (!StringUtils.isEmpty(deviceWakeupTopic)) {
                Map<String, Object> wakeupMap = new HashMap<>();
                wakeupMap.put("sn", serialNumber);
                wakeupMap.put("wakeup", wakeup);
                wakeupMap.put("traceId", traceId);
                wakeupMap.put("isWsKeepAlive", isWsKeepAlive);
                mqSender.send(deviceWakeupTopic, serialNumber, JsonUtil.toJson(wakeupMap));
            }
        }
    }

    @Override
    public KissDeviceStatus getDeviceStatus(String serialNumber) {
        final KissNode node = getKissNodeFromSignalServerCache(serialNumber);
        if (node == null) return null;
        String uri = node.getInnerApiAddr() + "/getDeviceStatus";
        final JSONObject reqBody = new JSONObject().fluentPut("sn", serialNumber);
        final KissApiResult result = post(uri, reqBody.toJSONString());
        return result != null && result.getCode() == 0 ? KissDeviceStatus.nameOf((String) result.getData()) : null;
    }

    @Override
    public boolean updateKeepAliveParams(String serialNumber, KeepAliveParams keepAliveParams) {
        if (deviceMsgSrcManager.isWsReplaceMqtt(serialNumber)) { // updateKeepAliveParams
            if (kissWsService.updateKeepAliveParams(serialNumber, keepAliveParams)) return true;
        }
        final KissNode node = getKissNodeFromSignalServerCache(serialNumber);
        if (node == null) return false;
        final String uri = node.getInnerApiAddr() + "/sendMessageToDevice";
        final JSONObject payload = (JSONObject) JSON.toJSON(keepAliveParams);
        payload.fluentPut("method", IotMsgMethod.KEEP_ALIVE_PARAMS.name()).fluentPut("timestamp", System.currentTimeMillis());
        final JSONObject reqBody = new JSONObject().fluentPut("sn", serialNumber).fluentPut("payload", payload);
        final KissApiResult result = post(uri, reqBody.toJSONString());
        return result != null && result.getCode() == 0;
    }

    @Override
    public boolean closeOldGroup(String sn) {
        if (deviceMsgSrcManager.isWsReplaceMqtt(sn)) { // closeOldGroup
            if (kissWsService.closeOldGroup(sn)) return true;
        }
        final KissNode node = getKissNodeFromSignalServerCache(sn);
        if (node == null) return false;
        final String uri = node.getInnerApiAddr() + "/closeOldGroup";
        final JSONObject reqBody = new JSONObject().fluentPut("sn", sn).fluentPut("requestId", MDC.get(MDCKeys.REQUEST_ID));
        final KissApiResult result = post(uri, reqBody.toJSONString());
        return result != null && result.getCode() == 0;
    }

    @Nullable
    @Override
    public Long getLastKissAckTime(String serialNumber) {
        return kissAllocationService.getLastAckUpdateTimeBySerialNumber(serialNumber);
    }

    @Override
    public List<IceServer> chooseTurnServer(String serialNumber, String clientId) {
        String countryNo = userRoleService.getCountryNoBySerialNumber(serialNumber);
        final ChooseNodeCondition condition = new ChooseNodeCondition().setCountryNo(countryNo)
                .setPurpose(deviceServerAllocService.queryBySn(serialNumber).getCoturnPurpose());
        UserPermit userPermit = generateCoturnUserPermit(clientId, "hxrMYtvC5Q1efUQn");
        List<CoturnNode> coturnNodeList = coturnFinder.chooseCoturnNodesBySNAndCountryNo(clientId, condition);
        coturnNodeList = modifyByCoturnConfig(serialNumber, coturnNodeList);
        return coturnNodeList.stream().map(n -> new IceServer()
                .setUsername(userPermit.getUsername())
                .setCredential(userPermit.getCredential())
                .setUrl("turn:" + n.getDomain() + ":5349")
                .setIpAddress(n.getExtranetIp4()))
                .collect(Collectors.toList());
    }

    /**
     * 根据coturn配置修改coturn节点列表
     */
    public List<CoturnNode> modifyByCoturnConfig(String serialNumber, List<CoturnNode> inputCoturnList) {
        try {
            final List<CoturnNode> coturnNodeList = new LinkedList<>();
            boolean isModify = false;
            final List<DeviceCoturnDO> deviceCoturnList = deviceWebrtcParamService.queryEnabledDeviceCoturnBySn(serialNumber);
            if (CollectionUtils.isEmpty(deviceCoturnList)) return inputCoturnList;
            final Map<Integer, List<DeviceCoturnDO>> map = deviceCoturnList.stream().collect(Collectors.groupingBy(it -> it.getType()));
            final List<DeviceCoturnDO> whiteCoturnList = map.get(DeviceCoturnDO.TYPE_WHITE_LIST);
            if (CollectionUtils.isNotEmpty(whiteCoturnList)) {  // 如果指定了白名单，则直接用白名单中的coturn服务器
                for (DeviceCoturnDO it : whiteCoturnList) {
                    CoturnNode coturnNode = new CoturnNode();
                    coturnNode.setDomain(it.getDomain());
                    coturnNode.setExtranetIp4(it.getExtranetIp4());
                    coturnNodeList.add(coturnNode);
                }
                isModify = true;
            } else {
                coturnNodeList.addAll(inputCoturnList);
            }
            final List<DeviceCoturnDO> blackCoturnList = map.get(DeviceCoturnDO.TYPE_BLACK_LIST);
            if (CollectionUtils.isNotEmpty(blackCoturnList)) { // 过滤掉黑名单中的coturn服务器
                Set<String> blackDomainSet = blackCoturnList.stream().map(it -> it.getDomain()).collect(Collectors.toSet());
                Set<String> blackIpSet = blackCoturnList.stream().map(it -> it.getExtranetIp4()).collect(Collectors.toSet());
                isModify |= coturnNodeList.removeIf(it -> blackDomainSet.contains(it.getDomain()) || blackIpSet.contains(it.getExtranetIp4()));
            }
            if (isModify) {
                log.info("modifyByCoturnConfig success! 修改前={},修改后={}", JSON.toJSONString(inputCoturnList), JSON.toJSONString(coturnNodeList));
            }
            return coturnNodeList;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "modifyByCoturnConfig error! sn={}", serialNumber, e);
            return inputCoturnList;
        }
    }

    /**
     * post
     * @param uri
     * @param paramMap
     * @return
     */
    private KissApiResult post(String uri, MultiValueMap<String, Object> paramMap) {
        Instant begin = Instant.now();
        try {
            // 设置header
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type","application/x-www-form-urlencoded");
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(paramMap, headers);
            ResponseEntity<JSONObject> r = restTemplate.postForEntity(uri, entity, JSONObject.class);
            if (r.getStatusCode() != HttpStatus.OK) {
                com.addx.iotcamera.util.LogUtil.error(log, "调用kiss接口 http异常 {} {} {}", uri, paramMap, r.toString());
                return null;
            }

            KissApiResult result = JSONObject.parseObject(
                    Objects.requireNonNull(r.getBody()).toJSONString(),
                    KissApiResult.class);
            if (!result.success()) {
                com.addx.iotcamera.util.LogUtil.error(log, "调用kiss接口 业务异常 {} {} {}", uri, paramMap, result);
                return null;
            }

            PrometheusMetricsUtil.setMetricNotExceptionally(()-> PrometheusMetricsUtil.getDeviceWakeupCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "SYNC").inc());
            return result;
        } finally {
            Instant end = Instant.now();
            log.info("调用kiss接口 {} 耗时 {} ms", uri, end.toEpochMilli() - begin.toEpochMilli());
        }
    }

    private KissApiResult post(String uri, String reqBodyJson) {
        final long beginTime = System.currentTimeMillis();
        try {
            // 设置header
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(reqBodyJson, headers);
            final ResponseEntity<String> r = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
            if (r.getStatusCode() != HttpStatus.OK) {
                com.addx.iotcamera.util.LogUtil.error(log, "调用kiss接口 postJson http异常 {} {} {} 耗时 {} ms", uri, reqBodyJson, r, System.currentTimeMillis() - beginTime);
                return null;
            }
            KissApiResult result = JSONObject.parseObject(r.getBody(), KissApiResult.class);
            if (!result.success()) {
                com.addx.iotcamera.util.LogUtil.error(log, "调用kiss接口 postJson 业务异常 {} {} {} 耗时 {} ms", uri, reqBodyJson, r, System.currentTimeMillis() - beginTime);
                return result;
            }
            PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getDeviceWakeupCounterOptional().get().labels(PrometheusMetricsUtil.getHostName(), "SYNC").inc());
            log.info("调用kiss接口 postJson 成功 {} {} {} 耗时 {} ms", uri, uri, reqBodyJson, r, System.currentTimeMillis() - beginTime);
            return result;
        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "调用kiss接口 postJson java异常 {} {} 耗时 {} ms", uri, reqBodyJson, System.currentTimeMillis() - beginTime, e);
            return null;
        }
    }

    /**
     * 获取coturn服务器的用户许可
     * @param clientId
     * @return
     */
    private UserPermit generateCoturnUserPermit(String clientId, String turnSecret) {
        long durationSecs = 60 * 60 * 48; // 初始授权有效期设置为: 48小时
        long expiredTimestamp = Instant.now().toEpochMilli() / 1000 + durationSecs; // 过期时间

        // 用户名
        String username = expiredTimestamp + ":" + clientId;
        // 采用hmac加密
        Mac mac = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_1, turnSecret.getBytes());
        mac.update(username.getBytes());
        // 许可证
        String credential = Base64.getEncoder().encodeToString(mac.doFinal());
        return new UserPermit().setUsername(username).setCredential(credential);
    }


    @Override
    public String sendReportEventResponse(String serialNumber, String reponseStr, String kissIpPort) { 
        String uri = null;
        String kissIp = kissIpPort.contains(":") ? kissIpPort.substring(0, kissIpPort.indexOf(":")) : kissIpPort;
        KissNode node = kissFinder.getNodeByKissIp(kissIp);
        if(node != null) {
            uri = node.getInnerApiAddr();
        } else {
            uri = String.join("", "http://", kissIpPort);
        }
        uri = String.join("", uri, "/sendReportEventResponse?sn=", serialNumber);
        log.info("sendReportEventResponse uri {}", uri);
        HttpUtils.http2RequestWithPool("POST", uri, null, reponseStr);
        return null;
    }
}
