package com.addx.iotcamera.aop;

import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.openapi.OpenApiResult;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.constants.PrefixConstants;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.constants.ServerConstants;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.GlobalLogService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.TokenService;
import com.addx.iotcamera.service.openapi.OpenApiAuthService;
import com.addx.iotcamera.service.prompt.ExceptionPromptService;
import com.addx.iotcamera.service.proto_msg.ProtoMsgService;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.RequestUtil;
import com.addx.iotcamera.util.TextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import lombok.Setter;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.addx.iotcamera.helper.JwtHelper.HEADER_STRING;

@Component
@Order(1)
public class JwtFilter implements Filter {
    public static final String IP_BLOCK_LIST = "IP_BLOCK_LIST";
    private JwtHelper jwtHelper;
    private TokenService tokenService;
    private OpenApiAuthService openApiAuthService;

    @Autowired
    private GlobalLogService globalLogService;
    @Autowired
    @Lazy
    private ProtoMsgService protoMsgService;

    @Setter
    @Autowired
    @Lazy
    private RedisService redisService;

    @Autowired
    private ExceptionPromptService exceptionPromptService;

    @Autowired
    public JwtFilter(JwtHelper jwtHelper, TokenService tokenService, OpenApiAuthService openApiAuthService) {
        this.jwtHelper = jwtHelper;
        this.tokenService = tokenService;
        this.openApiAuthService = openApiAuthService;
    }

    private List<String> urlWhiteList = handleUrlWhiteList(ImmutableList.of(
//            "/account/**", // account下有4个接口需要登录，不能用通配符
            "/account/login",
            "/account/**/login",
            "/account/retryLoginCode",
            "/account/quickLoginCode",
            "/account/register",
            "/account/resetpswd",
            "/account/registconfirm",
            "/account/resetconfirm",
            "/account/checkconfirm",
            "/account/getipinfo",
            "/account/verify/exist",
            "/account/sendConfirm",
            "/lambda/**",
//            "/factory/**", // 没有找到对应的controller
            "/management/**",
            "/actuator/**",
            "/system/**",
//            "/wakeup/**", // 没有找到对应的controller
            "/xxl/**",
            "/ops/**",
            "/payNotify/**",
            "/report/live/wowza/**",
            "/zendesk/auth/**",
            "/video/download/m3u8/**",
            "/video/uploadBegin",
            "/video/handleS3EventNotify",
            "/pay/wxpay/notify",
            "/pay/alipay/notify",
            "/open-sdk/queryNode",
            "/webhooks/**",
            "/release/model/**",
            "/workTemp/**",
            "/deviceMsg/httpToken", // 获取设备httpToken，靠签名校验
            "/backend/queryAbility", // 获取后端能力
            "/safeRTC/bx/**", // iot-cloud给safeRTC分配kiss地址的接口
            "/internal/**",
            "/internal-rpc/**",
            //获取abtest的特性对应规则库，无法设置cookie
            "/api/features/**",
            //swagger配置
            "/**/swagger-ui/**",
            "/**/swagger-ui.html",
            "/**/swagger-resources/**",
            "/**/v3/api-docs",
            "/**/v2/api-docs",
            "/doc.html",
            "/webjars/**"
    ));

    private List<String> openApiUrl = handleUrlWhiteList(ImmutableList.of(
            // 新功能初始化接口
            "/work/**",
            // paas接口
            "/open-api/**",
            // 内部api接口
            "/postSale/**",
            "/inner-api/**",
            "/log/analysis/pushConfig"
    ));

    private List<String> verifyCodeUrl = handleUrlWhiteList(ImmutableList.of(
            //发送验证码
            "/user/sendCode",
            //验证验证码
            "/user/authCode"
    ));

    private static final org.springframework.util.PathMatcher pathMatcher = new AntPathMatcher();
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtFilter.class);

    @Override
    public void init(FilterConfig filterConfig) {
    }

    public void InitResponse(HttpServletResponse httpResponse) {
        httpResponse.setCharacterEncoding("UTF-8");
        httpResponse.setContentType("application/json; charset=utf-8");
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpResponse.setHeader("Access-Control-Allow-Methods", "*");
        httpResponse.setHeader("Access-Control-Allow-Headers", "*");
        httpResponse.setHeader("Access-Control-Max-Age", "86400");
        httpResponse.setHeader("serverId", ServerConstants.SERVER_ID);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        InitResponse(httpResponse);
        String spath = httpRequest.getServletPath();
        Result res;
        String requestId = IDUtil.uuid();
        MDC.put(MDCKeys.REQUEST_ID, requestId);
        request.setAttribute(RequestAttributeKeys.REQUEST_ID, requestId); // Global日志可可以从httpRequest中取requestId

        if (checkRemoteIpInIpBlockList(httpRequest, response)) return;

        /*
        for (String url : urlWhiteList) {
            if (pathMatcher.match(url, spath)) {
                chain.doFilter(request, response);
                return;
            }
        }
        */
        // 发送验证码和验证验证码接口走单独鉴权
        if (verifyCodeUrl.stream().anyMatch(url -> pathMatcher.match(url, spath))) {
            Map<String, Object> tokenClaims = jwtHelper.validateTokenAndGetClaims(httpRequest);
            LOGGER.info("doFilter-token:{}", tokenClaims);
            res = validateCodeVerityClaims(tokenClaims, request, httpRequest.getHeader(HEADER_STRING));
            if (res != null) {
                String rspStr = PhosUtils.objToJsonString(res);
                PrintWriter out = response.getWriter();
                out.print(rspStr);
                out.flush();
                MDC.clear(); // http处理完成，清除
            } else {
                chain.doFilter(request, response);
                if (((HttpServletResponse) response).getStatus() == 404) {
                    LOGGER.info("verifyCodeUrl req path not exist: {}", ((HttpServletRequest) request).getRequestURI());
                }
                MDC.clear(); // http处理完成，清除
            }
            return;
        }

        // 如果是openApi的接口，走单独的鉴权逻辑
        if (openApiUrl.stream().anyMatch(url -> pathMatcher.match(url, spath))) {
            String authRequestUrl = httpRequest.getRequestURL().toString();
            String lambdaProxyHost = httpRequest.getHeader("lambda-proxy-host");
            if (StringUtils.isNotEmpty(lambdaProxyHost)) {
                authRequestUrl = lambdaProxyHost;
            }
            Result<OpenApiAccount> result = openApiAuthService.validateSign(authRequestUrl, httpRequest.getQueryString());
            LOGGER.info("validateSign 验证完成 url={}?{},result={}", authRequestUrl, httpRequest.getQueryString(), JSON.toJSONString(result));
            if (result.getResult() != Result.successFlag) {
                OpenApiAuthService.writeResponse(httpResponse, OpenApiResult.from(result));
                MDC.clear(); // http处理完成，清除
            } else {
                OpenApiAccount account = result.getData();
                MDC.put(MDCKeys.ACCOUNT_ID, account.getAccountId());
                httpRequest.setAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT, account);
//                paasAccessLogService.recordPaasAccess(httpRequest, account);
                chain.doFilter(request, response);
                MDC.clear(); // http处理完成，清除
            }
            return;
        }

        Map<String, Object> tokenClaims = jwtHelper.validateTokenAndGetClaims(httpRequest);

        res = validateClaims(tokenClaims, request, httpRequest.getHeader(HEADER_STRING));

        // 先解析token，再判断白名单。确保白名单中的接口也能拿到userId
        if (urlWhiteList.stream().anyMatch(url -> pathMatcher.match(url, spath))) {
            chain.doFilter(request, response);
            MDC.clear(); // http处理完成，清除
            return;
        }
        if (res != null) {
            LOGGER.info("token is invalid! uri={},token={},tokenClaims={},result={}", httpRequest.getRequestURL(), httpRequest.getHeader(HEADER_STRING), JSON.toJSONString(tokenClaims), JSON.toJSONString(res));
            // protobuf编码的 /deviceMsg/** 接口，如果鉴权失败了需要返回proto编码的响应
            if (protoMsgService.handleTokenInValid(httpRequest, httpResponse, res)) {
                MDC.clear(); // http处理完成，清除
                return;
            }
            String rspStr = PhosUtils.objToJsonString(res);
            PrintWriter out = response.getWriter();
            out.print(rspStr);
            out.flush();
            MDC.clear(); // http处理完成，清除
        } else {
            chain.doFilter(request, response);
            if (((HttpServletResponse) response).getStatus() == 404) {
                LOGGER.info("req path not exist: {}", ((HttpServletRequest) request).getRequestURI());
            }
            MDC.clear(); // http处理完成，清除
        }
    }

    private Result validateCodeVerityClaims(Map<String, Object> tokenClaims, ServletRequest request, String token) {
        LOGGER.info("validateCodeVerityClaims tokenClaims={}", JSON.toJSONString(tokenClaims));
        Result res;
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        if (tokenClaims == null) {
            res = Result.Error(-1025, "TOKEN_MISSING");
            return res;
        }

        // App用户的请求
        if (tokenClaims.containsKey(PrefixConstants.TEMP_TOKEN_PREFIX + TokenService.TOKEN_FIELD_USER_ID)) {
            LOGGER.info("validateCodeVerityClaims tokenClaims={}", JSON.toJSONString(tokenClaims));
            String userId = String.valueOf(tokenClaims.get(PrefixConstants.TEMP_TOKEN_PREFIX + TokenService.TOKEN_FIELD_USER_ID));
            String storedUser = String.valueOf(tokenClaims.get(PrefixConstants.TEMP_TOKEN_PREFIX + TokenService.TOKEN_FIELD_SEED));
            String stored = tokenService.queryUserSeed(PrefixConstants.TEMP_TOKEN_PREFIX + userId);
            if (stored == null) {
                LOGGER.info("用户token超时userId {} token {}", userId, token);
                res = Result.Error(-1023, "LOG_IN_EXPIRED");
            } else if (!stored.equals(storedUser)) {
                LOGGER.info("用户在别处登陆userId {} token {}", userId, token);
                res = Result.Error(-1024, "ACCOUNT_GET_KICKED");
            } else {
                // 需要userId的地方直接从attribute中取，不要在controller中重复解析
                request.setAttribute(RequestAttributeKeys.USER_ID, Integer.valueOf(userId));
                request.setAttribute(RequestAttributeKeys.SEED, storedUser);
                MDC.put(MDCKeys.USER_ID, userId);
                if (tokenClaims.containsKey(TokenService.TOKEN_FIELD_THIRD_USER_ID)) {
                    String thirdUserId = (String) tokenClaims.get(TokenService.TOKEN_FIELD_THIRD_USER_ID);
                    String accountId = (String) tokenClaims.get(TokenService.TOKEN_FIELD_ACCOUNT_ID);
                    request.setAttribute(RequestAttributeKeys.THIRD_USER_ID, thirdUserId);
                    request.setAttribute(RequestAttributeKeys.ACCOUNT_ID, accountId);
                    MDC.put(MDCKeys.THIRD_USER_ID, thirdUserId);
                    MDC.put(MDCKeys.ACCOUNT_ID, accountId);
                } else {
                    tokenService.setUserExpired(userId);
                }
                return null;
            }
            return res;
        }
        return Result.Error(-1027, "INVALID_TOKEN");
    }

    public boolean checkRemoteIpInIpBlockList(HttpServletRequest httpRequest, ServletResponse response) {
        try {
            Optional<Set<String>> ipsOptional = redisService.getFromLocalCache(IP_BLOCK_LIST).map(it -> TextUtil.splitToNotBlankSet(it, ','));
            if (ipsOptional.isPresent() && ipsOptional.get().contains(HttpUtils.getRemoteAddr(httpRequest))) {
                final String url = httpRequest.getRequestURL().toString();
                final Map<String, String> headers = RequestUtil.getHeaderMap(httpRequest);
                // 不能返回app端不认识的返回码。返回token失效让app回到登录页
                final Result<?> result = Result.Error(-1023, "Account suspended. Login restricted");
                final String resultStr = JSON.toJSONString(result);
                LOGGER.warn("check remote ip in IP_BLOCK_LIST! url={}, headers={}, result={}", url, JSON.toJSONString(headers), resultStr);
                PrintWriter out = response.getWriter();
                out.print(resultStr);
                out.flush();
                return true;
            }
        } catch (Throwable e) {
            LOGGER.error("check remote ip in IP_BLOCK_LIST error!", e);
        }
        return false;
    }

    @Override
    public void destroy() {

    }

    private Result validateClaims(Map<String, Object> tokenClaims, ServletRequest request, String token) {
        Result res;
        if (tokenClaims == null) {
            res = Result.Error(-1025, "TOKEN_MISSING");
            return res;
        }
        // App用户的请求
        if (tokenClaims.containsKey(TokenService.TOKEN_FIELD_USER_ID)) {
            String userId = String.valueOf(tokenClaims.get(TokenService.TOKEN_FIELD_USER_ID));
            String storedUser = String.valueOf(tokenClaims.get(TokenService.TOKEN_FIELD_SEED));
            String stored = tokenService.queryUserSeed(userId);
            if (stored == null) {
                LOGGER.warn("用户token超时userId {} token {}", userId, token);
                res = Result.Error(-1023, "LOG_IN_EXPIRED");
            } else if (!stored.equals(storedUser)) {
                LOGGER.warn("用户在别处登陆userId {} token {}", userId, token);
                res = Result.Error(-1024, "ACCOUNT_GET_KICKED");
            } else {
                // 需要userId的地方直接从attribute中取，不要在controller中重复解析
                request.setAttribute(RequestAttributeKeys.USER_ID, Integer.valueOf(userId));
                request.setAttribute(RequestAttributeKeys.SEED, storedUser);
                MDC.put(MDCKeys.USER_ID, userId);
                if (tokenClaims.containsKey(TokenService.TOKEN_FIELD_THIRD_USER_ID)) {
                    String thirdUserId = (String) tokenClaims.get(TokenService.TOKEN_FIELD_THIRD_USER_ID);
                    String accountId = (String) tokenClaims.get(TokenService.TOKEN_FIELD_ACCOUNT_ID);
                    request.setAttribute(RequestAttributeKeys.THIRD_USER_ID, thirdUserId);
                    request.setAttribute(RequestAttributeKeys.ACCOUNT_ID, accountId);
                    MDC.put(MDCKeys.THIRD_USER_ID, thirdUserId);
                    MDC.put(MDCKeys.ACCOUNT_ID, accountId);
                } else {
                    tokenService.setUserExpired(userId);
                }
                return null;
            }
            return res;
        }

        // 设备的请求
        Object serialNumber = tokenClaims.get(TokenService.TOKEN_FIELD_SERIAL_NUMBER);
        if (serialNumber != null) {
            request.setAttribute(RequestAttributeKeys.SERIAL_NUMBER, serialNumber.toString());
            // 设备http请求后端的日志中，把设备型号和版本号都打印出来，用于统计
            globalLogService.setGlobalFields(serialNumber.toString());
            LOGGER.debug("设备请求:serialNumber:{},url:{}", serialNumber, ((HttpServletRequest) request).getRequestURL());
            return null;
        }

        return Result.Error(-1027, "INVALID_TOKEN");
    }

    // 处理一下路径匹配模式，末尾带'/'和不带'/'的视为同一url
    public static List<String> handleUrlWhiteList(List<String> list) {
        Set<String> urls = new LinkedHashSet<>();
        for (String url : list) {
            urls.add(url);
            if (url.endsWith("*")) {
            } else if (url.endsWith("/")) {
                urls.add(StringUtils.substring(url, 0, -1));
            } else {
                urls.add(url + "/");
            }
        }
        return ImmutableList.sortedCopyOf(urls);
    }

    public String getLanguageFromBody(HttpServletRequest httpRequest) {
        LOGGER.info("getLanguageFromBody:{}",httpRequest.getContentType());
        try {
            if ("POST".equalsIgnoreCase(httpRequest.getMethod()) &&
                    httpRequest.getContentType().contains("application/json")){
                // 读取请求体内容，注意：这种方法会消费输入流，后续无法再次读取
                String requestBody = new String(httpRequest.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
                // 使用FastJSON解析JSON
                JSONObject json = JSON.parseObject(requestBody);
                // 获取顶层的language参数
                String language = json.getString("language");
                return language;
            }
        } catch (Exception e) {
            // 如果不是JSON格式，尝试获取表单参数
            String language = httpRequest.getParameter("language");
            return language;
        }
        return  "";
    }


    /**
     * 在过滤器中读取body中的json参数，防止单向流读取后失效使用ContentCachingRequestWrapper进行包装,同时可以设置一些属性值，方便后续获取
     * 后续过滤器链可以直接使用该返回值HttpServletRequest 进行传递
     * @param httpRequest
     * @return
     */
    public HttpServletRequest packAndSetHttpServletRequest(HttpServletRequest httpRequest) {
        LOGGER.info("packAndSetHttpServletRequest url:{}", httpRequest.getRequestURL());

        // 1. 始终先包装请求
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(httpRequest);

        try {
            // 2. 触发输入流读取以填充缓存
            try {
                wrappedRequest.getInputStream().readAllBytes();
            } catch (IOException e) {
                // 忽略空流异常
            }

            // 3. 检查是否是 JSON 请求
            if (wrappedRequest.getContentType() != null &&
                    wrappedRequest.getContentType().contains("application/json")) {

                // 4. 从缓存中获取内容
                byte[] content = wrappedRequest.getContentAsByteArray();
                if (content.length == 0) {
                    throw new RuntimeException("Body is empty");
                }

                String requestBody = new String(content, StandardCharsets.UTF_8);
                JSONObject json = JSON.parseObject(requestBody);
                String language = json.getString("language");

                if (language != null) {
                    wrappedRequest.setAttribute("language", language);
                    return wrappedRequest;
                }
            }
        } catch (Exception e) {
            // 降级到请求参数
        }

        // 5. 从请求参数获取
        String language = wrappedRequest.getParameter("language");
        wrappedRequest.setAttribute("language", language);
        return wrappedRequest;
    }
}
