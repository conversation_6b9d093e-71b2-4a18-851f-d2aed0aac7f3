package com.addx.iotcamera.statemachine;

import com.addx.iotcamera.mq.MqSender;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class StatemachineEventSender {

    private static volatile StatemachineEventSender sender = null;

    @Resource
    private MqSender mqSender;

    @Value("${spring.kafka.topics.statemachine}")
    private String statemachineTopic;

    @PostConstruct
    void init() {
        sender = this;
    }

    private void sendStatemachineEvent(String sn, String event, String data) {
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        map.put("data", data);
        map.put("eventTimestamp", System.currentTimeMillis());
        mqSender.send(statemachineTopic, sn, map);
    }

    public static void send(String sn, String event, String data) {
        if (data == null) {
            data = "";
        }
        try {
            if(event.startsWith("mqtt") && !DeviceMsgSrcManager.isUseMqttConnection(sn)) {
                return;
            }
            sender.sendStatemachineEvent(sn, event, data);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, e.getMessage(), e);
        }
    }
}
