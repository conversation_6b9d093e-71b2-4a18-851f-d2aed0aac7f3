package com.addx.iotcamera.controller.device.info;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.device.DeviceBindInfoDO;
import com.addx.iotcamera.bean.app.device.DeviceLocationRequest;
import com.addx.iotcamera.bean.device.DeviceMessageNotification;
import com.addx.iotcamera.bean.device.FoundDeviceInfoQuery;
import com.addx.iotcamera.bean.device.FoundDeviceInfoResult;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.LocationDO;
import com.addx.iotcamera.bean.domain.UserToken;
import com.addx.iotcamera.bean.domain.device.DeviceApInfoDO;
import com.addx.iotcamera.bean.domain.deviceplatform.PlatformLinkedDeviceMapDo;
import com.addx.iotcamera.bean.domain.deviceplatform.PlatformSupportedDeviceMapDo;
import com.addx.iotcamera.bean.response.device.DeviceManufactureInfoResponse;
import com.addx.iotcamera.bean.warranty.BindInfoRequest;
import com.addx.iotcamera.bean.warranty.WarrantyOrderRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.dao.factory.PurchaseSourceDAO;
import com.addx.iotcamera.enums.DevicePlatformEventEnums;
import com.addx.iotcamera.helper.DeviceApInfoHelper;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.publishers.deviceplatform.DevicePlatformEventPublisher;
import com.addx.iotcamera.publishers.vernemq.requests.RetrieveLocalVideosRequest;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.MessageNotificationSettingsService;
import com.addx.iotcamera.service.deviceplatform.DevicePlatformService;
import com.addx.iotcamera.service.factory.DeviceBindInfoService;
import com.addx.iotcamera.service.factory.FactoryDataQueryService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.util.FlatSupportJsonUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@Slf4j
@RestController
@RequestMapping("/device")
@Api(tags = "设备相关API")
public class DeviceInfoController {
    private static Logger LOGGER = LoggerFactory.getLogger(DeviceInfoController.class);

    @Autowired
    private JwtHelper jwtHelper;
    @Autowired
    private UserService userService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private DeviceAuthService deviceAuthService;
    @Resource
    private LocationInfoService locationInfoService;

    @Lazy
    @Autowired
    private OpenApiConfigService openApiConfigService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private DeviceApInfoHelper deviceApInfoHelper;

    @Autowired
    private DeviceSdCardService deviceSdCardService;

    @Autowired
    private RotationPointService rotationPointService;

    @Autowired
    private StreamService streamService;

    @Autowired
    private DeviceManualService deviceManualService;

    @Autowired
    private MessageNotificationSettingsService messageNotificationSettingsService;

    @Autowired
    private VipService vipService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private VideoSearchService videoSearchService;

    @Autowired(required = false)
    private DevicePlatformEventPublisher devicePlatformEventPublisher;

    @Autowired(required = false)
    private DevicePlatformService devicePlatformService;


    @Resource
    private FactoryDataQueryService factoryDataQueryService;
    @Autowired
    private DeviceBindInfoService deviceBindInfoService;
    @Autowired
    private PurchaseSourceDAO purchaseSourceDAO;


    /**
     * 更新设备名字
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updatedevicename", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateDeviceName(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                                   HttpServletRequest httpServletRequest) {
        DeviceDO device = DeviceDO.builder()
                .serialNumber(request.getSerialNumber())
                .deviceName(request.getDeviceName())
                .build();

        LOGGER.info(String.format("Updating device name to %s for user %d", device.getDeviceName(), userId));

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, device.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        if (devicePlatformEventPublisher != null) {
            Map<String, Object> deviceEventMap = new HashMap<>();
            deviceEventMap.put("userId", userId);
            deviceEventMap.put("serialNumber", device.getSerialNumber());
            deviceEventMap.put("newDeviceName", device.getDeviceName());
            devicePlatformEventPublisher.sendEventAsync(DevicePlatformEventEnums.DEVICE_UPDATE_NAME, deviceEventMap);
        }

        Integer result = deviceService.updateDeviceInfo(device);
        videoSearchService.clearSearchOptionCache(device.getSerialNumber());
        return Result.SqlOperationResult(result);
    }

    /**
     * 更新设备名字,绑定Location
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/binddevicelocation", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result binddevicelocation(@LoginUserToken UserToken userToken,
                                     @Valid @RequestBody DeviceLocationRequest request,
                                     BindingResult bindingResult,
                                     HttpServletRequest httpServletRequest) {
        if (bindingResult.hasErrors()) {
            return Result.Error(INVALID_PARAMS, bindingResult.getFieldError().getDefaultMessage());
        }

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userToken.getUserId(), request.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }
        deviceInfoService.bindDeviceLocation(request, userToken.getUserId());
        return Result.Success();
    }

    /**
     * 更新设备地址
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updatedevicelocation", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateDeviceLocation(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                                       HttpServletRequest httpServletRequest) {
        DeviceDO device = new DeviceDO(request.getDeviceName(), request.getSerialNumber());
        device.setLocationId(request.getLocationId());


        LOGGER.info(String.format("Updating device location to %d for user %d", device.getLocationId(), userId));

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, device.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }
        LocationDO locationDO = locationInfoService.selectSingleLocation(request.getLocationId());
        device.setHomeId(locationDO == null ? 0L : locationDO.getHomeId());
        return Result.SqlOperationResult(deviceService.updateDeviceInfo(device));
    }

    /**
     * 列出用户所有设备
     *
     * @param userId
     * @param request
     * @return
     */
    @ApiOperation(value = "设备列表")
    @LogRequestAndResponse
    @PostMapping(value = "/listuserdevices", produces = MediaType.APPLICATION_JSON_VALUE)
    Result listUserDevices(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                           HttpServletRequest httpServletRequest) {

        List<DeviceDO> deviceDOS = deviceInfoService.listDevicesByUserId(userId);
        List<JSONObject> result = FlatSupportJsonUtil.flatDeviceDOList(deviceDOS);
        return Result.ListResult(result);
    }

    @ApiOperation(value = "设备列表")
    @LogRequestAndResponse
    @PostMapping(value = "/listuserdevices/v3", produces = MediaType.APPLICATION_JSON_VALUE)
    Result listUserDevicesV3(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody ListUserDevicesRequest request,
                             HttpServletRequest httpServletRequest) {
        List<DeviceDO> deviceDOS = deviceInfoService.listDevicesByUserId(userId);

        UserDevicesVO res = new UserDevicesVO();
        deviceInfoService.fillListUserDeviceV3Params(deviceDOS, userId, request);

        List<JSONObject> result = FlatSupportJsonUtil.flatDeviceDOList(deviceDOS);

        res.setCxDeviceList(result);

        return new Result(res);
    }

    /**
     * 获取设备信息（用户）
     *
     * @param userId
     * @param request
     * @return
     */
    @Operation(summary = "设备详情")
    @LogRequestAndResponse
    @PostMapping(value = "/selectsingledevice", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponses(value = {
            @ApiResponse(responseCode = "0", description = "查询成功"),
            @ApiResponse(responseCode = "-2132", description = "没有权限"),
    })
    Result<JSONObject> selectSingleDevice(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                              HttpServletRequest httpServletRequest) {
        DeviceDO queryDeviceDo = JSON.parseObject(JSON.toJSONString(request), DeviceDO.class);

        queryDeviceDo.setUserId(userId);
        // 判断用户与设备是否有关联
        Integer commonUserCheck = deviceAuthService.checkDeviceCommonAccess(userId, queryDeviceDo.getSerialNumber());
        if (SUCCESS.getCode() != commonUserCheck) {
            return ResultCollection.getResult(commonUserCheck);
        }

        DeviceDO deviceDO = deviceInfoService.getSingleDevice(queryDeviceDo,userId);

        return new Result<>(FlatSupportJsonUtil.flatDeviceDO(deviceDO));
    }

    /**
     * 获取已经link到其他平台的设备
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/listplatformlinkeddevices", produces = MediaType.APPLICATION_JSON_VALUE)
    @SentinelResource("listplatformlinkeddevices")
    public Result listPlatformLinkedDevices(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request) {
        PlatformLinkedDeviceMapDo platformLinkedDeviceMapDo = null;
        if (devicePlatformService != null) {
            platformLinkedDeviceMapDo = devicePlatformService.queryPlatformLinkedDeviceMapDo((request != null && request.getApp() != null) ? request.getApp().getTenantId() : null, userId);
        } else {
            platformLinkedDeviceMapDo = PlatformLinkedDeviceMapDo.builder()
                    .platformLinkedDevicesMap(Collections.emptyMap())
                    .firmwareNotSupportDeviceInfoMap(Collections.emptyMap())
                    .platformDisplayControl(Collections.emptyList())
                    .build();
        }
        return new Result(platformLinkedDeviceMapDo);
    }

    /**
     * 获取支持alexa平台的设备(no auth check)
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/listplatformsupporteddevices", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result listPlatformSupportedDevices(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        PlatformSupportedDeviceMapDo platformSupportedDeviceMapDo = null;
        if(devicePlatformService!=null) {
            platformSupportedDeviceMapDo = devicePlatformService.queryPlatformSupportedDeviceMapDo(userId);
        }else {
            platformSupportedDeviceMapDo = PlatformSupportedDeviceMapDo.builder()
                    .platformSupportedDevicesMap(Collections.emptyMap())
                    .build();
        }
        return new Result(platformSupportedDeviceMapDo);
    }

    /**
     * 更新人型检测开关
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updatepersondetect", produces = MediaType.APPLICATION_JSON_VALUE)
    Result updatePersonDetect(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                              HttpServletRequest httpServletRequest) {
        DeviceDO device = DeviceDO.builder()
                .serialNumber(request.getSerialNumber())
                .personDetect(request.getPersonDetect())
                .build();

        LOGGER.info("Updating device person detect switch to {} for user {}", device.getPersonDetect(), userId);

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, device.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return Result.SqlOperationResult(deviceService.updateDeviceInfo(device));
    }

    /**
     * 更新设备包裹检测开关
     *
     * @param request
     * @return
     */
//    @LogRequestAndResponse
//    @PostMapping(value = "/updatePackagePush", produces = MediaType.APPLICATION_JSON_VALUE)
//    Result updatePackagePush(@LoginUserToken UserToken userToken, @RequestBody DeviceRequest request,
//                             HttpServletRequest httpServletRequest) {
//        Integer userId = userToken.getUserId();
//        // 只有设备实际拥有者拥有权限（被分享用户无权限）
//        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, request.getSerialNumber());
//        if (SUCCESS.getCode() != adminCheck) {
//            throw new BaseException(adminCheck, ResultCollection.getResult(adminCheck).getMsg());
//        }
//        //原包裹功能未上过prod
//        deviceInfoService.updateDevicePackagePush(userId, request.getSerialNumber(), request.getPackagePush());
//        return Result.Success();
//    }

    /**
     * 更改录像预置分辨率
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updaterecresolution", produces = MediaType.APPLICATION_JSON_VALUE)
    Result updateRecResolution(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                               HttpServletRequest httpServletRequest) {

        LOGGER.info("Updating rec resolution of {} to {} for user {}", request.getSerialNumber(), request.getRecResolution(), userId);

        // 判非空
        if (StringUtils.isEmpty(request.getSerialNumber()) || StringUtils.isEmpty(request.getRecResolution())) {
            return ResultCollection.INVALID_PARAMS.getResult();
        }

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, request.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }
        DeviceDO deviceDO = DeviceDO.builder()
                .serialNumber(request.getSerialNumber())
                .recResolution(request.getRecResolution())
                .build();
        Result result = Result.SqlOperationResult(deviceService.updateDeviceInfo(deviceDO));
        // 用户修改分辨率，重新发送config retained message
        openApiConfigService.publishDeviceConfigAsync(deviceDO.getSerialNumber(), null, userId);
        return result;
    }

    /**
     * 设置用户推送过滤
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/updateMessageNotification/v1")
    @LogRequestAndResponse
    public Result updateMessageNotificationSettingsV1(@LoginUserToken UserToken userToken,
                                                      @Validated @RequestBody MessageNotificationSettingRequest request,
                                                      HttpServletRequest httpServletRequest) {
        return notificationService.updateMessageNotificationSettingsV1(userToken.getUserId(), request);
    }

    /**
     * 设置用户推送过滤  （统一归一化的接口）
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/updateMessageNotification/v2")
    @LogRequestAndResponse
    public Result updateMessageNotificationSettingsV2(@LoginUserToken UserToken userToken,
                                                      @Validated @RequestBody MessageNotificationSettingRequest request,
                                                      HttpServletRequest httpServletRequest) {
        return notificationService.updateMessageNotificationSettingsV2(userToken.getUserId(), request);
    }

    /**
     * 获取用户推送过滤设置V1
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/queryMessageNotification/v1")
    @LogRequestAndResponse
    public Result queryMessageNotificationV1(@LoginUserToken UserToken userToken,
                                             @RequestBody MessageNotificationSettingRequest request,
                                             HttpServletRequest httpServletRequest) {

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userToken.getUserId(), request.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }
        List<DeviceMessageNotification> list = messageNotificationSettingsService.queryMessageNotificationSettingsListV1(request.getSerialNumber(), userToken.getUserId());
        if (Optional.ofNullable(request.getFilterByAiAnalyze()).orElse(true)) { // 老版app没有这个字段，默认过滤。新版app传flse，不过滤。
            list.removeIf(it -> !it.getEnable());
        }
        //兼容老的app，bird不显示
        if(!BooleanUtils.isTrue(request.getIncludeBird())
        || !AppConstants.TENANTID_KIWIBIT.equals(userService.getUserTenantId(userToken.getUserId()))) {
            list.removeIf(it -> it.getName().equalsIgnoreCase("bird"));
        }
        return Result.ListResult(list);
    }

    /**
     * 获取用户推送过滤设置V2  （批量接口）
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/queryMessageNotification/v2")
    @LogRequestAndResponse
    public Result queryMessageNotificationV2(@LoginUserToken UserToken userToken,
                                             @RequestBody MessageNotificationSettingRequest request,
                                             HttpServletRequest httpServletRequest) {
        List<JSONObject> result = new ArrayList<>();
        for(String serialNumber : request.getSerialNumbers()) {
            // 只有设备实际拥有者拥有权限（被分享用户无权限）
            Integer adminCheck = deviceAuthService.checkActivatedAccess(userToken.getUserId(), serialNumber);
            if (SUCCESS.getCode() != adminCheck) {
                continue;
            }
            List<DeviceMessageNotification> list = messageNotificationSettingsService.queryMessageNotificationSettingsListV1(serialNumber, userToken.getUserId());
            if (Optional.ofNullable(request.getFilterByAiAnalyze()).orElse(true)) { // 老版app没有这个字段，默认过滤。新版app传false，不过滤。
                list.removeIf(it -> !it.getEnable());
            }
            JSONObject object = new JSONObject();
            object.put("serialNumber", serialNumber);
            object.put("list", list);
            result.add(object);
        }
        return Result.ListResult(result);
    }

    /**
     * 获取用户推送过滤设置V3  （所有归一化的通知）
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/queryMessageNotification/v3")
    @LogRequestAndResponse
    public Result queryMessageNotificationV3(@LoginUserToken UserToken userToken,
                                             @RequestBody MessageNotificationSettingRequest request,
                                             HttpServletRequest httpServletRequest) {
        List<JSONObject> result = new ArrayList<>();
        for(String serialNumber : request.getSerialNumbers()) {
            // 只有设备实际拥有者拥有权限（被分享用户无权限）
            Integer adminCheck = deviceAuthService.checkActivatedAccess(userToken.getUserId(), serialNumber);
            if (SUCCESS.getCode() != adminCheck) {
                continue;
            }
            List<DeviceMessageNotification> list = messageNotificationSettingsService.queryMessageNotificationSettingsListV3(serialNumber, userToken.getUserId());
            JSONObject object = new JSONObject();
            object.put("serialNumber", serialNumber);
            object.put("list", list);
            result.add(object);
        }
        return new Result(result);
    }


        /**
         * 查询是否初次使用
         *
         * @param request
         * @param httpServletRequest
         * @return
         */
    @RequestMapping("/queryMessageNotification/first/use")
    @LogRequestAndResponse
    public Result queryMessageNotificationFirstUse(@LoginUserToken UserToken userToken,
                                                   @RequestBody MessageNotificationSettingRequest request,
                                                   HttpServletRequest httpServletRequest) {

        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userToken.getUserId(), request.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return new Result(messageNotificationSettingsService.queryMessageNotificationSetting(request.getSerialNumber(), userToken.getUserId()));
    }

    /**
     * 获取设备SD卡视频列表
     *
     * @param userId
     * @param retrieveLocalVideosRequest
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/retrieveLocalVideos")
    @LogRequestAndResponse
    public Result retrieveLocalVideos(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody RetrieveLocalVideosRequest retrieveLocalVideosRequest,
                                      HttpServletRequest httpServletRequest) {
        String serialNumber = retrieveLocalVideosRequest.getSerialNumber();

        LOGGER.info("Retrieve local videos for user {} and device {}", userId, serialNumber);

        Integer adminCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return new Result(deviceSdCardService.retrieveLocalVideos(retrieveLocalVideosRequest));
    }

    /**
     * 保存当前摇头点，返回当前截图
     *
     * @param userId
     * @param rotationPointSaveRequest
     * @return
     */
    @PostMapping(value = "/saveRotationPoint")
    Result saveRotationPoint(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @ModelAttribute RotationPointSaveRequest rotationPointSaveRequest,
                             HttpServletRequest httpServletRequest) {


        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, rotationPointSaveRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return rotationPointService.saveRotationPoint(rotationPointSaveRequest);
    }

    /**
     * 获取设备保存的摇头点
     *
     * @param userId
     * @param deviceRequest
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getRotationPoints", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getRotationPoints(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest deviceRequest,
                             HttpServletRequest httpServletRequest) {


        Integer commonAccess = deviceAuthService.checkCommonAccess(userId, deviceRequest.getSerialNumber());
        if (SUCCESS.getCode() != commonAccess) {
            return ResultCollection.getResult(commonAccess);
        }

        return Result.ListResult(rotationPointService.getRotationPoints(deviceRequest.getSerialNumber()));
    }

    /**
     * 旋转到到收藏的摇头点
     *
     * @param userId
     * @param rotationPointRequest
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/rotateToPoint", produces = MediaType.APPLICATION_JSON_VALUE)
    Result rotateToPoint(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody RotationPointRequest rotationPointRequest,
                         HttpServletRequest httpServletRequest) {


        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, rotationPointRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return rotationPointService.rotateToPoint(rotationPointRequest);
    }

    /**
     * 删除设备收藏的摇头点
     *
     * @param userId
     * @param rotationPointRequest
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/deleteRotationPoint", produces = MediaType.APPLICATION_JSON_VALUE)
    Result deleteRotationPoint(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody RotationPointRequest rotationPointRequest,
                               HttpServletRequest httpServletRequest) {


        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, rotationPointRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        return rotationPointService.deleteRotationPoint(rotationPointRequest);
    }

    /**
     * App发起换网请求
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/switchWifi", produces = MediaType.APPLICATION_JSON_VALUE)
    Result switchWifi(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                      HttpServletRequest httpServletRequest) {


        return Result.KVResult("switchWifiOperationId", deviceInfoService.generateSwitchWifiOperation(request, userId));
    }

    /**
     * App绑定完成后设备名、位置、固件是否需要升级
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/deviceBindInit", produces = MediaType.APPLICATION_JSON_VALUE)
    Result deviceBindInit(@LoginUserToken UserToken userToken,
                          @RequestBody DeviceRequest request,
                          HttpServletRequest httpServletRequest) {

        return new Result(deviceInfoService.deviceBindInit(userToken.getUserId(),request));
    }
    /**
     * App绑定完成后设备名、位置、固件是否需要升级-v1，返回包含Home 信息
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/deviceBindInit/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    Result deviceBindInitV1(@LoginUserToken UserToken userToken,
                          @RequestBody DeviceRequest request,
                          HttpServletRequest httpServletRequest) {

        return new Result(deviceInfoService.deviceBindInitV2(userToken.getUserId(),request));
    }

    /**
     * 根据userSn获取设备型号
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/deviceModelNo", produces = MediaType.APPLICATION_JSON_VALUE)
    Result deviceModeNo(@LoginUserToken UserToken userToken, @RequestBody DeviceRequest request,
                        HttpServletRequest httpServletRequest) {

        return deviceManualService.getModelNoByUserSn(request.getUserSn(), request.getBindCode());
    }

    /**
     * 设备push image
     *
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/devicePushImage", produces = MediaType.APPLICATION_JSON_VALUE)
    Result devicePushImage(@LoginUserToken UserToken userToken,
                           HttpServletRequest httpServletRequest) {

        return new Result(deviceInfoService.queryUserDevicePushImage(userToken.getUserId()));
    }

    /**
     * 获取设备的ap信息v2
     * @param userId
     * @param deviceApInfoRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryDeviceApInfo/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryDeviceApInfoV2(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceApInfoRequest deviceApInfoRequest) {
        JSONObject data = deviceApInfoHelper.parseApRuleText(deviceApInfoRequest.getApRuleText());
        if (data == null) return Result.Failure("解析失败");
        DeviceApInfoDO deviceApInfoDO = deviceInfoService.queryDeviceApInfo(userId, deviceApInfoRequest.getSerialNumber()
                , data.getString("userSn"), data.getString("apInfoVersion"));
        JSONObject deviceApInfoJson = (JSONObject) JSON.toJSON(deviceApInfoDO);
        return Result.KVResult("deviceApInfo", deviceApInfoJson.fluentPutAll(data));
    }

    /**
     * 获取设备的ap信息
     * @param userId
     * @param deviceApInfoRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryDeviceApInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryDeviceApInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceApInfoRequest deviceApInfoRequest, HttpServletRequest request) {
        // 解析apInfoVersion
        String userSn = deviceApInfoRequest.getUserSn();
        String apInfoVersion = null;
        String deviceState = null;
        String apRuleTextDeviceStatePosition = null;
        Integer supportApConnect = null;
        Integer supportApSetWifi = null;
        String networkModePosition = null;
        if (!StringUtils.isEmpty(deviceApInfoRequest.getApRuleText())) {
            String apRuleText = deviceApInfoRequest.getApRuleText();
            String apRuleTextWithOutAX = (apRuleText.startsWith("AX") || apRuleText.startsWith("ax")) ? apRuleText.substring(2) : apRuleText;
            String ruleVersion = apRuleTextWithOutAX.substring(0, 2);
            userSn = apRuleTextWithOutAX.length() >= 17 ? apRuleTextWithOutAX.substring(2, 17) : null;
            if (ruleVersion.equalsIgnoreCase("00")) {
                deviceState = apRuleTextWithOutAX.substring(17, 19);
                apRuleTextDeviceStatePosition = "17-19";
            } else if (ruleVersion.equalsIgnoreCase("01")) {
                deviceState = apRuleText.substring(19, 21);
                apRuleTextDeviceStatePosition = "19-21";
                apInfoVersion = apRuleText.length() >= 23 ? apRuleText.substring(21, 23) : null;
            } else if (ruleVersion.equalsIgnoreCase("02")) {
                deviceState = apRuleText.substring(19, 21);
                apRuleTextDeviceStatePosition = "19-21";
                apInfoVersion = apRuleText.length() >= 23 ? apRuleText.substring(21, 23) : null;
                supportApConnect = apRuleText.length() >= 24 ? Integer.valueOf(apRuleText.substring(23, 24)) : 0;
            } else if (ruleVersion.equalsIgnoreCase("03")) {
                deviceState = apRuleText.substring(19, 21);
                apRuleTextDeviceStatePosition = "19-21";
                apInfoVersion = apRuleText.length() >= 23 ? apRuleText.substring(21, 23) : null;
                supportApConnect = apRuleText.length() >= 24 ? Integer.valueOf(apRuleText.substring(23, 24)) : 0;
                supportApSetWifi = apRuleText.length() >= 25 ? Integer.valueOf(apRuleText.substring(24, 25)) : 0;
                networkModePosition = apRuleText.length() >= 26 ? "25-26" : null;
            } else {
                return queryDeviceApInfoV2(userId, deviceApInfoRequest);
            }
        }

        log.info("queryDeviceApInfo serialNumber {} userSn {} apInfoVersion {} deviceState {} apRuleTextDeviceStatePosition {}", deviceApInfoRequest.getSerialNumber(), userSn, apInfoVersion, deviceState, apRuleTextDeviceStatePosition);

        DeviceApInfoDO deviceApInfoDO = deviceInfoService.queryDeviceApInfo(userId, deviceApInfoRequest.getSerialNumber(), userSn, apInfoVersion);
        deviceApInfoDO.setApRuleTextDeviceStatePosition(apRuleTextDeviceStatePosition);
        deviceApInfoDO.setSupportApConnect(supportApConnect);
        deviceApInfoDO.setSupportApSetWifi(supportApSetWifi);
        deviceApInfoDO.setNetworkModePosition(networkModePosition);
        return Result.KVResult("deviceApInfo", deviceApInfoDO);
    }

    // 判断版本字符串是否大于等于最小版本
    // 如果绑定字符串只是新加字段，兼容以前的格式，没必要每次都修改后端代码
    public static boolean greaterVersionNum(String ruleVersion, int minVersionNum) {
        if (ruleVersion == null || !NumberUtils.isDigits(ruleVersion)) return false;
        return Integer.parseInt(ruleVersion) > minVersionNum;
    }

    /**
     * 查询通过发现的设备的信息
     *
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(path = "/queryFoundDeviceInfo")
    public Result queryFoundDeviceInfo(@RequestBody FoundDeviceInfoQuery input) {
        Result<FoundDeviceInfoResult> result = deviceInfoService.queryFoundDeviceInfo(input);
        return result;
    }



    /**
     * 查询设备出厂信息
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/queryDeviceManufactureInfo")
    @LogRequestAndResponse
    public Result queryDeviceManufactureInfo(@Valid @RequestBody DeviceRequest request,
                                             HttpServletRequest httpServletRequest){

        DeviceManufactureInfoResponse response = factoryDataQueryService.queryDeviceManufactureInfoDO(request.getUserSn(), request.getApp().getTenantId());
        if(org.apache.commons.lang3.StringUtils.isEmpty(response.getSerialNumber())){
            log.error("queryDeviceManufactureInfo error , {}",JSON.toJSONString(request));
        }
        return new Result<>(response);
    }

    /**
     * 获取设备组件绑定信息
     * @param serialNumberList
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/listDeviceComponentBindInfo")
    @LogRequestAndResponse
    public Result listDeviceComponentBindInfo(@Valid @RequestBody BindInfoRequest bindInfoRequest,
                                              HttpServletRequest httpServletRequest){
        List<DeviceBindInfoDO> bindInfoList = deviceBindInfoService.queryAllDeviceBindInfoDO(bindInfoRequest.getDevices());

        List<String> purchaseSourceList = purchaseSourceDAO.getAllPurchaseSources();

        Map<String, Object> response = new HashMap<>();
        response.put("bindInfo", bindInfoList);
        //购买渠道列表，单独维护一张表
        response.put("purchaseSourceList", purchaseSourceList);

        return new Result<>(response);
    }

    @RequestMapping("/warrantyOrder")
    @LogRequestAndResponse
    public Result insertWarrantyOrders(@Valid @RequestBody WarrantyOrderRequest warrantyOrderRequest,
                                       @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       HttpServletRequest httpServletRequest) {
        deviceBindInfoService.insertWarrantyOrders(warrantyOrderRequest.getOrderList(), userId);
        return Result.Success();
    }
}
