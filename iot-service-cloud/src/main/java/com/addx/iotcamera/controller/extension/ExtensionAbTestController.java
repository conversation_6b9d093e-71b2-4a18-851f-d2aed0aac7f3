package com.addx.iotcamera.controller.extension;

import com.addx.iotcamera.bean.request.ExtensionAbTestRequest;
import com.addx.iotcamera.bean.response.ExtensionAbTestResponse;
import com.addx.iotcamera.service.app.ExtensionAbTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.RequestAttributeKeys;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@Tag(name = "ExtensionAbTestController", description = "扩展功能AB测试接口")
@RequestMapping("/extension")
public class ExtensionAbTestController {

    @Autowired
    private ExtensionAbTestService extensionAbTestService;

    @PostMapping("/abTest")
    @Operation(summary = "获取扩展功能AB测试信息", description = "获取扩展功能AB测试信息，包括用户级别和设备级别的启用状态")
    public Result<ExtensionAbTestResponse> getExtensionAbTest(
            @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
            @RequestBody ExtensionAbTestRequest request) {
        log.info("Get extension AB test info for user: {}, extension: {}", userId, request.getExtensionName());
        ExtensionAbTestResponse response = extensionAbTestService.getExtensionAbTestInfo(userId, request.getExtensionName());
        return new Result<>(response);
    }
} 