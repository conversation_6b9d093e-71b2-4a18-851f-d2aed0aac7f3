package com.addx.iotcamera.controller.device.operation;

import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.device.DeviceDormancySwitchRequest;
import com.addx.iotcamera.bean.device.WebsocketTicketRequest;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.publishers.vernemq.VernemqPublisher;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.publishers.vernemq.requests.AlarmRequest;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceCallService;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import com.addx.iotcamera.service.deviceplatform.alexa.safemo.AlexaSafemoLiveService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.utils.PhosUtils;
import org.addx.iot.common.vo.Result;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.addx.iotcamera.bean.msg.MsgType.PIR_ALARM_CANCEL_USER;
import static com.addx.iotcamera.constants.DeviceInfoConstants.CMD_DEVICE_OPERATION_PREFIX;
import static com.addx.iotcamera.service.StreamService.DEFAULT_LIVE_RESOLUTION;
import static org.addx.iot.common.constant.AppConstants.TENANTID_SAFEMO;
import static org.addx.iot.common.enums.ResultCollection.DEVICE_DORMANCY_STATUS;
import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@RestController
@RequestMapping("/device")
@Slf4j
public class DeviceAVController {

    @SuppressWarnings("all")
    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private DeviceAuthService deviceAuthService;

    @Autowired
    private UserService userService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private StreamService streamService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private VernemqPublisher vernemqPublisher;

    @Autowired(required = false)
    private AlexaService alexaService;

    @Autowired(required = false)
    private GoogleHomeService googleHomeService;

    @Autowired
    private DeviceDormancyPlanService deviceDormancyPlanService;

    @Autowired
    AlexaSafemoLiveService alexaSafemoLiveService;

    @Autowired
    private VipService vipService;
    @Autowired
    private UserRoleService userRoleService;

    @Resource
    private NotificationService notificationService;

    @Resource
    private DeviceCallService deviceCallService;


    /**
     * 用途：
     * 1、方案产品的app，调用iot-service的这个接口，分配kiss地址
     * 2、sapp与绑定云端的cx，调用iot-service的这个接口，分配kiss-safertc地址（ss121新增）
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getWebrtcTicket")
    Result appGetLiveTicket(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                            @RequestBody WebrtcRequest request,
                            HttpServletRequest httpServletRequest) {

        // 检查权限
        if (deviceAuthService.checkCommonAccess(userId, request.getSerialNumber()) != SUCCESS.getCode()) {
            return ResultCollection.DEVICE_NO_ACCESS.getResult();
        }
        //限制4G设备非vip 的操作
        int deviceAdminUser = userRoleService.getDeviceAdminUser(request.getSerialNumber());
        if (request.getVerify4GVIPStatus() && deviceInfoService.checkIf4GDeviceHasOfficialSimCard(request.getSerialNumber()) &&
                !vipService.isVipDevice(deviceAdminUser, request.getSerialNumber())) {
            return ResultCollection.DEVICE_4G_NO_ACCESS.getResult();
        }
        if (request.getVerifyDormancyStatus()) {
            // 普通直播才需要验证是否在休眠计划内
            Boolean deviceDormancyStatus = deviceDormancyPlanService.checkDeviceDormancyStatus(request.getSerialNumber());
            try {
                if (deviceDormancyStatus && TENANTID_SAFEMO.equals(userService.getUserTenantId(userId))) {
                    Result result = deviceDormancyPlanService.deviceDormancySwitch(userId, new DeviceDormancySwitchRequest().setSerialNumber(request.getSerialNumber()).setDormancySwitch(0));
                    log.info("safemo getWebrtcTicket set deviceDormancySwitch end! userId={},sn={},result={}", userId, request.getSerialNumber(), JSON.toJSONString(result));
                    if (Result.successFlag.equals(result.getResult())) deviceDormancyStatus = false;
                }
            } catch (Throwable e) {
                log.error("safemo getWebrtcTicket set deviceDormancySwitch error! userId={},sn={}", userId, request.getSerialNumber(), e);
            }
            if (deviceDormancyStatus) {
                return Result.Error(DEVICE_DORMANCY_STATUS);
            }
        }
        // 就是这么简单
        return new Result(streamService.getWebrtcTicket(userId, request.getSerialNumber(), null, userId + ""
                , Optional.ofNullable(request.getSupportUnlimitedWebsocket()).orElse(false)
                , Optional.ofNullable(request.getNeedWakeupDevice()).orElse(true)
        ));
    }

    /**
     * 用途：
     * 1、方案产品的camera，调用iot-service的这个接口，分配kiss地址
     * 2、绑定云端的cx，调用iot-service的这个接口，分配kiss-safertc地址（ss121新增）
     * 3、绑定基站的cx，调用iot-local的这个接口，分配kiss-local地址
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getWebsocketTicket")
    Result<WebsocketTicketVO> getWebsocketTicket(@RequestAttribute(RequestAttributeKeys.SERIAL_NUMBER) String sn,
                                                 @RequestBody(required = false) WebsocketTicketRequest request,
                                                 HttpServletRequest httpServletRequest) {
        if (org.apache.commons.lang3.StringUtils.isBlank(sn)) {
            return ResultCollection.DEVICE_NO_ACCESS.getResult();
        }
        if (request == null) request = new WebsocketTicketRequest();
        request.setSn(sn);
        final WebsocketTicketVO websocketTicket = streamService.getWebsocketTicket(request);
        return new Result(websocketTicket);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getWebrtcConfig")
    Result<WebsocketTicketVO> getWebrtcConfig(@RequestAttribute(RequestAttributeKeys.SERIAL_NUMBER) String sn,
                                              HttpServletRequest httpServletRequest) {
        if (org.apache.commons.lang3.StringUtils.isBlank(sn)) {
            return ResultCollection.DEVICE_NO_ACCESS.getResult();
        }
        return new Result(streamService.getWebrtcConfig(sn));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/notAppGetWebrtcTicket")
    Result notAppGetWebrtcTicket(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                 @RequestBody NotAppWebrtcRequest request,
                                 HttpServletRequest httpServletRequest) {
        return notAppGetWebrtcTicket(userId, request);
    }

    public Result<P2PTicketResponse> notAppGetWebrtcTicket(Integer userId, NotAppWebrtcRequest request) {
        // 检查权限
        if (deviceAuthService.checkCommonAccess(userId, request.getSerialNumber()) != SUCCESS.getCode()) {
            return ResultCollection.DEVICE_NO_ACCESS.getResult();
        }

        // 检查sn权限
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setUserId(userId);
        deviceDO.setSerialNumber(request.getSerialNumber());
        deviceDO = deviceInfoService.getSingleDevice(deviceDO, userId);

        String tenantId = userService.queryTenantIdById(userId);

        P2PTicketResponse p2pTicketResponse = null;
        if (!StringUtils.isEmpty(request.getConfig())) {
            Map config = JsonUtil.fromJson(request.getConfig(), HashMap.class);
            String devicePlatform = config.containsKey("devicePlatform") ? config.get("devicePlatform").toString() : "alexa";
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(devicePlatform, "alexa") && alexaService != null && (!alexaService.isUserLinked(null, userId) || !alexaService.canSnSupport(deviceDO.getSerialNumber(), deviceDO.getUserSn()))) {
                return ResultCollection.DEVICE_SN_NOT_SUPPORT_PLATFORM.getResult();
            }
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(devicePlatform, "googlehome") && googleHomeService != null && (!googleHomeService.isUserLinked(null, userId) || !googleHomeService.canSnSupport(deviceDO.getSerialNumber(), deviceDO.getUserSn()))) {
                return ResultCollection.DEVICE_SN_NOT_SUPPORT_PLATFORM.getResult();
            }

            String traceId = String.join("_", devicePlatform, tenantId, config.containsKey("sessionId") ? config.get("sessionId").toString() : IDUtil.timeBasedRandomId16("webrtc"));
            p2pTicketResponse = streamService.getWebrtcTicket(userId, request.getSerialNumber(), traceId, "alexa-" + userId, false);
        }

        // 就是这么简单
        return new Result(p2pTicketResponse);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/notAppGetWebrtcTicketForSafemo")
    Result<P2PTicketResponse> notAppGetWebrtcTicketForSafemo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                               @RequestBody NotAppWebrtcRequest request) {
        final String bxSn, cxSn;
        if (request.getSerialNumber().contains("-")) {
            // 如果safemo的设备绑定在BX上，serialNumber的结构是: bxSn+"-"+cxSn
            String[] sns = request.getSerialNumber().split("-");
            bxSn = sns.length > 0 ? sns[0] : null;
            cxSn = sns.length > 1 ? sns[1] : null;
        } else {
            // 如果safemo的设备绑定在云端，serialNumber只是cxSn
            UserRoleDO userRoleDO = userRoleService.getUserRoleDOByUserIdAndSerialNumber(userId, request.getSerialNumber());
            if (userRoleDO == null) {
                return ResultCollection.DEVICE_NO_ACCESS.getResult();
            }
            bxSn = request.getSerialNumber();
            cxSn = request.getSerialNumber();
        }
        String tenantId = userService.queryTenantIdById(userId);
        Map config = JsonUtil.fromJson(request.getConfig(), HashMap.class);
        if (config == null) return null;
        String traceId = String.join("_", "alexa", tenantId, config.containsKey("sessionId") ? config.get("sessionId").toString() : IDUtil.timeBasedRandomId16("webrtc"));
        P2PTicketResponse p2pTicketResponse = alexaSafemoLiveService.getWebrtcTicketForSafemo(userId, bxSn, cxSn, traceId);
        // 就是这么简单
        return new Result<>(p2pTicketResponse);
    }


    /**
     * 开始直播
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/startlive", produces = MediaType.APPLICATION_JSON_VALUE)
    Result startLive(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                     HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {

        String serialNumber = request.getSerialNumber();

        log.info(String.format("Starting live for user: %d with serial number: %s", userId, serialNumber));

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.startLive(serialNumber);
    }

    /**
     * 优化后的开始直播
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/newstartlive", produces = MediaType.APPLICATION_JSON_VALUE)
    Result newStartLive(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request, HttpServletRequest httpServletRequest) {

        String serialNumber = request.getSerialNumber();

        String liveResolution = request.getLiveResolution();

        log.info("Starting live for user: {} with serial number: {} with resolution {}", userId, serialNumber, liveResolution);

        if (!streamService.isValidResolution(liveResolution)) {
            com.addx.iotcamera.util.LogUtil.warn(log, "Illegal resolution received.");
            liveResolution = DEFAULT_LIVE_RESOLUTION;
        }

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.newStartLive(userId, serialNumber, liveResolution);
    }


    @LogRequestAndResponse
    @PostMapping(value = "/changeLiveResolution", produces = MediaType.APPLICATION_JSON_VALUE)
    Result changeResolution(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request, HttpServletRequest httpServletRequest) {

        String serialNumber = request.getSerialNumber();

        String liveResolution = request.getLiveResolution();

        log.info("Changing live resolution for user: {} with serial number: {} with resolution {}", userId, serialNumber, liveResolution);

        if (!streamService.isValidResolution(liveResolution)) {
            com.addx.iotcamera.util.LogUtil.warn(log, "Illegal resolution received.");
            liveResolution = DEFAULT_LIVE_RESOLUTION;
        }

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.changeLiveResolution(serialNumber, liveResolution);
    }

    /**
     * 开始语音
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/startaudio", produces = MediaType.APPLICATION_JSON_VALUE)
    Result startAudio(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody StartAudioRequest request,
                      HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {

        String serialNumber = request.getSerialNumber();

        log.info(String.format("Starting audio for user: %d with serial number: %s", userId, serialNumber));

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.startAudio(request);
    }

    /**
     * 停止语音
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/stopaudio", produces = MediaType.APPLICATION_JSON_VALUE)
    Result stopAudio(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody StopAudioRequest request,
                     HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {

        String serialNumber = request.getSerialNumber();

        log.info(String.format("Stopping audio for user: %d with serial number: %s", userId, serialNumber));

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.stopAudio(serialNumber);
    }

    /**
     * 停止直播
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/stoplive", produces = MediaType.APPLICATION_JSON_VALUE)
    Result stopLive(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                    HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {

        String serialNumber = request.getSerialNumber();

        log.info(String.format("Stopping live for user: %d with serial number: %s", userId, serialNumber));

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.stopLive(userId, serialNumber);
    }

    /**
     * 报警
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/doalarm", produces = MediaType.APPLICATION_JSON_VALUE)
    Result doAlarm(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest request,
                   HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {
        DeviceDO device = JSON.parseObject(JSON.toJSONString(request), DeviceDO.class);

        String serialNumber = request.getSerialNumber();

        log.info(String.format("Alarming for user: %d with serial number: %s", userId, serialNumber));

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        AlarmRequest alarmRequest = new AlarmRequest();
        alarmRequest.setId(CMD_DEVICE_OPERATION_PREFIX + PhosUtils.getUUID());
        alarmRequest.setTime(PhosUtils.getUTCStamp());
        alarmRequest.setName("alarm");
        redisService.setDeviceOperationDOWithEmpty(alarmRequest.getId());
        return Result.OperationResult(VernemqPublisher.alarm(device.getSerialNumber(), alarmRequest));
    }

    /**
     * 报警
     *
     * @param userId
     * @param request
     * @return
     * @throws MqttException
     * @throws IdNotSetException
     */
    @LogRequestAndResponse
    @PostMapping(value = "/cancelAlarm", produces = MediaType.APPLICATION_JSON_VALUE)
    Result cancelAlarm(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                   @RequestBody DeviceRequest request,
                   HttpServletRequest httpServletRequest) throws MqttException, IdNotSetException {

        String serialNumber = request.getSerialNumber();

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        boolean cancelAlarm = deviceCallService.deviceCancelAlarm(serialNumber);
        if(cancelAlarm){
            notificationService.deviceAlarmMessagePush(serialNumber,request.getTraceId(),PIR_ALARM_CANCEL_USER,0,0,(int) Instant.now().getEpochSecond(),null,userId);
        }

        return Result.OperationResult(cancelAlarm);
    }

    /**
     * 根据视频索引，播放保存在SD卡上的视频
     *
     * @param userId
     * @param playLocalVideoRequest
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/playLocalVideo", produces = MediaType.APPLICATION_JSON_VALUE)
    Result playLocalVideo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody PlayLocalVideoRequest playLocalVideoRequest,
                          HttpServletRequest httpServletRequest) {

        String serialNumber = playLocalVideoRequest.getSerialNumber();

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return streamService.playLocalVideo(playLocalVideoRequest);
    }

    /**
     * 停止播放SD上的视频
     *
     * @param userId
     * @param deviceRequest
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/stopPlayLocalVideo", produces = MediaType.APPLICATION_JSON_VALUE)
    Result stopPlayLocalVideo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRequest deviceRequest,
                              HttpServletRequest httpServletRequest) {

        String serialNumber = deviceRequest.getSerialNumber();

        Integer commonCheck = deviceAuthService.checkCommonAccess(userId, serialNumber);
        if (SUCCESS.getCode() != commonCheck) {
            return ResultCollection.getResult(commonCheck);
        }

        return Result.OperationResult(vernemqPublisher.stopPlayLocalVideo(serialNumber));
    }
}
