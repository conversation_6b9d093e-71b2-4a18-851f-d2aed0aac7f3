package com.addx.iotcamera.controller.pay;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.app.payment.ApplePaymentRequest;
import com.addx.iotcamera.bean.app.payment.AppleRecoveryRequest;
import com.addx.iotcamera.bean.app.payment.PaymentRequest;
import com.addx.iotcamera.bean.domain.UserToken;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.PaymentFlagEnums;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.PaymentService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.IOS_NO_TRANSACTIONID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pay")
public class ApplePayController {
    private final static Logger logger = LoggerFactory.getLogger(ALiPaymentController.class);
    private static String appleOrderPre = "apple_order_verify_{tradeNo}";
    private static final int redisLockTimeout = 30;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private JwtHelper jwtHelper;
    @Autowired
    private RedisLockRegistry redisLockRegistry;

    /**
     * 苹果内购支付-生成订单
     *
     * @param paymentRequest
     * @param bindingResult
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/apple/submit/order", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result submitOrder(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @Valid @RequestBody PaymentRequest paymentRequest, BindingResult bindingResult
            , HttpServletRequest httpServletRequest) {

        if (bindingResult.hasErrors()) {
            paymentService.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.SUBMIT.getCode());
            return Result.Error(INVALID_PARAMS, bindingResult.getFieldError().getDefaultMessage());
        }

        logger.info(String.format("applePay User %d query user info.", userId));
        return paymentService.pay(userId, paymentRequest, PaymentTypeEnums.APPLE);
    }


    /**
     * 苹果内购支付-验证订单
     *
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/apple/order/verify", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result orderVerify(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              @Valid @RequestBody ApplePaymentRequest receipt, BindingResult bindingResult
            , HttpServletRequest httpServletRequest) {

        if (bindingResult.hasErrors()) {
            paymentService.sysReportPaymentError(userId, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.VERIFY.getCode());
            return Result.Error(INVALID_PARAMS, bindingResult.getFieldError().getDefaultMessage());
        }

        logger.info(String.format("applePay User %d query user info.", userId));
        String lockKey = appleOrderPre.replace("{tradeNo}", String.valueOf(userId));
        Lock lock = redisLockRegistry.obtain(lockKey);
        boolean result;
        try {
            boolean lockStatus = lock.tryLock(redisLockTimeout, TimeUnit.SECONDS);
            if (!lockStatus) {
                throw new BaseException("apple订单验证获取锁失败.");
            }
            logger.info("apple订单验证获取锁成功,userId:{}", userId);
            result = paymentService.applePaymentVerify(userId, receipt, PaymentTypeEnums.APPLE.getCode());
        } catch (Exception e) {
            result = false;
            com.addx.iotcamera.util.LogUtil.error(logger, "apple订单验证error;" + e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return new Result(result);
    }

    /**
     * 苹果内购支付-验证订单
     *
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/apple/order/verify/v1", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result orderVerifyV1(@LoginUserToken UserToken userToken,
                                @Valid @RequestBody ApplePaymentRequest receipt
            , HttpServletRequest httpServletRequest) throws Exception {

        Integer userId = userToken.getUserId();

        if(receipt.getTierDeviceList() != null){
            //过滤设备列表里 空字符串
            receipt.setTierDeviceList(
                    receipt.getTierDeviceList()
                            .stream()
                            .filter(d -> StringUtils.hasLength(d))
                            .collect(Collectors.toList())
            );
        }


        //增加分布式锁，避免重复计算
        String lockKey = appleOrderPre.replace("{tradeNo}", String.valueOf(userId));
        Lock lock = redisLockRegistry.obtain(lockKey);
        logger.info("applePay User {} query user info transactionId {} device {}", userId,receipt.getTransactionId(),receipt.getTierDeviceList());
        Result result;
        try {
            boolean lockStatus = lock.tryLock(redisLockTimeout, TimeUnit.SECONDS);
            if (!lockStatus) {
                throw new BaseException("apple订单验证获取锁失败.");
            }
            result = paymentService.applePaymentVerifyV1(userId, receipt, PaymentTypeEnums.APPLE.getCode());
        } catch (Exception e) {
            result = Result.Error(IOS_NO_TRANSACTIONID);
            com.addx.iotcamera.util.LogUtil.error(logger, "apple订单验证error;" + e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return result;
    }


    /**
     * 苹果内购支付-订单恢复
     *
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/apple/order/recovery", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result orderVerify(@LoginUserToken UserToken userToken,
                              @Valid @RequestBody AppleRecoveryRequest receipt, BindingResult bindingResult
            , HttpServletRequest httpServletRequest) throws Exception {
        paymentService.iosOrderRecovery(userToken.getUserId());
        return Result.Success();
    }
}
