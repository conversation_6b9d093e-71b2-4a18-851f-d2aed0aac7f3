package com.addx.iotcamera.controller.device.info;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.domain.device.SimCardInfo;
import com.addx.iotcamera.config.device.DeviceIotSimConfig;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/device")
public class DeviceSimController {

    @Autowired
    @Qualifier("appRestTemplate")
    private RestTemplate restTemplate;

    @Resource
    private DeviceIotSimConfig deviceIotSimConfig;

    @PostMapping("/querySimInfo")
    public Result callProjectSim(@RequestBody AppRequestBase request, HttpServletRequest httpServletRequest) {
        String url = deviceIotSimConfig.getConfig().getDomain() +"/sim/info?iccid=" + request.getIccid();

        HttpHeaders headers = new HttpHeaders();
        String authorizationHeader = httpServletRequest.getHeader("Authorization");
        if (authorizationHeader != null && !authorizationHeader.isEmpty()) {
            headers.set("Authorization", authorizationHeader);
        }

        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<SimCardInfo> response = restTemplate.exchange(url, HttpMethod.GET, entity, SimCardInfo.class);

        return new Result(response.getBody());
    }
}
