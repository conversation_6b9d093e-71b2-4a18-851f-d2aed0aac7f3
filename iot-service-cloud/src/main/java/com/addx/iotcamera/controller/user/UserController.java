package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.user.SendMailRequest;
import com.addx.iotcamera.bean.app.user.UserRegistRequest;
import com.addx.iotcamera.bean.domain.*;
import com.addx.iotcamera.bean.domain.verfiy.VerifyCodeResponseDO;
import com.addx.iotcamera.config.TenantTierConfig;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.dao.IPushDAO;
import com.addx.iotcamera.dao.IUserDAO;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.deviceplatform.alexa.AlexaService;
import com.addx.iotcamera.service.deviceplatform.googlehome.GoogleHomeService;
import com.addx.iotcamera.util.LogUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.addx.iotcamera.constants.ReportLogConstants.REPORT_TYPE_APP_RECEIVE_MSG;
import static org.addx.iot.common.enums.ResultCollection.USER_ACCOUNT_CANCEL;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {
    private static Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    @SuppressWarnings("all")
    @Autowired
    private IUserDAO userDAO;

    @SuppressWarnings("all")
    @Autowired
    private JwtHelper jwtHelper;

    @SuppressWarnings("all")
    @Autowired
    private IPushDAO pushDAO;

    @Autowired
    private UserService userService;

    @Autowired
    @Lazy
    private AppFormOptionsService appFormOptionsService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PushService pushService;

    @Autowired(required = false)
    private AlexaService alexaService;

    @Autowired(required = false)
    private GoogleHomeService googleHomeService;

    @Autowired
    private TenantTierConfig tenantTierConfig;

    @Resource
    private ReportLogService reportLogService;

    @Resource
    private MailConfirmService mailConfirmService;

    @Autowired
    private FreeStorageService freeStorageService;

    @LogRequestAndResponse
    @PostMapping(value = "/updatename", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateName(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UserRequest request,
                             HttpServletRequest httpServletRequest) {
        User user = new User();
        user.setName(request.getName());
        user.setLastName(StringUtils.hasLength(request.getLastName()) ? request.getLastName() : "");
        user.setId(userId);
        return userService.updateUserName(user);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getname", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getUserNamePost(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                  HttpServletRequest httpServletRequest) {


        return Result.KVResult("name", userService.getUserName(userId));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/setusertoken", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result setUserToken(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LoginRequest request,
                               HttpServletRequest httpServletRequest) {

        request.setId(userId);

        LOGGER.info(String.format("Setting user token for user: %d", userId));

        PushInfo pushInfo = new PushInfo();
        pushInfo.setAppType(request.getApp().getAppType());
        pushInfo.setBundleName(request.getApp().getBundle());
        pushInfo.setUserId(request.getId());
        if (request.getMsgType() != null && request.getMsgToken() != null) {
            pushInfo.setMsgType(request.getMsgType());
            pushInfo.setMsgToken(request.getMsgToken());
        }
        pushInfo.setIosVoipToken(request.getIosVoipToken());

        User user = userService.queryUserById(userId);
        userService.updateUserAppInfo(user,request.getApp().getAppType(),request.getApp().getVersion(),request.getApp().getVersionName());
        return Result.SqlOperationResult(pushService.setPushInfo(pushInfo));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/updatepassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updatePassword(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UpdatePasswordRequest request,
                                 HttpServletRequest httpServletRequest) {


        LOGGER.info("Update password for user with ID: " + userId);

        return userService.updatePassword(userId, request);
    }


    @LogRequestAndResponse
    @PostMapping(value = "/updateLanguage", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateLanguage(@LoginUserToken UserToken userToken, @RequestBody UserRequest request,
                                 HttpServletRequest httpServletRequest) {
        User user = new User();
        user.setId(userToken.getUserId());
        user.setLanguage(request.getTargetLanguage());
        user.setTenantId(request.getApp().getTenantId());
        return userService.updateUserLanguage(user);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getaccountinfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAccountInfo(@RequestHeader(value = "Authorization") String authStr,
                                 @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                 HttpServletRequest httpServletRequest) {
        LOGGER.info("Get account info for user : {}", userId);

        final User storedUser = userService.queryUserById(userId);
        HttpTokenDO httpTokenDO = new HttpTokenDO();
        httpTokenDO.setTokenType(authStr.split(" ")[0]);
        httpTokenDO.setToken(authStr);
        final LoginResponseDO responseDO = userService.buildLoginResponseDO(storedUser, httpTokenDO);

        // 触发计算用户link状态
        if (alexaService != null || googleHomeService != null) {
            CompletableFuture.runAsync(() -> {
                if (alexaService != null) {
                    alexaService.isUserLinked(null, userId);
                }
                if (googleHomeService != null) {
                    googleHomeService.isUserLinked(null, userId);
                }
            });
        }

        return new Result(responseDO);
    }

    /**
     * 用户注销
     *
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/cancellation", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result cancellation(@LoginUserToken UserToken userToken,
                               @RequestBody UserRequest request,
                               HttpServletRequest httpServletRequest) {
        request.setId(userToken.getUserId());
        userService.cancellationUser(request);
        return Result.Success();
    }

    /**
     * app上报接受到Push
     * @param userToken
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/reportReceivePush", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAccountInfo(@LoginUserToken UserToken userToken, @RequestBody JSONObject request,
                                 HttpServletRequest httpServletRequest) {

        long pushTime = request.getLong("timestamp");
        long receiveTime = System.currentTimeMillis() / 1000;

        request.put("pushTime", pushTime);
        request.put("msgPushToMsgReceiveTimeTook", receiveTime - pushTime);
        request.put("userId", userToken.getUserId());
        request.put("apiReceiveTime", receiveTime);
        reportLogService.appReportMsgReceive(REPORT_TYPE_APP_RECEIVE_MSG, request);
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getMsgPushToReceiveHistogramOptional().ifPresent(it -> {
            final String tenantId = Optional.ofNullable(request.getString("tenantId")).orElse("");
            final String pushType = Optional.ofNullable(request.getString("pushType")).orElse("");
            final String phoneModel = Optional.ofNullable(request.getString("phoneModel")).orElse("");
            final String[] labels = {PrometheusMetricsUtil.getHostName(), tenantId, pushType, phoneModel};
            it.labels(labels).observe(receiveTime - pushTime);
        }));
        return Result.Success();
    }

    /**
     * 查询 alexa account link 结果
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/accountlinkedplatforms", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryAccountLinkResult(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request) {
        List<String> accountLinkedPlatformList = new LinkedList<>();
        String tenantId = (request != null && request.getApp() != null) ? request.getApp().getTenantId() : null;
        if (alexaService != null && alexaService.isSupportTenantId(tenantId)) {
            if (alexaService.isUserLinked(tenantId, userId)) {
                accountLinkedPlatformList.add("alexa");
            }
            List<String> serialNumberList = userRoleService.getSerialNumbersByUserId(userId);
            if (!CollectionUtils.isEmpty(serialNumberList)) {
                boolean hasSnSupportAlexa = serialNumberList.stream().anyMatch(sn -> alexaService.canSnSupport(sn, null));
                if (hasSnSupportAlexa) {
                    accountLinkedPlatformList.add("sn_support_alexa");
                }
            }
        }

        if (googleHomeService != null && googleHomeService.isSupportTenantId(tenantId)) {
            if (googleHomeService.isUserLinked(tenantId, userId)) {
                accountLinkedPlatformList.add("googlehome");
            }
            List<String> serialNumberList = userRoleService.getSerialNumbersByUserId(userId);
            if (!CollectionUtils.isEmpty(serialNumberList)) {
                boolean hasSnSupportAlexa = serialNumberList.stream().anyMatch(sn -> googleHomeService.canSnSupport(sn, null));
                if (hasSnSupportAlexa) {
                    accountLinkedPlatformList.add("sn_support_googlehome");
                }
            }
        }

        return Result.ListResult(accountLinkedPlatformList);
    }

    /**
     * 查询user的tenantName
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/querytenantnamebyuserId", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryTenantNameByUserId(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        String tenantId = userService.queryTenantIdById(userId);
        String tenantName = tenantTierConfig.getConfig().containsKey(tenantId) ? tenantTierConfig.getConfig().get(tenantId) : tenantId;
        Map resultMap = new HashMap();
        resultMap.put("tenantId", tenantId);
        resultMap.put("tenantName", tenantName);
        return new Result(resultMap);
    }

    /**
     * 获取app填写表单选型
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getFormOptions", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getFormOptions(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppFormOptionsRequest appFormOptionsRequest) {
        AppFormOptionsDO appFormOptionsDO = null;
        try {
            appFormOptionsDO = appFormOptionsService.getAppFormOptions(userId, appFormOptionsRequest.getSerialNumber());
        } catch (Throwable e) {
            LogUtil.error(log, "getAppFormOptions error! userId={},sn={}", userId, appFormOptionsRequest.getSerialNumber(), e);
        }
        if (appFormOptionsDO == null) {
            appFormOptionsDO = userService.getAppFormOptions(userId, appFormOptionsRequest);
        }
        return new Result(appFormOptionsDO);
    }

    /**
     * 发送验证码
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/sendConfirmCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result sendConfirmCode(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  @Valid @RequestBody MailConfirmRequest mailConfirmRequest) throws ClientException {
        mailConfirmRequest.setUserId(userId);
        return mailConfirmService.sendConfirmCode(mailConfirmRequest);
    }


    /**
     * 验证邮箱验证码
     *
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/verifyConfirmCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result verifyConfirmCode(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  @RequestBody MailConfirmRequest mailConfirmRequest) {
        User user = userService.queryUserById(userId);
        if(user == null){
            return Result.Error(USER_ACCOUNT_CANCEL);
        }
        mailConfirmRequest.setEmail(user.getEmail());
        return mailConfirmService.checkConfirm(mailConfirmRequest);
    }

    // 只提供给paas-sdk使用
    @LogRequestAndResponse
    @PostMapping(value = "/queryFreeTier", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result queryFreeTier(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        return new Result(freeStorageService.getPaasFreeStorage(userId));
    }

    /**
     * 验证用户是否已注册
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/verify/exist", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result verifyExist(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              @RequestBody UserRegistRequest request) {
        return userService.verifyUserExist(userId,request.getEmail(),request.getApp().getTenantId());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/sendEmail", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result sendMail(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              @RequestBody SendMailRequest request) {
        return userService.sendMail4Kb(userId,request);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/sendCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result sendCode(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                           @RequestBody MailConfirmRequest request) throws ClientException {
        request.setUserId(userId);
        return mailConfirmService.sendCode(userId,request);
    }


    @LogRequestAndResponse
    @PostMapping(value = "/authCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result authCode(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                           @RequestBody MailConfirmRequest request) {
        if (StringUtils.isEmpty(request.getUserDeviceId())) {
            return Result.Error(-102, "INVALID_PARAMS");
        }

        User user = userService.queryUserById(userId);
        if(user == null){
            return Result.Error(USER_ACCOUNT_CANCEL);
        }
        request.setUserId(user.getId());
        request.setEmail(request.getEmail());
        request.setUserDeviceId(request.getUserDeviceId());
        Result result = mailConfirmService.checkOneFAConfirmCode(request);
        if (result.getResult().equals(0)) {
            userService.appendData(request, (VerifyCodeResponseDO) result.getData());
        }
        return result;
    }

}
