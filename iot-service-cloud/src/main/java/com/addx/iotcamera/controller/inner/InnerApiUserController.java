package com.addx.iotcamera.controller.inner;

import com.addx.iotcamera.bean.domain.PushInfo;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.openapi.OpenApiResult;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.publishers.notification.PushArgs;
import com.addx.iotcamera.service.PushInfoService;
import com.addx.iotcamera.service.UserService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;

@Slf4j
@RequestMapping("/inner-api/user")
@RestController
public class InnerApiUserController {

    @Autowired
    private UserService userService;
    @Autowired
    private PushInfoService pushInfoService;

    @PostMapping("/getPushInfoList")
    @LogRequestAndResponse
    public OpenApiResult getPushInfoList(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody JSONObject reqBody) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final JSONArray userIdsArr = Optional.ofNullable(reqBody.getJSONArray("userIds")).orElseGet(JSONArray::new);
        final List<PushInfo> pushInfoList = userIdsArr.toJavaList(Integer.class).stream().distinct()
                .map(pushInfoService::getPushInfo).filter(it -> it != null).collect(Collectors.toList());
        return OpenApiResult.builder().code(0).message("Success").data(new JSONObject().fluentPut("pushInfoList", pushInfoList)).build();
    }

    @PostMapping("/getPushArgsList")
    @LogRequestAndResponse
    public OpenApiResult getPushArgsList(HttpServletRequest request, HttpServletResponse response
            , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
            , @RequestBody JSONObject reqBody) throws Exception {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return OpenApiResult.<JSONObject>builder().code(102).message("tenantId参数错误:" + account.getTenantIds()).build();
        }
        final List<Integer> userIds = Optional.ofNullable(reqBody.getJSONArray("userIds")).orElseGet(JSONArray::new)
                .toJavaList(Integer.class).stream().distinct().collect(Collectors.toList());
        final List<PushArgs> pushArgsList = new LinkedList<>();
        for (Integer userId : userIds) {
            final User user = userService.queryUserById(userId);
            if (user == null) {
                log.error("getPushArgsList not found user! userId={}", userId);
                continue;
            }
            final PushInfo pushInfo = pushInfoService.getPushInfo(userId);
            if (pushInfo == null) {
                log.error("getPushArgsList not found pushInfo! userId={}", userId);
                continue;
            }
            final PushArgs pushArgs = PushArgs.builder()
                    .msgType(pushInfo.getMsgType())
                    .msgToken(pushInfo.getMsgToken())
                    .iosVoipToken(pushInfo.getIosVoipToken())
                    .bundleName(pushInfo.getBundleName())
                    .userId(pushInfo.getUserId())
                    .tenantId(user.getTenantId())
                    .serialNumber(null).traceId(null) // safepush自己填充
                    .build();
            pushArgsList.add(pushArgs);
        }
        return OpenApiResult.builder().code(0).message("Success").data(new JSONObject().fluentPut("pushArgsList", pushArgsList)).build();
    }

}
