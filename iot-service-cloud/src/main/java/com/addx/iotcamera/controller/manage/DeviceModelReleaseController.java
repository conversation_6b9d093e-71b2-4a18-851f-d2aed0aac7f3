package com.addx.iotcamera.controller.manage;

import com.addx.iotcamera.bean.device.FileIssuedSaveVO;
import com.addx.iotcamera.bean.device.model.*;
import com.addx.iotcamera.bean.domain.DeviceModel;
import com.addx.iotcamera.service.BatteryConfigPushService;
import com.addx.iotcamera.service.device.model.*;
import com.addx.iotcamera.service.firmware.KernelFirmwareService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/release/model")
public class DeviceModelReleaseController {
    @Resource
    private DeviceModelConfigService deviceModelConfigService;

    @Resource
    private DeviceModelEventService deviceModelEventService;

    @Resource
    private DeviceModelIconService deviceModelIconService;

    @Resource
    private DeviceModelTenantService deviceModelTenantService;

    @Resource
    private DeviceModelVoiceService deviceModelVoiceService;

    @Resource
    private DeviceModelZendeskService deviceModelZendeskService;

    @Resource
    private DeviceModelBatteryService deviceModelBatteryService;

    @Autowired
    private BatteryConfigPushService batteryConfigPushService;

    @Resource
    private KernelFirmwareService kernelFirmwareService;
    /**
     * 接收device model config
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/config")
    public Result deviceModelConfig(@RequestBody DeviceModel model
            ,HttpServletRequest httpServletRequest) {
        deviceModelConfigService.saveDeviceModelConfig(model);
        return Result.Success();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/issuedFiles")
    public Result deviceModelIssuedFiles(@RequestBody FileIssuedSaveVO input
            , HttpServletRequest httpServletRequest) {
        try {
            if (input.getIssuedFiles() != null) {
                if (StringUtils.isBlank(input.getModelNo())) {
                    return Result.Error(400, "modelNo参数不能为空!");
                }
                deviceModelConfigService.saveDeviceModelIssuedFiles(input);
            }
            return Result.Success();
        } catch (Throwable e) {
            log.error("saveDeviceModelIssuedFiles error! input={}", JSON.toJSONString(input), e);
            return Result.Failure("保存失败!");
        }
    }

    /**
     * 接收device model 支持的event
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/event")
    public Result deviceModelEvent(@RequestBody DeviceModelAISupportDo model
            ,HttpServletRequest httpServletRequest) {
        deviceModelEventService.saveDeviceModelEvent(model);
        return Result.Success();
    }

    /**
     * 接收device model icon
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/icon")
    public Result deviceModelIcon(@RequestBody DeviceModelIconDO model
            ,HttpServletRequest httpServletRequest) {
        deviceModelIconService.saveDeviceModelIcon(model);
        return Result.Success();
    }

    /**
     * 接收 tenant支持的 device model
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/tenant")
    public Result deviceModelTenant(@RequestBody DeviceModelTenantSupportDO model
            ,HttpServletRequest httpServletRequest) {

        List<DeviceModelTenantSupportDO> deviceModelTenantSupportDOS = deviceModelTenantService.queryModelTenantByModel(model.getModelNo());
        if(!CollectionUtils.isEmpty(deviceModelTenantSupportDOS)){
            Set<String> exitTenantSet = new HashSet<>( model.getTenantIds());
            for(DeviceModelTenantSupportDO deviceModelTenantSupportDO : deviceModelTenantSupportDOS){
                if(exitTenantSet.contains(deviceModelTenantSupportDO.getTenantId())){
                    continue;
                }

                deviceModelTenantService.deleteDeviceModelTenant(deviceModelTenantSupportDO);
            }
        }


        for(String tenantId : model.getTenantIds()){
            model.setTenantId(tenantId);
            deviceModelTenantService.saveDeviceModelTenant(model);
        }
        return Result.Success();
    }

    /**
     * 接收 device model 初始音量
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/voice")
    public Result deviceModelVoice(@RequestBody DeviceModelVolumeDO model
            ,HttpServletRequest httpServletRequest) {
        deviceModelVoiceService.saveDeviceModelVoice(model);
        return Result.Success();
    }

    /**
     * 接收 device model zendesk 文章地址
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/zendesk")
    public Result deviceModelZendesk(@RequestBody DeviceModelZendeskDO model
            ,HttpServletRequest httpServletRequest) {
        deviceModelZendeskService.saveDeviceModelZendesk(model);
        return Result.Success();
    }

    /**
     * 接收 device model battery
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/battery")
    public Result deviceModelBattery(@RequestBody DeviceModelBatteryDO model
            ,HttpServletRequest httpServletRequest) {
        deviceModelBatteryService.saveDeviceModelBattery(model);
        batteryConfigPushService.publishBatteryConfig(model, 1000); // 异步任务
        return Result.Success();
    }

    /**
     * 接收 model firmware platform
     * @param model
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/firmware/platform")
    public Result deviceModelFirmwarePlatform(@RequestBody DeviceModelFirmwarePlatformDO model
            ,HttpServletRequest httpServletRequest) {
        kernelFirmwareService.modelFirmwarePlatform(model.getModelNo(),model.getPlatform());
        return Result.Success();
    }
}
