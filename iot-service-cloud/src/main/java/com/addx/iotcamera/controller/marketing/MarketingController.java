package com.addx.iotcamera.controller.marketing;

import com.addx.iotcamera.bean.app.SolutionRequest;
import com.addx.iotcamera.config.device.MarketingServiceConfig;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/user/creative")
@Slf4j
public class MarketingController {
    @Autowired
    @Qualifier("appRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private MarketingServiceConfig marketingServiceConfig;

    @ApiOperation(value = "获取全部命中的solutions")
    @PostMapping(value = "/getCreativeList")
    @LogRequestAndResponse
    public Result getCreativeList(@RequestBody SolutionRequest request, HttpServletRequest httpServletRequest) {
        String url = marketingServiceConfig.getDomain() + "/user/creative/getCreativeList";

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", httpServletRequest.getHeader("Authorization"));

        HttpEntity<SolutionRequest> entity = new HttpEntity<>(request, headers);

        ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.POST, entity, JsonNode.class);

        return new Result<>(response.getBody());
    }

    @ApiOperation(value = "上报 关闭banner或者弹窗 行为")
    @PostMapping(value = "/noThanks")
    @LogRequestAndResponse
    public Result noThanks(@RequestBody SolutionRequest request, HttpServletRequest httpServletRequest) {
        String url = marketingServiceConfig.getDomain() + "/user/creative/noThanks";

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", httpServletRequest.getHeader("Authorization"));

        HttpEntity<SolutionRequest> entity = new HttpEntity<>(request, headers);

        ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.POST, entity, JsonNode.class);

        return new Result<>(response.getBody());
    }
}
