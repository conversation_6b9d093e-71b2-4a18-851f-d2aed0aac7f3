package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.CreativeRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.creative.CreativeService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/4/22 18:16
 */
@RestController
@RequestMapping("/user/creative")
public class CreativeController {
    @Autowired
    private CreativeService creativeService;
    @LogRequestAndResponse
    @RequestMapping("/querySlotNames")
    public Result querySlotIds(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                   HttpServletRequest httpServletRequest) throws UnsupportedEncodingException {
        return new Result(creativeService.getActiveSlotNames());
    }

    @LogRequestAndResponse
    @RequestMapping("/getCreative")
    public Result getCreative(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody CreativeRequest request,
                                   HttpServletRequest httpServletRequest) throws UnsupportedEncodingException {
        return new Result(creativeService.getCreative(request.getConfig(), request.getLanguage()));
    }


}
