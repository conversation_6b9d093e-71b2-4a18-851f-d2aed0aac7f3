package com.addx.iotcamera.controller.manage;

import com.addx.iotcamera.bean.app.StatusMarkRequest;
import com.addx.iotcamera.bean.app.StatusMarkResponse;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.AppMarkStatusService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * APP状态标记控制器
 */
@Slf4j
@RestController
@RequestMapping("/management")
public class AppMarkStatusController {

    @Autowired
    private AppMarkStatusService appMarkStatusService;

    /**
     * 获取点击状态
     *
     * @param userId 用户ID
     * @param request 请求参数
     * @param httpServletRequest HTTP请求
     * @return 响应结果
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getStatusMark", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getStatusMark(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                @RequestBody StatusMarkRequest request,
                                HttpServletRequest httpServletRequest) {
        log.info("Get status mark for user: {}, statusKey: {}", userId, request.getStatusKey());
        
        // 设置用户ID
        request.setUserId(userId);
        
        // 获取状态标记
        StatusMarkResponse response = appMarkStatusService.getStatusMark(request);
        
        return new Result<>(response);
    }

    /**
     * 点击状态标记
     *
     * @param userId 用户ID
     * @param request 请求参数
     * @param httpServletRequest HTTP请求
     * @return 响应结果
     */
    @LogRequestAndResponse
    @PostMapping(value = "/clickStatusMark", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result clickStatusMark(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  @RequestBody StatusMarkRequest request,
                                  HttpServletRequest httpServletRequest) {
        log.info("Click status mark for user: {}, statusKey: {}", userId, request.getStatusKey());
        
        // 设置用户ID
        request.setUserId(userId);

        // 点击状态标记
        boolean success = appMarkStatusService.clickStatusMark(request);
        
        return success ? Result.Success() : Result.Failure("Failed to update status mark");
    }
} 