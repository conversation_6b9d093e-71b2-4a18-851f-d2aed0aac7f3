package com.addx.iotcamera.controller.meta.library;

import com.addx.iotcamera.bean.db.VideoImageDO;
import com.addx.iotcamera.bean.db.VideoSliceDO;
import com.addx.iotcamera.bean.exception.BaseException;
import com.addx.iotcamera.bean.tuple.Tuple2;
import com.addx.iotcamera.bean.video.*;
import com.addx.iotcamera.config.VideoSliceConfig;
import com.addx.iotcamera.constants.VideoResolutionConstants;
import com.addx.iotcamera.helper.SliceReportHelper;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.video.StorageAllocateService;
import com.addx.iotcamera.service.video.VideoGenerateService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.addx.iotcamera.util.HttpUtils;
import com.addx.iotcamera.util.SnsUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.event.S3EventNotification;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.constant.RequestAttributeKeys;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URL;
import java.util.LinkedList;
import java.util.List;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.helper.TimeRecorder.videoSliceTimeRecorder;

@Slf4j
@Controller  // 不一定是rest接口，不用@RestController
@RequestMapping(path = PATH_VIDEO)
public class VideoController {

    @Autowired
    private LibraryStatusService libraryStatusService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private VideoService videoService;
    @Autowired
    private VideoStoreService videoStoreService;
    @Autowired
    private VideoGenerateService videoGenerateService;
    @Autowired
    @Lazy
    private StorageAllocateService storageAllocateService;

    @Value("${shardingJdbcLibraryDao.video_slice.batch_insert_ratio:0}")
    private Integer videoSliceBatchInsertRatio = 0;
    @Value("${shardingJdbcLibraryDao.video_slice.batch_insert_white_sn:}")
    private String videoSliceBatchInsertWhiteSn;
    @Autowired
    PlanSupportService planSupportService;
    @Autowired
    StorageServiceAvailableManager storageServiceAvailableManager;


    /**
     * 设备端开始上传视频
     * 实际设备端是通过mqtt调的，这个方法只是用来测试
     *
     * @param request
     */
    @SneakyThrows
    @PostMapping(path = "/uploadBegin", consumes = MediaType.APPLICATION_JSON_VALUE
            , produces = MediaType.APPLICATION_JSON_VALUE)
    public void videoUploadBegin(@RequestBody UploadVideoBeginRequest request, HttpServletResponse response) {
        long pirBeginTimestamp = System.currentTimeMillis();
        // 支持推送视频在app收到通知后的跳转策略
        Result result = videoService.videoUploadBegin(request);
        response.getWriter().write(JSON.toJSONString(result));
        videoService.recordPirTriggerTime(request.getSerialNumber(), request.getTraceId()
                , request.getMqttEventTime(), pirBeginTimestamp, System.currentTimeMillis());
    }

    /**
     * 设备端上报视频上传完成
     *
     * @return
     */
    @LogRequestAndResponse(isPrintResponse = false, requestLogKeyword = "handleVideoMsg begin!")
    @PostMapping(path = "/uploadComplete", consumes = MediaType.APPLICATION_JSON_VALUE
            , produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result videoUploadComplete(@RequestBody JSONObject reqBody) {
        final UploadVideoCompleteRequest request = reqBody.toJavaObject(UploadVideoCompleteRequest.class);
        // 记录是否上传失败
        storageServiceAvailableManager.updateUnAvailableServiceInfo(request);

        JSONObject errMsgMap = UploadVideoCompleteRequest.validate(request);
        if (!errMsgMap.isEmpty()) {
            log.error("videoUploadComplete errorMap={}", JSON.toJSONString(errMsgMap));
            return new Result(ResultCollection.INVALID_PARAMS.getCode(), "参数错误!", errMsgMap);
        }
        Result result = UploadVideoCompleteRequest.preHandle(request);
        if (result.getResult() != Result.successFlag) return result;
        if (videoGenerateService.isEnable(request.getSerialNumber(), request.getTraceId())) {
            Integer userId = userRoleService.getDeviceAdminUser(request.getSerialNumber());
            videoGenerateService.sendVideoMsg(userId, VideoMsgType.UPLOAD_COMPLETE, request.getTraceId(), reqBody);
            return Result.Success();
        }
        return videoService.videoUploadComplete(request);
    }

    /**
     * app端获取视频m3u8文件
     *
     * @param traceId
     * @param token
     * @param playListType 0-VOD,1-EVENT,2-LIVE
     * @param request
     * @param response
     */
    @GetMapping(path = PATH_DOWNLOAD_M3U8 + "/{traceId}" + POSTFIX_M3U8)
    public void downloadM3u8(@PathVariable(value = "traceId", required = false) String traceId,
                             @RequestHeader(value = HEADER_SAFE_RTC_ROOT_URL, required = false) String safeRtcRootUrl,
                             @RequestParam(value = PARAM_TOKEN, required = false) String token,
                             @RequestParam(value = "playListType", required = false, defaultValue = "0") int playListType,
                             @RequestParam(value = "fillVacancy", required = false, defaultValue = "false") boolean fillVacancy, // 是否填充空缺
                             @RequestParam(value = "resolution", required = false) String resolution, // 用于生成特定分辨率的子播放列表
                             @RequestParam(value = PARAM_SUPPORT_MASTER_PLAYLIST, required = false, defaultValue = "false") boolean supportMasterPlaylist, // 是否支持Master Playlist
                             HttpServletRequest request, HttpServletResponse response
    ) throws IOException {
        log.info("downloadM3u8 traceId={},playListType={},fillVacancy={},safeRtcRootUrl={},token={}", traceId, playListType, fillVacancy, safeRtcRootUrl, token);
        if (traceId == null || StringUtils.isBlank(token)) {
            responseError(response, HttpStatus.BAD_REQUEST.value(), "请求参数错误!");
            return;
        }
        // token -> userId
        Integer userId = videoService.validateM3u8Token(traceId, token);
        if (userId == null) {
            responseError(response, HttpStatus.UNAUTHORIZED.value(), "token无效！");
            return;
        }
        Integer adminId = libraryStatusService.queryAdminIdByUserIdAndTraceId(userId, traceId);
        if (adminId == null) {
            throw new BaseException(ResultCollection.NO_LIBRARY_ACCESS, "视频不存在");
        }
        /*
        有两种请求 m3u8 播放列表的方法：一是通过 m3u8 的 URI 进行请求，则该文件必须以 .m3u8 或 .m3u 结尾；
        二是通过 HTTP 进行请求，则请求头Content-Type必须设置为 application/vnd.apple.mpegurl或者audio/mpegurl。
        */
        response.setStatus(HttpStatus.OK.value());
        response.setHeader(HttpHeaders.CONTENT_TYPE, M3U8_CONTENT_TYPE);
//        response.setCharacterEncoding(M3U8_CHARSET);
//        String fileName = "video_" + libraryId + "_" + System.currentTimeMillis() + ".m3u8";
//        response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
        try (PrintWriter writer = response.getWriter()) {
            if (resolution != null) {
                // 如果指定了分辨率，生成特定分辨率的播放列表（用于Master Playlist的子播放列表）
                List<VideoSliceDO> sliceList = videoStoreService.querySliceByAdminUserIdAndTraceIdAndResolution(adminId, traceId, resolution);
                videoService.downloadVideoM3u8(userId, sliceList, playListType, fillVacancy, safeRtcRootUrl, writer);
                return;
            }
            if (!supportMasterPlaylist) {
                videoService.downloadVideoM3u8(adminId, traceId, playListType, fillVacancy, safeRtcRootUrl, writer);
                return;
            }
            videoService.downloadMasterPlaylistM3u8(adminId, traceId, playListType, fillVacancy, safeRtcRootUrl, token, request, writer);
        }
    }

    /**
     * 查询视频支持的分辨率列表
     * @param traceId 视频追踪ID
     * @param userId 用户ID
     * @return 分辨率选项列表
     */
    @GetMapping("/video/{traceId}/resolutions")
    public Result<List<ResolutionInfo>> getAvailableResolutions(@PathVariable String traceId,
                                                                @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        try {
            Integer adminId = libraryStatusService.queryAdminIdByUserIdAndTraceId(userId, traceId);
            if (adminId == null) {
                return Result.Error(ResultCollection.NO_LIBRARY_ACCESS, "no library access");
            }

            List<ResolutionInfo> resolutions = videoStoreService.queryAvailableResolutionsByTraceId(adminId, traceId);

            resolutions.forEach((e) -> e.setDisplayName(VideoResolutionConstants.getDisplayName(e.getResolution())));

            return new Result<>(resolutions);
        } catch (Exception e) {
            log.error("getAvailableResolutions error, traceId={}, userId={}", traceId, userId, e);
            return Result.Error(ResultCollection.QUERY_RESOLUTION_FAILED, "QUERY_RESOLUTION_FAILED");
        }
    }

    private void responseError(HttpServletResponse response, int statusCode, String message) throws IOException {
        response.setStatus(statusCode);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(message);
        }
    }

    @Autowired
    private VideoSliceConfig videoSliceConfig;

    /*
    处理S3事件通知
    https://docs.aws.amazon.com/zh_cn/sns/latest/dg/sns-example-code-endpoint-java-servlet.html
    eg:
    确认订阅:
    x-amz-sns-message-type: SubscriptionConfirmation
    x-amz-sns-message-id: 165545c9-2a5c-472c-8df2-7ff2be2b3b1b
    x-amz-sns-topic-arn: arn:aws:sns:us-west-2:123456789012:MyTopic
    事件通知:
    x-amz-sns-message-type: Notification
    x-amz-sns-message-id: 22b80b92-fdea-4c2c-8f9d-bdfb0c7bf324
    x-amz-sns-topic-arn: arn:aws:sns:us-west-2:123456789012:MyTopic
    x-amz-sns-subscription-arn: arn:aws:sns:us-west-2:123456789012:MyTopic:c9135db0-26c4-47ec-8998-413945fb5a96
     */
    @PostMapping(path = "/handleS3EventNotify")
    public void handleS3EventNotify(@RequestHeader("x-amz-sns-message-type") String messageType,
                                    @RequestHeader("x-amz-sns-message-id") String messageId,
                                    @RequestHeader("x-amz-sns-topic-arn") String topicArn,
                                    @RequestBody String body,
                                    HttpServletResponse response) {
        log.info("handleS3EventNotify start!");
        log.info("handleS3EventNotify 收到sns消息!messageType={},messageId={},topicArn={},body={}"
                , messageType, messageId, topicArn, body);

        videoSliceTimeRecorder.recordBegin("controller");
        response.setStatus(HttpStatus.OK.value());
        // 不做s3Notify重复校验，后续处理中会根据traceId-order作防重校验
//        if (s3NotifyMessageIdRepeatChecker.isRepeat(messageId)) {
//            com.addx.iotcamera.util.LogUtil.error(log, "handleS3EventNotify 收到重复消息！messageId={}", messageId);
//            return;
//        }
        SnsUtil.Message snsMsg = JSON.parseObject(body, SnsUtil.Message.class);
        if (!SnsUtil.isMessageSignatureValid(snsMsg, videoSliceConfig.getAwsCertEndpoint())) {
            com.addx.iotcamera.util.LogUtil.error(log, "handleS3EventNotify 验证签名失败！body={}", body);
            return;
        }
        if ("SubscriptionConfirmation".equals(messageType)) {
            //   "SubscribeURL" : "https://sns.us-west-2.amazonaws.com/?Action=ConfirmSubscription&TopicArn=arn:aws:sns:us-west-2:123456789012:MyTopic&Token=2336412f37...",
            String subscribeURL = snsMsg.getSubscribeURL();
            try {
                String subscribeResp = IOUtils.toString(new URL(subscribeURL).openStream());
                log.info("handleS3EventNotify 收到订阅确认消息!打开url成功!subscribeURL={},subscribeResp={}", subscribeURL, subscribeResp);
            } catch (IOException e) {
                com.addx.iotcamera.util.LogUtil.error(log, "handleS3EventNotify 收到订阅确认消息!打开url失败!subscribeURL={}", subscribeURL, e);
            }
        } else if ("UnsubscribeConfirmation".equals(messageType)) {
            log.info("handleS3EventNotify 收到取消订阅确认消息!message={}", snsMsg.getMessage());

        } else if ("Notification".equals(messageType)) {
            S3EventNotification s3EventNotification = S3EventNotification.parseJson(snsMsg.getMessage());
            videoService.handleS3EventNotify(s3EventNotification);
        } else {
            log.info("handleS3EventNotify 收到未知消息类型!messageType={}", messageType);
        }
        videoSliceTimeRecorder.recordEnd("controller");
        // 暂时先注释掉。需要统计的时候再打开
//        LinkedHashMap<String, TimeRecorder.Times> key2Times = videoSliceTimeRecorder.clearKey2Times();
//        log.info(buildReportLog(videoSliceTimeRecorder.getName(), key2Times));
    }

    /**
     * 设备端上报切片,取代sns通知
     *
     * @param request
     */
    @LogRequestAndResponse(isPrintResponse = false, requestLogKeyword = "handleVideoMsg begin!")
    @PostMapping(path = "/sliceReport")
    @ResponseBody
    public Result videoSliceReport(@RequestBody VideoSliceReport request, HttpServletRequest httpServletRequest) {
//        if (PirServiceName.valueOf(request.getServiceName()) == null) {
//            return Result.Error(ResultCollection.INVALID_PARAMS, "serviceName参数错误!");
//        }
        if (StringUtils.isBlank(request.getTraceId())) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "traceId参数错误!");
        }
        if (StringUtils.isBlank(request.getSerialNumber())) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "serialNumber参数错误!");
        }
        if (!(request.getFileSize() != null && request.getFileSize() >= 0)) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "fileSize参数错误!");
        }
        if (StringUtils.isBlank(request.getTimezone())) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "timezone参数错误!");
        }
        String imageUrl = request.getImagePath();

        final int userId = userRoleService.getDeviceAdminUser(request.getSerialNumber());
        if (userId <= 0) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "device is unbound!");
        }
        List<VideoSliceDO> sliceList = new LinkedList<>();
        boolean isImageMedia = com.addx.iotcamera.enums.MediaType.IMAGE.equals(request.getMediaType());
        Tuple2<Boolean, Boolean> noPlanAndDeviceSupportImageEvent = planSupportService.isNoPlanAndDeviceSupportImageEvent(userId, request.getSerialNumber());
        boolean isNoPlan = noPlanAndDeviceSupportImageEvent.v0();
        boolean isSupportImageEvent = noPlanAndDeviceSupportImageEvent.v1();

        if (StringUtils.isNotBlank(request.getVideoPath())) {
            if (!(request.getPeriod() != null && request.getPeriod() >= 0)) {
                return Result.Error(ResultCollection.INVALID_PARAMS, "period参数错误!");
            }
            if (!(request.getOrder() != null && request.getOrder() >= 0)) {
                return Result.Error(ResultCollection.INVALID_PARAMS, "order参数错误!");
            }
            if (!(request.getIsLast() != null && request.getIsLast() >= 0 && request.getIsLast() <= 1)) {
                return Result.Error(ResultCollection.INVALID_PARAMS, "isLast参数错误!");
            }
            if (StringUtils.isBlank(request.getServiceName())) {
                final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(request.getVideoPath());
                if ((storeBucket == null || !storageAllocateService.isEnableServiceName(storeBucket.getServiceName())) && !isImageMedia) {
                    com.addx.iotcamera.util.LogUtil.error(log, "sliceReport 不支持的云存储:{}", request.getVideoPath());
                    return Result.Failure("不支持的云存储:" + request.getVideoPath()); // 设备切换节点时可能会有这种情况
                }
            }

            boolean batchInsert = false;
            if (StringUtils.contains(videoSliceBatchInsertWhiteSn, request.getSerialNumber())) {
                batchInsert = true;
            } else {
                int traceIdHashCode = request.getTraceId().hashCode();
                traceIdHashCode = traceIdHashCode ^ traceIdHashCode >>> 16;
                batchInsert = videoSliceBatchInsertRatio <= 0 ? false : (Math.abs(traceIdHashCode) % videoSliceBatchInsertRatio < 5);
            }

            final long utcTimestampMillis = request.getUtcTimestampMillis() != null ? request.getUtcTimestampMillis() : System.currentTimeMillis();
            final long endUtcTimestampMillis = request.getEndUtcTimestampMillis() != null ? request.getEndUtcTimestampMillis() : (utcTimestampMillis + request.getPeriod().longValue());

            VideoSliceDO videoSlice = VideoSliceDO.builder()
                    .serialNumber(request.getSerialNumber())
                    .userId(userId)
                    .traceId(request.getTraceId())
                    .period(VideoSliceDO.computeSliceSeconds(request.getPeriod())) // 存秒值
                    .order(request.getOrder())
                    .isLast(request.getIsLast() == 1)
                    .videoUrl(SliceReportHelper.transFileUrl(request, VideoSliceReport::getVideoPath))
                    .fileSize(request.getFileSize())
                    .s3EventTime(utcTimestampMillis)
                    .endUtcTimestampMillis(endUtcTimestampMillis)
                    .codec(request.getCodec())
                    .timeZone(request.getTimezone())
                    .imageUrl(SliceReportHelper.transFileUrl(request, VideoSliceReport::getImagePath))
                    .serviceName(request.getServiceName())
                    .activateZoneIds(request.getTriggeredZonesIds())
                    .deviceIp(HttpUtils.getRemoteAddr(httpServletRequest))
                    .batchInsert(batchInsert)
                    .timeWatermarkPosition(request.getTimeWatermarkPosition())
                    .logoWatermarkPosition(request.getLogoWatermarkPosition())
                    .resolution(request.getResolution())
                    .build();
            VideoSliceDO.fillTimestampIfNull(videoSlice);

            if (!isImageMedia && request.getVideoType() != null) { // 有新增加参数时，校验utcTimestampMillis和endUtcTimestampMillis
                if (request.getUtcTimestampMillis() == null || request.getUtcTimestampMillis() <= 0) {
                    return Result.Error(ResultCollection.INVALID_PARAMS, "snapshot recording utcTimestampMillis param error!");
                }
                if (request.getEndUtcTimestampMillis() == null || request.getEndUtcTimestampMillis() <= 0) {
                    return Result.Error(ResultCollection.INVALID_PARAMS, "snapshot recording endUtcTimestampMillis param error!");
                }
            }
            videoSlice.setEndUtcTimestampMillis(VideoSliceDO.computeEndUtcTimestampMillis(videoSlice));
            final VideoTypeEnum videoType = VideoTypeEnum.codeOf(request.getVideoType());
            videoSlice.setVideoType(videoType.getCode());
            if (VideoTypeEnum.SNAPSHOT_RECORDING.equals(videoType)) {
                if (request.getSnapshotCaptureInterval() == null || request.getSnapshotCaptureInterval() <= 0) {
                    return Result.Error(ResultCollection.INVALID_PARAMS, "snapshot recording snapshotCaptureInterval param error!");
                }
                if (isImageMedia) {
                    return Result.Error(ResultCollection.INVALID_PARAMS, "snapshot recording motionType param error!");
                }
                videoSlice.setSnapshotCaptureInterval(request.getSnapshotCaptureInterval());
            } else {
                if (request.getMainTraceId() != null && StringUtils.isBlank(request.getMainTraceId())) {
                    return Result.Error(ResultCollection.INVALID_PARAMS, "event recording mainTraceId param error!");
                }
                videoSlice.setMainTraceId(request.getMainTraceId());
            }

            // 图文事件 生成图文事件
            if (request.getMediaType() != null) {
                videoSlice.setMediaType(request.getMediaType());
            }
            if (isNoPlan && !isSupportImageEvent && request.getMediaType() == null) {
                videoSlice.setMediaType(com.addx.iotcamera.enums.MediaType.IMAGE);
            }
            sliceList.add(videoSlice);
            if (videoGenerateService.isEnable(request.getSerialNumber(), request.getTraceId())) {
                videoGenerateService.sendVideoMsg(userId, VideoMsgType.SLICE_REPORT, request.getTraceId(), videoSlice);
                return new Result(new JSONObject().fluentPut("videoPath", request.getVideoPath()));
            }
        } else if (StringUtils.isNotBlank(request.getImagePath())) {
            if (StringUtils.isBlank(request.getServiceName())) {
                final StoreBucket storeBucket = storageAllocateService.getStoreBucketFromUrl(request.getImagePath());
                if (storeBucket == null || !storageAllocateService.isEnableServiceName(storeBucket.getServiceName())) {
                    com.addx.iotcamera.util.LogUtil.error(log, "sliceReport 不支持的云存储:{}", request.getImagePath());
                    return Result.Failure("不支持的云存储:" + request.getImagePath()); // 设备切换节点时可能会有这种情况
                }
            }
            imageUrl = request.getImagePath();
            if (videoGenerateService.isEnable(request.getSerialNumber(), request.getTraceId())) {
                VideoImageDO videoImage = VideoImageDO.builder().serialNumber(request.getSerialNumber()).traceId(request.getTraceId())
                        .imageUrl(SliceReportHelper.transFileUrl(request, VideoSliceReport::getImagePath))
                        .fileSize(request.getFileSize()).s3EventTime(System.currentTimeMillis())
                        .videoType(VideoTypeEnum.codeOf(request.getVideoType()).getCode())
                        .build();
                videoGenerateService.sendVideoMsg(userId, VideoMsgType.IMAGE_REPORT, request.getTraceId(), videoImage);
                return new Result(new JSONObject().fluentPut("imagePath", request.getImagePath()));
            }
        } else {
            return Result.Error(ResultCollection.INVALID_PARAMS, "videoPath和imagePath不能同时为空!");
        }
        videoService.handleVideoSliceUpdateAsync(request.getTraceId(), request.getSerialNumber(), imageUrl, sliceList);
        return Result.Success();
    }

}
