package com.addx.iotcamera.controller.device_msg;

import com.addx.iotcamera.bean.device_msg.KissMsgMethod;
import com.addx.iotcamera.bean.recording.RefreshCredentialRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.ProtoMsgName;
import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.VideoService;
import com.addx.iotcamera.service.device.DeviceDormancyPlanService;
import com.addx.iotcamera.service.device_msg.BaseWsService;
import com.addx.iotcamera.service.device_msg.DeviceMsgService;
import com.addx.iotcamera.service.device_msg.DeviceMsgServiceInterface;
import com.addx.iotcamera.service.device_msg.IotLocalAndCloudProtocol;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.proto_msg.ProtoMsgService;
import com.addx.iotcamera.service.video.VideoStoreService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.proto.deviceMsg.PbLocalVideoDeleted;
import org.addx.iot.common.proto.deviceMsg.PbLocalVideoDeletedResponse;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffset;
import org.addx.iot.common.proto.deviceMsg.PbSyncTimeZoneOffsetResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RequestMapping("/deviceMsg")
@RestController
public class DeviceMsgController {

    @Lazy
    @Autowired
    private DeviceMsgServiceInterface deviceMsgService;
    @Lazy
    @Autowired
//    @Qualifier("deviceMsgService")
    private DeviceMsgService protoDeviceMsgService;
    @Lazy
    @Autowired
    private ProtoMsgService protoMsgService;
    @Lazy
    @Autowired
    private OpenApiConfigService openApiConfigService;
    @Lazy
    @Autowired
    private DeviceDormancyPlanService deviceDormancyPlanService;
    @Lazy
    @Autowired
    private VideoService videoService;
    @Lazy
    @Autowired
    private DeviceInfoService deviceInfoService;

    public static final String CONTENT_TYPE_APPLICATION_PROTOBUF = "application/protobuf";
    @Autowired
    @Lazy
    private VideoStoreService videoStoreService;
    @Autowired
    @Lazy
    private BaseWsService kissWsService;

    @PostConstruct
    public void init() {
        log.info("DeviceMsgController init! deviceMsgService={},protoDeviceMsgService={}", deviceMsgService, protoDeviceMsgService);
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/httpToken", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result httpTokenByJson(HttpServletRequest httReq, HttpServletResponse httpResp
            , @RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER, required = false) String sn
            , @RequestHeader("Host") String host, @RequestBody String payload) {
        return deviceMsgService.httpTokenByJson(sn, payload);
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/status", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result statusByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return deviceMsgService.statusByJson(sn, payload);
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/connection", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result connectionByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return deviceMsgService.connectionByJson(sn, payload);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/config", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result configByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return openApiConfigService.buildDeviceConfigMessage(sn);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/setting", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result settingByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return openApiConfigService.buildDeviceSettingMessage(sn);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/dormancyPlanSetting", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result dormancyPlanSettingByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return new Result<>(deviceDormancyPlanService.buildDeviceDormancyPlan(sn));
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/refreshCredential", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result refreshCredentialByJson(HttpServletRequest httReq, HttpServletResponse httpResp
            , @RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody RefreshCredentialRequest input) {
        input.getValue().setSerialNumber(sn);
        return videoService.refreshCredential(input);
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/{msgName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result requestByJson(HttpServletRequest httReq, HttpServletResponse httpResp
            , @RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @PathVariable(name = "msgName") String msgName, @RequestBody String payload) {
        return deviceMsgService.requestByJson(msgName, sn, payload);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/queryRetainedMsg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result queryRetainedMsgByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String payload) {
        return deviceMsgService.queryRetainedMsgByJson(sn, payload);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/localVideoDeleted", consumes = MediaType.APPLICATION_JSON_VALUE)
    public String localVideoDeletedByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String jsonStr) {
        final PbLocalVideoDeleted input = (PbLocalVideoDeleted) ProtoMsgName.localVideoDeleted.getProtoMsgConvertor().parseJsonStr(jsonStr);
        final PbLocalVideoDeletedResponse resp = videoStoreService.localVideoDeleted(sn, input);
        return ProtoMsgName.localVideoDeleted.getProtoResponseMsgConvertor().printJsonStr(resp);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/syncTimeZoneOffset", consumes = MediaType.APPLICATION_JSON_VALUE)
    public String syncTimeZoneOffsetByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody String jsonStr) {
        final PbSyncTimeZoneOffset input = (PbSyncTimeZoneOffset) ProtoMsgName.syncTimeZoneOffset.getProtoMsgConvertor().parseJsonStr(jsonStr);
        final PbSyncTimeZoneOffsetResponse resp = deviceInfoService.syncTimeZoneOffsetByProto(sn, input);
        return ProtoMsgName.syncTimeZoneOffset.getProtoResponseMsgConvertor().printJsonStr(resp);
    }

    @LogRequestAndResponse(requestLogKeyword = "receive mqtt") // 加上"receive mqtt"的关键字，不影响现有mqtt统计
    @PostMapping(value = "/reportCmdResult", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result reportCmdResultByJson(@RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER) String sn
            , @RequestBody JSONObject ack) {
        ack.fluentPut(IotLocalAndCloudProtocol.FIELD_METHOD, KissMsgMethod.REPORT_CMD_RESULT)
                .fluentPut(IotLocalAndCloudProtocol.FIELD_SENDER_CLIENT_ID, sn);
        kissWsService.sendDeviceMsg(sn, JSON.toJSONString(ack)); // app与设备在同一kiss节点
        return Result.Success();
    }

    @PostMapping(value = "/{msgName}", consumes = CONTENT_TYPE_APPLICATION_PROTOBUF, produces = CONTENT_TYPE_APPLICATION_PROTOBUF)
    public void handleProtoDeviceMsg(HttpServletRequest httReq, HttpServletResponse httpResp
            , @RequestAttribute(name = RequestAttributeKeys.SERIAL_NUMBER, required = false) String sn
            , @PathVariable(name = "msgName") String msgNameStr, @RequestBody byte[] reqBodyBytes) {
        httReq.setAttribute(RequestAttributeKeys.REQ_BODY_BYTES, reqBodyBytes);
        protoMsgService.handleHttpProtoMsg(httReq, httpResp, msgNameStr, (pbMsg, jsonStr) -> {
            ProtoMsgName msgName = ProtoMsgName.msgNameOf(msgNameStr);
            /*
            对于现有消息，把pbMsg对象转成json，用现有的requestHandler处理
            新定义的消息应该直接基于pbMsg对象写处理逻辑，没必要多转换一层。当前lambda里就有pbMsg对象了
            比如：新加xyz消息，就增加一个protoMsgName定义，在这里增加case分支进行处理，返回javaBean或pbResponseMsg对象
             */
            switch (msgName) {
                case httpToken: {
                    return protoDeviceMsgService.httpTokenByJson(sn, jsonStr);
                }
                case status: {
                    /**
                     * json 消息中的dns是 { "statusList": [{"name":"k1","value":"v1"},{"name":"k2","value":"v2"}] }
                     * proto 消息中的dns是 { "statusList": {"k1":"v1","k2":"v2} }
                     */
                    return protoDeviceMsgService.statusByJson(sn, jsonStr);
                }
                case connection: {
                    return protoDeviceMsgService.connectionByJson(sn, jsonStr);
                }
                case config: {
                    return openApiConfigService.buildDeviceConfigMessage(sn);
                }
                case setting: {
                    return openApiConfigService.buildDeviceSettingMessage(sn);
                }
                case dormancyPlanSetting: {
                    return new Result<>(deviceDormancyPlanService.buildDeviceDormancyPlan(sn));
                }
                case refreshCredential: {
                    RefreshCredentialRequest input = JSON.parseObject(jsonStr, RefreshCredentialRequest.class);
                    input.getValue().setSerialNumber(sn);
                    return videoService.refreshCredential(input);
                }
                case localVideoDeleted: {
                    return videoStoreService.localVideoDeleted(sn, (PbLocalVideoDeleted) pbMsg);
                }
                case syncTimeZoneOffset: {
                    return deviceInfoService.syncTimeZoneOffsetByProto(sn, (PbSyncTimeZoneOffset) pbMsg);
                }
                default: {
                    if (msgName.getSource().getAction() != null) {
                        return protoDeviceMsgService.requestByJson(msgName.getSource().getAction(), sn, jsonStr);
                    }
                    return Result.Error(ResultCollection.NOT_PROTO_REQ_HANDLER);
                }
            }
        });
    }

}
