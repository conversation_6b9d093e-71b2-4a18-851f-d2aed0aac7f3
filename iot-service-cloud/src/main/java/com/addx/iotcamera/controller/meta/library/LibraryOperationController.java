package com.addx.iotcamera.controller.meta.library;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.app.LibraryDonateRequest;
import com.addx.iotcamera.bean.app.LibraryStatusTbRequest;
import com.addx.iotcamera.bean.app.RemoveLibraryRequest;
import com.addx.iotcamera.bean.app.questionback.QuestionBackCommitRequest;
import com.addx.iotcamera.bean.domain.UserToken;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.LibraryService;
import com.addx.iotcamera.service.LibraryStatusService;
import com.addx.iotcamera.service.QuestionBackService;
import io.micrometer.core.annotation.Timed;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;


@RestController
@RequestMapping("/library")
public class LibraryOperationController {
    private static Logger LOGGER = LoggerFactory.getLogger(LibraryOperationController.class);

    @SuppressWarnings("all")
    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private LibraryStatusService libraryStatusService;

    @Autowired
    private QuestionBackService questionBackService;

    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/updatemissing", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateLibraryRead(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryStatusTbRequest request,
                             HttpServletRequest httpServletRequest) {
        request.setUserId(userId);

        if(StringUtils.isNotEmpty(request.getTraceIds()) && CollectionUtils.isEmpty(request.getTraceIdList())) {
            request.setTraceIdList(Arrays.asList(request.getTraceIds().split(",")));
        }

        Integer res = libraryStatusService.updateLibraryRead(request);
        return Result.SqlOperationResult(res);
    }

    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/updatemarked", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateLibraryMarked(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryStatusTbRequest request,
                               HttpServletRequest httpServletRequest) {

        request.setUserId(userId);

        if(StringUtils.isNotEmpty(request.getTraceIds()) && CollectionUtils.isEmpty(request.getTraceIdList())) {
            request.setTraceIdList(Arrays.asList(request.getTraceIds().split(",")));
        }

        Integer res = libraryStatusService.updateLibraryMarked(request);
        return Result.SqlOperationResult(res);
    }

    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/deletelibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result deleteLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody RemoveLibraryRequest request,
                         HttpServletRequest httpServletRequest) {


        if (request.getLibraryList() == null && request.getTraceIdList() == null) {
            return ResultCollection.NO_LIBRARY_ACCESS.getResult();
        }

        return new Result(libraryService.deleteLibraries(userId, request));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/saveLibraryDonate", produces = MediaType.APPLICATION_JSON_VALUE)
    Result saveLibraryDonate(@LoginUserToken UserToken userToken, @Valid @RequestBody LibraryDonateRequest request, BindingResult bindingResult,
                             HttpServletRequest httpServletRequest) {

        if (bindingResult.hasErrors()) {
            return Result.Error(INVALID_PARAMS, bindingResult.getFieldError().getDefaultMessage());
        }

        return new Result(libraryService.saveLibraryDonate(request, userToken.getUserId()));
    }

    /**
     * 提交问题反馈
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/commitQuestionBack", produces = MediaType.APPLICATION_JSON_VALUE)
    Result commitQuestionBack(@RequestBody QuestionBackCommitRequest request,
                              @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        return questionBackService.commitQuestionBack(request);
    }
}
