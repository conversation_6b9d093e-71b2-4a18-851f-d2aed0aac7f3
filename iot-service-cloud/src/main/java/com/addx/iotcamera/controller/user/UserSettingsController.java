package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.user.UserMarkRequest;
import com.addx.iotcamera.bean.app.user.UserSettingRequest;
import com.addx.iotcamera.bean.db.user.UserSettingsDO;
import com.addx.iotcamera.bean.domain.UserRoleDO;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.UserRoleEnums;
import com.addx.iotcamera.service.UserRoleService;
import com.addx.iotcamera.service.UserVipService;
import com.addx.iotcamera.service.user.UserSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@Schema(description = "用户设置")
@RestController
@RequestMapping("/usersetting")
public class UserSettingsController {
    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserVipService userVipService;

    @LogRequestAndResponse
    @PostMapping(value = "/queryUserMark", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "查询用户标记")
    public Result queryUserMark(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                @RequestBody UserMarkRequest userMarkRequest,
                             HttpServletRequest httpServletRequest) {
        return new Result(userSettingService.queryUserMark(userId, userMarkRequest.getKey()));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/setUserMark", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "设置用户标记")
    public Result setUserMark(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              @RequestBody UserMarkRequest userMarkRequest,
                              HttpServletRequest httpServletRequest) {
        if(userMarkRequest.getFlag() == null){
            return Result.Error(ResultCollection.INVALID_PARAMS);
        }
        return new Result(userSettingService.setUserMark(userId, userMarkRequest.getKey(), userMarkRequest.getFlag()));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/queryswitch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result querySwitch(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                              HttpServletRequest httpServletRequest) {
        return new Result(userSettingService.queryUserSetting(userId));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/switch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateSwitch(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @Valid @RequestBody  UserSettingRequest request,
                             HttpServletRequest httpServletRequest) {

        UserSettingsDO userSettingsDO = UserSettingsDO.builder()
                .userId(userId)
                .messageMergeSwitch(request.getMessageMergeSwitch())
                .build();
        return new Result(userSettingService.updateUserSetting(userSettingsDO)) ;
    }

    @LogRequestAndResponse
    @PostMapping(value = "/showRedeem", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result showRedeem(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @Valid @RequestBody AppRequestBase request,
                             HttpServletRequest httpServletRequest) {
        return new Result(userSettingService.showRedeem(userId));
    }

}
