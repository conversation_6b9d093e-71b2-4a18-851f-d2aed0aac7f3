package com.addx.iotcamera.controller.manage;

import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.util.DTIMKeyMigrationUtil;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.xml.bind.DatatypeConverter;

/**
 * 开发工具控制器 - 提供数据迁移功能
 */
@RestController
@RequestMapping("/management/dev/migration")
public class DevMigrationController {

    private static final String SECRET_KEY = "addx_iot_migration_2023";

    
    @Resource(name = "businessRedisTemplateClusterClient") 
    private StringRedisTemplate redisTemplate;
    
    /**
     * 验证请求的有效性
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 是否验证通过
     */
    private boolean checkMd5(Long requestTime, String auth) {
        if (requestTime == null || auth == null || auth.isEmpty()) {
            return false;
        }

        
        try {
            // 计算预期的MD5: requestTime + SECRET_KEY
            String content = requestTime + SECRET_KEY;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(content.getBytes());
            String expectedAuth = DatatypeConverter.printHexBinary(digest).toLowerCase();
            
            // 比较提供的auth与预期的auth
            return expectedAuth.equals(auth.toLowerCase());
        } catch (NoSuchAlgorithmException e) {
            return false;
        }
    }

    /**
     * 执行一次批量迁移，每次处理100条数据
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 迁移结果
     */
    @RequestMapping("/dtim/migrate")
    @LogRequestAndResponse
    public Result<Map<String, Object>> migrateDTIMData(Long requestTime, String auth) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        boolean completed = DTIMKeyMigrationUtil.migrateData(redisTemplate);
        
        Map<String, Object> result = new HashMap<>();
        result.put("completed", completed);
        result.put("progress", DTIMKeyMigrationUtil.getSimpleMigrationProgress(redisTemplate));
        
        return new Result<>(result);
    }
    
    /**
     * 执行批量迁移，支持指定批次数量
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @param batchCount 要处理的批次数量，每批100条数据，设置为负数表示处理所有数据直到完成
     * @return 迁移结果
     */
    @RequestMapping("/dtim/migrate/batch")
    @LogRequestAndResponse
    public Result<Map<String, Object>> migrateDTIMDataWithBatch(Long requestTime, String auth, Integer batchCount) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        if (batchCount == null) {
            batchCount = 100; // 默认处理100批，即10,000条数据
        }
        
        boolean completed = DTIMKeyMigrationUtil.migrateData(redisTemplate, batchCount);
        
        Map<String, Object> result = new HashMap<>();
        result.put("completed", completed);
        result.put("progress", DTIMKeyMigrationUtil.getSimpleMigrationProgress(redisTemplate));
        result.put("processedBatches", batchCount > 0 ? Math.min(batchCount, 
                completed ? Integer.MAX_VALUE : batchCount) : "all");
        
        return new Result<>(result);
    }
    
    /**
     * 重置迁移状态
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 操作结果
     */
    @RequestMapping("/dtim/reset")
    @LogRequestAndResponse
    public Result<String> resetMigration(Long requestTime, String auth) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        DTIMKeyMigrationUtil.resetMigration(redisTemplate);
        return new Result<>("迁移状态已重置");
    }
    
    /**
     * 查询迁移进度
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 迁移进度
     */
    @RequestMapping("/dtim/progress")
    @LogRequestAndResponse
    public Result<Map<String, Object>> getMigrationProgress(Long requestTime, String auth) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        Map<String, Object> progress = DTIMKeyMigrationUtil.getMigrationProgress(redisTemplate);
        return new Result<>(progress);
    }

    /**
     * 查询简化的迁移进度（性能更好）
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 简化的迁移进度
     */
    @RequestMapping("/dtim/progress/simple")
    @LogRequestAndResponse
    public Result<Map<String, Object>> getSimpleMigrationProgress(Long requestTime, String auth) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        Map<String, Object> progress = DTIMKeyMigrationUtil.getSimpleMigrationProgress(redisTemplate);
        return new Result<>(progress);
    }

    /**
     * 显示迁移指南
     * @param requestTime 请求时间戳
     * @param auth 认证字符串
     * @return 操作结果
     */
    @RequestMapping("/dtim/guide")
    @LogRequestAndResponse
    public Result<String> showMigrationGuide(Long requestTime, String auth) {
        // 权限检查
        if (!checkMd5(requestTime, auth)) {
            return Result.Error(401, "认证失败");
        }
        
        DTIMKeyMigrationUtil.printMigrationGuide();
        return new Result<>("迁移指南已输出到日志");
    }
} 