package com.addx.iotcamera.controller.xxlJob;

import com.addx.iotcamera.bean.app.device.DeviceSettingRequest;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.publishers.zendesk.ZendeskClient;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.home.DeviceMessageDelayService;
import com.addx.iotcamera.service.openapi.DeviceSettingInitDataService;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.service.user.UserAppScoreService;
import com.addx.iotcamera.service.user.UserSafemoSendEmailService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.service.vip.TwoYearFreTierService;
import com.addx.iotcamera.util.TextUtil;
import com.xxl.job.core.biz.model.ReturnT;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

@RestController
@RequestMapping("/xxl")
public class XxlJobController {
    private static Logger LOGGER = LoggerFactory.getLogger(XxlJobController.class);

    @Autowired
    LibraryService libraryService;
    @Autowired
    MembershipService membershipService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    private UserVipService userVipService;

    @Autowired
    private UserService userService;

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private PaymentService paymentService;
    @Lazy
    @Autowired
    private DeviceSettingInitDataService deviceSettingInitDataService;
    @Autowired
    private UserVipActivateService userVipActivateService;

    @Lazy
    @Autowired
    private RedisService redisService;

    @Autowired
    private UserSafemoSendEmailService userSafemoSendEmailService;

    @Autowired(required = false)
    private ZendeskClient zendeskClient;
    @Resource
    private TwoYearFreTierService twoYearFreTierService;
    @Resource
    private UserAppScoreService userAppScoreService;

    @Resource
    private Device4GService device4GService;

    @Lazy
    @Resource
    private FreeLicenseService freeLicenseService;

    @Autowired
    private LinodeService linodeService;

    @Resource
    private DeviceMessageDelayService deviceMessageDelayService;

    private void runVideoClearTask(Executor executor, Integer startUserId, String whiteUserIds, Consumer<User> handler) {
        if (StringUtils.isNotBlank(whiteUserIds)) {
            for (String userIdStr : TextUtil.splitToNotBlankSet(whiteUserIds, ',')) {
                User user = userService.queryUserById(Integer.valueOf(userIdStr));
                if (user == null) {
                    com.addx.iotcamera.util.LogUtil.error(LOGGER, "runVideoClearTask whiteUserId not exist! userId={}", userIdStr);
                } else {
                    handler.accept(user);
                }
            }
        } else {
            userService.foreachUser(executor, startUserId, handler);
        }
    }

//    @LogRequestAndResponse
//    @PostMapping(value = "/markPersonDetect")
//    public ReturnT<String> markPersonDetect(HttpServletRequest httpServletRequest) {
//        LOGGER.info("Marking markPersonDetect");
//        LOGGER.info("***********execute daily task : markPersonDetect****************");
//        List<UserRoleDO> userRoleDOList = userRoleService.getSerialNumberByRoleId(ADMIN_ROLE_ID);
//        StringBuilder xxlInfoBuilder = new StringBuilder();
//        long time = System.currentTimeMillis();
//        userRoleDOList.forEach(userRoleDO -> {
//            LOGGER.info("markPersonDetect: by userId: {}", userRoleDO.getUserId());
//            //确认是否需要更新设备检测
//            userVipService.switchDeviceMonitor(userRoleDO.getUserId(), time, userRoleDO.getSerialNumber());
//            LOGGER.info("markPersonDetect userId : {}, timestamp : {}", userRoleDO.getUserId(), time);
//        });
//        LOGGER.info("***********end of markPersonDetect****************");
//        return new ReturnT<>(xxlInfoBuilder.toString());
//    }

    @LogRequestAndResponse
    @PostMapping(value = "/libraryLog")
    public ReturnT<String> libraryLog(HttpServletRequest httpServletRequest) {
        LOGGER.info("libraryLog start");
        LOGGER.info("***********execute daily task : libraryLog****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();
        /*
        List<User> userList = userService.selectAll();
        if (!CollectionUtils.isEmpty(userList)) {
            for (User user : userList) {
                LOGGER.info("libraryLog userId:{}", user.getId());
                libraryService.syncLibraryByUser(user);
            }
        }
        */
        // 处理被标记删除的视频，没必要按客户维度处理
        libraryService.syncLibrary(Integer.MAX_VALUE);

        LOGGER.info("***********end of libraryLog****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/protectionMessage")
    public ReturnT<String> protectionMessage(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********execute satrt : protectionMessage****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        userVipService.protectionMessage();

        LOGGER.info("***********end of protectionMessage****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/iosSubOrder")
    public ReturnT<String> iosSubOrder(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********execute satrt : iosSubOrder****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        applePayService.iosSubOrder();

        LOGGER.info("***********end of iosSubOrder****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/initDeviceSetting")
    public ReturnT<String> initDeviceSetting(@RequestBody DeviceSettingRequest request, HttpServletRequest httpServletRequest) throws InterruptedException {
        LOGGER.info("***********execute satrt : initDeviceSetting****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();
//        deviceSettingService.initDeviceSetting(request);
        deviceSettingInitDataService.handleNeedInitData();
        LOGGER.info("***********end of initDeviceSetting****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }


    @PostMapping(value = "/subOrderCheck")
    public ReturnT<String> subOrderCheck(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********execute satrt : subOrderCheck****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        paymentService.verifySubOrderFlow();

        LOGGER.info("***********end of iosSubOrder****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }


    @PostMapping(value = "/orderPaymentFlow")
    public ReturnT<String> orderPaymentFlow(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********execute satrt : orderPaymentFlow****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        paymentService.queryOrderFlowList();

        LOGGER.info("***********end of orderPaymentFlow****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @PostMapping(value = "/momopay/order/verify")
    public ReturnT<String> momoPayOrderVVerify(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********execute satrt : momoPayOrderVVerify****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        paymentService.momoPayOrderVerify();

        LOGGER.info("***********end of momoPayOrderVVerify****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @LogRequestAndResponse
    @PostMapping(value = "/clearRedisCache")
    public ReturnT<String> clearRedisCache(HttpServletRequest httpServletRequest
            , @RequestParam(value = "names", required = false) List<String> names) {
        return new ReturnT<>(redisService.clearRedisCache(names).toJSONString());
    }

    /**
     * xxljob send zendesk new user feedback email
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/executeZendeskNewUserFeedbackEmail")
    public ReturnT<String> executeZendeskNewUserFeedbackEmail(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : executeZendeskNewUserFeedbackEmail****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        if(zendeskClient != null) {
            zendeskClient.executeZendeskNewUserFeedbackEmail();

            zendeskClient.executeAppReportHomeAddCameraFeedbackEmail();
        }

        LOGGER.info("***********end of executeZendeskNewUserFeedbackEmail****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    /**
     * xxljob user vip activate deactivate
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/executeUserVipActivateAndDeactivate")
    public ReturnT<String> executeUserVipActivateAndDeactivate(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : executeUserVipActivateAndDeactivate****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        userVipActivateService.executeUserVipActivateAndDeactivate();

        LOGGER.info("***********end of executeUserVipActivateAndDeactivate****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    /**
     * xxljob user free tier 刷新
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/executeUserDeviceFreeTierCache")
    public ReturnT<String> executeUserDeviceFreeTierCache(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : executeUserDeviceFreeTierCache****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        freeLicenseService.refreshUserDeviceFreeTierCache();

        LOGGER.info("***********end of executeUserDeviceFreeTierCache****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }


    /**
     * xxljob user score moment
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/syncUserScoreMoment")
    public ReturnT<String> syncUserScoreMoment(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : syncUserScoreMoment****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        userAppScoreService.syncStoreUserMoment();

        LOGGER.info("***********end of syncUserScoreMoment****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }


    /**
     * 查询谷歌取消、退款接口
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/googleVoidedPurchases")
    public ReturnT<String> googleVoidedPurchases(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : googleVoidedPurchases****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        paymentService.queryGoogleCancelOrder();

        LOGGER.info("***********end of googleVoidedPurchases****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }


    /**
     * 2年期免费套餐到期提醒
     * @param httpServletRequest
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/twoYearFreeTierExpire")
    @LogRequestAndResponse
    public ReturnT<String> twoYearFreeTierExpire(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("***********execute satrt : twoYearFreeTierExpire****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        twoYearFreTierService.twoYearFreeTierExpire30Day();
        twoYearFreTierService.twoYearFreeTierExpire();
        twoYearFreTierService.twoYearFreeTierExpire180Day();

        LOGGER.info("***********end of twoYearFreeTierExpire****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    /**
     * 定时删除设备端流量统计记录(只保留7天)
     * @param httpServletRequest
     * @return
     */
    @PostMapping(value = "/clearSimTrafficData")
    public ReturnT<String> clearSimTrafficData(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of clearSimTrafficData****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        device4GService.clearSimTrafficData();

        LOGGER.info("***********end of clearSimTrafficData****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @PostMapping(value = "/executeSendEmail4Safemo112APP")
    public ReturnT<String> executeSendEmail4Safemo112APP(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of executeSendEmail4Safemo112APP****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        userSafemoSendEmailService.sendEmail();

        LOGGER.info("***********end of executeSendEmail4Safemo112APP****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    /**
     * 提审账号被删除后自动恢复
     */
    @PostMapping(value = "/reviewAccountCheck")
    public ReturnT<String> reviewAccountCheck(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of reviewAccountCheck****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        userService.reviewAccountCheck();

        LOGGER.info("***********end of reviewAccountCheck****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @PostMapping(value = "/refreshLinodePar")
    public ReturnT<String> refreshLinodePar(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of refreshLinodePar****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        linodeService.refreshPar(true);
        LOGGER.info("***********end of refreshLinodePar****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    @PostMapping(value = "/monitorLinodeQPSAndSize")
    public ReturnT<String> monitorLinodeQPSAndSize(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of monitorLinodeQPSAndSize****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        linodeService.monitorQPSAndSize();
        LOGGER.info("***********end of monitorLinodeQPSAndSize****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }

    /**
     * 延时setting通知
     */
    @PostMapping(value = "/settingDelay")
    public ReturnT<String> settingDelay(HttpServletRequest httpServletRequest) {
        LOGGER.info("***********start of settingDelay****************");
        StringBuilder xxlInfoBuilder = new StringBuilder();

        deviceMessageDelayService.syncDeviceSettingDelay();

        LOGGER.info("***********end of settingDelay****************");
        return new ReturnT<>(xxlInfoBuilder.toString());
    }
}
