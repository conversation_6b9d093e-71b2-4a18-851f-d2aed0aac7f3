package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.bean.app.userorder.BoughtProductInfo;
import com.addx.iotcamera.bean.app.vip.TierServiceInfoRequest;
import com.addx.iotcamera.bean.app.vip.UserOrderSubscriptionCancelRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.UserOrderService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户订单接口
 */
@RestController
@RequestMapping("/user/order")
public class UserOrderController {

    @Autowired
    private UserOrderService userOrderService;

    /**
     * 用户购买历史记录
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/bought/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result userBoughtList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                 @RequestBody TierServiceInfoRequest request,
                                 HttpServletRequest httpServletRequest) {
        BoughtProductInfo boughtProductInfo = userOrderService.getBoughtProductList(request, userId);
        return new Result(boughtProductInfo);
    }


    /**
     * 用户当前生效订阅订单
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/current/subscription/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result userSubBoughtList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    @RequestBody TierServiceInfoRequest request,
                                 HttpServletRequest httpServletRequest) {
        BoughtProductInfo boughtProductInfo = userOrderService.queryUserCurrentSubscriptionOrder(userId,request);
        return new Result(boughtProductInfo);
    }


    /**
     * 取消订阅订单
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/subscription/cancel", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result userOrderSubscriptionCancel(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    @RequestBody UserOrderSubscriptionCancelRequest request,
                                    HttpServletRequest httpServletRequest) {
        userOrderService.userSubscriptionOrderCancel(userId,request);
        return Result.Success();
    }
}
