package com.addx.iotcamera.controller.zendesk;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.config.app.ZendeskConfig;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.util.AesUtil;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.ZendeskUtil;
import com.google.common.collect.ImmutableMap;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.utils.IDUtil;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.Map;

/**
 * 用户使用流程
 * 1、已登录的用户请求 POST /zendesk/user/token 获得identifier
 * 2、用户调用zendesk接口，zendesk请求 POST /zendesk/auth/token 验证用户身份，返回jwt
 */
@Slf4j
@RestController
public class ZendeskController {

    @Autowired
    private UserService userService;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Resource
    private ZendeskConfig zendeskConfig;

    @LogRequestAndResponse
    @PostMapping("/zendesk/auth/token")
    public ResponseEntity<Object> authJwt(@RequestParam("user_token") String token,
                                          HttpServletRequest httpServletRequest) {
        log.info("收到zendesk token : {}", token);
        String jsonContent = AesUtil.decryptAES(token, AesUtil.DEFAULT_KEY);
        TenantUserCache cache = JsonUtil.fromJson(jsonContent, TenantUserCache.class);
        log.info("zendesk验证授权 : {}", cache);

        // 验证token格式
        if (cache == null || cache.getUserId() == null) {
            com.addx.iotcamera.util.LogUtil.warn(log, "非法的zendesk token : {}", token);
            return new ResponseEntity<>("", HttpStatus.UNAUTHORIZED);
        }

        // 验证用户是否存在
        User user = userService.queryUserById(cache.getUserId());
        if (user == null) {
            log.info("zendesk验证异常 userId : {} jsonContent : {}", cache.getUserId(), cache);
            return new ResponseEntity<>("", HttpStatus.UNAUTHORIZED);

        }

        // 验证环境
        if (!activeProfile.equals(cache.zone)) {
            log.info("zendesk验证节点不同 userId : {} jsonContent : {}", cache.getUserId(), cache);
            return new ResponseEntity<>("", HttpStatus.UNAUTHORIZED);
        }

        // 验证邮箱
        String email = ZendeskUtil.generateZendeskEmail(user);
        if (StringUtils.isEmpty(email)) {
            return new ResponseEntity<>("", HttpStatus.UNAUTHORIZED);
        }

//        String tenantId = Optional.ofNullable(user.getTenantId()).orElse("vicoo");
//        String extId = activeProfile + "_" + tenantId + "_" + user.getId();
        Map<String, Object> map = ImmutableMap.of(
                "name", user.getName(),
                "email", email,
                "jti", IDUtil.uuid(),
                "iat", Instant.now().getEpochSecond());
//                "external_id", extId);

        log.info("生成Zendesk jwt参数:" + map.toString());

        ZendeskConfig.ZendeskUser zendeskUser = zendeskConfig.queryZendeskUser(user.getTenantId());
        // 生成jwt字符串
        String jwt = Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setClaims(map)
                .signWith(SignatureAlgorithm.HS256, zendeskUser.getJwtsecret().getBytes())
                .compact();
        return new ResponseEntity<>(ImmutableMap.builder().put("jwt", jwt).build(), HttpStatus.OK);
    }

    @LogRequestAndResponse
    @PostMapping("/zendesk/user/token")
    public Object getIdentifier(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                @RequestBody AppRequestBase request,
                                HttpServletRequest httpServletRequest) {

        String tenantId = request.getApp().getTenantId();
        TenantUserCache cache = new TenantUserCache(activeProfile, tenantId, userId);
        String token = AesUtil.encryptAES(JsonUtil.toJson(cache), AesUtil.DEFAULT_KEY);
        log.info("生成zendesk token : {} {}", cache, token);
        return new Result<>(token);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class TenantUserCache {
        private String zone;
        private String tenantId;
        private Integer userId;
    }
}