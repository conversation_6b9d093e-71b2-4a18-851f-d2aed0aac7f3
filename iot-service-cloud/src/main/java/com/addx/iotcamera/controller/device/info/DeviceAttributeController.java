package com.addx.iotcamera.controller.device.info;

import com.addx.iotcamera.bean.device.attributes.*;
import com.addx.iotcamera.constants.DeviceAttributeName;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceAttributeService;
import com.addx.iotcamera.service.device.DeviceModeService;
import com.addx.iotcamera.service.vip.TierService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.constant.AppConstants;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@Slf4j
@RequestMapping("/device")
@RestController
public class DeviceAttributeController {

    @Autowired
    private DeviceAuthService deviceAuthService;
    @Autowired
    private DeviceAttributeService deviceAttributeService;

    @Autowired
    private VipService vipService;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserService userService;
    @Autowired
    private DeviceModeService deviceModeService;
    @Autowired
    private TierService tierService;

    @LogRequestAndResponse
    @PostMapping(value = "/getCameraAttributes/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAllCameraAttributes(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody DeviceAttributesQuery input) {
        input.setUserId(userId);
        if(input.getSerialNumbers() == null || input.getSerialNumbers().size() ==0){
            return INVALID_PARAMS.getResult();
        }
        return Result.ListResult(input.getSerialNumbers().stream()
                .filter((serialNumber)->deviceAuthService.checkDeviceCommonAccess(userId, serialNumber) == SUCCESS.getCode() )
                .map((serialNumber)->{
                    input.setSerialNumber(serialNumber);
                    return deviceAttributeService.getDeviceAttributes(input).getData();
                })
                .collect(Collectors.toList()));
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getDeviceAttributes/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getAllDeviceAttributes(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody DeviceAttributesQuery input) {
        if (input.getSerialNumbers() == null || input.getSerialNumbers().isEmpty()) {
            return INVALID_PARAMS.getResult();
        }
        return Result.ListResult(input.getSerialNumbers().stream()
                .filter((serialNumber) -> deviceAuthService.checkDeviceCommonAccess(userId, serialNumber) == SUCCESS.getCode())
                .map((serialNumber) -> {
                    input.setSerialNumber(serialNumber);
                    return this.getDeviceAttributes(userId, input).getData();
                })
                .collect(Collectors.toList()));
    }

    @LogRequestAndResponse
    @PostMapping(value = {"/getCameraAttributes", "/getDeviceAttributes"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getDeviceAttributes(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody DeviceAttributesQuery input) {
        input.setUserId(userId);
        // 判断用户与设备是否有关联
        Integer commonUserCheck = deviceAuthService.checkDeviceCommonAccess(userId, input.getSerialNumber());
        if (SUCCESS.getCode() != commonUserCheck) {
            return ResultCollection.getResult(commonUserCheck);
        }
        Result<DeviceAttributes> result = deviceAttributeService.getDeviceAttributes(input);
        if (result.getData() != null && result.getData().getFixedAttributes() != null && !CollectionUtils.isEmpty(result.getData().getFixedAttributes().getSupportJson()))  {
            DeviceAttributes data = result.getData();
            DeviceAttributesJson deviceAttributesJson = new DeviceAttributesJson();
            deviceAttributesJson.setFixedAttributes(new JSONObject());
            deviceAttributesJson.getFixedAttributes().putAll((JSONObject) JSON.toJSON(data.getFixedAttributes()));

            deviceAttributesJson.setSerialNumber(result.getData().getSerialNumber());
            deviceAttributesJson.setModifiableAttributes(data.getModifiableAttributes());
            deviceAttributesJson.setRealTimeAttributes(data.getRealTimeAttributes());

            JSONObject supportJson = result.getData().getFixedAttributes().getSupportJson();
            if (deviceAttributesJson != null && deviceAttributesJson.getFixedAttributes() != null && !CollectionUtils.isEmpty(supportJson)) {
                deviceAttributesJson.getFixedAttributes().putAll(supportJson);
                return new Result(result.getResult(), result.getMsg(), deviceAttributesJson);
            }
        }
        return result;
    }

    @LogRequestAndResponse
    @PostMapping(value = {"/modifyCameraAttributes", "/modifyDeviceAttributes"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result modifyDeviceAttributes(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody DeviceAttributesModify input) {
        int deviceAdminUser = userRoleService.getDeviceAdminUser(input.getSerialNumber());
        if (deviceInfoService.checkIf4GDeviceHasOfficialSimCard(input.getSerialNumber()) &&
                !vipService.isVipDevice(deviceAdminUser, input.getSerialNumber())) {
            return ResultCollection.DEVICE_4G_NO_ACCESS.getResult();
        }
        input.setUserId(userId);
        Integer adminCheck = deviceAuthService.checkAdminAccess(userId, input.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }
        List<DeviceModifiableAttribute> errorAttrs = new LinkedList<>();
        for (DeviceModifiableAttribute attr : input.getModifiableAttributes()) {
            DeviceAttributeName attrName = DeviceAttributeName.nameOf(attr.getName());
            if (attrName == null || attr.getValue() == null) continue;
            if (!attrName.getType().getValueType().isAssignableFrom(attr.getValue().getClass())) {
                errorAttrs.add(attr);
            }
        }
        // Check if enabling smart alert pro on unsupported devices
        for (DeviceModifiableAttribute attr : input.getModifiableAttributes()) {
            if ("enableSmartAlertProSwitch".equals(attr.getName()) && Integer.valueOf(1).equals(attr.getValue())) {
                // Check if device is 4G or bird feeder
                if (deviceInfoService.checkIfDeviceUse4G(input.getSerialNumber()) ||
                    tierService.isBirdDevice(input.getSerialNumber())) {
                    return ResultCollection.OPERATION_NOT_SUPPORTED.getResult();
                }
            }
        }
        if (!errorAttrs.isEmpty()) {
            return new Result(INVALID_PARAMS.getCode(), "请求参数错误!", errorAttrs);
        }

        String tenantId = userService.queryTenantIdById(userId);
        if (AppConstants.TENANTID_SAFEMO.equals(tenantId)) {
            log.info("start to setModeNone, userId:{}", userId);
            deviceModeService.dealWithModeChange(input);
        }
        return deviceAttributeService.modifyDeviceAttributes(input);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/modifyDeviceAttributes/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result modifyDeviceAttributesBatch(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody DeviceAttributesModifies input) {

        Map<String, Integer> data = new HashMap<>();
        for(DeviceAttributesModify modify:input.getAttributes()){
            modify.setUserId(userId);
            data.put(modify.getSerialNumber(), this.modifyDeviceAttributes(userId, modify).getResult());
        }
        return new Result(SUCCESS.getCode(),SUCCESS.getMsg(), data) ;
    }

}
