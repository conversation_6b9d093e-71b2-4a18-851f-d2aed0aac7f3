package com.addx.iotcamera.controller.pay;

import com.addx.iotcamera.bean.app.payment.GooglePublisher;
import com.addx.iotcamera.bean.app.vip.GoogleHistoryRequest;
import com.addx.iotcamera.bean.db.pay.OrderDO;
import com.addx.iotcamera.bean.exception.PayNotifyException;
import com.addx.iotcamera.bean.open.PayNotifyRequest;
import com.addx.iotcamera.constants.ConfigCenterKeyPrefix;
import com.addx.iotcamera.enums.PaymentFlagEnums;
import com.addx.iotcamera.enums.PaymentTypeEnums;
import com.addx.iotcamera.helper.JwtHelper;
import com.addx.iotcamera.service.PaymentService;
import com.addx.iotcamera.service.pay.ApplePayService;
import com.addx.iotcamera.service.pay.GooglePayService;
import com.addx.iotcamera.service.vip.OrderService;
import com.addx.iotcamera.util.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;
import static org.addx.iot.common.enums.ResultCollection.PAY_NOTIFY_ERROE;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payNotify")
@Slf4j
public class PayNotifyController {
    private final static Logger logger = LoggerFactory.getLogger(PayNotifyController.class);

    @Autowired
    private GooglePayService googlePayService;

    @Resource
    private OrderService orderService;
    @Resource
    private ApplePayService applePayService;
    @Autowired
    private PaymentService paymentService;

    @Autowired
    private JwtHelper jwtHelper;

    @Value("${iosPayNotifyRedirectUrl}")
    private String iosPayNotifyRedirectUrl;

    /**
     * 谷歌订阅通知
     *
     * @param pub
     */
    @RequestMapping("/google/publisher")
    @LogRequestAndResponse
    public void googlePublisher(HttpServletRequest request, @RequestBody GooglePublisher pub) throws Exception {
        logger.info("googlePublisher:{}", pub.toString());
        boolean result = false;
        try {
            result = googlePayService.publisherMessage(pub, ConfigCenterKeyPrefix.payNotifyTenantIdVicoo);
        } catch (Exception e) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePublisher exception :{}", e);
            result = false;
        } finally {
            if (!result) {
                throw new PayNotifyException(PAY_NOTIFY_ERROE.getCode(), "谷歌订阅通知错误");
            }
        }
    }

    /**
     * 谷歌订阅通知-soliom
     *
     * @param pub
     */
    @RequestMapping("/google/publisher/{tenantId}")
    @LogRequestAndResponse
    public void googlePublisherSoliom(HttpServletRequest request,
                                      @PathVariable("tenantId") String tenantId,
                                      @RequestBody GooglePublisher pub) throws Exception {
        logger.info("googlePublisher:{}", pub.toString());
        boolean result = false;
        try {
            result = googlePayService.publisherMessage(pub, tenantId);
        } catch (Exception e) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.GOOGLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "googlePublisher exception :{}", e);
            result = false;
        } finally {
            if (!result) {
                throw new PayNotifyException(PAY_NOTIFY_ERROE.getCode(), "谷歌订阅通知错误");
            }
        }
    }

    /**
     * ios订阅通知
     */
    @RequestMapping("/ios/publisher/{tenantId}")
    @LogRequestAndResponse
    public void iosPublisher(HttpServletRequest request, @RequestBody JSONObject pub,
                             @PathVariable("tenantId") String tenantId) throws Exception {
        boolean result = true;
        try {
            logger.debug("iosPublisher {} redirectNum {}", tenantId,pub.containsKey("redirectNum"));
            log.debug("iosPublisher 完整通知内容 {}", JSON.toJSONString(pub));
            String signedPayload = pub.getString("signedPayload");
            //苹果返回的值进行了两层加密首先获取解密第一层下data中的信息在进行解码
            DecodedJWT decode = JWT.decode(signedPayload);
            Map<String, Claim> map =decode.getClaims();
            log.debug("iosPublisher decode map {}",JSON.toJSONString(decode.getClaims()));
            Claim claim = map.get("data");
            Map<String, Object> asMap = claim.asMap();
            //获取到解码后的signedTransactionInfo信息
            String signedTransactionInfo = asMap.get("signedTransactionInfo").toString();
            log.debug("iosPublisher decode asMap {}",JSON.toJSONString(asMap));

            //查询订阅状态
            String notificationType = JWT.decode(signedPayload).getClaim("notificationType").asString();
            log.debug("iosPublisher decode notificationType {}",JSON.toJSONString(JWT.decode(signedPayload)));

            //获取苹果原始交易ID
            String originalTransactionId = JWT.decode(signedTransactionInfo).getClaim("originalTransactionId").asString();
            log.debug("iosPublisher decode signedTransactionInfo {}",JSON.toJSONString(JWT.decode(signedTransactionInfo)));

            logger.info("iosPublisher notificationType {} originalTransactionId {}",notificationType,originalTransactionId);

            List<OrderDO> orderDOList = orderService.queryBytradeNoBatch(originalTransactionId);
            if(CollectionUtils.isEmpty(orderDOList)){
                //订单不在本节点
                if(!pub.containsKey("redirectNum")){
                    //未转发过，需要转发到其他节点
                    pub.put("redirectNum",1);

                    HttpUtils.httpsPost(pub.toJSONString(), iosPayNotifyRedirectUrl + request.getRequestURI()) ;
                }
                return;
            }

            for(OrderDO orderDO : orderDOList){
                applePayService.appleOrderResult(orderDO,notificationType);
            }

        } catch (Exception e) {
            paymentService.sysReportPaymentError(null, PaymentTypeEnums.APPLE.getCode(), PaymentFlagEnums.NOTIFY.getCode());
            com.addx.iotcamera.util.LogUtil.error(logger, "iosPublisher exception :{}", e);
            result = false;
        } finally {
            if (!result) {
                throw new PayNotifyException(PAY_NOTIFY_ERROE.getCode(), "iosPublisher通知错误");
            }
        }
    }


    /**
     * ios订阅通知
     */
    @RequestMapping("/ios/publisher")
    @LogRequestAndResponse
    public void iosPublisher(HttpServletRequest request, @RequestBody JSONObject pub) throws Exception {
        boolean result = true;
        try {
            logger.info("兼容之前的接受，不做处理");
        } catch (Exception e) {
            result = false;
        }
    }

    /**
     * MTN 支付通知
     */
    @RequestMapping("/{tenantId}")
    @LogRequestAndResponse
    public Result momoApiNotify(
                                @Valid @RequestBody PayNotifyRequest request,
                                @RequestHeader(value = "Authorization") String authStr,
                                @PathVariable("tenantId") String tenantId) throws Exception {
        Integer userId;
        try {
            // 这个Authorization 只是为了识别用户id,不作为登录验证
            userId = jwtHelper.getUserId(authStr);
        }catch (Exception e){
            logger.info("获取用户id 错误",e);
            return Result.Error(INVALID_PARAMS,"token does not exist");
        }

        return paymentService.initOrderMomo(userId,request.getProductId(),request.getTransactionId(),tenantId);
    }


    /**
     * 谷歌历史订单信息-free trial
     */
    @RequestMapping("/google/refund")
    @LogRequestAndResponse
    public Result googleGistoryOrderRefund(
            @RequestBody GoogleHistoryRequest request,
            HttpServletRequest httpServletRequest) {
        try {
            orderService.notifyGoogleOrder(request);
        }catch (Exception e){
            com.addx.iotcamera.util.LogUtil.error(logger, "notifyGoogleOrder error",e);
            return Result.Error(INVALID_PARAMS,"param error");
        }

        return Result.Success();
    }
}
