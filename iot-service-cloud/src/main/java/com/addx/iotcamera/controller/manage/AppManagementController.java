package com.addx.iotcamera.controller.manage;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.LoginRequest;
import com.addx.iotcamera.bean.db.ApkInfoDO;
import com.addx.iotcamera.bean.db.app.IosVersionReleaseDO;
import com.addx.iotcamera.config.AppPackageTenantConfig;
import com.addx.iotcamera.service.AppInfoService;
import com.addx.iotcamera.service.tenant.TenantSettingService;
import io.micrometer.core.annotation.Timed;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/management")
public class AppManagementController {
    private static Logger LOGGER = LoggerFactory.getLogger(AppManagementController.class);

    @Autowired
    private AppInfoService appInfoService;

    @Resource
    private AppPackageTenantConfig appPackageTenantConfig;

    @Autowired
    private TenantSettingService tenantSettingService;

    /**
     * 设置安卓最新APK信息
     *
     * @param apkInfoDO
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/setapk", produces = MediaType.APPLICATION_JSON_VALUE)
    Result setLatestApk(@RequestBody ApkInfoDO apkInfoDO, HttpServletRequest httpServletRequest) {
        LOGGER.info("Set latest app info");

        return appInfoService.setLatestApk(apkInfoDO);
    }

    /**
     * 设置IOS最新版本信息
     *
     * @param iosVersionDO
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/setiosversion", produces = MediaType.APPLICATION_JSON_VALUE)
    Result setLatestIosVersion(@RequestBody IosVersionReleaseDO iosVersionDO, HttpServletRequest httpServletRequest) {
        LOGGER.info("Set latest Ios version info");

        return appInfoService.setLatestIosVersion(iosVersionDO);
    }

    /**
     * 获取设备请求节点
     *
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryNode", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryNode(@RequestBody LoginRequest request,
                     HttpServletRequest httpServletRequest) {

        return new Result(appInfoService.queryNode(request));
    }

    /**
     * 获取设备请求节点
     *
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/queryNode/v1", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryNodeV1(@RequestBody LoginRequest request,
                       HttpServletRequest httpServletRequest) {

        request.getApp().setTenantId(appPackageTenantConfig.queryAppTenant(request.getApp().getBundle().replace(".",""),request.getApp().getTenantId()));
        final String trackerUrl = tenantSettingService.getTenantSettingByTenantId(request.getApp().getTenantId()).getTrackerUrl();
        return new Result(appInfoService.queryNodeV1(request).setTrackerUrl(trackerUrl));
    }

    /**
     * 判断是否展示迁移声明
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/verifyTransfer", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result verifyTransfer(@RequestBody AppRequestBase request,
                                 HttpServletRequest httpServletRequest) {
        return new Result(appInfoService.verifyTransfer(request));
    }

    /**
     * 判断是否支持 zendeskChat
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/zendeskChat", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result zendeskChat(@RequestBody AppRequestBase request,
                              HttpServletRequest httpServletRequest) {
        return new Result(appInfoService.queryZendeskSupport(request.getLanguage()));
    }

    /**
     * 获取协议工具生成的的app file // POST METHOD
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/app-files/{tempalteCode}/{fileName}")
    public Result getAppFiles(@PathVariable("tempalteCode") String tempalteCode, @PathVariable("fileName") String fileName, @RequestBody AppRequestBase request,
                              HttpServletRequest httpServletRequest) {
        String requestHost = getRequestHostFromRequest(httpServletRequest);
        return (Result)appInfoService.getOrDownloadAppFile(requestHost, request.getApp().getTenantId(), tempalteCode, request.getLanguage(), fileName, true, false).getBody();
    }

    /**
     * 获取协议工具生成的的app file // GET METHOD
     *
     * @param httpServletRequest
     * @return
     */
    @GetMapping(value = "/app-files/{tenantId}/{tempalteCode}/{tempalteLang}/{fileName}")
    public ResponseEntity getAppFiles(@PathVariable("tenantId") String tenantId, @PathVariable("tempalteCode") String tempalteCode, @PathVariable("tempalteLang") String tempalteLang, @PathVariable("fileName") String fileName, HttpServletRequest httpServletRequest) {
        String requestHost = getRequestHostFromRequest(httpServletRequest);
        LOGGER.info("getAppFiles requestHost {} tenantId {} tempalteCode {} tempalteLang {} fileName {}", requestHost, tenantId, tempalteCode, tempalteLang, fileName);
        return appInfoService.getOrDownloadAppFile(requestHost, tenantId, tempalteCode, tempalteLang, fileName, false, false);
    }
    /**
     * 获取协议工具生成的的app file // GET METHOD
     *
     * @param httpServletRequest
     * @return
     */
    @GetMapping(value = "/app-files/{tenantId}/{tempalteCode}/{tempalteLang}/{fileName}/download")
    public ResponseEntity downloadAppFiles(@PathVariable("tenantId") String tenantId, @PathVariable("tempalteCode") String tempalteCode, @PathVariable("tempalteLang") String tempalteLang, @PathVariable("fileName") String fileName, HttpServletRequest httpServletRequest) {
        String requestHost = getRequestHostFromRequest(httpServletRequest);
        LOGGER.info("downloadAppFiles requestHost {} tenantId {} tempalteCode {} tempalteLang {} fileName {}", requestHost, tenantId, tempalteCode, tempalteLang, fileName);
        return appInfoService.getOrDownloadAppFile(requestHost, tenantId, tempalteCode, tempalteLang, fileName, false, true);
    }

    public String getRequestHostFromRequest(HttpServletRequest httpServletRequest) {
        String requestHost = StringUtils.defaultIfEmpty(httpServletRequest.getHeader("Host"), httpServletRequest.getHeader("host"));
        if(StringUtils.isNotEmpty(requestHost) && requestHost.startsWith("api")) {
            requestHost = requestHost.substring(requestHost.indexOf(".") + 1);
        }
        return requestHost;
    }
}
