package com.addx.iotcamera.controller.pay;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.request.*;
import com.addx.iotcamera.bean.response.AirwallexCardInfoResponse;
import com.addx.iotcamera.bean.response.AirwallexPaymentConsentResponse;
import com.addx.iotcamera.bean.response.SubscribeSwitchInfoResponse;
import com.addx.iotcamera.bean.response.AirwallexPaymentIntentResponse;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.pay.AirwallexPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.locks.LockRegistry;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static com.addx.iotcamera.constants.PayConstants.AIRWALLEX_PAYMENT_USER_;
import static org.addx.iot.common.enums.ResultCollection.INVALID_PARAMS;

/**
 * Airwallex支付控制器
 */
@RestController
@RequestMapping("/apay")
@Api(tags = {"Airwallex支付"})
public class AirwallexPayController {
    private final static Logger logger = LoggerFactory.getLogger(AirwallexPayController.class);
    
    @Autowired
    private AirwallexPayService airwallexPayService;
    
    @Autowired
    private LockRegistry redisLockRegistry;
    
    private static final int REDIS_LOCK_TIMEOUT = 30; // 30秒锁超时时间

    /**
     * 获取绑卡信息
     * 用于前端初始化绑卡组件
     *
     * @param userId 用户ID
     * @return 包含customer_id和client_secret的响应
     */
    @ApiOperation(value = "获取绑卡信息", notes = "获取用于前端初始化绑卡组件的信息")
    @LogRequestAndResponse
    @PostMapping("/getCustomerInfo")
    public Result<AirwallexCardInfoResponse> getCustomerInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        logger.info("获取Airwallex绑卡信息 userId={}", userId);
        try {
            AirwallexCardInfoResponse response = airwallexPayService.getCustomerInfo(userId);

            return new Result(response);
        } catch (Exception e) {
            logger.error("获取绑卡信息 出现异常", e);
            return new Result(new AirwallexCardInfoResponse());
        }
    }
    
    /**
     * 获取支付授权列表
     * 用于展示用户已绑定的支付方式
     *
     * @param userId 用户ID（从请求属性中获取）
     * @return 支付授权列表响应
     */
    @ApiOperation(value = "获取支付授权列表", notes = "获取用户已绑定的支付方式列表")
    @LogRequestAndResponse
    @PostMapping("/getPaymentConsentList")
    public Result<AirwallexPaymentConsentResponse> getPaymentConsentList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                                         @Valid @RequestBody AirwallexCardListRequest request) {
        logger.info("获取Airwallex支付授权列表 userId={}", userId);
        
        AirwallexPaymentConsentResponse response = airwallexPayService.getPaymentConsentList(userId, request);
        
        return new Result(response);
    }
    
    /**
     * 设置默认支付卡
     * 
     * @param userId 用户ID（从请求属性中获取）
     * @param request 支付授权ID请求
     * @return 操作结果
     */
    @ApiOperation(value = "设置默认支付卡", notes = "将指定的支付卡设置为默认卡")
    @LogRequestAndResponse
    @PostMapping("/setDefaultCard")
    public Result setDefaultCard(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, 
                                @RequestBody DefaultCardRequest request) {
        logger.info("设置默认支付卡 userId={}, paymentConsentId={}", userId, request.getPaymentConsentId());
        
        boolean success = airwallexPayService.setDefaultCard(userId, request.getPaymentConsentId());
        
        return Result.Success();
    }
    
    /**
     * 执行支付
     *
     * @param userId 用户ID（从请求属性中获取）
     * @param request 支付请求参数
     * @return 支付结果响应
     */
    @ApiOperation(value = "执行支付")
    @LogRequestAndResponse
    @PostMapping("/makePayment")
    public Result makePayment(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                       @Valid @RequestBody AirwallexPaymentRequest request) {
        logger.info("执行Airwallex支付 userId={}", userId);

        // 使用基于用户维度的分布式锁
        String lockKey = AIRWALLEX_PAYMENT_USER_ + userId;
        Lock lock = redisLockRegistry.obtain(lockKey);

        try {
            // 尝试获取锁，最多等待指定时间
            boolean lockStatus = lock.tryLock(REDIS_LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!lockStatus) {
                logger.warn("Airwallex支付获取用户锁失败，可能存在并发处理 userId={}", userId);
                return Result.Failure("获取锁失败，请稍后重试");
            }

            // 执行支付并添加套餐
            Result result = airwallexPayService.makePaymentAndAddTier(userId, request);
            return result;
            
        } catch (InterruptedException e) {
            logger.error("Airwallex支付获取锁被中断 userId={}", userId, e);
            Thread.currentThread().interrupt();
            return ResultCollection.PAYMENT_FAILED.getNullDataResult();
        } catch (Exception e) {
            logger.error("Airwallex支付处理异常 userId={}", userId, e);
            return ResultCollection.PAYMENT_FAILED.getNullDataResult();
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.warn("释放Airwallex支付用户锁异常 userId={}", userId, e);
            }
        }
    }
    
    /**
     * 获取订阅切换信息
     * 用于计算切换套餐后的总天数和剩余天数
     *
     * @param userId 用户ID（从请求属性中获取）
     * @param request 包含产品ID的请求
     * @return 订阅切换信息响应
     */
    @ApiOperation(value = "获取订阅切换信息", notes = "计算切换套餐后的总天数和剩余天数")
    @LogRequestAndResponse
    @PostMapping("/getSubscribeSwitchInfo")
    public Result<SubscribeSwitchInfoResponse> getSubscribeSwitchInfo(
                @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                @Valid @RequestBody SubscribeSwitchInfoRequest request) {
        logger.info("获取订阅切换信息 userId={}, productId={}", userId, request.getProductId());
        
        SubscribeSwitchInfoResponse response = airwallexPayService.calculateSubscribeSwitchInfo(userId, request.getProductId());
        
        return new Result<>(response);
    }
    
    /**
     * 更新订阅任务的支付授权ID
     *
     * @param userId 用户ID（从请求属性中获取）
     * @param request 更新支付授权ID请求
     * @return 操作结果
     */
    @ApiOperation(value = "更新订阅任务的支付授权ID", notes = "更新指定订单的待处理订阅任务的支付授权ID")
    @LogRequestAndResponse
    @PostMapping("/updateTaskPaymentConsent")
    public Result updateTaskPaymentConsent(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                          @Valid @RequestBody UpdatePaymentConsentRequest request) {
        logger.info("更新订阅任务支付授权ID userId={}, orderId={}, paymentConsentId={}",
                userId, request.getOrderId(), request.getPaymentConsentId());
        
        // 调用服务方法更新支付授权ID
        Result result = airwallexPayService.updateTaskPaymentConsentId(userId, request.getOrderId(), request.getPaymentConsentId());
        
        return result;
    }
    
    /**
     * apple pay 支付方式
     * 创建支付意向
     * 用于获取支付意向和client_secret，前端可以使用此信息进行支付
     *
     * @param userId 用户ID（从请求属性中获取）
     * @param request 支付意向请求参数
     * @return 支付意向响应
     */
    @ApiOperation(value = "创建支付意向", notes = "创建支付意向并返回client_secret")
    @LogRequestAndResponse
    @PostMapping("/createPaymentIntent")
    public Result<AirwallexPaymentIntentResponse> createPaymentIntent(
                @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                @Valid @RequestBody CreatePaymentIntentRequest request) {
        logger.info("创建Airwallex支付意向 userId={}, amount={}, currency={}", 
                userId, request.getAmount(), request.getCurrency());
        
        try {
            // 调用服务创建支付意向
            AirwallexPaymentIntentResponse response = airwallexPayService.createPaymentIntentResponse(
                    userId, request);
            
            return new Result<>(response);
        } catch (Exception e) {
            logger.error("创建支付意向失败", e);
            return Result.Failure(e.getMessage());
        }
    }

    /**
     * apple pay 验证结果
     * 验证支付结果
     * 验证支付意向状态，并处理支付成功后的后续流程
     *
     * @param userId 用户ID（从请求属性中获取）
     * @param request 支付验证请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "验证支付结果", notes = "验证支付意向状态，并处理支付成功后的后续流程")
    @LogRequestAndResponse
    @PostMapping("/verifyPaymentResult")
    public Result verifyPaymentResult(
            @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
            @Valid @RequestBody VerifyPaymentRequest request) {
        logger.info("验证Airwallex支付结果 userId={}, paymentIntentId={}",
                userId, request.getPaymentIntentId());
        // 使用基于用户维度的分布式锁
        String lockKey = AIRWALLEX_PAYMENT_USER_ + userId;
        Lock lock = redisLockRegistry.obtain(lockKey);

        try {
            // 尝试获取锁，最多等待指定时间
            boolean lockStatus = lock.tryLock(REDIS_LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!lockStatus) {
                logger.warn("Airwallex验证支付获取用户锁失败，可能存在并发处理 userId={}", userId);
                return Result.Failure("获取锁失败，请稍后重试");
            }

            // 调用服务验证支付结果
            Result result = airwallexPayService.verifyPaymentResult(userId, request);
            return result;
            
        } catch (InterruptedException e) {
            logger.error("Airwallex验证支付获取锁被中断 userId={}", userId, e);
            Thread.currentThread().interrupt();
            return ResultCollection.VERIFY_PAYMENT_RESULT_FAIL.getNullDataResult();
        } catch (Exception e) {
            logger.error("验证支付结果失败 userId={}", userId, e);
            return ResultCollection.VERIFY_PAYMENT_RESULT_FAIL.getNullDataResult();
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.warn("释放Airwallex验证支付用户锁异常 userId={}", userId, e);
            }
        }
    }

    @ApiOperation(value = "获取公司名称", notes = "获取公司名称")
    @LogRequestAndResponse
    @PostMapping("/getMerchantName")
    public Result getMerchantShortName(
            @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
            @Valid @RequestBody AppRequestBase request) {
        logger.info("getMerchantShortName request={}", request);
        if (request == null || request.getApp() == null || request.getApp().getAppType() == null || request.getApp().getTenantId() == null) {
            return Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        }
        try {
            // 调用服务验证支付结果
            Result result = airwallexPayService.getMerchantShortName(request);
            return result;
        } catch (Exception e) {
            logger.error("获取公司名称结果失败", e.getMessage());
            return Result.Error(ResultCollection.FIND_COMPANY_NAME_FAIL.getCode(),"获取公司名称结果失败");
        }
    }


    @ApiOperation(value = "创建 client_secret", notes = "创建 client_secret")
    @LogRequestAndResponse
    @PostMapping("/createClientSecret")
    public Result createClientSecretInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                         @Valid @RequestBody CreateClientSecretRequest request) {
        logger.info("createClientSecret userId:{} request={}", userId, request);
        if (request == null || userId == null || request.getCustomerId() == null) {
            return Result.Error(INVALID_PARAMS, "INVALID_PARAMS");
        }
        return airwallexPayService.createClientSecretInfo(userId, request);
    }


}