package com.addx.iotcamera.controller.info;

import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.CountryRequest;
import com.addx.iotcamera.bean.app.DescriptionRequest;
import com.addx.iotcamera.service.AppInfoService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/system")
public class AppInfoController {
    private static Logger LOGGER = LoggerFactory.getLogger(AppInfoController.class);

    @Autowired
    private AppInfoService appInfoService;

    /**
     * 获取安卓最新APK信息
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getlatestapp", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getLatestApk(@RequestBody AppRequestBase request, HttpServletRequest httpServletRequest) {
        LOGGER.info(String.format("RequestID: %s | querying for latest apk.", request.getRequestId()));

        return new Result(appInfoService.getLatestApk(request));
    }

    /**
     * 获取IOS最新版本信息
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getlatestiosversion", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getLatestIosVersion(@RequestBody AppRequestBase request, HttpServletRequest httpServletRequest) {
        LOGGER.info(String.format("RequestID: %s | querying for latest Ios version.", request.getRequestId()));

        return new Result(appInfoService.getLatestIosVersion(request));
    }

    /**
     * 获取对应文案接口
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/copywriting/description", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getLatestIosVersion(@RequestBody DescriptionRequest request, HttpServletRequest httpServletRequest) {

        return new Result(appInfoService.getDescription(request));
    }

    /**
     * 获取国家列表
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/country/list", produces = MediaType.APPLICATION_JSON_VALUE)
    Result countryList(@RequestBody AppRequestBase request, HttpServletRequest httpServletRequest) {

        return new Result(appInfoService.queryCountryList(request.getLanguage()));
    }

    /**
     * 获取国家列表
     *
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/country/query", produces = MediaType.APPLICATION_JSON_VALUE)
    Result countryList(@RequestBody CountryRequest request, HttpServletRequest httpServletRequest) {

        return new Result(appInfoService.queryCountry(request));
    }
}