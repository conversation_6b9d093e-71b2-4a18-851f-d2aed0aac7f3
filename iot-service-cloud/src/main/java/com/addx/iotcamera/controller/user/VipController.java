package com.addx.iotcamera.controller.user;

import com.addx.iotcamera.annotation.LoginUserToken;
import com.addx.iotcamera.bean.apollo.ProductExplan;
import com.addx.iotcamera.bean.app.AppRequestBase;
import com.addx.iotcamera.bean.app.UserTierDeviceRequest;
import com.addx.iotcamera.bean.app.additional_tier.AdditionalTierInfo;
import com.addx.iotcamera.bean.app.payment.PaymentRequest;
import com.addx.iotcamera.bean.app.vip.*;
import com.addx.iotcamera.bean.db.ProductDO;
import com.addx.iotcamera.bean.domain.User;
import com.addx.iotcamera.bean.domain.UserToken;
import com.addx.iotcamera.bean.exchangeCode.ExchangeCodeUse;
import com.addx.iotcamera.bean.exchangeCode.getTierInfoForExchangeCode;
import com.addx.iotcamera.bean.response.user.ABTestResult;
import com.addx.iotcamera.bean.response.user.FreeLicenseABTestResult;
import com.addx.iotcamera.bean.response.user.UserTierRemindResponse;
import com.addx.iotcamera.bean.response.vip.DeviceCloudVipInfoResponse;
import com.addx.iotcamera.bean.response.vip.FreeLicenseResponse;
import com.addx.iotcamera.bean.response.vip.VipDiscountAlertResponse;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.pay.TierServiceTypeEnums;
import com.addx.iotcamera.helper.FrequencyChecker;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.abtest.AbTestService;
import com.addx.iotcamera.service.template.UserDiscountAlertService;
import com.addx.iotcamera.service.user.UserDiscountAlertReportService;
import com.addx.iotcamera.service.user.UserDiscountBannerReportService;
import com.addx.iotcamera.service.vip.FreeLicenseService;
import com.addx.iotcamera.service.vip.TierService;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.PayConstants.DEFAULT_FREE_TRAIL_DAY;
import static com.addx.iotcamera.constants.PayConstants.ZERO_FREE_TRAIL_DAY;
import static com.addx.iotcamera.enums.pay.TierServiceTypeEnums.TIER_4G;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/vip")
@Api(tags = "用户vip相关功能API")
public class VipController {
    private final static Logger logger = LoggerFactory.getLogger(VipController.class);
    @Autowired
    private UserVipService userVipService;
    @Autowired
    private VipService vipService;
    @Autowired
    private TierService tierService;
    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ProductService productService;
    @Autowired
    private ProductExchangeCodeService productExchangeCodeService;
    @Autowired
    private UserService userService;
    @Autowired
    private AdditionalUserTierService additionalUserTierService;

    @Autowired
    private UserTierDeviceService userTierDeviceService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Autowired
    private UserDiscountAlertService userDiscountAlertService;

    @Autowired
    private UserDiscountAlertReportService userDiscountAlertReportService;
    @Autowired
    private UserDiscountBannerReportService userDiscountBannerReportService;
    @Autowired
    private FreeLicenseService freeLicenseService;
    @Autowired
    private AbTestService abTestService;

    /**
     * 用户中心-获取用户vip会员信息
     *
     * @return
     * @throws UnsupportedEncodingException
     */
    @LogRequestAndResponse
    @RequestMapping("/user/info")
    public Result queryVipUserInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                   HttpServletRequest httpServletRequest) throws UnsupportedEncodingException {
        UserVipTier userVipTier = userVipService.queryUserVipInfo(userId, request.getLanguage(), request.getApp().getTenantId());
        //App 不需要此数据
        userVipTier.setSubTierListGroup(Maps.newHashMap());
        userVipService.userVipTierName(userVipTier, request.getApp().getApiVersion());
        userVipTier.setSn2VipLevel(vipService.querySn2VipLevel(userId));

        userVipTier.setShouldReminder(userVipService.shouldTierReminder(userId,userVipTier.isTierReceive()));

        //计算推荐商品
        tierService.initRecommendProduct(userVipTier,request);
        userVipTier.setTierNameKey(tierService.initTierNameKey(tierService.queryTierById(userVipTier.getTierId()), request.getApp().getTenantId()));

        // 套餐到期提醒需要限制频率
        userVipService.verifyNeedNotify(userId,userVipTier);

        Map<String, Integer> abFeatureSimpleResultList = userVipService.getAbResultMap(userId, request, userVipTier);
        userVipTier.setAbFeatureSimpleResultList(abFeatureSimpleResultList);
        return new Result(userVipTier);
    }

    @RequestMapping("/tier/list")
    @LogRequestAndResponse
    @Timed
    public Result queryTierList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                HttpServletRequest httpServletRequest) {

        logger.info("User {} query queryTierList.", userId);
        return Result.ListResult(tierService.queryTierList(request.getApp().getTenantId(), request.getLanguage()));
    }

    @RequestMapping("/tier/list/v2")
    @LogRequestAndResponse
    @Timed
    public Result queryTierListV2(@LoginUserToken UserToken userToken, @RequestBody TierListRequest request,
                                  HttpServletRequest httpServletRequest) {
        Integer userId = userToken.getUserId();
        logger.info("User {} query queryTierListV2.", userId);
        return Result.ListResult(tierService.queryTierListV2(request));
    }

    @RequestMapping("/tier/list/v3")
    @LogRequestAndResponse
    @Timed
    public Result queryTierListV3(@LoginUserToken UserToken userToken, @RequestBody TierListRequest request,
                                  HttpServletRequest httpServletRequest) {
        Integer userId = userToken.getUserId();
        logger.info("User {} query queryTierListV3.", userId);
        List<TierInfo> userVipTierList = tierService.queryTierListV2(request);

        // 查询叠加包信息
        List<AdditionalTierInfo> additionalTierInfoList = null;
        try {
            additionalTierInfoList = tierService.queryAdditionalTierList(request, userId);
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(logger, "queryAdditionalTierList failed", e);
        }

        Map<String, Object> tierMap = new HashMap<>();
        tierMap.put("list", userVipTierList);

        tierMap.put("additionalList", tierService.queryAdditionalTierInfoList(userId, request.getApp().getTenantId()));

        //推荐商品id
        tierMap.put("commendProductId",tierService.queryRecommendProductId(userId,request));

        return new Result(tierMap);
    }



    /**
     * 套餐列表v4-- 套餐设备数量限制
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/tier/list/v4")
    @LogRequestAndResponse
    public Result queryTierListV4(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  @RequestBody TierListRequest request,
                                  HttpServletRequest httpServletRequest) {

        Map<String, Object> tierMap = new HashMap<>();

        //套餐类型
        TierServiceTypeEnums tierServiceTypeEnums = TierServiceTypeEnums.codeOf(request.getTierServiceType());

        FreeLicenseABTestResult abResult = abTestService.getFreeLicenseAbResult(userId, request);
        ABTestResult awarenessFreeTrailDayAbResult = abTestService.getAwarenessFreeTrailDayAbResult(userId, request);
        tierMap.put("cloudFreeTrialDay", awarenessFreeTrailDayAbResult.isExperimentSuccessful() ? awarenessFreeTrailDayAbResult.getValue() : DEFAULT_FREE_TRAIL_DAY);
        tierMap.put("tierList", tierService.initTierListV2(tierServiceTypeEnums,request.getApp().getTenantId(), abResult.isNotFreeTrial(), abResult.isNotFreeTrial() ? ZERO_FREE_TRAIL_DAY : awarenessFreeTrailDayAbResult.isExperimentSuccessful()? awarenessFreeTrailDayAbResult.getValue() : DEFAULT_FREE_TRAIL_DAY));
        // 权益对比
        tierMap.put("copywriteDiff", TIER_4G.equals(tierServiceTypeEnums)? new HashMap<String, Object>()  : userVipService.queryTierCopywriteDiff(userId,request.getApp().getTenantId(), request));
        //商品说明
        ProductExplan explan = productService.queryProductExplain(request);
        tierMap.put("productExplain", explan == null ? Lists.newArrayList() : (TIER_4G.equals(tierServiceTypeEnums) ? explan.getFourG() : explan.getSub()));

        //是否领取或者购买过云服务设备
        boolean tierReceive = userVipService.isTierReceived(userId, request.getApp().getTenantId());
        tierMap.put("tierReceive", tierReceive);

        //是否领取或者购买过4G设备
        boolean tierReceive4G = userVipService.isTierReceived(userId, request.getApp().getTenantId(), TierServiceTypeEnums.TIER_4G.getCode());
        tierMap.put("tierReceive4G", tierReceive4G);
        //是否有4G设备
        boolean hasDevice4g = deviceInfoService.hasServiceTypeDevice(userId,TierServiceTypeEnums.TIER_4G);

        //推荐商品id
        tierMap.put("commendProduct",tierService.queryRecommendProductV1(tierReceive,request, userId));
        tierMap.put("commendProduct4G",tierService.queryRecommendProduct4G(request));
        tierService.processProduct4GResult(tierMap, userId, request);

        //推荐商品id-年订阅
        tierMap.put("commendProductYearList", hasDevice4g ? Lists.newArrayList() : tierService.queryRecommendProductYearList(tierReceive,request, userId));

        tierMap.put("hasBirdDevice",tierService.hasSupportBirdDevice(userId,Lists.newArrayList()));
        tierMap.put("deviceTypeList", userVipService.getDeviceTypeList(userId));
        return new Result(tierMap);
    }

    @RequestMapping("/tierproduct/list")
    @LogRequestAndResponse
    public Result queryTierProductList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody TierListRequest request,
                                       HttpServletRequest httpServletRequest) {

        logger.info("User {} query queryTierProductList.", userId);
        return Result.ListResult(tierService.queryTierProductList(request));
    }


    @RequestMapping("/tierproduct/list/new")
    @LogRequestAndResponse
    @Timed
    public Result queryTierProductListNew(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody TierListRequest request,
                                          HttpServletRequest httpServletRequest) {

        logger.info("User {} query queryTierProductList.", userId);
        return new Result(tierService.queryTierProductList( request));
    }

    @RequestMapping("/protection/notify")
    @LogRequestAndResponse
    public Result protectionNotify(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                   HttpServletRequest httpServletRequest) {

        logger.info("User {}  protection/notify.", userId);

        return new Result(userVipService.protectionNotify(String.valueOf(userId)));
    }

    @RequestMapping("/receive")
    @LogRequestAndResponse
    public Result tierReceive(@LoginUserToken UserToken userToken, @RequestBody AppRequestBase requestBase,
                              HttpServletRequest httpServletRequest) throws Exception {
        userVipService.receiveTier(userToken.getUserId(), requestBase);
        return Result.Success();
    }


    @RequestMapping("/product/explain")
    @LogRequestAndResponse
    public Result productExplain(@LoginUserToken UserToken userToken, @RequestBody AppRequestBase requestBase,
                                 HttpServletRequest httpServletRequest) throws Exception {
        return new Result(productService.queryProductExplain(requestBase));
    }

    @Autowired
    @Qualifier("useProductExchangeCodeFrequencyChecker")
    private FrequencyChecker useProductExchangeCodeFrequencyChecker;

    // 使用兑换码
    @RequestMapping("/useProductExchangeCode")
    @LogRequestAndResponse
    public Result useProductExchangeCode(@LoginUserToken UserToken userToken, @RequestBody ExchangeCodeUse body) throws Exception {
        /*
        当短期内尝试过于频繁，则进行以下策略
        5分钟内连续10次失败（无论不正确或过期），则已最后一次起15分钟内禁止继续尝试
        专门的错误码返回：”您已的操作过于频繁，请于15分钟后再试“
        */
        if (useProductExchangeCodeFrequencyChecker.isBan(userToken.getUserId())) {
            return ResultCollection.PRODUCT_EXCHANGE_CODE_USE_FREQUENT.getNullDataResult();
        }
        User user = userService.queryUserById(userToken.getUserId());
        if (user == null) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "用户不存在或已经被禁用");
        }

        Result<ProductDO> result;
        // 根据是否提供序列号和强制标志选择不同的处理方法
        if (!StringUtils.isEmpty(body.getSerialNumber())) {
            // 使用带序列号和强制标志的方法
            result = productExchangeCodeService.useExchangeCode(
                    body.getCode(),
                    userToken.getUserId(),
                    user.getTenantId(),
                    body.getSerialNumber(),
                    body.getForce()
            );
        } else {
            // 使用不带序列号和强制标志的方法
            result = productExchangeCodeService.useExchangeCode(
                    body.getCode(),
                    userToken.getUserId(),
                    user.getTenantId()
            );
        }

        // 如果结果不是成功也不是失败，增加访问次数计数
        if (result.getResult() != Result.successFlag && result.getResult() != Result.failureFlag) {
            useProductExchangeCodeFrequencyChecker.incrementAccessNum(userToken.getUserId());
        }
        if (result.getResult() != Result.successFlag) return result;
        return new Result(result.getData().getId());
    }

    // 获取兑换码
    @RequestMapping("/getTierInfoForExchangeCode")
    @LogRequestAndResponse
    public Result getTierInfoForExchangeCode(@LoginUserToken UserToken userToken, @RequestBody getTierInfoForExchangeCode body) {
        User user = userService.queryUserById(userToken.getUserId());
        return productExchangeCodeService.getTierInfoByProductId(body.getId(), user.getTenantId(), body.getLanguage(), body.getSerialNumber());
    }

    // 免费领取叠加包
    @RequestMapping("/freeReceiveAdditionalTier")
    @LogRequestAndResponse
    public Result freeReceiveAdditionalTier(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody Map freePayInfo) throws Exception {
        String additionalTierUid = String.valueOf(freePayInfo.get("tierUid"));
        Integer additionalTierProductId = Integer.valueOf(freePayInfo.get("productId").toString());
        return additionalUserTierService.addFreeAdditionalTier(userId, additionalTierUid, additionalTierProductId);
    }

    /**
     * 获取套餐设备管理列表
     * @param userId
     * @param requestBase
     * @return
     * @throws Exception
     */
    @RequestMapping("/user/device/list")
    @LogRequestAndResponse
    public Result getUserTierDeviceList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase requestBase) throws Exception {
        List<UserTierDeviceInfo> userTierDeviceInfoList = userTierDeviceService.getManageUserTierDeviceInfoList(requestBase, userId);
        return new Result(userTierDeviceInfoList);
    }

    /**
     * 获取当前套餐设备
     * @param userId
     * @param requestBase
     * @return
     * @throws Exception
     */
    @RequestMapping("/user/device/currentList")
    @LogRequestAndResponse
    public Result getUserCurrentTierDeviceList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase requestBase) throws Exception {
        List<UserTierDeviceInfo> userTierDeviceInfoList = userTierDeviceService.getCurrentTierDeviceInfoList(requestBase, userId);
        if (CollectionUtils.isEmpty(userTierDeviceInfoList)) {
            return Result.Success();
        }

        UserTierDeviceInfo userCurrentTierDeviceInfo = userTierDeviceInfoList.stream().filter(userTierDeviceInfo -> userTierDeviceInfo.getTierId() != null).findAny().orElse(null);
        List<UserTierDeviceInfo> userCurrentAdditionalTierDeviceInfo = userTierDeviceInfoList.stream().filter(userTierDeviceInfo -> StringUtils.isNotEmpty(userTierDeviceInfo.getTierUid())).collect(Collectors.toList());
        Map resultMap = new HashMap();
        resultMap.put("activeDeviceList", userCurrentTierDeviceInfo != null ? userCurrentTierDeviceInfo.getActiveDeviceList() : Collections.emptyList());
        return new Result(resultMap);
    }


    /**
     * 获取当前套餐设备
     * @param userId
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "服务详情")
    @RequestMapping("/user/service/info")
    @LogRequestAndResponse
    public Result queryUserServiceInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       @RequestBody TierServiceInfoRequest request) throws Exception {

        return new Result(userVipService.queryUserCurrentVipInfo(userId,request));
    }


    /**
     * 更新当前套餐回看天数
     * @param userId
     * @param requestBase
     * @return
     * @throws Exception
     */
    @RequestMapping("/user/rolling/day")
    @LogRequestAndResponse
    public Result updateUserServiceRollingDay(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       @RequestBody UserVipRollingDayRequest requestBase) throws Exception {
        userVipService.updateUserVipRollingDay(userId,requestBase);
        return Result.Success();
    }

    /**
     * 更新套餐设备
     * @param userId
     * @param userTierDeviceRequest
     * @return
     * @throws Exception
     */
    @RequestMapping("/user/device/update")
    @LogRequestAndResponse
    public Result updateUserTierDeviceList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UserTierDeviceRequest userTierDeviceRequest) throws Exception {
        userTierDeviceService.updateUserTierDeviceInfo(userId, userTierDeviceRequest);
        return Result.Success();
    }


    /**
     * 套餐权益对比
     * @param userId
     * @param requestBase
     * @return
     * @throws Exception
     */
    @RequestMapping("/tier/copywrite/diff")
    @LogRequestAndResponse
    public Result tierCopywriteDiff(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                           @RequestBody TierListRequest requestBase) throws Exception {
        return new Result<>(userVipService.queryTierCopywriteDiff(userId,requestBase.getApp().getTenantId(), requestBase));
    }



    /**
     * 用户中心-获取用户推荐商品提醒标记
     *
     * @return
     * @throws UnsupportedEncodingException
     */
    @LogRequestAndResponse
    @RequestMapping("/tier/reminder")
    public Result queryUserVipReminder(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                       @RequestBody AppRequestBase request,
                                   HttpServletRequest httpServletRequest) {

        return new Result(userVipService.initUserTierRemindResponse(userId,request));
    }

    @LogRequestAndResponse
    @RequestMapping("/tier/product/list")
    @ApiOperation(value = "/tier/product/list", notes = "用户中心-获取用户推荐商品列表")
    public Result<UserTierRemindResponse> queryUserVipList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                           @RequestBody AppRequestBase request,
                                                           HttpServletRequest httpServletRequest) {

        return new Result<>(userVipService.initUserTierBirdProductListResponse(userId,request));
    }

    /**
     * 用户中心-更新用户推荐商品提醒标记次数
     *
     * @return
     * @throws UnsupportedEncodingException
     */
    @LogRequestAndResponse
    @RequestMapping("/tier/reminder/report")
    public Result updateUserVipReminder(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                        @RequestBody UserTierReminderRequest request,
                                       HttpServletRequest httpServletRequest) {
        userVipService.updateReminderCount(userId,request.getQueryId());
        return Result.Success();
    }


    /**
     * 查询可以购买的设备
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @RequestMapping("/available/purchase/device")
    public Result queryAvailableForPurchaseDevice(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                  @RequestBody PaymentRequest request){
        return new Result<>(paymentService.verifyProductPurchase(userId, request.getProductId(), request.getApp().getTenantId(),request.getRemoveAvailable()));
    }

    /**
     * 获取兑换的鸟类设备
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @RequestMapping("/available/purchase/aiBirdDevice")
    public Result queryAvailableForPurchaseAiBirdDevice(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                        @RequestBody  ExchangeCodeUse exchangeCodeUse){
        return new Result<>(productExchangeCodeService.queryAvailableForPurchaseAiBirdDevice(userId, exchangeCodeUse.getCode()));
    }

    /**
     * 查询设备云服务vip 信息
     * @return
     */
    @LogRequestAndResponse
    @ApiOperation(value = "设备云服务vip信息查询")
    @PostMapping("/device/cloud/info")
    public Result<DeviceCloudVipInfoResponse> queryDeviceCloudVipInfo(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                                     @Valid  @RequestBody DeviceVipInfoRequest request){
        return new Result<>(userVipService.queryDeviceCloudVipInfo(userId,request));
    }


    @LogRequestAndResponse
    @ApiOperation(value = "折扣信息弹窗")
    @PostMapping("/discount")
    public Result<VipDiscountAlertResponse> queryVipDiscountAlert(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                                  @Valid  @RequestBody AppRequestBase request){
        return new Result<>(userDiscountAlertService.queryVipDiscountAlert(userId, request));
    }

    @LogRequestAndResponse
    @ApiOperation(value = "折扣信息弹窗上报成功")
    @PostMapping("/discountAlertShow")
    public Result discountAlertShow(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    @Valid  @RequestBody UserDiscountAlertRequest request){
        userDiscountAlertReportService.updateReport(request.getDiscountAlertId());
        return Result.Success();
    }

    @LogRequestAndResponse
    @ApiOperation(value = "banner弹窗关闭上报")
    @PostMapping("/closeDiscountBanner")
    public Result closeDiscountBanner(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                      @Valid  @RequestBody UserDiscountBannerRequest request){
        userDiscountBannerReportService.updateReport(request.getDiscountBannerId());
        return Result.Success();
    }

    @LogRequestAndResponse
    @ApiOperation(value = "free license页面数据")
    @PostMapping("/tier/freeLicense")
    public Result<FreeLicenseResponse> tierFreeLicense(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                       @Valid  @RequestBody FreeLicenseRequest request){
        FreeLicenseResponse response = freeLicenseService.getFreeLicenseData(userId, request.getDeviceList());
        return new Result<>(response);
    }

    @LogRequestAndResponse
    @ApiOperation(value = "freeLicense兑换")
    @PostMapping("/tier/freeLicenseRedeem")
    public Result tierFreeLicenseRedeem(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                      @Valid  @RequestBody FreeLicenseRedeemRequest request){
        freeLicenseService.redeemFreeLicense(request, userId);
        return Result.Success();
    }

    @LogRequestAndResponse
    @ApiOperation(value = "freeLicense NoThanks上报")
    @PostMapping("/tier/freeLicenseNoThanks")
    public Result tierFreeLicenseNoThanks(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                      @Valid  @RequestBody FreeLicenseRequest request){
        freeLicenseService.reportFreeLicenseNoThanks(request, userId);
        return Result.Success();
    }
}
