package com.addx.iotcamera.controller.inner;

import com.addx.iotcamera.bean.device.SingleDeviceCodecDo;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.DeviceManualDO;
import com.addx.iotcamera.bean.domain.DeviceStateDO;
import com.addx.iotcamera.bean.openapi.OpenApiAccount;
import com.addx.iotcamera.bean.request.CodecProfileRequest;
import com.addx.iotcamera.bean.request.DeactivateDeviceRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.dao.device.DeviceCodecDAO;
import com.addx.iotcamera.enums.DeviceOnlineStatusEnums;
import com.addx.iotcamera.service.BindService;
import com.addx.iotcamera.service.DynamicModelConfigV2Service;
import com.addx.iotcamera.service.StateMachineService;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.DeviceService;
import com.addx.iotcamera.service.device.DeviceSupportService;
import com.addx.iotcamera.service.video.VideoStoreService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.config.entity.CloudDeviceSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static com.addx.iotcamera.service.openapi.OpenApiConfigService.PAAS_OWNED_TENANT_ID;

/**
 * description: 提供给外部的device信息接口
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2023/6/26 15:04
 */
@Slf4j
@RequestMapping("/inner-api/device")
@Controller
public class DeviceController {
    @Setter
    @Autowired
    private DynamicModelConfigV2Service dynamicModelConfigV2Service;

    @Setter
    @Autowired
    private DeviceManualService deviceManualService;

    @Setter
    @Autowired
    private DeviceSupportService deviceSupportService;

    @Setter
    @Autowired
    private DeviceCodecDAO deviceCodecDAO;

    @Resource
    private DeviceService deviceService;

    @Resource
    private BindService bindService;

    @Resource
    private StateMachineService stateMachineService;
    @Resource
    @Lazy
    private VideoStoreService videoStoreService;

    @PostMapping(path = "/sendSettingIncludeCodec")
    @LogRequestAndResponse
    @ResponseBody
    public Result sendSettingIncludeCodec(@RequestBody CodecProfileRequest request , @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account
    ) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        dynamicModelConfigV2Service.sendSettingIncludeCodec(request.getSerialNo(), request.getData());
        return Result.Success();
    }

    @PostMapping(path = "/getDeviceManualByUserSn")
    @LogRequestAndResponse
    @ResponseBody
    public Result getDeviceManualByUserSn(@RequestBody String userNo, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        DeviceManualDO deviceManual = deviceManualService.getDeviceManualByUserSn(userNo);
        Result<DeviceManualDO> result = new Result<>(deviceManual);
        return result;
    }

    @PostMapping(path = "/getDeviceSupport")
    @LogRequestAndResponse
    @ResponseBody
    public Result getDeviceSupport(@RequestBody String serialNumber, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        CloudDeviceSupport cloudDeviceSupport = deviceSupportService.queryDeviceSupportBySn(serialNumber);
        Result<CloudDeviceSupport> result = new Result<>(cloudDeviceSupport);
        return result;
    }

    @PostMapping(path = "/queryDeviceCodec")
    @LogRequestAndResponse
    @ResponseBody
    public Result queryDeviceCodec(@RequestBody String serialNumber, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        List<SingleDeviceCodecDo> list = deviceCodecDAO.queryDeviceCodec(serialNumber);
        Result<List<SingleDeviceCodecDo>> result = new Result<>(list);
        return result;
    }

    @PostMapping(path = "/insertDeviceCodec")
    @LogRequestAndResponse
    @ResponseBody
    public Result insertDeviceCodec(@RequestBody SingleDeviceCodecDo singleDeviceCodecDo, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        boolean result = deviceCodecDAO.insert(singleDeviceCodecDo);
        Result<Boolean> response = new Result<>(result);
        return response;
    }

    @PostMapping(path = "/updateDeviceCodec")
    @LogRequestAndResponse
    @ResponseBody
    public Result updateDeviceCodec(@RequestBody SingleDeviceCodecDo singleDeviceCodecDo, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        boolean result  = deviceCodecDAO.update(singleDeviceCodecDo);
        Result<Boolean> response = new Result<>(result);
        return response;
    }

    @PostMapping(path = "/deactivateDevice")
    @LogRequestAndResponse
    @ResponseBody
    public Result deactivateDevice(@RequestBody DeactivateDeviceRequest request, @RequestAttribute(RequestAttributeKeys.OPEN_API_ACCOUNT) OpenApiAccount account) {
        if (!account.getTenantIds().contains(PAAS_OWNED_TENANT_ID)) {
            return Result.Error(102,"tenantId参数错误:" + account.getTenantIds());
        }
        log.info(String.format("Deactivating device %s from a4x management user %d", request.getSerialNo(), request.getUserId()));
        // 判断设备是否有绑定关系
        DeviceDO storedDevice = deviceService.getAllDeviceInfo(request.getSerialNo());
        // 设备不存在或用户不是实际拥有者或被分享用户
        if (storedDevice == null) {
            return Result.Failure("设备不存在");
        }
        // 设备未被激活
        if (storedDevice.getActivated() == 0) {
            return Result.Failure("设备未激活");
        }
        DeviceStateDO deviceStateDO = stateMachineService.batchGetDeviceState(Arrays.asList(request.getSerialNo())).get(request.getSerialNo());
        log.info(String.format("Deactivating get device {} for status {}", request.getSerialNo(), deviceStateDO));
        // 当前设备在线时
        if (deviceStateDO !=null && DeviceOnlineStatusEnums.OFFLINE != deviceStateDO.getOnlineStatus()) {
            return Result.Error(-2, "当前设备在线");
        }

        // 更新设备相关表
        Result result = bindService.deactivateDeviceBySerialNumber(request.getSerialNo(), BindService.DELETE_DEVICE_REASON_INNER);

        // 重发mqtt config
//        User user = userService.queryUserById(userId);
//        if(user!=null) {
//            String tenantId = OpenApiConfigService.getTenantIdForDeviceConfig(user);
//            openApiConfigService.publishDeviceUnbindConfig(serialNumber, tenantId);
//        }
        return result;
    }
}