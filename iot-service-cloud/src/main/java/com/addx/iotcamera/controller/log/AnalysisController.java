package com.addx.iotcamera.controller.log;

import com.addx.iotcamera.bean.log.GetConfigRequest;
import com.addx.iotcamera.bean.log.PushConfigRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.log.AnalysisService;
import com.addx.iotcamera.util.JsonUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RequestMapping("/log/analysis")
@RestController
@Api(value = "日志分析", tags = "分析分析相关")
public class AnalysisController {

    @Autowired
    private AnalysisService analysisService;

    @ApiOperation(value = "推送config")
    @LogRequestAndResponse
    @PostMapping(value = "/pushConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result pushConfig(@RequestBody PushConfigRequest request) {
        log.info("pushConfig request:{}", JSONObject.toJSONString(request));
        return analysisService.pushConfig(request);
    }

    @ApiOperation(value = "获取config")
    @LogRequestAndResponse
    @PostMapping(value = "/getConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getConfig(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody GetConfigRequest request) {
        return analysisService.getConfig(userId, request);
    }
}
