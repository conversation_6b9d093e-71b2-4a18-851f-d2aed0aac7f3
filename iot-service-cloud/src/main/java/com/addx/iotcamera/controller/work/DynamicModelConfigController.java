package com.addx.iotcamera.controller.work;

import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChanged;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelChangedV2;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfig;
import com.addx.iotcamera.bean.dynamicmodel.DynamicModelConfigV2;
import com.addx.iotcamera.service.DynamicModelConfigService;
import com.addx.iotcamera.service.DynamicModelConfigV2Service;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.vo.Result;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@RequestMapping("/webhooks")
@Controller
public class DynamicModelConfigController {

    @Setter
    @Autowired
    private DynamicModelConfigService dynamicModelConfigService;
    @Setter
    @Autowired
    private DynamicModelConfigV2Service dynamicModelConfigV2Service;
    /**
     * v1文件名
     */
    private final static String V1_FILE_NAME = "dynamic-model-setting.yml";
    /**
     * v2文件名
     */
    private final static String V2_FILE_NAME = "dynamic-model-setting-v2.yml";

    /**
     * @param request
     * @param response
     */
//    @PostMapping(path = "/updateDynamicModelConfig")
    @ResponseBody
    public Result<DynamicModelChanged> updateDynamicModelConfig(MultipartHttpServletRequest request, HttpServletResponse response) {
        Map<String, Map<String, DynamicModelConfig>> rootKey2ModelMap = new LinkedHashMap<>();
        try {
            int partIndex = 0;
            for (Part part : request.getParts()) {
                log.info("updateDynamicModelConfig receivePart! i={},contentType={},name={},fileName={},size={}"
                        , partIndex++, part.getContentType(), part.getName(), part.getSubmittedFileName(), part.getSize());
                if (part.getSize() == 0) continue;
                String name = part.getName();
                String value = IOUtils.toString(part.getInputStream(), "UTF-8");
                Result result = DynamicModelConfigService.parseDynamicModelConfig(name, value, rootKey2ModelMap);
                if (result.getResult() != Result.successFlag) return result;
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfig 读取请求体异常!", e);
            return Result.Failure("updateDynamicModelConfig 读取请求体异常! " + ExceptionUtils.getStackTrace(e));
        }
        Result<DynamicModelChanged> result = dynamicModelConfigService.updateDynamicModelConfig(rootKey2ModelMap);
        if (result.getResult() == Result.successFlag) {
            dynamicModelConfigService.publishDynamicSetting(result.getData(), 1000); // 异步任务
        }
        return result;
    }

    @PostMapping(path = "/updateDynamicModelConfig")
    @ResponseBody
    public Result<DynamicModelChangedV2> updateDynamicModelConfigV2(MultipartHttpServletRequest request, HttpServletResponse response) {
        Result<DynamicModelChanged> v1Result = null;
        Result<DynamicModelChangedV2> v2Result = null;
        try {
            int partIndex = 0;
            for (Part part : request.getParts()) {
                log.info("updateDynamicModelConfigV2 receivePart! i={},contentType={},name={},fileName={},size={}"
                        , partIndex++, part.getContentType(), part.getName(), part.getSubmittedFileName(), part.getSize());
                if (part.getSize() == 0) continue;
                String name = part.getName();
                String value = IOUtils.toString(part.getInputStream(), "UTF-8");
                if (V2_FILE_NAME.equals(name)) {
                    v2Result = updateDynamicModelConfigV2(name, value);
                } else if (V1_FILE_NAME.equals(name)) {
                    v1Result = updateDynamicModelConfig(name, value);
                }
            }
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfigV2 读取请求体异常!", e);
            return Result.Failure("updateDynamicModelConfigV2 读取请求体异常! " + ExceptionUtils.getStackTrace(e));
        }

        // 全部成功才返回成功，否则返回失败,此外也支持只有一个文件的情况
        if ((v1Result != null && v2Result != null) && (v1Result.getResult() == Result.successFlag && v2Result.getResult() == Result.successFlag)) {
            v2Result.getData().merge(v1Result.getData());
            return v2Result;
        } else if (v1Result != null && v1Result.getResult() == Result.successFlag) {
            DynamicModelChangedV2 dynamicModelChangedV2 = new DynamicModelChangedV2();
            dynamicModelChangedV2.merge(v1Result.getData());
            return new Result<>(dynamicModelChangedV2);
        } else if (v2Result != null && v2Result.getResult() == Result.successFlag) {
            return v2Result;
        }else {
            return Result.Failure("无修改文件");
        }
    }

    /**
     * 同步V2文件到数据库
     *
     * @param name  文件名称
     * @param value 文件内容
     * @return
     */
    private Result<DynamicModelChangedV2> updateDynamicModelConfigV2(String name, String value) {
        Map<String, Map<String, DynamicModelConfigV2>> rootKey2ModelMap = new LinkedHashMap<>();
        try {
            Result result = DynamicModelConfigV2Service.parseDynamicModelConfigV2(name, value, rootKey2ModelMap);
            if (result.getResult() != Result.successFlag) return result;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfigV2 读取请求体异常!", e);
            return Result.Failure("updateDynamicModelConfigV2 读取请求体异常! " + ExceptionUtils.getStackTrace(e));
        }
        Result<DynamicModelChangedV2> resultVo = dynamicModelConfigV2Service.updateDynamicModelConfigV2(rootKey2ModelMap);
        if (resultVo.getResult() == Result.successFlag) {
            dynamicModelConfigV2Service.publishDynamicSetting(resultVo.getData(), 1000); // 异步任务
        }
        return resultVo;
    }

    /**
     * 同步V1文件到数据库
     *
     * @param name  文件名称
     * @param value 文件内容
     * @return
     */
    private Result<DynamicModelChanged> updateDynamicModelConfig(String name, String value) {
        Map<String, Map<String, DynamicModelConfig>> rootKey2ModelMap = new LinkedHashMap<>();
        try {
            Result result = DynamicModelConfigService.parseDynamicModelConfig(name, value, rootKey2ModelMap);
            if (result.getResult() != Result.successFlag) return result;
        } catch (Exception e) {
            com.addx.iotcamera.util.LogUtil.error(log, "updateDynamicModelConfigV2 读取请求体异常!", e);
            return Result.Failure("updateDynamicModelConfigV2 读取请求体异常! " + ExceptionUtils.getStackTrace(e));
        }
        Result<DynamicModelChanged> resultVo = dynamicModelConfigService.updateDynamicModelConfig(rootKey2ModelMap);
        if (resultVo.getResult() == Result.successFlag) {
            dynamicModelConfigService.publishDynamicSetting(resultVo.getData(), 1000); // 异步任务
        }
        return resultVo;
    }
}
