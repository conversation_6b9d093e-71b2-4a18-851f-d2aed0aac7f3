package com.addx.iotcamera.controller.meta.library;

import com.addx.iotcamera.bean.app.*;
import com.addx.iotcamera.bean.app.questionback.QuestionBackOptionsRequest;
import com.addx.iotcamera.bean.db.UserLibraryViewDO;
import com.addx.iotcamera.bean.domain.DeviceDO;
import com.addx.iotcamera.bean.domain.UserDeviceLibraryCountDO;
import com.addx.iotcamera.bean.domain.library.LibraryCountDay;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackData;
import com.addx.iotcamera.bean.domain.questionback.QuestionBackOption;
import com.addx.iotcamera.bean.response.library.LibraryEventView;
import com.addx.iotcamera.bean.response.library.QueryLibraryResult;
import com.addx.iotcamera.bean.video.TimeLineLibraryRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.enums.VideoQuestionEnums;
import com.addx.iotcamera.service.*;
import com.addx.iotcamera.service.device.DeviceManualService;
import com.addx.iotcamera.service.device.model.DeviceModelEventService;
import com.addx.iotcamera.service.template.TierFreeUserNotifyService;
import com.addx.iotcamera.service.video.TimeLineLibraryService;
import com.addx.iotcamera.util.FuncUtil;
import com.addx.iotcamera.util.JsonUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSONObject;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.PageResponse;
import org.addx.iot.common.vo.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.HEADER_SAFE_RTC_ROOT_URL;
import static org.addx.iot.common.constant.AppConstants.TENANTID_SAFEMO;

@RestController
@RequestMapping("/library")
public class LibraryQueryController {
    private static Logger LOGGER = LoggerFactory.getLogger(LibraryQueryController.class);

    @Autowired
    private LibraryStatusService libraryStatusService;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DeviceAuthService deviceAuthService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private LibraryService libraryService;

    @Lazy
    @Autowired
    private TimeLineLibraryService timeLineLibraryService;

    @Autowired
    private QuestionBackService questionBackService;

    @Autowired
    private VipService vipService;

    @Autowired
    private VideoSearchService videoSearchService;

    @Resource
    private TierFreeUserNotifyService tierFreeUserNotifyService;
    @Resource
    @Lazy
    private UserRoleService userRoleService;
    @Autowired
    @Lazy
    private DeviceModelEventService deviceModelEventService;
    @Autowired
    @Lazy
    private DeviceManualService deviceManualService;


    /**
     * 获取用户所有设备的录像
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/selectlibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result selectLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                         HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        initLibraryRequestDefaultValue(request);
        List<UserLibraryViewDO> list = libraryService.selectLibrary(request);
        s3Service.presingDeviceLibraryViewDOList(PresignParams.create(request), list);
        Integer total = libraryStatusService.queryLibraryCount(request);
        return Result.PageResult(list, total);
    }

    /**
     * 获取用户所有设备的录像
     *
     * @param userId
     * @param request
     * @return
     */
    @Operation(summary = "获取视频列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功")
    })
    @LogRequestAndResponse(isPrintResponse = false) // 视频列表响应体很大，反复序列号浪费时间
    @Timed
    @PostMapping(value = "/newselectlibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result newSelectLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                            HttpServletRequest httpServletRequest) {
        final String tenantId = Optional.ofNullable(request).map(AppRequestBase::getApp).map(AppInfo::getTenantId).orElse("");
        /*
        if (TENANTID_SAFEMO.equals(tenantId) && request.getVideoTypes() != null && request.getVideoTypes().contains(0)) {
            Result result = selectTimeLineLibrary(userId, request, httpServletRequest);
            if (Result.isSuccess(result)) return result;
        }
        */
        request.setUserId(userId);

        if(StringUtils.isNotEmpty(request.getVideoEventKey()) && ObjectUtils.compare(request.getVideoEvent(), 0L) <= 0) {
            request.setVideoEvent(Long.valueOf(request.getVideoEventKey()));
        }
        initLibraryRequestDefaultValue(request);
        List<UserLibraryViewDO> list = libraryService.selectLibrary(request);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(userLibraryViewDO -> {
                if (userLibraryViewDO != null) {
                    userLibraryViewDO.setId(null);
                    if (!UserLibraryViewDO.getIsShowPeriod(userLibraryViewDO) && !TENANTID_SAFEMO.equals(tenantId)) {
                        userLibraryViewDO.setPeriod(new BigDecimal(-1));
                    }
                }
            });
        }
        s3Service.presingDeviceLibraryViewDOList(PresignParams.create(request), list);
        Integer total = libraryStatusService.queryLibraryCount(request);
        Result<PageResponse<UserLibraryViewDO>> result = Result.PageResult(list, total);
        PageResponse.printLog("newSelectLibrary end!", result.getData(), UserLibraryViewDO::toLogString);
        return result;
    }

    @Operation(summary = "获取时间轴视频列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功")
    })
    @LogRequestAndResponse(isPrintResponse = false) // 视频列表响应体很大，反复序列号浪费时间
    @Timed
    @PostMapping(value = "/selectTimeLineLibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result selectTimeLineLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                                 HttpServletRequest httpServletRequest) {
        final long beginTime = System.currentTimeMillis();
        Result<PageResponse<UserLibraryViewDO>> result = null;
        try {
            /*
                "\t\"startTimestamp\": " + startTimestamp + ",\n" +
                "\t\"endTimestamp\": " + endTimestamp + ",\n" +
                "\t\"serialNumber\": [\"" + sn + "\"],\n" +
                "\t\"videoTypes\": [0, 1],\n" +
                "\t\"hasSliceList\": [0, 1, 2]\n" +
             */
            final String sn = Optional.ofNullable(request.getSerialNumber()).filter(CollectionUtils::isNotEmpty)
                    .map(it -> it.get(0)).filter(StringUtils::isNotBlank).orElse(null);
            if (sn == null) {
                return result = Result.Error(ResultCollection.INVALID_PARAMS, "serialNumber not can be empty!");
            }
            TimeLineLibraryRequest req = new TimeLineLibraryRequest().setUserId(userId).setSerialNumber(sn)
                    .setStartTimestamp(request.getStartTimestamp()).setEndTimestamp(request.getEndTimestamp())
                    .setVideoTypes(request.getVideoTypes()).setHasSliceList(request.getHasSliceList())
                    .setCurrentBindSns(userRoleService.getSerialNumbersByUserId(userId));
            timeLineLibraryService.selectTimeLineLibrary(req);

            if (StringUtils.isNotEmpty(request.getVideoEventKey()) && ObjectUtils.compare(request.getVideoEvent(), 0L) <= 0) {
                request.setVideoEvent(Long.valueOf(request.getVideoEventKey()));
            }
            request.setUserId(userId);
            request.setCurrentBindSns(req.getCurrentBindSns());
            long t1 = System.currentTimeMillis();
            List<UserLibraryViewDO> list = libraryService.selectLibrary(request);
            long costTime1 = System.currentTimeMillis() - t1;
            LOGGER.info("selectTimeLineLibrary selectLibrary! userId={},list={},costTime={}", userId, list.size(), costTime1);
            PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                it.labels(PrometheusMetricsUtil.getHostName(), "selectLibrary", "0").observe(costTime1);
            }));

            String tenantId = Optional.ofNullable(request.getApp()).map(it -> it.getTenantId()).orElse("");
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(userLibraryViewDO -> {
                    if (userLibraryViewDO != null) {
                        userLibraryViewDO.setId(null);
                        if (!UserLibraryViewDO.getIsShowPeriod(userLibraryViewDO) && !TENANTID_SAFEMO.equals(tenantId)) {
                            userLibraryViewDO.setPeriod(new BigDecimal(-1));
                        }
                    }
                });
            }
            long t2 = System.currentTimeMillis();
            s3Service.presingDeviceLibraryViewDOList(PresignParams.create(request), list);
            long costTime2 = System.currentTimeMillis() - t2;
            LOGGER.info("selectTimeLineLibrary presingDeviceLibraryViewDOList! userId={},list={},costTime={}", userId, list.size(), costTime2);
            PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                it.labels(PrometheusMetricsUtil.getHostName(), "presingDeviceLibraryViewDOList", "0").observe(costTime2);
            }));

            long t3 = System.currentTimeMillis();
            Integer total = libraryStatusService.queryLibraryCount(request);
            long costTime3 = System.currentTimeMillis() - t3;
            LOGGER.info("selectTimeLineLibrary queryLibraryCount! userId={},costTime={}", userId, costTime3);
            PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                it.labels(PrometheusMetricsUtil.getHostName(), "queryLibraryCount", "0").observe(costTime3);
            }));

            result = Result.PageResult(list, total);
            PageResponse.printLog("newSelectLibrary end!", result.getData(), UserLibraryViewDO::toLogString);
            return result;
        } catch (Throwable e) {
            LOGGER.error("selectTimeLineLibrary error! userId={}", userId, e);
            return result = Result.Error(ResultCollection.FATAL_ERROR);
        } finally {
            final String stepResult = result != null ? (result.getResult() + "") : "";
            final long costTime = System.currentTimeMillis() - beginTime;
            LOGGER.info("selectTimeLineLibrary finally! userId={},stepResult={},costTime={}", userId, stepResult, costTime);
            PrometheusMetricsUtil.setMetricNotExceptionally(pm -> pm.getTimeLineCostTimHistogramOptional().ifPresent(it -> {
                it.labels(PrometheusMetricsUtil.getHostName(), "total", stepResult).observe(costTime);
            }));
        }
    }

    @Operation(summary = "获取视频切片列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功"),
    })
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/querySliceList", produces = MediaType.APPLICATION_JSON_VALUE)
    Result<PageResponse<UserLibraryViewDO>> querySliceList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                                           @RequestHeader(value = HEADER_SAFE_RTC_ROOT_URL, required = false) String safeRtcRootUrl,
                                                           @RequestBody QuerySliceListVO request) {
        request.setUserId(userId);
        request.setSafeRtcRootUrl(safeRtcRootUrl);
        List<UserLibraryViewDO> list = libraryService.querySliceList(request);
        return Result.PageResult(list, list.size());
    }

    /**
     * 获取用户录像列表-事件分组
     *
     * @param userId
     * @param request
     * @return
     */
    @Deprecated
    @LogRequestAndResponse
    @PostMapping(value = "/selectlibrary/event", produces = MediaType.APPLICATION_JSON_VALUE)
    Result selectLibraryEvent(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                              HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        initLibraryRequestDefaultValue(request);
        return new Result(libraryService.selectLibraryEvent(request));
    }

    /**
     * 获取用户录像列表-事件分组
     *
     * @param userId
     * @param request
     * @return
     */
    @Deprecated
    @SuppressWarnings("This api is just for old app use. New app should use /newselectlibrary/newevent for return videoEventKey")
    @LogRequestAndResponse
    @PostMapping(value = "/newselectlibrary/event", produces = MediaType.APPLICATION_JSON_VALUE)
    Result newSelectLibraryEvent(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                                 HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        initLibraryRequestDefaultValue(request);
        QueryLibraryResult queryLibraryResult = libraryService.selectLibraryEvent(request);
        if (CollectionUtils.isNotEmpty(queryLibraryResult.getList())) {
            queryLibraryResult.getList().forEach(libraryEventView -> {
                if (libraryEventView != null) {
                    libraryEventView.setLibraryIds(null);
                }
            });
        }
        return new Result(queryLibraryResult);
    }

    /**
     * 获取用户录像列表-事件分组
     *
     * @param userId
     * @param request
     * @return
     */
    @Operation(summary = "获取视频事件列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功"),
    })
    @LogRequestAndResponse(isPrintResponse = false) // 视频列表响应体很大，反复序列号浪费时间
    @PostMapping(value = "/newselectlibrary/newevent", produces = MediaType.APPLICATION_JSON_VALUE)
    Result<QueryLibraryResult> newSelectLibraryNewEvent(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                                                        HttpServletRequest httpServletRequest) {
        request.setUserId(userId);

        if(StringUtils.isNotEmpty(request.getVideoEventKey()) && ObjectUtils.compare(request.getVideoEvent(), 0L) <= 0) {
            request.setVideoEvent(Long.valueOf(request.getVideoEventKey()));
        }
        initLibraryRequestDefaultValue(request);
        QueryLibraryResult queryLibraryResult = libraryService.selectLibraryEvent(request);
        Map<String, Set<String>> serialNumberMap = queryLibraryResult.getList().stream().map(LibraryEventView::getSerialNumber).collect(Collectors.toSet()).stream()
                .collect(Collectors.toMap(serialNumber -> serialNumber, serialNumber -> deviceModelEventService.queryRowDeviceModelEvent(deviceManualService.getModelNoBySerialNumber(serialNumber))));

        String tenantId = Optional.ofNullable(request.getApp()).map(it -> it.getTenantId()).orElse("");
        if (CollectionUtils.isNotEmpty(queryLibraryResult.getList())) {
            queryLibraryResult.getList().forEach(libraryEventView -> {
                if (libraryEventView != null) {
                    libraryEventView.setLibraryIds(null);
                    libraryEventView.setVideoEvent(null);
                    libraryEventView.setDeviceAiEventList(serialNumberMap.get(libraryEventView.getSerialNumber()));
                    if (!libraryEventView.getIsShowPeriod() && !TENANTID_SAFEMO.equals(tenantId)) {
                        libraryEventView.setPeriod(new BigDecimal(-1));
                    }
                }
            });
        }
        QueryLibraryResult.printLog("newSelectLibrary newEvent end!", queryLibraryResult);
        return new Result<>(queryLibraryResult);
    }

    private void initLibraryRequestDefaultValue(LibraryRequest request) {
        request.setCurrentBindSns(userRoleService.getSerialNumbersByUserId(request.getUserId()));
        if (request.getVideoTypes() == null) {
            request.setVideoTypes(Arrays.asList(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode())); // 事件聚合列表中只聚合event主画面视频
        }
    }

    /**
     * 返回指定日期录像的数量
     *
     * @param userId
     * @param request
     * @return
     */
    @Operation(summary = "获取每天的视频数")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功"),
    })
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/librarystatus", produces = MediaType.APPLICATION_JSON_VALUE)
    Result<PageResponse<LibraryCountDay>> selectDatePost(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody LibraryRequest request,
                                                         HttpServletRequest httpServletRequest) {

        request.setUserId(userId);
        request.setUse("day");
        initLibraryRequestDefaultValue(request);
        List<LibraryCountDay> source = libraryService.queryLibraryCountDayGroup(request);
        return Result.ListResult(source);
    }

    /**
     * 获取单个录像信息
     *
     * @param userId
     * @param request
     * @return
     */
    @Deprecated
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/selectsinglelibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result selectSingleLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UserLibraryViewRequest request,
                               HttpServletRequest httpServletRequest) {
        UserLibraryViewDO userLibraryView = libraryService.selectSingleLibrary(request.getId(), request.getTraceId(), userId);
        if (userLibraryView == null) {
            return ResultCollection.NO_LIBRARY_ACCESS.getResult();
        }
        s3Service.presingDeviceLibraryViewDO(userLibraryView);
        return new Result(userLibraryView);
    }

    /**
     * 新的获取单个录像信息
     *
     * @param userId
     * @param request
     * @return
     */
    @Operation(summary = "获取单个视频")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "接口访问成功"),
    })
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/newselectsinglelibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result<UserLibraryViewDO> newSelectSingleLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody UserLibraryViewRequest request,
                                                     HttpServletRequest httpServletRequest) {
        UserLibraryViewDO userLibraryView = libraryService.selectSingleLibrary(request.getId(), request.getTraceId(), userId);
        if (userLibraryView == null) {
            return ResultCollection.NO_LIBRARY_ACCESS.getResult();
        }
        userLibraryView.setId(null);
        s3Service.presingDeviceLibraryViewDO(userLibraryView);
        return new Result<>(userLibraryView);
    }

    /**
     * 查询用户都在那些摄像头下有录像
     * 2019-11-25重做解绑逻辑后，现在已经解绑的设备依然可以查到尚未被删除的录像
     *
     * @param userId
     * @param request
     * @return
     */
    @LogRequestAndResponse
    @Timed
    @PostMapping(value = "/getdeviceswithlibrary", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getDevicesWithLibrary(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppRequestBase request,
                                 HttpServletRequest httpServletRequest) {

        LOGGER.info(String.format("User %d is querying for devices with library", userId));
        // 2019-12-03 临时业务回退
        // return new Result(libraryDAO.getDevicesWithLibrary(userId));

        List<DeviceDO> list = deviceInfoService.listDevicesByUserId(userId);
        List<UserDeviceLibraryCountDO> resultList = new ArrayList<UserDeviceLibraryCountDO>();
        for (DeviceDO deviceDO : list) {
            UserDeviceLibraryCountDO countDO = new UserDeviceLibraryCountDO();
            countDO.setSerialNumber(deviceDO.getSerialNumber());
            countDO.setDeviceName(deviceDO.getDeviceName());
            countDO.setUserId(userId);
            countDO.setActivated(1);
            countDO.setUserSn(deviceDO.getUserSn());
            resultList.add(countDO);
        }

        return new Result(resultList);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getLibraryByTraceId")
    Result getLibraryByTraceId(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                               HttpServletRequest httpServletRequest,
                               @RequestBody TraceLibraryRequest request) {


        UserLibraryViewDO view = libraryService.selectSingleLibrary(null, request.getTraceId(), userId);
        LOGGER.debug("getLibraryByTraceId traceId:{} library:{}", request.getTraceId(), JsonUtil.toJson(view));
        if (view != null) {
            s3Service.presingDeviceLibraryViewDO(view);
            return new Result(view);
        }

        return ResultCollection.NO_LIBRARY_ACCESS.getResult();
    }

    /**
     * 视频捐献原因list
     *
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/listDonateReason", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getLibraryDonateReason(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  @RequestBody UserLibraryViewRequest request,
                                  HttpServletRequest httpServletRequest) {

        return new Result(libraryService.queryLibraryDonateList(userId, request.getLanguage(), request.getId().longValue()));
    }

    /**
     * 查询问题反馈选项
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/getQuestionBackOptions", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getQuestionBackOptions(@RequestBody QuestionBackOptionsRequest request,
                                  @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                  HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(request);
        if (result.getResult() != Result.successFlag) return result;
        QuestionBackData data = result.getData();

        List<QuestionBackOption> options = VideoQuestionEnums.getQuestionBackOptions(request.getLanguage(), data.getCheckedCodes());
        QuestionBackOption aiOption = FuncUtil.removeFirst(options, it -> it.getCode() == VideoQuestionEnums.AI_RECOGNITION.getCode());
        data.setPlayOptions(options);
        data.setAiRecognitionTitle(aiOption.getTitle());
        return result;
    }

    @LogRequestAndResponse
    @PostMapping(value = "/getQuestionBackOptionsV2", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getQuestionBackOptionsV2(@RequestBody QuestionBackOptionsRequest request,
                                    @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        Result<QuestionBackData> result = questionBackService.getQuestionBackOptions(request);
        if (result.getResult() != Result.successFlag) return result;
        QuestionBackData data = result.getData();
        List<QuestionBackOption> aiOptions = result.getData().getOptions();

        List<QuestionBackOption> options = VideoQuestionEnums.getQuestionBackOptions(request.getLanguage(), data.getCheckedCodes());
        if (vipService.isVipDevice(userId, data.getSerialNumber())) {
            options.stream().filter(it -> it.getCode() == VideoQuestionEnums.AI_RECOGNITION.getCode())
                    .forEach(it -> it.setChildOptions(aiOptions));
        } else {
            options.removeIf(it -> it.getCode() == VideoQuestionEnums.AI_RECOGNITION.getCode());
        }
        result.getData().setOptions(options);
        return result;
    }
    @LogRequestAndResponse
    @PostMapping(value = "/getQuestionBackOptions/v3", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getQuestionBackOptionsV3(@RequestBody QuestionBackOptionsRequest request,
                                    @RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    HttpServletRequest httpServletRequest) {
        request.setUserId(userId);
        LOGGER.debug("feedbackType:{}", request.getFeedbackType());
        if(request.getFeedbackType() == null || "video".equals(request.getFeedbackType())) {
            return this.getQuestionBackOptionsV2(request, userId, httpServletRequest);
        } else if ("shortSummary".equals(request.getFeedbackType())) {
            return questionBackService.getShortSummaryQuestionBackOptions(request);
        }

        return Result.Error(ResultCollection.INVALID_PARAMS, "feedBackType参数错误!");
    }


    @LogRequestAndResponse
    @PostMapping(value = "/othertagnamelist", produces = MediaType.APPLICATION_JSON_VALUE)
    Result getOtherTagNameList(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                               HttpServletRequest httpServletRequest) {

        Set<String> otherTagNames = libraryService.getOtherQueryLibraryTagNameList(userId);
        return Result.KVResult("otherTagNameList", otherTagNames);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/queryVideoSearchOption", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryVideoSearchOption(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId) {
        return videoSearchService.queryVideoSearchOption(userId);
    }

    @LogRequestAndResponse
    @PostMapping(value = "/queryVideoSearchOptionBySn", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryVideoSearchOptionBySn(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId
            , @RequestBody JSONObject reqBody) {
        final String sn = reqBody.getString("sn");
        if (StringUtils.isBlank(sn)) {
            return Result.Error(ResultCollection.INVALID_PARAMS, "sn参数不能为空!");
        }
        return videoSearchService.queryVideoSearchOption(userId, sn);
    }

    /**
     * 免费用户视频页banner
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/freeuser/banner", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryFreeUserBanner(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                               @RequestBody AppRequestBase request ,
                               HttpServletRequest httpServletRequest) {
        return new Result<>(tierFreeUserNotifyService.verify(userId,request));
    }



    /**
     * 免费用户视频页banner关闭
     * @param userId
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/freeuser/banner/close", produces = MediaType.APPLICATION_JSON_VALUE)
    Result queryFreeUserBannerClose(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                    @RequestBody AppRequestBase request ,
                                    HttpServletRequest httpServletRequest) {
        tierFreeUserNotifyService.updateProtectionPeriod(userId);
        return Result.Success();
    }
}
