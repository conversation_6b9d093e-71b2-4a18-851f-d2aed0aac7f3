package com.addx.iotcamera.controller.device.lifecycle;

import com.addx.iotcamera.bean.app.device.home.DeviceHomeRequest;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.service.device.home.DeviceHomeService;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.vo.Result;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


@RestController
@RequestMapping("/home")
public class DeviceHomeController {

    @Resource
    private DeviceHomeService deviceHomeService;

    /**
     * 新增home
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/addHome", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result addHome(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                                   @Valid @RequestBody DeviceHomeRequest request,
                                   HttpServletRequest httpServletRequest) {
        return new Result<>(deviceHomeService.saveDeviceHome(userId,request));
    }


    /**
     * 查询Home list
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/listHome", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result listHome(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                          @RequestBody DeviceHomeRequest request,
                          HttpServletRequest httpServletRequest) {
        return new Result<>(deviceHomeService.queryUserHomeList(userId,request));
    }


    /**
     * 删除一个Home
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/deleteHome", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deleteHome(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                           @RequestBody DeviceHomeRequest request,
                           HttpServletRequest httpServletRequest) {
        deviceHomeService.deleteUserHome(userId,request.getHomeId());
        return Result.Success();
    }


    /**
     * 更新homeName
     * @param userId
     * @param request
     * @param httpServletRequest
     * @return
     */
    @LogRequestAndResponse
    @PostMapping(value = "/updateHomeName", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateHomeName(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId,
                             @RequestBody DeviceHomeRequest request,
                             HttpServletRequest httpServletRequest) {
        deviceHomeService.updateUserHome(userId,request.getHomeId(),request.getHomeName());
        return Result.Success();
    }
}
