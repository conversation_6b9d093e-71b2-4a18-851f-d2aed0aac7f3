package com.addx.iotcamera.mqtt;

import com.addx.iotcamera.config.MqttConfig;
import com.addx.iotcamera.service.device_msg.MqttResponseHolder;
import com.addx.iotcamera.mqtt.enums.EOutputTopicType;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import com.addx.iotcamera.service.device_msg.KissWsService;
import com.addx.iotcamera.util.LogUtil;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import io.netty.handler.codec.mqtt.MqttQoS;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.addx.iotcamera.service.device_msg.DeviceMsgService.RETAINED_MSG_NAMES;

@Component
@Slf4j
public class MqttSender {

    @Lazy
    @Resource
    private MqttConfig mqttConfig;

    @Autowired
    private DeviceMsgSrcManager deviceMsgSrcManager;
    @Autowired
    private KissWsService kissWsService;

    private static final AtomicBoolean atomicBooleanFlag = new AtomicBoolean(false);

    /**
     * send message
     *
     * @param type
     * @param serialNumber
     * @param data
     */
    public void sendMessage(EOutputTopicType type, String serialNumber, String data) {
        send(type, false, serialNumber, new MqttPayload(data, null));
    }

    public void sendMessage(EOutputTopicType type, String serialNumber, Object data) {
        send(type, false, serialNumber, new MqttPayload(JSON.toJSONString(data), null));
    }

    /**
     * send message
     * @param type
     * @param retained
     * @param serialNumber
     * @param payload
     */
    void send(EOutputTopicType type, boolean retained, String serialNumber, MqttPayload payload) {
        final String strTopic = buildMqttTopic(serialNumber, type.getValue());
        log.info("send mqtt {} {} {}", serialNumber, strTopic, payload.toString());
        final MqttResponseHolder respHolder = MqttResponseHolder.getHolder(type);
        if (respHolder != null) {
            if (!MqttResponseHolder.setResponse(respHolder, serialNumber, payload)) {
                log.error("不匹配responseHolder! holder={},type={},retained={},serialNumber={},payload={}", respHolder, type, retained, serialNumber, payload);
            }
        } else if (deviceMsgSrcManager.isWsReplaceMqtt(serialNumber)) {  // 是否通过kiss-ws发送设备消息
            if (RETAINED_MSG_NAMES.contains(type.getValue())) { // 用ws发送retainedMsg
                kissWsService.sendRetainedMsgToDevice(type.getValue(), serialNumber, payload);
            } else if (type == EOutputTopicType.CMD) { // 用ws发送cmd
                kissWsService.sendCmdToDevice(serialNumber, payload);
            } else if (type == EOutputTopicType.WEBRTC) {
                // ws下发mqtt消息的设备，不需要WEBRTC消息，直接忽略
            } else {
                log.warn("不支持的wsDeviceMsg! type={},serialNumber={},payload={}", type, serialNumber, payload);
            }
        } else {
            send(strTopic, retained, MqttQoS.AT_LEAST_ONCE.value(), payload.toBinary());
        }
    }


    /**
     * send retained message
     * @param type
     * @param serialNumber
     * @param data
     */
    public void sendRetainedMessage(EOutputTopicType type, String serialNumber, String data) {
        send(type, true, serialNumber, new MqttPayload(data, null));
    }

    public void sendRetainedMessage(EOutputTopicType type, String serialNumber, Object data) {
        send(type, true, serialNumber, new MqttPayload(JSON.toJSONString(data), null));
    }

    /**
     * 发布消息
     * @param topic
     * @param retained
     * @param qos
     * @param payload
     */
    private void send(String topic, boolean retained, int qos,  byte[] payload) {
        MqttMessage message = new MqttMessage(payload);
        message.setRetained(retained);
        message.setQos(qos);

        try {
            mqttConfig.getClient().publish(topic, message);
        } catch (MqttException e) {
            log.error( "发布mqtt消息失败 {} , atomicBooleanFlag = {}, " , e.getMessage(), atomicBooleanFlag,  e);
            // 期望当前是false，设置成true
            if(atomicBooleanFlag.compareAndSet(false, true)){
                log.info("遇到问题，重新建立mqtt-client连接");
                mqttConfig.initClient();
                atomicBooleanFlag.set(false);
            }

        }
        PrometheusMetricsUtil.setMetricNotExceptionally(() -> {
            final String type = topic.substring(topic.lastIndexOf('/') + 1);
            PrometheusMetricsUtil.getSendMqttCountOptional().ifPresent(it -> it
                    .labels(PrometheusMetricsUtil.getHostName(), type, retained + "", qos + "").inc());
        });
    }

    public static String buildMqttTopic(String sn, String topicType) {
        return "eaveye/device/" + sn + "/" + topicType;
    }

    // 清空设备sn的所有mqtt-retainedMsg
    public void clearMqttRetainedMsg(String sn) {
        List<String> clearedTopics = new LinkedList<>();
        for (final String topicType : RETAINED_MSG_NAMES) {
            final String topic = buildMqttTopic(sn, topicType);
            try {
                send(topic, true, MqttQoS.AT_LEAST_ONCE.value(), new byte[0]);
                clearedTopics.add(topic);
            } catch (Throwable e) {
                LogUtil.error(log, "clearMqttRetainedMsg error! sn={},topic={}", sn, topic, e);
            }
        }
        log.info("clearMqttRetainedMsg end! sn={},clearedTopics={}", sn, JSON.toJSONString(clearedTopics));
    }

}
