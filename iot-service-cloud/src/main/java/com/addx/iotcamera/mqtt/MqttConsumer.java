package com.addx.iotcamera.mqtt;

import com.addx.iotcamera.bean.device_msg.DeviceMsgSrc;
import com.addx.iotcamera.config.ThreadPoolConfig;
import com.addx.iotcamera.config.ThreadPoolsConfig;
import com.addx.iotcamera.constants.MDCKeys;
import com.addx.iotcamera.mqtt.handler.MqttHandler;
import com.addx.iotcamera.mqtt.handler.extend.*;
import com.addx.iotcamera.service.GlobalLogService;
import com.addx.iotcamera.service.device_msg.DeviceMsgSrcManager;
import com.addx.iotcamera.util.PrometheusMetricsUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.IDUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class MqttConsumer {

    @Setter
    @Autowired
    private GlobalLogService globalLogService;

    @Setter
    @Autowired
    @Lazy
    private DeviceMsgSrcManager deviceMsgSrcManager;

    @Setter
    @Qualifier("mqttConsume")
    @Autowired
    private ThreadPoolTaskExecutor executor;
    private int capacity;

    @Autowired
    public void setThreadPoolsConfig(ThreadPoolsConfig threadPoolsConfig) {
        ThreadPoolConfig config = threadPoolsConfig.getRequiredExecutorConfig("mqttConsume");
        capacity = config.getQueueCapacity();
    }

    private Map<String, MqttHandler> handlerMap = new ConcurrentHashMap<>();

    // 把单例之间的依赖关系显式表达出来
    @Autowired
    public MqttConsumer(
            CmdAckHandler cmdAckHandler,
            ConnectionHandler connectionHandler,
            RequestHandler requestHandler,
            SettingAckHandler settingAckHandler,
            StatusHandler statusHandler,
            WebrtcAckHandler webrtcAckHandler
    ) {
        for (MqttHandler mqttHandler : Arrays.asList(
                cmdAckHandler, connectionHandler, requestHandler
                , settingAckHandler, statusHandler, webrtcAckHandler)) {
            handlerMap.put(mqttHandler.type().getValue(), mqttHandler);
        }
    }

    /**
     * 消费消息
     *
     * @param topic
     * @param payload
     */
    public void consume(String topic, String payload, DeviceMsgSrc deviceMsgSrc) {
        final String requestId = IDUtil.uuid();
        final Identity identity;
        try {
            MDC.put(MDCKeys.REQUEST_ID, requestId); // log中加入requestId
            identity = new Identity(topic);
            MDC.put(MDCKeys.SERIAL_NUMBER, identity.serialNumber);
            log.info("receive mqtt from {} <{}> {} {}", deviceMsgSrc, requestId, topic, payload);

        } catch (Throwable e) {
            com.addx.iotcamera.util.LogUtil.error(log, "receive mqtt from {} <{}> {} {}", deviceMsgSrc, requestId, topic, payload, e);
            return;
        }
        deviceMsgSrcManager.putDeviceMsgSrc(identity.getSerialNumber(), deviceMsgSrc);
        final String msgSrc = Optional.ofNullable(deviceMsgSrc).map(it -> it.name()).orElse("");

        PrometheusMetricsUtil.setMetricNotExceptionally(()-> PrometheusMetricsUtil.getMqttConsumeCountOptional().get().labels(PrometheusMetricsUtil.getHostName(), msgSrc).inc());

        int queueSize = executor.getThreadPoolExecutor().getQueue().size();
        if (queueSize > capacity / 2) {
            com.addx.iotcamera.util.LogUtil.error(log, "MQTT消费能力不足，当前阻塞消息数量为: " + queueSize);
        }

        // 执行任务
        executor.execute(() -> {
            long handleBegin = System.currentTimeMillis();
            try {
                MDC.put(MDCKeys.REQUEST_ID, requestId); // log中加入requestId
                // 设备mqtt请求后端的日志中，把设备型号和版本号都打印出来，用于统计
                globalLogService.setGlobalFields(identity.serialNumber);
                MqttHandler handler = handlerMap.get(identity.action);
                if (handler == null) {
                    com.addx.iotcamera.util.LogUtil.error(log, "找不到type对应的mqttHandler:identity={}", identity);
                } else {
                    handler.process(identity.serialNumber, JSON.parseObject(payload));
                }

                // 尝试获取RequestHandler处理后的 action参数，具体值在RequestHandler里设置
                String requestAction = StringUtils.defaultIfEmpty(MDC.get(MDCKeys.REQUEST_ACTION), "");
                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getMqttHandleCostTimeHistogramOptional().get().labels(PrometheusMetricsUtil.getHostName(), msgSrc, String.valueOf(identity.action), requestAction).observe(System.currentTimeMillis() - handleBegin));
            } catch (Throwable e) {
                com.addx.iotcamera.util.LogUtil.error(log, e.getMessage(), e);

                PrometheusMetricsUtil.setMetricNotExceptionally(() -> PrometheusMetricsUtil.getMqttHandleErrorCountOptional().get().labels(PrometheusMetricsUtil.getHostName(), msgSrc, identity.getAction()).inc());
            } finally {
                PrometheusMetricsUtil.setMetricNotExceptionally(()-> PrometheusMetricsUtil.getMqttHandleCountOptional().get().labels(PrometheusMetricsUtil.getHostName(), msgSrc, identity.getAction()).inc());
                MDC.clear(); // 线程执行完毕，清理所有的log字段
            }
        });
    }

    @Data
    public static class Identity {
        public String action;
        public String serialNumber;

        public Identity(String topic) {
            String[] array = topic.split("/");
            if (array.length < 4) {
                throw new RuntimeException("invalid topic: " + topic);
            }
            serialNumber = array[2].trim();
            action = array[3].trim();
            // 设备可能读取未初始化的内存，展示形式为utf-16的编码形式
            if (serialNumber.getBytes().length != serialNumber.length()) {
                throw new RuntimeException("invalid topic: " + topic);
            }
        }
    }
}
