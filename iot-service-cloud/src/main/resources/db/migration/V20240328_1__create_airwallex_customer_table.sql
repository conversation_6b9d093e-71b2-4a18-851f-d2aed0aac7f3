-- 创建Airwallex客户表，只保留必要的user_id和customer_id对应关系
CREATE TABLE IF NOT EXISTS `camera`.`airwallex_customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `customer_id` varchar(64) NOT NULL COMMENT 'Airwallex客户ID',
  `cdate` int(11) NOT NULL COMMENT '创建时间（Unix时间戳）',
  `mdate` int(11) NOT NULL COMMENT '修改时间（Unix时间戳）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  INDEX `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Airwallex客户信息表';