-- 创建支付授权信息表
CREATE TABLE IF NOT EXISTS `camera`.`payment_consents` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `customer_id` varchar(100) NOT NULL COMMENT 'Airwallex客户ID',
  `payment_consent_id` varchar(100) NOT NULL COMMENT 'Airwallex支付授权ID',
  `payment_method` text COMMENT '支付方式信息JSON',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认支付方式：1-是，0-否',
  `cdate` int NOT NULL COMMENT '创建时间',
  `mdate` int NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_consent_id` (`payment_consent_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付授权信息表'; 