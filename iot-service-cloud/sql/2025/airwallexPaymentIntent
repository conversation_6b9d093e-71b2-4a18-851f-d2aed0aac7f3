CREATE TABLE camera.`airwallex_payment_intent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `payment_Intent_id` varchar(100) DEFAULT NULL COMMENT '支付意向id',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
  `currency` varchar(45) DEFAULT NULL COMMENT '币种',
  `merchant_order_id` varchar(100) DEFAULT NULL COMMENT '商户订单id',
  `customer_id` varchar(100) DEFAULT NULL COMMENT '客户id',
  `status` varchar(45) DEFAULT NULL COMMENT '支付意向状态',
  `created_at` varchar(45) DEFAULT NULL COMMENT '创建时间',
  `original_amount` decimal(10,2) DEFAULT NULL,
  `original_currency` varchar(45) DEFAULT NULL,
  `tier_device` text COMMENT '指定生效的设备',
  `product_id` int(11) DEFAULT '0' COMMENT '购买的套餐商品id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单Id',
  `user_id` int(11) DEFAULT '0' COMMENT '用户Id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payment_intent` (`payment_Intent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


ALTER TABLE `camera`.`airwallex_payment_intent`
ADD COLUMN `payment_consent_id` VARCHAR(100) NULL COMMENT '支付授权id' ;





ALTER TABLE `camera`.`subscription_payment_tasks`
ADD COLUMN `trade_no` VARCHAR(100) NULL COMMENT '当前订单号' ,
ADD COLUMN `success_time` INT NULL DEFAULT 0 COMMENT '续订成功时间' ;


ALTER TABLE `camera`.`airwallex_payment_intent`
ADD COLUMN `subscription_task_id` BIGINT(20) NULL DEFAULT 0 COMMENT '续订task id' ,
ADD INDEX `idx_task_id` (`subscription_task_id` ASC);

ALTER TABLE `camera`.`payment_flow` ADD COLUMN `merchant_order_id` VARCHAR(100) NULL COMMENT '商户订单id' ;
