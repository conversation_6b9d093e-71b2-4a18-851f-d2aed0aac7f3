
CREATE TABLE camera.user_agree (
                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT  COMMENT 'id',
                                   `user_id` int(11) NOT NULL DEFAULT 0  COMMENT'用户id',
                                   `agree_type` tinyint(4) NOT NULL DEFAULT 0  COMMENT '同意的类型 1 safemo登陆协议，2 埋点开关',
                                   `agree_path` tinyint(4) NOT NULL DEFAULT 0 COMMENT '同意路径 1 app',
                                   `is_agree` tinyint(4) NOT NULL DEFAULT 0 COMMENT '1 同意 0 不同意',
                                   `version` varchar(64) NOT NULL DEFAULT '0'  COMMENT '同意的版本号',
                                   `create_timestamp` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '创建时间',
                                   `update_timestamp` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '修改时间',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `uniq_user_id_type` (`user_id`, `agree_type`)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '用户同意表'

CREATE TABLE camera.user_agree_water (
                                         `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT'id',
                                         `user_agree_id` int(11) NOT NULL DEFAULT 0 COMMENT'user_agree_id',
                                         `user_id` int(11) NOT NULL DEFAULT 0 COMMENT'用户id',
                                         `is_agree` tinyint(4) NOT NULL DEFAULT 0 COMMENT '1 同意 0 不同意',
                                         `agree_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT'同意的类型 1 safemo登陆协议，2 埋点开关',
                                         `agree_path` tinyint(4) NOT NULL DEFAULT 0 COMMENT'同意路径 1 app',
                                         `version` varchar(64) NOT NULL DEFAULT '0' COMMENT '同意的版本号',
                                         `create_timestamp` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
                                         `update_timestamp` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '修改时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_user_agree_id` (`user_agree_id`),
                                         KEY `idx_user_id_type` (`user_id`, `agree_type`)
)  AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '用户同意流水表'