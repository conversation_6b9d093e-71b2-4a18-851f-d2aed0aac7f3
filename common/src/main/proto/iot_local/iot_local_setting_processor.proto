syntax = "proto3";
import "common.proto";
option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.iot_local";
option java_outer_classname = "IotLocalSettingProcessorProto";


service IotLocalSettingProcessor {
    rpc statusReport (StatusRequest) returns (SendResult) {}
    rpc supportReport (DeviceSupportRequest) returns (SendResult) {}
    rpc formatSSDReport (FormatSSDStatusRequest) returns (SendResult) {}
    rpc queryAbility (AbilityRequest) returns (AbilityResult) {}
    rpc queryStatus (StatusQueryRequest) returns (StatusResult) {}
    rpc changeMode (SecurityModeRequest) returns (SendResult) {}
    rpc getSignSecret(SignSecretRequest) returns (SignSecretResult) {}
    rpc commonConfigReport (IotLocalCommonConfigReportRequest) returns (SendResult) {}
}

message SignSecretResult{
    optional int32 code = 1; // code=0表示调用成功
    optional string message = 2;  // 错误信息
    string secret = 3;   // 秘钥
}

message SignSecretRequest{
    string requestId=1;
}

message IotLocalCommonConfigReportRequest{
  optional string endPoint = 1; // 埋点上报域名
  optional string namespace = 2; // 传空或者空串 iot_local走自己的namespace，否则使用传的namespace
  optional int32 scene = 3; //1 服务启动初始化 2 绑定 3 开启沙盒 4 关闭沙盒
}

message SecurityModeRequest {
    string serialNumber = 1;
    string requestId = 2;
    string modeName = 3;

}

message AbilityRequest {
    string serialNumber = 1;
    string requestId = 2;
}

message StatusQueryRequest {
    string serialNumber = 1;
    string requestId = 2;
}



message AbilityResult {
    int32 code = 1;
    string data = 2;
}


message StatusResult {
    int32 code = 1;
    string data = 2;
}



message FormatSSDStatusRequest {
    string serialNumber = 1;
    //同下发的id
    string requestId = 2;
    //0 成功 ，其他失败
    int32 code = 3;
    //失败原因
    string msg= 4;
    string ssdTotal = 5;
    string ssdUsed = 6;
    int32 ssdStatus = 7;
}

message DeviceSupportRequest {
    string serialNumber = 1;
    string language = 2;
    string countryNo = 3;
    string requestId = 4;
    int32 supportAlarm = 5;
    int32 supportVoiceLanguage= 6;
    int32 supportVoiceVolume = 7;
    string currentVersion = 8;
    string deviceSupportLanguage = 9;
    //设备支持的ai事件
    string supportAiEvent = 10;
    string securityMode = 11; // 逗号分割，小写字母 ， 例如： home,away,disalarmed
    int32 supportAlarmFlashLight = 12; // 0 不支持，1 支持

    optional bool supportOverallLightSwitch = 13; // 是否支持总体灯光开关
    optional IntRange overallLightRange = 14; // 总体灯光强度范围
    optional bool supportEventAnalytics = 15; // 是否支持埋点开关

    optional string socModel = 16;  // 基站SOC型号
    optional string wifiModel = 17; // 基站WiFi型号
}

message IntRange {
    int32 min = 1;
    int32 max = 2;
    int32 interval = 3;
}

message StatusRequest {
    string serialNumber = 1;
    string language = 2;
    string countryNo = 3;
    string requestId = 4;
    int32 batteryLevel = 5;
    //设备实时联网方式。0-WIFI,1-有线
    int32 deviceNetType = 6;
    string storageTotal = 7;
    string storageSystem = 8;
    string storageData = 9;
    string storageReservered = 18; // bstationd预留空间大小
    string ssdTotal = 10;
    string ssdUsed = 11;
    //wifi强度
    int32 signalStrength = 12;
    //当前wifi名称
    string networkName = 13;
    string ip = 14;
    int32 wifiChannel = 15;
    int32 ssdStatus = 16;
    string ssdPath = 17;
}