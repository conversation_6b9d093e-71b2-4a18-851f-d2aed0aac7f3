syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbStatusProto";

// h2接口：/deviceMsg/status
message PbStatus {

  // path=["status","statusList","battery"] , path[2]=statusList
  optional PbStatusList statusList = 1;
  // path=["status","time"] , path[2]=time , types=["java.lang.Integer"] , values=["1732802597","1732802599","1732802591","1732802595","1732802598","1732802606","1732802607","1732802589","1732802596","1732802600"]
  optional int32 time = 2;

  message PbStatusList {

    // path=["status","statusList","battery"] , path[3]=battery , types=["java.lang.Integer"] , values=["100","99","1","97","98","96","95","94","89","93"]
    optional int32 battery = 1;
    // path=["status","statusList","mcc"] , path[3]=mcc , types=["java.lang.String"] , values=["232","460","708","502","310"]
    optional string mcc = 2;
    // path=["status","statusList","downloadBandWidth"] , path[3]=downloadBandWidth , types=["java.lang.Integer"] , values=["3","2","5","4"]
    optional int32 downloadBandWidth = 3;
    // path=["status","statusList","algorithmVoltage"] , path[3]=algorithmVoltage , types=["java.lang.Integer"] , values=["100","97","1","99","98","96","89","95","93","94"]
    optional int32 algorithmVoltage = 4;
    // path=["status","statusList","wifiProtocolMode"] , path[3]=wifiProtocolMode , types=["java.lang.Integer"] , values=["0","6","1"]
    optional int32 wifiProtocolMode = 5;
    // path=["status","statusList","ip"] , path[3]=ip , types=["java.lang.String"] , values=["***********","***********","***********","***********","***********","***********","***********","*************","*************","***********"]
    optional string ip = 6;
    // path=["status","statusList","mnc"] , path[3]=mnc , types=["java.lang.String"] , values=["01","11","02","12","41","28"]
    optional string mnc = 7;
    // path=["status","statusList","alarmWhenRemoveToggleOn"] , path[3]=alarmWhenRemoveToggleOn , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 alarmWhenRemoveToggleOn = 8;
    // path=["status","statusList","[]","value","free"] , path[4]=value
    // PbValue value = 9;
    // path=["status","statusList","uploadBandWidth"] , path[3]=uploadBandWidth , types=["java.lang.Integer"] , values=["3","2","5","4"]
    optional int32 uploadBandWidth = 10;
    // path=["status","statusList","sdCardStatus","formatStatus"] , path[3]=sdCardStatus
    optional PbSdCardStatus sdCardStatus = 11;
    // path=["status","statusList","iccid"] , path[3]=iccid , types=["java.lang.String"] , values=["89430101524057323214","89860322492006047006","89430101524057388738","89430101524057386146","89430101524057293755","8950402704555529907F","89430101524057290041","89430101524057292039","89430101524057291775","89430101524057291825"]
    optional string iccid = 12;
    // path=["status","statusList","liveAudioToggleOn"] , path[3]=liveAudioToggleOn , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 liveAudioToggleOn = 13;
    // path=["status","statusList","rssiTotal"] , path[3]=rssiTotal , types=["java.lang.Integer"] , values=["-49","-36","-61","-60","-48","-50","-68","-67","-62","-64"]
    optional int32 rssiTotal = 14;
    // path=["status","statusList","batteryRemoval"] , path[3]=batteryRemoval , types=["java.lang.Integer"] , values=["1"]
    optional int32 batteryRemoval = 15;
    // path=["status","statusList","apn"] , path[3]=apn , types=["java.lang.String"] , values=["linksnet","","unet","broadband"]
    optional string apn = 16;
    // path=["status","statusList","pcid"] , path[3]=pcid , types=["java.lang.Integer"] , values=["5230","1125","2560","2452","2460","66536","67086","975","2499","66986"]
    optional int32 pcid = 17;
    // path=["status","statusList","[]","otaRequest"] , path[4]=otaRequest , types=["java.lang.Integer"] , values=["0"]
    optional int32 otaRequest = 18;
    // path=["status","statusList","userSn"] , path[3]=userSn , types=["java.lang.String"] , values=["AICXJE2H87E3758","AICUKX6GFCR0384","AICLQ7BZH2L5264","AICH882VSYU3921","AIC5DLAHBRL4737","AICLQ7BZH2L3579","AICFF63ZN6B3693","AICFF63ZN6B2764","AICNRRR26YZ1004","AICD1V3XW7M6494"]
    optional string userSn = 19;
    // path=["status","statusList","adcVoltage"] , path[3]=adcVoltage , types=["java.lang.Integer"] , values=["4123","4120","4109","4102","4127","4130","4116","4113","4106","4099"]
    optional int32 adcVoltage = 20;
    // path=["status","statusList","liveSpeakerVolume"] , path[3]=liveSpeakerVolume , types=["java.lang.Integer"] , values=["100","75","2","50","88","0","47","64","60","53"]
    optional int32 liveSpeakerVolume = 21;
    // path=["status","statusList","wifiRssi"] , path[3]=wifiRssi , types=["java.lang.Integer"] , values=["-58","-64","-62","-46","-50","-56","-52","-54","-40","-60"]
    optional int32 wifiRssi = 22;
    // path=["status","statusList","wifiPowerLevel"] , path[3]=wifiPowerLevel , types=["java.lang.Integer"] , values=["3","0","1","2","4","6","9","10","5","7"]
    optional int32 wifiPowerLevel = 23;
    // path=["status","statusList","codec"] , path[3]=codec , types=["java.lang.String"] , values=["main=h265&sub=h265","main=h264&sub=h264"]
    optional string codec = 24;
    // path=["status","statusList","batteryModelNo"] , path[3]=batteryModelNo , types=["java.lang.String"] , values=[""]
    optional string batteryModelNo = 25;
    // path=["status","statusList","freqBandIndex"] , path[3]=freqBandIndex , types=["java.lang.Integer"] , values=["5","2","13","66","12","4"]
    optional int32 freqBandIndex = 26;
    // path=["status","statusList","charge"] , path[3]=charge , types=["java.lang.Integer"] , values=["0","1","2","-1"]
    optional int32 charge = 27;
    // path=["status","statusList","chargingMode"] , path[3]=chargingMode , types=["java.lang.Integer"] , values=["0","3","2","1"]
    optional int32 chargingMode = 28;
    // path=["status","statusList","wifiPowerLevelGitsha"] , path[3]=wifiPowerLevelGitsha , types=["java.lang.String"] , values=["0238d1","82ebd3","f83f9a","fa458d","","af91af","354178"]
    optional string wifiPowerLevelGitsha = 29;
    // path=["status","statusList","ap"] , path[3]=ap , types=["java.lang.String"] , values=["Home","MySpectrumWiFib0-2G","SpectrumSetup-C8","MySpectrumWiFi98-2G","NETGEAR48","NETGEAR12","STARLINK","SpectrumSetup-39","MySpectrumWiFi28-2G","MySpectrumWiFi48-2G"]
    optional string ap = 30;
    // path=["status","statusList","whiteLight"] , path[3]=whiteLight , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 whiteLight = 31;
    // path=["status","statusList","recordingAudioToggleOn"] , path[3]=recordingAudioToggleOn , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 recordingAudioToggleOn = 32;
    // path=["status","statusList","adcCapacity"] , path[3]=adcCapacity , types=["java.lang.Integer"] , values=["100","90","97","1","80","99","98","96","89","95"]
    optional int32 adcCapacity = 33;
    // path=["status","statusList","wifiChannel"] , path[3]=wifiChannel , types=["java.lang.Integer"] , values=["1","11","6","10","2","9","3","8","4","5"]
    optional int32 wifiChannel = 34;
    // path=["status","statusList","signalLevel"] , path[3]=signalLevel , types=["java.lang.Integer"] , values=["4","3","2","1","0","14"]
    optional int32 signalLevel = 35;
    // path=["status","statusList","solarModelNo"] , path[3]=solarModelNo , types=["java.lang.String"] , values=[""]
    optional string solarModelNo = 36;
    // path=["status","statusList","solarSn"] , path[3]=solarSn , types=["java.lang.String"] , values=[""]
    optional string solarSn = 37;
    // path=["status","statusList","solarOriginalModelNo"] , path[3]=solarOriginalModelNo , types=["java.lang.String"] , values=[""]
    optional string solarOriginalModelNo = 38;
    // path=["status","statusList","meterVoltage"] , path[3]=meterVoltage , types=["java.lang.Integer"] , values=["4130","4155","4151","4123","4144","4141","4120","4127","4137","4148"]
    optional int32 meterVoltage = 39;
    // path=["status","statusList","solarDisplayModelNo"] , path[3]=solarDisplayModelNo , types=["java.lang.String"] , values=[""]
    optional string solarDisplayModelNo = 40;
    // path=["status","statusList","batterySn"] , path[3]=batterySn , types=["java.lang.String"] , values=[""]
    optional string batterySn = 41;
    // path=["status","statusList","uid"] , path[3]=uid , types=["java.lang.String"] , values=["d87ed6d434a0640fd82e5ddf3d61df80","7ddadf51e7f2c581f8890f8fc721a5ad","e667764a454df564cb233fae3969aa44","87782bbbc7e99c4d5ccc511d13c0bacf","91efd0c96aa4094e143403e0181fcbed","b367d89aff0ca4fc7d63162b52c5d61a","723c9bbfcb8aabd49e152dd735f30f87","9bc2a0700e59acaafcce667d04456f7c","7b1c5f2a722c624799c30853a8eb7476","14d26560fc470e5e44b98fc7ecd7bf48"]
    optional string uid = 42;
    // path=["status","statusList","tcp_acc"] , path[3]=tcp_acc , types=["java.lang.String"] , values=["cubic"]
    optional string tcp_acc = 43;
    // path=["status","statusList","batteryDisplayModelNo"] , path[3]=batteryDisplayModelNo , types=["java.lang.String"] , values=[""]
    optional string batteryDisplayModelNo = 44;
    // path=["status","statusList","sinr"] , path[3]=sinr , types=["java.lang.Integer"] , values=["-4","5","18","24","4","14","7","12","19","8"]
    optional int32 sinr = 45;
    // path=["status","statusList","hasDeviceSupport"] , path[3]=hasDeviceSupport , types=["java.lang.String"] , values=["1"]
    optional string hasDeviceSupport = 46;
    // path=["status","statusList","batteryOriginalModelNo"] , path[3]=batteryOriginalModelNo , types=["java.lang.String"] , values=[""]
    optional string batteryOriginalModelNo = 47;
    // path=["status","statusList","deviceNetType"] , path[3]=deviceNetType , types=["java.lang.Integer"] , values=["0","1","2","3"]
    optional int32 deviceNetType = 48;

    message PbSdCardStatus {

      // path=["status","statusList","sdCardStatus","formatStatus"] , path[4]=formatStatus , types=["java.lang.Integer"] , values=["1","0","13","2"]
      optional int32 formatStatus = 1;
      // path=["status","statusList","sdCardStatus","free"] , path[4]=free , types=["java.lang.Integer"] , values=["0","199","200","201","202","203","204","205","198","206"]
      optional int32 free = 2;
      // path=["status","statusList","sdCardStatus","total"] , path[4]=total , types=["java.lang.Integer"] , values=["0","121866","121932","120505","121834","60860","59299","59423","60304","30424"]
      optional int32 total = 3;

    }

  }

}
