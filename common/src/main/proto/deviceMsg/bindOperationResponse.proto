syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbBindOperationResponseProto";

// h2接口：/deviceMsg/bindOperationResponse
message PbBindOperationResponse {

  // path=["bindOperationResponse","data","value","userId"] , path[2]=data
  optional PbData data = 1;
  // path=["bindOperationResponse","msg"] , path[2]=msg , types=["java.lang.String"] , values=["Success"]
  optional string msg = 2;
  // path=["bindOperationResponse","result"] , path[2]=result , types=["java.lang.Integer"] , values=["0"]
  optional int32 result = 3;

  message PbData {

    // path=["bindOperationResponse","data","value","userId"] , path[3]=value
    optional PbValue value = 1;
    // path=["bindOperationResponse","data","name"] , path[3]=name , types=["java.lang.String"] , values=["bindOperation"]
    optional string name = 2;
    // path=["bindOperationResponse","data","id"] , path[3]=id , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 id = 3;
    // path=["bindOperationResponse","data","time"] , path[3]=time , types=["java.lang.Integer"] , values=["1732800612","1732796839","1732802116","1732793677","1732800610","1732801820","1732794925","1732802634","1732793037","1732801701"]
    optional int32 time = 4;
    // path=["bindOperationResponse","data","code"] , path[3]=code , types=["java.lang.Integer"] , values=["0","-302"]
    optional int32 code = 5;

    message PbValue {

      // path=["bindOperationResponse","data","value","userId"] , path[4]=userId , types=["java.lang.Integer"] , values=["2817950","2821447","2821440","496059","2821489","2416265","1161313","2821633","2821609","2819646"]
      optional int32 userId = 1;
      // path=["bindOperationResponse","data","value","tenantId"] , path[4]=tenantId , types=["java.lang.String"] , values=["vicoo","dzeesHome","netvue","kiwibit","longse","xsense"]
      optional string tenantId = 2;
      // path=["bindOperationResponse","data","value","result"] , path[4]=result , types=["java.lang.String"] , values=[""]
      optional string result = 3;

      /* com.addx.iotcamera.publishers.vernemq.responses.MqttDeviceBindResponse.MqttDeviceBindResponseValue begin */
      optional string cloudUserEvn = 4;
      optional int32 cloudUserId = 5;
      /* com.addx.iotcamera.publishers.vernemq.responses.MqttDeviceBindResponse.MqttDeviceBindResponseValue end */

    }

  }

}
