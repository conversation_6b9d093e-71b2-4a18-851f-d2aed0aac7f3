syntax = "proto2";

package tutorial;

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbDeviceSupportProto";

// h2接口：/deviceMsg/deviceSupport
message PbDeviceSupport {

  // path=["deviceSupport","value","supportRotateCalibration"] , path[2]=value
  optional PbValue value = 1;
  // path=["deviceSupport","time"] , path[2]=time , types=["java.lang.Integer"] , values=["1629417601","1629417602","1732790509","1732800363","1732790726","1732790239","1732790464","1732791720","1732791657","1732793447"]
  optional int32 time = 2;
  // path=["deviceSupport","id"] , path[2]=id , types=["java.lang.Integer"] , values=["0","1","3","2","6"]
  optional int32 id = 3;
  // path=["deviceSupport","name"] , path[2]=name , types=["java.lang.String"] , values=["deviceSupport"]
  optional string name = 4;

  message PbValue {

    // path=["deviceSupport","value","supportRotateCalibration"] , path[3]=supportRotateCalibration , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportRotateCalibration = 1;
    // path=["deviceSupport","value","deviceDormancySupport"] , path[3]=deviceDormancySupport , types=["java.lang.Integer"] , values=["1"]
    optional int32 deviceDormancySupport = 2;
    // path=["deviceSupport","value","supportManualFloodlightSwitch"] , path[3]=supportManualFloodlightSwitch , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportManualFloodlightSwitch = 3;
    // path=["deviceSupport","value","isShield"] , path[3]=isShield , types=["java.lang.Boolean"] , values=["true","false"]
    optional bool isShield = 4;
    // path=["deviceSupport","value","supportLiveAudioToggle"] , path[3]=supportLiveAudioToggle , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportLiveAudioToggle = 5;
    // path=["deviceSupport","value","floodlightLuminanceRange","interval"] , path[3]=floodlightLuminanceRange
    optional PbFloodlightLuminanceRange floodlightLuminanceRange = 6;
    // path=["deviceSupport","value","supportBxStorage"] , path[3]=supportBxStorage , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportBxStorage = 7;
    // path=["deviceSupport","value","supportManualFloodlightLuminance"] , path[3]=supportManualFloodlightLuminance , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportManualFloodlightLuminance = 8;
    // path=["deviceSupport","value","postMotionDetectResult"] , path[3]=postMotionDetectResult , types=["java.lang.Integer"] , values=["0"]
    optional int32 postMotionDetectResult = 9;
    // path=["deviceSupport","value","isExternalAntenna"] , path[3]=isExternalAntenna , types=["java.lang.Integer"] , values=["0"]
    optional int32 isExternalAntenna = 10;
    // path=["deviceSupport","value","supportOtaAutoUpgrade"] , path[3]=supportOtaAutoUpgrade , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportOtaAutoUpgrade = 11;
    // path=["deviceSupport","value","supportMechanicalDingDong"] , path[3]=supportMechanicalDingDong , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 supportMechanicalDingDong = 12;
    // path=["deviceSupport","value","supportPersonAi"] , path[3]=supportPersonAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportPersonAi = 13;
    // path=["deviceSupport","value","pirType"] , path[3]=pirType , types=["java.lang.String"] , values=["P824M","BL412","MK411","D210HX","ZSIR","D210HM","ZSIR0"]
    optional string pirType = 14;
    // path=["deviceSupport","value","supportDeviceCall"] , path[3]=supportDeviceCall , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 supportDeviceCall = 15;
    // path=["deviceSupport","value","deviceSupportAlarm"] , path[3]=deviceSupportAlarm , types=["java.lang.Integer"] , values=["1"]
    optional int32 deviceSupportAlarm = 16;
    // path=["deviceSupport","value","supportWifiPowerLevel"] , path[3]=supportWifiPowerLevel , types=["java.lang.Boolean"] , values=["true"]
    optional bool supportWifiPowerLevel = 17;
    // path=["deviceSupport","value","supportAlexaWebrtc"] , path[3]=supportAlexaWebrtc , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportAlexaWebrtc = 18;
    // path=["deviceSupport","value","supportCos"] , path[3]=supportCos , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportCos = 19;
    // path=["deviceSupport","value","supportSdCardVideoModes"] , path[3]=supportSdCardVideoModes , types=["java.lang.String"] , values=["eventual","eventual,continual"]
    optional string supportSdCardVideoModes = 20;
    // path=["deviceSupport","value","wifiPowerModeOptions","[]"] , path[3]=wifiPowerModeOptions , types=["java.lang.String"] , values=["default","turbo"]
    repeated string wifiPowerModeOptions = 21;
    // path=["deviceSupport","value","supportOci"] , path[3]=supportOci , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportOci = 22;
    // path=["deviceSupport","value","supportAiEvent"] , path[3]=supportAiEvent , types=["java.lang.String"] , values=["person,pet,vehicle,package,bird"]
    optional string supportAiEvent = 23;
    // path=["deviceSupport","value","supportWebrtc"] , path[3]=supportWebrtc , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportWebrtc = 24;
    // path=["deviceSupport","value","p2pConnMgtStrategy"] , path[3]=p2pConnMgtStrategy , types=["java.lang.Integer"] , values=["1"]
    optional int32 p2pConnMgtStrategy = 25;
    // path=["deviceSupport","value","version"] , path[3]=version , types=["java.lang.String"] , values=["1.9.8","1.8.37","1.9.4","1.9.9","1.9.5","1.8.36","1.9.10","1.9.15"]
    optional string version = 26;
    // path=["deviceSupport","value","supportFloodlightMode"] , path[3]=supportFloodlightMode , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportFloodlightMode = 27;
    // path=["deviceSupport","value","supportTriggerFloodlightSwitch"] , path[3]=supportTriggerFloodlightSwitch , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportTriggerFloodlightSwitch = 28;
    // path=["deviceSupport","value","resetVolSupport"] , path[3]=resetVolSupport , types=["java.lang.Integer"] , values=["0"]
    optional int32 resetVolSupport = 29;
    // path=["deviceSupport","value","deviceSupportResolution"] , path[3]=deviceSupportResolution , types=["java.lang.String"] , values=["2304x1296,640x360,auto","1920x1080,1280x720,auto","2560x1440,1280x720,auto","2048x1536,640x480,auto","3840x2160,1280x720,auto"]
    optional string deviceSupportResolution = 30;
    // path=["deviceSupport","value","supportStarlightSensor"] , path[3]=supportStarlightSensor , types=["java.lang.Boolean"] , values=["true"]
    optional bool supportStarlightSensor = 31;
    // path=["deviceSupport","value","supportUnlimitedWebsocket"] , path[3]=supportUnlimitedWebsocket , types=["java.lang.Boolean"] , values=["true","false"]
    optional bool supportUnlimitedWebsocket = 32;
    // path=["deviceSupport","value","supportPlanFloodlightSwitch"] , path[3]=supportPlanFloodlightSwitch , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportPlanFloodlightSwitch = 33;
    // path=["deviceSupport","value","supportCrossCameraAi"] , path[3]=supportCrossCameraAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportCrossCameraAi = 34;
    // path=["deviceSupport","value","triggerFloodlightCooldownTimeOptions","[]"] , path[3]=triggerFloodlightCooldownTimeOptions , types=["java.lang.String"] , values=["30s","60s","180s"]
    repeated string triggerFloodlightCooldownTimeOptions = 35;
    // path=["deviceSupport","value","supportDoorBellRingKey"] , path[3]=supportDoorBellRingKey , types=["java.lang.String"] , values=["","1,2,3"]
    optional string supportDoorBellRingKey = 36;
    // path=["deviceSupport","value","voltameterType"] , path[3]=voltameterType , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 voltameterType = 37;
    // path=["deviceSupport","value","supportAddxStorage"] , path[3]=supportAddxStorage , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportAddxStorage = 38;
    // path=["deviceSupport","value","killKeepAlive"] , path[3]=killKeepAlive , types=["java.lang.Integer"] , values=["1"]
    optional int32 killKeepAlive = 39;
    // path=["deviceSupport","value","antiflickerSupport"] , path[3]=antiflickerSupport , types=["java.lang.Integer"] , values=["1"]
    optional int32 antiflickerSupport = 40;
    // path=["deviceSupport","value","supportImageEvent"] , path[3]=supportImageEvent , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportImageEvent = 41;
    // path=["deviceSupport","value","canStandby"] , path[3]=canStandby , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 canStandby = 42;
    // path=["deviceSupport","value","canRotate"] , path[3]=canRotate , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 canRotate = 43;
    // path=["deviceSupport","value","deviceSupportMirrorFlip"] , path[3]=deviceSupportMirrorFlip , types=["java.lang.Boolean"] , values=["true","false"]
    optional bool deviceSupportMirrorFlip = 44;
    // path=["deviceSupport","value","supportGoogleStorage"] , path[3]=supportGoogleStorage , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportGoogleStorage = 45;
    // path=["deviceSupport","value","supportFaceAi"] , path[3]=supportFaceAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportFaceAi = 46;
    // path=["deviceSupport","value","supportPetAi"] , path[3]=supportPetAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportPetAi = 47;
    // path=["deviceSupport","value","audioCodectype"] , path[3]=audioCodectype , types=["java.lang.String"] , values=["aac"]
    optional string audioCodectype = 48;
    // path=["deviceSupport","value","supportSdCardCooldownSeconds"] , path[3]=supportSdCardCooldownSeconds , types=["java.lang.String"] , values=["10s,30s,60s,180s,300s,600s,1200s,1800s","10s,30s,60s,180s,300s"]
    optional string supportSdCardCooldownSeconds = 49;
    // path=["deviceSupport","value","quantityCharge"] , path[3]=quantityCharge , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 quantityCharge = 50;
    // path=["deviceSupport","value","supportPackageAi"] , path[3]=supportPackageAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportPackageAi = 51;
    // path=["deviceSupport","value","supportSdCardCooldown"] , path[3]=supportSdCardCooldown , types=["java.lang.Integer"] , values=["2","0","1"]
    optional int32 supportSdCardCooldown = 52;
    // path=["deviceSupport","value","supportIndoor"] , path[3]=supportIndoor , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportIndoor = 53;
    // path=["deviceSupport","value","supportAlarmWhenRemoveToggle"] , path[3]=supportAlarmWhenRemoveToggle , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 supportAlarmWhenRemoveToggle = 54;
    // path=["deviceSupport","value","supportCryDetect"] , path[3]=supportCryDetect , types=["java.lang.Integer"] , values=["0"]
    optional int32 supportCryDetect = 55;
    // path=["deviceSupport","value","pirCooldownTimeOptions","[]"] , path[3]=pirCooldownTimeOptions , types=["java.lang.String"] , values=["1800s","600s","10s","30s","300s","60s","1200s","180s"]
    repeated string pirCooldownTimeOptions = 56;
    // path=["deviceSupport","value","supportFrequency"] , path[3]=supportFrequency , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportFrequency = 57;
    // path=["deviceSupport","value","supportSDCardslot"] , path[3]=supportSDCardslot , types=["java.lang.Integer"] , values=["0"]
    optional int32 supportSDCardslot = 58;
    // path=["deviceSupport","value","supportPirRecordTime"] , path[3]=supportPirRecordTime , types=["java.lang.Boolean"] , values=["true","false"]
    optional bool supportPirRecordTime = 59;
    // path=["deviceSupport","value","devicePersonDetect"] , path[3]=devicePersonDetect , types=["java.lang.Integer"] , values=["0"]
    optional int32 devicePersonDetect = 60;
    // path=["deviceSupport","value","antiDisassemblyAlarm"] , path[3]=antiDisassemblyAlarm , types=["java.lang.Integer"] , values=["1"]
    optional int32 antiDisassemblyAlarm = 61;
    // path=["deviceSupport","value","supportAlarmVolume"] , path[3]=supportAlarmVolume , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportAlarmVolume = 62;
    // path=["deviceSupport","value","supportPassbySunshine"] , path[3]=supportPassbySunshine , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 supportPassbySunshine = 63;
    // path=["deviceSupport","value","supportChargeAutoPowerOn"] , path[3]=supportChargeAutoPowerOn , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 supportChargeAutoPowerOn = 64;
    // path=["deviceSupport","value","pirRecordTimeOptions","[]"] , path[3]=pirRecordTimeOptions , types=["java.lang.String"] , values=["120s","10s","20s","60s","180s","15s"]
    repeated string pirRecordTimeOptions = 65;
    // path=["deviceSupport","value","mirrorFlip"] , path[3]=mirrorFlip , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 mirrorFlip = 66;
    // path=["deviceSupport","value","deviceSupportLanguage"] , path[3]=deviceSupportLanguage , types=["java.lang.String"] , values=["cn,en,ja,de,ru,fr,it,es","cn,en,ja,de,ru,fr,it,es,pt","de,en,fr,it,pt,es"]
    optional string deviceSupportLanguage = 67;
    // path=["deviceSupport","value","iccid"] , path[3]=iccid , types=["java.lang.String"] , values=["89430103524057116665","89852202408160598412"]
    optional string iccid = 68;
    // path=["deviceSupport","value","supportRecordingAudioToggle"] , path[3]=supportRecordingAudioToggle , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportRecordingAudioToggle = 69;
    // path=["deviceSupport","value","supportRecLamp"] , path[3]=supportRecLamp , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 supportRecLamp = 70;
    // path=["deviceSupport","value","supportVoiceVolume"] , path[3]=supportVoiceVolume , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportVoiceVolume = 71;
    // path=["deviceSupport","value","supportSendAiImageDirect"] , path[3]=supportSendAiImageDirect , types=["java.lang.Boolean"] , values=["true"]
    optional bool supportSendAiImageDirect = 72;
    // path=["deviceSupport","value","keepAliveProtocol"] , path[3]=keepAliveProtocol , types=["java.lang.String"] , values=["websocket","tcp"]
    optional string keepAliveProtocol = 73;
    // path=["deviceSupport","value","supportVideo12Hour"] , path[3]=supportVideo12Hour , types=["java.lang.Boolean"] , values=["true"]
    optional bool supportVideo12Hour = 74;
    // path=["deviceSupport","value","batteryCode"] , path[3]=batteryCode , types=["java.lang.String"] , values=["DT5000","HK5000","PH5000","","TP5200","CM5200","BT5200","XL4400","BTW5200","YT5200"]
    optional string batteryCode = 75;
    // path=["deviceSupport","value","supportLiveSpeakerVolume"] , path[3]=supportLiveSpeakerVolume , types=["java.lang.Integer"] , values=["0"]
    optional int32 supportLiveSpeakerVolume = 76;
    // path=["deviceSupport","value","streamProtocol"] , path[3]=streamProtocol , types=["java.lang.String"] , values=["webrtc"]
    optional string streamProtocol = 77;
    // path=["deviceSupport","value","supportPlanFloodlightLuminance"] , path[3]=supportPlanFloodlightLuminance , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportPlanFloodlightLuminance = 78;
    // path=["deviceSupport","value","sdCardPirRecordTimeOptions","[]"] , path[3]=sdCardPirRecordTimeOptions , types=["java.lang.String"] , values=["120s","10s","20s","60s","180s","15s"]
    repeated string sdCardPirRecordTimeOptions = 79;
    // path=["deviceSupport","value","floodlightModeOptions","[]"] , path[3]=floodlightModeOptions , types=["java.lang.String"] , values=["always","auto"]
    repeated string floodlightModeOptions = 80;
    // path=["deviceSupport","value","supportChangeCodec"] , path[3]=supportChangeCodec , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 supportChangeCodec = 81;
    // path=["deviceSupport","value","supportPirSliceReport"] , path[3]=supportPirSliceReport , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportPirSliceReport = 82;
    // path=["deviceSupport","value","supportVehicleAi"] , path[3]=supportVehicleAi , types=["java.lang.Integer"] , values=["1"]
    optional int32 supportVehicleAi = 83;
    // path=["deviceSupport","value","supportPirCooldown"] , path[3]=supportPirCooldown , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 supportPirCooldown = 84;
    // path=["deviceSupport","value","flash"] , path[3]=flash , types=["java.lang.String"] , values=["GD25Q127C","w25q64","XM25QH128C","PY25Q64HA","XT25F128B","P25Q64H","WIN25Q128","XM25QH128A",""]
    optional string flash = 85;
    // path=["deviceSupport","value","supportEventAnalytics"] , path[3]=supportEventAnalytics , types=["java.lang.Boolean"] , values=["false","true"]
    optional bool supportEventAnalytics = 86;
    // path=["deviceSupport","value","supportMotionTrack"] , path[3]=supportMotionTrack , types=["java.lang.Integer"] , values=["0","1"]
    optional int32 supportMotionTrack = 87;
    // path=["deviceSupport","value","whiteLight"] , path[3]=whiteLight , types=["java.lang.Integer"] , values=["1","0"]
    optional int32 whiteLight = 88;
    // path=["deviceSupport","value","firmwareType"] , path[3]=firmwareType , types=["java.lang.String"] , values=["IN3B","AK0F","AK0E","IN6G","IN6B","IN1B","AK0I","IN2B","IN3G","IN6K"]
    optional string firmwareType = 89;
    // path=["deviceSupport","value","supportNetTest"] , path[3]=supportNetTest , types=["java.lang.Boolean"] , values=["true"]
    optional bool supportNetTest = 90;
    // path=["deviceSupport","value","isFilter"] , path[3]=isFilter , types=["java.lang.Boolean"] , values=["true","false"]
    optional bool isFilter = 91;

    // 是否支持快照录像 . thingModel: {"identifier":"supportSnapshotRecording","name":"是否支持快照录像","type":"bool"}
    optional bool supportSnapshotRecording = 92;
    // 是否支持事件录像的双画面功能 . thingModel: {"identifier":"supportEventRecordingDualViewSwitch","name":"是否支持事件录像的双画面功能","type":"bool"}
    optional bool supportEventRecordingDualViewSwitch = 93;
    // 摄像头支持双画面直播类型（电子 or 物理) . thingModel: {"identifier":"deviceDualViewType","name":"摄像头支持双画面直播类型（电子 or 物理)","type":"int"}
    optional int32 deviceDualViewType = 94;
    // 是否展示云盘SPEED . thingModel: {"identifier":"supportPanTiltSpeed","name":"是否展示云盘SPEED","type":"bool"}
    optional bool supportPanTiltSpeed = 95;
    // 是否支持自动间隔 . thingModel: {"identifier":"supportCaptureInterval","name":"是否支持自动间隔","type":"bool"}
    optional bool supportCaptureInterval = 96;
    // 是否云盘追踪 . thingModel: {"identifier":"supportMotionTrackingForRecordingSwitch","name":"是否云盘追踪","type":"bool"}
    optional bool supportMotionTrackingForRecordingSwitch = 97;
    // 自动间隔列表 . thingModel: {"identifier":"captureIntervalOptions","name":"自动间隔列表","type":"array"}
    repeated string captureIntervalOptions = 98;
    // 云盘速度范围 . thingModel: {"identifier":"panTiltSpeedIntRange","name":"云盘速度范围","type":"object"}
    optional PbIntRange panTiltSpeedIntRange = 99;
    // 直播双画面分辨率 . thingModel: {"identifier":"deviceDualViewInfo","name":"直播双画面分辨率","type":"array"}
    repeated PbDeviceDualViewInfo deviceDualViewInfo = 100;
    // 是否支持linode . thingModel: {"identifier":"supportLinode","name":"是否支持linode","type":"int"}
    optional int32 supportLinode = 101;
    // 支持存储类型 . thingModel: {"identifier":"supportStorage","name":"支持存储类型","type":"int"}
    optional int32 supportStorage = 102;
    // 是否支持换网绑定 . thingModel: {"identifier":"supportChangeNetworkBind","name":"是否支持换网绑定","type":"bool"}
    optional bool supportChangeNetworkBind = 103;
    /* 12.音视频流联动(直播流编码)*/
    optional string liveStreamCodecOptions = 104;
    /* motionzone 支持外部不录像 */
    optional int32 supportOffZoneRecording = 105;

    /* 支持alarmDelay */
    optional bool supportAlarmDelay = 106;
    /* 支持鸟类检测，motion detect 时决定是否可以选择鸟类检测 */
    optional int32 supportBirdDetect = 107;
    /* 支持小动物检测，motion detect 时决定是否可以选择小动物检测 */
    optional int32 supportNuisanceAnimalDetect = 108;
    /* 支持鸟类报警 */
    optional int32 supportNuisanceAnimalAlarm = 109;
    /* 支持远程快门 */
    optional int32 supportShutterRemote = 110;
    /* 支持录像过滤 */
    optional bool supportRecordingFilterEnable = 111;
    /* 支持人体识别上报打标签 */
    optional bool supportPersonTag = 112;
    /* 支持宠物识别上报打标签 */
    optional bool supportPetTag = 113;

    /* 支持时间水印 */
    optional bool supportTimeWatermark = 114;
    /* 支持logo水印 */
    optional bool supportLogoWatermark = 115;

    message PbFloodlightLuminanceRange {

      // path=["deviceSupport","value","floodlightLuminanceRange","interval"] , path[4]=interval , types=["java.lang.Integer"] , values=["1"]
      optional int32 interval = 1;
      // path=["deviceSupport","value","floodlightLuminanceRange","max"] , path[4]=max , types=["java.lang.Integer"] , values=["100"]
      optional int32 max = 2;
      // path=["deviceSupport","value","floodlightLuminanceRange","min"] , path[4]=min , types=["java.lang.Integer"] , values=["1"]
      optional int32 min = 3;

    }

    message PbIntRange {
      optional uint32 min = 1;
      optional uint32 max = 2;
      optional uint32 interval = 3;
    }

    message PbDeviceDualViewInfo {
      optional string viewType = 1;
      optional string supportResolution = 2;
    }

  }

}
