syntax = "proto2";

package tutorial;

//import "ai_cloud/ai_cloud_param.proto";

option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.deviceMsg";
option java_outer_classname = "PbSettingResponseProto";

// h2接口：/deviceMsg/setting
message PbSettingResponse {

  // path=["queryRetainedMsgResponse","data","dormancy_plan_setting","value","value","3","[]","endMinute"] , path[2]=data
  optional PbData data = 1;
  // path=["queryRetainedMsgResponse","msg"] , path[2]=msg , types=["java.lang.String"] , values=["Success"]
  optional string msg = 2;
  // path=["queryRetainedMsgResponse","result"] , path[2]=result , types=["java.lang.Integer"] , values=["0"]
  optional int32 result = 3;

  message PbData {

    // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","2","areaThresh"] , path[5]=value
    optional PbValue2 value = 1;
    // path=["queryRetainedMsgResponse","data","setting","value","name"] , path[5]=name , types=["java.lang.String"] , values=["settings"]
    optional string name = 2;
    // path=["queryRetainedMsgResponse","data","setting","value","id"] , path[5]=id , types=["java.lang.String"] , values=["cmd:5864136da3574ca6b564d2011b596be4","cmd:3f176b097984447893448008fde46546","cmd:34e263a5f11642d0a7e946d84084a433","cmd:f85065af284a4e4fb2096b907054ce31","cmd:1599e38ba5d445ea98c5a072e0cd248b","cmd:c6e326b27fac4cc0abf7d09b8897f61a","cmd:f7bdee45d2394df298d87455747366de","cmd:0ab221371adf4f09b8000fe1b26a87d4","cmd:6365205e2ec44d00a8418159830e5f34","cmd:630a7e0c49d94e569fd03c8ca2858d77"]
    optional string id = 3;
    // path=["queryRetainedMsgResponse","data","setting","value","time"] , path[5]=time , types=["java.lang.Integer"] , values=["1732802404","1732802403","1732802405","1732802402","1732802597","1732802581","1732802589","1732802598","1732802602","1732802604"]
    optional int32 time = 4;

    message PbValue2 {

      // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","2","areaThresh"] , path[6]=pirSensor
      optional PbPirSensor pirSensor = 1;
      // path=["queryRetainedMsgResponse","data","setting","value","value","defaultCodec"] , path[6]=defaultCodec , types=["java.lang.String"] , values=["h264","h265"]
      optional string defaultCodec = 2;
      // path=["queryRetainedMsgResponse","data","setting","value","value","pirRecordTime"] , path[6]=pirRecordTime , types=["java.lang.String"] , values=["10s","20s","15s","180s","120s","60s"]
      optional string pirRecordTime = 3;
      // path=["queryRetainedMsgResponse","data","setting","value","value","videoAntiFlickerFrequency"] , path[6]=videoAntiFlickerFrequency , types=["java.lang.String"] , values=["60Hz","50Hz"]
      optional string videoAntiFlickerFrequency = 4;
      // path=["queryRetainedMsgResponse","data","setting","value","value","wifiPowerLevel"] , path[6]=wifiPowerLevel , types=["java.lang.Integer"] , values=["0","6","3","1","2","10","4","7","5"]
      optional int32 wifiPowerLevel = 5;
      // path=["queryRetainedMsgResponse","data","setting","value","value","alarmVolume"] , path[6]=alarmVolume , types=["java.lang.Integer"] , values=["75","100","10","50","90","51","99","80","85","98"]
      optional int32 alarmVolume = 6;
      // path=["queryRetainedMsgResponse","data","setting","value","value","mtu"] , path[6]=mtu , types=["java.lang.Integer"] , values=["1480","1400"]
      optional int32 mtu = 7;
      // path=["queryRetainedMsgResponse","data","setting","value","value","antiflicker"] , path[6]=antiflicker , types=["java.lang.Integer"] , values=["60","50","0"]
      optional int32 antiflicker = 8;
      // path=["queryRetainedMsgResponse","data","setting","value","value","liveAudioToggleOn"] , path[6]=liveAudioToggleOn , types=["java.lang.Boolean"] , values=["true","false"]
      optional bool liveAudioToggleOn = 9;
      // path=["queryRetainedMsgResponse","data","setting","value","value","recordingAudioToggleOn"] , path[6]=recordingAudioToggleOn , types=["java.lang.Boolean"] , values=["true","false"]
      optional bool recordingAudioToggleOn = 10;
      // path=["queryRetainedMsgResponse","data","setting","value","value","floodlightSchedulePlan"] , path[6]=floodlightSchedulePlan , types=["java.lang.String"] , values=["","[{\"endHour\":6,\"endMinute\":0,\"luminance\":100,\"startHour\":5,\"startMinute\":30}]","[{\"startMinute\":0,\"endMinute\":0,\"startHour\":17,\"luminance\":100,\"endHour\":6}]","[{\"luminance\":100,\"endMinute\":0,\"startMinute\":0,\"endHour\":7,\"startHour\":17}]","[{\"startMinute\":0,\"luminance\":98,\"endHour\":7,\"endMinute\":0,\"startHour\":18}]","[{\"endHour\":0,\"endMinute\":0,\"luminance\":60,\"startHour\":20,\"startMinute\":6}]","[{\"startHour\":18,\"endMinute\":0,\"luminance\":79,\"startMinute\":0,\"endHour\":6}]","[{\"startMinute\":15,\"endHour\":5,\"luminance\":94,\"endMinute\":30,\"startHour\":5}]","[{\"endHour\":7,\"endMinute\":0,\"luminance\":87,\"startHour\":15,\"startMinute\":55}]","[{\"endHour\":0,\"endMinute\":0,\"luminance\":30,\"startHour\":5,\"startMinute\":30}]"]
      repeated PbFloodlightSchedulePlan floodlightSchedulePlan = 11;
      // path=["queryRetainedMsgResponse","data","setting","value","value","videoResolution"] , path[6]=videoResolution , types=["java.lang.String"] , values=["mid","high","2k","4k"]
      optional string videoResolution = 12;
      // path=["queryRetainedMsgResponse","data","setting","value","value","voiceVolume"] , path[6]=voiceVolume , types=["java.lang.Integer"] , values=["100","75","0","50","80","90","70","51","60","49"]
      optional int32 voiceVolume = 13;
      // path=["queryRetainedMsgResponse","data","setting","value","value","otaAutoUpgrade"] , path[6]=otaAutoUpgrade , types=["java.lang.Boolean"] , values=["true","false"]
      optional bool otaAutoUpgrade = 14;
      // path=["queryRetainedMsgResponse","data","setting","value","value","doorbellRingKey"] , path[6]=doorbellRingKey , types=["java.lang.Integer"] , values=["2","3","1"]
      optional int32 doorbellRingKey = 15;
      // path=["queryRetainedMsgResponse","data","setting","value","value","reportPetAi"] , path[6]=reportPetAi , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool reportPetAi = 16;
      // path=["queryRetainedMsgResponse","data","setting","value","value","doorbellPressNotifySwitch"] , path[6]=doorbellPressNotifySwitch , types=["java.lang.Boolean"] , values=["true","false"]
      optional bool doorbellPressNotifySwitch = 17;
      // path=["queryRetainedMsgResponse","data","setting","value","value","sdCardCooldownSeconds"] , path[6]=sdCardCooldownSeconds , types=["java.lang.String"] , values=["10s","30s","60s","300s","180s","1800s","600s","1200s"]
      optional string sdCardCooldownSeconds = 18;
      // path=["queryRetainedMsgResponse","data","setting","value","value","whiteLightScintillation"] , path[6]=whiteLightScintillation , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 whiteLightScintillation = 19;
      // path=["queryRetainedMsgResponse","data","setting","value","value","mechanicalDingDongDuration"] , path[6]=mechanicalDingDongDuration , types=["java.lang.Integer"] , values=["0","200","2","1000","300","400","800","900","700","500"]
      optional int32 mechanicalDingDongDuration = 20;
      // path=["queryRetainedMsgResponse","data","setting","value","value","powerDisplay","powerMethod"] , path[6]=powerDisplay
      optional PbPowerDisplay powerDisplay = 21;
      // path=["queryRetainedMsgResponse","data","setting","value","value","motionTrack"] , path[6]=motionTrack , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 motionTrack = 22;
      // path=["queryRetainedMsgResponse","data","setting","value","value","language"] , path[6]=language , types=["java.lang.String"] , values=["en","es","cn","fr","ja","pt","ru","de","it"]
      optional string language = 23;
      // path=["queryRetainedMsgResponse","data","setting","value","value","voiceVolumeSwitch"] , path[6]=voiceVolumeSwitch , types=["java.lang.Integer"] , values=["1"]
      optional int32 voiceVolumeSwitch = 24;
      // path=["queryRetainedMsgResponse","data","setting","value","value","nightThresholdLevel"] , path[6]=nightThresholdLevel , types=["java.lang.Integer"] , values=["2","0","3","1","20","50","8"]
      optional int32 nightThresholdLevel = 25;
      // path=["queryRetainedMsgResponse","data","setting","value","value","deviceCallToggleOn"] , path[6]=deviceCallToggleOn , types=["java.lang.Boolean"] , values=["true","false"]
      optional bool deviceCallToggleOn = 26;
      // path=["queryRetainedMsgResponse","data","setting","value","value","reportPersonAi"] , path[6]=reportPersonAi , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool reportPersonAi = 27;
      // path=["queryRetainedMsgResponse","data","setting","value","value","alarmWhenRemoveToggleOn"] , path[6]=alarmWhenRemoveToggleOn , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool alarmWhenRemoveToggleOn = 28;
      // path=["queryRetainedMsgResponse","data","setting","value","value","detectPersonAi"] , path[6]=detectPersonAi , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool detectPersonAi = 29;
      // path=["queryRetainedMsgResponse","data","setting","value","value","liveSpeakerVolume"] , path[6]=liveSpeakerVolume , types=["java.lang.Integer"] , values=["100","0","50","91","2","88","94","80","60","53"]
      optional int32 liveSpeakerVolume = 30;
      // path=["queryRetainedMsgResponse","data","setting","value","value","sdCardCooldownSwitch"] , path[6]=sdCardCooldownSwitch , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 sdCardCooldownSwitch = 31;
      // path=["queryRetainedMsgResponse","data","setting","value","value","motionFloodlightTimer"] , path[6]=motionFloodlightTimer , types=["java.lang.String"] , values=["","30s","60s","180s"]
      optional string motionFloodlightTimer = 32;
      // path=["queryRetainedMsgResponse","data","setting","value","value","devicePersonDetect"] , path[6]=devicePersonDetect , types=["java.lang.Integer"] , values=["0"]
      optional int32 devicePersonDetect = 33;
      // path=["queryRetainedMsgResponse","data","setting","value","value","cryDetectLevel"] , path[6]=cryDetectLevel , types=["java.lang.Integer"] , values=["3"]
      optional int32 cryDetectLevel = 34;
      // path=["queryRetainedMsgResponse","data","setting","value","value","floodlightMode"] , path[6]=floodlightMode , types=["java.lang.String"] , values=["auto","always"]
      optional string floodlightMode = 35;
      // path=["queryRetainedMsgResponse","data","setting","value","value","cryDetect"] , path[6]=cryDetect , types=["java.lang.Integer"] , values=["0"]
      optional int32 cryDetect = 36;
      // path=["queryRetainedMsgResponse","data","setting","value","value","chargeAutoPowerOnCapacity"] , path[6]=chargeAutoPowerOnCapacity , types=["java.lang.Integer"] , values=["10"]
      optional int32 chargeAutoPowerOnCapacity = 37;
      // path=["queryRetainedMsgResponse","data","setting","value","value","floodlightScheduleSwitch"] , path[6]=floodlightScheduleSwitch , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool floodlightScheduleSwitch = 38;
      // path=["queryRetainedMsgResponse","data","setting","value","value","pirSirenDuration"] , path[6]=pirSirenDuration , types=["java.lang.Integer"] , values=["0","5","15","10"]
      optional int32 pirSirenDuration = 39;
      // path=["queryRetainedMsgResponse","data","setting","value","value","mechanicalDingDongSwitch"] , path[6]=mechanicalDingDongSwitch , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 mechanicalDingDongSwitch = 40;
      // path=["queryRetainedMsgResponse","data","setting","value","value","chargeAutoPowerOnSwitch"] , path[6]=chargeAutoPowerOnSwitch , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 chargeAutoPowerOnSwitch = 41;
      // path=["queryRetainedMsgResponse","data","setting","value","value","recLen"] , path[6]=recLen , types=["java.lang.Integer"] , values=["10","20","15","-1","30","60","120"]
      optional int32 recLen = 42;
      // path=["queryRetainedMsgResponse","data","setting","value","value","deviceStatusInterval"] , path[6]=deviceStatusInterval , types=["java.lang.Integer"] , values=["60"]
      optional int32 deviceStatusInterval = 43;
      // path=["queryRetainedMsgResponse","data","setting","value","value","motionTriggeredFloodlightSwitch"] , path[6]=motionTriggeredFloodlightSwitch , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool motionTriggeredFloodlightSwitch = 44;
      // path=["queryRetainedMsgResponse","data","setting","value","value","debugReport"] , path[6]=debugReport , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool debugReport = 45;
      // path=["queryRetainedMsgResponse","data","setting","value","value","mirrorFlip"] , path[6]=mirrorFlip , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 mirrorFlip = 46;
      // path=["queryRetainedMsgResponse","data","setting","value","value","pirCooldownTime"] , path[6]=pirCooldownTime , types=["java.lang.String"] , values=["60s","10s","300s","180s","30s","1800s","600s","1200s"]
      optional string pirCooldownTime = 47;
      // path=["queryRetainedMsgResponse","data","setting","value","value","recLamp"] , path[6]=recLamp , types=["java.lang.Integer"] , values=["1","0"]
      optional int32 recLamp = 48;
      // path=["queryRetainedMsgResponse","data","setting","value","value","nightVisionMode"] , path[6]=nightVisionMode , types=["java.lang.Integer"] , values=["0","1"]
      optional int32 nightVisionMode = 49;
      // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensitivity"] , path[6]=pirSensitivity , types=["java.lang.String"] , values=["mid","high","low"]
      optional string pirSensitivity = 50;
      // path=["queryRetainedMsgResponse","data","setting","value","value","pir"] , path[6]=pir , types=["java.lang.Integer"] , values=["2","1","3","0"]
      optional int32 pir = 51;
      // path=["queryRetainedMsgResponse","data","setting","value","value","powerSource"] , path[6]=powerSource , types=["java.lang.String"] , values=["unselected","solar_panel","battery_power_only","plug_in","wired"]
      optional string powerSource = 52;
      // path=["queryRetainedMsgResponse","data","setting","value","value","timeZone"] , path[6]=timeZone , types=["java.lang.Integer"] , values=["-300","-360","-480","-240","-180","-420","540","180","480","120"]
      optional int32 timeZone = 53;
      // path=["queryRetainedMsgResponse","data","setting","value","value","logUpload"] , path[6]=logUpload , types=["java.lang.Boolean"] , values=["false","true"]
      optional bool logUpload = 54;
      // path=["queryRetainedMsgResponse","data","setting","value","value","irThreshold"] , path[6]=irThreshold , types=["java.lang.Integer"] , values=["20","50","0","8"]
      optional int32 irThreshold = 55;
      // path=["queryRetainedMsgResponse","data","setting","value","value","sdCardVideoMode"] , path[6]=sdCardVideoMode , types=["java.lang.String"] , values=["eventual","continual"]
      optional string sdCardVideoMode = 56;
      // path=["queryRetainedMsgResponse","data","setting","value","value","alarmDuration"] , path[6]=alarmDuration , types=["java.lang.Integer"] , values=["5"]
      optional int32 alarmDuration = 57;
      // path=["queryRetainedMsgResponse","data","setting","value","value","motionTrackMode"] , path[6]=motionTrackMode , types=["java.lang.Integer"] , values=["0"]
      optional int32 motionTrackMode = 58;
      // path=["queryRetainedMsgResponse","data","setting","value","value","cooldownInS"] , path[6]=cooldownInS , types=["java.lang.Integer"] , values=["60","0","10","120","300","180","30","1800","600","1200"]
      optional int32 cooldownInS = 59;
      // path=["queryRetainedMsgResponse","data","setting","value","value","sdCardPirRecordTime"] , path[6]=sdCardPirRecordTime , types=["java.lang.String"] , values=["10s","20s","15s","180s","60s","auto","120s"]
      optional string sdCardPirRecordTime = 60;

      optional int32 whiteLight = 61;
      optional string wifiPowerMode = 62;
      optional bool detectFaceAi = 63;
      optional bool detectCrossCameraAi = 64;
      optional bool detectPetAi = 65;
      optional bool detectPackageAi = 66;
      optional bool detectVehicleAi = 67;
      optional bool enableOtherMotionAi = 68;
      optional bool enableAllRecordMotionsInDark = 69;
      optional bool video12HourSwitch = 70;

      optional PbCodecProfile codecProfile = 71;
      // pb不支持数字开头的字段，下划线开头也不行，用字母开头没问题
      optional Pb4G device4g = 72;

      // 存储到camera开关 . thingModel: {"identifier":"storeSwitch","defaultValue":1,"dataType":{"specs":{"0":"关闭","1":"canmea开启存储","2":"hub开启存储"},"type":"int"},"name":"存储到camera开关","type":"INT","supportName":"supportStorage"}
      optional int32 storeSwitch = 73;
      // camra在白天和晚上自动按不同间隔进行拍摄 . thingModel: {"identifier":"snapshotCaptureInterval","defaultValue":"auto","dataType":{"type":"string"},"name":"camra在白天和晚上自动按不同间隔进行拍摄","optionName":"captureIntervalOptions","type":"ENUM","supportName":"supportCaptureInterval"}
      optional string snapshotCaptureInterval = 74;
      // 快照开关 . thingModel: {"identifier":"snapshotRecordingSwitch","defaultValue":false,"dataType":{"specs":{"0":"关闭","1":"开启"},"type":"bool"},"name":"快照开关","type":"SWITCH","supportName":"supportSnapshotRecording"}
      optional int32 snapshotRecordingSwitch = 75;
      // 云盘开关 . thingModel: {"identifier":"motionTrackingForRecordingSwitch","defaultValue":false,"dataType":{"specs":{"0":"关闭","1":"开启"},"type":"bool"},"name":"云盘开关","type":"SWITCH","supportName":"supportMotionTrackingForRecordingSwitch"}
      optional int32 motionTrackingForRecordingSwitch = 76;
      // 云盘速度 . thingModel: {"identifier":"panTiltSpeed","defaultValue":50,"dataType":{"type":"int"},"name":"云盘速度","optionName":"panTiltSpeedIntRange","type":"RANGE","supportName":"supportPanTiltSpeed"}
      optional int32 panTiltSpeed = 77;
      // 云盘速度开关 . thingModel: {"identifier":"eventRecordingDualViewSwitch","defaultValue":false,"dataType":{"specs":{"0":"关闭","1":"开启"},"type":"bool"},"name":"云盘速度开关","type":"SWITCH","supportName":"supportEventRecordingDualViewSwitch"}
      optional int32 eventRecordingDualViewSwitch = 78;
      // alarm 警报延时时间,单位:s
      optional int32 alarmDelay = 79;
      // motionZone 外部不录像 . thingModel: {"identifier":"offZoneRecordingSwitch","defaultValue":true,"dataType":{"specs":{"0":"关闭","1":"开启"},"type":"bool"},"name":"云盘速度开关","type":"SWITCH","supportName":"supportOffZoneRecording"}
      optional int32 offZoneRecordingSwitch = 80;
      // 鸟类侦测
      optional int32 detectBirdAi = 81;
      //小动物侦测
      optional int32 detectNuisanceAnimalAi = 82;

      // 快门设置信息
      optional int32 shutterRemoteFeatureSwitch = 83;

      // 小动物告警信息
      optional int32 nuisanceAnimalAlarmSwitch = 84;
      // 小动物告警信息 时长   5s  10s 15s
      optional int32 nuisanceAnimalAlarmDuration = 85;

      optional IrParams irParams = 86;

      // 时间水印开关
      optional int32 timeWatermarkSwitch = 87;
      // logo水印开关
      optional int32 logoWatermarkSwitch = 88;

      message IrParams {
        optional int32 irCtrlMode = 1;
        optional int32 irPwmAutoAdjustIsEnable = 2;
        optional int32 irPwmPeriod = 3;
        optional int32 irPwmDutyMin = 4;
        optional int32 irPwmDutyMax = 5;
        optional int32 irPwmDutyStep = 6;
        optional int32 irMaxEv = 7;
        optional int32 irLightTagRatio = 8;
        optional int32 irLightTagEvTolerance = 9;
        optional int32 irAdjustIntervalTimes = 10;
      }

      message PbPirSensor {

        // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","2","areaThresh"] , path[7]=MotionDetection
        map<string, PbMotionDetection> MotionDetection = 1;
        // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","P824M","3","windowSize"] , path[7]=P824M
        map<string, PbP824M> P824M = 2;
        // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","D210HM","2","pulseWidth"] , path[7]=D210HM
        map<string, PbD210HM> D210HM = 3;
        // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","D210HX","1","pulseWidth"] , path[7]=D210HX
        map<string, PbD210HX> D210HX = 4;

        map<string, PbSunshineDetection> SunshineDetection = 5;

        map<string, PbZSIR> ZSIR = 6;
        map<string, PbNP624M> NP624M = 7;
        map<string, PbZSIR0> ZSIR0 = 8;

        // 原本是在 SunshineDetection 的key中，不符合pb结构，提到外面
        optional int32 sunshineOpend = 9;

        message PbMotionDetection {

          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","2","areaThresh"] , path[9]=areaThresh , types=["java.math.BigDecimal"] , values=["0.0022222"]
          optional double areaThresh = 1;
          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","1","sadMoveThresh"] , path[9]=sadMoveThresh , types=["java.math.BigDecimal"] , values=["0.0039215"]
          optional double sadMoveThresh = 2;
          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","MotionDetection","1","mdRatioThresh"] , path[9]=mdRatioThresh , types=["java.math.BigDecimal"] , values=["0.25"]
          optional double mdRatioThresh = 3;

        }

        message PbP824M {

          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","P824M","3","windowSize"] , path[9]=windowSize , types=["java.lang.Integer"] , values=["2"]
          optional int32 windowSize = 1;
          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","P824M","2","pulseCounterThresh"] , path[9]=pulseCounterThresh , types=["java.lang.Integer"] , values=["2"]
          optional int32 pulseCounterThresh = 2;
          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","P824M","3","voltageThresh"] , path[9]=voltageThresh , types=["java.lang.Integer"] , values=["40","50","60"]
          optional int32 voltageThresh = 3;

        }

        message PbD210HM {

          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","D210HM","2","pulseWidth"] , path[9]=pulseWidth , types=["java.lang.Integer"] , values=["55","15"]
          optional int32 pulseWidth = 1;

        }

        message PbD210HX {

          // path=["queryRetainedMsgResponse","data","setting","value","value","pirSensor","D210HX","1","pulseWidth"] , path[9]=pulseWidth , types=["java.lang.Integer"] , values=["70"]
          optional int32 pulseWidth = 1;

        }

        /*
        "SunshineDetection": {
          "1": {
            "adcR_th": 23,
            "mdAD_th": 0.139,
            "mdSD_th": 0.05
          }
        }
         */
        message PbSunshineDetection {

          optional int32 adcR_th = 1;
          optional double mdAD_th = 2;
          optional double mdSD_th = 3;

        }

        message PbZSIR {
          // "voltageThresh": 15
          optional int32 voltageThresh = 1;
        }

        // 与P824M结构一样
        message PbNP624M {
          optional int32 windowSize = 1;
          optional int32 pulseCounterThresh = 2;
          optional int32 voltageThresh = 3;
        }

        message PbZSIR0 {
          // "voltageThresh": 15
          optional int32 voltageThresh = 1;
        }

      }

      message PbPowerDisplay {

        // path=["queryRetainedMsgResponse","data","setting","value","value","powerDisplay","powerMethod"] , path[7]=powerMethod , types=["java.lang.String"] , values=["algo","gauge"]
        optional string powerMethod = 1;

      }

      /*
      [{
        "endHour": 0,
        "endMinute": 0,
        "luminance": 30,
        "startHour": 5,
        "startMinute": 30
      }]
       */
      message PbFloodlightSchedulePlan{
        optional int32 endHour = 1;
        optional int32 endMinute = 2;
        optional int32 luminance = 3;
        optional int32 startHour = 4;
        optional int32 startMinute = 5;
      }

      message PbCodecProfile{

        optional string name = 1;
        optional string version = 2;
        repeated PbDeviceCodec value = 3;

        /*
        https://a4x-paas.feishu.cn/wiki/wikcnr7795fwubZrXei4FGpbhCd
        `camera`.`device_codec`
         */
        message PbDeviceCodec{
          optional string ch = 1;
          optional string cc = 2;
          optional string res = 3;
          optional string rc = 4;
          optional int32 fr = 5;
          optional int32 gop = 6;
          optional int32 br = 7;
          optional int32 mbr = 8;
          optional int32 xbr = 9;
          optional int32 mqp = 10;
          optional int32 xqp = 11;
          optional int32 th = 12;
        }

      }

      message Pb4G {
        optional int32 mode = 1;
        optional string prov_edrx = 2;
        optional string req_edrx = 3;
        optional int32 type = 4;
      }

    }

  }

}
