syntax = "proto3";
import "common.proto";
option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto.bstationd";
option java_outer_classname = "BstationdSettingProcessorProto";


service BstationdSettingProcessor {
    // 处理setting任务
    rpc handleSetting (SettingRequest) returns (SendResult) {}

    // 处理ssd format任务
    rpc handleFormatSSD(FormatSSDRequest) returns (SendResult) {}

    // 处理单项设置任务
    rpc handleSingleSetting (SingleSettingRequest) returns (SendResult) {}

    rpc handleCmd (CmdRequest) returns (SendResult) {}

    // 处理埋点开关修改时间
    rpc handleEventAnalyticsSwitch(HandleEventAnalyticsSwitchRequest) returns (SendResult) {}

    rpc startTestAnalytics(StartTestAnalyticsRequest) returns(SendResult){}

    rpc setDiagnosticsConfig(DiagnosticsConfigRequest) returns(SendResult){}
}

message StartTestAnalyticsRequest {
    string endPoint = 1;
    string namespace = 2;
    int32  type = 3; // 1 开启沙盒模式 2退出沙盒模式
}

message CmdRequest {
    string serialNumber = 1;
    string requestId = 2;
    repeated CmdInfo cmdInfos = 3;

}

message CmdInfo {
    string name = 1;
    string value= 2;
}


message SingleSettingRequest {
    string serialNumber = 1;
    string requestId = 2;
    string name = 3;
    string value= 4;
}

message FormatSSDRequest {
    string serialNumber = 1;
    //如果收到重复id请求， 可以忽略，直接返回
    string requestId = 2;
}


message SettingRequest {
    string serialNumber = 1;
    string language = 2;
    string countryNo = 3;
    string requestId = 4;
    int32 alarmVolume = 5;
    int32 voiceVolume= 6;
    int32 needAlarm = 7;
    int32 alarmSeconds = 8;
    string deviceLanguage = 9;
    string timezone = 10;  // 时区
    int64 time = 11; // utc 时间戳-毫秒
    int32 standardOffset = 12;//标注时间偏移量
    int32 dstOffset = 13;//夏令时偏移量
    bool otaAutoUpgrade = 14; //自动ota开关 true on ,false off
    bool logUpload = 15; //日志自动上传开关 true on ,false off
    int32 alarmFlash= 16; //告警时是否闪烁 1是，0 否
    string securityMode= 17;// 模式代号

    optional bool overallLightSwitch = 18; // 总体灯光开关
    optional int32 overallLightIntensity = 19; // 总体灯光强度
}



