syntax = "proto3";

import "common.proto";
option java_multiple_files = true;
option java_package = "org.addx.iot.common.proto";
option java_outer_classname = "AiCloudParamProto";

message Activityzonelist {
  uint32 id = 1;
  repeated double vertices = 2;
  repeated double pose = 3;
}

message Outstorage {
  string bucket = 1;
  string keyPrefix = 2;
  string serviceName = 3;
}

// 水印位置信息结构体
message WatermarkPosition {
  int32 x = 1;        // 左上角x坐标
  int32 y = 2;        // 左上角y坐标
  int32 width = 3;    // 宽度
  int32 height = 4;   // 高度
}

message AiCloudParam {
  uint32 ownerId = 1;  // 4 个字节
  string tenantId = 2;   // 最多10个字节
  string context = 3;   //  17 个字节
  string outParams = 4;  // 41 字节
  string countryNo = 5;  // 2个字节
  repeated Activityzonelist activityZoneList = 6;    // 字节100  一个zone * 5 = 500
  repeated string recognitionObjects = 7;  // 4 * 5 = 20
  repeated string identificationObjects = 8;   // 4* 5 = 20
  Outstorage outStorage = 9;     // 最多 44
  string outputTopic = 10;       // 26
  repeated Color colors = 11;
  repeated AIFeature features = 12; // 字节数量 需要ai Task 列表 如果这个字段不为空， 优先使用这个字段，否者使用recognitionObjects、identificationObjects，后期慢慢废弃recognitionObjects、identificationObjects的使用
  repeated Biometrics allowed = 13;  //是否容许 生物特征，人脸 人体
  WatermarkPosition timeWatermarkPosition = 14;  // 时间水印位置
  WatermarkPosition logoWatermarkPosition = 15;  // logo水印位置
}