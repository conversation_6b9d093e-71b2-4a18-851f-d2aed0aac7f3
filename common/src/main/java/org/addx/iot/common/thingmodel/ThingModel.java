package org.addx.iot.common.thingmodel;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/10/9 16:08
 */

@Slf4j
@Getter
@Setter
public class ThingModel {
    private String schema;
    private List<ThingEntity> properties = new ArrayList<>();

    private List<ThingEntity> supports = new ArrayList<>();

    private Map<String, ThingEntity> identifier2Property = new LinkedHashMap<>();
    private Map<String, ThingEntity> identifier2Support = new LinkedHashMap<>();

    public JSONObject parseSupports(JSONObject reportJson) {
        //根据thing_model.json里supports的定义，解析value中的对应项出来并返回
        JSONObject jsonObject = new JSONObject();
        for (ThingEntity thingEntity : this.supports){
            if(reportJson.containsKey(thingEntity.getIdentifier())){
                jsonObject.put(thingEntity.getIdentifier(),reportJson.get(thingEntity.getIdentifier()));
            }
        }
        return jsonObject;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThingEntity {
        private String identifier;
        private String name;
        private DataType dataType;
        private String type;
        private String convertFunction;
        private String supportName;
        private String optionName;
        private Object defaultValue;
        private List<String> defaultOptions;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataType {
        private String type;
        private Map<String, String> specs;
    }

    public ThingModel init() {
        properties.forEach(it -> identifier2Property.put(it.getIdentifier(), it));
        supports.forEach(it -> identifier2Support.put(it.getIdentifier(), it));
        return this;
    }

    public static boolean containsProperty(ThingModel thingModel, String identifier) {
        return Optional.ofNullable(thingModel).map(it -> it.getIdentifier2Property().containsKey(identifier)).orElse(false);
    }

    public static boolean containsSupport(ThingModel thingModel, String identifier) {
        return Optional.ofNullable(thingModel).map(it -> it.getIdentifier2Support().containsKey(identifier)).orElse(false);
    }

    // 获取物模型中配置的设备设置属性的默认值
    public static <T> T getPropertyDefaultValue(ThingModel thingModel, String identifier, Class<T> cls) {
        if (thingModel == null || StringUtils.isBlank(identifier)) return null;
        return Optional.ofNullable(thingModel.getIdentifier2Property()).map(it -> it.get(identifier))
                .map(ThingEntity::getDefaultValue).map(it -> JSON.parseObject(JSON.toJSONString(it), cls)).orElse(null);
    }

    // 获取物模型中配置的设备设置属性值
    public static <T> T getPropertyValue(ThingModel thingModel, JSONObject propertyJson, String identifier, Class<T> cls) {
        try {
            T value = Optional.ofNullable(propertyJson).map(it -> it.getObject(identifier, cls))
                    .orElseGet(() -> ThingModel.getPropertyDefaultValue(thingModel, identifier, cls));
            log.debug("getPropertyValue end! value={},identifier={},cls={},propertyJson={}", value, identifier, identifier, propertyJson);
            return value;
        } catch (Throwable e) {
            log.error("getPropertyValue error! identifier={},cls={},propertyJson={}", identifier, identifier, propertyJson);
            return null;
        }
    }

}
