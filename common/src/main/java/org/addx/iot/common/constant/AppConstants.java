package org.addx.iot.common.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AppConstants {
    // 区分接口version
    public final static String defaultVersion = "v0";
    public final static String TENANTID_VICOO = "vicoo";
    public final static String TENANTID_GUARD = "guard";
    public final static String TENANTID_SHENMOU = "shenmou";
    public final static String TENANTID_KIWIBIT = "kiwibit";
    public final static Set<String> TENANTID_NO_NEED_WELCOME = new HashSet<>(Arrays.asList("monkeyvision"));

    public final static String tenantIdVicoHome = "vicohome";
    public final static String tierNameVicoHome = "VicoHome";
    public final static String tierNameVicoo = "Vicoo";
    public final static String TENANTID_SAFEMO = "safemo";

    public final static String TIER_NAME_MONKEY = "Monkey Vision";
    public final static String tenantIdKiwibit = "kiwibit";

    public final static String REDIS_PREFIX_CACHE_NAME = "unified_v1:";

    //指定设备限制套餐
    public final static Set<String> TIER_DEVICE_LIMIT_APP = new HashSet<>(Arrays.asList(TENANTID_VICOO, TENANTID_GUARD));

    public final static Set<String> TENANT_DZEES = new HashSet<>(Arrays.asList("dzees","dzeesHome"));

    //vicoHome 包名
    public final static String VICOHOME_PACKAGE = "com.smartaddx.vicohome";
    // 全橙看家 包名
    public final static String GUARD_PACKAGE = "comaiaddxguard";

    public final static String ZENDESK_NODE_CN = "CN";
    public final static String ZENDESK_NODE_EU = "EU";
    public final static String ZENDESK_NODE_US = "US";
    // app 语言-英语
    public final static String APP_LANGUAGE_ZH = "zh";
    public final static String APP_LANGUAGE_EN = "en";
    // appType 安卓
    public final static String APP_TYPE_ANDROID = "Android";
    public final static String APP_TYPE_IOS = "iOS";


    public final static int APP_LIVE_AWAKE_DEVICE_EXPIRE = 30;

    public static final String APP_LOG_FILE_NAME_PATTERN = "app_log/{userId}/{year}/{month}/{day}/{logStartTime}_{logEndTime}_{logType}";

    public static final String DEFAULT_PACKAGE_VICOO = "addx.ai.vicoo";

    public static final int SATISFIED_SCORE = 3;
    // 统计直播成功率，需要直播5次以上
    public static final int SATISFIED_LIVE_NUM = 5;
    // 统计直播成功率，成功率需要大于99%
    public static final int SATISFIED_SCORE_VALUE = 99;

    //新增的jdk目前识别不了的 America/Ciudad_Juarez 时区
    public static final String TIME_ZONE_AMERICA_CIUDAD_JUAREZ = "America/Ciudad_Juarez";
    //如果有不能识别的时候，默认返回伦敦时区
    public static final String DEFAULT_TIME_ZONE_EUROPE_LONDON = "Europe/London";
    public static final String TIME_ZONE_AMERICA_OJINAGA = "America/Ojinaga";

    // 验证失败次数超过3次会引导忘记密码
    public static final int LOGIN_ERROR_GUIDE_PASSWORD_COUNT = 3;

    public static final List<Integer> SUPPORT_ROLLING_DAY = Arrays.asList(30,60);
    
    public final static String VICOO_SEND_EMAIL = "<EMAIL>";

  // 新版本welcome 邮件内容, 目前只 vicohome且英文
  public final static String WELCOM_BODY_VICOO =
      "Hello $USER_NAME, welcome to $APP_NAME! For helpful articles about using $APP_NAME, please visit our <a href=\"https://www.vicohome.io/support\">$APP_NAME Help Center</a>. If you need assistance, do not hesitate to submit feedback within the app or email $SUPPORT_EMAIL, and we are always happy to help. ";

    public final static String LINK_PARAN = "$LINK_PARAM";

    public final static String WELCOME_BODY_SAFEMO = "zendeskWelcomeHtmlBodySafemo";

    public final static String B_SERVICE_NAME_PARAM = "{b_service_name}";

    //日期是年-月-日格式的语言
    public final static Set<String> DATE_YEAR_MONTH_DAY = new HashSet<>(Arrays.asList("zh","zh-Hant","ja","ar","vi"));

    public final static Set<String> APP_WELCOME_SPECIAL = new HashSet<>(Arrays.asList(TENANTID_KIWIBIT,TENANTID_SAFEMO));

    public final static String APP_WELCOME_SPECIAL_TITLE = "zendeskWelcomeSubject_{tenantId}";
    public final static String APP_WELCOME_SPECIAL_BODY = "zendeskWelcomeHtmlBody_{tenantId}";

    public final static String APP_48_NO_BIND_TITLE = "noBindCameraAfter48hSubject_{tenantId}";
    public final static String APP_48_NO_BIND_BODY = "noBindCameraAfter48hHtmlBody_{tenantId}";

    public static final String PLACEHOLDER_SUPPORT_PHONE = "$PHONE_SUPPORT";
    public static final String PLACEHOLDER_YOUTWOBE_LINK = "$YOUTUBE_ChANNEL_LINK";
    public static final String PLACEHOLDER_HELP_CENTER = "$HELP_CENTER_LINK";
    public static final String PLACEHOLDER_GUIDE_LINK1 = "$GUIDE_LINK1";
    public static final String PLACEHOLDER_GUIDE_LINK2 = "$GUIDE_LINK2";
    public static final String PLACEHOLDER_TOP_IMAGE_URL = "$TOP_IMAGE_URL";

    public static final String PLACEHOLDER_TEXT_DIRECTION = "$TEXT_DIRECTION";
    public static final String PLACEHOLDER_TEXT_ALIGN = "$TEXT_ALIGN";

    public static final Set<String> PLACEHOLDER_TEXT_ALIGN_LANGUAGE = new HashSet<>(Arrays.asList("ar","he"));
    public static final String PLACEHOLDER_TEXT_DIRECTION_RIGHT = "rtl";
    public static final String PLACEHOLDER_TEXT_DIRECTION_LEFT = "ltr";

    public static final String PLACEHOLDER_TEXT_ALIGN_RIGHT = "right";
    public static final String PLACEHOLDER_TEXT_ALIGN_LEFT = "left";



    public static final String APP_WELCOME_SPECIAL_HTML = "<body style=\"margin: 0; padding: 0; font-family: SF Pro, sans-serif; background-color: #f4f4f4; color: #181D19;\" dir=\"$TEXT_DIRECTION\">" +
            "    <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"100%\" style=\"max-width: 540px; margin: 0 auto; background-color: #ffffff;\">" +
            "        <tr>" +
            "            <td style=\"text-align: center; \">" +
            "                <img src=\"$TOP_IMAGE_URL\" alt=\"Logo\" style=\"width: 100%; height: auto;\">" +
            "            </td>" +
            "        </tr>" +
            "        <tr>" +
            "            $BODY" +
            "        </tr>" +
            "    </table>" +
            "</body>";


    /** pir alarm start */
    public final static String MODE_ALARM_NOTIFY_TITLE = "notify_title_alarm_count_down";
    public final static String MODE_ALARMING_NOTIFY_TITLE = "notify_title_alarming_now";
    public final static String MODE_ALARMING_NOTIFY_MAIN_TEXT = "notify_subtitle_tap_to_view_details";

    public final static String MODE_ALARM_CANCEL_NOTIFY_TITLE = "notify_title_alarm_canceled";
    public final static String MODE_ALARM_CANCEL_USER_NOTIFY_TEXT = "notify_subtitle_user_canceled";
    public final static String MODE_ALARM_CANCEL_MODE_NOTIFY_MAIN_TEXT = "notify_subtitle_mode_change_canceled";
    public final static String MODE_ALARM_CANCEL_MODE_NOTIFY_MODE_NAME = "{modeName}_mode_name";
    /** pir alarm end */
    public final static String SUPPORT_HOME_MODE = "a_support_home:{userId}";
    public final static String HOME_MODE_DELAY_KEY = "home_mode_delay:{homeId}";
    public final static String SUPPORT_FROM_DEVICE = "device";

    public final static String SUPPORT_FROM_MODEL = "model";

}
