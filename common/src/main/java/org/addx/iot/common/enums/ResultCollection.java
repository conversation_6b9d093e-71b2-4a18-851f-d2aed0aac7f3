package org.addx.iot.common.enums;

import lombok.Getter;
import lombok.ToString;
import org.addx.iot.common.vo.Result;

import java.util.TreeMap;

/**
 * 把原来的ResultCollection常量类改成枚举类
 * 去掉getResult中巨量的if-else判断
 */
@ToString
public enum ResultCollection {

    SUCCESS(0, Result.Success()),

    INVALID_PARAMS(-102),
    ILLEGAL_VALUE(-103),
    ILLEGAL_SIGNATURE(-401),
    INVALID_PHONE(-1011),
    ACCOUNT_IN_USE(-1002),
    USER_COMMON(-1003, getDefaultResult()),
    ACCOUNT_NOT_REGISTERED(-1012),
    ADMIN_ACCOUNT_NOT_REGISTERED(-1013),
    ACCOUNT_NEED_REGISTERED_AGAIN(-1014),
    NOT_LOGGED_IN(-1021),
    NOT_LOGGED_INFOR(-1020),
    USER_ACCOUNT_CANCEL(-1050, Result.Error(INVALID_PARAMS, "USER_ACCOUNT_CANCEL")),
    USER_ACCOUNT_TRANSFER(-1051, getDefaultResult()),
    PASSWORD_OR_CODE_ERROR(-1026),
    TOO_MAY_ERROR(-1027),
    RETRY_TOO_MAY_ERROR(-1028),
    TOO_MAY_ERROR_GUIDE_PASSWORD(-1029),
    FREQUENT_CONFIRM(-1033),
    CONFIRM_BEYOND_LIMIT(-1035),
    CONFIRM_EXPIRED(-1036),
    DEVICE_SHARED(-1037),
    NO_CONTACT(-1041),
    BIND_MULTI_CONTACTS(-1042),
    DEVICE_AUTH_LIMITATION(-2001),
    DEVICE_NO_ACCESS(-2002),
    DEVICE_SN_NOT_SUPPORT_PLATFORM(-2003),

    DEVICE_SN_NOT_SUPPORT_APP(-2004),

    DEVICE_4G_NO_ACCESS(-2005),
    TIER_DEVICE_NUM_ERROE(-2006),

    SHARE_ID_EXPIRED(-2011),
    INVALID_SHARE_ID(-2012),
    SHARE_TO_SELF(-2013),
    // 分享者与请求者不在同一节点下
    SHARE_DIFF_NODE(-2014, getDefaultResult()),
    // 设备已关机
    DEVICE_SHUTDOWN(-2103, getDefaultResult()),
    SHARE_ID_EXIST(-2015),
    DEVICE_HOME_NAME_EXIST(-2020),
    DEVICE_HOME_IN_USE(-2021),
    DEVICE_ACTIVATED(-2111),

    DEVICE_UNACTIVATED(-2112), // 设备端收到 -2112 就会解绑
    //设备未上报支持属性
    DEVICE_NO_SUPPORT_INFO(-2113),
    //设备不支持reset电量计
    DEVICE_NO_SUPPORT_RESET_VOL(-2114),
    DEVICE_MODEL_BIND_SUPPORT(-2115),
    DEVICE_BSTATIOND_UNACTIVATED(-2115),
    DEVICE_OFFLINE(-2131),

    DEVICE_NO_ROLE(-2132),

    DEVICE_DORMANCY_STATUS(-2133),

    DEVICE_BIND_OTHER(-2134),

    LOCATION_NAME_USED(-2201),

    LOCATION_IN_USE(-2211),

    DEVICE_NO_EXIT(-2140),

    INVALID_REQUEST_ID(-3011),

    REQUEST_EXPIRED(-3012),

    REQUEST_NOT_HANDLED(-3013),

    DEVICE_NO_RESPONSE(-3021),

    OTA_START_REFUSED(-3111),

    OUTDATED_FIRMWARE(-3112),

    NO_FIRMWARE(-3120),

    NO_LIBRARY_ACCESS(-4111),

    LIBRARY_EXPIRED(-4112),

    ALREADY_PURCHASED_ERROR(-5001),

    INSERT_VIDEO_ERROR(-6001),

    PAY_NOTIFY_ERROE(-6002, getDefaultResult()),

    PUSH_MQTT_ERROR(-6003),

    FACTORY_LOGIN_ERROR(-7001),

    OPERATION_NOT_SUPPORTED(-8001, getDefaultResult()),

    BEYOND_ROTATION_LIMITATION(-8002),

    FATAL_ERROR(-9999, getDefaultResult()),

    OPERTION_DB_ERROR(-6112),

    IOS_NO_TRANSACTIONID(-8001),
    IOS_ORDER_EMPTY(-8002),
    IOS_ORDER_STATUS_ERROR(-8003),
    IOS_ORDER_PACKAGE_ERROR(-8004),
    TIER_DEVICE_LIMIT_ERROR(-8010),
    ORDER_CANCEL(-8011),

    AWS_ASSUME_ROLE_ERROR(-9001, "aws授权失败"),
    REFUSE_OVERWRITE_BINDING(-9011, "拒绝覆盖绑定"),

    PRODUCT_EXCHANGE_CODE_NOT_EXIST(-9101, "兑换码不正确"),
    PRODUCT_EXCHANGE_CODE_NOT_REGULAR(-9101, "兑换码不正确"), // 兑换码不符合规则
    PRODUCT_EXCHANGE_CODE_USED(-9102, "兑换码已被使用"),
    PRODUCT_EXCHANGE_CODE_EXPIRED(-9103, "兑换码已过期"),
    PRODUCT_EXCHANGE_CODE_USE_FREQUENT(-9104, "您已的操作过于频繁，请于15分钟后再试"),
    PRODUCT_EXCHANGE_CODE_NO_DEVICE(-9105, "没有设备"),
    PRODUCT_EXCHANGE_CODE_ALREADY_REDEEMED(-9106, "该设备已经被兑换了"),
    PRODUCT_EXCHANGE_CODE_MODEL_NO_NOT_ELIGIBLE(-9107, "该设备类型不能兑换"),
    PRODUCT_EXCHANGE_CODE_USED_V2(-9108, "兑换码已被使用v2"),

    USER_NOT_VIP(-9151, "您不是VIP,无法使用此功能"),
    BIND_OPERATION_ID_DUPLICATED(-9161, "bindOperationId duplicated!"),

    EXTENSION_NOT_SUPPORT(-9152),
    EXTENSION_NOT_INSTALL(-9153),
    EXTENSION_ALREADY_INSTALLED(-9154),
    EXTENSION_INSTALL_FAILED(-9155),
    EXTENSION_CANNOT_FIND_USER(-9156),
    EXTENSION_CANNOT_IDENTIFY_USER(-9157),

    FACE_OPT_ERROR(-9300),
    FACE_MAX_ERROR(-9301),
    FACE_NAME_ALREADY_EXIST(-9302),
    PERMISSION_DENIED(-10001),

    PARSE_PROTO_REQ_FAIL(-7401, "parse proto request fail!"),
    TRANS_PROTO_TO_JSON_FAIL(-7402, "trans proto to json fail!"),
    HANDLE_PROTO_REQ_DEFAULT_RESULT(-7403, "handle proto request result is null!"),
    NOT_PROTO_REQ_HANDLER(-7404, "not proto request handler!"),
    HANDLE_PROTO_REQ_BIZ_ERROR(-7501, "handle proto request happen biz error!"),
    HANDLE_PROTO_REQ_UNKNOWN_ERROR(-7502, "handle proto request happen unknown error!"),
    HANDLE_JSON_TO_PROTO_ERROR(-7503, "trans json to proto error!"),

    NODE_NOT_SUPPORT(-9162, "node not support"),

    // 多分辨率相关错误
    QUERY_RESOLUTION_FAILED(-4201, "QUERY_RESOLUTION_FAILED"),
    ;

    @Getter
    private final int code;
    @Getter
    private final String msg;
    // 枚举的result参数就是根据switch分支创建的
    private final Result result;

    public Result getResult() {
        // result是可变对象，复制后返回，防止被修改
        return new Result(result.getResult(), result.getMsg(), result.getData());
    }

    public <T> Result<T> getNullDataResult() {
        // result是可变对象，复制后返回，防止被修改
        return new Result(result.getResult(), result.getMsg(), null);
    }

    ResultCollection(int code, String msg, Result result) {
        this.code = code;
        this.msg = msg != null ? msg : this.name();
        this.result = result != null ? result : Result.Error(this.code, this.msg);
    }

    ResultCollection(int code) {
        this(code, null, null);
    }

    ResultCollection(int code, Result result) {
        this(code, null, result);
    }

    ResultCollection(int code, String msg) {
        this(code, msg, null);
    }

    private static final TreeMap<Integer, ResultCollection> errorEnumMap = new TreeMap<>();

    static {
        for (ResultCollection errorEnum : ResultCollection.values()) {
            errorEnumMap.put(errorEnum.getCode(), errorEnum);
        }
    }

    public static ResultCollection codeOf(int code) {
        return errorEnumMap.get(code);
    }

    /**
     * 在原getResult方法中有些常量在switch中找不到分支（估计是创建时遗漏了，先不动这个逻辑）
     * 如果参数code=上述常量或code不在定义的常量中，最终会进入default分支。
     * 在新getResult方法中code属于以上情形的，会调用getDefaultResult方法，最终返回值和原getResult完全一样
     */
    public static Result getResult(int code) {
        ResultCollection errorEnum = codeOf(code);
        if (errorEnum == null) return getDefaultResult();
        return errorEnum.getResult();
    }

    // 原getResult的switch语句的default分支
    private static Result getDefaultResult() {
        return Result.Failure("Unknown Error");
    }

}
