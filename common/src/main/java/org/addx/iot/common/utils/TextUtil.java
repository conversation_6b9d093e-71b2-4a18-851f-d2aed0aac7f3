package org.addx.iot.common.utils;

import com.google.common.base.Strings;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TextUtil {

    /**
     * 对备注文本去空格并且检查条件
     *
     * @param remark
     * @param conditions 条件
     * @return
     */
    public static Result<String> trimAndCheckRemark(String remark, List<Function<StringBuilder, Result>> conditions) {
        if (StringUtils.isBlank(remark)) {
            return new Result<>("");
        }
        StringBuilder strBuilder = new StringBuilder();
        for (String lineStr : remark.split("\n")) {
            String line = lineStr.trim();
            if (line.isEmpty()) continue;
            strBuilder.append(line);
            for (Function<StringBuilder, Result> condition : conditions) {
                Result result = condition.apply(strBuilder);
                if (result.getResult() != Result.successFlag) return result;
            }
            strBuilder.append("\n");
        }
        return new Result<>(strBuilder.substring(0, strBuilder.length() - 1));
    }

    public static Result<String> trimAndCheckRemark(String remark, int maxLength, String errMsgPrefix) {
        return trimAndCheckRemark(remark, Arrays.asList(strBuilder -> {
            if (strBuilder.length() > maxLength) {
                return Result.Error(ResultCollection.INVALID_PARAMS, errMsgPrefix + "文本不能超过" + maxLength + "个字符!");
            }
            return Result.Success();
        }));
    }

    // 去除字符串两端的字符串
    public static String removeTwoEndChar(String prefix, String startStr, String endStr) {
        if (prefix.startsWith(startStr)) prefix = prefix.substring(startStr.length());
        if (prefix.endsWith(endStr)) prefix = prefix.substring(0, prefix.length() - endStr.length());
        return prefix;
    }

    // 分割字符串，过滤掉空字符串
    public static Stream<String> splitToNotBlankStream(String text, char separator) {
        if (StringUtils.isBlank(text)) return Stream.empty();
        return Arrays.stream(StringUtils.split(text, separator)).map(String::trim).filter(StringUtils::isNotBlank);
    }

    public static Set<String> splitToNotBlankSet(String text, char separator) {
        return splitToNotBlankStream(text, separator).collect(LinkedHashSet::new, LinkedHashSet::add, LinkedHashSet::addAll);
    }

    public static List<String> splitToNotBlankList(String text, char separator) {
        return splitToNotBlankStream(text, separator).collect(Collectors.toList());
    }

    public static List<Integer> splitToIntList(String text, char separator) {
        return splitToNotBlankStream(text, separator).map(Integer::valueOf).collect(Collectors.toList());
    }

    // 固定宽度，填充剩余，文本靠左
    public static String fixWidthL(Object obj, int width, String str) {
        String text = obj + "";
        return text + Strings.repeat(str, Math.max(width - text.length(), 0));
    }

    // 固定宽度，填充剩余，文本靠右
    public static String fixWidthR(Object obj, int width, String str) {
        String text = obj + "";
        return Strings.repeat(str, Math.max(width - text.length(), 0)) + text;
    }

    public static String toCamel(String word) {
        StringBuilder builder = new StringBuilder();
        for (String str : word.split("_")) {
            if (StringUtils.isBlank(str)) continue;
            if (builder.length() == 0) {
                builder.append(str);
            } else {
                builder.append(str.substring(0, 1).toUpperCase()).append(str.substring(1));
            }
        }
        return builder.toString();
    }

    public static String toHeadUpper(String str) {
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    private static final Pattern camelPattern = Pattern.compile("(^|[A-Z])[_a-z0-9]*");

    public static String toUnderline(String str) {
        StringBuilder result = new StringBuilder();
        Matcher matcher = camelPattern.matcher(str);
        while (matcher.find()) {
            String s = matcher.group();
            if (Character.isUpperCase(s.charAt(0))) {
                result.append('_');
                result.append(Character.toLowerCase(s.charAt(0)));
                result.append(s, 1, s.length());
            } else {
                result.append(s);
            }
        }
        return result.toString();
    }

    public static final String concatPostfixIfNotExists(String str, String postfix) {
        if (str == null) return postfix;
        return str.endsWith(postfix) ? str : (str + postfix);
    }

    public static final String[] splitUntilFromLast(String content, String separator, String endStr) {
        LinkedList<String> list = new LinkedList<>();
        if (content == null) return new String[0];
        for (int i, j = content.length(); j > 0; j = i) {
            i = content.lastIndexOf(separator, j - 1);
            final String str = content.substring(i != -1 ? i + separator.length() : 0, j);
            if (str.equals(endStr)) {
                list.addFirst(content.substring(0, j));
                break;
            } else {
                list.addFirst(str);
            }
        }
        return list.toArray(new String[0]);
    }

    public static String join(Collection<?> list, String delimiter, String left, String right) {
        return list.stream().map(it -> left + it + right).collect(Collectors.joining(delimiter));
    }

}
