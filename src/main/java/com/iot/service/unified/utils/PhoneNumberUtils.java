package com.iot.service.unified.utils;

import java.util.regex.Pattern;

/**
 * 手机号码工具类
 */
public class PhoneNumberUtils {
    
    /**
     * 中国移动生效号码段匹配模式
     */
    private static final String VALID_MOBILE_PATTERN = "^1(34[0-8]|3[5-9]|47|5[0-2]|5[7-9]|7[28]|8[2-4]|8[7-8]|9[578])\\d{8}$";
    
    /**
     * 判断手机号是否为中国移动号码
     * @param phoneNumber 手机号码
     * @return 是否为中国移动号码
     */
    public static boolean isChinaMobile(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() != 11) {
            return false;
        }
        
        return Pattern.matches(VALID_MOBILE_PATTERN, phoneNumber);
    }
} 