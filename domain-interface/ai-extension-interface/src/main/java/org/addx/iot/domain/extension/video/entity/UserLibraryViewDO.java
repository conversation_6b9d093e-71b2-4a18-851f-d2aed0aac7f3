package org.addx.iot.domain.extension.video.entity;

import lombok.Data;
import org.addx.iot.domain.extension.ai.vo.AiVideoObjectInfoResult;

import java.util.LinkedList;
import java.util.List;

/**
 * CREATE VIEW `user_library` AS
 * select
 * `dl`.`id` AS `id`,
 * `dl`.`date` AS `date`,
 * `dl`.`timestamp` AS `timestamp`,
 * `dl`.`serial_number` AS `serial_number`,
 * `dl`.`image_url` AS `image_url`,
 * `dl`.`video_url` AS `video_url`,
 * `dl`.`period` AS `period`,
 * `dl`.`file_size` AS `file_size`,
 * `dl`.`image_only` AS `image_only`,
 * `dl`.`device_name` AS `device_name`,
 * `dl`.`location_id` AS `location_id`,
 * `dl`.`location_name` AS `location_name`,
 * `ls`.`missing` AS `missing`,
 * `ls`.`marked` AS `marked`,
 * `dr`.`admin_id` AS `admin_id`,
 * `dr`.`admin_name` AS `admin_name`,
 * `dr`.`user_id` AS `user_id`,
 * `dr`.`user_name` AS `user_name`,
 * `dr`.`role` AS `role`
 * from ((`device_library` `dl`
 * join `library_status` `ls` on((`ls`.`library_id` = `dl`.`id`)))
 * join `device_role` `dr` on((`dr`.`serial_number` = `dl`.`serial_number`)));
 */
@Data
public class UserLibraryViewDO extends DeviceLibraryViewDO {
    Integer missing;
    Integer marked;
    Integer adminId;
    String adminName;
    Integer userId;
    String userName;
    Integer role;
    /**
     * 所属设备的用户是否vip
     */
    Boolean adminIsVip;

    String activityZoneName;

    String shareUserIds;

    Boolean supportMagicPix;

    List<AiVideoObjectInfoResult> objects;

    private List<UserLibraryViewDO> subVideos = new LinkedList<>();

    private List<SliceDetailVO> sliceList = new LinkedList<>();

}
