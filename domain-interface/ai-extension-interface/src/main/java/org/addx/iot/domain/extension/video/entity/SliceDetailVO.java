package org.addx.iot.domain.extension.video.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SliceDetailVO {

    /*
    "videoUrl": "https://api-local.safemo.com/download/o/v3/slice_4000_1_0.ts",
    "timestamp": 1730000002001 // 切片录制的开始时间，utc-millis
    "period": 2000, // 切片时长，millis
    "serviceName": "cxs"
     */
    private String videoUrl;
    private long timestamp;
    private int period;
    private int order;
    private Boolean isLast;
    private int fileSize;

}
