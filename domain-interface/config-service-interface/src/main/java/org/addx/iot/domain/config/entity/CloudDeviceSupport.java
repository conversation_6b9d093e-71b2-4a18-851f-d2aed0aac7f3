package org.addx.iot.domain.config.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.addx.iot.common.enums.PirServiceName;
import org.addx.iot.common.utils.TextUtil;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;
import org.addx.iot.domain.config.service.IDeviceEnumMappingService;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备支持项
 * 1、字段总数：62
 * 2、只来自设备上报：47。可以有默认值
 * 3、来自设备上报或 deviceModel ：9。不能有默认值！
 * 4、来自设备上报或 deviceStatus ：2。不能有默认值！
 * 5、来自设备上报或 老版设备上报 ：1。不能有默认值！
 * 6、设备上报的只读值 ：3。不能有默认值！supportPirSliceReport,batteryCode,voltameterType
 */
@Data
@ToString
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "设备支持项")
public class CloudDeviceSupport extends DeviceSupportCommon {
    /**
     * 是否支持云端拍摄时长，4g设备上报false。没有上报默认true
     */
    private Boolean supportPirRecordTime;
    /**
     * 是否支持google存储
     */
    @Builder.Default
    private Boolean supportGoogleStorage = false;
    /**
     * 是否支持cos
     */
    @Builder.Default
    private Boolean supportCos = false;
    /**
     * 是否支持oci
     */
    @Builder.Default
    private Boolean supportOci = false;

    private Integer supportSdCardCooldown; // 0 不支持 1:旧版sd卡 2:新版sd卡
    private List<String> supportSdCardVideoModes = Collections.emptyList();
    private List<String> supportSdCardCooldownSeconds = Collections.emptyList();

    private String iccid;

    /** 泛光灯需求新增加字段 start **/
    // 是否支持首页泛光能开关
    private Boolean supportManualFloodlightSwitch = false;
    // 是否支持首页泛光能亮度
    private Boolean supportManualFloodlightLuminance = false;
    // 是否支持定时计划开关
    private Boolean supportPlanFloodlightSwitch = false;
    // 是否支持定时计划亮度能力
    private Boolean supportPlanFloodlightLuminance = false;
    // 是否支持运动检测开关
    private Boolean supportTriggerFloodlightSwitch = false;
    // 运动检测间隔时间
    @Builder.Default
    private List<String> triggerFloodlightCooldownTimeOptions = Collections.EMPTY_LIST;
    // 灯光范围
    private DeviceAttributeIntRange floodlightLuminanceRange;

    private Boolean supportFloodlightMode;

    private List<String>  floodlightModeOptions;
    /** 泛光灯需求新增加字段 end **/

    // 是否支持直接上传算法图片帧给算法服务
    private Boolean supportSendAiImageDirect;

    @Builder.Default
    private Integer supportImageEvent = 0;

    @Schema(description = "新增的支持项都在这里", example = "{\n" +
            "                        \"supportSDCardslot\": 1\n" +
            "                    }")

    private Boolean supportVideo12Hours; // 录像支持12小时设置

    public static CloudDeviceSupport from(CloudDeviceRemoteDO remoteDO) {
        CloudDeviceSupport cloudDeviceSupport = CloudDeviceSupport.builder()
                .deviceSupportResolution(TextUtil.splitToNotBlankList(remoteDO.getDeviceSupportResolution(), ','))
                .deviceSupportAlarm(remoteDO.getDeviceSupportAlarm() > 0)
                .deviceSupportMirrorFlip(remoteDO.getDeviceSupportMirrorFlip())
                .supportWebrtc(remoteDO.getSupportWebrtc())
                .supportRecLamp(remoteDO.getSupportRecLamp())
                .supportVoiceVolume(remoteDO.getSupportVoiceVolume())
                .supportAlarmVolume(remoteDO.getSupportAlarmVolume())
                .supportLiveAudioToggle(remoteDO.getSupportLiveAudioToggle())
                .supportRecordingAudioToggle(remoteDO.getSupportRecordingAudioToggle())
                .supportLiveSpeakerVolume(remoteDO.getSupportLiveSpeakerVolume())
                .supportAlarmWhenRemoveToggle(remoteDO.getSupportAlarmWhenRemoveToggle())
                .postMotionDetectResult(remoteDO.getPostMotionDetectResult())
                .killKeepAlive(remoteDO.getKillKeepAlive() > 0)
                .supportCryDetect(remoteDO.getSupportCryDetect())
                .deviceDormancySupport(remoteDO.getDeviceDormancySupport())
                .p2pConnMgtStrategy(remoteDO.getP2pConnMgtStrategy())
                .supportAlexaWebrtc(remoteDO.getSupportAlexaWebrtc())
                .supportChangeCodec(remoteDO.getSupportChangeCodec())
                .supportPirRecordTime(remoteDO.getSupportPirRecordTime())
                .supportPirCooldown(remoteDO.getSupportPirCooldown())
                .supportResetVoltameter(remoteDO.getResetVolSupport() > 0)
                .supportStreamProtocol(remoteDO.getStreamProtocol())
                .supportCanStandby(remoteDO.getCanStandby())
                .supportKeepAliveProtocol(remoteDO.getKeepAliveProtocol())
                .supportAudioCodectype(remoteDO.getAudioCodectype())
                .supportDevicePersonDetect(remoteDO.getDevicePersonDetect())
                .supportDeviceCall(remoteDO.getSupportDeviceCall())
                .supportMechanicalDingDong(remoteDO.getSupportMechanicalDingDong() > 0)
                .supportChargeAutoPowerOn(remoteDO.getSupportChargeAutoPowerOn())
                .deviceSupportLanguage(TextUtil.splitToNotBlankList(remoteDO.getDeviceSupportLanguage(), ','))
                .antiflickerSupport(remoteDO.getAntiflickerSupport())
                .quantityCharge(remoteDO.getQuantityCharge())
                .supportPirSliceReport(remoteDO.getSupportPirSliceReport())
                .supportDoorBellRingKey(TextUtil.splitToIntList(remoteDO.getSupportDoorBellRingKey(), ','))
                .batteryCode(remoteDO.getBatteryCode())
                .voltameterType(remoteDO.getVoltameterType())
                .canRotate(remoteDO.getCanRotate())
                .supportMotionTrack(remoteDO.getSupportMotionTrack())
                .supportFrequency(remoteDO.getSupportFrequency())
                .antiDisassemblyAlarm(remoteDO.getAntiDisassemblyAlarm())
                .supportOtaAutoUpgrade(remoteDO.getSupportOtaAutoUpgrade())
                .pirSensitivityOptions(remoteDO.getPirSensitivityOptions())
                .pirRecordTimeOptions(remoteDO.getPirRecordTimeOptions())
                .sdCardPirRecordTimeOptions(remoteDO.getSdCardPirRecordTimeOptions())
                .pirCooldownTimeOptions(remoteDO.getPirCooldownTimeOptions())
                .videoResolutionOptions(remoteDO.getVideoResolutionOptions())
                .videoAntiFlickerFrequencyOptions(remoteDO.getVideoAntiFlickerFrequencyOptions())
                .liveStreamCodecOptions(remoteDO.getLiveStreamCodecOptions())
                .supportDoorbellPressNotifySwitch(remoteDO.getSupportDoorbellPressNotifySwitch())
                .supportGoogleStorage(remoteDO.getSupportGoogleStorage() > 0)
                .supportCos(remoteDO.getSupportCos() > 0)
                .supportOci(remoteDO.getSupportOci() > 0)
                .isShield(remoteDO.getIsShield())
                .supportWifiPowerLevel(remoteDO.getSupportWifiPowerLevel())
                .supportNetTest(remoteDO.getSupportNetTest())
                /*** 设备设置通用化改造二期 开始 */
                .supportLocalVideoLookBack(remoteDO.getSupportLocalVideoLookBack()) // 是否支持本地存储视频回看
                .localVideoStorageType(remoteDO.getLocalVideoStorageType()) // 本地视频存储类型
                .mechanicalDingDongDurationRange(remoteDO.getMechanicalDingDongDurationRange()) // 是否支持机械响铃时长控制
                .supportSdCardFormat(remoteDO.getSupportSdCardFormat()) // 是否支持SD卡格式化
                .supportSdCardCooldown(remoteDO.getSupportSdCardCooldown())
                .supportSdCardVideoModes(TextUtil.splitToNotBlankList(remoteDO.getSupportSdCardVideoModes(), ','))
                .supportSdCardCooldownSeconds(TextUtil.splitToNotBlankList(remoteDO.getSupportSdCardCooldownSeconds(), ','))
                .supportNightVisionSwitch(remoteDO.getSupportNightVisionSwitch()) // 是否支持夜视开关控制
                .nightVisionSensitivityOptions(remoteDO.getNightVisionSensitivityOptions()) // 是否支持夜视灵敏度控制
                .nightVisionModeOptions(remoteDO.getNightVisionModeOptions()) // 是否支持夜视模式调节
                .alarmDurationOptions(remoteDO.getAlarmDurationOptions()) // 警铃时长选项。当前：5s 10s 15s
                .alarmVolumeRange(remoteDO.getAlarmVolumeRange()) // 警铃音量调节范围
                .voiceVolumeRange(remoteDO.getVoiceVolumeRange()) // 提示音音量调节范围
                .liveSpeakerVolumeRange(remoteDO.getLiveSpeakerVolumeRange()) // 直播对讲音量调节范围
                .supportWhiteLight(remoteDO.getSupportWhiteLight()) // 是否支持白光灯
                .supportAlarmFlashLight(remoteDO.getSupportAlarmFlashLight()) // 是否支持报警闪光灯
                /*** 设备设置通用化改造二期 结束 */
                .supportRotateCalibration(remoteDO.getSupportRotateCalibration())
                .supportUnlimitedWebsocket(remoteDO.getSupportUnlimitedWebsocket())
                .supportIndoor(remoteDO.getSupportIndoor()) // 是否支持室内机功能
                .supportStarlightSensor(remoteDO.getSupportStarlightSensor())
                .supportPersonAi(remoteDO.getSupportPersonAi())
                .supportPetAi(remoteDO.getSupportPetAi())
                .pirType(remoteDO.getPirType())
                .flash(remoteDO.getFlash())
                .iccid(remoteDO.getIccid()==null ? "" : remoteDO.getIccid())
                /*** 泛光灯需求新增加字段 start */
                .supportManualFloodlightSwitch(remoteDO.getSupportManualFloodlightSwitch())
                .supportManualFloodlightLuminance(remoteDO.getSupportManualFloodlightLuminance())
                .supportPlanFloodlightSwitch(remoteDO.getSupportPlanFloodlightSwitch())
                .supportPlanFloodlightLuminance(remoteDO.getSupportPlanFloodlightLuminance())
                .supportTriggerFloodlightSwitch(remoteDO.getSupportTriggerFloodlightSwitch())
                .triggerFloodlightCooldownTimeOptions(remoteDO.getTriggerFloodlightCooldownTimeOptions())
                .floodlightLuminanceRange(remoteDO.getFloodlightLuminanceRange())
                .supportFloodlightMode(remoteDO.getSupportFloodlightMode())
                .supportSendAiImageDirect(remoteDO.getSupportSendAiImageDirect())
                .floodlightModeOptions(remoteDO.getFloodlightModeOptions())
                .supportImageEvent(remoteDO.getSupportImageEvent())
                /*** 泛光灯需求新增加字段 end */
                .supportVideo12Hours(remoteDO.getSupportVideo12Hour())
                .supportJson(remoteDO.getSupportJson())
                /** 归一化新加字段 */
                .supportVehicleAi(remoteDO.getSupportVehicleAi())
                .supportPackageAi(remoteDO.getSupportPackageAi())
                .supportFaceAi(remoteDO.getSupportFaceAi())
                .supportCrossCameraAi(remoteDO.getSupportCrossCameraAi())
                .supportOtherMotionAi(remoteDO.getSupportOtherMotionAi())
                .supportBxStorage(remoteDO.getSupportBxStorage())
                .wifiPowerModeOptions(remoteDO.getWifiPowerModeOptions())
                .supportOverallLightSwitch(remoteDO.getSupportOverallLightSwitch())
                .overallLightRange(remoteDO.getOverallLightRange())
                .supportEventAnalytics(remoteDO.getSupportEventAnalytics())
                .socModel(remoteDO.getSocModel())
                .wifiModel(remoteDO.getWifiModel())

                .build();
        return cloudDeviceSupport;
    }

    /**
     * 获取设备支持的云存储服务
     */
    public static Set<PirServiceName> getSupportServiceNames(CloudDeviceSupport support) {
        final Set<PirServiceName> set = new LinkedHashSet<>();
        set.add(PirServiceName.s3);
        if (support == null) return set;
        if (Optional.ofNullable(support.getSupportGoogleStorage()).orElse(false)) {
            set.add(PirServiceName.gcs);
        }
        if (Optional.ofNullable(support.getSupportCos()).orElse(false)) {
            set.add(PirServiceName.cos);
        }
        if (Optional.ofNullable(support.getSupportOci()).orElse(false)) {
            set.add(PirServiceName.oci);
        }
        set.add(PirServiceName.s3); // s3优先级在最后，负责兜底
        return set;
    }

    // 根据枚举值查找分辨率
    public static String getRecResolution(List<String> deviceSupportResolution, String newEnumName) {
        if (CollectionUtils.isEmpty(deviceSupportResolution) || newEnumName == null) return null;
        final Set<String> supportResolutionValue = deviceSupportResolution.stream()
                .map(it -> IDeviceEnumMappingService.oldEnumName2Value.getOrDefault(it, it)).collect(Collectors.toSet());
        return IDeviceEnumMappingService.value2NewEnum.entrySet().stream() // eg: {"640x360", "mid"}
                .filter(en -> supportResolutionValue.contains(en.getKey()))
                .filter(en -> newEnumName.equals(en.getValue()))
                .map(it -> it.getKey()).findFirst().orElse(null);
    }

    // 根据分辨率获取枚举值选项
    public static List<String> getVideoResolutionOptions(List<String> deviceSupportResolution) {
        if (CollectionUtils.isEmpty(deviceSupportResolution)) return Collections.emptyList();
        return deviceSupportResolution.stream()
                .map(it -> IDeviceEnumMappingService.oldEnumName2Value.getOrDefault(it, it))
                .map(it -> IDeviceEnumMappingService.value2NewEnum.get(it)).filter(it -> it != null)
                .collect(Collectors.toList());
    }

    public static boolean getSupportSnapshotRecording(CloudDeviceSupport deviceSupport) {
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportSnapshotRecording")).orElse(false);
    }

    public static boolean getSupportEventRecordingDualViewSwitch(CloudDeviceSupport deviceSupport) {
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportEventRecordingDualViewSwitch")).orElse(false);
    }

    public static boolean getSupportNuisanceAnimalDetect(CloudDeviceSupport deviceSupport){
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportNuisanceAnimalDetect")).orElse(false);
    }

    public static boolean getSupportBirdDetect(CloudDeviceSupport deviceSupport){
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportBirdDetect")).orElse(false);
    }

    public static boolean getSupportShutterRemote(CloudDeviceSupport deviceSupport){
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportShutterRemote")).orElse(false);
    }

    public static boolean getSupportTimeWatermark(CloudDeviceSupport deviceSupport) {
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportTimeWatermark")).orElse(false);
    }

    public static boolean getSupportLogoWatermark(CloudDeviceSupport deviceSupport) {
        return Optional.ofNullable(deviceSupport).map(it -> it.getSupportJson().getBoolean("supportLogoWatermark")).orElse(false);
    }
}
