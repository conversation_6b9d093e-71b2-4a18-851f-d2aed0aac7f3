package org.addx.iot.domain.config.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DeviceFixedAttributesCommon {
    private String userSn;
    private String modelNo;
    private String displayModelNo;
    private Integer modelCategory;
    private Integer canStandBy;
    private Boolean quantityCharge;
    private String macAddress; // 无线mac地址
    private String wiredMacAddress; // 有线mac地址
    private String icon;
    private String smallIcon;
    private Boolean supportOtaAutoUpgrade;
    private Integer activatedTime;
    private Integer activated;
    private Integer role;
    private String roleName;

    /*** deviceSupport中需要透传给设备的支持项 开始 */
    private Boolean deviceSupportAlarm; // 是否支持警铃
    private Integer supportWebrtc; // 是否支持webRtc
    private Integer supportRecLamp; // 是否支持录像指示灯
    private Integer supportVoiceVolume; // 是否支持提示音音量调节
    private Integer supportAlarmVolume; // 是否支持警铃音量调节
    private Integer supportLiveAudioToggle; // 是否支持直播录音开关设置
    private Integer supportRecordingAudioToggle; // 是否支持sd卡直播录音开关设置
    private Integer supportLiveSpeakerVolume; // 是否支持对讲音量设置
    private Integer supportAlarmWhenRemoveToggle; // 是否支持门铃拆除时报警开关
    private Integer p2pConnMgtStrategy; // p2p连接消息策略
    private Integer supportAlexaWebrtc; // 是否支持alexa
    private Integer resetVolSupport; // 是否支持alexa
    private Boolean supportResetVoltameter; // 是否支持重置电量计
    private String supportKeepAliveProtocol; // 保活协议, ps: tcp/udp
    private Integer supportDeviceCall; // 是否支持设备呼叫
    private Integer supportChargeAutoPowerOn; // 是否支持充电自动开机
    private Integer canRotate; // 是否支持摇头,0:否1:是
    private Integer supportMotionTrack; // 是否支持运动追踪
    private Integer supportFrequency; // 是否支持频率
    private Integer antiDisassemblyAlarm; // 是否支持防拆除警报
    private Boolean supportDoorbellPressNotifySwitch; // 是否支持按铃通知开关
    private Boolean isShield; // 是否带屏蔽罩
    private Boolean supportWifiPowerLevel; // 是否支持wifi发射功率档位调节
    private Boolean supportNetTest; // 是否支持网络测试
    /*** deviceSupport中需要透传给设备的支持项 结束 */

    /*** 设备设置通用化改造二期 开始 */
    private Boolean supportLocalVideoLookBack; // 是否支持本地存储视频回看
    private Integer localVideoStorageType; // 本地视频存储类型
    private DeviceAttributeIntRange mechanicalDingDongDurationRange; // 是否支持机械响铃时长控制
    private Boolean supportSdCardFormat; // 是否支持SD卡格式化
    private Boolean supportNightVisionSwitch; // 是否支持夜视开关控制
    private List<String> nightVisionSensitivityOptions; // 是否支持夜视灵敏度控制
    private List<String> nightVisionModeOptions; // 是否支持夜视模式调节
    private DeviceAttributeIntRange alarmVolumeRange; // 警铃音量调节范围
    private DeviceAttributeIntRange voiceVolumeRange; // 提示音音量调节范围
    private Boolean supportWhiteLight; // 是否支持白光灯
    private Boolean supportAlarmFlashLight; // 是否支持报警闪光灯
    /*** 设备设置通用化改造二期 结束 */
    private Boolean supportRotateCalibration;
    private Boolean supportUnlimitedWebsocket; // 是否支持websocket长链接
    private Boolean supportIndoor; // 是否支持室内机功能。兼容处理：supportIndoor=modelCategory==门铃
    private Boolean supportStarlightSensor; // 是否支持星光镜头
    private Boolean supportPirAi;

    private Boolean supportMagicPix;
}