package org.addx.iot.domain.config.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.addx.iot.common.utils.DeviceCodecUtil;
import org.addx.iot.domain.config.model.DeviceAttributeIntRange;

import java.util.Collections;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRemoteDO {
    protected String serialNumber;
    protected String SN;
    protected String MAC;
    protected Integer battery;
    protected Integer charge;
    protected Integer live;
    protected Integer wifiRssi;
    /*** wifi发射功率单位。int值，范围:[1,10] */
    protected Integer wifiPowerLevel;
    protected String ap;
    protected String deviceType;
    protected String version;
    protected String mcuVersion;
    protected String ip;
    protected Integer wifiChannel;

    // 是否支持webrtc
    protected Integer supportWebrtc = 0;

    // 是否支持录像指示灯
    protected Integer supportRecLamp = 0;

    // 是否支持提示音音量设置
    protected Integer supportVoiceVolume = 0;

    // 是否支持警铃音音量设置
    protected Integer supportAlarmVolume = 0;

    // 是否支持直播录音开关设置
    protected Integer supportLiveAudioToggle = 0;

    // 是否支持录播录音开关设置
    protected Integer supportRecordingAudioToggle = 0;

    // 是否支持对讲音量设置
    protected Integer supportLiveSpeakerVolume = 0;

    // 直播录音开关状态
    protected Integer liveAudioToggleOn = -1;

    // 录播录音开关状态
    protected Integer recordingAudioToggleOn = -1;

    // 设备当前直播对讲音量
    protected Integer liveSpeakerVolume = -1;

    // 是否支持门铃拆除时报警开关
    protected Integer supportAlarmWhenRemoveToggle = 0;

    // 门铃拆除时报警开关状态
    protected Integer alarmWhenRemoveToggleOn = -1;

    // 是否上报本地运动检测结果
    protected Integer postMotionDetectResult = 0;

    // 是否支持哭声检测
    protected Integer supportCryDetect = 0;

    // 是否支持拍摄间隔
    protected Integer supportPirCooldown = 0;

    // 是否支持pir切片上报
    protected Integer supportPirSliceReport = null; // 不能有默认值!

    private String batterySn;

    private String batteryModelNo;

    private Integer batteryRemoval;


    @JsonProperty("SN")
    public String getSN() {
        return SN;
    }

    public void setSN(String SN) {
        this.SN = SN;
    }

    @JsonProperty("MAC")
    public String getMAC() {
        return MAC;
    }

    public void setMAC(String MAC) {
        this.MAC = MAC;
    }

    protected JSONObject sdCardStatus;
    @Builder.Default
    Integer whiteLight = 0;

    protected String deviceSupportLanguage;
    /**
     * 是否支持抗频闪
     */
    @Builder.Default
    protected Integer antiflickerSupport = 0;

    /**
     * 支持的分辨率
     */
    @Builder.Default
    protected String deviceSupportResolution = "720P,1080P";
    /**
     * 是否支持警铃
     */
    @Builder.Default
    protected Integer deviceSupportAlarm = 1;
    /**
     * 是否精确电量
     */
    @Builder.Default
    protected Integer quantityCharge = 0;

    /**
     * 是否支持视频反转
     */
    @Builder.Default
    protected Boolean deviceSupportMirrorFlip = false;
    /**
     * 固件构建号(后N位)
     */
    protected String displayGitSha;
    /**
     * 是否支持 killKeepAlive,默认不支持
     */
    @Builder.Default
    protected Integer killKeepAlive = 0;

    /**
     * 是否支持设备休眠计划
     */
    @Builder.Default
    protected Integer deviceDormancySupport = 0;

    /**
     * 是否使用p2p
     */
    @Builder.Default
    protected Integer p2pConnMgtStrategy = 0;

    /**
     * 是否支持alexa webrtc
     */
    protected Integer supportAlexaWebrtc = -1;

    /**
     * 是否支持codec切换
     */
    protected Integer supportChangeCodec = -1;

    /**
     * 视频编码格式h264/h265
     */
    protected String codec = DeviceCodecUtil.H264;

    @Builder.Default
    protected Integer resetVolSupport = 0;

    /**
     * 充电方式
     */
    @Builder.Default
    protected Integer chargingMode = 0;

    /**
     * 设备支持的流协议,ps: rtsp
     */
    protected String streamProtocol;
    /**
     * 是否支持休眠,0否1是
     */
    protected Integer canStandby;
    /**
     * 保活协议, ps: tcp/udp
     */
    protected String keepAliveProtocol;
    /**
     * 是否支持设备端人型检测,0否1是
     */
    protected Integer devicePersonDetect;
    /**
     * 音频编码格式
     */
    protected String audioCodectype;
    /**
     * 当前门铃铃音Key
     */
    @Builder.Default
    protected Integer doorBellRingKey = 0;
    /**
     * 设备支持的门铃铃音
     */
    @Builder.Default
    protected String supportDoorBellRingKey = "";

    // 是否支持设备呼叫
    protected Integer supportDeviceCall = -1;
    /**
     * 是否支持机械叮咚 0否1是
     */
    protected Integer supportMechanicalDingDong = 0;

    // 是否支持充电自动开机
    protected Integer supportChargeAutoPowerOn;

    // 设备会不会发request-deviceSupport消息
    @Builder.Default
    protected Integer hasDeviceSupport = 0;

    // request-deviceSupport新加字段。
    protected String batteryCode;
    // request-deviceSupport新加字段。
    protected Integer voltameterType;

    protected Integer canRotate = null; // 是否支持摇头,0:否1:是 // 不能有默认值!
    protected Integer supportMotionTrack = null; // 是否支持运动追踪 // 不能有默认值!
    protected Integer supportFrequency = null; // 是否支持频率 // 不能有默认值!
    protected Integer antiDisassemblyAlarm = null; // 是否支持防拆除警报 // 不能有默认值!

    @ApiModelProperty("是否支持ota自动升级")
    @Builder.Default
    protected Boolean supportOtaAutoUpgrade = false;

    /* 2.运动检测-灵敏度*/
    protected List<String> pirSensitivityOptions = Collections.emptyList();
    /* 3.运动检测-录制时长*/
    protected List<String> pirRecordTimeOptions = Collections.emptyList();
    /* 3.sdCard运动检测-录制时长*/
    protected List<String> sdCardPirRecordTimeOptions = Collections.emptyList();
    /* 5.运动检测-触发间隔时间*/
    protected List<String> pirCooldownTimeOptions = Collections.emptyList();
    /* 8.视频-分辨率*/
    protected List<String> videoResolutionOptions = Collections.emptyList();
    /* 11.视频-抗频闪频率*/
    protected List<String> videoAntiFlickerFrequencyOptions = Collections.emptyList();
    /* 12.音视频流联动(直播流编码)*/
    protected List<String> liveStreamCodecOptions = Collections.emptyList();
    /* 是否支持按铃通知开关*/
    @ApiModelProperty("是否支持按铃通知开关")
    protected Boolean supportDoorbellPressNotifySwitch;

    /* 是否带屏蔽罩*/
    protected Boolean isShield;
    /* 是否支持wifi发射功率档位调节*/
    protected Boolean supportWifiPowerLevel = false;
    /* 是否支持网络测试*/
    protected Boolean supportNetTest = false;

    @ApiModelProperty("设备实时联网方式")
    protected Integer deviceNetType; // status上报的设备实时联网方式。0-WIFI,1-有线

    /*** 设备设置通用化改造二期 开始 */
    protected Boolean supportLocalVideoLookBack; // 是否支持本地存储视频回看
    protected Integer localVideoStorageType; // 本地视频存储类型
    protected DeviceAttributeIntRange mechanicalDingDongDurationRange; // 是否支持机械响铃时长控制
    protected Boolean supportSdCardFormat; // 是否支持SD卡格式化
    protected Boolean supportNightVisionSwitch; // 是否支持夜视开关控制
    protected List<String> nightVisionSensitivityOptions = Collections.EMPTY_LIST; // 是否支持夜视灵敏度控制
    protected List<String> nightVisionModeOptions = Collections.EMPTY_LIST; // 是否支持夜视模式调节
    protected List<String> alarmDurationOptions = Collections.EMPTY_LIST; // 警铃时长选项。当前：5s 10s 15s
    protected DeviceAttributeIntRange alarmVolumeRange; // 警铃音量调节范围
    protected DeviceAttributeIntRange voiceVolumeRange; // 提示音音量调节范围
    protected DeviceAttributeIntRange liveSpeakerVolumeRange; // 直播对讲音量调节范围
    protected Boolean supportWhiteLight; // 是否支持白光灯
    protected Boolean supportAlarmFlashLight; // 是否支持报警闪光灯
    /*** 设备设置通用化改造二期 结束 */

    protected Boolean supportRotateCalibration = false;
    protected Boolean supportIndoor; // 是否支持室内机功能。兼容处理：supportIndoor=modelCategory==门铃
    protected Boolean supportStarlightSensor; // 是否支持星光镜头

    protected Boolean supportUnlimitedWebsocket; // 是否支持websocket长链接

    protected Boolean supportPersonAi;
    protected Boolean supportPetAi;

    protected Boolean supportVehicleAi;
    protected Boolean supportPackageAi;
    protected Boolean supportFaceAi;
    protected Boolean supportCrossCameraAi;
    protected Boolean supportOtherMotionAi;

    protected Integer supportBxStorage; // 是否支持bx文件上传，对应serviceName="bxs"
    protected List<String> wifiPowerModeOptions; // "wifiPowerModeOptions": [ "default", "turbo" ] // 设备上报的wifi功率模式选项
    protected Boolean supportOverallLightSwitch; // 是否支持总体灯光开关
    protected DeviceAttributeIntRange overallLightRange; // 总体灯光强度范围
    protected Boolean supportEventAnalytics; // 是否支持埋点上报
    protected String socModel; // 基站SOC型号
    protected String wifiModel; // 基站WiFi型号

    private String solarSn;

    private String solarModelNo;

    private String solarOriginalModelNo;

    protected String pirType; // "pirType":"P824M"
    protected String flash; // "flash":"XM25QH128C"
    protected JSONObject supportJson;
}
