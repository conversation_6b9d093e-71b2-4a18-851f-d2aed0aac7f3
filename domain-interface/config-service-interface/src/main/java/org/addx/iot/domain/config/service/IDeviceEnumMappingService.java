package org.addx.iot.domain.config.service;

import com.google.common.collect.ImmutableMap;
import org.addx.iot.domain.config.param.DeviceModelEnumMappingDO;
import org.addx.iot.domain.config.vo.OptionEnumMapping;

import java.util.List;

public interface IDeviceEnumMappingService {
    ImmutableMap<String, String> oldEnumName2Value = ImmutableMap.<String, String>builder()
            .put("360P", "640x360") // mid
            .put("480P", "640x480") // mid，较少
            .put("720P", "1280x720") // mid
            .put("1080P", "1920x1080") // high
            .put("1536P", "2048x1536") // 2k，较少
            .put("1296P", "2304x1296") // 2k，较少
            .put("1440P", "2560x1440") // 2k
            .put("2160P", "3840x2160") // 4k，较少
            .put("4320P", "7680x4320") // 4k，较少
            .build();

    ImmutableMap<String, String> value2NewEnum = ImmutableMap.<String, String>builder()
            .put("640x360", "mid") // mid
            .put("640x480", "mid") // mid，较少
            .put("1280x720", "mid") // mid
            .put("1920x1080", "high") // high
            .put("2048x1536", "2k") // 2k，较少
            .put("2304x1296", "2k") // 2k，较少
            .put("2560x1440", "2k") // 2k
            .put("3840x2160", "4k") // 4k，较少
            .put("7680x4320", "4k") // 4k，较少
            .build();

    OptionEnumMapping getDefaultEnumMapping();

    OptionEnumMapping buildEnumMapping(List<DeviceModelEnumMappingDO> list);

    OptionEnumMapping getEnumMappingByModelNo(String modelNo);
}
