device:
  setting:
    mirrorFlip:
      CG621-W-THR: 1
      CQ240B-JS2: 1
      CG623-W-THR: 1
      CG623-W-8102: 1
      CG623-W-8202: 1
      CG623B-W-THR: 1
      CG623B-W-8102: 1
      CG623B-W-8202: 1
      CG623B-W-8201: 1
      CG623B-W-MG: 1
      CG623B-W-CJJ: 1
      CG623B-W-XBT: 1
      CG623B-W-YFWL: 1
      CG623B-W-SS: 1
      CG623B-W-CS3: 1
      CG623B-W-SL: 1
      CG623B-W-ST1B: 1
      CG623B-W-TNBD: 1
      CG623B-W-CS4: 1
      CG623B-W-ST1BQJ: 1
      CG623B-W-TNBDWX: 1
      CG623C-WSK1-YFWL: 1
      CG623C-WSK2-SL: 1
      CG623C-WSK1-THR: 1
      CG623C-WSK1-8202: 1
      CG623C-WSK1-8201: 1
      CG623C-WSK1-8102: 1
      CG623B-W-ST1BDC: 1
      CG623B-W-ST1BMB: 1
      CG623C-ANBD: 1
      CG623C-JSBD-CS: 1
      CG623C-JSBD: 1
      CG623C-JSBD-AB: 1
      CG623C-ST1B: 1
      CG623C-ST1BQJ: 1
      CG623C-ST1DC: 1
      CG623C-ST1BMB: 1
      CG623C-TNBD-SS: 1
      CG623C-TNBD-WX: 1
      CG623C-TNBD: 1
      CG623C-ANBD-SS: 1
      CG623C-JSBD-CZ: 1
      CG623C-TNBD-TJ: 1
      CG623C-WSK2-SW: 1
      CG623C-WSK2-SL1: 1
    motionSensitivity:
      CG122-JS: 2
      CG122: 2
      CG122-DC: 2
      CG122-FTK: 2
      CG122-CA: 2
      CG122-JA: 2
      CG122-ST: 2
      CG122-FTK-THR: 2
      CG122-WSK-THR: 2
      CG122-ST-ITRON: 2
      CG122-VT: 2
      CG122-JS-BTN: 2
      CG122-WSK: 2
      CG122-DCR: 2
      CG621-W-TN: 2
      CG621-W-HDG: 2
      DB121A: 2
      DB121B: 2
      DB121A1: 2
      DB121B1: 2
      DB121C: 2
      CQ121B-TN-YTY: 2
      CG522-TN-YSY: 2
      CQ121C-TN-YTY: 2
      CG623C-WSK2-SL2: 3
      CG623B-W-ST1BQJ: 3
      CG623G-WSK2-SL2: 3
      CG623G-TPBD2: 3
      CG623G-TPBD: 3
      CG623G-TPBD1: 3
      CG623G-TPBD2WX: 3
      CG623G-TPBD3: 3
      CG625-BD-TP2: 3
      CG625-BD-TP: 3
      CG625-BD-W1: 3
      CG625-BD-TP3: 3
      CG625-BD-TP4: 3
      CB260D1-LB-QJ1: 2
      CL060D-QJ2: 2
      CG625A2-TN6: 3
      CG625-BD1-TP: 3
      CG625-BD1-TP1: 3
      CG625-BD1-TP2: 3
      CG625-BD1-TP3: 3
      CG625-BD1-TP4: 3
      CG625-BD1-TP5: 3
      CG625-BD1-TP6: 3
      CG625-BD1-TP7: 3
      CG625-BD1-TP8: 2
      CG625-BD1-W1: 3
      CG625-BD1-TP9: 3
      CG625-BD2-TP: 3
      CG625-BD2-TP1: 3
      CG625-BD2-TP2: 3
      CG625-BD2-TP3: 3
      CG625-BD2-TP4: 3
      CG625-BD2-TP5: 3
      CG625-BD2-TP6: 3
      CG625-BD2-TP7: 3
      CG625-BD2-TP9: 3
      CG625-BD2-TP10: 3

    antiflickerSwitch: ["CB060-AN1","CB060-JS1","CB060-XF2","CB060-XF1","CB060-TN2","CB060-TN4","CB060-TN1","CB060-XF4","CB060-XF3","CB060","CB160-JS2","CB160-A4X","CB160-JS1","CB160","CB260-SS1","CB260","CB260-A4X","CB260-TN1","CK160-A4X","CK160","CK160B","CK160A1","CK160B-A4X","CK160A1-A4X","CK160-JS","CB060-LB-XF2","CB060-LB-XF1",
                        "CB260-ST1","CK160C","CK160C-A4X"
    ]
    chargeAutoPowerOnSwitch: ["CG625-BD1-TP9","CG625-BD2-TP9"]
    recLamp:
      KF126-TKW: 0
      KF126-P7G1: 0
      KF126-P7G2: 0
    enableOtherMotionAi:
      KF126-TKW: 1
      KF126-P7G1: 1
      KF126-P7G2: 1
      # 物模型里的属性配置，可以按型号覆盖
    thingModelProperty:
      SS1211B1:
        storeSwitch:
          defaultValue: "3"
          defaultOptions: [ "0", "1", "2", "3" ]
        snapshotRecordingSwitch:
          defaultValue: 1
        eventRecordingDualViewSwitch:
          defaultValue: 1
      SS1211W1:
        storeSwitch:
          defaultValue: "3"
          defaultOptions: [ "0", "1", "2", "3" ]
        snapshotRecordingSwitch:
          defaultValue: 1
        eventRecordingDualViewSwitch:
          defaultValue: 1
      SS1311B1:
        storeSwitch:
          defaultValue: "3"
          defaultOptions: [ "0", "1", "2", "3" ]
        snapshotRecordingSwitch:
          defaultValue: 1
        eventRecordingDualViewSwitch:
          defaultValue: 1
      SS1311W1:
        storeSwitch:
          defaultValue: "3"
          defaultOptions: [ "0", "1", "2", "3" ]
        snapshotRecordingSwitch:
          defaultValue: 1
        eventRecordingDualViewSwitch:
          defaultValue: 1


