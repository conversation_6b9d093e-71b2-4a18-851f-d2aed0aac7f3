{"properties": [{"identifier": "overallLightIntensity", "name": "总体灯光强度范围", "type": "RANGE", "defaultValue": 100, "optionName": "overallLightRange", "dataType": {"type": "int"}}, {"identifier": "overallLightSwitch", "name": "总体灯光开关", "type": "SWITCH", "defaultValue": 1, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "wifiPowerMode", "name": "wifi模式", "type": "ENUM", "defaultValue": "default", "optionName": "wifiPowerModeOptions", "dataType": {"type": "string"}}, {"identifier": "pushSwitch", "name": "推送开关", "type": "SWITCH", "defaultValue": 1, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "messageMergeSwitch", "name": "消息合并开关", "type": "SWITCH", "defaultValue": 1, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "enableSmartAlertProSwitch", "name": "开启专业推送开关", "type": "SWITCH", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "storeSwitch", "name": "存储到camera开关", "type": "BITENUM", "supportName": "supportStorage", "defaultValue": "3", "convertFunction": "convertToInteger", "defaultOptions": ["2", "3"]}, {"identifier": "enableAllRecordMotionsInDark", "name": "黑暗录制开关", "type": "SWITCH", "defaultValue": 1, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "snapshotCaptureInterval", "name": "camra在白天和晚上自动按不同间隔进行拍摄", "supportName": "supportCaptureInterval", "optionName": "captureIntervalOptions", "type": "ENUM", "defaultValue": "5s", "dataType": {"type": "string"}}, {"identifier": "snapshotRecordingSwitch", "name": "快照开关", "supportName": "supportSnapshotRecording", "type": "SWITCH", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "eventRecordingDualViewSwitch", "name": "事件录像双画面开关", "supportName": "supportEventRecordingDualViewSwitch", "type": "SWITCH", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "offZoneRecordingSwitch", "name": "Motion外部不录像 设置开关", "type": "SWITCH", "supportName": "supportOffZoneRecording", "defaultValue": 1, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "detectBirdAi", "name": "鸟类检测开关", "type": "SWITCH", "supportName": "supportBirdDetect", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "detectNuisanceAnimalAi", "name": "小动物检测开关", "type": "SWITCH", "supportName": "supportNuisanceAnimalDetect", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "shutterRemoteFeatureSwitch", "name": "无线快门设置", "type": "SWITCH", "supportName": "supportShutterRemote", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "critical<PERSON><PERSON><PERSON>", "name": "Critical Alerts", "type": "SWITCH", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "nuisanceAnimalAlarmSwitch", "name": "小动物告警设置开关", "type": "SWITCH", "supportName": "supportNuisanceAnimalAlarm", "defaultValue": 0, "dataType": {"type": "bool", "specs": {"0": "关闭", "1": "开启"}}}, {"identifier": "nuisanceAnimalAlarmDuration", "name": "小动物告警设置开关", "type": "ENUM", "supportName": "supportNuisanceAnimalAlarm", "defaultValue": "5s", "convertFunction": "convertDuration", "defaultOptions": ["5s", "10s", "15s"]}], "supports": [{"identifier": "supportSDCardslot", "name": "是否支持SD卡", "type": "int"}, {"identifier": "supportSnapshotRecording", "name": "是否支持快照录像", "type": "bool"}, {"identifier": "supportEventRecordingDualViewSwitch", "name": "是否支持事件录像的双画面功能", "type": "bool"}, {"identifier": "deviceDualViewType", "name": "摄像头支持双画面直播类型（电子 or 物理)", "type": "int"}, {"identifier": "supportCaptureInterval", "name": "是否支持自动间隔", "type": "bool"}, {"identifier": "captureIntervalOptions", "name": "自动间隔列表", "type": "array"}, {"identifier": "deviceDualViewInfo", "name": "直播双画面分辨率", "type": "array"}, {"identifier": "supportStorage", "name": "支持存储类型", "type": "int"}, {"identifier": "wifiPowerModeOptions", "name": "设备上报的wifi功率模式选项", "type": "array"}, {"identifier": "overallLightRange", "name": "总体灯光强度范围", "type": "object"}, {"identifier": "supportChangeNetworkBind", "name": "是否支持换网绑定", "type": "bool"}, {"identifier": "supportBirdDetect", "name": "是否支持端上鸟检测", "type": "bool"}, {"identifier": "supportNuisanceAnimalDetect", "name": "是否支持端上小动物检测", "type": "bool"}, {"identifier": "supportNuisanceAnimalAlarm", "name": "是否支持端上小动物告警", "type": "bool"}, {"identifier": "supportShutterRemote", "name": "是否支持端上远程快门控制", "type": "bool"}, {"identifier": "supportOffZoneRecording", "name": "是否支持Motion外部不录像功能", "type": "int"}, {"identifier": "panTiltSpeedIntRange", "name": "速率范围", "type": "object"}, {"identifier": "supportLinode", "name": "是否支持linode", "type": "int"}, {"identifier": "supportAlarmDelay", "name": "是否支持alarmDelay", "type": "bool"}]}