server:
  shutdown: graceful
  #本地自测时注释掉address，以便基站上的其他程序可以访问到
  address: 127.0.0.1
  port: 7777
  #tomcat配置: 60k*800ms/5min
  tomcat:
    # 核心池线程数,默认10
    min-spare-threads: 30
    # 最大线程数,默认200
    max-threads: 200
    # 允许最大连接数,默认10000,当达到临界值时,系统可能会基于accept-count继续接受连接
    max-connections: 10000
    # 当所有线程都在使用时,建立连接的请求的等待队列长度,默认100
    accept-count: 4000
  http2:
    enabled: true

spring:
  aop:
    proxy-target-class: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    # url: ****************************************************
    url: **********************************************************************
    driver-class-name: org.sqlite.JDBC
    hikari:
      maximum-pool-size: 10 # hikari默认值
      minimum-idle: 2
      connection-init-sql: |
        PRAGMA journal_mode=WAL;
        PRAGMA busy_timeout=60000;
        PRAGMA synchronous=NORMAL;
        PRAGMA cache_size=10000;
        PRAGMA temp_store=MEMORY;

    dynamic:
      primary: camera
      datasource:
        camera:
          type: com.addx.iotcamera.db.CustomSQLiteDataSource
          url: **********************************************************************
          driver-class-name: org.sqlite.JDBC
        library:
          type: com.addx.iotcamera.db.CustomSQLiteDataSource
          url: ***********************************************************************************
          driver-class-name: org.sqlite.JDBC
  jmx:
    enabled: false # 避免hikari重复注册jmx报错
  profiles:
    active: test
    #本地自测时使用remote ，远程端口改为你的基站ip
    #active: remote
  redis:
    host: 127.0.0.1
    port: 6379
    password: ""
  lifecycle:
    timeout-per-shutdown-phase: 60s
  application:
    name: iot-camera
  servlet:
    multipart:
      # bx上的tomcat产生的临时文件句柄释放不了，应用层自己处理multipart
      enabled: false
      max-file-size: 10MB
  main:
    allow-bean-definition-overriding: true

mybatis:
  configuration:
    map-underscore-to-camel-case: true
#    lazy-loading-enabled: true

tempDir: '/addx/temp/'

storage:
  firmwareDir: '/addx/firmware'

factory:
  host: https://factory.addx.live
  port: 7777

timeout:
  token:
    user: 1209600
    device: 604800

smsali:
  signName: Vicoo
  accessKeyId: LTAI4FcSFX3b5tPY94pE5Gcs
  accessKeySecret: ******************************
  templateCode: SMS_172425225

wowconfig:
  retryInterval: 4
  retryCount: 12
  ban1Ref: 50
  ban2Ref: 49
  ban3Ref: 50


# http连接管理
http-mgr:
  pool:
    max: 200 # 最大连接数量
    defaultMaxPerRoute: 10 # 每个路由最大连接数量
    validateAfterInactivity: 2000 # 闲置的tcp连接发送keepalive的间隔，单位ms

  connectTimeout: 3000
  requestTimeout: 3000
  readTimeout: 3000

# Snowplow配置
snowplow:
  timeout:
    connectTimeoutSeconds: 10000  # 连接超时时间（秒）
    readTimeoutSeconds: 2000     # 读取超时时间（秒）
    writeTimeoutSeconds: 2000    # 写入超时时间（秒）

webrtc:
  ticket:
    validDurationMillis: 259200000 #3天

# 配置健康检查
management:
  endpoint:
    info:
      enabled: true
    health:
      show-details: always
    metrics:
      enabled: true
    threaddump:
      enabled: false
    loggers:
      enabled: true
  endpoints:
    web:
      exposure:
        include: info,health,metrics,prometheus,loggers
        exclude: shutdown,threaddump
  health:
    db:
      enabled: true
    diskspace:
      enabled: true
    redis:
      enabled: true



countly:
  app-type-to-tenant-id-to-params:
    iOS:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: bf83767885e01ae5f1e09a2b2a270d4c02f2ce27
    Android:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: 11af45a88650e97c9990ee81e3c7080708940d8e

zendesk-chat:
  app-id: 263638608207147009
  account-key: WxruMDytZwODUXaiYNivNA3u4nZqmCmp

open-api:
  node: test-cn
  auth-url:
    # test-cn环境连staging-cn的auth
    validate-request: http://81.70.126.101/auth/validate-request

zendesk:
  notSupportedTenantIdSet: provisionhome
  supportedTenantIdSet: vicoo,dzeeshome

thread-pools:
  'executorConfigs':
    # 默认线程池参数: 30*60ms/5min
    'commonPool':
      corePoolSize: 4
      maxPoolSize: 64
      keepAliveSeconds: 10
      queueCapacity: 100
      rejectedPolicy: 'java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy'
      threadNamePrefix: 'common-'
    'scheduledPool':
      corePoolSize: 1
      maxPoolSize: 8
      keepAliveSeconds: 10
      queueCapacity: 100
      rejectedPolicy: 'java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy'
      threadNamePrefix: 'scheduledPool-'
  'forkJoinConfigs':
    # 视频更新
    'videoUpdate':
      parallelism: 4

device-ap-info:
  versionMap:
    "00": "{\"ssid\":\"IPC_%s\",\"password\":\"%s\",\"safePassword\":\"%s\",\"asideServerIp\":\"***********\", \"asideServerPort\":\"23450\"}"
    "01": "{\"ssid\":\"IPC_%s\",\"password\":\"%s\",\"safePassword\":\"%s\",\"asideServerIp\":\"***********\", \"asideServerPort\":\"23450\"}"

# 耗电统计开关（默认配置开），放电时的耗电量n（默认5）、累计未上报天数m（10），充电时的充电量x（20）、累计未上报天数y（5）
power-stat:
  powerStatSwitch: true
  dischargeStatCapThresh: 5
  dischargeNonReportDays: 10
  chargingStatCapThresh: 20
  chargingNonReportDays: 5

video-generate:
  enable: false
  whiteSnSet:

net-test:
  defaultSupportNetTest: false
  iperf3:
    username: 'netTest'
    password: '9cAmPVaNB09wEZMR8JjjO1'
    servers:
      'cn': { title: '中国', ip: '**************', port: 5001 }
      'us': { title: '美国', ip: '************', port: 5001 }
      'eu': { title: '欧洲', ip: '***********', port: 5001 }
  traceroute:
    coturnPort: 5349

coturn:
  port: 3478

websocket:
  port: 8443
  logging: false
  ssl:
    # 第三方签名：*.safemo.com
    enabled: true
    key-alias: safemo
    key-store-type: JKS
    key-store: classpath:tls/safemo.com.jks
    key-store-password: dNePHn6Wb
    key-password: dNePHn6Wb
  max-content-length: 131072 # 128KB

iot-service:
  rootPath: 'http://localhost:${server.port}'
ai-service:
  rootPath: 'http://localhost:8888'

http-token:
  secret: 'N38Ywi2YR4qddOqXBjDRhQ=='

# kiss线程池配置
scheduled-executor:
  corePoolSize: 2
  keepAliveSeconds: 600

# iot-local接口域名配置
iot-local:
  nodeName: 'local'
  http:
    domain: 'api-local.safemo.com'
    port: 7777
  kiss:
    domain: ''
    port: 8443
    secret: 'q3m@ZQIS$@jpm3fv'
  coturn:
    domain: ''
    port: 3478
    secret: 'hxrMYtvC5Q1efUQn'

# grpc服务配置
grpc:
  saas-ai-service: 'localhost:50031'
  #连接bstationd server的地址
  client:
    GLOBAL:
      negotiation-type: plaintext
      # 修改客户端端默认入参最大大小，默认值为4M ，这里修改为20M   20*1024*1024
      max-inbound-message-size: 20971520
      # 客户端指定连接服务端地址
      address: 'static://localhost:51003'

globalconfig:
  tenant:
    - vicoo
  language:
    - zh
    - en
    - ja
    - de
    - fr
    - ru
    - it
    - es
    - fi
    - he
    - ar
    - vi
    - iw
    - pt
    - pl
    - "zh-Hant"
    - tr
    - cs

iot-local-ability:
  stationSupports:
    - name: Alexa
      value: 1
      version: 1
      ext: "{}"
    - name: smartAlert
      value: 1
      version: 2
      ext: "{}"
    - name: motionZone
      value: 1
      version: 2
      ext: "{}"

video-file:
  limit-library-db:
    singleVideoBytes: 1024 # 单个视频在library.db中约占1KB大小
    deleteVideoNum: 128 # 每次删除128个视频
    maxBytes: 2147483648 # 单个视频约占1MB空间。2T空间能存2048*1024个视频，需要libraryDB空间2G

logging:
  pattern:
    level: trace_id=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} %5p