
wowza:
  elb: ''

codebind:
  node-matcher:
    endpoint: 'https://node-matcher-test.addx.live'
    accessKey: '2vi9ZxRSW9J2xkZBrBO8H1'
    secretKey: 'TNzniwrZSW2j+TzwU23Piw=='

servernode: CN
nodeEvn: test
release:
  evn: test

videoslice:
  rootPath: ''
  roleArn: arn:aws-cn:iam::437416304740:role/test_bucket_access_role
  durationSeconds: 43200
  awsCertEndpoint: sns.cn-north-1.amazonaws.com.cn
  deviceS3AccelerateEnable: false

countly:
  app-type-to-tenant-id-to-params:
    iOS:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: bf83767885e01ae5f1e09a2b2a270d4c02f2ce27
    Android:
      default:
        countlyServer: https://countly.vicohome.io
        countlyKey: 11af45a88650e97c9990ee81e3c7080708940d8e

open-api:
  node: test-cn
  auth-url:
    # test-cn环境连staging-cn的auth
    validate-request: http://81.70.126.101/auth/validate-request

video-file:
  api:
    rootUrl: 'https://api-local.safemo.com'
  storage:
    rootPath: '/data/iot-service/'
    maxBytes: 17179869184 # 16G
    #    maxBytes: 1073741824 # 1G
    #    maxBytes: 52428800 # 50Mb
    #    maxBytes: 209715200 # 200Mb
    #    maxBytes: 209715200 # 200Mb
    #    maxBytes: 524288000 # 500Mb
    #    maxBytes: 1073741824 # 1Gb
    #    maxBytes: 2147483648 # 2Gb
  extStorage: # 外接磁盘
    rootPath: '/tmp/ssd/'
#    maxRatios: 0.1 # 最大使用10%的空间
    maxRatios: 0.8 # 最大使用80%的空间
  uploadAIImage:
    repository: '/data/iot-service/temp/uploadAIImage'
    sizeThreshold: 102400 # 100k，实际的AI图片,4k分辨率大约1M
    sizeMax: 10485760 # 10M
  clear:
    executeInterval: 300
    queryBatchNum: 500

video-config:
  storage:
    rootPath: '/data/iot-service/'
    minUsableBytes: 524288000 # 500*1024*1024 byte
    preserveBytes: 104857600 # 100*1024*1024 byte
  ssdStorage:
    minUsableBytes: 0 # ssd可以占满
  clearParams:
    epochQueryNum: 60 # 视频平均大小 1739451 byte, 104857600/1739451=60
  clearVideo:
    delay: 120
    interval: 180
    timeUnit: 'SECONDS'
  clearFile:
    delay: 1
    interval: 1
    timeUnit: 'HOURS'

http-token:
  secret: 'N38Ywi2YR4qddOqXBjDRhQ=='


# iot-local接口域名配置
iot-local:
  nodeName: 'local'
  http:
    domain: 'api-local.safemo.com'
  kiss:
    domain: 'signal-local.safemo.com'
    secret: 'q3m@ZQIS$@jpm3fv'
  coturn:
    domain: 'coturn-local.safemo.com'

grpc:
  saas-ai-service: 'localhost:50031'
  bstationd-service: 'localhost:51003'
  client:
    GLOBAL:
      negotiation-type: plaintext
      # 修改客户端端默认入参最大大小，默认值为4M ，这里修改为20M   20*1024*1024
      max-inbound-message-size: 20971520
      # 客户端指定连接服务端地址
      address: 'static://localhost:51003'
    alexa-grpc-server:
      address: 'static://localhost:51004'
      enableKeepAlive: true
      keepAliveTime: 30s
      keepAliveTimeout: 5s
      plaintext: true
    ai-station-grpc-server:
      address: 'static://localhost:50031'
      enableKeepAlive: true
      keepAliveTime: 30s
      keepAliveTimeout: 5s
      plaintext: true

log-masker:
  masks:
    token:
      - token:\sBearer\s([^,\s"$]+)
      - token:Bearer\s([^,\s"$]+)
      - token=Bearer\s([^,\s"$]+)
      - token\sBearer\s([^,\s"$]+)
      - token":"Bearer\s([^,\s"$]+)
      - token":\s"Bearer\s([^,\s"$]+)
      - token:\s([^,\s"$]+)
      - token:([^,\s"$]+)
      - token=([^,\s"$]+)
      - token\s([^,\s"$]+)
      - token":"([^,\s"$]+)
      - token":\s"([^,\s"$]+)
    email:
      - email:\s([^,\s"$]+)
      - email:([^,\s"$]+)
      - email=([^,\s"$]+)
      - email\s([^,\s"$]+)
      - email":"([^,\s"$]+)
      - email":\s"([^,\s"$]+)
    phone:
      - phone:\s([^,\s"$]+)
      - phone:([^,\s"$]+)
      - phone=([^,\s"$]+)
      - phone\s([^,\s"$]+)
      - phone":"([^,\s"$]+)
      - phone":\s"([^,\s"$]+)
    password:
      - password:\s([^,\s"$]+)
      - password:([^,\s"$]+)
      - password=([^,\s"$]+)
      - password\s([^,\s"$]+)
      - password":"([^,\s"$]+)
      - password":\s"([^,\s"$]+)
    wifiSsid:
      - wifiSsid:\s([^,\s"$]+)
      - wifiSsid:([^,\s"$]+)
      - wifiSsid=([^,\s"$]+)
      - wifiSsid\s([^,\s"$]+)
      - wifiSsid":"([^,\s"$]+)
      - wifiSsid":\s"([^,\s"$]+)
    wifiKey:
      - wifiKey:\s([^,\s"$]+)
      - wifiKey:([^,\s"$]+)
      - wifiKey=([^,\s"$]+)
      - wifiKey\s([^,\s"$]+)
      - wifiKey":"([^,\s"$]+)
      - wifiKey":\s"([^,\s"$]+)
    macAddress:
      - macAddress:\s([^,\s"$]+)
      - macAddress:([^,\s"$]+)
      - macAddress=([^,\s"$]+)
      - macAddress\s([^,\s"$]+)
      - macAddress":"([^,\s"$]+)
      - macAddress":\s"([^,\s"$]+)
    credential:
      - credential:\s([^,\s"$]+)
      - credential:([^,\s"$]+)
      - credential=([^,\s"$]+)
      - credential\s([^,\s"$]+)
      - credential":"([^,\s"$]+)
      - credential":\s"([^,\s"$]+)
    accessKey:
      - accessKey:\s([^,\s"$]+)
      - accessKey:([^,\s"$]+)
      - accessKey=([^,\s"$]+)
      - accessKey\s([^,\s"$]+)
      - accessKey":"([^,\s"$]+)
      - accessKey":\s"([^,\s"$]+)
    accessSecret:
      - accessSecret:\s([^,\s"$]+)
      - accessSecret:([^,\s"$]+)
      - accessSecret=([^,\s"$]+)
      - accessSecret\s([^,\s"$]+)
      - accessSecret":"([^,\s"$]+)
      - accessSecret":\s"([^,\s"$]+)
    objectImageBase64:
      - objectImageBase64:\s([^,\s"$]+)
      - objectImageBase64:([^,\s"$]+)
      - objectImageBase64=([^,\s"$]+)
      - objectImageBase64\s([^,\s"$]+)
      - objectImageBase64":"([^,\s"$]+)
      - objectImageBase64":\s"([^,\s"$]+)
    cropedImageBase64:
      - cropedImageBase64:\s([^,\s"$]+)
      - cropedImageBase64:([^,\s"$]+)
      - cropedImageBase64=([^,\s"$]+)
      - cropedImageBase64\s([^,\s"$]+)
      - cropedImageBase64":"([^,\s"$]+)
      - cropedImageBase64":\s"([^,\s"$]+)
    ip:
      - ip:\s([^,\s"$]+)
      - ip:([^,\s"$]+)
      - ip=([^,\s"$]+)
      - ip\s([^,\s"$]+)
      - ip":"([^,\s"$]+)
      - ip":\s"([^,\s"$]+)
