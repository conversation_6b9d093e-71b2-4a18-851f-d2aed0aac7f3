package com.addx.iotcamera.dao;

import com.addx.iotcamera.bean.db.ActivityZoneDO;
import com.addx.iotcamera.bean.domain.library.ActivityZone;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.context.annotation.Lazy;

import java.util.List;

@Repository @Lazy
public interface ActivityZoneDAO {
    @Select("SELECT `activity_zone`.`id`, " +
            "    `activity_zone`.`serial_number`, " +
            "    `activity_zone`.`zone_name`, " +
            "    `activity_zone`.`vertices`, " +
            "    `activity_zone`.`need_push`, " +
            "    `activity_zone`.`need_record`, " +
            "    `activity_zone`.`need_alarm` " +
            "FROM `activity_zone`" +
            "WHERE `serial_number` = #{serialNumber} " +
            "AND `deleted` = 0; ")
    List<ActivityZoneDO> getActivityZonesBySerailNumber(String serialNumber);

    @Select("SELECT `activity_zone`.`id`, " +
            "    `activity_zone`.`vertices` " +
            "FROM `activity_zone`" +
            "WHERE `serial_number` = #{serialNumber} " +
            "AND `deleted` = 0; ")
    List<ActivityZone> getActivityZonesBySerialNumberForAi(String serialNumber);

    @Select({"<script>SELECT `activity_zone`.`id`,`activity_zone`.`zone_name`,`activity_zone`.`serial_number`,vertices " +
            "FROM `activity_zone`" +
            "WHERE serial_number in " +
            "<foreach collection='serialNumbers' item='serialNumber' open='(' separator=',' close=')'>" +
            "    #{serialNumber}" +
            " </foreach> " +
            "AND `deleted` = 0 </script>"})
    List<ActivityZoneDO> getActivityZonesByUserId(@Param("serialNumbers") List<String> serialNumbers);

    @Insert("INSERT INTO `activity_zone` " +
            "(`serial_number`, " +
            "`zone_name`, " +
            "`vertices`, " +
            "`need_push`, " +
            "`need_record`, " +
            "`need_alarm`, " +
            "`deleted`) " +
            "VALUES " +
            "(#{serialNumber}, " +
            "#{zoneName}, " +
            "#{vertices}, " +
            "#{needPush}, " +
            "#{needRecord}, " +
            "#{needAlarm}, " +
            "0); ")
    @SelectKey(before = false, keyProperty = "id", resultType = Integer.class, statementType = StatementType.STATEMENT, statement = "SELECT last_insert_rowid() AS id;")
    Integer insertActivityZone(ActivityZoneDO activityZoneDO);

    @Update("UPDATE `activity_zone` " +
            "SET " +
            "`zone_name` = #{zoneName}, " +
            "`vertices` = #{vertices}, " +
            "`need_push` = #{needPush}, " +
            "`need_record` = #{needRecord}, " +
            "`need_alarm` = #{needAlarm} " +
            "WHERE `id` = #{id} " +
            "AND `serial_number` = #{serialNumber}; ")
    Integer updateActivityZone(ActivityZoneDO activityZoneDO);

    @Update("UPDATE `activity_zone` " +
            "SET " +
            "`deleted` = 1 " +
            "WHERE `id` = #{id} " +
            "AND `serial_number` = #{serialNumber}; ")
    Integer deleteActivityZone(ActivityZoneDO activityZoneDO);

    @Select("SELECT `activity_zone`.`id`, " +
            "    `activity_zone`.`serial_number`, " +
            "    `activity_zone`.`zone_name`, " +
            "    `activity_zone`.`vertices`, " +
            "    `activity_zone`.`need_push`, " +
            "    `activity_zone`.`need_record`, " +
            "    `activity_zone`.`need_alarm` " +
            "FROM `activity_zone`" +
            "WHERE `id` = #{id}; ")
    ActivityZoneDO getActivityZonesById(Integer id);

    @Update("UPDATE `activity_zone` SET `deleted` = 1 WHERE `serial_number` = #{serialNumber};")
    Integer deleteActivityZoneBySerialNumber(@Param("serialNumber") String serialNumber);
}
