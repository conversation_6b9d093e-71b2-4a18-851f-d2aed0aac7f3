package com.addx.iotcamera.bean.video;

import com.addx.iotcamera.util.TextUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedHashSet;
import java.util.Set;

@Data
@Accessors(chain = true)
public class VideoTagSummary {

    private String sn;
    private Set<String> tags = new LinkedHashSet<>();
    private Set<String> aiEdgeTags = new LinkedHashSet<>();
    private Set<String> doorbellTags = new LinkedHashSet<>();
    private Set<String> deviceCallEventTag = new LinkedHashSet<>();

    public VideoTagSummary setTags(String tags) {
        this.tags = TextUtil.splitToNotBlankSet(tags, ',');
        return this;
    }

    public VideoTagSummary setAiEdgeTags(String aiEdgeTags) {
        this.aiEdgeTags = TextUtil.splitToNotBlankSet(aiEdgeTags, ',');
        return this;
    }

    public VideoTagSummary setDoorbellTags(String doorbellTags) {
        this.doorbellTags = TextUtil.splitToNotBlankSet(doorbellTags, ',');
        return this;
    }

    public VideoTagSummary setDeviceCallEventTag(String deviceCallEventTag) {
        this.deviceCallEventTag = TextUtil.splitToNotBlankSet(deviceCallEventTag, ',');
        return this;
    }

    public VideoTagSummary merge(VideoTagSummary other) {
        if (other == null) return this;
        this.tags.addAll(other.getTags());
        this.aiEdgeTags.addAll(other.getAiEdgeTags());
        this.doorbellTags.addAll(other.getDoorbellTags());
        this.deviceCallEventTag.addAll(other.getDeviceCallEventTag());
        return this;
    }
}
