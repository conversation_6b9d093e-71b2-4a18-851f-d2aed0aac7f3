package com.addx.iotcamera.controller.device.operation;

import com.addx.biz.SecurityModeBiz;
import com.addx.biz.dto.SecurityModeInfo;
import com.addx.domain.settings.securitymode.model.SecurityMode;
import com.addx.iotcamera.bean.app.device.AppResetDeviceRequest;
import com.addx.iotcamera.bean.app.device.DeviceRemoveRequest;
import com.addx.iotcamera.bean.domain.DeviceAppSettingsDO;
import com.addx.iotcamera.constants.RequestAttributeKeys;
import com.addx.iotcamera.publishers.vernemq.exceptions.IdNotSetException;
import com.addx.iotcamera.service.BindService;
import com.addx.iotcamera.service.DeviceAuthService;
import com.addx.iotcamera.service.DeviceConfigService;
import com.addx.iotcamera.service.UserService;
import com.addx.iotcamera.service.alexa.AlexaSafemoRequestService;
import com.addx.iotcamera.service.device.DeviceResetService;
import com.addx.iotcamera.service.device.DeviceSettingService;
import com.addx.iotcamera.service.message.MessageToAppService;
import com.addx.iotcamera.service.openapi.OpenApiConfigService;
import com.addx.iotcamera.service.videofile.LocalSpaceService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.annotation.LogRequestAndResponse;
import org.addx.iot.common.enums.ResultCollection;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.core.IExtensionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.addx.iotcamera.service.BindService.RESET_DEFAULT_ACTION;
import static org.addx.iot.common.enums.ResultCollection.SUCCESS;

@Slf4j
@RestController
@Lazy
@Api("设备reset优化")
@RequestMapping("/device/reset")
public class DeviceResetController {

    @Autowired
    @Lazy
    private DeviceAuthService deviceAuthService;

    @Autowired
    @Lazy
    private DeviceConfigService deviceConfigService;

    @Autowired
    @Lazy
    private DeviceSettingService deviceSettingService;

    @Autowired
    @Lazy
    private BindService bindService;

    @Resource
    @Lazy
    IExtensionService extensionService;

    @Autowired
    @Lazy
    private LocalSpaceService localSpaceService;
    @Autowired
    @Lazy
    private AlexaSafemoRequestService alexaSafemoRequestService;


    @Autowired
    @Lazy
    private UserService userService;

    @Autowired
    @Lazy
    private OpenApiConfigService openApiConfigService;


    @Autowired
    @Lazy
    private SecurityModeBiz securityModeBiz;

    @Autowired
    @Lazy
    private MessageToAppService messageToAppService;

    @Autowired
    @Lazy
    private DeviceResetService deviceResetService;

    @Operation(summary = "重置摄像头属性", description = "-2002 没有权限 \n 0 正常")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "0", description = "重置成功"),
            @ApiResponse(responseCode = "-2002", description = "没有权限"),
    })
    @LogRequestAndResponse
    @PostMapping(value = "/resetCameraToDefault", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result resetCameraToDefault(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @io.swagger.v3.oas.annotations.parameters.RequestBody @RequestBody AppResetDeviceRequest deviceResetRequest) throws IdNotSetException {
        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, deviceResetRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        Result result = deviceSettingService.sendWakeupAndWait( deviceResetRequest.getSerialNumber());
        if(!Result.successFlag.equals(result.getResult())){
            return result;
        }


        bindService.resetToDefault(userId, deviceResetRequest.getSerialNumber() ,  deviceResetRequest.getLanguage(), false);

        Map<String, Object> map = new HashMap<>();
        map.put("action", "resetToDefault");
        result = deviceSettingService.sendCmd("resetToDefault", deviceResetRequest.getSerialNumber(), map);
        if(!Result.successFlag.equals(result.getResult())){
            return result;
        }


        return Result.Success();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/resetBstationToDefault", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result resetBstationToDefault(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody AppResetDeviceRequest deviceResetRequest) throws IdNotSetException, InterruptedException {
        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, deviceResetRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }


        bindService.resetToDefault(userId, deviceResetRequest.getSerialNumber(), deviceResetRequest.getLanguage(),true);

        SecurityModeInfo securityModeInfo = securityModeBiz.querySecurityModesByUid(userId);
        Integer selectedModeId = securityModeInfo.getSelectedModeId();

        Optional<SecurityMode> defaultMode = securityModeInfo.getSecurityModes().stream().filter(item -> item.getKey().equals(DeviceSettingService.DEFAULT_MODE_WHEN_NOT_INIT)).findFirst();
        //初始化一下
        if (defaultMode.isPresent()) {
            if (selectedModeId != null && !selectedModeId.equals(defaultMode.get().getId())) {
                securityModeBiz.switchMode(userId, defaultMode.get().getId());
                messageToAppService.sendStatusMsg(userId);
            }
        }

        DeviceAppSettingsDO appSettingsDO = new DeviceAppSettingsDO();
        appSettingsDO.setSerialNumber(deviceResetRequest.getSerialNumber());
        appSettingsDO.setAction(RESET_DEFAULT_ACTION);
        deviceSettingService.updateUserBxConfig(userId, appSettingsDO, System.currentTimeMillis());
        return Result.Success();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/removeBstation", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result removeBstation(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRemoveRequest deviceRemoveRequest) {
        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, deviceRemoveRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        log.info("removeBstation  {}", deviceRemoveRequest);
        extensionService.uninstallAllExtension(userId);
        alexaSafemoRequestService.reportBxDeviceDeactivate(deviceRemoveRequest.getSerialNumber());
        bindService.deviceReset(deviceRemoveRequest.getSerialNumber(), !deviceRemoveRequest.getCleanStorage());
        if (deviceRemoveRequest.getCleanStorage()) {
            localSpaceService.videoReset(deviceRemoveRequest.getSerialNumber());
        }
        bindService.sendDeActiveByGrpc(deviceRemoveRequest.getSerialNumber());
        return Result.Success();
    }

    @LogRequestAndResponse
    @PostMapping(value = "/removeCamera", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result removeCamera(@RequestAttribute(RequestAttributeKeys.USER_ID) Integer userId, @RequestBody DeviceRemoveRequest deviceRemoveRequest) {
        // 只有设备实际拥有者拥有权限（被分享用户无权限）
        Integer adminCheck = deviceAuthService.checkActivatedAccess(userId, deviceRemoveRequest.getSerialNumber());
        if (SUCCESS.getCode() != adminCheck) {
            return ResultCollection.getResult(adminCheck);
        }

        if(deviceRemoveRequest.getCleanStorage()!=null && deviceRemoveRequest.getCleanStorage()){
            Result result = deviceSettingService.sendWakeupAndWait( deviceRemoveRequest.getSerialNumber());
            if(!Result.successFlag.equals(result.getResult())){
                return result;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("deleteResource", true);
            result = deviceSettingService.sendCmd("deleteResource", deviceRemoveRequest.getSerialNumber(), map);
            if(!Result.successFlag.equals(result.getResult())){
                return result;
            }
        }

        deviceResetService.removeCamera( deviceRemoveRequest.getSerialNumber(), userId,  deviceRemoveRequest.getCleanStorage());

        return Result.Success();
    }
}
