package com.addx.iotcamera.event;

import com.addx.iotcamera.service.DeviceInfoService;
import com.addx.iotcamera.service.RedisService;
import com.addx.iotcamera.service.UserRoleService;
import com.bugsnag.Bugsnag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * description:
 *
 * <AUTHOR>
 * @version 1.0
 * date: 2024/9/6 17:29
 */
@Component
@Slf4j
public class BugsnagManager {
    @Autowired
    @Lazy
    private DeviceInfoService deviceInfoService;

    @Autowired
    @Lazy
    private UserRoleService userRoleService;
    @Autowired
    @Lazy
    private Bugsnag bugsnag;

    private String sn ;

    public boolean notify(Throwable throwable) {
        if (sn == null){
            sn = userRoleService.getBxSn();
        }
        if(StringUtils.isEmpty(sn)){
            log.info("notify ,getBxSn is null {}", sn);
            return false;
        }
        Boolean switchOn = deviceInfoService.getEventTrackingSwitch(sn);
        log.info("getEventTrackingSwitch : {} , {}", sn, switchOn);
        if(switchOn) {
            return bugsnag.notify(throwable);
        }
        return false;
    }
}
