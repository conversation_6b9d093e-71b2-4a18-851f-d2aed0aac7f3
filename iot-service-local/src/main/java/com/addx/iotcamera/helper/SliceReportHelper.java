package com.addx.iotcamera.helper;

import com.addx.iotcamera.bean.video.VideoSliceReport;
import com.addx.iotcamera.service.videofile.VideoFileService;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.enums.PirServiceName;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;
import java.util.regex.Matcher;

import static com.addx.iotcamera.constants.VideoConstants.VIDEO_CXS_PREFIX;
import static com.addx.iotcamera.service.videofile.VideoFileService.DEFAULT_DISKSPACE;
import static com.addx.iotcamera.service.videofile.VideoFileService.videoHttpUrlPtn;

@Slf4j
public class SliceReportHelper {

    /*
    1. 保存云端
    根据云端下发的规则， 设备端拼出来
    - 第一个切片：https://s3.safemo.com/device_video_slice/4ca3f419c7d8f9697f33cf8d6a7b29b8/id_01_1734936195_e34a_1/id_01_1734936195_e34a_1_0.ts
    2. 保存在kx
    - 第一个切片：https://api-local.safemo.com/device_video_slice/4ca3f419c7d8f9697f33cf8d6a7b29b8/id_01_1734936195_e34a_1/id_01_1734936195_e34a_1_0.ts
    3. 保存在摄像头
    需要设备端特殊处理
    主画面：id_01_1734936195_e34a_1_0.ts
    子画面：id_01_1734936195_e34a_2_0.ts
    aov：id_01_1734936195_e34a_0_0.ts
     */
    // 把设备端通过sliceReport上报的文件url转成后端存储的url
    public static String transFileUrl(VideoSliceReport request, Function<VideoSliceReport, String> urlGetter) {
        if (request == null) return null;
        String rawUrl = urlGetter.apply(request);
        if (StringUtils.isBlank(rawUrl)) return rawUrl;
        String videoFileUri = VideoFileService.getVideoRelFileUri(rawUrl);
        if (videoFileUri != null) return videoFileUri;
        if (PirServiceName.cxs.name().equals(request.getServiceName())) {
            // 签名后url：${safeRtcRootUrl}/camerastorage/${slieceId}.ts?cxSn=${cxSn}&sn=${bxSn}
            // 保存在数据库中:              /camerastorage/${slieceId}.ts?cxSn=${cxSn}
            String url = VIDEO_CXS_PREFIX + rawUrl + "?cxSn=" + request.getSerialNumber();
            log.debug("transFileUrl end! serviceName={},rawUrl={},url={}", request.getServiceName(), rawUrl, url);
            return url;
        }
        return rawUrl;
    }

    public static String getStorageId(VideoSliceReport request) {
        if (request != null && request.getVideoPath() != null) {
            Matcher matcher = videoHttpUrlPtn.matcher(request.getVideoPath());
            if (matcher.find()) return matcher.group(1);
        }
        return DEFAULT_DISKSPACE;
    }

}
