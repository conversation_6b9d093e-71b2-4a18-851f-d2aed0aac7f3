package com.addx.iotcamera.helper;

import com.addx.iotcamera.dao.LocalSettingDAO;
import com.addx.kiss.peer.longconn.ClientSender;
import com.addx.kiss.peer.longconn.ClientSenderImpl;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.ImmutableSet;
import io.netty.channel.Channel;
import io.netty.channel.socket.SocketChannel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.utils.SpringContextUtil;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.net.InetSocketAddress;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 获取iotLocal本地的ip，用于下发给设备或app
 * 目前使用http或websocket请求的targetIp
 */
@Data
@Slf4j
@Component @Lazy
public class IpHelper {

    @Value("${iot-local.nodeName:null}")
    private String nodeName;
    @Value("${iot-local.http.domain:null}")
    private String httpDomain;
    @Value("${iot-local.kiss.domain:null}")
    private String kissDomain;
    @Value("${iot-local.coturn.domain:null}")
    private String coturnDomain;

    @Value("${iot-local.http.port}")
    private int httpPort = 7777;
    @Value("${iot-local.kiss.port}")
    private int kissPort = 8443;
    @Value("${iot-local.coturn.port}")
    private int coturnPort = 3478;

    @Value("${iot-local.kiss.secret:null}")
    private String kissSecret;
    @Value("${iot-local.coturn.secret:null}")
    private String coturnSecret;

    @Autowired @Lazy
    private LocalSettingDAO localSettingDAO;

    private Cache<String, Optional<String>> localSettingCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(5))
            .build();

    public Optional<String> getLocalSetting(String key) {
        try {
            return localSettingCache.get(key, k -> {
                try {
                    final String value = localSettingDAO.get(key);
                    log.info("getLocalSetting from db end! key={},value={}", key, value);
                    return Optional.ofNullable(value);
                } catch (Throwable e) {
                    log.error("getLocalSetting from db error! key={}", key, e);
                    return Optional.empty();
                }
            });
        } catch (Throwable e) {
            log.error("getLocalSetting from cache error! key={}", key, e);
            return Optional.empty();
        }
    }

    public static IpHelper getInstance() {
        return SpringContextUtil.getBean(IpHelper.class);
    }

    /*
    websocket:
    io.netty.channel.AbstractChannel.toString
    [id: 0x8860e7b3, L:/**************:8443 - R:/*************:37358]
    http:

     */

    public static String LOCAL_IP = "127.0.0.1";
    public static Set<String> EXCLUDE_IPS = ImmutableSet.of(LOCAL_IP, "0:0:0:0:0:0:0:1");

    // 客户端id -> 服务端ip(不同网络下服务端可能ip不同)
    private static Map<String, String> clientId2ServerIp = new ConcurrentHashMap<>();

    // 当socket连上时，记录远端ip与本地ip的关系
    public static void onSocketConnected(String clientId, Channel channel) {
        String clientIp = "", serverIp = "";
        try {
            final SocketChannel sc = (SocketChannel) channel;
            clientIp = sc.remoteAddress().getAddress().getHostAddress();
            serverIp = sc.localAddress().getAddress().getHostAddress();
            if (EXCLUDE_IPS.contains(clientIp) || EXCLUDE_IPS.contains(serverIp)) return;
            clientId2ServerIp.put(clientId, serverIp);
//            log.info("onSocketConnected end! clientId={},clientIp={},serverIp={},channel={},remoteAddr={},localAddr={}", clientId, clientIp, serverIp, channel, getAddrDetail(sc.remoteAddress()), getAddrDetail(sc.localAddress()));
            log.info("onSocketConnected end! clientId={},clientIp={},serverIp={},channel={}", clientId, clientIp, serverIp, channel);
        } catch (Throwable e) {
            log.error("onSocketConnected error! clientId={},clientIp={},serverIp={},channel={}", clientId, clientIp, serverIp, channel, e);
        }
    }

    public static void onWsClientAuth(String id, ClientSender sender) {
        try {
            log.info("onWsClientAuth begin! clientId={}", id);
            final Channel channel = Optional.ofNullable(sender).filter(it -> it instanceof ClientSenderImpl)
                    .map(it -> ((ClientSenderImpl) it).getChannel("onWsClientAuth")).orElse(null);
            if (channel != null) {
                IpHelper.onSocketConnected(id, channel); // 新ws鉴权成功时
            } else {
                log.info("onWsClientAuth not found channel! clientId={}", id);
            }
        } catch (Throwable e) {
            log.error("onWsClientAuth error! clientId={}", id, e);
        }
    }

    // 当设备http请求时，记录(sn,远端ip)与本地ip的关系
    // app的请求都经过safeRTC转发，iot-local看来就是本地请求，不记录
    public static void onHttpRequest(String clientId, HttpServletRequest request) {
        String clientIp = "", serverIp = "", requestUrl = "";
        try {
            clientIp = request.getRemoteAddr();
            serverIp = request.getLocalAddr();
            requestUrl = request.getRequestURL().toString();
            if (EXCLUDE_IPS.contains(clientIp) || EXCLUDE_IPS.contains(serverIp)) return;
            clientId2ServerIp.put(clientId, serverIp);
            log.info("onHttpRequest end! clientId={},clientIp={},serverIp={},requestUrl={}", clientId, clientIp, serverIp, requestUrl);
        } catch (Throwable e) {
            log.error("onHttpRequest error! clientId={},clientIp={},serverIp={},requestUrl={}", clientId, clientIp, serverIp, requestUrl, e);
        }
    }

    public static String getServerIp(String clientId) {
        final String serverIp = clientId != null ? clientId2ServerIp.get(clientId) : null;
        final String returnServerIp = serverIp != null ? serverIp : clientId2ServerIp.values().stream()
                .filter(ip -> !EXCLUDE_IPS.contains(ip)).findFirst().orElse(LOCAL_IP);
        log.info("getServerIp end! clientId={},serverIp={},returnServerIp={}", clientId, serverIp, returnServerIp);
        return returnServerIp;
    }

    public String getNodeName() {
        return getLocalSetting("nodeName").filter(StringUtils::isNotBlank).orElse(nodeName);
    }

    public String getHttpDomain() {
        return getLocalSetting("httpDomain").filter(StringUtils::isNotBlank).orElse(httpDomain);
    }

    public String getKissDomain() {
        return getLocalSetting("kissDomain").filter(StringUtils::isNotBlank).orElse(kissDomain);
    }

    public String getCoturnDomain() {
        return getLocalSetting("coturnDomain").filter(StringUtils::isNotBlank).orElse(coturnDomain);
    }

    // 组装下发给cx的上传路径
    public static String getHttpRootUrl(String clientId) {
        final String httpDomain = getInstance().getHttpDomain();
        if (StringUtils.isNotBlank(httpDomain)) {
//            return "http://" + getInstance().getHttpDomain() + ":" + getInstance().getHttpPort();
            return "https://" + httpDomain;
        } else {
            return "http://" + getServerIp(clientId) + ":" + getInstance().getHttpPort();
        }
    }

    public static String getHttpRootUrl(HttpServletRequest httpRequest) {
        final String httpDomain = getInstance().getHttpDomain();
        if (StringUtils.isNotBlank(httpDomain)) {
            return "https://" + httpDomain + ":" + getInstance().getHttpPort();
        } else {
            return "https://" + httpRequest.getLocalAddr() + ":" + getInstance().getHttpPort();
        }
    }

    // 组装下发给cx的wss根路径
    public static String getKissRootUrl(String clientId) {
        final String kissDomain = getInstance().getKissDomain();
        if (StringUtils.isNotBlank(kissDomain)) {
            return "wss://" + kissDomain + ":" + getInstance().getKissPort();
        } else {
            return "wss://" + getServerIp(clientId) + ":" + getInstance().getKissPort();
        }
    }

    public static String getCoturnRootUrl(String clientId) {
        final String coturnDomain = getInstance().getCoturnDomain();
        if (StringUtils.isNotBlank(coturnDomain)) {
            return "stun:" + coturnDomain + ":" + getInstance().getCoturnPort();
        } else {
            return "stun:" + getServerIp(clientId) + ":" + getInstance().getCoturnPort();
        }
    }

    public static JSONObject getAddrDetail(InetSocketAddress inetSocketAddr) {
        return new JSONObject(new LinkedHashMap<>()).fluentPut("toString", inetSocketAddr.toString()).fluentPut("hostName", inetSocketAddr.getHostName()).fluentPut("hostString", inetSocketAddr.getHostString()).fluentPut("port", inetSocketAddr.getPort()).fluentPut("address", inetSocketAddr.getAddress().toString()).fluentPut("address.hostName", inetSocketAddr.getAddress().getHostName()).fluentPut("address.hostAddress", inetSocketAddr.getAddress().getHostAddress()).fluentPut("address.address", Hex.encodeHexString(inetSocketAddr.getAddress().getAddress())).fluentPut("address.canonicalHostName", inetSocketAddr.getAddress().getCanonicalHostName());
    }

}
