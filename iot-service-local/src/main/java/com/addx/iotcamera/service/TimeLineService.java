package com.addx.iotcamera.service;

import com.addx.iotcamera.bean.db.LibraryStatusTb;
import com.addx.iotcamera.bean.db.LibraryTb;
import com.addx.iotcamera.dao.library.IShardingLibraryDAO;
import com.addx.iotcamera.dao.library.IShardingLibraryStatusDAO;
import com.addx.iotcamera.dao.library.IShardingVideoSliceDAO;
import com.addx.iotcamera.helper.DataSourceHelper;
import com.addx.iotcamera.helper.IpHelper;
import com.addx.iotcamera.service.device.DeviceStatusService;
import com.addx.iotcamera.service.videofile.VideoFileService;
import com.addx.iotcamera.servlet.LoggableDispatcherServlet;
import com.alibaba.fastjson.JSON;
import io.reactivex.Observable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.addx.iot.common.constant.VideoTypeEnum;
import org.addx.iot.common.enums.PirServiceName;
import org.addx.iot.common.utils.TextUtil;
import org.addx.iot.common.vo.PageResponse;
import org.addx.iot.common.vo.Result;
import org.addx.iot.domain.extension.video.entity.DeviceLibraryViewDO;
import org.addx.iot.domain.extension.video.entity.SliceDetailVO;
import org.addx.iot.domain.extension.video.entity.UserLibraryViewDO;
import org.addx.iot.domain.extension.video.param.LibraryRequest;
import org.addx.iot.domain.video.entity.VideoSliceDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

import static com.addx.iotcamera.constants.VideoConstants.*;
import static com.addx.iotcamera.enums.VideoType.DEVICE_SLICE;
import static com.addx.iotcamera.service.videofile.VideoFileService.HTTP_HEADER_SAFE_RTC_ROOT_URL;
import static org.addx.iot.common.constant.AppConstants.TENANTID_SAFEMO;

@Slf4j
@Lazy
@Service
public class TimeLineService {

    @Autowired
    @Lazy
    private IShardingLibraryDAO shardingLibraryDAO;
    @Autowired
    @Lazy
    private IShardingLibraryStatusDAO shardingLibraryStatusDAO;
    @Autowired
    @Lazy
    private IShardingVideoSliceDAO shardingVideoSliceDAO;
    @Autowired
    @Lazy
    private DataSourceHelper dataSourceHelper;
    @Autowired
    @Lazy
    private S3Service s3Service;
    @Autowired
    @Lazy
    private DeviceStatusService deviceStatusService;
    @Autowired
    @Lazy
    private VideoFileService videoFileService;

    /*
    时间轴接口同时要aov和pir视频，且不分页。
       {"endTimestamp":1744833599,"hasSliceList":[0,1,2],"serialNumber":["3b00884f98ceee4e0d5189f76573767b"]
        ,"startTimestamp":1744819200,"videoTypes":[0,1]
        ,"currentBindSns":["3b00884f98ceee4e0d5189f76573767b"],"userId":123}
     */
    public static boolean isTimeLineRequest(LibraryRequest request) {
        return request != null && request.getApp() != null && TENANTID_SAFEMO.equals(request.getApp().getTenantId())
                && CollectionUtils.isNotEmpty(request.getSerialNumber())
                && request.getStartTimestamp() != null && request.getEndTimestamp() != null
                && request.getFrom() == null && request.getTo() == null
                && request.getUserId() != null && request.getCurrentBindSns() != null;
    }

    public static boolean isExpectVideoTypes(LibraryRequest request) {
        return request.getVideoTypes() != null
                && request.getVideoTypes().contains(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode())
                && request.getVideoTypes().contains(VideoTypeEnum.SNAPSHOT_RECORDING.getCode());
    }

    public static boolean isExpectHasSliceList(LibraryRequest request) {
        return request.getHasSliceList() != null
                && request.getHasSliceList().contains(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode())
                && request.getHasSliceList().contains(VideoTypeEnum.EVNET_RECORDING_SUB_VIEW.getCode())
                && request.getHasSliceList().contains(VideoTypeEnum.SNAPSHOT_RECORDING.getCode());
    }

    public String selectTimeLineLibrary(LibraryRequest request) {
        boolean expectVideoTypes = isExpectVideoTypes(request);
        boolean expectHasSliceList = isExpectHasSliceList(request);
        log.info("selectTimeLineLibrary begin! request={},expectVideoTypes={},expectHasSliceList={}", JSON.toJSONString(request), expectVideoTypes, expectHasSliceList);
        final long t1 = System.currentTimeMillis();
        final long startMillis = request.getStartTimestamp() * 1000L;
        // app传的是秒为单位的闭区间: [100,199]，后端sql是以毫秒为单位的闭区间：[100_000,199_999]
        final long endMillis = request.getEndTimestamp() * 1000L + 999L;
        final String sn = request.getSerialNumber().get(0);
        final boolean onlyBxs = !request.getCurrentBindSns().contains(sn);
        // 查出时间范围内的所有切片
        final Map<String, ArrayList<SliceDetailVO>> traceId2SliceList = selectTimeLineSliceList(startMillis, endMillis, sn, onlyBxs);
        // 根据切片构造视频
        final Map<String, UserLibraryViewDO> allViewMap = mergeSliceList(sn, traceId2SliceList);
        final long t2 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary selectTimeLineSliceList end! costTime={},traceId2SliceList.size={},allViewMap.size={}", (t2 - t1), traceId2SliceList.size(), allViewMap.size());
        // 查找用户-视频关系表
        Map<String, LibraryStatusTb> libraryStatusTbMap = new LinkedHashMap<>();
        Observable.fromIterable(allViewMap.keySet()).buffer(100).forEach(traceIds -> {
            for (LibraryStatusTb libraryTb : shardingLibraryStatusDAO.selectLibraryStatusTbByTraceIds(request.getUserId(), traceIds)) {
                libraryStatusTbMap.put(libraryTb.getTraceId(), libraryTb);
            }
        });
        // 查找视频表
        Map<String, LibraryTb> libraryTbMap = new LinkedHashMap<>();
        Observable.fromIterable(allViewMap.keySet()).buffer(100).forEach(traceIds -> {
            for (LibraryTb libraryTb : shardingLibraryDAO.selectLibraryTbByTraceIds(traceIds)) {
                libraryTbMap.put(libraryTb.getTraceId(), libraryTb);
            }
        });
        // 组织主子视频
        List<UserLibraryViewDO> mainViewList = new ArrayList<>();
        List<UserLibraryViewDO> allViewList = new LinkedList<>();
        for (UserLibraryViewDO viewDO : allViewMap.values()) {
            LibraryStatusTb libraryStatusTb = libraryStatusTbMap.get(viewDO.getTraceId());
            if (libraryStatusTb == null) {
                log.debug("selectTimeLineLibrary not found libraryStatusTb! viewDO={}", JSON.toJSONString(viewDO));
                continue;
            }
            LibraryTb libraryTb = libraryTbMap.get(viewDO.getTraceId());
            if (libraryTb == null) {
                log.debug("selectTimeLineLibrary not found libraryTb! viewDO={}", JSON.toJSONString(viewDO));
                continue;
            }
            // libraryStatusTb就是用request.userId查出来的
            viewDO.setUserId(request.getUserId());
            viewDO.setSerialNumber(sn);
            // 填充视频列表 marked、missing、tags
            Optional.ofNullable(libraryStatusTb.getMarked()).ifPresent(viewDO::setMarked);
            Optional.ofNullable(libraryStatusTb.getMissing()).ifPresent(viewDO::setMissing);
            Optional.ofNullable(libraryStatusTb.getTags()).filter(StringUtils::isNotBlank).ifPresent(viewDO::setTags);
            // 填充视频列表 videoUrl、imageUrl、codec
            Optional.ofNullable(libraryTb.getVideoUrl()).filter(StringUtils::isNotBlank).ifPresent(viewDO::setVideoUrl);
            Optional.ofNullable(libraryTb.getImageUrl()).filter(StringUtils::isNotBlank).ifPresent(viewDO::setImageUrl);
            Optional.ofNullable(libraryTb.getCodec()).filter(StringUtils::isNotBlank).ifPresent(viewDO::setCodec);
            viewDO.setAdminId(libraryTb.getAdminId());
            viewDO.setEnableDelete(Objects.equals(viewDO.getUserId(), viewDO.getAdminId()));
            viewDO.setType(libraryTb.getType());
            viewDO.setMainTraceId(libraryTb.getMainTraceId());
            viewDO.setVideoType(libraryTb.getVideoType());

            if (viewDO.getMainTraceId() != null) {
                UserLibraryViewDO mainLibraryViewDO = allViewMap.get(viewDO.getMainTraceId());
                if (mainLibraryViewDO == null) {
                    log.warn("selectTimeLineLibrary not found mainLibraryViewDO! viewDO={}", JSON.toJSONString(viewDO));
                    continue;
                }
                mainLibraryViewDO.getSubVideos().add(viewDO);
            } else {
                mainViewList.add(viewDO);
            }
            allViewList.add(viewDO);
        }
        final long t3 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary selectLibraryStatusTbByTraceIds end! costTime={},libraryStatusTbMap.size={},libraryTbMap.size={},mainViewList.size={},allViewList.size={}", (t3 - t2), libraryStatusTbMap.size(), libraryTbMap.size(), mainViewList.size(), allViewList.size());
        // 给视频列表排序
        Collections.sort(mainViewList, Comparator.comparingInt(UserLibraryViewDO::getTimestamp).reversed());
        for (UserLibraryViewDO viewDO : allViewList) {
            // 给切片列表排序
            Collections.sort(viewDO.getSliceList(), Comparator.comparingInt(SliceDetailVO::getOrder));
        }
        final long t4 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary selectLibraryTbByTraceIds end! costTime={},libraryTbMap.size={}", (t4 - t3), libraryTbMap.size());
        preSign(request.getUserId(), allViewList);
        final long t5 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary preSign total end! costTime={},mainViewList.size={}", (t5 - t4), mainViewList.size());
        Result<PageResponse<UserLibraryViewDO>> result = Result.PageResult(mainViewList, allViewList.size());
        String resultStr = JSON.toJSONString(result);
        final long t6 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary toJSONString end! costTime={},resultStr.length={},totalCostTime={}", (t6 - t5), resultStr.length(), (t6 - t1));
        return resultStr;
    }

    @NotNull
    private static Map<String, UserLibraryViewDO> mergeSliceList(String sn, Map<String, ArrayList<SliceDetailVO>> traceId2SliceList) {
        final long t1 = System.currentTimeMillis();
        try {
            Map<String, UserLibraryViewDO> allViewMap = traceId2SliceList.entrySet().parallelStream().map(it -> buildUserLibraryViewDO(sn, it.getKey(), it.getValue()))
                    .filter(Objects::nonNull).collect(Collectors.toMap(DeviceLibraryViewDO::getTraceId, it -> it));
            final long t2 = System.currentTimeMillis();
            log.info("selectTimeLineLibrary mergeSliceList end! costTime={},traceId2SliceList.size={},allViewMap.size={}", (t2 - t1), traceId2SliceList.size(), allViewMap.size());
            return allViewMap;
        } catch (Throwable e) {
            final long t2 = System.currentTimeMillis();
            log.error("selectTimeLineLibrary mergeSliceList error! costTime={},traceId2SliceList.size={}", (t2 - t1), traceId2SliceList.size(), e);
            throw new RuntimeException(e);
        }
    }

    private static UserLibraryViewDO buildUserLibraryViewDO(String sn, String traceId, ArrayList<SliceDetailVO> sliceList) {
        if (CollectionUtils.isEmpty(sliceList)) return null;
        try {
            // 给切片列表排序
            Collections.sort(sliceList, Comparator.comparingInt(SliceDetailVO::getOrder));

            final SliceDetailVO slice = sliceList.get(0);
            final UserLibraryViewDO viewDO = new UserLibraryViewDO();
            viewDO.setVideoType(VideoTypeEnum.EVNET_RECORDING_MAIN_VIEW.getCode());
            viewDO.setMainTraceId(null);
            viewDO.setTraceId(traceId);
            viewDO.setServiceName(slice.getVideoUrl().startsWith(VIDEO_REL_PREFIX) ? PirServiceName.bxs.name() : PirServiceName.cxs.name());
            viewDO.setVideoUrl(VideoFileService.getVideoDirUrlFromSliceUrl(slice.getVideoUrl(), sn, traceId));
            viewDO.setImageUrl(null);
            viewDO.setMarked(0);
            viewDO.setMissing(1);
            viewDO.setTags("");
            viewDO.setEnableDelete(false);
            viewDO.setType(DEVICE_SLICE.getCode());
            // 累计初始值
            viewDO.setTimestamp((int) (sliceList.stream().map(SliceDetailVO::getTimestamp).reduce(Long::min).orElse(0L) / 1000L));
            viewDO.setEndTimestamp((int) (sliceList.stream().map(SliceDetailVO::getEndTimestamp).reduce(Long::max).orElse(0L) / 1000L));
            viewDO.setPeriod(VideoSliceDO.computeSliceSeconds(sliceList.stream().map(SliceDetailVO::getPeriod).reduce(Integer::sum).orElse(0))); // 秒
            viewDO.setSliceList(sliceList);
            return viewDO;
        } catch (Exception e) {
            log.error("selectTimeLineLibrary buildUserLibraryViewDO error! size={},slice0={}", sliceList.size(), JSON.toJSONString(sliceList.get(0)), e);
            return null;
        }
    }

    private void preSign(Integer userId, List<UserLibraryViewDO> allViewList) {
        //        s3Service.presingDeviceLibraryViewDOList(PresignParams.create(request), allViewList);
        final long t0 = System.currentTimeMillis();
        final BatchPreSignUtil batchPreSignUtil = new BatchPreSignUtil(userId);
        final long t1 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary prepare end! costTime={},batchPreSignUtil={}", (t1 - t0), JSON.toJSON(batchPreSignUtil));
        for (UserLibraryViewDO viewDO : allViewList) {
//            viewDO.setImageUrl(s3Service.preSignUrl(viewDO.getImageUrl()));
            viewDO.setImageUrl(batchPreSignUtil.preSignFileUrl(viewDO.getImageUrl()));
        }
        final long t2 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary preSign imageUrl end! costTime={},allViewList.size={}", (t2 - t1), allViewList.size());
        for (UserLibraryViewDO viewDO : allViewList) {
//            viewDO.setVideoUrl(s3Service.preSignUrl(viewDO.getVideoUrl()));
            viewDO.setVideoUrl(batchPreSignUtil.preSignVideoUrl(viewDO.getVideoUrl()));
        }
        final long t3 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary preSign videoUrl end! costTime={},allViewList.size={}", (t3 - t2), allViewList.size());
        int sliceNum = 0;
        for (UserLibraryViewDO viewDO : allViewList) {
            if (CollectionUtils.isEmpty(viewDO.getSliceList())) continue;
            for (SliceDetailVO slice : viewDO.getSliceList()) {
//                slice.setVideoUrl(s3Service.preSignUrl(slice.getVideoUrl()));
                slice.setVideoUrl(batchPreSignUtil.preSignFileUrl(slice.getVideoUrl()));
            }
            sliceNum += viewDO.getSliceList().size();
        }
        final long t4 = System.currentTimeMillis();
        log.info("selectTimeLineLibrary preSign sliceUrl end! costTime={},allViewList.size={},sliceNum={}", (t4 - t3), allViewList.size(), sliceNum);
    }

    @Data
    public class BatchPreSignUtil {

        private final Integer userId;
        private final String bxSn;
        private final String signUrlQuery;
        private final String safeRtcRootUrl;
        private final String rootUrl;

        public BatchPreSignUtil(Integer userId) {
            this.userId = userId;
            bxSn = deviceStatusService.getBxSn();
            final String userDownloadToken = videoFileService.createDownloadToken(VIDEO_USER_ID_PREFIX + userId);
            signUrlQuery = "?sn=" + bxSn + "&token=" + userDownloadToken;
            safeRtcRootUrl = LoggableDispatcherServlet.getRequestHeader(HTTP_HEADER_SAFE_RTC_ROOT_URL);
            rootUrl = StringUtils.isNotBlank(safeRtcRootUrl) ? safeRtcRootUrl : IpHelper.getHttpRootUrl((String) null);
        }

        /**
         * 对文件url进行签名。可能有：cxs文件、相对路径文件、绝对路径文件
         */
        public String preSignFileUrl(String fileUri) {
            if (StringUtils.isBlank(fileUri)) {
                return "";
            } else if (fileUri.startsWith(VIDEO_CXS_PREFIX)) {
                // 保存在数据库中:              /camerastorage/${slieceId}.ts?cxSn=${cxSn}
                // 签名后url：${safeRtcRootUrl}/camerastorage/${slieceId}.ts?cxSn=${cxSn}&sn=${bxSn}
                return rootUrl + fileUri + "&sn=" + bxSn;
            } else if (fileUri.startsWith(VIDEO_REL_PREFIX)) {
                // videorel:3b00884f98ceee4e0d5189f76573767b/id_01_1745511533_7HZw_2/id_01_1745511533_7HZw_2.jpg
                // 签名后url：http://ntclient.addx.live:3210/videoFile/download/o/id_01_1745511533_7HZw_2/id_01_1745511533_7HZw_2.jpg?sn=80101db92908202cffc193b69155175a&token=1MIBSkQTVve2RJ1WZxzLjH1z29Xd_rap
                final String traceIdStartUri = TextUtil.subStringAfter(fileUri, "/");
                return rootUrl + "/videoFile/download/o/" + traceIdStartUri + signUrlQuery;
            } else if (fileUri.startsWith(VIDEO_FILE_PREFIX)) {
                // file:/data/iot-service/pir/device_video_slice/c3f3b51962e88e970daab72353a5786c/03981706851854Ja64wedUw8VeTox/image.jpg
                // 签名后url：http://ntclient.addx.live:3210/videoFile/download/o/id_01_1745511533_7HZw_2/id_01_1745511533_7HZw_2.jpg?sn=80101db92908202cffc193b69155175a&token=1MIBSkQTVve2RJ1WZxzLjH1z29Xd_rap
                final String traceIdStartUri = TextUtil.subStringAfter(fileUri, "/" + UPLOAD_KEY_HEAD + "/", "/");
                return rootUrl + "/videoFile/download/o/" + traceIdStartUri + signUrlQuery;
            } else {
                log.warn("BatchPreSignUtil preSignFileUrl not support! fileUri={}", fileUri);
                return fileUri;
            }
        }

        /**
         * 对视频根目录url进行签名。
         */
        public String preSignVideoUrl(String fileUri) {
            if (StringUtils.isBlank(fileUri)) {
                return "";
            } else if (fileUri.startsWith(VIDEO_DIR_PREFIX)) {
                // videodir:/data/iot-service/pir/device_video_slice/c3f3b51962e88e970daab72353a5786c/03981706848624ZEV0enPodNoswVR
                // 签名后url：http://ntclient.addx.live:3210/videoFile/download/m3u8/o/id_01_1745511533_7HZw_2.m3u8?sn=80101db92908202cffc193b69155175a&token=1MIBSkQTVve2RJ1WZxzLjH1z29Xd_rap
                final String traceIdStartUri = TextUtil.subStringAfter(fileUri, "/" + UPLOAD_KEY_HEAD + "/", "/");
                return rootUrl + "/videoFile/download/m3u8/o/" + traceIdStartUri + ".m3u8" + signUrlQuery;
            } else {
                log.warn("BatchPreSignUtil preSignVideoUrl not support! fileUri={}", fileUri);
                return fileUri;
            }
        }

    }

    private Map<String, ArrayList<SliceDetailVO>> selectTimeLineSliceList(long startMillis, long endMillis, String sn, boolean onlyBxs) {
        final long t1 = System.currentTimeMillis();
        final Map<String, ArrayList<SliceDetailVO>> traceId2SliceList = new LinkedHashMap<>();
        int[] totalSliceNums = {0};
        dataSourceHelper.doOnDataSource("library", dataSource -> {
            int totalSliceNum = 0;
            try (Connection conn = dataSource.getConnection()) {
                /*
                select video_url ,trace_id , `order` , is_last , period , file_size ,  s3_event_time , end_utc_timestamp_millis  from video_slice where serial_number='3b00884f98ceee4e0d5189f76573767b' and s3_event_time >= 0 and s3_event_time <=1744337755147;
               ['video_url', 'trace_id', 'order', 'is_last', 'period', 'file_size', 's3_event_time', 'end_utc_timestamp_millis']
                ('/camerastorage/id_01_1744337508_gGL3_0_4.ts?cxSn=3b00884f98ceee4e0d5189f76573767b', 'id_01_1744337508_gGL3_0', 4, 0, 0.5, 85164, 1744337553135, 1744337563947)
                ('videorel:3b00884f98ceee4e0d5189f76573767b/id_01_1744337508_gGL3_0/id_01_1744337508_gGL3_0_5.ts', 'id_01_1744337508_gGL3_0', 5, 0, 0.75, 119944, 1744337718872, 1744337739605)
                 */
                String sql = " select video_url ,trace_id , `order` , is_last ," +
                        " period , file_size ,  s3_event_time , end_utc_timestamp_millis " +
                        " from video_slice" +
                        " where serial_number = ?" +
                        " and s3_event_time >= ?" +
                        " and s3_event_time <= ?;";
                if (onlyBxs) {
                    sql += " and video_url like 'videorel:%'";
                }
                PreparedStatement ps = conn.prepareStatement(sql);
                ps.setString(1, sn);
                ps.setLong(2, startMillis);
                ps.setLong(3, endMillis);
                ResultSet resultSet = ps.executeQuery();
                final long t2 = System.currentTimeMillis();
                log.info("selectTimeLineSliceList begin! costTime={},sliceList.size={}", (t2 - t1), totalSliceNum);
                while (resultSet.next()) {
                    final String traceId = resultSet.getString("trace_id");
                    SliceDetailVO videoSliceDO = new SliceDetailVO()
                            .setVideoUrl(resultSet.getString("video_url"))
                            .setOrder(resultSet.getInt("order"))
                            .setIsLast(resultSet.getBoolean("is_last"))
                            .setPeriod(VideoSliceDO.computeSliceMillis(resultSet.getBigDecimal("period"))) // ms
                            .setFileSize(resultSet.getInt("file_size"))
                            .setTimestamp(resultSet.getLong("s3_event_time"));
                    // 当字段值为 NULL 时，ResultSet.getLong() 不会抛出异常，而是返回 0
                    final long endUtcTimestampMillis = resultSet.getLong("end_utc_timestamp_millis"); // ms
                    // resultSet.wasNull(): 检测resultSet的最后一次取值是否为null
                    if (!resultSet.wasNull() && endUtcTimestampMillis > 0L) {
                        videoSliceDO.setEndTimestamp(endUtcTimestampMillis); // ms
                    } else {
                        videoSliceDO.setEndTimestamp(videoSliceDO.getTimestamp() + videoSliceDO.getPeriod()); // ms
                    }
                    traceId2SliceList.computeIfAbsent(traceId, k -> new ArrayList<>()).add(videoSliceDO);
                    totalSliceNum++;
                    if (totalSliceNum % 100 == 0) {
                        long t3 = System.currentTimeMillis();
                        log.info("selectTimeLineSliceList batch end! costTime={},sliceList.size={}", (t3 - t2), totalSliceNum);
                    }
                }
            }
            totalSliceNums[0] = totalSliceNum;
        });
        long t4 = System.currentTimeMillis();
        log.info("selectTimeLineSliceList end! costTime={},sliceList.size={}", (t4 - t1), totalSliceNums[0]);
        return traceId2SliceList;
    }

}
