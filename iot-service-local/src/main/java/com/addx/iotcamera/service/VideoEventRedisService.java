package com.addx.iotcamera.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * videoEvent计算涉及到的redis操作读新库
 * 上线时，新库是空的会多产生一个videoEvent，没有影响，因此不读老库
 */
@Slf4j
@Lazy
@Component
public class VideoEventRedisService {

    @Autowired
    @Lazy
    private StringRedisTemplate businessRedisTemplateClusterClient;

    public String get(String key) {
        return businessRedisTemplateClusterClient.opsForValue().get(key);
    }

    public List<String> rangeList(String key) {
        return businessRedisTemplateClusterClient.opsForList().range(key, 0, -1);
    }

    public Map<Object, Object> getHashEntries(String key) {
        return businessRedisTemplateClusterClient.opsForHash().entries(key);
    }

    public void set(String key, String value, Integer timeOut) {
        businessRedisTemplateClusterClient.opsForValue().set(key, value, timeOut, TimeUnit.SECONDS);
    }

    public void saveList(String key, String value, long expireTime) {
        businessRedisTemplateClusterClient.opsForList().leftPush(key, value);
        businessRedisTemplateClusterClient.expire(key, expireTime, TimeUnit.SECONDS);
    }

    public void setHashFieldValue(String key, String field, String value) {
        businessRedisTemplateClusterClient.opsForHash().put(key, field, value);
    }

    public void setHashFieldValueMap(String key, Map<Object, Object> param, Long expire) {
        businessRedisTemplateClusterClient.opsForHash().putAll(key, param);
        businessRedisTemplateClusterClient.expire(key, expire, TimeUnit.SECONDS);
    }

    public void deleteListValue(String key, String value) {
        businessRedisTemplateClusterClient.opsForList().remove(key, 0, value);
    }


}
