package com.addx.iotcamera.service;

import com.addx.iotcamera.helper.VideoEventCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import static com.addx.iotcamera.constants.VideoConstants.*;

@Component @Lazy
@Slf4j
public class AIService {

    @Autowired @Lazy
    private NotificationService notificationService;

    public void pirNotify(String serialNumber, String traceId, String imageUrl, String eventObject) {
        notificationService.PirNotify(serialNumber, traceId, imageUrl, eventObject);
    }


    /**
     * 获取视频当前所属eventKey
     *
     * @param serialNumber
     * @return
     */
    public String getVideoEventKey(String serialNumber, String traceId) {
        if(!StringUtils.hasLength(serialNumber) || !StringUtils.hasLength(traceId)){
            return "";
        }
        final VideoEventCacheHelper redisService = VideoEventCacheHelper.getInstance(); // caffeine替代redisService
        String videoEvent;
        String eventKey = DEVICE_VIDEO_EVENT.replace("{serialNumber}", serialNumber);
        String traceToEventKey = LIBRARY_TRACE_TO_VIDEO_EVENT.replace("{traceId}", traceId);

        long currentTime = System.currentTimeMillis();
        Map<Object, Object> videoEventMap = redisService.getHashEntries(eventKey);
        if (!videoEventMap.keySet().isEmpty()) {
            boolean inVideoEvent = this.videoInVideoEvent(videoEventMap, currentTime);
            if (inVideoEvent) {
                videoEvent = videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME).toString();

                redisService.set(traceToEventKey, videoEvent, LIBRARY_TRACE_TIMEOUT);
                //更新最后更新时间，整体过期时间不需要处理
                redisService.setHashFieldValue(eventKey, DEVICE_VIDEO_EVENT_LAST_TIME, String.valueOf(currentTime));
                // 符合当前事件规则,返回当前事件key
                return videoEvent;
            }
        }
        // 记录 videoEvent 最新更新时间、开始时间
        videoEventMap.put(DEVICE_VIDEO_EVENT_LAST_TIME, String.valueOf(currentTime));
        videoEventMap.put(DEVICE_VIDEO_EVENT_START_TIME, String.valueOf(currentTime));
        redisService.setHashFieldValueMap(eventKey, videoEventMap, LIBRARY_VIDEO_EVENT_EXPIRE);

        videoEvent = String.valueOf(currentTime);
        // 记录trace -> videoEvent key
        redisService.set(traceToEventKey, videoEvent, LIBRARY_TRACE_TIMEOUT);

        return videoEvent;
    }

    /**
     * 判断是否符合当前事件key
     *
     * @param videoEventMap
     * @return
     */
    private boolean videoInVideoEvent(Map<Object, Object> videoEventMap, Long currentTime) {
        long lastTime = Long.valueOf(videoEventMap.get(DEVICE_VIDEO_EVENT_LAST_TIME).toString());
        if ((currentTime - lastTime) > DEVICE_VIDEO_EVENT_LAST_TIME_LIMIT) {
            // 距离最近一次视频超过15s
            return false;
        }

        long startTime = Long.valueOf(videoEventMap.get(DEVICE_VIDEO_EVENT_START_TIME).toString());
        if ((currentTime - startTime) > DEVICE_VIDEO_EVENT_START_TIME_LIMIT) {
            // 距离本次事件开始超过30m
            return false;
        }
        return true;
    }
}
