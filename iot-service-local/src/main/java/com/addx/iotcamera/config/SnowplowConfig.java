package com.addx.iotcamera.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Snowplow配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "snowplow")
public class SnowplowConfig {
    
    private TimeoutConfig timeout = new TimeoutConfig();
    
    @Data
    public static class TimeoutConfig {
        /**
         * 连接超时时间（秒）
         */
        private int connectTimeoutSeconds = 10000;
        
        /**
         * 读取超时时间（秒）
         */
        private int readTimeoutSeconds = 1000;
        
        /**
         * 写入超时时间（秒）
         */
        private int writeTimeoutSeconds = 1000;
    }
}
