package com.addx.kiss.peer;

import com.addx.kiss.peer.longconn.Client;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum PeerReason {

    DEFAULT(0),
    TO_DORMANT(1), // 对端下电
    WS_DISCONNECT(2), // 对端ws断开

    TO_NORMAL(11), // 对端上电
    WS_CONNECT(12), // 对端ws连上
    VIEWER_JOIN_LIVE(13), // 观看端加入直播
    ;

    private final int code;

    public static PeerReason fromClientStatus(Client.Status status) {
        if (status == Client.Status.normal) return TO_NORMAL;
        else if (status == Client.Status.dormant) return TO_DORMANT;
        else return DEFAULT;
    }

    public static int getCode(PeerReason reason) {
        return reason != null ? reason.getCode() : PeerReason.DEFAULT.getCode();
    }

}
