package main

import (
	"fmt"
	"os"

	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/nfs"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// 测试程序：验证所有服务启动都使用了智能判断
func main() {
	fmt.Println("🧪 测试服务智能启动功能")
	fmt.Println("=========================")

	// 模拟配置
	config := &types.Config{
		TargetDevice: types.TargetDevice{
			IP: "*************",
			SSH: struct {
				Username string `yaml:"username"`
				Password string `yaml:"password"`
				Port     int    `yaml:"port"`
				KeyFile  string `yaml:"key_file"`
			}{
				Username: "root",
				Password: "password",
				Port:     22,
			},
		},
		LocalMachine: types.LocalMachine{
			IP:         "**************",
			MountPoint: "/mnt/camera_data",
		},
		FileSharing: types.FileSharing{
			Type: "samba",
			Directories: []types.ShareDirectory{
				{
					Source:      "/data/sqlite",
					ShareName:   "sqlite",
					Description: "SQLite数据库目录",
				},
			},
		},
	}

	// 创建SSH客户端
	sshClient := ssh.NewClient(config.TargetDevice.IP, config.TargetDevice.SSH.Port, config.TargetDevice.SSH.Username, config.TargetDevice.SSH.Password)

	// 尝试连接SSH
	if err := sshClient.Connect(); err != nil {
		fmt.Printf("❌ SSH连接失败: %v\n", err)
		fmt.Println("⚠️  无法测试远程服务器的智能启动功能")
		fmt.Println("💡 提示：请确保目标设备可访问并且SSH服务正常运行")
		os.Exit(1)
	}
	defer sshClient.Close()

	fmt.Printf("✅ SSH连接成功: %s:%d\n", config.TargetDevice.IP, config.TargetDevice.SSH.Port)
	fmt.Println()

	// 测试1：SSH客户端的智能启动方法
	fmt.Println("1️⃣  测试SSH客户端智能启动方法")
	fmt.Println("----------------------------------")
	
	testServices := []string{"sshd", "systemd-resolved", "dbus"}
	
	for _, service := range testServices {
		fmt.Printf("🔧 测试服务: %s\n", service)
		
		// 测试StartService方法
		if err := sshClient.StartService(service); err != nil {
			fmt.Printf("⚠️  启动服务失败: %v\n", err)
		}
		
		// 测试EnableService方法
		if err := sshClient.EnableService(service); err != nil {
			fmt.Printf("⚠️  启用服务失败: %v\n", err)
		}
		
		fmt.Println()
	}

	// 测试2：SAMBA管理器的智能启动
	fmt.Println("2️⃣  测试SAMBA管理器智能启动")
	fmt.Println("-------------------------------")
	
	sambaManager := fileshare.NewSambaManager(config, sshClient)
	
	fmt.Println("🔧 调用SAMBA SetupServer方法...")
	if err := sambaManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  SAMBA设置失败: %v\n", err)
	} else {
		fmt.Println("✅ SAMBA设置成功")
	}
	fmt.Println()

	// 测试3：NFS管理器的智能启动
	fmt.Println("3️⃣  测试NFS管理器智能启动")
	fmt.Println("----------------------------")
	
	nfsManager := nfs.NewManager(config, sshClient)
	
	fmt.Println("🔧 调用NFS SetupServer方法...")
	if err := nfsManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  NFS设置失败: %v\n", err)
	} else {
		fmt.Println("✅ NFS设置成功")
	}
	fmt.Println()

	// 测试4：重复调用验证智能判断
	fmt.Println("4️⃣  测试重复调用验证智能判断")
	fmt.Println("--------------------------------")
	
	fmt.Println("🔄 第二次调用SAMBA SetupServer...")
	if err := sambaManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  第二次SAMBA设置失败: %v\n", err)
	} else {
		fmt.Println("✅ 第二次SAMBA设置成功")
	}
	
	fmt.Println("🔄 第二次调用NFS SetupServer...")
	if err := nfsManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  第二次NFS设置失败: %v\n", err)
	} else {
		fmt.Println("✅ 第二次NFS设置成功")
	}
	
	fmt.Println()
	fmt.Println("🎉 测试完成！")
	
	// 总结
	fmt.Println()
	fmt.Println("📋 智能启动功能总结:")
	fmt.Println("====================")
	fmt.Println("✅ SSH客户端的StartService和EnableService方法已实现智能判断")
	fmt.Println("✅ SAMBA管理器的服务启动已实现智能判断")
	fmt.Println("✅ NFS管理器的服务启动已实现智能判断")
	fmt.Println("✅ 重复调用时会跳过已运行/已启用的服务")
	fmt.Println()
	fmt.Println("🎯 期望的效果:")
	fmt.Println("- 第一次调用：显示启动/启用命令")
	fmt.Println("- 第二次调用：显示'服务已在运行'或'服务已启用'")
	fmt.Println("- 避免重复执行systemctl命令")
	fmt.Println("- 提供清晰的状态反馈")
} 