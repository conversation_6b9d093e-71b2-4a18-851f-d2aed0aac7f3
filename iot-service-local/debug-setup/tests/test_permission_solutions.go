package main

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/addx/iot-debug-setup/internal/utils"
)

func main() {
	fmt.Println("🧪 测试权限解决方案")
	fmt.Println("===================")
	
	// 创建权限管理器
	pm := utils.NewPermissionManager()
	
	// 测试路径
	testPaths := []string{
		"/mnt/camera_data",
		"/opt/test_directory",
		"/usr/local/test_directory",
	}
	
	// Windows环境下使用不同的测试路径
	if runtime.GOOS == "windows" {
		testPaths = []string{
			"C:/Program Files/test_directory",
			"C:/Windows/test_directory",
			filepath.Join(os.Getenv("USERPROFILE"), "test_directory"),
		}
	}
	
	fmt.Printf("🖥️  当前操作系统: %s\n", runtime.GOOS)
	fmt.Println()
	
	// 测试WSL环境检测
	wslHelper := &utils.WSLPathHelper{}
	if wslHelper.IsWSL() {
		fmt.Println("🔍 检测到WSL环境")
		for _, path := range testPaths {
			wslPath := wslHelper.GetWSLMountPath(path)
			fmt.Printf("   原始路径: %s -> WSL路径: %s\n", path, wslPath)
		}
		fmt.Println()
	}
	
	// 测试每个路径的权限情况
	for i, path := range testPaths {
		fmt.Printf("📁 测试路径 %d: %s\n", i+1, path)
		fmt.Println("----------------------------------------")
		
		// 测试直接创建
		fmt.Println("🔧 尝试直接创建目录...")
		if err := pm.CreateDirectoryWithPermission(path); err != nil {
			fmt.Printf("❌ 创建失败: %v\n", err)
			
			// 显示解决方案
			pm.ShowPermissionSolutions(path, err)
			
			// 获取推荐路径
			if recommendedPath, recErr := pm.GetRecommendedMountPath(path); recErr == nil {
				fmt.Printf("\n💡 推荐路径: %s\n", recommendedPath)
				
				// 尝试创建推荐路径
				fmt.Println("🔄 尝试创建推荐路径...")
				if err := pm.CreateDirectoryWithPermission(recommendedPath); err == nil {
					fmt.Printf("✅ 推荐路径创建成功: %s\n", recommendedPath)
					
					// 清理测试目录
					if err := os.RemoveAll(recommendedPath); err != nil {
						fmt.Printf("⚠️  清理测试目录失败: %v\n", err)
					} else {
						fmt.Printf("🧹 清理测试目录成功\n")
					}
				} else {
					fmt.Printf("❌ 推荐路径创建失败: %v\n", err)
				}
			} else {
				fmt.Printf("❌ 获取推荐路径失败: %v\n", recErr)
			}
		} else {
			fmt.Printf("✅ 直接创建成功: %s\n", path)
			
			// 清理测试目录
			if err := os.RemoveAll(path); err != nil {
				fmt.Printf("⚠️  清理测试目录失败: %v\n", err)
			} else {
				fmt.Printf("🧹 清理测试目录成功\n")
			}
		}
		
		fmt.Println()
	}
	
	// 演示完整的权限解决方案
	fmt.Println("📋 权限问题完整解决方案演示:")
	fmt.Println("=====================================")
	
	problemPath := "/mnt/camera_data"
	if runtime.GOOS == "windows" {
		problemPath = "C:/Program Files/camera_data"
	}
	
	fmt.Printf("🎯 问题路径: %s\n", problemPath)
	
	// 模拟权限错误
	permissionErr := fmt.Errorf("Permission denied")
	pm.ShowPermissionSolutions(problemPath, permissionErr)
	
	fmt.Println("\n🎉 测试完成！")
	
	// 实际使用指南
	fmt.Println("\n📖 实际使用指南:")
	fmt.Println("1. 在SAMBA管理器中已经集成了权限管理器")
	fmt.Println("2. 如果权限不足，会自动显示解决方案")
	fmt.Println("3. 会尝试推荐合适的替代路径")
	fmt.Println("4. 支持WSL环境的路径转换")
	fmt.Println("5. 适配不同操作系统的权限模式")
} 