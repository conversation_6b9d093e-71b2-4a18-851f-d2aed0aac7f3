package main

import (
	"fmt"
	"os"

	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/nfs"
	"github.com/addx/iot-debug-setup/internal/service"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// 测试程序：验证重构后的服务管理功能
func main() {
	fmt.Println("🧪 测试重构后的服务管理功能")
	fmt.Println("==============================")

	// 模拟配置
	config := &types.Config{
		TargetDevice: types.TargetDevice{
			IP: "*************",
			SSH: struct {
				Username string `yaml:"username"`
				Password string `yaml:"password"`
				Port     int    `yaml:"port"`
				KeyFile  string `yaml:"key_file"`
			}{
				Username: "root",
				Password: "password",
				Port:     22,
			},
		},
		LocalMachine: types.LocalMachine{
			IP:         "**************",
			MountPoint: "/mnt/camera_data",
		},
		FileSharing: types.FileSharing{
			Type: "samba",
			Directories: []types.ShareDirectory{
				{
					Source:      "/data/sqlite",
					ShareName:   "sqlite",
					Description: "SQLite数据库目录",
				},
			},
		},
	}

	// 创建SSH客户端
	sshClient := ssh.NewClient(config.TargetDevice.IP, config.TargetDevice.SSH.Port, config.TargetDevice.SSH.Username, config.TargetDevice.SSH.Password)

	// 尝试连接SSH
	if err := sshClient.Connect(); err != nil {
		fmt.Printf("❌ SSH连接失败: %v\n", err)
		fmt.Println("⚠️  无法测试远程服务器功能")
		fmt.Println("💡 提示：请确保目标设备可访问并且SSH服务正常运行")
		os.Exit(1)
	}
	defer sshClient.Close()

	fmt.Printf("✅ SSH连接成功: %s:%d\n", config.TargetDevice.IP, config.TargetDevice.SSH.Port)
	fmt.Println()

	// 测试1：抽象服务管理器功能
	fmt.Println("1️⃣  测试抽象服务管理器功能")
	fmt.Println("----------------------------------")
	
	serviceManager := service.NewLinuxServiceManager(sshClient)
	
	// 测试单个服务管理
	testService := "sshd"
	fmt.Printf("🔧 测试服务: %s\n", testService)
	
	// 检查服务状态
	if active, err := serviceManager.IsServiceActive(testService); err == nil {
		fmt.Printf("🔍 %s 服务状态: %t\n", testService, active)
	}
	
	// 启动服务
	if err := serviceManager.StartService(testService); err != nil {
		fmt.Printf("⚠️  启动服务失败: %v\n", err)
	}
	
	// 启用服务
	if err := serviceManager.EnableService(testService); err != nil {
		fmt.Printf("⚠️  启用服务失败: %v\n", err)
	}
	
	fmt.Println()

	// 测试2：服务组管理功能
	fmt.Println("2️⃣  测试服务组管理功能")
	fmt.Println("---------------------------")
	
	// 创建SAMBA服务组
	sambaGroup := service.CreateSambaServiceGroup(serviceManager)
	fmt.Printf("📋 SAMBA服务组: %v\n", sambaGroup.Services)
	
	// 检查服务组状态
	fmt.Println("🔍 检查SAMBA服务组状态...")
	sambaGroup.CheckStatus()
	
	// 启动服务组
	fmt.Println("🚀 启动SAMBA服务组...")
	if err := sambaGroup.StartAll(); err != nil {
		fmt.Printf("⚠️  启动服务组失败: %v\n", err)
	}
	
	fmt.Println()

	// 测试3：NFS服务组
	fmt.Println("3️⃣  测试NFS服务组")
	fmt.Println("-------------------")
	
	nfsGroup := service.CreateNFSServiceGroup(serviceManager)
	fmt.Printf("📋 NFS服务组: %v\n", nfsGroup.Services)
	
	// 检查并启动NFS服务组
	fmt.Println("🔍 检查NFS服务组状态...")
	nfsGroup.CheckStatus()
	
	fmt.Println("🚀 启动NFS服务组...")
	if err := nfsGroup.StartAll(); err != nil {
		fmt.Printf("⚠️  启动服务组失败: %v\n", err)
	}
	
	fmt.Println()

	// 测试4：集成到现有管理器
	fmt.Println("4️⃣  测试集成到现有管理器")
	fmt.Println("----------------------------")
	
	// 测试重构后的SAMBA管理器
	fmt.Println("🔧 测试重构后的SAMBA管理器...")
	sambaManager := fileshare.NewSambaManager(config, sshClient)
	if err := sambaManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  SAMBA设置失败: %v\n", err)
	} else {
		fmt.Println("✅ SAMBA设置成功（使用抽象服务管理器）")
	}
	
	// 测试重构后的NFS管理器
	fmt.Println("🔧 测试重构后的NFS管理器...")
	nfsManager := nfs.NewManager(config)
	nfsManager.SetSSHClient(sshClient)
	if err := nfsManager.SetupServer(); err != nil {
		fmt.Printf("⚠️  NFS设置失败: %v\n", err)
	} else {
		fmt.Println("✅ NFS设置成功（使用抽象服务管理器）")
	}
	
	fmt.Println()

	// 测试5：验证重复调用的智能判断
	fmt.Println("5️⃣  验证重复调用的智能判断")
	fmt.Println("-------------------------------")
	
	fmt.Println("🔄 第二次启动SAMBA服务组...")
	if err := sambaGroup.StartAll(); err != nil {
		fmt.Printf("⚠️  第二次启动失败: %v\n", err)
	}
	
	fmt.Println("🔄 第二次启动NFS服务组...")
	if err := nfsGroup.StartAll(); err != nil {
		fmt.Printf("⚠️  第二次启动失败: %v\n", err)
	}
	
	fmt.Println()
	fmt.Println("🎉 测试完成！")
	
	// 总结
	fmt.Println()
	fmt.Println("📋 重构效果总结:")
	fmt.Println("==================")
	fmt.Println("✅ 消除了重复代码")
	fmt.Println("✅ 创建了抽象服务管理器")
	fmt.Println("✅ 实现了服务组管理")
	fmt.Println("✅ 保持了智能判断功能")
	fmt.Println("✅ 提高了代码的可维护性")
	fmt.Println("✅ 更加面向对象的设计")
	fmt.Println()
	fmt.Println("🎯 代码改进:")
	fmt.Println("- 所有服务管理逻辑集中在service包中")
	fmt.Println("- 各个管理器只需要调用抽象接口")
	fmt.Println("- 支持服务组批量管理")
	fmt.Println("- 更容易扩展和测试")
} 