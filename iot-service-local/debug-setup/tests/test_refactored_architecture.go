package main

import (
	"fmt"
	"log"

	"github.com/addx/iot-debug-setup/internal/cleanup"
	"github.com/addx/iot-debug-setup/internal/config"
	"github.com/addx/iot-debug-setup/internal/connection"
	"github.com/addx/iot-debug-setup/internal/diagnosis"
	"github.com/addx/iot-debug-setup/internal/errors"
	"github.com/addx/iot-debug-setup/internal/factory"
	"github.com/addx/iot-debug-setup/internal/types"
)

func main() {
	fmt.Println("🔧 重构后架构测试")
	fmt.Println("================================================================================")
	
	// 测试配置加载器
	testConfigLoader()
	
	// 测试连接管理器
	testConnectionManager()
	
	// 测试工厂模式
	testManagerFactory()
	
	// 测试诊断管理器
	testDiagnosisManager()
	
	// 测试清理管理器
	testCleanupManager()
	
	// 测试错误建议系统
	testErrorSuggestions()
	
	fmt.Println("\n🎉 所有测试完成！")
}

// 测试配置加载器
func testConfigLoader() {
	fmt.Println("\n📋 测试配置加载器...")
	
	// 模拟配置文件路径
	configPath := "configs/test_config.yml"
	
	// 使用新的配置加载器
	cfg, err := config.LoadConfigFromPath(configPath)
	if err != nil {
		fmt.Printf("❌ 配置加载失败: %v\n", err)
		// 展示验证结果
		loader := config.NewLoader(configPath)
		if loader != nil {
			result := loader.ValidateWithResult()
			loader.PrintValidationResult(result)
		}
		return
	}
	
	fmt.Printf("✅ 配置加载成功: %s\n", cfg.TargetDevice.IP)
	fmt.Printf("   文件共享类型: %s\n", cfg.FileSharing.Type)
	fmt.Printf("   共享目录数量: %d\n", len(cfg.FileSharing.Directories))
}

// 测试连接管理器
func testConnectionManager() {
	fmt.Println("\n🔗 测试连接管理器...")
	
	// 创建测试配置
	cfg := &types.Config{
		TargetDevice: types.TargetDevice{
			IP: "*************",
			SSH: struct {
				Username string `yaml:"username"`
				Password string `yaml:"password"`
				Port     int    `yaml:"port"`
				KeyFile  string `yaml:"key_file"`
			}{
				Username: "testuser",
				Password: "testpass",
				Port:     22,
			},
		},
	}
	
	// 创建连接管理器
	connManager := connection.NewManager(cfg)
	
	// 创建事件监听器
	eventListener := connection.NewEventListener(connManager, func(event connection.ConnectionEvent) {
		fmt.Printf("   事件: %s - %s\n", event.Type, event.Status.String())
		if event.Error != nil {
			fmt.Printf("   错误: %v\n", event.Error)
		}
	})
	
	// 开始监听事件
	eventListener.Start()
	defer eventListener.Stop()
	
	// 模拟连接（实际环境中会真正连接）
	fmt.Println("   模拟连接过程...")
	fmt.Printf("   连接状态: %s\n", connManager.GetStatus().String())
	
	// 获取连接信息
	info := connManager.GetConnectionInfo()
	fmt.Printf("   目标IP: %v\n", info["target_ip"])
	fmt.Printf("   目标端口: %v\n", info["target_port"])
	fmt.Printf("   用户名: %v\n", info["username"])
	
	fmt.Println("✅ 连接管理器测试完成")
}

// 测试管理器工厂
func testManagerFactory() {
	fmt.Println("\n🏭 测试管理器工厂...")
	
	// 创建测试配置
	cfg := &types.Config{
		TargetDevice: types.TargetDevice{
			IP: "*************",
			SSH: struct {
				Username string `yaml:"username"`
				Password string `yaml:"password"`
				Port     int    `yaml:"port"`
				KeyFile  string `yaml:"key_file"`
			}{
				Username: "testuser",
				Password: "testpass",
				Port:     22,
			},
		},
		FileSharing: types.FileSharing{
			Type: "samba",
			Directories: []types.ShareDirectory{
				{
					Source:    "/tmp/test",
					ShareName: "test_share",
					Options:   "rw,sync",
				},
			},
		},
	}
	
	// 使用工厂创建管理器集合
	factory := factory.NewManagerFactory(cfg)
	
	// 创建基础管理器集合
	managerSet, err := factory.CreateManagerSet()
	if err != nil {
		fmt.Printf("❌ 创建管理器集合失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ 基础管理器集合创建成功")
	
	// 使用构建器创建自定义管理器集合
	builder := factory.NewManagerBuilder(cfg)
	customManagerSet, err := builder.
		WithComponent("config").
		WithComponent("diagnosis").
		WithComponent("cleanup").
		Build()
	
	if err != nil {
		fmt.Printf("❌ 创建自定义管理器集合失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ 自定义管理器集合创建成功")
	
	// 展示可用的管理器
	fmt.Printf("   配置管理器: %t\n", customManagerSet.ConfigManager != nil)
	fmt.Printf("   诊断管理器: %t\n", customManagerSet.DiagnosisManager != nil)
	fmt.Printf("   清理管理器: %t\n", customManagerSet.CleanupManager != nil)
	
	// 清理资源
	managerSet.Cleanup()
	customManagerSet.Cleanup()
}

// 测试诊断管理器
func testDiagnosisManager() {
	fmt.Println("\n🔍 测试诊断管理器...")
	
	// 创建测试配置
	cfg := &types.Config{
		FileSharing: types.FileSharing{
			Type: "samba",
		},
		DockerControl: types.DockerControl{
			Enabled: true,
			Containers: []types.Container{
				{
					Name:        "test_container",
					Description: "测试容器",
				},
			},
		},
	}
	
	// 创建诊断管理器
	diagManager := diagnosis.NewManager(cfg)
	
	// 测试诊断表格生成
	testResults := []diagnosis.DiagnosisTableRow{
		{
			Component: "SSH连接",
			Status:    "✅ 正常",
			Detail:    "*************:22",
		},
		{
			Component: "SAMBA文件共享",
			Status:    "❌ 异常",
			Detail:    "服务未运行",
		},
		{
			Component: "Docker服务",
			Status:    "✅ 正常",
			Detail:    "版本 20.10.8",
		},
	}
	
	fmt.Println("   生成诊断表格:")
	diagManager.GenerateDiagnosisTable(testResults)
	
	// 测试启用状态显示
	fmt.Printf("   Docker控制状态: %s\n", diagManager.GetEnabledStatus(cfg.DockerControl.Enabled))
	fmt.Printf("   NFS控制状态: %s\n", diagManager.GetEnabledStatus(false))
	
	fmt.Println("✅ 诊断管理器测试完成")
}

// 测试清理管理器
func testCleanupManager() {
	fmt.Println("\n🧹 测试清理管理器...")
	
	// 创建测试配置
	cfg := &types.Config{
		FileSharing: types.FileSharing{
			Type: "samba",
		},
		DockerControl: types.DockerControl{
			Enabled: true,
		},
	}
	
	// 创建清理管理器
	cleanupManager := cleanup.NewManager(cfg)
	
	// 使用任务构建器
	builder := cleanup.NewTaskBuilder(cleanupManager)
	
	// 模拟添加清理任务
	fmt.Println("   添加清理任务...")
	
	// 创建模拟任务
	type mockTask struct {
		name     string
		taskType string
		shouldFail bool
	}
	
	mockTask1 := &mockTask{
		name:     "清理测试端口",
		taskType: "port",
		shouldFail: false,
	}
	
	mockTask2 := &mockTask{
		name:     "清理测试文件共享",
		taskType: "fileshare",
		shouldFail: false,
	}
	
	// 实现CleanupTask接口
	type mockCleanupTask struct {
		*mockTask
	}
	
	func (m *mockCleanupTask) Execute() error {
		if m.shouldFail {
			return fmt.Errorf("模拟任务失败")
		}
		return nil
	}
	
	func (m *mockCleanupTask) GetName() string {
		return m.name
	}
	
	func (m *mockCleanupTask) GetType() string {
		return m.taskType
	}
	
	// 添加模拟任务
	cleanupManager.AddTask(&mockCleanupTask{mockTask1})
	cleanupManager.AddTask(&mockCleanupTask{mockTask2})
	
	// 执行清理任务
	fmt.Println("   执行清理任务:")
	results := cleanupManager.ExecuteAll()
	
	// 显示结果
	fmt.Println("   清理结果:")
	for _, result := range results {
		status := "✅ 成功"
		if !result.Success {
			status = "❌ 失败"
		}
		fmt.Printf("   - %s: %s\n", result.TaskName, status)
	}
	
	fmt.Println("✅ 清理管理器测试完成")
}

// 测试错误建议系统
func testErrorSuggestions() {
	fmt.Println("\n💡 测试错误建议系统...")
	
	// 初始化建议管理器
	errors.InitSuggestionManager()
	
	// 测试不同类型的错误建议
	testCases := []struct {
		errorType errors.ErrorType
		errorMsg  string
		desc      string
	}{
		{
			errorType: errors.ErrorTypeNFS,
			errorMsg:  "nfsd module not found",
			desc:      "NFS模块错误",
		},
		{
			errorType: errors.ErrorTypeSamba,
			errorMsg:  "smbd command not found",
			desc:      "SAMBA命令错误",
		},
		{
			errorType: errors.ErrorTypeDocker,
			errorMsg:  "permission denied docker",
			desc:      "Docker权限错误",
		},
		{
			errorType: errors.ErrorTypeSSH,
			errorMsg:  "connection refused",
			desc:      "SSH连接错误",
		},
	}
	
	for _, tc := range testCases {
		fmt.Printf("   测试 %s:\n", tc.desc)
		
		// 创建错误
		err := fmt.Errorf("%s", tc.errorMsg)
		
		// 获取建议
		suggestions := errors.GetSuggestionsForError(tc.errorType, err)
		
		// 显示建议
		for i, suggestion := range suggestions {
			fmt.Printf("     %d. %s\n", i+1, suggestion)
		}
		
		if len(suggestions) == 0 {
			fmt.Println("     无具体建议")
		}
		
		fmt.Println()
	}
	
	fmt.Println("✅ 错误建议系统测试完成")
}

// 工具函数
func logError(operation string, err error) {
	if err != nil {
		log.Printf("❌ %s失败: %v", operation, err)
	} else {
		log.Printf("✅ %s成功", operation)
	}
} 