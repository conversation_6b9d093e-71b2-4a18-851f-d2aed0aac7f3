# IoT调试环境自动化设置工具

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com/addx/iot-debug-setup)

本工具用于自动化设置IoT设备调试环境，解决在本地开发环境中调试运行在Linux设备上的Spring Boot应用程序的问题。

## 🎯 功能特性

- **🚀 跨平台支持**：支持Windows、Linux、macOS
- **📁 多种文件共享**：默认使用SAMBA，支持NFS、SSHFS多种共享方式
- **🔧 自动化设置**：一键设置文件共享服务，并在本地挂载
- **🔗 端口转发**：自动设置iptables规则和本地端口转发
- **⚙️ 配置文件自动化**：支持修改多个Spring Boot配置文件
- **🔍 诊断功能**：内置问题诊断和解决方案建议
- **🧹 一键清理**：程序退出时自动清理所有设置
- **🔒 安全可靠**：自动备份配置文件和iptables规则

## 📦 快速开始

### 安装

**方式1：下载预编译版本**

从 [Releases](https://github.com/addx/iot-debug-setup/releases) 页面下载适合您系统的预编译版本。

**方式2：从源码构建**

```bash
git clone https://github.com/addx/iot-debug-setup.git
cd iot-debug-setup
make build
```

### 基本使用

1. **配置环境**

```bash
cp configs/debug_config.yml my_config.yml
# 编辑 my_config.yml，设置您的录像机IP和SSH信息
```

2. **运行工具**

```bash
# 正常运行
./iot-debug-setup my_config.yml

# 诊断模式（当NFS启动失败时使用）
./iot-debug-setup --diagnose my_config.yml
```

3. **开始调试**

保持工具运行，在您的IDE中启动Spring Boot应用的调试模式。

4. **退出清理**

按 `Ctrl+C` 退出，工具会自动清理所有设置。

## 🔧 环境要求

### 开发机要求
- Go 1.21+（仅构建时需要）
- NFS客户端支持
- SSH客户端
- 管理员权限（用于挂载NFS和设置端口转发）

### 录像机要求
- Linux系统
- SSH服务器
- root权限
- iptables支持
- NFS服务器支持

## 📂 项目结构

```
debug-setup/
├── cmd/                    # 主应用程序
│   └── iot-debug-setup/
├── internal/               # 内部包
│   ├── app/               # 应用程序逻辑
│   ├── config/            # 配置管理
│   ├── nfs/               # NFS管理
│   ├── port/              # 端口管理
│   ├── ssh/               # SSH客户端
│   └── types/             # 类型定义
├── configs/               # 配置文件
├── docs/                  # 文档
├── scripts/               # 构建脚本
├── go.mod                 # Go模块
├── go.sum                 # 依赖锁定
├── Makefile              # 构建配置
└── README.md             # 项目说明
```

## 🛠️ 开发

### 构建

```bash
# 构建当前平台
make build

# 构建所有平台
make build-all

# 运行测试
make test

# 清理构建文件
make clean
```

### 配置示例

```yaml
target_device:
  ip: "*************"
  ssh:
    username: "root"
    password: "your_password"
    port: 22

local_machine:
  ip: "**************"
  nfs_mount_point: "/mnt/camera_data"

execution_steps:
  setup_nfs: true
  setup_port_forwarding: true
  update_config_file: true
  cleanup_on_exit: true
```

## 🔗 与IDE集成

### IntelliJ IDEA

1. 打开 `Run/Debug Configurations`
2. 添加 `External tool`：
   - **Program**: `iot-debug-setup`的完整路径
   - **Arguments**: 配置文件路径
   - **Working directory**: 项目根目录
3. 在 `Before launch` 中添加该外部工具

### VS Code

在 `tasks.json` 中添加：

```json
{
  "label": "setup-debug-env",
  "type": "shell",
  "command": "./iot-debug-setup",
  "args": ["configs/debug_config.yml"]
}
```

## 📁 文件共享选项

### 支持的共享类型

**SAMBA（默认推荐）**
- ✅ 跨平台兼容性最好
- ✅ 不依赖内核模块
- ✅ Windows原生支持
- ✅ 配置简单

**NFS（传统方案）**
- ⚠️ 需要内核支持
- ⚠️ Windows支持有限
- ✅ 性能较好
- ❌ 可能遇到内核模块问题

**SSHFS（备用方案）**
- ✅ 只需SSH连接
- ✅ 安全性高
- ❌ Windows支持有限
- ⚠️ 性能相对较低

### 切换文件共享类型

在配置文件中修改 `file_sharing.type`：

```yaml
file_sharing:
  type: "samba"  # 或 "nfs" 或 "sshfs"
```

### 📍 文件路径映射说明 **（重要）**

**路径映射原理：**

文件共享系统会将远程目录挂载到本地，路径映射关系如下：

```
远程录像机目录 → 本地挂载点 → 应用程序访问路径
```

**SAMBA路径映射示例：**

```yaml
file_sharing:
  directories:
    - source: "/data/sqlite"          # 录像机上的实际目录
      share_name: "sqlite"            # 本地挂载的子目录名
      description: "SQLite数据库目录"
```

**映射结果：**
- 远程路径：`/data/sqlite/camera.db`
- 本地挂载：`/mnt/camera_data/sqlite/camera.db`
- JDBC连接：`*********************************************`

**⚠️ 重要注意事项：**

1. **`share_name` 必须与配置文件中的路径匹配**
2. **路径不匹配会导致应用程序找不到文件**
3. **不同共享类型的路径映射方式不同**

**完整映射表：**

| 远程目录 | share_name | 本地挂载路径 | 应用配置路径 |
|----------|------------|-------------|-------------|
| `/data/sqlite` | `sqlite` | `/mnt/camera_data/sqlite/` | `{{.local_mount_path}}/sqlite/` |
| `/data/iot-service` | `iot-service` | `/mnt/camera_data/iot-service/` | `{{.local_mount_path}}/iot-service/` |
| `/tmp/ssd` | `ssd` | `/mnt/camera_data/ssd/` | `{{.local_mount_path}}/ssd/` |
| `/addx/temp` | `temp` | `/mnt/camera_data/temp/` | `{{.local_mount_path}}/temp/` |
| `/addx/firmware` | `firmware` | `/mnt/camera_data/firmware/` | `{{.local_mount_path}}/firmware/` |

**调试路径问题：**

如果遇到文件找不到的问题，请检查：
1. 挂载是否成功：`ls -la /mnt/camera_data/`
2. 文件是否存在：`ls -la /mnt/camera_data/sqlite/`
3. 权限是否正确：`ls -la /mnt/camera_data/sqlite/camera.db`

## 🔧 高级配置

### 配置文件自动定位

IoT Debug Setup支持Spring Boot的多配置文件机制。只需指定项目根目录，程序会自动定位标准的配置文件：

```yaml
config_replacements:
  # 项目根目录（绝对路径）
  project_root: "C:/Users/<USER>/Project/java/iot-service-unified/iot-service-local"
  
  # 程序会自动根据project_root构建以下路径：
  # - {project_root}/src/main/resources/application.yml
  # - {project_root}/src/main/resources/application-test.yml
  
  replacements:
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
```

**工作原理**：
- 程序根据`project_root`自动构建Spring Boot标准配置文件路径
- 使用绝对路径确保无论程序在哪里运行都能正确找到配置文件
- 同样的替换规则会应用到所有配置文件
- 自动备份和恢复功能支持多个文件
- 支持Windows、Linux、macOS的路径分隔符自动转换

**Spring Boot配置加载顺序**：
1. `application.yml` - 基础配置
2. `application-test.yml` - 测试环境配置
3. 后加载的文件会覆盖前面的相同配置项

## 🚨 故障排除

### NFS服务启动失败

如果遇到 `systemctl start nfs-server` 失败，请使用诊断模式：

```bash
./iot-debug-setup --diagnose my_config.yml
```

**常见错误及解决方案：**

1. **"A dependency job for nfs-server.service failed"**
   - 检查 `rpcbind` 服务是否正常运行
   - 运行：`sudo systemctl start rpcbind && sudo systemctl enable rpcbind`
   - 不同系统的NFS服务名可能不同：
     - `nfs-utils` (ARM系统常用)
     - `nfs-server` (CentOS/RHEL)
     - `nfs-kernel-server` (Ubuntu/Debian)

2. **端口被占用**
   - 检查111端口（rpcbind）和2049端口（NFS）是否被占用
   - 运行：`sudo netstat -tulpn | grep :111`

3. **防火墙阻塞**
   - 开放NFS相关端口：`sudo ufw allow 111 && sudo ufw allow 2049`

4. **NFS软件包缺失**
   - Ubuntu/Debian：`sudo apt-get install nfs-kernel-server nfs-common`
   - CentOS/RHEL：`sudo yum install nfs-utils rpcbind`

### SSH连接问题

- 确认SSH凭据正确
- 检查目标设备的SSH服务状态
- 验证网络连接

### 权限问题

- 确保运行程序的用户有管理员权限
- 检查NFS导出目录的权限设置

## 📋 使用场景

- **远程调试**：在本地IDE中调试运行在Linux设备上的Spring Boot应用
- **开发测试**：快速设置测试环境，无需手动配置网络和文件系统
- **团队协作**：标准化的开发环境设置流程
- **CI/CD集成**：自动化测试和部署流程

## 🔍 故障排除

查看 [故障排除指南](docs/TROUBLESHOOTING.md) 了解常见问题的解决方案。

## 📖 文档

- [快速开始指南](docs/QUICKSTART.md)
- [配置说明](docs/CONFIGURATION.md)
- [故障排除](docs/TROUBLESHOOTING.md)
- [开发指南](docs/DEVELOPMENT.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork本仓库
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证。查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](https://github.com/addx/iot-debug-setup/issues)
3. 创建新的Issue

## 🌟 致谢

感谢所有贡献者和使用者的支持！

---

**Happy Debugging!** 🎉 