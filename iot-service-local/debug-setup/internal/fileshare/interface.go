package fileshare

import (
	"github.com/addx/iot-debug-setup/internal/types"
)

// FileShareManager 文件共享管理器接口
type FileShareManager interface {
	// 设置服务器端文件共享
	SetupServer() error
	
	// 在本地挂载文件共享
	MountOnLocal() error
	
	// 清理文件共享设置
	Cleanup() error
	
	// 诊断文件共享问题（带输出）
	DiagnoseIssues() error
	
	// 静默诊断文件共享状态（无输出）
	DiagnoseStatus() (bool, string, error)
	
	// 获取文件共享类型
	GetShareType() string
}

// CreateFileShareManager 工厂函数，根据配置创建相应的文件共享管理器
func CreateFileShareManager(config *types.Config, sshClient interface{}) (FileShareManager, error) {
	shareType := config.FileSharing.Type
	if shareType == "" {
		shareType = "samba" // 默认使用SAMBA
	}
	
	switch shareType {
	case "samba":
		return NewSambaManager(config, sshClient), nil
	case "nfs":
		return NewNFSManager(config, sshClient), nil
	case "sshfs":
		return NewSSHFSManager(config, sshClient), nil
	default:
		return NewSambaManager(config, sshClient), nil // 默认fallback到SAMBA
	}
} 