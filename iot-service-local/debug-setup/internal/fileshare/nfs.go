package fileshare

import (
	"fmt"

	"github.com/addx/iot-debug-setup/internal/nfs"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// NFSManager NFS文件共享管理器（包装器）
type NFSManager struct {
	config     *types.Config
	nfsManager *nfs.Manager
	sshClient  *ssh.Client
}

// NewNFSManager 创建新的NFS管理器包装器
func NewNFSManager(config *types.Config, sshClient interface{}) *NFSManager {
	// 将新的FileSharing配置转换为旧的NFSExports配置
	legacyConfig := convertToLegacyConfig(config)
	
	sshClientTyped := sshClient.(*ssh.Client)
	nfsManager := nfs.NewManager(legacyConfig)
	nfsManager.SetSSHClient(sshClientTyped)
	
	return &NFSManager{
		config:     config,
		nfsManager: nfsManager,
		sshClient:  sshClientTyped,
	}
}

// convertToLegacyConfig 将新的配置格式转换为旧的NFS配置格式
func convertToLegacyConfig(config *types.Config) *types.Config {
	// 创建旧格式的配置
	legacyConfig := &types.Config{
		TargetDevice:       config.TargetDevice,
		LocalMachine:       types.LocalMachine{
			IP:         config.LocalMachine.IP,
			NFSMountPoint: config.LocalMachine.MountPoint, // 转换字段名
		},
		PortForwarding:     config.PortForwarding,
		ConfigReplacements: config.ConfigReplacements,
		DockerControl:      config.DockerControl,
		ExecutionSteps:     types.ExecutionSteps{
			SetupNFS:            config.ExecutionSteps.SetupFileSharing, // 转换字段名
			SetupPortForwarding: config.ExecutionSteps.SetupPortForwarding,
			UpdateConfigFile:    config.ExecutionSteps.UpdateConfigFile,
			SetupDockerControl:  config.ExecutionSteps.SetupDockerControl,
			CleanupOnExit:       config.ExecutionSteps.CleanupOnExit,
		},
	}
	
	// 创建NFSExports结构
	nfsExports := types.NFSExports{
		Directories: make([]types.NFSDirectory, 0),
	}
	
	// 转换目录配置
	for _, dir := range config.FileSharing.Directories {
		nfsDir := types.NFSDirectory{
			Source:     dir.Source,
			ExportPath: dir.ShareName, // 对于NFS，ShareName就是ExportPath
			Options:    dir.Options,
		}
		nfsExports.Directories = append(nfsExports.Directories, nfsDir)
	}
	
	// 设置NFSExports字段
	legacyConfig.NFSExports = nfsExports
	
	return legacyConfig
}

// SetupServer 在远程服务器上设置NFS服务
func (s *NFSManager) SetupServer() error {
	fmt.Println("🔧 设置NFS文件共享服务...")
	return s.nfsManager.SetupNFS()
}

// MountOnLocal 在本地挂载NFS共享
func (s *NFSManager) MountOnLocal() error {
	fmt.Println("🔗 在本地挂载NFS共享...")
	// NFS管理器的SetupNFS已经包含了挂载逻辑
	return nil
}

// Cleanup 清理挂载和配置
func (s *NFSManager) Cleanup() error {
	fmt.Println("🧹 清理NFS挂载...")
	return s.nfsManager.Cleanup()
}

// DiagnoseIssues 诊断NFS问题
func (s *NFSManager) DiagnoseIssues() error {
	fmt.Println("🔍 开始诊断NFS问题...")
	return s.nfsManager.DiagnoseNFSIssues()
}

// GetShareType 获取共享类型
func (s *NFSManager) GetShareType() string {
	return "nfs"
}

// DiagnoseStatus 静默诊断NFS状态
func (s *NFSManager) DiagnoseStatus() (bool, string, error) {
	// 检查rpcbind服务状态
	rpcStatus, err := s.sshClient.ExecuteCommand("systemctl is-active rpcbind")
	if err != nil || rpcStatus != "active" {
		return false, "rpcbind服务未运行", nil
	}
	
	// 检查NFS服务状态
	nfsStatus, err := s.sshClient.ExecuteCommand("systemctl is-active nfs-server")
	if err != nil || nfsStatus != "active" {
		return false, "nfs-server服务未运行", nil
	}
	
	// 检查端口监听
	port111, err := s.sshClient.ExecuteCommand("netstat -tulpn | grep ':111' | wc -l")
	if err != nil || port111 == "0" {
		return false, "端口111未监听", nil
	}
	
	return true, "服务运行正常", nil
} 