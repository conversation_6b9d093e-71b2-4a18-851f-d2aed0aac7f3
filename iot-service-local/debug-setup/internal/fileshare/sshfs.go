package fileshare

import (
	"fmt"
	"os/exec"
	"runtime"
	"strings"

	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// SSHFSManager SSHFS文件共享管理器
type SSHFSManager struct {
	config    *types.Config
	sshClient *ssh.Client
	mounted   []string // 记录已挂载的路径，用于清理
}

// NewSSHFSManager 创建新的SSHFS管理器
func NewSSHFSManager(config *types.Config, sshClient interface{}) *SSHFSManager {
	return &SSHFSManager{
		config:    config,
		sshClient: sshClient.(*ssh.Client),
		mounted:   make([]string, 0),
	}
}

// SetupServer 在远程服务器上设置SSHFS服务（实际上SSHFS不需要特殊服务）
func (s *SSHFSManager) SetupServer() error {
	fmt.Println("🔧 设置SSHFS文件共享服务...")
	
	// SSHFS只需要SSH服务，检查SSH连接是否正常
	if err := s.sshClient.TestConnection(); err != nil {
		return fmt.Errorf("SSH连接测试失败: %v", err)
	}

	// 确保远程目录存在
	for _, dir := range s.config.FileSharing.Directories {
		if !s.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("📁 创建远程目录: %s\n", dir.Source)
			if err := s.sshClient.CreateDirectory(dir.Source); err != nil {
				return fmt.Errorf("创建远程目录失败 %s: %v", dir.Source, err)
			}
		}
	}

	fmt.Println("✅ SSHFS文件共享服务设置完成（使用SSH协议）")
	return nil
}

// MountOnLocal 在本地挂载SSHFS
func (s *SSHFSManager) MountOnLocal() error {
	fmt.Println("🔗 在本地挂载SSHFS...")

	// 创建挂载点
	mountPoint := s.config.LocalMachine.MountPoint
	if err := s.createMountPoint(mountPoint); err != nil {
		return fmt.Errorf("创建挂载点失败: %v", err)
	}

	// 确保本地有SSHFS支持
	if err := s.ensureSSHFSInstalled(); err != nil {
		return fmt.Errorf("安装SSHFS失败: %v", err)
	}

	// 挂载每个目录
	for _, dir := range s.config.FileSharing.Directories {
		localPath := fmt.Sprintf("%s/%s", mountPoint, dir.ShareName)
		if err := s.mountSingleSSHFS(dir.Source, localPath); err != nil {
			fmt.Printf("⚠️  挂载目录失败 %s: %v\n", dir.Source, err)
			continue
		}
	}

	fmt.Println("✅ SSHFS挂载完成")
	return nil
}

// createMountPoint 创建挂载点
func (s *SSHFSManager) createMountPoint(path string) error {
	var cmd string
	switch runtime.GOOS {
	case "windows":
		// Windows下SSHFS支持有限
		return fmt.Errorf("Windows暂不支持SSHFS，请使用SAMBA")
	default:
		cmd = fmt.Sprintf("mkdir -p %s", path)
	}

	return s.executeLocalCommand(cmd)
}

// ensureSSHFSInstalled 确保本地安装了SSHFS
func (s *SSHFSManager) ensureSSHFSInstalled() error {
	fmt.Println("📦 检查本地SSHFS安装状态...")

	switch runtime.GOOS {
	case "windows":
		return fmt.Errorf("Windows暂不支持SSHFS，请使用SAMBA")
	case "darwin":
		// macOS使用Homebrew安装
		if s.isSSHFSInstalled() {
			fmt.Println("✅ SSHFS已安装，跳过安装步骤")
			return nil
		}
		fmt.Println("📥 SSHFS未安装，开始安装...")
		fmt.Println("🔄 使用Homebrew安装SSHFS...")
		if err := s.executeLocalCommand("brew install sshfs"); err != nil {
			return fmt.Errorf("安装SSHFS失败: %v", err)
		}
		if s.isSSHFSInstalled() {
			fmt.Println("✅ SSHFS安装完成并验证成功")
		} else {
			fmt.Println("⚠️  SSHFS安装可能未完全成功，但将继续尝试使用")
		}
		return nil
	case "linux":
		// Linux使用包管理器安装
		if s.isSSHFSInstalled() {
			fmt.Println("✅ SSHFS已安装，跳过安装步骤")
			return nil
		}
		fmt.Println("📥 SSHFS未安装，开始安装...")
		// 尝试不同的包管理器
		installCmds := []string{
			"sudo apt-get update && sudo apt-get install -y sshfs",
			"sudo yum install -y fuse-sshfs",
			"sudo dnf install -y fuse-sshfs",
		}
		
		var lastErr error
		for _, cmd := range installCmds {
			fmt.Printf("🔄 尝试安装命令: %s\n", cmd)
			if err := s.executeLocalCommand(cmd); err == nil {
				if s.isSSHFSInstalled() {
					fmt.Println("✅ SSHFS安装完成并验证成功")
					return nil
				} else {
					fmt.Println("⚠️  安装命令执行成功，但SSHFS仍不可用")
				}
			} else {
				fmt.Printf("⚠️  命令失败: %v\n", err)
				lastErr = err
			}
		}
		return fmt.Errorf("安装SSHFS失败: %v", lastErr)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// isSSHFSInstalled 检查SSHFS是否已安装
func (s *SSHFSManager) isSSHFSInstalled() bool {
	// 方法1：检查sshfs命令是否存在
	if err := s.executeLocalCommand("which sshfs"); err == nil {
		fmt.Println("  🔍 找到sshfs命令")
		return true
	}

	// 方法2：检查/usr/bin/sshfs文件是否存在
	if err := s.executeLocalCommand("test -f /usr/bin/sshfs"); err == nil {
		fmt.Println("  🔍 找到sshfs文件: /usr/bin/sshfs")
		return true
	}

	// 方法3：检查/usr/local/bin/sshfs文件是否存在（macOS Homebrew）
	if err := s.executeLocalCommand("test -f /usr/local/bin/sshfs"); err == nil {
		fmt.Println("  🔍 找到sshfs文件: /usr/local/bin/sshfs")
		return true
	}

	// 方法4：检查包管理器中的sshfs包
	switch runtime.GOOS {
	case "linux":
		// 检查dpkg（Ubuntu/Debian）
		if err := s.executeLocalCommand("dpkg -l | grep -q sshfs"); err == nil {
			fmt.Println("  🔍 找到sshfs包（dpkg）")
			return true
		}
		// 检查rpm（CentOS/RHEL/Fedora）
		if err := s.executeLocalCommand("rpm -qa | grep -q fuse-sshfs"); err == nil {
			fmt.Println("  🔍 找到fuse-sshfs包（rpm）")
			return true
		}
	case "darwin":
		// 检查brew（macOS）
		if err := s.executeLocalCommand("brew list | grep -q sshfs"); err == nil {
			fmt.Println("  🔍 找到sshfs包（brew）")
			return true
		}
	}

	fmt.Println("  ❌ 未找到SSHFS安装")
	return false
}

// mountSingleSSHFS 挂载单个SSHFS目录
func (s *SSHFSManager) mountSingleSSHFS(remotePath, localPath string) error {
	fmt.Printf("🔗 挂载目录 %s 到 %s...\n", remotePath, localPath)

	// 创建本地挂载目录
	if err := s.executeLocalCommand(fmt.Sprintf("mkdir -p %s", localPath)); err != nil {
		return fmt.Errorf("创建本地目录失败: %v", err)
	}

	// 构建SSHFS挂载命令
	mountCmd := s.getSSHFSMountCommand(remotePath, localPath)
	
	// 执行挂载
	if err := s.executeLocalCommand(mountCmd); err != nil {
		return fmt.Errorf("挂载失败: %v", err)
	}

	// 记录已挂载的路径
	s.mounted = append(s.mounted, localPath)
	fmt.Printf("✅ 成功挂载: %s\n", localPath)
	return nil
}

// getSSHFSMountCommand 获取SSHFS挂载命令
func (s *SSHFSManager) getSSHFSMountCommand(remotePath, localPath string) string {
	serverIP := s.config.TargetDevice.IP
	username := s.config.TargetDevice.SSH.Username
	port := s.config.TargetDevice.SSH.Port
	
	if port == 0 {
		port = 22
	}

	// 构建SSHFS命令
	cmd := fmt.Sprintf("sshfs %s@%s:%s %s -p %d", username, serverIP, remotePath, localPath, port)
	
	// 添加常用选项
	cmd += " -o allow_other,default_permissions,uid=$(id -u),gid=$(id -g)"
	
	// 如果有SSH密钥文件
	if s.config.TargetDevice.SSH.KeyFile != "" {
		cmd += fmt.Sprintf(" -o IdentityFile=%s", s.config.TargetDevice.SSH.KeyFile)
	}

	return cmd
}

// executeLocalCommand 执行本地命令
func (s *SSHFSManager) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	if len(output) > 0 {
		fmt.Printf("  📤 输出: %s\n", strings.TrimSpace(string(output)))
	}

	return nil
}

// Cleanup 清理挂载
func (s *SSHFSManager) Cleanup() error {
	fmt.Println("🧹 清理SSHFS挂载...")

	// 卸载所有已挂载的目录
	for _, mountPath := range s.mounted {
		if err := s.unmountSSHFS(mountPath); err != nil {
			fmt.Printf("⚠️  卸载失败 %s: %v\n", mountPath, err)
		}
	}

	s.mounted = s.mounted[:0] // 清空记录
	fmt.Println("✅ SSHFS清理完成")
	return nil
}

// unmountSSHFS 卸载SSHFS
func (s *SSHFSManager) unmountSSHFS(path string) error {
	var cmd string
	switch runtime.GOOS {
	case "darwin":
		cmd = fmt.Sprintf("umount %s", path)
	default:
		cmd = fmt.Sprintf("fusermount -u %s", path)
	}

	return s.executeLocalCommand(cmd)
}

// DiagnoseIssues 诊断SSHFS问题
func (s *SSHFSManager) DiagnoseIssues() error {
	fmt.Println("🔍 开始诊断SSHFS问题...")

	// 检查SSH连接
	fmt.Println("🔗 检查SSH连接...")
	if err := s.sshClient.TestConnection(); err != nil {
		fmt.Printf("❌ SSH连接失败: %v\n", err)
	} else {
		fmt.Println("✅ SSH连接正常")
	}

	// 检查本地SSHFS安装
	fmt.Println("📦 检查本地SSHFS安装...")
	if err := s.executeLocalCommand("which sshfs"); err != nil {
		fmt.Println("❌ SSHFS未安装")
	} else {
		fmt.Println("✅ SSHFS已安装")
	}

	// 检查远程目录
	fmt.Println("📁 检查远程目录...")
	for _, dir := range s.config.FileSharing.Directories {
		if s.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("✅ 远程目录存在: %s\n", dir.Source)
		} else {
			fmt.Printf("❌ 远程目录不存在: %s\n", dir.Source)
		}
	}

	// 检查当前挂载状态
	fmt.Println("🔗 检查当前挂载状态...")
	if err := s.executeLocalCommand("mount | grep sshfs"); err != nil {
		fmt.Println("ℹ️  没有找到SSHFS挂载")
	}

	fmt.Println("✅ SSHFS诊断完成")
	return nil
}

// GetShareType 获取共享类型
func (s *SSHFSManager) GetShareType() string {
	return "sshfs"
}

// DiagnoseStatus 静默诊断SSHFS状态
func (s *SSHFSManager) DiagnoseStatus() (bool, string, error) {
	// SSHFS主要依赖SSH连接，检查SSH连接是否正常
	if s.sshClient == nil {
		return false, "SSH客户端未初始化", nil
	}
	
	// 测试SSH连接
	_, err := s.sshClient.ExecuteCommand("echo 'test'")
	if err != nil {
		return false, "SSH连接失败", err
	}
	
	return true, "SSH连接正常", nil
} 