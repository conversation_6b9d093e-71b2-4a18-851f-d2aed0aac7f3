package fileshare

import (
	"fmt"
	"os/exec"
	"runtime"

	"github.com/addx/iot-debug-setup/internal/logger"
	"github.com/addx/iot-debug-setup/internal/platform"
	"github.com/addx/iot-debug-setup/internal/service"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
	"github.com/addx/iot-debug-setup/internal/utils"
)

// SambaManagerRefactored 重构后的SAMBA文件共享管理器
// 演示如何使用新的CommandProvider设计
type SambaManagerRefactored struct {
	config          *types.Config
	sshClient       *ssh.Client
	commandProvider *platform.CommandProvider
	mounted         []string // 记录已挂载的路径，用于清理
	serviceManager  service.ServiceManager
	backupManager   *utils.BackupManager
}

// NewSambaManagerRefactored 创建新的重构后的SAMBA管理器
func NewSambaManagerRefactored(config *types.Config, sshClient interface{}) *SambaManagerRefactored {
	sshClientImpl := sshClient.(*ssh.Client)
	return &SambaManagerRefactored{
		config:          config,
		sshClient:       sshClientImpl,
		commandProvider: platform.NewCommandProvider(config),
		mounted:         make([]string, 0),
		serviceManager:  service.NewLinuxServiceManager(sshClientImpl),
		backupManager:   utils.NewBackupManager(sshClientImpl),
	}
}

// SetupServer 在远程服务器上设置SAMBA服务
func (s *SambaManagerRefactored) SetupServer() error {
	logger.Config("设置SAMBA文件共享服务")

	// 检查平台支持
	if !s.commandProvider.IsSupported(platform.ShareTypeSamba) {
		return fmt.Errorf("当前平台 %s 不支持SAMBA", runtime.GOOS)
	}

	// 确保SAMBA已安装
	if err := s.ensureSambaInstalled(); err != nil {
		return fmt.Errorf("安装SAMBA失败: %v", err)
	}

	// 配置SAMBA共享
	if err := s.configureSambaShares(); err != nil {
		return fmt.Errorf("配置SAMBA共享失败: %v", err)
	}

	// 启动SAMBA服务
	if err := s.startSambaService(); err != nil {
		return fmt.Errorf("启动SAMBA服务失败: %v", err)
	}

	logger.Success("SAMBA文件共享服务设置完成")
	return nil
}

// MountOnLocal 在本地挂载SAMBA共享
func (s *SambaManagerRefactored) MountOnLocal() error {
	logger.Network("在本地挂载SAMBA共享")

	// 创建挂载点
	mountPoint := s.config.LocalMachine.MountPoint
	if err := s.createMountPoint(mountPoint); err != nil {
		return fmt.Errorf("创建挂载点失败: %v", err)
	}

	// 确保本地有SAMBA客户端支持
	if err := s.ensureSambaClientInstalled(); err != nil {
		return fmt.Errorf("安装SAMBA客户端失败: %v", err)
	}

	// 挂载每个共享
	for _, dir := range s.config.FileSharing.Directories {
		localPath := fmt.Sprintf("%s/%s", mountPoint, dir.ShareName)
		if err := s.mountSingleSambaShare(dir.ShareName, localPath); err != nil {
			logger.Warn("挂载共享失败 %s: %v", dir.ShareName, err)
			continue
		}
	}

	logger.Success("SAMBA共享挂载完成")
	return nil
}

// mountSingleSambaShare 挂载单个SAMBA共享
func (s *SambaManagerRefactored) mountSingleSambaShare(shareName, localPath string) error {
	logger.Network("挂载共享 %s 到 %s", shareName, localPath)

	// 创建本地挂载目录
	if err := s.executeLocalCommand(fmt.Sprintf("mkdir -p %s", localPath)); err != nil {
		return fmt.Errorf("创建本地目录失败: %v", err)
	}

	// 使用CommandProvider获取挂载命令
	params := platform.CommandParams{
		TargetDeviceIP: s.config.TargetDevice.IP,
		ShareName:      shareName,
		MountPoint:     localPath,
		Username:       s.config.TargetDevice.SSH.Username,
		Port:           s.config.TargetDevice.SSH.Port,
	}

	mountCmd, err := s.commandProvider.GetCommand(
		platform.ShareTypeSamba,
		platform.CommandTypeMount,
		params,
	)
	if err != nil {
		return fmt.Errorf("获取挂载命令失败: %v", err)
	}

	// 执行挂载
	if err := s.executeLocalCommand(mountCmd); err != nil {
		return fmt.Errorf("挂载失败: %v", err)
	}

	// 记录已挂载的路径
	s.mounted = append(s.mounted, localPath)
	logger.Success("成功挂载: %s", localPath)
	return nil
}

// Cleanup 清理挂载和配置
func (s *SambaManagerRefactored) Cleanup() error {
	logger.Cleanup("清理SAMBA挂载")

	// 卸载所有已挂载的共享
	for _, mountPath := range s.mounted {
		if err := s.unmountSamba(mountPath); err != nil {
			logger.Warn("卸载失败 %s: %v", mountPath, err)
		}
	}

	s.mounted = s.mounted[:0] // 清空记录
	
	// 恢复SAMBA配置文件
	if err := s.backupManager.RestoreFile("/etc/samba/smb.conf"); err != nil {
		logger.Warn("恢复SAMBA配置失败: %v", err)
	}
	
	logger.Success("SAMBA清理完成")
	return nil
}

// unmountSamba 卸载SAMBA共享
func (s *SambaManagerRefactored) unmountSamba(mountPath string) error {
	// 使用CommandProvider获取卸载命令
	params := platform.CommandParams{
		MountPoint: mountPath,
	}

	unmountCmd, err := s.commandProvider.GetCommand(
		platform.ShareTypeSamba,
		platform.CommandTypeUnmount,
		params,
	)
	if err != nil {
		return fmt.Errorf("获取卸载命令失败: %v", err)
	}

	return s.executeLocalCommand(unmountCmd)
}

// ensureSambaInstalled 确保SAMBA已安装
func (s *SambaManagerRefactored) ensureSambaInstalled() error {
	logger.Progress("检查SAMBA安装状态")

	// 检查是否已安装
	if _, err := s.sshClient.ExecuteCommand("which smbd"); err == nil {
		logger.Success("SAMBA已安装")
		return nil
	}

	logger.Progress("安装SAMBA")

	// 更新包管理器
	if _, err := s.sshClient.ExecuteCommand("apt-get update"); err != nil {
		logger.Warn("更新包管理器失败: %v", err)
	}

	// 安装SAMBA
	installCmd := "apt-get install -y samba samba-common-bin"
	if _, err := s.sshClient.ExecuteCommand(installCmd); err != nil {
		return fmt.Errorf("安装SAMBA失败: %v", err)
	}

	logger.Success("SAMBA安装完成")
	return nil
}

// configureSambaShares 配置SAMBA共享
func (s *SambaManagerRefactored) configureSambaShares() error {
	fmt.Println("📝 配置SAMBA共享...")

	// 备份原有配置 - 使用统一的备份管理器
	if err := s.backupManager.BackupFile("/etc/samba/smb.conf"); err != nil {
		fmt.Printf("⚠️  备份SAMBA配置失败: %v\n", err)
	}

	// 生成SAMBA配置
	config := s.generateSambaConfig()

	// 写入配置文件
	writeCmd := fmt.Sprintf("cat > /etc/samba/smb.conf << 'EOF'\n%s\nEOF", config)
	if _, err := s.sshClient.ExecuteCommand(writeCmd); err != nil {
		return fmt.Errorf("写入SAMBA配置失败: %v", err)
	}

	// 创建共享目录
	for _, dir := range s.config.FileSharing.Directories {
		if !s.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("📁 创建目录: %s\n", dir.Source)
			if err := s.sshClient.CreateDirectory(dir.Source); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", dir.Source, err)
			}
		}

		// 设置目录权限
		chmodCmd := fmt.Sprintf("chmod 755 %s", dir.Source)
		if _, err := s.sshClient.ExecuteCommand(chmodCmd); err != nil {
			fmt.Printf("⚠️  设置目录权限失败 %s: %v\n", dir.Source, err)
		}
	}

	fmt.Println("✅ SAMBA共享配置完成")
	return nil
}

// generateSambaConfig 生成SAMBA配置文件内容
func (s *SambaManagerRefactored) generateSambaConfig() string {
	// 这里的实现与原来相同，但逻辑更加清晰
	// 省略具体实现，因为重点是演示CommandProvider的使用
	return "[global]\n   workgroup = WORKGROUP\n   security = user\n   map to guest = bad user\n"
}

// startSambaService 启动SAMBA服务
func (s *SambaManagerRefactored) startSambaService() error {
	fmt.Println("🚀 启动SAMBA服务...")

	// 使用服务组管理器启动SAMBA服务
	sambaServiceGroup := service.CreateSambaServiceGroup(s.serviceManager)
	if err := sambaServiceGroup.StartAll(); err != nil {
		return err
	}

	fmt.Println("✅ SAMBA服务启动成功")
	return nil
}

// createMountPoint 创建挂载点
func (s *SambaManagerRefactored) createMountPoint(path string) error {
	// 根据操作系统使用不同的命令
	var cmd string
	switch runtime.GOOS {
	case "windows":
		// Windows下不需要创建挂载点
		return nil
	default:
		cmd = fmt.Sprintf("mkdir -p %s", path)
	}

	return s.executeLocalCommand(cmd)
}

// ensureSambaClientInstalled 确保本地安装了SAMBA客户端
func (s *SambaManagerRefactored) ensureSambaClientInstalled() error {
	fmt.Println("📦 检查本地SAMBA客户端...")

	switch runtime.GOOS {
	case "windows":
		// Windows原生支持SMB/CIFS
		fmt.Println("✅ Windows原生支持SMB/CIFS")
		return nil
	case "darwin":
		// macOS原生支持SMB
		fmt.Println("✅ macOS原生支持SMB")
		return nil
	case "linux":
		// Linux需要检查cifs-utils
		if err := s.executeLocalCommand("which mount.cifs"); err != nil {
			fmt.Println("📥 安装SAMBA客户端...")
			// 尝试安装cifs-utils
			installCmds := []string{
				"sudo apt-get update && sudo apt-get install -y cifs-utils",
				"sudo yum install -y cifs-utils",
				"sudo dnf install -y cifs-utils",
			}
			
			var lastErr error
			for _, cmd := range installCmds {
				if err := s.executeLocalCommand(cmd); err == nil {
					fmt.Println("✅ SAMBA客户端安装完成")
					return nil
				} else {
					lastErr = err
				}
			}
			return fmt.Errorf("安装SAMBA客户端失败: %v", lastErr)
		}
		fmt.Println("✅ SAMBA客户端已安装")
		return nil
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// executeLocalCommand 执行本地命令
func (s *SambaManagerRefactored) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

// 实现FileShareManager接口
func (s *SambaManagerRefactored) DiagnoseIssues() error {
	// 诊断实现
	return nil
}

func (s *SambaManagerRefactored) DiagnoseStatus() (bool, string, error) {
	// 状态诊断实现
	return true, "SAMBA正常", nil
}

func (s *SambaManagerRefactored) GetShareType() string {
	return "samba"
} 