package fileshare

import (
	"fmt"
	"os/exec"
	"runtime"
	"strings"

	"github.com/addx/iot-debug-setup/internal/service"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
	"github.com/addx/iot-debug-setup/internal/utils"
)

// SambaManager SAMBA文件共享管理器
type SambaManager struct {
	config          *types.Config
	sshClient       *ssh.Client
	mounted         []string // 记录已挂载的路径，用于清理
	permissionMgr   *utils.PermissionManager
	serviceManager  service.ServiceManager
	backupManager   *utils.BackupManager
}

// NewSambaManager 创建新的SAMBA管理器
func NewSambaManager(config *types.Config, sshClient interface{}) *SambaManager {
	sshClientImpl := sshClient.(*ssh.Client)
	return &SambaManager{
		config:          config,
		sshClient:       sshClientImpl,
		mounted:         make([]string, 0),
		permissionMgr:   utils.NewPermissionManager(),
		serviceManager:  service.NewLinuxServiceManager(sshClientImpl),
		backupManager:   utils.NewBackupManager(sshClientImpl),
	}
}

// SetupServer 在远程服务器上设置SAMBA服务
func (s *SambaManager) SetupServer() error {
	fmt.Println("🔧 设置SAMBA文件共享服务...")

	// 确保SAMBA已安装
	if err := s.ensureSambaInstalled(); err != nil {
		return fmt.Errorf("安装SAMBA失败: %v", err)
	}

	// 配置SAMBA共享
	if err := s.configureSambaShares(); err != nil {
		return fmt.Errorf("配置SAMBA共享失败: %v", err)
	}

	// 启动SAMBA服务
	if err := s.startSambaService(); err != nil {
		return fmt.Errorf("启动SAMBA服务失败: %v", err)
	}

	fmt.Println("✅ SAMBA文件共享服务设置完成")
	return nil
}

// ensureSambaInstalled 确保SAMBA已安装
func (s *SambaManager) ensureSambaInstalled() error {
	fmt.Println("📦 检查SAMBA安装状态...")

	// 检查SAMBA是否已安装（多种检查方式）
	if s.isSambaInstalled() {
		fmt.Println("✅ SAMBA已安装，跳过安装步骤")
		return nil
	}

	fmt.Println("📥 SAMBA未安装，开始安装...")

	// 更新包管理器
	fmt.Println("🔄 更新包管理器...")
	if _, err := s.sshClient.ExecuteCommand("apt-get update"); err != nil {
		fmt.Printf("⚠️  更新包管理器失败: %v\n", err)
		// 继续尝试安装，有时候更新失败不影响安装
	}

	// 安装SAMBA
	fmt.Println("📦 安装SAMBA软件包...")
	installCmd := "apt-get install -y samba samba-common-bin"
	if _, err := s.sshClient.ExecuteCommand(installCmd); err != nil {
		return fmt.Errorf("安装SAMBA失败: %v", err)
	}

	// 验证安装结果
	if s.isSambaInstalled() {
		fmt.Println("✅ SAMBA安装完成并验证成功")
	} else {
		fmt.Println("⚠️  SAMBA安装可能未完全成功，但将继续尝试配置")
	}
	
	return nil
}

// isSambaInstalled 检查SAMBA是否已安装
func (s *SambaManager) isSambaInstalled() bool {
	// 方法1：检查smbd命令是否存在
	if output, err := s.sshClient.ExecuteCommand("which smbd"); err == nil {
		fmt.Printf("  🔍 找到smbd: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法2：检查samba包是否已安装
	if output, err := s.sshClient.ExecuteCommand("dpkg -l | grep -E '^ii.*samba[^-]'"); err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("  🔍 找到samba包: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法3：检查systemctl中是否有samba服务
	if output, err := s.sshClient.ExecuteCommand("systemctl list-unit-files | grep -E '^smbd|^samba'"); err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("  🔍 找到samba服务: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法4：检查/usr/sbin/smbd文件是否存在
	if _, err := s.sshClient.ExecuteCommand("test -f /usr/sbin/smbd"); err == nil {
		fmt.Printf("  🔍 找到smbd文件: /usr/sbin/smbd\n")
		return true
	}

	fmt.Println("  ❌ 未找到SAMBA安装")
	return false
}

// configureSambaShares 配置SAMBA共享
func (s *SambaManager) configureSambaShares() error {
	fmt.Println("📝 配置SAMBA共享...")

	// 备份原有配置 - 使用统一的备份管理器
	if err := s.backupManager.BackupFile("/etc/samba/smb.conf"); err != nil {
		fmt.Printf("⚠️  备份SAMBA配置失败: %v\n", err)
	}

	// 生成SAMBA配置
	config := s.generateSambaConfig()

	// 写入配置文件
	writeCmd := fmt.Sprintf("cat > /etc/samba/smb.conf << 'EOF'\n%s\nEOF", config)
	if _, err := s.sshClient.ExecuteCommand(writeCmd); err != nil {
		return fmt.Errorf("写入SAMBA配置失败: %v", err)
	}

	// 创建共享目录
	for _, dir := range s.config.FileSharing.Directories {
		if !s.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("📁 创建目录: %s\n", dir.Source)
			if err := s.sshClient.CreateDirectory(dir.Source); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", dir.Source, err)
			}
		}

		// 设置目录权限
		chmodCmd := fmt.Sprintf("chmod 755 %s", dir.Source)
		if _, err := s.sshClient.ExecuteCommand(chmodCmd); err != nil {
			fmt.Printf("⚠️  设置目录权限失败 %s: %v\n", dir.Source, err)
		}
	}

	fmt.Println("✅ SAMBA共享配置完成")
	return nil
}

// generateSambaConfig 生成SAMBA配置文件内容
func (s *SambaManager) generateSambaConfig() string {
	var config strings.Builder

	// 全局配置
	config.WriteString("[global]\n")
	config.WriteString("   workgroup = WORKGROUP\n")
	config.WriteString("   server string = IoT Debug Server\n")
	config.WriteString("   security = user\n")
	config.WriteString("   map to guest = bad user\n")
	config.WriteString("   guest account = nobody\n")
	config.WriteString("   log file = /var/log/samba/log.%m\n")
	config.WriteString("   max log size = 1000\n")
	config.WriteString("   logging = file\n")
	config.WriteString("   panic action = /usr/share/samba/panic-action %d\n")
	config.WriteString("   server role = standalone server\n")
	config.WriteString("   obey pam restrictions = yes\n")
	config.WriteString("   unix password sync = yes\n")
	config.WriteString("   passwd program = /usr/bin/passwd %u\n")
	config.WriteString("   passwd chat = *Enter\\snew\\s*\\spassword:* %n\\n *Retype\\snew\\s*\\spassword:* %n\\n *password\\supdated\\ssuccessfully* .\n")
	config.WriteString("   pam password change = yes\n")
	config.WriteString("   map to guest = Bad User\n")
	config.WriteString("\n")

	// 共享配置
	for _, dir := range s.config.FileSharing.Directories {
		config.WriteString(fmt.Sprintf("[%s]\n", dir.ShareName))
		config.WriteString(fmt.Sprintf("   comment = %s\n", dir.Description))
		config.WriteString(fmt.Sprintf("   path = %s\n", dir.Source))
		config.WriteString("   browseable = yes\n")
		config.WriteString("   read only = no\n")
		config.WriteString("   guest ok = yes\n")
		config.WriteString("   create mask = 0755\n")
		config.WriteString("   directory mask = 0755\n")
		
		// 添加自定义选项
		if dir.Options != "" {
			config.WriteString(fmt.Sprintf("   %s\n", dir.Options))
		}
		config.WriteString("\n")
	}

	return config.String()
}

// startSambaService 启动SAMBA服务
func (s *SambaManager) startSambaService() error {
	fmt.Println("🚀 启动SAMBA服务...")

	// 使用服务组管理器启动SAMBA服务
	sambaServiceGroup := service.CreateSambaServiceGroup(s.serviceManager)
	return sambaServiceGroup.StartAll()

	// 检查服务状态
	if output, err := s.sshClient.ExecuteCommand("systemctl is-active smbd"); err == nil && strings.TrimSpace(output) == "active" {
		fmt.Println("✅ SAMBA服务启动成功")
	} else {
		return fmt.Errorf("SAMBA服务启动失败")
	}

	// 检查端口445是否监听
	if output, err := s.sshClient.ExecuteCommand("netstat -tulpn | grep :445"); err == nil && output != "" {
		fmt.Println("✅ SAMBA端口445正在监听")
	} else {
		fmt.Println("⚠️  SAMBA端口445未监听")
	}

	return nil
}

// MountOnLocal 在本地挂载SAMBA共享
func (s *SambaManager) MountOnLocal() error {
	fmt.Println("🔗 在本地挂载SAMBA共享...")

	// 创建挂载点
	mountPoint := s.config.LocalMachine.MountPoint
	if err := s.createMountPoint(mountPoint); err != nil {
		return fmt.Errorf("创建挂载点失败: %v", err)
	}

	// 确保本地有SAMBA客户端支持
	if err := s.ensureSambaClientInstalled(); err != nil {
		return fmt.Errorf("安装SAMBA客户端失败: %v", err)
	}

	// 挂载每个共享
	for _, dir := range s.config.FileSharing.Directories {
		localPath := fmt.Sprintf("%s/%s", mountPoint, dir.ShareName)
		if err := s.mountSingleSambaShare(dir.ShareName, localPath); err != nil {
			fmt.Printf("⚠️  挂载共享失败 %s: %v\n", dir.ShareName, err)
			continue
		}
	}

	fmt.Println("✅ SAMBA共享挂载完成")
	return nil
}

// createMountPoint 创建挂载点
func (s *SambaManager) createMountPoint(path string) error {
	// 根据操作系统使用不同的命令
	var cmd string
	switch runtime.GOOS {
	case "windows":
		// Windows下不需要创建挂载点
		return nil
	default:
		cmd = fmt.Sprintf("mkdir -p %s", path)
	}

	return s.executeLocalCommand(cmd)
}

// ensureSambaClientInstalled 确保本地安装了SAMBA客户端
func (s *SambaManager) ensureSambaClientInstalled() error {
	fmt.Println("📦 检查本地SAMBA客户端...")

	switch runtime.GOOS {
	case "windows":
		// Windows原生支持SMB/CIFS
		fmt.Println("✅ Windows原生支持SMB/CIFS")
		return nil
	case "darwin":
		// macOS原生支持SMB
		fmt.Println("✅ macOS原生支持SMB")
		return nil
	case "linux":
		// Linux需要检查cifs-utils
		if err := s.executeLocalCommand("which mount.cifs"); err != nil {
			fmt.Println("📥 安装SAMBA客户端...")
			// 尝试安装cifs-utils
			installCmds := []string{
				"sudo apt-get update && sudo apt-get install -y cifs-utils",
				"sudo yum install -y cifs-utils",
				"sudo dnf install -y cifs-utils",
			}
			
			var lastErr error
			for _, cmd := range installCmds {
				if err := s.executeLocalCommand(cmd); err == nil {
					fmt.Println("✅ SAMBA客户端安装完成")
					return nil
				} else {
					lastErr = err
				}
			}
			return fmt.Errorf("安装SAMBA客户端失败: %v", lastErr)
		}
		fmt.Println("✅ SAMBA客户端已安装")
		return nil
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// mountSingleSambaShare 挂载单个SAMBA共享
func (s *SambaManager) mountSingleSambaShare(shareName, localPath string) error {
	fmt.Printf("🔗 挂载共享 %s 到 %s...\n", shareName, localPath)

	// 创建本地挂载目录（使用权限管理器）
	if err := s.permissionMgr.CreateDirectoryWithPermission(localPath); err != nil {
		// 显示权限问题解决方案
		s.permissionMgr.ShowPermissionSolutions(localPath, err)
		
		// 尝试获取推荐的挂载路径
		if recommendedPath, recErr := s.permissionMgr.GetRecommendedMountPath(localPath); recErr == nil {
			fmt.Printf("🔄 尝试使用推荐路径: %s\n", recommendedPath)
			if err := s.permissionMgr.CreateDirectoryWithPermission(recommendedPath); err == nil {
				fmt.Printf("✅ 使用推荐路径成功，更新挂载路径\n")
				localPath = recommendedPath
			} else {
				return fmt.Errorf("创建挂载点失败: %v", err)
			}
		} else {
			return fmt.Errorf("创建挂载点失败: %v", err)
		}
	}

	// 获取挂载命令
	mountCmd := s.getSambaMountCommand(shareName, localPath)
	
	// 执行挂载
	if err := s.executeLocalCommand(mountCmd); err != nil {
		return fmt.Errorf("挂载失败: %v", err)
	}

	// 记录已挂载的路径
	s.mounted = append(s.mounted, localPath)
	fmt.Printf("✅ 成功挂载: %s\n", localPath)
	return nil
}

// getSambaMountCommand 获取SAMBA挂载命令
func (s *SambaManager) getSambaMountCommand(shareName, localPath string) string {
	serverIP := s.config.TargetDevice.IP
	
	switch runtime.GOOS {
	case "windows":
		// Windows net use命令
		return fmt.Sprintf(`net use "%s" "\\%s\%s" /persistent:no`, localPath, serverIP, shareName)
	case "darwin":
		// macOS mount命令
		return fmt.Sprintf("mount -t smbfs //%s@%s/%s %s", "guest", serverIP, shareName, localPath)
	case "linux":
		// Linux mount.cifs命令
		return fmt.Sprintf("sudo mount -t cifs //%s/%s %s -o guest,uid=$(id -u),gid=$(id -g),iocharset=utf8", serverIP, shareName, localPath)
	default:
		// 默认使用Linux命令
		return fmt.Sprintf("sudo mount -t cifs //%s/%s %s -o guest,uid=$(id -u),gid=$(id -g),iocharset=utf8", serverIP, shareName, localPath)
	}
}

// executeLocalCommand 执行本地命令
func (s *SambaManager) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	if len(output) > 0 {
		fmt.Printf("  📤 输出: %s\n", strings.TrimSpace(string(output)))
	}

	return nil
}

// Cleanup 清理挂载和配置
func (s *SambaManager) Cleanup() error {
	fmt.Println("🧹 清理SAMBA挂载...")

	// 卸载所有已挂载的共享
	for _, mountPath := range s.mounted {
		if err := s.unmountSamba(mountPath); err != nil {
			fmt.Printf("⚠️  卸载失败 %s: %v\n", mountPath, err)
		}
	}

	s.mounted = s.mounted[:0] // 清空记录
	
	// 恢复SAMBA配置文件
	if err := s.backupManager.RestoreFile("/etc/samba/smb.conf"); err != nil {
		fmt.Printf("⚠️  恢复SAMBA配置失败: %v\n", err)
	}
	
	fmt.Println("✅ SAMBA清理完成")
	return nil
}

// unmountSamba 卸载SAMBA共享
func (s *SambaManager) unmountSamba(path string) error {
	var cmd string
	switch runtime.GOOS {
	case "windows":
		cmd = fmt.Sprintf(`net use "%s" /delete`, path)
	default:
		cmd = fmt.Sprintf("sudo umount %s", path)
	}

	return s.executeLocalCommand(cmd)
}

// DiagnoseIssues 诊断SAMBA问题
func (s *SambaManager) DiagnoseIssues() error {
	fmt.Println("🔍 开始诊断SAMBA问题...")

	// 检查系统信息
	if output, err := s.sshClient.ExecuteCommand("uname -a"); err == nil {
		fmt.Printf("🖥️  系统信息: %s\n", output)
	}

	// 检查SAMBA安装状态
	fmt.Println("📦 检查SAMBA安装状态...")
	if output, err := s.sshClient.ExecuteCommand("dpkg -l | grep samba"); err == nil {
		fmt.Printf("  SAMBA包: %s\n", output)
	}

	// 检查服务状态
	fmt.Println("🔧 检查SAMBA服务状态...")
	services := []string{"smbd", "nmbd"}
	for _, service := range services {
		if output, err := s.sshClient.ExecuteCommand(fmt.Sprintf("systemctl status %s", service)); err == nil {
			fmt.Printf("  %s状态:\n%s\n", service, output)
		}
	}

	// 检查端口监听
	fmt.Println("🔌 检查端口监听...")
	ports := []string{"445", "139"}
	for _, port := range ports {
		if output, err := s.sshClient.ExecuteCommand(fmt.Sprintf("netstat -tulpn | grep :%s", port)); err == nil {
			fmt.Printf("  端口%s: %s\n", port, output)
		}
	}

	// 检查配置文件
	fmt.Println("📁 检查SAMBA配置...")
	if output, err := s.sshClient.ExecuteCommand("testparm -s"); err == nil {
		fmt.Printf("  SAMBA配置:\n%s\n", output)
	}

	// 检查共享列表
	fmt.Println("📤 检查共享列表...")
	if output, err := s.sshClient.ExecuteCommand("smbclient -L localhost -U%"); err == nil {
		fmt.Printf("  共享列表:\n%s\n", output)
	}

	// 检查防火墙
	fmt.Println("🔥 检查防火墙状态...")
	if output, err := s.sshClient.ExecuteCommand("ufw status || iptables -L"); err == nil {
		fmt.Printf("  防火墙状态:\n%s\n", output)
	}

	fmt.Println("✅ SAMBA诊断完成")
	return nil
}

// GetShareType 获取共享类型
func (s *SambaManager) GetShareType() string {
	return "samba"
}

// DiagnoseStatus 静默诊断SAMBA状态
func (s *SambaManager) DiagnoseStatus() (bool, string, error) {
	// 检查SAMBA服务状态
	smdbStatus, err := s.sshClient.ExecuteCommand("systemctl is-active smbd")
	if err != nil || smdbStatus != "active" {
		return false, "smbd服务未运行", nil
	}
	
	nmbdStatus, err := s.sshClient.ExecuteCommand("systemctl is-active nmbd")
	if err != nil || nmbdStatus != "active" {
		return false, "nmbd服务未运行", nil
	}
	
	// 检查端口监听
	port445, err := s.sshClient.ExecuteCommand("netstat -tulpn | grep ':445' | wc -l")
	if err != nil || port445 == "0" {
		return false, "端口445未监听", nil
	}
	
	return true, "服务运行正常", nil
} 