package utils

import (
	"fmt"
	"os"
	"io/ioutil"
	"github.com/addx/iot-debug-setup/internal/logger"
	"github.com/addx/iot-debug-setup/internal/ssh"
)

// BackupManager 统一的备份管理器
type BackupManager struct {
	sshClient *ssh.Client
}

// NewBackupManager 创建备份管理器
func NewBackupManager(sshClient *ssh.Client) *BackupManager {
	return &BackupManager{
		sshClient: sshClient,
	}
}

// BackupFile 备份文件 - 统一的.bak后缀策略
func (bm *BackupManager) BackupFile(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查源文件是否存在
	if !bm.sshClient.FileExists(filePath) {
		return fmt.Errorf("源文件不存在: %s", filePath)
	}
	
	// 检查是否已经有备份文件
	if bm.sshClient.FileExists(backupPath) {
		logger.File("备份文件已存在: %s", backupPath)
		return nil
	}
	
	// 创建备份
	backupCmd := fmt.Sprintf("cp %s %s", filePath, backupPath)
	if _, err := bm.sshClient.ExecuteCommand(backupCmd); err != nil {
		return fmt.Errorf("创建备份文件失败: %v", err)
	}
	
	logger.File("文件备份成功: %s → %s", filePath, backupPath)
	return nil
}

// RestoreFile 恢复文件 - 从.bak文件恢复
func (bm *BackupManager) RestoreFile(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查备份文件是否存在
	if !bm.sshClient.FileExists(backupPath) {
		return fmt.Errorf("备份文件不存在: %s", backupPath)
	}
	
	// 从备份恢复
	restoreCmd := fmt.Sprintf("cp %s %s", backupPath, filePath)
	if _, err := bm.sshClient.ExecuteCommand(restoreCmd); err != nil {
		return fmt.Errorf("恢复文件失败: %v", err)
	}
	
	logger.Success("文件恢复成功: %s ← %s", filePath, backupPath)
	return nil
}

// CleanupBackup 清理备份文件
func (bm *BackupManager) CleanupBackup(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查备份文件是否存在
	if !bm.sshClient.FileExists(backupPath) {
		return nil // 没有备份文件，无需清理
	}
	
	// 删除备份文件
	cleanupCmd := fmt.Sprintf("rm -f %s", backupPath)
	if _, err := bm.sshClient.ExecuteCommand(cleanupCmd); err != nil {
		return fmt.Errorf("清理备份文件失败: %v", err)
	}
	
	logger.Cleanup("备份文件清理完成: %s", backupPath)
	return nil
}

// LocalBackupManager 本地文件备份管理器
type LocalBackupManager struct{}

// NewLocalBackupManager 创建本地备份管理器
func NewLocalBackupManager() *LocalBackupManager {
	return &LocalBackupManager{}
}

// BackupFile 备份本地文件
func (lbm *LocalBackupManager) BackupFile(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查源文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("源文件不存在: %s", filePath)
	}
	
	// 检查是否已经有备份文件
	if _, err := os.Stat(backupPath); err == nil {
		logger.File("备份文件已存在: %s", backupPath)
		return nil
	}
	
	// 读取源文件内容
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取源文件失败: %v", err)
	}
	
	// 创建备份文件
	if err := ioutil.WriteFile(backupPath, content, 0644); err != nil {
		return fmt.Errorf("创建备份文件失败: %v", err)
	}
	
	logger.File("文件备份成功: %s → %s", filePath, backupPath)
	return nil
}

// RestoreFile 恢复本地文件
func (lbm *LocalBackupManager) RestoreFile(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backupPath)
	}
	
	// 读取备份文件内容
	content, err := ioutil.ReadFile(backupPath)
	if err != nil {
		return fmt.Errorf("读取备份文件失败: %v", err)
	}
	
	// 恢复文件
	if err := ioutil.WriteFile(filePath, content, 0644); err != nil {
		return fmt.Errorf("恢复文件失败: %v", err)
	}
	
	logger.Success("文件恢复成功: %s ← %s", filePath, backupPath)
	return nil
}

// CleanupBackup 清理本地备份文件
func (lbm *LocalBackupManager) CleanupBackup(filePath string) error {
	backupPath := filePath + ".bak"
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return nil // 没有备份文件，无需清理
	}
	
	// 删除备份文件
	if err := os.Remove(backupPath); err != nil {
		return fmt.Errorf("清理备份文件失败: %v", err)
	}
	
	logger.Cleanup("备份文件清理完成: %s", backupPath)
	return nil
} 