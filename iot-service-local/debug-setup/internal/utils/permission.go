package utils

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
)

// PermissionManager 权限管理器
type PermissionManager struct{}

// NewPermissionManager 创建权限管理器
func NewPermissionManager() *PermissionManager {
	return &PermissionManager{}
}

// CreateDirectoryWithPermission 创建目录，解决权限问题
func (pm *PermissionManager) CreateDirectoryWithPermission(path string) error {
	fmt.Printf("🔧 创建目录: %s\n", path)

	// 方案1：直接尝试创建（针对有权限的情况）
	if err := os.MkdirAll(path, 0755); err == nil {
		fmt.Printf("✅ 目录创建成功: %s\n", path)
		return nil
	}

	// 方案2：使用sudo创建
	if runtime.GOOS == "linux" {
		return pm.createWithSudo(path)
	}

	// 方案3：使用用户目录作为替代
	if runtime.GOOS == "windows" {
		return pm.createAlternativePath(path)
	}

	return fmt.Errorf("无法创建目录: %s", path)
}

// createWithSudo 使用sudo创建目录
func (pm *PermissionManager) createWithSudo(path string) error {
	fmt.Printf("🔐 使用sudo创建目录: %s\n", path)

	// 创建目录
	cmd := exec.Command("sudo", "mkdir", "-p", path)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("sudo创建目录失败: %v, 输出: %s", err, string(output))
	}

	// 设置权限，让当前用户可以访问
	username := os.Getenv("USER")
	if username == "" {
		username = os.Getenv("USERNAME")
	}
	
	if username != "" {
		chownCmd := exec.Command("sudo", "chown", "-R", username, path)
		if output, err := chownCmd.CombinedOutput(); err != nil {
			fmt.Printf("⚠️  设置目录所有者失败: %v, 输出: %s\n", err, string(output))
			// 不返回错误，因为目录已经创建成功
		}
	}

	// 设置权限为755
	chmodCmd := exec.Command("sudo", "chmod", "-R", "755", path)
	if output, err := chmodCmd.CombinedOutput(); err != nil {
		fmt.Printf("⚠️  设置目录权限失败: %v, 输出: %s\n", err, string(output))
	}

	fmt.Printf("✅ 使用sudo创建目录成功: %s\n", path)
	return nil
}

// createAlternativePath 创建替代路径
func (pm *PermissionManager) createAlternativePath(path string) error {
	// 对于Windows或者权限受限的情况，使用用户目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %v", err)
	}

	// 创建替代路径
	altPath := filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(path))
	
	fmt.Printf("🔄 使用替代路径: %s\n", altPath)
	
	if err := os.MkdirAll(altPath, 0755); err != nil {
		return fmt.Errorf("创建替代路径失败: %v", err)
	}

	fmt.Printf("✅ 替代路径创建成功: %s\n", altPath)
	return nil
}

// GetRecommendedMountPath 获取推荐的挂载路径
func (pm *PermissionManager) GetRecommendedMountPath(originalPath string) (string, error) {
	// 检查原始路径是否可以创建
	if err := pm.testPathPermission(originalPath); err == nil {
		return originalPath, nil
	}

	// 根据操作系统推荐合适的路径
	switch runtime.GOOS {
	case "linux":
		return pm.getLinuxRecommendedPath(originalPath)
	case "windows":
		return pm.getWindowsRecommendedPath(originalPath)
	case "darwin":
		return pm.getMacRecommendedPath(originalPath)
	default:
		return pm.getFallbackPath(originalPath)
	}
}

// testPathPermission 测试路径权限
func (pm *PermissionManager) testPathPermission(path string) error {
	// 创建父目录
	parentDir := filepath.Dir(path)
	if err := os.MkdirAll(parentDir, 0755); err != nil {
		return err
	}

	// 尝试创建测试文件
	testFile := filepath.Join(parentDir, ".permission_test")
	file, err := os.Create(testFile)
	if err != nil {
		return err
	}
	file.Close()
	os.Remove(testFile)

	return nil
}

// getLinuxRecommendedPath 获取Linux推荐路径
func (pm *PermissionManager) getLinuxRecommendedPath(originalPath string) (string, error) {
	// 优先级列表
	candidates := []string{
		originalPath,                          // 原始路径
		"/tmp" + originalPath,                 // /tmp下的路径
		"/home/" + os.Getenv("USER") + "/mnt" + filepath.Base(originalPath), // 用户目录下
	}

	homeDir, err := os.UserHomeDir()
	if err == nil {
		candidates = append(candidates, filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(originalPath)))
	}

	for _, candidate := range candidates {
		if err := pm.testPathPermission(candidate); err == nil {
			return candidate, nil
		}
	}

	return "", fmt.Errorf("找不到可用的挂载路径")
}

// getWindowsRecommendedPath 获取Windows推荐路径
func (pm *PermissionManager) getWindowsRecommendedPath(originalPath string) (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("获取用户目录失败: %v", err)
	}

	// Windows下使用用户目录
	recommendedPath := filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(originalPath))
	return recommendedPath, nil
}

// getMacRecommendedPath 获取macOS推荐路径
func (pm *PermissionManager) getMacRecommendedPath(originalPath string) (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("获取用户目录失败: %v", err)
	}

	// macOS下使用用户目录
	recommendedPath := filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(originalPath))
	return recommendedPath, nil
}

// getFallbackPath 获取备用路径
func (pm *PermissionManager) getFallbackPath(originalPath string) (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("获取用户目录失败: %v", err)
	}

	fallbackPath := filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(originalPath))
	return fallbackPath, nil
}

// ShowPermissionSolutions 显示权限问题解决方案
func (pm *PermissionManager) ShowPermissionSolutions(path string, err error) {
	fmt.Printf("❌ 权限不足: %v\n", err)
	fmt.Printf("📋 针对路径 %s 的解决方案:\n", path)
	
	fmt.Println("\n🔧 解决方案:")
	fmt.Println("1. 使用sudo权限:")
	fmt.Printf("   sudo mkdir -p %s\n", path)
	fmt.Printf("   sudo chown -R $USER %s\n", path)
	fmt.Printf("   sudo chmod -R 755 %s\n", path)
	
	fmt.Println("\n2. 使用替代路径:")
	if homeDir, err := os.UserHomeDir(); err == nil {
		altPath := filepath.Join(homeDir, "debug-setup", "mount", filepath.Base(path))
		fmt.Printf("   %s\n", altPath)
	}
	
	fmt.Println("\n3. 修改配置文件:")
	fmt.Println("   在配置文件中将mount_point修改为用户有权限的目录")
	
	fmt.Println("\n4. WSL环境解决方案:")
	fmt.Println("   在WSL中运行: sudo mkdir -p /mnt/camera_data")
	fmt.Println("   或者使用: /mnt/c/Users/<USER>/camera_data")
}

// WSLPathHelper WSL路径帮助器
type WSLPathHelper struct{}

// ConvertToWSLPath 转换为WSL路径
func (wsl *WSLPathHelper) ConvertToWSLPath(windowsPath string) string {
	// 将Windows路径转换为WSL路径
	if strings.HasPrefix(windowsPath, "C:") {
		return strings.Replace(windowsPath, "C:", "/mnt/c", 1)
	}
	return windowsPath
}

// IsWSL 检查是否在WSL环境中
func (wsl *WSLPathHelper) IsWSL() bool {
	if runtime.GOOS != "linux" {
		return false
	}

	// 检查/proc/version文件
	if data, err := os.ReadFile("/proc/version"); err == nil {
		return strings.Contains(string(data), "Microsoft") || strings.Contains(string(data), "WSL")
	}

	return false
}

// GetWSLMountPath 获取WSL挂载路径
func (wsl *WSLPathHelper) GetWSLMountPath(originalPath string) string {
	if wsl.IsWSL() {
		// 在WSL中，推荐使用/mnt/c/Users/<USER>/目录
		if username := os.Getenv("USER"); username != "" {
			return fmt.Sprintf("/mnt/c/Users/<USER>/camera_data", username)
		}
	}
	return originalPath
} 