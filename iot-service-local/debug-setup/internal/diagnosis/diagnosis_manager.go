package diagnosis

import (
	"fmt"

	"github.com/addx/iot-debug-setup/internal/docker"
	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/types"
)

// DiagnosisResult 诊断结果结构体
type DiagnosisResult struct {
	Status string
	Detail string
	Issues []string
}

// DiagnosticTarget 诊断目标接口
type DiagnosticTarget interface {
	DiagnoseStatus() (bool, string, error)
	GetType() string
}

// Manager 诊断管理器
type Manager struct {
	config *types.Config
}

// NewManager 创建诊断管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config: config,
	}
}

// DiagnoseTarget 诊断目标对象
func (m *Manager) DiagnoseTarget(target DiagnosticTarget) DiagnosisResult {
	isHealthy, detail, err := target.DiagnoseStatus()
	if err != nil {
		return DiagnosisResult{
			Status: "❌ 错误",
			Detail: "诊断失败",
			Issues: []string{err.Error()},
		}
	}

	if isHealthy {
		return DiagnosisResult{
			Status: "✅ 正常",
			Detail: detail,
			Issues: []string{},
		}
	} else {
		return DiagnosisResult{
			Status: "❌ 异常",
			Detail: detail,
			Issues: []string{detail},
		}
	}
}

// DiagnoseFileShare 诊断文件共享
func (m *Manager) DiagnoseFileShare(fileManager fileshare.FileShareManager) DiagnosisResult {
	return m.DiagnoseTarget(&fileShareAdapter{fileManager})
}

// DiagnoseDockerService 诊断Docker服务
func (m *Manager) DiagnoseDockerService(dockerManager *docker.Manager) DiagnosisResult {
	if !m.config.DockerControl.Enabled {
		return DiagnosisResult{
			Status: "⚪ 已禁用",
			Detail: "Docker控制未启用",
			Issues: []string{},
		}
	}

	return m.DiagnoseTarget(&dockerServiceAdapter{dockerManager})
}

// DiagnoseContainer 诊断容器状态
func (m *Manager) DiagnoseContainer(dockerManager *docker.Manager, containerName string) DiagnosisResult {
	return m.DiagnoseTarget(&containerAdapter{dockerManager, containerName})
}

// GenerateDiagnosisTable 生成诊断表格
func (m *Manager) GenerateDiagnosisTable(results []DiagnosisTableRow) {
	fmt.Println("┌──────────────────────────────────────────────────────────────────────────────┐")
	fmt.Println("│                              诊断结果汇总                                     │")
	fmt.Println("├──────────────────────────┬─────────────────┬─────────────────────────────────┤")
	fmt.Println("│ 组件                     │ 状态            │ 详细信息                        │")
	fmt.Println("├──────────────────────────┼─────────────────┼─────────────────────────────────┤")

	for _, row := range results {
		fmt.Printf("│ %-24s │ %-15s │ %-31s │\n", 
			m.truncateString(row.Component, 24), 
			row.Status, 
			m.truncateString(row.Detail, 31))
	}

	fmt.Println("└──────────────────────────┴─────────────────┴─────────────────────────────────┘")
}

// DiagnosisTableRow 诊断表格行
type DiagnosisTableRow struct {
	Component string
	Status    string
	Detail    string
}

// PrintRecommendations 打印诊断建议
func (m *Manager) PrintRecommendations(shareStatus, dockerStatus DiagnosisResult) {
	hasIssues := len(shareStatus.Issues) > 0 || len(dockerStatus.Issues) > 0

	// 检查Docker控制是否启用但有问题
	if m.config.DockerControl.Enabled && dockerStatus.Status != "✅ 正常" {
		hasIssues = true
	}

	if !hasIssues {
		fmt.Println("🎉 恭喜！所有组件运行正常")
		fmt.Println("💡 您可以直接运行调试环境设置命令")
		fmt.Printf("   示例: %s configs/debug_config.yml\n", "iot-debug-setup")
		return
	}

	fmt.Println("⚠️  发现问题及解决建议")
	fmt.Println("────────────────────────────────────────────────────────────────────────────────")

	// 文件共享问题建议
	if len(shareStatus.Issues) > 0 {
		m.printFileShareSuggestions()
	}

	// Docker问题建议
	if m.config.DockerControl.Enabled && len(dockerStatus.Issues) > 0 {
		m.printDockerSuggestions()
	}

	fmt.Println("🔧 快速修复建议:")
	fmt.Println("   • 运行诊断获取详细信息: iot-debug-setup --diagnose config.yml")
	fmt.Println("   • 解决问题后重新运行程序")
	if m.config.FileSharing.Type == "nfs" {
		fmt.Println("   • 如NFS有问题，推荐切换到SAMBA（更稳定）")
	}
}

// printFileShareSuggestions 打印文件共享建议
func (m *Manager) printFileShareSuggestions() {
	fmt.Printf("📁 %s文件共享问题:\n", m.config.FileSharing.Type)
	switch m.config.FileSharing.Type {
	case "samba":
		fmt.Println("   1. 检查SAMBA服务: sudo systemctl status smbd nmbd")
		fmt.Println("   2. 验证配置文件: sudo testparm")
		fmt.Println("   3. 检查端口监听: netstat -tulpn | grep ':445\\|:139'")
		fmt.Println("   4. 重启服务: sudo systemctl restart smbd nmbd")
	case "nfs":
		fmt.Println("   1. 检查NFS服务: sudo systemctl status nfs-server")
		fmt.Println("   2. 检查内核模块: lsmod | grep nfs")
		fmt.Println("   3. 验证exports: sudo exportfs -v")
		fmt.Println("   4. 考虑切换到SAMBA: file_sharing.type: \"samba\"")
	case "sshfs":
		fmt.Println("   1. 检查SSH连接: ssh user@host")
		fmt.Println("   2. 安装SSHFS: sudo apt-get install sshfs")
		fmt.Println("   3. 检查FUSE模块: modprobe fuse")
	}
	fmt.Println()
}

// printDockerSuggestions 打印Docker建议
func (m *Manager) printDockerSuggestions() {
	fmt.Println("🐳 Docker问题:")
	fmt.Println("   1. 检查Docker服务: sudo systemctl status docker")
	fmt.Println("   2. 创建缺失的容器: docker run --name container_name ...")
	fmt.Println("   3. 查看容器日志: docker logs container_name")
	fmt.Println("   4. 或禁用Docker控制: docker_control.enabled: false")
	fmt.Println()
}

// truncateString 截断字符串
func (m *Manager) truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

// GetEnabledStatus 获取启用状态文本
func (m *Manager) GetEnabledStatus(enabled bool) string {
	if enabled {
		return "✅ 已启用"
	}
	return "⚪ 已禁用"
}

// 适配器模式 - 将不同类型的对象适配为DiagnosticTarget接口

// fileShareAdapter 文件共享适配器
type fileShareAdapter struct {
	fileManager fileshare.FileShareManager
}

func (f *fileShareAdapter) DiagnoseStatus() (bool, string, error) {
	return f.fileManager.DiagnoseStatus()
}

func (f *fileShareAdapter) GetType() string {
	return f.fileManager.GetShareType()
}

// dockerServiceAdapter Docker服务适配器
type dockerServiceAdapter struct {
	dockerManager *docker.Manager
}

func (d *dockerServiceAdapter) DiagnoseStatus() (bool, string, error) {
	return d.dockerManager.DiagnoseDockerStatus()
}

func (d *dockerServiceAdapter) GetType() string {
	return "docker"
}

// containerAdapter 容器适配器
type containerAdapter struct {
	dockerManager *docker.Manager
	containerName string
}

func (c *containerAdapter) DiagnoseStatus() (bool, string, error) {
	return c.dockerManager.DiagnoseContainerStatus(c.containerName)
}

func (c *containerAdapter) GetType() string {
	return "container"
} 