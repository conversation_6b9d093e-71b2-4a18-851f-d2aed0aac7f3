package platform

import (
	"fmt"
	"runtime"
	"strings"
	"text/template"
	"bytes"
	
	"github.com/addx/iot-debug-setup/internal/types"
)

// CommandProvider 平台命令提供者
type CommandProvider struct {
	config *types.Config
}

// NewCommandProvider 创建新的命令提供者
func NewCommandProvider(config *types.Config) *CommandProvider {
	return &CommandProvider{
		config: config,
	}
}

// CommandType 命令类型
type CommandType string

const (
	CommandTypeMount   CommandType = "mount"
	CommandTypeUnmount CommandType = "unmount"
)

// ShareType 共享类型
type ShareType string

const (
	ShareTypeNFS   ShareType = "nfs"
	ShareTypeSamba ShareType = "samba"
	ShareTypeSSHFS ShareType = "sshfs"
)

// CommandParams 命令参数
type CommandParams struct {
	TargetDeviceIP string
	Username       string
	Port           int
	ShareName      string
	ExportPath     string
	RemotePath     string
	MountPoint     string
	Options        map[string]string
}

// GetCommand 获取平台特定的命令
func (p *CommandProvider) GetCommand(shareType ShareType, cmdType CommandType, params CommandParams) (string, error) {
	// 根据当前操作系统选择命令模板
	var cmdTemplate string
	
	switch shareType {
	case ShareTypeNFS:
		cmdTemplate = p.getNFSCommand(cmdType)
	case ShareTypeSamba:
		cmdTemplate = p.getSambaCommand(cmdType)
	case ShareTypeSSHFS:
		cmdTemplate = p.getSSHFSCommand(cmdType)
	default:
		return "", fmt.Errorf("不支持的共享类型: %s", shareType)
	}
	
	// 解析命令模板
	return p.parseCommandTemplate(cmdTemplate, params)
}

// getNFSCommand 获取NFS命令模板
func (p *CommandProvider) getNFSCommand(cmdType CommandType) string {
	switch runtime.GOOS {
	case "linux":
		if cmdType == CommandTypeMount {
			return "sudo mount -t nfs {{.TargetDeviceIP}}:{{.ExportPath}} {{.MountPoint}}"
		} else {
			return "sudo umount {{.MountPoint}}"
		}
	case "darwin":
		if cmdType == CommandTypeMount {
			return "sudo mount -t nfs {{.TargetDeviceIP}}:{{.ExportPath}} {{.MountPoint}}"
		} else {
			return "sudo umount {{.MountPoint}}"
		}
	case "windows":
		if cmdType == CommandTypeMount {
			return "mount -t nfs {{.TargetDeviceIP}}:{{.ExportPath}} {{.MountPoint}}"
		} else {
			return "umount {{.MountPoint}}"
		}
	default:
		return ""
	}
}

// getSambaCommand 获取SAMBA命令模板
func (p *CommandProvider) getSambaCommand(cmdType CommandType) string {
	switch runtime.GOOS {
	case "linux":
		if cmdType == CommandTypeMount {
			return "sudo mount -t cifs //{{.TargetDeviceIP}}/{{.ShareName}} {{.MountPoint}} -o guest,uid=$(id -u),gid=$(id -g),iocharset=utf8"
		} else {
			return "sudo umount {{.MountPoint}}"
		}
	case "darwin":
		if cmdType == CommandTypeMount {
			return "mount -t smbfs //guest@{{.TargetDeviceIP}}/{{.ShareName}} {{.MountPoint}}"
		} else {
			return "umount {{.MountPoint}}"
		}
	case "windows":
		if cmdType == CommandTypeMount {
			return "net use {{.MountPoint}} \\\\{{.TargetDeviceIP}}\\{{.ShareName}} /persistent:no"
		} else {
			return "net use {{.MountPoint}} /delete"
		}
	default:
		return ""
	}
}

// getSSHFSCommand 获取SSHFS命令模板
func (p *CommandProvider) getSSHFSCommand(cmdType CommandType) string {
	switch runtime.GOOS {
	case "linux":
		if cmdType == CommandTypeMount {
			return "sshfs {{.Username}}@{{.TargetDeviceIP}}:{{.RemotePath}} {{.MountPoint}} -p {{.Port}} -o allow_other,default_permissions"
		} else {
			return "fusermount -u {{.MountPoint}}"
		}
	case "darwin":
		if cmdType == CommandTypeMount {
			return "sshfs {{.Username}}@{{.TargetDeviceIP}}:{{.RemotePath}} {{.MountPoint}} -p {{.Port}} -o allow_other,default_permissions"
		} else {
			return "umount {{.MountPoint}}"
		}
	case "windows":
		// Windows下SSHFS支持有限
		return ""
	default:
		return ""
	}
}

// parseCommandTemplate 解析命令模板
func (p *CommandProvider) parseCommandTemplate(cmdTemplate string, params CommandParams) (string, error) {
	if cmdTemplate == "" {
		return "", fmt.Errorf("当前操作系统 %s 不支持此命令", runtime.GOOS)
	}
	
	// 创建模板
	tmpl, err := template.New("command").Parse(cmdTemplate)
	if err != nil {
		return "", fmt.Errorf("解析命令模板失败: %v", err)
	}
	
	// 执行模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, params); err != nil {
		return "", fmt.Errorf("执行命令模板失败: %v", err)
	}
	
	return strings.TrimSpace(buf.String()), nil
}

// IsSupported 检查当前平台是否支持指定的共享类型
func (p *CommandProvider) IsSupported(shareType ShareType) bool {
	switch shareType {
	case ShareTypeNFS:
		return runtime.GOOS == "linux" || runtime.GOOS == "darwin" || runtime.GOOS == "windows"
	case ShareTypeSamba:
		return runtime.GOOS == "linux" || runtime.GOOS == "darwin" || runtime.GOOS == "windows"
	case ShareTypeSSHFS:
		return runtime.GOOS == "linux" || runtime.GOOS == "darwin"
	default:
		return false
	}
}

// GetSupportedShareTypes 获取当前平台支持的共享类型
func (p *CommandProvider) GetSupportedShareTypes() []ShareType {
	var supported []ShareType
	
	allTypes := []ShareType{ShareTypeNFS, ShareTypeSamba, ShareTypeSSHFS}
	for _, shareType := range allTypes {
		if p.IsSupported(shareType) {
			supported = append(supported, shareType)
		}
	}
	
	return supported
}

// GetRecommendedShareType 获取推荐的共享类型
func (p *CommandProvider) GetRecommendedShareType() ShareType {
	// 根据平台返回推荐的共享类型
	switch runtime.GOOS {
	case "windows":
		return ShareTypeSamba // Windows原生支持SMB
	case "linux":
		return ShareTypeSamba // SAMBA兼容性最好
	case "darwin":
		return ShareTypeSamba // macOS对SMB支持良好
	default:
		return ShareTypeSamba
	}
} 