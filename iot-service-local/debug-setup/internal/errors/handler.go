package errors

import (
	"fmt"
	"strings"
)

// ErrorType 错误类型
type ErrorType int

const (
	ErrorTypeGeneric ErrorType = iota
	ErrorTypeSSH
	ErrorTypeNFS
	ErrorTypeSamba
	ErrorTypeSSHFS
	ErrorTypePortForwarding
	ErrorTypeConfig
	ErrorTypeDocker
	ErrorTypeKernel
	ErrorTypeNetwork
	ErrorTypePermission
	ErrorTypeNotFound
)

// SetupError 设置错误结构体
type SetupError struct {
	Type        ErrorType
	Step        string
	Message     string
	Cause       error
	Suggestions []string
}

// Error 实现error接口
func (e *SetupError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.Step, e.Message, e.Cause)
}

// NewSetupError 创建设置错误
func NewSetupError(errorType ErrorType, step, message string, cause error) *SetupError {
	return &SetupError{
		Type:        errorType,
		Step:        step,
		Message:     message,
		Cause:       cause,
		Suggestions: getSuggestions(errorType, cause),
	}
}

// getSuggestions 根据错误类型和原因获取建议
func getSuggestions(errorType ErrorType, cause error) []string {
	return GetSuggestionsForError(errorType, cause)
}

// getNFSSuggestions NFS相关建议
func getNFSSuggestions(causeStr string) []string {
	suggestions := []string{}

	if strings.Contains(causeStr, "module") || strings.Contains(causeStr, "nfsd") {
		suggestions = append(suggestions, []string{
			"内核不支持NFS服务器模块",
			"解决方案：",
			"1. 切换到SAMBA文件共享：在配置文件中设置 file_sharing.type: \"samba\"",
			"2. 或者使用SSHFS：设置 file_sharing.type: \"sshfs\"",
			"3. 检查内核配置：cat /boot/config-$(uname -r) | grep NFS",
			"4. 如果可能，重新编译内核并启用NFS支持",
		}...)
	} else if strings.Contains(causeStr, "not implemented") {
		suggestions = append(suggestions, []string{
			"系统不支持NFS导出功能",
			"解决方案：",
			"1. 推荐使用SAMBA：在配置文件中设置 file_sharing.type: \"samba\"",
			"2. 安装NFS工具：sudo apt-get install nfs-kernel-server",
			"3. 检查rpcbind服务：sudo systemctl status rpcbind",
		}...)
	} else if strings.Contains(causeStr, "permission") {
		suggestions = append(suggestions, []string{
			"权限问题",
			"解决方案：",
			"1. 检查目录权限：ls -la /path/to/directory",
			"2. 修改权限：sudo chmod 755 /path/to/directory",
			"3. 检查SELinux设置（如果适用）",
		}...)
	} else {
		suggestions = append(suggestions, []string{
			"NFS设置失败",
			"解决方案：",
			"1. 检查NFS服务状态：sudo systemctl status nfs-server",
			"2. 查看/etc/exports文件语法",
			"3. 重启NFS服务：sudo systemctl restart nfs-server",
			"4. 检查防火墙设置（端口111, 2049）",
		}...)
	}

	return suggestions
}

// getSambaSuggestions SAMBA相关建议
func getSambaSuggestions(causeStr string) []string {
	suggestions := []string{}

	if strings.Contains(causeStr, "not found") || strings.Contains(causeStr, "command not found") {
		suggestions = append(suggestions, []string{
			"SAMBA软件包未安装",
			"解决方案：",
			"1. 安装SAMBA：sudo apt-get install samba samba-common-bin",
			"2. 启动服务：sudo systemctl start smbd nmbd",
			"3. 设置开机自启：sudo systemctl enable smbd nmbd",
		}...)
	} else if strings.Contains(causeStr, "permission") {
		suggestions = append(suggestions, []string{
			"权限问题",
			"解决方案：",
			"1. 检查目录权限：ls -la /path/to/directory",
			"2. 修改权限：sudo chmod 755 /path/to/directory",
			"3. 检查SAMBA用户：sudo smbpasswd -a username",
		}...)
	} else {
		suggestions = append(suggestions, []string{
			"SAMBA设置失败",
			"解决方案：",
			"1. 检查SAMBA服务状态：sudo systemctl status smbd nmbd",
			"2. 验证配置文件：sudo testparm",
			"3. 重启SAMBA服务：sudo systemctl restart smbd nmbd",
			"4. 检查防火墙设置（端口445, 139）",
		}...)
	}

	return suggestions
}

// getSSHFSSuggestions SSHFS相关建议
func getSSHFSSuggestions(causeStr string) []string {
	return []string{
		"SSHFS设置失败",
		"解决方案：",
		"1. 安装SSHFS：sudo apt-get install sshfs",
		"2. 检查SSH连接：ssh user@host",
		"3. 检查FUSE模块：modprobe fuse",
		"4. 检查用户权限：将用户添加到fuse组",
	}
}

// getPortForwardingSuggestions 端口转发相关建议
func getPortForwardingSuggestions(causeStr string) []string {
	suggestions := []string{}

	if strings.Contains(causeStr, "port") && strings.Contains(causeStr, "use") {
		suggestions = append(suggestions, []string{
			"端口被占用",
			"解决方案：",
			"1. 查看端口占用：sudo netstat -tlnp | grep :port",
			"2. 杀死占用进程：sudo kill -9 PID",
			"3. 修改配置文件中的端口号",
		}...)
	} else if strings.Contains(causeStr, "iptables") {
		suggestions = append(suggestions, []string{
			"iptables配置失败",
			"解决方案：",
			"1. 检查iptables服务：sudo systemctl status iptables",
			"2. 检查权限：确保有sudo权限",
			"3. 手动添加规则：sudo iptables -t nat -A OUTPUT -p tcp --dport port -j REDIRECT --to-ports port",
		}...)
	} else {
		suggestions = append(suggestions, []string{
			"端口转发设置失败",
			"解决方案：",
			"1. 检查防火墙设置",
			"2. 验证端口配置",
			"3. 检查网络连接",
		}...)
	}

	return suggestions
}

// getDockerSuggestions Docker相关建议
func getDockerSuggestions(causeStr string) []string {
	suggestions := []string{}

	if strings.Contains(causeStr, "not found") || strings.Contains(causeStr, "command not found") {
		suggestions = append(suggestions, []string{
			"Docker未安装",
			"解决方案：",
			"1. 安装Docker：curl -fsSL https://get.docker.com | sh",
			"2. 启动Docker服务：sudo systemctl start docker",
			"3. 添加用户到docker组：sudo usermod -aG docker $USER",
		}...)
	} else if strings.Contains(causeStr, "permission") {
		suggestions = append(suggestions, []string{
			"Docker权限问题",
			"解决方案：",
			"1. 添加用户到docker组：sudo usermod -aG docker $USER",
			"2. 重新登录或重启终端",
			"3. 检查Docker守护进程：sudo systemctl status docker",
		}...)
	} else if strings.Contains(causeStr, "no such container") {
		suggestions = append(suggestions, []string{
			"容器不存在",
			"解决方案：",
			"1. 检查容器名称是否正确",
			"2. 查看所有容器：docker ps -a",
			"3. 创建容器或修改配置中的容器名称",
		}...)
	} else {
		suggestions = append(suggestions, []string{
			"Docker操作失败",
			"解决方案：",
			"1. 检查Docker服务状态：sudo systemctl status docker",
			"2. 查看Docker日志：sudo journalctl -u docker",
			"3. 重启Docker服务：sudo systemctl restart docker",
		}...)
	}

	return suggestions
}

// getSSHSuggestions SSH相关建议
func getSSHSuggestions(causeStr string) []string {
	suggestions := []string{}

	if strings.Contains(causeStr, "connection refused") {
		suggestions = append(suggestions, []string{
			"SSH连接被拒绝",
			"解决方案：",
			"1. 检查SSH服务状态：sudo systemctl status ssh",
			"2. 检查端口是否正确（默认22）",
			"3. 检查防火墙设置",
			"4. 验证IP地址是否正确",
		}...)
	} else if strings.Contains(causeStr, "timeout") {
		suggestions = append(suggestions, []string{
			"SSH连接超时",
			"解决方案：",
			"1. 检查网络连接：ping target_ip",
			"2. 检查防火墙设置",
			"3. 增加连接超时时间",
		}...)
	} else if strings.Contains(causeStr, "authentication") {
		suggestions = append(suggestions, []string{
			"SSH认证失败",
			"解决方案：",
			"1. 检查用户名和密码",
			"2. 检查SSH密钥文件权限",
			"3. 验证密钥文件路径",
		}...)
	} else {
		suggestions = append(suggestions, []string{
			"SSH连接失败",
			"解决方案：",
			"1. 检查网络连接",
			"2. 验证SSH配置",
			"3. 查看SSH日志",
		}...)
	}

	return suggestions
}

// getConfigSuggestions 配置相关建议
func getConfigSuggestions(causeStr string) []string {
	return []string{
		"配置文件问题",
		"解决方案：",
		"1. 检查YAML语法：使用在线YAML验证器",
		"2. 检查文件路径是否正确",
		"3. 验证配置项是否完整",
		"4. 参考示例配置文件",
	}
}

// getKernelSuggestions 内核相关建议
func getKernelSuggestions(causeStr string) []string {
	return []string{
		"内核不支持相关功能",
		"解决方案：",
		"1. 检查内核版本：uname -r",
		"2. 检查内核模块：lsmod | grep module_name",
		"3. 尝试加载模块：sudo modprobe module_name",
		"4. 考虑使用替代方案",
	}
}

// getNetworkSuggestions 网络相关建议
func getNetworkSuggestions(causeStr string) []string {
	return []string{
		"网络连接问题",
		"解决方案：",
		"1. 检查网络连接：ping target_ip",
		"2. 检查路由表：route -n",
		"3. 检查DNS解析：nslookup hostname",
		"4. 检查防火墙设置",
	}
}

// getPermissionSuggestions 权限相关建议
func getPermissionSuggestions(causeStr string) []string {
	return []string{
		"权限不足",
		"解决方案：",
		"1. 检查文件权限：ls -la file_path",
		"2. 修改权限：chmod 755 file_path",
		"3. 检查用户组：groups username",
		"4. 使用sudo运行命令",
	}
}

// getNotFoundSuggestions 未找到相关建议
func getNotFoundSuggestions(causeStr string) []string {
	return []string{
		"文件或命令未找到",
		"解决方案：",
		"1. 检查文件路径是否正确",
		"2. 安装所需软件包",
		"3. 检查PATH环境变量",
		"4. 验证文件是否存在",
	}
}

// PrintDetailedError 打印详细错误信息
func PrintDetailedError(err error) {
	if setupErr, ok := err.(*SetupError); ok {
		fmt.Printf("\n❌ 设置失败：%s\n", setupErr.Step)
		fmt.Printf("📋 错误信息：%s\n", setupErr.Message)
		if setupErr.Cause != nil {
			fmt.Printf("🔍 详细原因：%s\n", setupErr.Cause)
		}
		
		if len(setupErr.Suggestions) > 0 {
			fmt.Printf("\n💡 解决建议：\n")
			for _, suggestion := range setupErr.Suggestions {
				if strings.HasPrefix(suggestion, "解决方案：") || strings.HasSuffix(suggestion, "：") {
					fmt.Printf("  %s\n", suggestion)
				} else if strings.HasPrefix(suggestion, " ") {
					fmt.Printf("  %s\n", suggestion)
				} else {
					fmt.Printf("  • %s\n", suggestion)
				}
			}
		}
		
		fmt.Printf("\n🔄 您可以尝试以下操作：\n")
		fmt.Printf("  • 根据上述建议解决问题后重新运行\n")
		fmt.Printf("  • 使用诊断模式获取更多信息：--diagnose\n")
		fmt.Printf("  • 切换到其他实现方式（如SAMBA替代NFS）\n")
		
	} else {
		fmt.Printf("\n❌ 设置失败：%s\n", err)
		fmt.Printf("💡 建议：检查系统日志以获取更多信息\n")
	}
}

// AnalyzeError 分析错误类型
func AnalyzeError(err error, step string) *SetupError {
	if err == nil {
		return nil
	}
	
	errStr := strings.ToLower(err.Error())
	
	// 根据错误内容判断错误类型
	var errorType ErrorType
	var message string
	
	switch {
	case strings.Contains(errStr, "nfs") || strings.Contains(errStr, "nfsd"):
		errorType = ErrorTypeNFS
		message = "NFS文件共享设置失败"
	case strings.Contains(errStr, "samba") || strings.Contains(errStr, "smb"):
		errorType = ErrorTypeSamba
		message = "SAMBA文件共享设置失败"
	case strings.Contains(errStr, "sshfs"):
		errorType = ErrorTypeSSHFS
		message = "SSHFS文件共享设置失败"
	case strings.Contains(errStr, "docker"):
		errorType = ErrorTypeDocker
		message = "Docker容器控制失败"
	case strings.Contains(errStr, "ssh") || strings.Contains(errStr, "connection"):
		errorType = ErrorTypeSSH
		message = "SSH连接失败"
	case strings.Contains(errStr, "port") || strings.Contains(errStr, "iptables"):
		errorType = ErrorTypePortForwarding
		message = "端口转发设置失败"
	case strings.Contains(errStr, "config") || strings.Contains(errStr, "yaml"):
		errorType = ErrorTypeConfig
		message = "配置文件处理失败"
	case strings.Contains(errStr, "module") || strings.Contains(errStr, "kernel"):
		errorType = ErrorTypeKernel
		message = "内核模块不支持"
	case strings.Contains(errStr, "network") || strings.Contains(errStr, "timeout"):
		errorType = ErrorTypeNetwork
		message = "网络连接问题"
	case strings.Contains(errStr, "permission") || strings.Contains(errStr, "denied"):
		errorType = ErrorTypePermission
		message = "权限不足"
	case strings.Contains(errStr, "not found") || strings.Contains(errStr, "no such"):
		errorType = ErrorTypeNotFound
		message = "文件或命令未找到"
	default:
		errorType = ErrorTypeGeneric
		message = "未知错误"
	}
	
	return NewSetupError(errorType, step, message, err)
} 