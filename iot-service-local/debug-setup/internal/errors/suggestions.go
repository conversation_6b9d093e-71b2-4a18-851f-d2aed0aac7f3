package errors

import (
	"strings"
)

// SuggestionProvider 建议提供者接口
type SuggestionProvider interface {
	GetSuggestions(causeStr string) []string
	GetType() ErrorType
}

// SuggestionRule 建议规则
type SuggestionRule struct {
	Keywords    []string
	Suggestions []string
	Priority    int
}

// SuggestionManager 建议管理器
type SuggestionManager struct {
	providers map[ErrorType]SuggestionProvider
}

// NewSuggestionManager 创建建议管理器
func NewSuggestionManager() *SuggestionManager {
	manager := &SuggestionManager{
		providers: make(map[ErrorType]SuggestionProvider),
	}
	
	// 注册默认提供者
	manager.RegisterProvider(&NFSSuggestionProvider{})
	manager.RegisterProvider(&SambaSuggestionProvider{})
	manager.RegisterProvider(&SSHFSSuggestionProvider{})
	manager.RegisterProvider(&PortForwardingSuggestionProvider{})
	manager.RegisterProvider(&DockerSuggestionProvider{})
	manager.RegisterProvider(&SSHSuggestionProvider{})
	manager.RegisterProvider(&ConfigSuggestionProvider{})
	manager.RegisterProvider(&KernelSuggestionProvider{})
	manager.RegisterProvider(&NetworkSuggestionProvider{})
	manager.RegisterProvider(&PermissionSuggestionProvider{})
	manager.RegisterProvider(&NotFoundSuggestionProvider{})
	
	return manager
}

// RegisterProvider 注册建议提供者
func (sm *SuggestionManager) RegisterProvider(provider SuggestionProvider) {
	sm.providers[provider.GetType()] = provider
}

// GetSuggestions 获取建议
func (sm *SuggestionManager) GetSuggestions(errorType ErrorType, causeStr string) []string {
	provider, exists := sm.providers[errorType]
	if !exists {
		return []string{
			"检查系统日志以获取更多信息",
			"尝试重新运行程序",
			"如果问题持续，请联系技术支持",
		}
	}
	
	return provider.GetSuggestions(causeStr)
}

// BaseSuggestionProvider 基础建议提供者
type BaseSuggestionProvider struct {
	errorType ErrorType
	rules     []SuggestionRule
}

// NewBaseSuggestionProvider 创建基础建议提供者
func NewBaseSuggestionProvider(errorType ErrorType) *BaseSuggestionProvider {
	return &BaseSuggestionProvider{
		errorType: errorType,
		rules:     make([]SuggestionRule, 0),
	}
}

// AddRule 添加规则
func (p *BaseSuggestionProvider) AddRule(keywords []string, suggestions []string, priority int) {
	p.rules = append(p.rules, SuggestionRule{
		Keywords:    keywords,
		Suggestions: suggestions,
		Priority:    priority,
	})
}

// GetSuggestions 获取建议
func (p *BaseSuggestionProvider) GetSuggestions(causeStr string) []string {
	var allSuggestions []string
	matchedRules := make([]SuggestionRule, 0)
	
	// 匹配规则
	for _, rule := range p.rules {
		for _, keyword := range rule.Keywords {
			if strings.Contains(causeStr, keyword) {
				matchedRules = append(matchedRules, rule)
				break
			}
		}
	}
	
	// 如果没有匹配的规则，返回默认建议
	if len(matchedRules) == 0 {
		return p.getDefaultSuggestions()
	}
	
	// 按优先级排序并收集建议
	for _, rule := range matchedRules {
		allSuggestions = append(allSuggestions, rule.Suggestions...)
	}
	
	return allSuggestions
}

// getDefaultSuggestions 获取默认建议
func (p *BaseSuggestionProvider) getDefaultSuggestions() []string {
	return []string{
		"检查系统日志以获取更多信息",
		"尝试重新运行程序",
	}
}

// GetType 获取错误类型
func (p *BaseSuggestionProvider) GetType() ErrorType {
	return p.errorType
}

// NFS建议提供者
type NFSSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *NFSSuggestionProvider) GetType() ErrorType {
	return ErrorTypeNFS
}

func (p *NFSSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeNFS)
	
	provider.AddRule(
		[]string{"module", "nfsd"},
		[]string{
			"内核不支持NFS服务器模块",
			"请检查内核配置: lsmod | grep nfsd",
			"可能需要安装nfs-kernel-server: sudo apt-get install nfs-kernel-server",
			"或者切换到SAMBA文件共享",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"NFS权限问题",
			"检查exports配置: sudo exportfs -v",
			"确保目录权限正确: ls -la /path/to/share",
			"检查NFS服务状态: sudo systemctl status nfs-server",
		},
		2,
	)
	
	provider.AddRule(
		[]string{"connection refused", "connection timeout"},
		[]string{
			"NFS连接问题",
			"检查NFS服务是否运行: sudo systemctl status nfs-server",
			"检查防火墙设置: sudo ufw status",
			"确保端口2049开放: netstat -tulpn | grep 2049",
		},
		3,
	)
	
	return provider.GetSuggestions(causeStr)
}

// SAMBA建议提供者
type SambaSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *SambaSuggestionProvider) GetType() ErrorType {
	return ErrorTypeSamba
}

func (p *SambaSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeSamba)
	
	provider.AddRule(
		[]string{"command not found", "samba", "smbd"},
		[]string{
			"SAMBA未安装",
			"安装SAMBA: sudo apt-get install samba",
			"检查安装状态: which smbd",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"SAMBA权限问题",
			"检查SAMBA配置: sudo testparm",
			"确保共享目录权限正确",
			"检查SAMBA用户: sudo smbpasswd -a username",
		},
		2,
	)
	
	provider.AddRule(
		[]string{"connection refused", "connection timeout"},
		[]string{
			"SAMBA连接问题",
			"检查SAMBA服务: sudo systemctl status smbd nmbd",
			"确保端口开放: netstat -tulpn | grep ':445\\|:139'",
			"检查防火墙设置",
		},
		3,
	)
	
	return provider.GetSuggestions(causeStr)
}

// SSHFS建议提供者
type SSHFSSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *SSHFSSuggestionProvider) GetType() ErrorType {
	return ErrorTypeSSHFS
}

func (p *SSHFSSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeSSHFS)
	
	provider.AddRule(
		[]string{"command not found", "sshfs"},
		[]string{
			"SSHFS未安装",
			"安装SSHFS: sudo apt-get install sshfs",
			"检查FUSE模块: modprobe fuse",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"SSHFS权限问题",
			"检查SSH连接: ssh user@host",
			"确保用户有足够权限",
			"检查目录权限",
		},
		2,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 端口转发建议提供者
type PortForwardingSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *PortForwardingSuggestionProvider) GetType() ErrorType {
	return ErrorTypePortForwarding
}

func (p *PortForwardingSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypePortForwarding)
	
	provider.AddRule(
		[]string{"iptables", "command not found"},
		[]string{
			"iptables未安装",
			"安装iptables: sudo apt-get install iptables",
			"检查防火墙服务",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"iptables权限问题",
			"需要root权限执行iptables命令",
			"检查sudo配置",
		},
		2,
	)
	
	return provider.GetSuggestions(causeStr)
}

// Docker建议提供者
type DockerSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *DockerSuggestionProvider) GetType() ErrorType {
	return ErrorTypeDocker
}

func (p *DockerSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeDocker)
	
	provider.AddRule(
		[]string{"command not found", "docker"},
		[]string{
			"Docker未安装",
			"安装Docker: curl -fsSL https://get.docker.com | sh",
			"启动Docker服务: sudo systemctl start docker",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"Docker权限问题",
			"添加用户到docker组: sudo usermod -aG docker $USER",
			"重新登录或重启docker服务",
		},
		2,
	)
	
	provider.AddRule(
		[]string{"no such container", "container not found"},
		[]string{
			"容器不存在",
			"检查容器列表: docker ps -a",
			"创建容器或修改配置",
		},
		3,
	)
	
	return provider.GetSuggestions(causeStr)
}

// SSH建议提供者
type SSHSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *SSHSuggestionProvider) GetType() ErrorType {
	return ErrorTypeSSH
}

func (p *SSHSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeSSH)
	
	provider.AddRule(
		[]string{"connection refused", "connection timeout"},
		[]string{
			"SSH连接问题",
			"检查目标设备SSH服务: systemctl status sshd",
			"检查网络连接: ping target_ip",
			"确认SSH端口: netstat -tlnp | grep :22",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"authentication failed", "permission denied"},
		[]string{
			"SSH认证问题",
			"检查用户名和密码",
			"确认SSH密钥配置",
			"检查SSH配置: /etc/ssh/sshd_config",
		},
		2,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 配置建议提供者
type ConfigSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *ConfigSuggestionProvider) GetType() ErrorType {
	return ErrorTypeConfig
}

func (p *ConfigSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeConfig)
	
	provider.AddRule(
		[]string{"yaml", "parse", "unmarshal"},
		[]string{
			"配置文件格式错误",
			"检查YAML语法",
			"验证配置文件: yamllint config.yml",
		},
		1,
	)
	
	provider.AddRule(
		[]string{"file not found", "no such file"},
		[]string{
			"配置文件不存在",
			"检查文件路径",
			"创建配置文件模板",
		},
		2,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 内核建议提供者
type KernelSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *KernelSuggestionProvider) GetType() ErrorType {
	return ErrorTypeKernel
}

func (p *KernelSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeKernel)
	
	provider.AddRule(
		[]string{"module", "not found"},
		[]string{
			"内核模块问题",
			"检查可用模块: lsmod",
			"加载模块: modprobe module_name",
		},
		1,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 网络建议提供者
type NetworkSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *NetworkSuggestionProvider) GetType() ErrorType {
	return ErrorTypeNetwork
}

func (p *NetworkSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeNetwork)
	
	provider.AddRule(
		[]string{"network", "unreachable"},
		[]string{
			"网络不可达",
			"检查网络连接",
			"确认IP地址和路由",
		},
		1,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 权限建议提供者
type PermissionSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *PermissionSuggestionProvider) GetType() ErrorType {
	return ErrorTypePermission
}

func (p *PermissionSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypePermission)
	
	provider.AddRule(
		[]string{"permission denied", "access denied"},
		[]string{
			"权限不足",
			"检查文件/目录权限",
			"使用sudo或修改权限",
		},
		1,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 未找到建议提供者
type NotFoundSuggestionProvider struct {
	*BaseSuggestionProvider
}

func (p *NotFoundSuggestionProvider) GetType() ErrorType {
	return ErrorTypeNotFound
}

func (p *NotFoundSuggestionProvider) GetSuggestions(causeStr string) []string {
	provider := NewBaseSuggestionProvider(ErrorTypeNotFound)
	
	provider.AddRule(
		[]string{"not found", "no such file", "command not found"},
		[]string{
			"资源不存在",
			"检查路径或命令",
			"安装相关软件包",
		},
		1,
	)
	
	return provider.GetSuggestions(causeStr)
}

// 全局建议管理器实例
var globalSuggestionManager *SuggestionManager

// InitSuggestionManager 初始化建议管理器
func InitSuggestionManager() {
	globalSuggestionManager = NewSuggestionManager()
}

// GetSuggestionsForError 获取错误建议
func GetSuggestionsForError(errorType ErrorType, cause error) []string {
	if globalSuggestionManager == nil {
		InitSuggestionManager()
	}
	
	if cause == nil {
		return []string{}
	}
	
	causeStr := strings.ToLower(cause.Error())
	return globalSuggestionManager.GetSuggestions(errorType, causeStr)
} 