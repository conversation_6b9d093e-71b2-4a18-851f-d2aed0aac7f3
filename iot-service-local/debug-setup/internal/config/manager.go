package config

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"text/template"
	"time"

	"gopkg.in/yaml.v3"

	"github.com/addx/iot-debug-setup/internal/types"
	"github.com/addx/iot-debug-setup/internal/utils"
)

// Manager 配置管理器结构体
type Manager struct {
	config           *types.Config
	backupManager    *utils.LocalBackupManager
	originalContents map[string]string
	templateVars     map[string]interface{}
	configFiles      []string // 跟踪处理过的配置文件
}

// NewManager 创建新的配置管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config:           config,
		backupManager:    utils.NewLocalBackupManager(),
		originalContents: make(map[string]string),
		templateVars:     make(map[string]interface{}),
		configFiles:      make([]string, 0),
	}
}

// UpdateConfigFile 更新配置文件
func (m *Manager) UpdateConfigFile() error {
	fmt.Println("📝 开始更新配置文件...")
	
	// 准备模板变量
	m.prepareTemplateVars()
	
	// 获取配置文件路径
	configPaths := m.getConfigPaths()
	if len(configPaths) == 0 {
		return fmt.Errorf("没有找到配置文件路径")
	}
	
	// 查找可用的配置文件
	availableConfigs := m.findAvailableConfigs(configPaths)
	if len(availableConfigs) == 0 {
		return fmt.Errorf("没有找到可用的配置文件")
	}
	
	// 根据 active_profile 处理配置替换
	if err := m.processConfigReplacements(availableConfigs); err != nil {
		return fmt.Errorf("处理配置替换失败: %v", err)
	}
	
	fmt.Println("✅ 配置文件更新完成")
	return nil
}

// findAvailableConfigs 查找可用的配置文件
func (m *Manager) findAvailableConfigs(configPaths []string) []string {
	var availableConfigs []string
	
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			availableConfigs = append(availableConfigs, path)
			fmt.Printf("✅ 找到配置文件: %s\n", path)
		} else {
			fmt.Printf("⚠️  配置文件不存在: %s\n", path)
		}
	}
	
	return availableConfigs
}

// processConfigReplacements 处理配置替换
func (m *Manager) processConfigReplacements(availableConfigs []string) error {
	// 读取所有可用配置文件的内容
	configContents := make(map[string]string)
	for _, configPath := range availableConfigs {
		if err := m.readOriginalConfig(configPath); err != nil {
			return fmt.Errorf("读取原始配置失败 [%s]: %v", configPath, err)
		}
		
		if err := m.backupOriginalConfig(configPath); err != nil {
			return fmt.Errorf("备份原始配置失败 [%s]: %v", configPath, err)
		}
		
		configContents[configPath] = m.originalContents[configPath]
	}
	
	// 处理每个替换规则
	for _, replacement := range m.config.ConfigReplacements.Replacements {
		if err := m.processReplacementWithPriority(replacement, availableConfigs, configContents); err != nil {
			return fmt.Errorf("处理替换规则失败 [%s]: %v", replacement.Key, err)
		}
	}
	
	// 写回修改后的配置文件
	for configPath, content := range configContents {
		if err := m.writeConfigFile(configPath, content); err != nil {
			return fmt.Errorf("写入配置文件失败 [%s]: %v", configPath, err)
		}
	}
	
	return nil
}

// processReplacementWithPriority 按优先级处理单个替换规则
func (m *Manager) processReplacementWithPriority(replacement types.Replacement, availableConfigs []string, configContents map[string]string) error {
	fmt.Printf("🔄 处理替换: %s -> %s\n", replacement.Key, replacement.Value)
	
	// 处理模板变量
	processedValue, err := m.processTemplate(replacement.Value)
	if err != nil {
		return fmt.Errorf("处理模板变量失败: %v", err)
	}
	
	// 如果设置了 active_profile，优先在 profile 文件中查找和替换
	if m.config.ConfigReplacements.ActiveProfile != "" {
		profileFileName := fmt.Sprintf("application-%s.yml", m.config.ConfigReplacements.ActiveProfile)
		
		for _, configPath := range availableConfigs {
			if strings.Contains(configPath, profileFileName) {
				fmt.Printf("  🎯 优先在profile文件中查找: %s\n", configPath)
				
				// 尝试在profile文件中替换
				newContent, err := m.replaceConfigValue(configContents[configPath], replacement.Key, processedValue)
				if err == nil {
					configContents[configPath] = newContent
					fmt.Printf("  ✅ 在profile文件中替换成功: %s\n", replacement.Key)
					return nil
				} else {
					fmt.Printf("  ⚠️  在profile文件中未找到键: %s\n", replacement.Key)
				}
			}
		}
	}
	
	// 如果在profile文件中未找到，或者没有设置active_profile，尝试在基础配置文件中替换
	for _, configPath := range availableConfigs {
		if strings.Contains(configPath, "application.yml") && !strings.Contains(configPath, "application-") {
			fmt.Printf("  🎯 在基础配置文件中查找: %s\n", configPath)
			
			newContent, err := m.replaceConfigValue(configContents[configPath], replacement.Key, processedValue)
			if err == nil {
				configContents[configPath] = newContent
				fmt.Printf("  ✅ 在基础配置文件中替换成功: %s\n", replacement.Key)
				return nil
			} else {
				fmt.Printf("  ⚠️  在基础配置文件中未找到键: %s\n", replacement.Key)
			}
		}
	}
	
	return fmt.Errorf("未在任何配置文件中找到键: %s", replacement.Key)
}

// writeConfigFile 写入配置文件
func (m *Manager) writeConfigFile(configPath, content string) error {
	fmt.Printf("💾 写入配置文件: %s\n", configPath)
	
	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}
	
	// 写入文件
	if err := ioutil.WriteFile(configPath, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}
	
	return nil
}

// getConfigPaths 获取配置文件路径列表
func (m *Manager) getConfigPaths() []string {
	// 如果没有配置项目根目录，返回空列表
	if m.config.ConfigReplacements.ProjectRoot == "" {
		fmt.Println("⚠️  警告：未配置项目根目录")
		return []string{}
	}
	
	// 根据项目根目录构建标准的Spring Boot配置文件路径
	projectRoot := m.config.ConfigReplacements.ProjectRoot
	fmt.Printf("📂 项目根目录: %s\n", projectRoot)
	
	// 转换为当前环境的路径格式
	convertedRoot := m.convertToLocalPath(projectRoot)
	fmt.Printf("📂 转换后的根目录: %s\n", convertedRoot)
	
	// 构建配置文件路径
	var paths []string
	
	// 基础配置文件
	baseConfigPath := filepath.Join(convertedRoot, "src", "main", "resources", "application.yml")
	paths = append(paths, baseConfigPath)
	
	// 如果设置了active_profile，添加对应的profile文件
	if m.config.ConfigReplacements.ActiveProfile != "" {
		profileFileName := fmt.Sprintf("application-%s.yml", m.config.ConfigReplacements.ActiveProfile)
		profileConfigPath := filepath.Join(convertedRoot, "src", "main", "resources", profileFileName)
		paths = append(paths, profileConfigPath)
		fmt.Printf("📋 激活的profile: %s\n", m.config.ConfigReplacements.ActiveProfile)
	}
	
	fmt.Printf("📋 构建的配置文件路径：\n")
	for i, path := range paths {
		fmt.Printf("  %d. %s\n", i+1, path)
	}
	
	return paths
}

// convertToLocalPath 将Windows路径转换为当前环境的路径格式
func (m *Manager) convertToLocalPath(path string) string {
	// 如果路径为空，直接返回
	if path == "" {
		return path
	}
	
	// 检查是否在WSL环境中
	if m.isWSLEnvironment() {
		// 如果是Windows路径（包含冒号），转换为WSL路径
		if strings.Contains(path, ":") {
			// 将 C:/path 转换为 /mnt/c/path
			// 首先替换反斜杠为斜杠
			path = strings.ReplaceAll(path, "\\", "/")
			
			// 检查是否是Windows驱动器路径格式
			if len(path) >= 2 && path[1] == ':' {
				drive := strings.ToLower(string(path[0]))
				restPath := path[2:]
				// 确保路径以斜杠开头
				if !strings.HasPrefix(restPath, "/") {
					restPath = "/" + restPath
				}
				convertedPath := "/mnt/" + drive + restPath
				fmt.Printf("  - 路径转换: %s -> %s\n", path, convertedPath)
				return convertedPath
			}
		}
	}
	
	return path
}

// isWSLEnvironment 检查是否在WSL环境中
func (m *Manager) isWSLEnvironment() bool {
	// 检查 /proc/version 文件是否包含 WSL 信息
	if content, err := ioutil.ReadFile("/proc/version"); err == nil {
		return strings.Contains(strings.ToLower(string(content)), "wsl") || 
			   strings.Contains(strings.ToLower(string(content)), "microsoft")
	}
	return false
}

// prepareTemplateVars 准备模板变量
func (m *Manager) prepareTemplateVars() {
	// 设置基本变量
	m.templateVars["target_device"] = map[string]interface{}{
		"ip": m.config.TargetDevice.IP,
	}
	
	m.templateVars["local_machine"] = map[string]interface{}{
		"ip": m.config.LocalMachine.IP,
	}

	// 设置通用挂载点路径（支持NFS/SAMBA/SSHFS）
	var mountPath string
	if m.config.LocalMachine.NFSMountPoint != "" {
		// 向后兼容：使用旧的NFSMountPoint配置
		mountPath = m.convertToLocalPath(m.config.LocalMachine.NFSMountPoint)
		fmt.Printf("  - 使用NFSMountPoint: %s -> %s\n", m.config.LocalMachine.NFSMountPoint, mountPath)
	} else {
		// 使用通用的MountPoint配置
		mountPath = m.convertToLocalPath(m.config.LocalMachine.MountPoint)
		fmt.Printf("  - 使用MountPoint: %s -> %s\n", m.config.LocalMachine.MountPoint, mountPath)
	}
	
	// 设置模板变量（支持多种文件共享类型）
	m.templateVars["local_mount_path"] = mountPath

	fmt.Printf("📋 模板变量准备完成:\n")
	fmt.Printf("  - target_device.ip: %s\n", m.config.TargetDevice.IP)
	fmt.Printf("  - local_machine.ip: %s\n", m.config.LocalMachine.IP)
	fmt.Printf("  - local_mount_path: %s\n", mountPath)
	fmt.Printf("  - WSL环境: %t\n", m.isWSLEnvironment())
}

// readOriginalConfig 读取原始配置文件
func (m *Manager) readOriginalConfig(configPath string) error {
	// 规范化路径
	cleanPath := filepath.Clean(configPath)
	fmt.Printf("🔍 尝试读取配置文件: %s\n", cleanPath)
	
	// 检查文件是否存在
	if fileInfo, err := os.Stat(cleanPath); os.IsNotExist(err) {
		// 添加额外的调试信息
		fmt.Printf("❌ 文件不存在: %s\n", cleanPath)
		fmt.Printf("   当前工作目录: %s\n", getCurrentDir())
		fmt.Printf("   WSL环境: %t\n", m.isWSLEnvironment())
		
		// 如果是WSL环境，尝试列出目录内容
		if m.isWSLEnvironment() {
			parentDir := filepath.Dir(cleanPath)
			fmt.Printf("   尝试列出父目录内容: %s\n", parentDir)
			if files, err := ioutil.ReadDir(parentDir); err == nil {
				fmt.Printf("   目录内容:\n")
				for _, file := range files {
					fmt.Printf("     - %s\n", file.Name())
				}
			} else {
				fmt.Printf("   无法列出目录内容: %v\n", err)
			}
		}
		
		return fmt.Errorf("配置文件不存在: %s", cleanPath)
	} else if err != nil {
		return fmt.Errorf("检查配置文件失败: %v", err)
	} else {
		fmt.Printf("✅ 找到配置文件: %s (大小: %d bytes)\n", cleanPath, fileInfo.Size())
	}

	// 读取文件内容
	content, err := ioutil.ReadFile(cleanPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	m.originalContents[configPath] = string(content)
	fmt.Printf("📖 读取配置文件: %s (%d bytes)\n", cleanPath, len(content))
	return nil
}

// getCurrentDir 获取当前工作目录
func getCurrentDir() string {
	if dir, err := os.Getwd(); err != nil {
		return "未知"
	} else {
		return dir
	}
}

// backupOriginalConfig 备份原始配置文件
func (m *Manager) backupOriginalConfig(configPath string) error {
	// 规范化路径
	cleanPath := filepath.Clean(configPath)
	
	// 使用统一的备份管理器
	if err := m.backupManager.BackupFile(cleanPath); err != nil {
		return fmt.Errorf("备份配置文件失败: %v", err)
	}
	
	// 记录已处理的配置文件
	m.configFiles = append(m.configFiles, cleanPath)
	
	return nil
}

// applyConfigReplacements 应用配置替换
func (m *Manager) applyConfigReplacements(configPath string) error {
	// 规范化路径
	cleanPath := filepath.Clean(configPath)
	fmt.Printf("🔄 应用配置替换到: %s\n", cleanPath)
	
	modifiedContent := m.originalContents[configPath]
	
	// 应用每个替换规则
	for i, replacement := range m.config.ConfigReplacements.Replacements {
		fmt.Printf("  %d. %s\n", i+1, replacement.Description)
		
		// 处理模板变量
		processedValue, err := m.processTemplate(replacement.Value)
		if err != nil {
			return fmt.Errorf("处理模板变量失败 %s: %v", replacement.Key, err)
		}

		// 应用替换
		newContent, err := m.replaceConfigValue(modifiedContent, replacement.Key, processedValue)
		if err != nil {
			return fmt.Errorf("替换配置值失败 %s: %v", replacement.Key, err)
		}
		
		modifiedContent = newContent
		fmt.Printf("     %s: %s\n", replacement.Key, processedValue)
	}

	// 写入修改后的配置文件
	if err := ioutil.WriteFile(cleanPath, []byte(modifiedContent), 0644); err != nil {
		return fmt.Errorf("写入修改后的配置文件失败: %v", err)
	}

	fmt.Printf("✅ 配置替换完成，共应用 %d 个替换规则\n", len(m.config.ConfigReplacements.Replacements))
	return nil
}

// processTemplate 处理模板变量
func (m *Manager) processTemplate(templateStr string) (string, error) {
	// 如果不包含模板语法，直接返回
	if !strings.Contains(templateStr, "{{") {
		return templateStr, nil
	}

	// 创建模板
	tmpl, err := template.New("config").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %v", err)
	}

	// 执行模板
	var result strings.Builder
	if err := tmpl.Execute(&result, m.templateVars); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	return result.String(), nil
}

// replaceConfigValue 替换配置值
func (m *Manager) replaceConfigValue(content, key, value string) (string, error) {
	// 解析层级键 (例如: "spring.redis.host" -> ["spring", "redis", "host"])
	keyParts := strings.Split(key, ".")
	
	if len(keyParts) == 1 {
		// 简单键，使用原来的逻辑
		return m.replaceSimpleKey(content, key, value)
	}
	
	// 处理层级键
	return m.replaceNestedKey(content, keyParts, value)
}

// replaceSimpleKey 替换简单键（非层级）
func (m *Manager) replaceSimpleKey(content, key, value string) (string, error) {
	// 尝试不同的YAML格式匹配
	patterns := []string{
		// 标准格式: key: value
		fmt.Sprintf(`^(\s*)%s:\s*(.*)$`, regexp.QuoteMeta(key)),
		// 带引号的格式: key: "value"
		fmt.Sprintf(`^(\s*)%s:\s*"[^"]*"(.*)$`, regexp.QuoteMeta(key)),
		// 带单引号的格式: key: 'value'
		fmt.Sprintf(`^(\s*)%s:\s*'[^']*'(.*)$`, regexp.QuoteMeta(key)),
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		if regex.MatchString(content) {
			// 确定值的格式（是否需要引号）
			formattedValue := m.formatValue(value)
			replacement := fmt.Sprintf("${1}%s: %s${2}", key, formattedValue)
			
			result := regex.ReplaceAllStringFunc(content, func(match string) string {
				return regex.ReplaceAllString(match, replacement)
			})
			
			if result != content {
				return result, nil
			}
		}
	}

	// 如果没有找到匹配，尝试多行搜索
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if strings.HasPrefix(trimmed, key+":") {
			// 提取缩进
			indent := strings.TrimSuffix(line, trimmed)
			formattedValue := m.formatValue(value)
			lines[i] = fmt.Sprintf("%s%s: %s", indent, key, formattedValue)
			return strings.Join(lines, "\n"), nil
		}
	}

	return content, fmt.Errorf("未找到配置键: %s", key)
}

// replaceNestedKey 替换层级键
func (m *Manager) replaceNestedKey(content string, keyParts []string, value string) (string, error) {
	fmt.Printf("  🔍 查找层级键: %v\n", keyParts)
	lines := strings.Split(content, "\n")
	
	// 寻找层级结构
	currentLevel := 0
	targetIndents := make([]int, len(keyParts))
	found := false
	
	for i, line := range lines {
		// 计算当前行的缩进
		indent := len(line) - len(strings.TrimLeft(line, " \t"))
		trimmed := strings.TrimSpace(line)
		
		// 跳过空行和注释
		if trimmed == "" || strings.HasPrefix(trimmed, "#") {
			continue
		}
		
		// 检查是否匹配当前层级的键
		if currentLevel < len(keyParts) {
			expectedKey := keyParts[currentLevel] + ":"
			if strings.HasPrefix(trimmed, expectedKey) {
				fmt.Printf("    ✅ 找到层级 %d: %s (缩进: %d)\n", currentLevel, keyParts[currentLevel], indent)
				targetIndents[currentLevel] = indent
				currentLevel++
				
				// 如果是最后一级，直接替换值
				if currentLevel == len(keyParts) {
					// 提取键和值的部分
					colonIndex := strings.Index(trimmed, ":")
					if colonIndex != -1 {
						keyPart := trimmed[:colonIndex]
						formattedValue := m.formatValue(value)
						
						fmt.Printf("    🔄 替换值: %s -> %s\n", trimmed, formattedValue)
						
						// 构建新行
						lineIndent := strings.Repeat(" ", indent)
						lines[i] = fmt.Sprintf("%s%s: %s", lineIndent, keyPart, formattedValue)
						found = true
						break
					}
				}
			} else if currentLevel > 0 && indent <= targetIndents[currentLevel-1] {
				// 如果缩进回到了更浅的层级，重置搜索
				fmt.Printf("    ↩️  重置搜索，缩进回退: %d -> %d\n", indent, targetIndents[currentLevel-1])
				currentLevel = 0
				// 重新检查当前行
				if strings.HasPrefix(trimmed, keyParts[0]+":") {
					fmt.Printf("    ✅ 重新找到层级 0: %s\n", keyParts[0])
					targetIndents[0] = indent
					currentLevel = 1
				}
			}
		}
	}
	
	if !found {
		fmt.Printf("    ❌ 未找到完整的层级键路径\n")
		
		// 提供详细的错误信息和建议
		if currentLevel == 0 {
			return "", fmt.Errorf("未找到配置键的根节点 '%s'\n    💡 建议: 检查配置文件中是否存在该根节点", keyParts[0])
		} else {
			return "", fmt.Errorf("未找到配置键: %s\n    💡 找到了 %d 级，但缺少: %s\n    💡 建议: 检查配置文件中是否存在完整的键路径", 
				strings.Join(keyParts, "."), currentLevel, strings.Join(keyParts[currentLevel:], "."))
		}
	}
	
	return strings.Join(lines, "\n"), nil
}

// formatValue 格式化值（确定是否需要引号）
func (m *Manager) formatValue(value string) string {
	// 如果值包含特殊字符，加引号
	if strings.Contains(value, " ") || 
	   strings.Contains(value, ":") || 
	   strings.Contains(value, "/") ||
	   strings.Contains(value, "\\") {
		// 转义内部引号
		escaped := strings.ReplaceAll(value, "\"", "\\\"")
		return fmt.Sprintf("\"%s\"", escaped)
	}
	return value
}

// ValidateConfig 验证配置文件语法
func (m *Manager) ValidateConfig() error {
	configPaths := m.getConfigPaths()
	
	// 验证每个配置文件
	for _, configPath := range configPaths {
		// 规范化路径
		cleanPath := filepath.Clean(configPath)
		
		// 读取修改后的配置文件
		content, err := ioutil.ReadFile(cleanPath)
		if err != nil {
			return fmt.Errorf("读取配置文件失败 [%s]: %v", cleanPath, err)
		}

		// 尝试解析YAML
		var yamlData interface{}
		if err := yaml.Unmarshal(content, &yamlData); err != nil {
			return fmt.Errorf("YAML语法错误 [%s]: %v", cleanPath, err)
		}
		
		fmt.Printf("✅ 配置文件语法验证通过: %s\n", cleanPath)
	}

	return nil
}

// RestoreOriginalConfig 恢复原始配置
func (m *Manager) RestoreOriginalConfig() error {
	if len(m.configFiles) == 0 {
		return fmt.Errorf("没有备份文件可恢复")
	}

	// 恢复每个配置文件
	for _, configPath := range m.configFiles {
		if err := m.backupManager.RestoreFile(configPath); err != nil {
			return fmt.Errorf("恢复配置文件失败 [%s]: %v", configPath, err)
		}
	}

	return nil
}

// GetConfigDiff 获取配置差异
func (m *Manager) GetConfigDiff() (string, error) {
	if len(m.originalContents) == 0 {
		return "", fmt.Errorf("没有原始配置内容")
	}

	var diff strings.Builder
	configPaths := m.getConfigPaths()
	
	for _, configPath := range configPaths {
		originalContent, exists := m.originalContents[configPath]
		if !exists {
			continue
		}

		currentContent, err := ioutil.ReadFile(configPath)
		if err != nil {
			diff.WriteString(fmt.Sprintf("❌ 读取当前配置文件失败 [%s]: %v\n", configPath, err))
			continue
		}

		// 简单的差异对比
		originalLines := strings.Split(originalContent, "\n")
		currentLines := strings.Split(string(currentContent), "\n")

		diff.WriteString(fmt.Sprintf("📄 配置文件: %s\n", configPath))
		diff.WriteString("================\n")

		for i, originalLine := range originalLines {
			if i < len(currentLines) && originalLine != currentLines[i] {
				diff.WriteString(fmt.Sprintf("- %s\n", originalLine))
				diff.WriteString(fmt.Sprintf("+ %s\n", currentLines[i]))
			}
		}
		diff.WriteString("\n")
	}

	return diff.String(), nil
}

// copyFile 复制文件
func (m *Manager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// Cleanup 清理（可选择恢复原始配置）
func (m *Manager) Cleanup(restore bool) error {
	if restore {
		return m.RestoreOriginalConfig()
	}
	return nil
} 