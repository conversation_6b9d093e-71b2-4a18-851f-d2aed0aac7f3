package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"

	"github.com/addx/iot-debug-setup/internal/types"
)

// Loader 配置加载器
type Loader struct {
	configPath string
	config     *types.Config
}

// NewLoader 创建配置加载器
func NewLoader(configPath string) *Loader {
	return &Loader{
		configPath: configPath,
	}
}

// LoadAndValidate 加载和验证配置文件
func (l *Loader) LoadAndValidate() (*types.Config, error) {
	// 检查文件是否存在
	if err := l.checkFileExists(); err != nil {
		return nil, err
	}

	// 获取绝对路径
	absPath, err := l.getAbsolutePath()
	if err != nil {
		return nil, err
	}
	l.configPath = absPath

	// 读取配置文件
	if err := l.loadConfig(); err != nil {
		return nil, err
	}

	// 验证配置
	if err := l.validateConfig(); err != nil {
		return nil, err
	}

	return l.config, nil
}

// checkFileExists 检查文件是否存在
func (l *Loader) checkFileExists() error {
	if _, err := os.Stat(l.configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", l.configPath)
	}
	return nil
}

// getAbsolutePath 获取绝对路径
func (l *Loader) getAbsolutePath() (string, error) {
	absPath, err := filepath.Abs(l.configPath)
	if err != nil {
		return "", fmt.Errorf("无法获取配置文件绝对路径: %v", err)
	}
	return absPath, nil
}

// loadConfig 加载配置文件
func (l *Loader) loadConfig() error {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	l.config = &types.Config{}
	if err := yaml.Unmarshal(data, l.config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	return nil
}

// validateConfig 验证配置
func (l *Loader) validateConfig() error {
	if l.config == nil {
		return fmt.Errorf("配置为空")
	}

	// 验证目标设备配置
	if err := l.validateTargetDevice(); err != nil {
		return fmt.Errorf("目标设备配置错误: %v", err)
	}

	// 验证本地机器配置
	if err := l.validateLocalMachine(); err != nil {
		return fmt.Errorf("本地机器配置错误: %v", err)
	}

	// 验证文件共享配置
	if err := l.validateFileSharing(); err != nil {
		return fmt.Errorf("文件共享配置错误: %v", err)
	}

	return nil
}

// validateTargetDevice 验证目标设备配置
func (l *Loader) validateTargetDevice() error {
	if l.config.TargetDevice.IP == "" {
		return fmt.Errorf("目标设备IP地址不能为空")
	}

	if l.config.TargetDevice.SSH.Username == "" {
		return fmt.Errorf("SSH用户名不能为空")
	}

	if l.config.TargetDevice.SSH.Port == 0 {
		l.config.TargetDevice.SSH.Port = 22 // 默认SSH端口
	}

	if l.config.TargetDevice.SSH.Password == "" && l.config.TargetDevice.SSH.KeyFile == "" {
		return fmt.Errorf("SSH密码或密钥文件至少需要提供一个")
	}

	return nil
}

// validateLocalMachine 验证本地机器配置
func (l *Loader) validateLocalMachine() error {
	if l.config.LocalMachine.IP == "" {
		return fmt.Errorf("本地机器IP地址不能为空")
	}

	if l.config.LocalMachine.MountPoint == "" {
		return fmt.Errorf("挂载点不能为空")
	}

	return nil
}

// validateFileSharing 验证文件共享配置
func (l *Loader) validateFileSharing() error {
	if l.config.FileSharing.Type == "" {
		return fmt.Errorf("文件共享类型不能为空")
	}

	validTypes := []string{"samba", "nfs", "sshfs"}
	validType := false
	for _, t := range validTypes {
		if l.config.FileSharing.Type == t {
			validType = true
			break
		}
	}

	if !validType {
		return fmt.Errorf("不支持的文件共享类型: %s，支持的类型: %v", l.config.FileSharing.Type, validTypes)
	}

	if len(l.config.FileSharing.Directories) == 0 {
		return fmt.Errorf("至少需要配置一个共享目录")
	}

	for i, dir := range l.config.FileSharing.Directories {
		if dir.Source == "" {
			return fmt.Errorf("第%d个共享目录的源路径不能为空", i+1)
		}
		if dir.ShareName == "" {
			return fmt.Errorf("第%d个共享目录的共享名称不能为空", i+1)
		}
	}

	return nil
}

// GetConfig 获取配置
func (l *Loader) GetConfig() *types.Config {
	return l.config
}

// GetConfigPath 获取配置文件路径
func (l *Loader) GetConfigPath() string {
	return l.configPath
}

// LoadConfigFromPath 从路径加载配置的便捷方法
func LoadConfigFromPath(configPath string) (*types.Config, error) {
	loader := NewLoader(configPath)
	return loader.LoadAndValidate()
}

// ConfigValidator 配置验证器接口
type ConfigValidator interface {
	Validate(config *types.Config) error
}

// DefaultValidator 默认验证器
type DefaultValidator struct{}

// Validate 验证配置
func (v *DefaultValidator) Validate(config *types.Config) error {
	loader := &Loader{config: config}
	return loader.validateConfig()
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid  bool
	Errors []string
}

// ValidateWithResult 验证配置并返回详细结果
func (l *Loader) ValidateWithResult() ValidationResult {
	err := l.validateConfig()
	if err != nil {
		return ValidationResult{
			Valid:  false,
			Errors: []string{err.Error()},
		}
	}
	return ValidationResult{
		Valid:  true,
		Errors: []string{},
	}
}

// PrintValidationResult 打印验证结果
func (l *Loader) PrintValidationResult(result ValidationResult) {
	if result.Valid {
		fmt.Println("✅ 配置文件验证通过")
	} else {
		fmt.Println("❌ 配置文件验证失败:")
		for _, err := range result.Errors {
			fmt.Printf("  • %s\n", err)
		}
	}
} 