package nfs

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/addx/iot-debug-setup/internal/service"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// Manager NFS管理器结构体
type Manager struct {
	config         *types.Config
	sshClient      *ssh.Client
	mounted        []string // 记录已挂载的路径，用于清理
	serviceManager service.ServiceManager
}

// NewManager 创建新的NFS管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config:  config,
		mounted: make([]string, 0),
	}
}

// SetSSHClient 设置SSH客户端
func (m *Manager) SetSSHClient(client *ssh.Client) {
	m.sshClient = client
	m.serviceManager = service.NewLinuxServiceManager(client)
}

// SetupNFS 设置NFS
func (m *Manager) SetupNFS() error {
	// 确保SSH客户端已连接
	if m.sshClient == nil {
		return fmt.Errorf("SSH客户端未设置")
	}

	// 检查并安装NFS服务
	if err := m.ensureNFSServerInstalled(); err != nil {
		return fmt.Errorf("安装NFS服务失败: %v", err)
	}

	// 配置NFS导出
	if err := m.configureNFSExports(); err != nil {
		return fmt.Errorf("配置NFS导出失败: %v", err)
	}

	// 启动NFS服务
	if err := m.startNFSService(); err != nil {
		return fmt.Errorf("启动NFS服务失败: %v", err)
	}

	// 在本地挂载NFS
	if err := m.mountNFSOnLocal(); err != nil {
		return fmt.Errorf("挂载NFS失败: %v", err)
	}

	fmt.Println("✅ NFS设置完成")
	return nil
}

// ensureNFSServerInstalled 确保NFS服务器已安装
func (m *Manager) ensureNFSServerInstalled() error {
	fmt.Println("📦 检查NFS服务器安装状态...")

	// 检查NFS服务器是否已安装（多种检查方式）
	if m.isNFSServerInstalled() {
		fmt.Println("✅ NFS服务器已安装，跳过安装步骤")
		return nil
	}

	fmt.Println("📥 NFS服务器未安装，开始安装...")

	// 检测操作系统发行版
	distro := m.detectDistribution()
	fmt.Printf("🖥️  检测到操作系统: %s\n", distro)

	// 根据不同的发行版安装NFS服务器
	var installCmd string
	switch distro {
	case "ubuntu", "debian":
		fmt.Println("🔄 更新包管理器...")
		if _, err := m.sshClient.ExecuteCommand("apt-get update"); err != nil {
			fmt.Printf("⚠️  更新包管理器失败: %v\n", err)
		}
		installCmd = "apt-get install -y nfs-kernel-server nfs-common"
	case "centos", "rhel":
		installCmd = "yum install -y nfs-utils rpcbind"
	case "fedora":
		installCmd = "dnf install -y nfs-utils rpcbind"
	case "alpine":
		installCmd = "apk add nfs-utils"
	default:
		// 尝试通用的安装命令
		fmt.Println("⚠️  未知的操作系统，尝试通用安装方法...")
		commands := []string{
			"apt-get update && apt-get install -y nfs-kernel-server nfs-common",
			"yum install -y nfs-utils rpcbind",
			"dnf install -y nfs-utils rpcbind",
			"apk add nfs-utils",
		}
		
		var lastErr error
		for _, cmd := range commands {
			fmt.Printf("🔄 尝试安装命令: %s\n", cmd)
			if _, err := m.sshClient.ExecuteCommand(cmd); err == nil {
				fmt.Println("✅ NFS服务器安装成功")
				break
			} else {
				fmt.Printf("⚠️  命令失败: %v\n", err)
				lastErr = err
			}
		}
		
		if lastErr != nil {
			return fmt.Errorf("无法安装NFS服务器: %v", lastErr)
		}
	}

	if installCmd != "" {
		fmt.Printf("📦 执行安装命令: %s\n", installCmd)
		if _, err := m.sshClient.ExecuteCommand(installCmd); err != nil {
			return fmt.Errorf("安装NFS服务器失败: %v", err)
		}
	}

	// 验证安装结果
	if m.isNFSServerInstalled() {
		fmt.Println("✅ NFS服务器安装完成并验证成功")
	} else {
		fmt.Println("⚠️  NFS服务器安装可能未完全成功，但将继续尝试配置")
	}

	return nil
}

// isNFSServerInstalled 检查NFS服务器是否已安装
func (m *Manager) isNFSServerInstalled() bool {
	// 方法1：检查exportfs命令是否存在
	if output, err := m.sshClient.ExecuteCommand("which exportfs"); err == nil {
		fmt.Printf("  🔍 找到exportfs: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法2：检查/usr/sbin/exportfs文件是否存在
	if _, err := m.sshClient.ExecuteCommand("test -f /usr/sbin/exportfs"); err == nil {
		fmt.Printf("  🔍 找到exportfs文件: /usr/sbin/exportfs\n")
		return true
	}

	// 方法3：检查nfs-utils包是否已安装
	if output, err := m.sshClient.ExecuteCommand("dpkg -l | grep -E '^ii.*nfs-kernel-server'"); err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("  🔍 找到nfs-kernel-server包: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法4：检查nfs-utils包（CentOS/RHEL）
	if output, err := m.sshClient.ExecuteCommand("rpm -qa | grep nfs-utils"); err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("  🔍 找到nfs-utils包: %s\n", strings.TrimSpace(output))
		return true
	}

	// 方法5：检查systemctl中是否有nfs服务
	if output, err := m.sshClient.ExecuteCommand("systemctl list-unit-files | grep -E '^nfs-|^rpc'"); err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("  🔍 找到nfs相关服务: %s\n", strings.TrimSpace(output))
		return true
	}

	fmt.Println("  ❌ 未找到NFS服务器安装")
	return false
}

// detectDistribution 检测操作系统发行版
func (m *Manager) detectDistribution() string {
	// 检查/etc/os-release文件
	if output, err := m.sshClient.ExecuteCommand("cat /etc/os-release | grep '^ID=' | cut -d'=' -f2 | tr -d '\"'"); err == nil {
		distro := strings.TrimSpace(strings.ToLower(output))
		if distro != "" {
			return distro
		}
	}

	// 检查lsb_release命令
	if output, err := m.sshClient.ExecuteCommand("lsb_release -si 2>/dev/null"); err == nil {
		distro := strings.TrimSpace(strings.ToLower(output))
		if distro != "" {
			return distro
		}
	}

	// 检查特定的发行版文件
	distroFiles := map[string]string{
		"ubuntu":  "/etc/lsb-release",
		"debian":  "/etc/debian_version",
		"centos":  "/etc/centos-release",
		"fedora":  "/etc/fedora-release",
		"alpine":  "/etc/alpine-release",
	}

	for distro, file := range distroFiles {
		if _, err := m.sshClient.ExecuteCommand(fmt.Sprintf("test -f %s", file)); err == nil {
			return distro
		}
	}

	return "unknown"
}

// configureNFSExports 配置NFS导出
func (m *Manager) configureNFSExports() error {
	fmt.Println("📝 配置NFS导出...")

	// 创建导出配置
	var exportLines []string
	for _, dir := range m.config.NFSExports.Directories {
		// 确保目录存在
		if !m.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("📁 创建目录: %s\n", dir.Source)
			if err := m.sshClient.CreateDirectory(dir.Source); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", dir.Source, err)
			}
		}

		// 添加导出行
		exportLine := fmt.Sprintf("%s %s(%s)",
			dir.Source,
			m.config.LocalMachine.IP,
			dir.Options)
		exportLines = append(exportLines, exportLine)
	}

	// 备份原有的exports文件
	_, _ = m.sshClient.ExecuteCommand("cp /etc/exports /etc/exports.bak.$(date +%Y%m%d_%H%M%S)")

	// 写入新的exports配置
	exportsContent := strings.Join(exportLines, "\n")
	writeCmd := fmt.Sprintf("echo '%s' > /etc/exports", exportsContent)
	if _, err := m.sshClient.ExecuteCommand(writeCmd); err != nil {
		return fmt.Errorf("写入exports配置失败: %v", err)
	}

	fmt.Println("✅ NFS导出配置完成")
	return nil
}

// startNFSService 启动NFS服务
func (m *Manager) startNFSService() error {
	fmt.Println("🚀 启动NFS服务...")

	// 按依赖顺序启动服务
	if err := m.startDependencyServices(); err != nil {
		return fmt.Errorf("启动依赖服务失败: %v", err)
	}

	// 启动NFS主服务
	if err := m.startMainNFSService(); err != nil {
		return fmt.Errorf("启动NFS主服务失败: %v", err)
	}

	// 重新加载exports配置
	if _, err := m.sshClient.ExecuteCommand("exportfs -r"); err != nil {
		fmt.Printf("❌ 重新加载exports配置失败: %v\n", err)
		fmt.Println("⚠️  NFS服务已启动，但导出配置有问题")
		return fmt.Errorf("导出配置失败: %v", err)
	}

	fmt.Println("✅ NFS服务启动成功")
	return nil
}

// startDependencyServices 启动依赖服务
func (m *Manager) startDependencyServices() error {
	fmt.Println("🔧 启动依赖服务...")

	// 启动rpcbind服务（NFS的核心依赖）
	if err := m.startAndEnableService("rpcbind"); err != nil {
		return fmt.Errorf("启动rpcbind服务失败: %v", err)
	}

	// 检查rpcbind是否正常运行
	if _, err := m.sshClient.ExecuteCommand("systemctl is-active rpcbind"); err != nil {
		return fmt.Errorf("rpcbind服务未正常运行: %v", err)
	}

	fmt.Println("✅ 依赖服务启动成功")
	return nil
}

// startMainNFSService 启动NFS主服务
func (m *Manager) startMainNFSService() error {
	fmt.Println("🚀 启动NFS主服务...")

	// 尝试启动的NFS服务列表（不同发行版可能有不同的服务名）
	nfsServices := []string{
		"nfs-utils",        // 一些ARM系统使用此服务名
		"nfs-server",
		"nfs-kernel-server",
		"nfs",
	}

	var lastError error
	for _, service := range nfsServices {
		fmt.Printf("🔄 尝试启动服务: %s\n", service)
		
		// 检查服务是否存在
		if _, err := m.sshClient.ExecuteCommand(fmt.Sprintf("systemctl list-unit-files | grep -q %s", service)); err != nil {
			fmt.Printf("ℹ️  服务不存在: %s\n", service)
			continue
		}

		// 尝试启动服务
		if err := m.startAndEnableService(service); err != nil {
			fmt.Printf("⚠️  启动服务失败 %s: %v\n", service, err)
			lastError = err
			continue
		}

		// 检查服务是否正常运行
		if _, err := m.sshClient.ExecuteCommand(fmt.Sprintf("systemctl is-active %s", service)); err != nil {
			fmt.Printf("⚠️  服务未正常运行 %s: %v\n", service, err)
			lastError = err
			continue
		}

		fmt.Printf("✅ 成功启动服务: %s\n", service)
		return nil
	}

	return fmt.Errorf("所有NFS服务启动失败，最后错误: %v", lastError)
}

// startAndEnableService 启动并启用服务
func (m *Manager) startAndEnableService(serviceName string) error {
	// 使用抽象的服务管理器
	return m.serviceManager.StartAndEnableService(serviceName)
}

// 保持原有的启动服务逻辑作为备用
func (m *Manager) startNFSServiceLegacy() error {
	fmt.Println("🚀 启动NFS服务（传统方式）...")

	// 使用服务组管理器启动NFS服务
	nfsServiceGroup := service.CreateNFSServiceGroup(m.serviceManager)
	nfsServiceGroup.StartAll()

	// 重新加载exports
	if _, err := m.sshClient.ExecuteCommand("exportfs -ra"); err != nil {
		return fmt.Errorf("重新加载exports失败: %v", err)
	}

	// 验证导出状态
	output, err := m.sshClient.ExecuteCommand("exportfs -v")
	if err != nil {
		return fmt.Errorf("验证导出状态失败: %v", err)
	}

	fmt.Printf("📋 当前NFS导出状态:\n%s\n", output)
	fmt.Println("✅ NFS服务启动成功")
	return nil
}

// DiagnoseNFSIssues 诊断NFS问题
func (m *Manager) DiagnoseNFSIssues() error {
	fmt.Println("🔍 开始诊断NFS问题...")

	// 检查系统信息
	if output, err := m.sshClient.ExecuteCommand("uname -a"); err == nil {
		fmt.Printf("🖥️  系统信息: %s\n", output)
	}

	// 检查NFS相关包是否安装
	fmt.Println("📦 检查NFS相关包安装状态...")
	packages := []string{"nfs-kernel-server", "nfs-common", "rpcbind"}
	for _, pkg := range packages {
		if output, err := m.sshClient.ExecuteCommand(fmt.Sprintf("dpkg -l | grep %s || rpm -qa | grep %s", pkg, pkg)); err == nil {
			fmt.Printf("  %s: %s\n", pkg, output)
		}
	}

	// 检查服务状态
	fmt.Println("🔧 检查服务状态...")
	services := []string{"rpcbind", "nfs-server", "nfs-kernel-server", "nfs-utils", "nfs"}
	for _, service := range services {
		if output, err := m.sshClient.ExecuteCommand(fmt.Sprintf("systemctl status %s", service)); err == nil {
			fmt.Printf("  %s状态:\n%s\n", service, output)
		} else {
			fmt.Printf("  %s状态: ❌ 命令执行失败或服务不存在\n", service)
		}
		
		// 检查服务是否active
		if output, err := m.sshClient.ExecuteCommand(fmt.Sprintf("systemctl is-active %s", service)); err == nil {
			fmt.Printf("  %s是否运行: %s\n", service, output)
		}
	}

	// 检查端口占用
	fmt.Println("🔌 检查端口占用...")
	ports := []string{"111", "2049"}
	for _, port := range ports {
		if output, err := m.sshClient.ExecuteCommand(fmt.Sprintf("netstat -tulpn | grep :%s", port)); err == nil {
			fmt.Printf("  端口%s: %s\n", port, output)
		}
	}

	// 检查防火墙
	fmt.Println("🔥 检查防火墙状态...")
	if output, err := m.sshClient.ExecuteCommand("ufw status || iptables -L"); err == nil {
		fmt.Printf("  防火墙状态:\n%s\n", output)
	}

	// 检查/etc/exports文件
	fmt.Println("📁 检查/etc/exports文件...")
	if output, err := m.sshClient.ExecuteCommand("cat /etc/exports"); err == nil {
		fmt.Printf("  /etc/exports内容:\n%s\n", output)
	}

	// 检查内核模块
	fmt.Println("🔌 检查NFS内核模块...")
	if output, err := m.sshClient.ExecuteCommand("lsmod | grep -E '(nfs|nfsd)'"); err == nil {
		if output != "" {
			fmt.Printf("  已加载的NFS模块:\n%s\n", output)
		} else {
			fmt.Println("  ⚠️  没有发现已加载的NFS模块")
			// 尝试加载模块
			fmt.Println("  🔧 尝试加载NFS模块...")
			m.sshClient.ExecuteCommand("modprobe nfsd")
			m.sshClient.ExecuteCommand("modprobe exportfs")
			if output2, err2 := m.sshClient.ExecuteCommand("lsmod | grep -E '(nfs|nfsd)'"); err2 == nil && output2 != "" {
				fmt.Printf("  ✅ 成功加载NFS模块:\n%s\n", output2)
			} else {
				fmt.Println("  ❌ 无法加载NFS模块，可能内核不支持NFS服务器")
			}
		}
	}

	// 检查内核文件系统支持
	fmt.Println("🗂️  检查内核文件系统支持...")
	if output, err := m.sshClient.ExecuteCommand("cat /proc/filesystems | grep nfs"); err == nil {
		if output != "" {
			fmt.Printf("  NFS文件系统支持:\n%s\n", output)
		} else {
			fmt.Println("  ❌ 内核不支持NFS文件系统")
		}
	}

	// 检查导出状态
	fmt.Println("📤 检查导出状态...")
	if output, err := m.sshClient.ExecuteCommand("exportfs -v"); err == nil {
		fmt.Printf("  导出状态:\n%s\n", output)
	} else {
		fmt.Printf("  ❌ 导出状态检查失败: %v\n", err)
		
		// 尝试重新导出
		fmt.Println("  🔧 尝试重新导出...")
		if _, err2 := m.sshClient.ExecuteCommand("exportfs -r"); err2 == nil {
			fmt.Println("  ✅ 重新导出成功")
		} else {
			fmt.Printf("  ❌ 重新导出失败: %v\n", err2)
		}
	}

	// 检查文件系统类型
	fmt.Println("🗄️  检查导出目录文件系统类型...")
	directories := []string{"/data/sqlite", "/data/iot-service", "/tmp/ssd", "/addx/temp", "/addx/firmware"}
	for _, dir := range directories {
		if output, err := m.sshClient.ExecuteCommand(fmt.Sprintf("df -T %s", dir)); err == nil {
			fmt.Printf("  %s: %s", dir, output)
		}
	}

	// 检查内核配置
	fmt.Println("⚙️  检查内核NFS配置...")
	if output, err := m.sshClient.ExecuteCommand("zcat /proc/config.gz | grep -E '(CONFIG_NFS|CONFIG_NFSD)' 2>/dev/null"); err == nil && output != "" {
		fmt.Printf("  内核NFS配置:\n%s\n", output)
	} else {
		fmt.Println("  ⚠️  无法获取内核配置信息")
		
		// 尝试从模块目录检查
		if output2, err2 := m.sshClient.ExecuteCommand("find /lib/modules/$(uname -r) -name '*nfs*' -type f | head -10"); err2 == nil && output2 != "" {
			fmt.Printf("  可用的NFS模块文件:\n%s\n", output2)
		}
	}

	// 检查日志
	fmt.Println("📋 检查相关日志...")
	if output, err := m.sshClient.ExecuteCommand("journalctl -u nfs-server -u nfs-utils -u rpcbind --no-pager -l --since '10 minutes ago'"); err == nil {
		fmt.Printf("  最近10分钟日志:\n%s\n", output)
	}

	// 检查dmesg中的NFS相关信息
	fmt.Println("🔍 检查系统消息中的NFS信息...")
	if output, err := m.sshClient.ExecuteCommand("dmesg | grep -i nfs | tail -10"); err == nil && output != "" {
		fmt.Printf("  系统消息中的NFS信息:\n%s\n", output)
	}

	return nil
}

// mountNFSOnLocal 在本地挂载NFS
func (m *Manager) mountNFSOnLocal() error {
	fmt.Println("🔗 在本地挂载NFS...")

	// 创建挂载点
	mountPoint := m.config.LocalMachine.NFSMountPoint
	if err := m.createMountPoint(mountPoint); err != nil {
		return fmt.Errorf("创建挂载点失败: %v", err)
	}

	// 检查本地NFS客户端
	if err := m.ensureNFSClientInstalled(); err != nil {
		return fmt.Errorf("安装NFS客户端失败: %v", err)
	}

	// 挂载各个目录
	for _, dir := range m.config.NFSExports.Directories {
		localPath := filepath.Join(mountPoint, strings.TrimPrefix(dir.ExportPath, "/"))
		
		// 创建本地目录
		if err := m.createMountPoint(localPath); err != nil {
			return fmt.Errorf("创建本地目录失败 %s: %v", localPath, err)
		}

		// 挂载NFS
		if err := m.mountSingleNFS(dir.ExportPath, localPath); err != nil {
			return fmt.Errorf("挂载NFS失败 %s: %v", localPath, err)
		}

		m.mounted = append(m.mounted, localPath)
		fmt.Printf("✅ 挂载成功: %s -> %s\n", dir.ExportPath, localPath)
	}

	return nil
}

// createMountPoint 创建挂载点
func (m *Manager) createMountPoint(path string) error {
	if err := os.MkdirAll(path, 0755); err != nil {
		return fmt.Errorf("创建目录失败 %s: %v", path, err)
	}
	return nil
}

// ensureNFSClientInstalled 确保NFS客户端已安装
func (m *Manager) ensureNFSClientInstalled() error {
	var installCmd string
	
	switch runtime.GOOS {
	case "linux":
		// 检查是否已安装
		if _, err := exec.LookPath("mount.nfs"); err == nil {
			return nil
		}
		
		// 尝试安装
		installCmd = "sudo apt-get install -y nfs-common || sudo yum install -y nfs-utils || sudo dnf install -y nfs-utils"
	case "darwin":
		// macOS通常已经内置NFS支持
		return nil
	case "windows":
		// Windows需要启用NFS客户端功能
		fmt.Println("⚠️  Windows需要手动启用NFS客户端功能")
		return nil
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	if installCmd != "" {
		fmt.Println("📦 安装NFS客户端...")
		if err := m.executeLocalCommand(installCmd); err != nil {
			return fmt.Errorf("安装NFS客户端失败: %v", err)
		}
	}

	return nil
}

// mountSingleNFS 挂载单个NFS
func (m *Manager) mountSingleNFS(remotePath, localPath string) error {
	// 首先检查是否已经挂载
	if m.isMounted(localPath) {
		fmt.Printf("📌 %s 已经挂载\n", localPath)
		return nil
	}

	// 构建挂载命令
	var mountCmd string
	nfsServer := fmt.Sprintf("%s:%s", m.config.TargetDevice.IP, remotePath)
	
	switch runtime.GOOS {
	case "linux":
		mountCmd = fmt.Sprintf("sudo mount -t nfs %s %s", nfsServer, localPath)
	case "darwin":
		mountCmd = fmt.Sprintf("sudo mount -t nfs %s %s", nfsServer, localPath)
	case "windows":
		// Windows NFS挂载命令
		mountCmd = fmt.Sprintf("mount -t nfs %s %s", nfsServer, localPath)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	// 执行挂载命令
	if err := m.executeLocalCommand(mountCmd); err != nil {
		return fmt.Errorf("挂载命令执行失败: %v", err)
	}

	return nil
}

// isMounted 检查是否已挂载
func (m *Manager) isMounted(path string) bool {
	var checkCmd string
	
	switch runtime.GOOS {
	case "linux", "darwin":
		checkCmd = fmt.Sprintf("mount | grep %s", path)
	case "windows":
		checkCmd = fmt.Sprintf("mount | findstr %s", path)
	default:
		return false
	}

	err := m.executeLocalCommand(checkCmd)
	return err == nil
}

// executeLocalCommand 执行本地命令
func (m *Manager) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

// Cleanup 清理NFS挂载
func (m *Manager) Cleanup() error {
	fmt.Println("🧹 清理NFS挂载...")

	// 卸载所有挂载的NFS
	for _, mountPath := range m.mounted {
		if err := m.unmountNFS(mountPath); err != nil {
			fmt.Printf("⚠️  卸载失败 %s: %v\n", mountPath, err)
		} else {
			fmt.Printf("✅ 卸载成功: %s\n", mountPath)
		}
	}

	// 清理挂载记录
	m.mounted = m.mounted[:0]

	return nil
}

// unmountNFS 卸载NFS
func (m *Manager) unmountNFS(path string) error {
	var unmountCmd string
	
	switch runtime.GOOS {
	case "linux", "darwin":
		unmountCmd = fmt.Sprintf("sudo umount %s", path)
	case "windows":
		unmountCmd = fmt.Sprintf("umount %s", path)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	return m.executeLocalCommand(unmountCmd)
} 