package connection

import (
	"fmt"
	"sync"
	"time"

	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// ConnectionStatus 连接状态
type ConnectionStatus int

const (
	StatusDisconnected ConnectionStatus = iota
	StatusConnecting
	StatusConnected
	StatusError
)

// String 返回状态字符串
func (s ConnectionStatus) String() string {
	switch s {
	case StatusDisconnected:
		return "断开连接"
	case StatusConnecting:
		return "正在连接"
	case StatusConnected:
		return "已连接"
	case StatusError:
		return "连接错误"
	default:
		return "未知状态"
	}
}

// ConnectionEvent 连接事件
type ConnectionEvent struct {
	Type      string
	Status    ConnectionStatus
	Error     error
	Timestamp time.Time
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	MaxRetries      int
	RetryDelay      time.Duration
	HealthCheckInterval time.Duration
	AutoReconnect   bool
}

// DefaultConnectionConfig 默认连接配置
func DefaultConnectionConfig() *ConnectionConfig {
	return &ConnectionConfig{
		MaxRetries:      3,
		RetryDelay:      5 * time.Second,
		HealthCheckInterval: 30 * time.Second,
		AutoReconnect:   true,
	}
}

// Manager 连接管理器
type Manager struct {
	config       *types.Config
	connConfig   *ConnectionConfig
	sshClient    *ssh.Client
	status       ConnectionStatus
	lastError    error
	mu           sync.RWMutex
	eventChan    chan ConnectionEvent
	stopChan     chan struct{}
	healthTicker *time.Ticker
	retryCount   int
}

// NewManager 创建连接管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config:     config,
		connConfig: DefaultConnectionConfig(),
		status:     StatusDisconnected,
		eventChan:  make(chan ConnectionEvent, 100),
		stopChan:   make(chan struct{}),
		retryCount: 0,
	}
}

// NewManagerWithConfig 使用自定义配置创建连接管理器
func NewManagerWithConfig(config *types.Config, connConfig *ConnectionConfig) *Manager {
	return &Manager{
		config:     config,
		connConfig: connConfig,
		status:     StatusDisconnected,
		eventChan:  make(chan ConnectionEvent, 100),
		stopChan:   make(chan struct{}),
		retryCount: 0,
	}
}

// Connect 连接到远程服务器
func (m *Manager) Connect() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.status == StatusConnected {
		return nil
	}

	m.setStatus(StatusConnecting)
	m.publishEvent("connecting", StatusConnecting, nil)

	// 创建SSH客户端
	m.sshClient = ssh.NewClient(m.config)

	// 尝试连接
	err := m.connectWithRetry()
	if err != nil {
		m.setStatusWithError(StatusError, err)
		m.publishEvent("connect_failed", StatusError, err)
		return err
	}

	m.setStatus(StatusConnected)
	m.publishEvent("connected", StatusConnected, nil)
	m.retryCount = 0

	// 启动健康检查
	if m.connConfig.AutoReconnect {
		m.startHealthCheck()
	}

	return nil
}

// connectWithRetry 带重试的连接
func (m *Manager) connectWithRetry() error {
	var lastErr error
	
	for i := 0; i <= m.connConfig.MaxRetries; i++ {
		err := m.sshClient.Connect()
		if err == nil {
			return nil
		}
		
		lastErr = err
		m.retryCount = i
		
		if i < m.connConfig.MaxRetries {
			m.publishEvent("retry", StatusConnecting, fmt.Errorf("连接失败，第%d次重试: %v", i+1, err))
			time.Sleep(m.connConfig.RetryDelay)
		}
	}
	
	return fmt.Errorf("连接失败，已重试%d次: %v", m.connConfig.MaxRetries, lastErr)
}

// Disconnect 断开连接
func (m *Manager) Disconnect() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.status == StatusDisconnected {
		return
	}

	// 停止健康检查
	m.stopHealthCheck()

	// 关闭SSH连接
	if m.sshClient != nil {
		m.sshClient.Close()
		m.sshClient = nil
	}

	m.setStatus(StatusDisconnected)
	m.publishEvent("disconnected", StatusDisconnected, nil)
}

// GetClient 获取SSH客户端
func (m *Manager) GetClient() *ssh.Client {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.sshClient
}

// GetStatus 获取连接状态
func (m *Manager) GetStatus() ConnectionStatus {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.status
}

// GetLastError 获取最后的错误
func (m *Manager) GetLastError() error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastError
}

// IsConnected 检查是否已连接
func (m *Manager) IsConnected() bool {
	return m.GetStatus() == StatusConnected
}

// GetEventChannel 获取事件通道
func (m *Manager) GetEventChannel() <-chan ConnectionEvent {
	return m.eventChan
}

// setStatus 设置状态
func (m *Manager) setStatus(status ConnectionStatus) {
	m.status = status
	m.lastError = nil
}

// setStatusWithError 设置状态和错误
func (m *Manager) setStatusWithError(status ConnectionStatus, err error) {
	m.status = status
	m.lastError = err
}

// publishEvent 发布事件
func (m *Manager) publishEvent(eventType string, status ConnectionStatus, err error) {
	select {
	case m.eventChan <- ConnectionEvent{
		Type:      eventType,
		Status:    status,
		Error:     err,
		Timestamp: time.Now(),
	}:
	default:
		// 事件通道已满，丢弃旧事件
	}
}

// startHealthCheck 启动健康检查
func (m *Manager) startHealthCheck() {
	if m.healthTicker != nil {
		m.healthTicker.Stop()
	}
	
	m.healthTicker = time.NewTicker(m.connConfig.HealthCheckInterval)
	
	go func() {
		for {
			select {
			case <-m.healthTicker.C:
				m.performHealthCheck()
			case <-m.stopChan:
				return
			}
		}
	}()
}

// stopHealthCheck 停止健康检查
func (m *Manager) stopHealthCheck() {
	if m.healthTicker != nil {
		m.healthTicker.Stop()
		m.healthTicker = nil
	}
	
	// 发送停止信号
	select {
	case m.stopChan <- struct{}{}:
	default:
	}
}

// performHealthCheck 执行健康检查
func (m *Manager) performHealthCheck() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.status != StatusConnected || m.sshClient == nil {
		return
	}
	
	// 执行简单的命令来检查连接状态
	_, err := m.sshClient.ExecuteCommand("echo 'health_check'")
	if err != nil {
		m.setStatusWithError(StatusError, err)
		m.publishEvent("health_check_failed", StatusError, err)
		
		// 尝试重连
		if m.connConfig.AutoReconnect {
			go m.attemptReconnect()
		}
	} else {
		m.publishEvent("health_check_ok", StatusConnected, nil)
	}
}

// attemptReconnect 尝试重连
func (m *Manager) attemptReconnect() {
	m.publishEvent("reconnecting", StatusConnecting, nil)
	
	// 关闭当前连接
	if m.sshClient != nil {
		m.sshClient.Close()
	}
	
	// 重新连接
	err := m.Connect()
	if err != nil {
		m.publishEvent("reconnect_failed", StatusError, err)
	} else {
		m.publishEvent("reconnected", StatusConnected, nil)
	}
}

// GetConnectionInfo 获取连接信息
func (m *Manager) GetConnectionInfo() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	info := map[string]interface{}{
		"status":      m.status.String(),
		"target_ip":   m.config.TargetDevice.IP,
		"target_port": m.config.TargetDevice.SSH.Port,
		"username":    m.config.TargetDevice.SSH.Username,
		"retry_count": m.retryCount,
	}
	
	if m.lastError != nil {
		info["last_error"] = m.lastError.Error()
	}
	
	return info
}

// ExecuteCommand 执行命令的便捷方法
func (m *Manager) ExecuteCommand(command string) (string, error) {
	if !m.IsConnected() {
		return "", fmt.Errorf("未连接到远程服务器")
	}
	
	client := m.GetClient()
	if client == nil {
		return "", fmt.Errorf("SSH客户端为空")
	}
	
	return client.ExecuteCommand(command)
}

// WithConnection 使用连接执行函数
func (m *Manager) WithConnection(fn func(*ssh.Client) error) error {
	if !m.IsConnected() {
		return fmt.Errorf("未连接到远程服务器")
	}
	
	client := m.GetClient()
	if client == nil {
		return fmt.Errorf("SSH客户端为空")
	}
	
	return fn(client)
}

// EventListener 事件监听器
type EventListener struct {
	manager  *Manager
	callback func(ConnectionEvent)
	stopChan chan struct{}
}

// NewEventListener 创建事件监听器
func NewEventListener(manager *Manager, callback func(ConnectionEvent)) *EventListener {
	return &EventListener{
		manager:  manager,
		callback: callback,
		stopChan: make(chan struct{}),
	}
}

// Start 开始监听事件
func (l *EventListener) Start() {
	go func() {
		for {
			select {
			case event := <-l.manager.GetEventChannel():
				l.callback(event)
			case <-l.stopChan:
				return
			}
		}
	}()
}

// Stop 停止监听事件
func (l *EventListener) Stop() {
	close(l.stopChan)
}

// ConnectionPool 连接池
type ConnectionPool struct {
	configs     map[string]*types.Config
	managers    map[string]*Manager
	mu          sync.RWMutex
}

// NewConnectionPool 创建连接池
func NewConnectionPool() *ConnectionPool {
	return &ConnectionPool{
		configs:  make(map[string]*types.Config),
		managers: make(map[string]*Manager),
	}
}

// AddConnection 添加连接
func (p *ConnectionPool) AddConnection(name string, config *types.Config) *Manager {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	p.configs[name] = config
	manager := NewManager(config)
	p.managers[name] = manager
	
	return manager
}

// GetConnection 获取连接
func (p *ConnectionPool) GetConnection(name string) (*Manager, bool) {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	manager, exists := p.managers[name]
	return manager, exists
}

// RemoveConnection 移除连接
func (p *ConnectionPool) RemoveConnection(name string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	if manager, exists := p.managers[name]; exists {
		manager.Disconnect()
		delete(p.managers, name)
		delete(p.configs, name)
	}
}

// CloseAll 关闭所有连接
func (p *ConnectionPool) CloseAll() {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	for name, manager := range p.managers {
		manager.Disconnect()
		delete(p.managers, name)
		delete(p.configs, name)
	}
} 