package app

import (
	"fmt"
	"io/ioutil"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"gopkg.in/yaml.v3"

	"github.com/addx/iot-debug-setup/internal/config"
	"github.com/addx/iot-debug-setup/internal/docker"
	"github.com/addx/iot-debug-setup/internal/errors"
	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/logger"
	"github.com/addx/iot-debug-setup/internal/port"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// DebugSetupApp 主应用程序结构体
type DebugSetupApp struct {
	config        *types.Config
	sshClient     *ssh.Client
	fileManager   fileshare.FileShareManager
	portManager   *port.Manager
	configManager *config.Manager
	dockerManager *docker.Manager
}

// NewDebugSetupApp 创建新的应用程序实例
func NewDebugSetupApp(configPath string) (*DebugSetupApp, error) {
	app := &DebugSetupApp{}
	
	// 加载配置文件
	if err := app.loadConfig(configPath); err != nil {
		return nil, errors.NewSetupError(
			errors.ErrorTypeConfig,
			"配置文件加载",
			"配置文件解析失败",
			err,
		)
	}

	// 初始化SSH客户端
	app.sshClient = ssh.NewClient(app.config)

	// 连接到远程服务器
	if err := app.sshClient.Connect(); err != nil {
		return nil, errors.NewSetupError(
			errors.ErrorTypeSSH,
			"SSH连接",
			"连接到远程录像机失败",
			err,
		)
	}

	// 创建文件共享管理器
	var err error
	app.fileManager, err = fileshare.CreateFileShareManager(app.config, app.sshClient)
	if err != nil {
		return nil, errors.NewSetupError(
			errors.ErrorTypeGeneric,
			"文件共享管理器创建",
			"文件共享管理器初始化失败",
			err,
		)
	}

	// 初始化其他管理器
	app.portManager = port.NewManager(app.config)
	app.configManager = config.NewManager(app.config)
	app.dockerManager = docker.NewManager(app.config, app.sshClient)

	return app, nil
}

// loadConfig 加载配置文件
func (app *DebugSetupApp) loadConfig(configPath string) error {
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	app.config = &types.Config{}
	if err := yaml.Unmarshal(data, app.config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	return nil
}

// Run 运行主程序
func (app *DebugSetupApp) Run() error {
	logger.StartupBanner("IoT调试环境设置工具", "1.0.0")
	logger.Info("目标设备: %s", app.config.TargetDevice.IP)
	logger.Info("本地机器: %s", app.config.LocalMachine.IP)
	logger.Info("操作系统: %s", runtime.GOOS)

	// 设置信号处理，用于优雅退出
	if app.config.ExecutionSteps.CleanupOnExit {
		app.setupSignalHandling()
	}

	// SSH连接已在NewDebugSetupApp中建立
	logger.Network("已连接到录像机: %s", app.config.TargetDevice.IP)
	defer app.sshClient.Close()

	// 设置文件共享
	if app.config.ExecutionSteps.SetupFileSharing {
		logger.Section(fmt.Sprintf("设置%s文件共享", app.fileManager.GetShareType()))
		
		// 设置远程服务器
		if err := app.fileManager.SetupServer(); err != nil {
			shareType := app.fileManager.GetShareType()
			return errors.NewSetupError(
				getFileShareErrorType(shareType),
				fmt.Sprintf("%s文件共享服务器设置", shareType),
				"远程服务器配置失败",
				err,
			)
		}
		
		// 在本地挂载
		if err := app.fileManager.MountOnLocal(); err != nil {
			shareType := app.fileManager.GetShareType()
			return errors.NewSetupError(
				getFileShareErrorType(shareType),
				fmt.Sprintf("%s文件共享本地挂载", shareType),
				"本地挂载失败",
				err,
			)
		}
	}

	// 设置端口转发
	if app.config.ExecutionSteps.SetupPortForwarding {
		logger.Section("设置端口转发")
		app.portManager.SetSSHClient(app.sshClient)
		if err := app.portManager.SetupPortForwarding(); err != nil {
			return errors.NewSetupError(
				errors.ErrorTypePortForwarding,
				"端口转发设置",
				"端口转发配置失败",
				err,
			)
		}
	}

	// 更新配置文件
	if app.config.ExecutionSteps.UpdateConfigFile {
		logger.Section("更新配置文件")
		if err := app.configManager.UpdateConfigFile(); err != nil {
			return errors.NewSetupError(
				errors.ErrorTypeConfig,
				"配置文件更新",
				"application.yml配置文件更新失败",
				err,
			)
		}
	}

	// 设置Docker容器控制
	if app.config.ExecutionSteps.SetupDockerControl {
		if err := app.dockerManager.SetupDockerControl(); err != nil {
			return errors.NewSetupError(
				errors.ErrorTypeDocker,
				"Docker容器控制",
				"Docker容器启动控制失败",
				err,
			)
		}
	}

	logger.Success("调试环境设置完成！")
	logger.Info("现在可以在IDE中启动调试模式")
	logger.Info("按 Ctrl+C 退出并清理环境")

	// 等待用户中断
	select {}
}

// setupSignalHandling 设置信号处理
func (app *DebugSetupApp) setupSignalHandling() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		logger.Cleanup("收到退出信号，开始清理")
		app.cleanup()
		os.Exit(0)
	}()
}

// cleanup 清理资源
func (app *DebugSetupApp) cleanup() {
	logger.Cleanup("清理端口转发")
	if app.portManager != nil {
		app.portManager.Cleanup()
	}

	logger.Cleanup("清理%s文件共享", app.fileManager.GetShareType())
	if app.fileManager != nil {
		app.fileManager.Cleanup()
	}

	logger.Cleanup("恢复配置文件")
	if app.configManager != nil {
		app.configManager.Cleanup(true) // true表示恢复原始配置
	}

	logger.Cleanup("恢复Docker容器状态")
	if app.dockerManager != nil {
		app.dockerManager.CleanupDockerControl()
	}

	logger.Cleanup("关闭SSH连接")
	if app.sshClient != nil {
		app.sshClient.Close()
	}

	logger.Success("清理完成")
}

// GetConfig 获取配置
func (app *DebugSetupApp) GetConfig() *types.Config {
	return app.config
}

// GetSSHClient 获取SSH客户端
func (app *DebugSetupApp) GetSSHClient() *ssh.Client {
	return app.sshClient
}

// GetFileShareManager 获取文件共享管理器
func (app *DebugSetupApp) GetFileShareManager() fileshare.FileShareManager {
	return app.fileManager
}

// GetPortManager 获取端口管理器
func (app *DebugSetupApp) GetPortManager() *port.Manager {
	return app.portManager
}

// GetConfigManager 获取配置管理器
func (app *DebugSetupApp) GetConfigManager() *config.Manager {
	return app.configManager
}

// GetDockerManager 获取Docker管理器
func (app *DebugSetupApp) GetDockerManager() *docker.Manager {
	return app.dockerManager
}

// getFileShareErrorType 根据文件共享类型获取错误类型
func getFileShareErrorType(shareType string) errors.ErrorType {
	switch shareType {
	case "samba":
		return errors.ErrorTypeSamba
	case "nfs":
		return errors.ErrorTypeNFS
	case "sshfs":
		return errors.ErrorTypeSSHFS
	default:
		return errors.ErrorTypeGeneric
	}
} 