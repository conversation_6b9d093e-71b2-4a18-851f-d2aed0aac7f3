package factory

import (
	"fmt"

	"github.com/addx/iot-debug-setup/internal/cleanup"
	"github.com/addx/iot-debug-setup/internal/config"
	"github.com/addx/iot-debug-setup/internal/connection"
	"github.com/addx/iot-debug-setup/internal/diagnosis"
	"github.com/addx/iot-debug-setup/internal/docker"
	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/port"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// ManagerSet 管理器集合
type ManagerSet struct {
	ConnectionManager *connection.Manager
	ConfigManager     *config.Manager
	FileShareManager  fileshare.FileShareManager
	PortManager       *port.Manager
	DockerManager     *docker.Manager
	DiagnosisManager  *diagnosis.Manager
	CleanupManager    *cleanup.Manager
}

// ManagerFactory 管理器工厂
type ManagerFactory struct {
	config *types.Config
}

// NewManagerFactory 创建管理器工厂
func NewManagerFactory(config *types.Config) *ManagerFactory {
	return &ManagerFactory{
		config: config,
	}
}

// CreateManagerSet 创建管理器集合
func (f *ManagerFactory) CreateManagerSet() (*ManagerSet, error) {
	// 创建连接管理器
	connectionManager := connection.NewManager(f.config)
	
	// 创建配置管理器
	configManager := config.NewManager(f.config)
	
	// 创建诊断管理器
	diagnosisManager := diagnosis.NewManager(f.config)
	
	// 创建清理管理器
	cleanupManager := cleanup.NewManager(f.config)
	
	// 创建端口管理器
	portManager := port.NewManager(f.config)
	
	// 创建Docker管理器 (需要SSH连接)
	var dockerManager *docker.Manager
	
	// 创建文件共享管理器 (需要SSH连接)
	var fileShareManager fileshare.FileShareManager
	
	return &ManagerSet{
		ConnectionManager: connectionManager,
		ConfigManager:     configManager,
		FileShareManager:  fileShareManager,
		PortManager:       portManager,
		DockerManager:     dockerManager,
		DiagnosisManager:  diagnosisManager,
		CleanupManager:    cleanupManager,
	}, nil
}

// CreateConnectedManagerSet 创建已连接的管理器集合
func (f *ManagerFactory) CreateConnectedManagerSet() (*ManagerSet, error) {
	// 创建基础管理器集合
	managerSet, err := f.CreateManagerSet()
	if err != nil {
		return nil, fmt.Errorf("创建管理器集合失败: %v", err)
	}
	
	// 建立连接
	if err := managerSet.ConnectionManager.Connect(); err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}
	
	// 获取SSH客户端
	sshClient := managerSet.ConnectionManager.GetClient()
	if sshClient == nil {
		return nil, fmt.Errorf("SSH客户端为空")
	}
	
	// 创建需要SSH连接的管理器
	managerSet.DockerManager = docker.NewManager(f.config, sshClient)
	
	fileShareManager, err := fileshare.CreateFileShareManager(f.config, sshClient)
	if err != nil {
		return nil, fmt.Errorf("创建文件共享管理器失败: %v", err)
	}
	managerSet.FileShareManager = fileShareManager
	
	// 设置端口管理器的SSH客户端
	managerSet.PortManager.SetSSHClient(sshClient)
	
	// 设置清理管理器的依赖
	managerSet.CleanupManager.SetManagers(
		sshClient,
		managerSet.FileShareManager,
		managerSet.PortManager,
		managerSet.ConfigManager,
		managerSet.DockerManager,
	)
	
	return managerSet, nil
}

// CreateDiagnosisOnlyManagerSet 创建仅用于诊断的管理器集合
func (f *ManagerFactory) CreateDiagnosisOnlyManagerSet() (*ManagerSet, error) {
	// 创建连接管理器
	connectionManager := connection.NewManager(f.config)
	
	// 建立连接
	if err := connectionManager.Connect(); err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}
	
	// 获取SSH客户端
	sshClient := connectionManager.GetClient()
	if sshClient == nil {
		return nil, fmt.Errorf("SSH客户端为空")
	}
	
	// 创建诊断管理器
	diagnosisManager := diagnosis.NewManager(f.config)
	
	// 创建文件共享管理器
	fileShareManager, err := fileshare.CreateFileShareManager(f.config, sshClient)
	if err != nil {
		return nil, fmt.Errorf("创建文件共享管理器失败: %v", err)
	}
	
	// 创建Docker管理器
	dockerManager := docker.NewManager(f.config, sshClient)
	
	return &ManagerSet{
		ConnectionManager: connectionManager,
		FileShareManager:  fileShareManager,
		DockerManager:     dockerManager,
		DiagnosisManager:  diagnosisManager,
	}, nil
}

// CreateCleanupOnlyManagerSet 创建仅用于清理的管理器集合
func (f *ManagerFactory) CreateCleanupOnlyManagerSet() (*ManagerSet, error) {
	// 创建连接管理器
	connectionManager := connection.NewManager(f.config)
	
	// 建立连接
	if err := connectionManager.Connect(); err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}
	
	// 获取SSH客户端
	sshClient := connectionManager.GetClient()
	if sshClient == nil {
		return nil, fmt.Errorf("SSH客户端为空")
	}
	
	// 创建需要的管理器
	configManager := config.NewManager(f.config)
	portManager := port.NewManager(f.config)
	portManager.SetSSHClient(sshClient)
	
	fileShareManager, err := fileshare.CreateFileShareManager(f.config, sshClient)
	if err != nil {
		return nil, fmt.Errorf("创建文件共享管理器失败: %v", err)
	}
	
	dockerManager := docker.NewManager(f.config, sshClient)
	
	// 创建清理管理器
	cleanupManager := cleanup.NewManager(f.config)
	cleanupManager.SetManagers(
		sshClient,
		fileShareManager,
		portManager,
		configManager,
		dockerManager,
	)
	
	return &ManagerSet{
		ConnectionManager: connectionManager,
		ConfigManager:     configManager,
		FileShareManager:  fileShareManager,
		PortManager:       portManager,
		DockerManager:     dockerManager,
		CleanupManager:    cleanupManager,
	}, nil
}

// ManagerBuilder 管理器构建器
type ManagerBuilder struct {
	config         *types.Config
	withConnection bool
	withSSH        bool
	components     []string
}

// NewManagerBuilder 创建管理器构建器
func NewManagerBuilder(config *types.Config) *ManagerBuilder {
	return &ManagerBuilder{
		config:     config,
		components: make([]string, 0),
	}
}

// WithConnection 启用连接
func (b *ManagerBuilder) WithConnection() *ManagerBuilder {
	b.withConnection = true
	return b
}

// WithSSH 启用SSH
func (b *ManagerBuilder) WithSSH() *ManagerBuilder {
	b.withSSH = true
	return b
}

// WithComponent 添加组件
func (b *ManagerBuilder) WithComponent(component string) *ManagerBuilder {
	b.components = append(b.components, component)
	return b
}

// WithComponents 添加多个组件
func (b *ManagerBuilder) WithComponents(components ...string) *ManagerBuilder {
	b.components = append(b.components, components...)
	return b
}

// Build 构建管理器集合
func (b *ManagerBuilder) Build() (*ManagerSet, error) {
	managerSet := &ManagerSet{}
	
	// 如果需要连接，创建连接管理器
	if b.withConnection {
		connectionManager := connection.NewManager(b.config)
		managerSet.ConnectionManager = connectionManager
		
		if err := connectionManager.Connect(); err != nil {
			return nil, fmt.Errorf("连接失败: %v", err)
		}
	}
	
	// 获取SSH客户端
	var sshClient *ssh.Client
	if b.withSSH && managerSet.ConnectionManager != nil {
		sshClient = managerSet.ConnectionManager.GetClient()
		if sshClient == nil {
			return nil, fmt.Errorf("SSH客户端为空")
		}
	}
	
	// 根据组件列表创建管理器
	for _, component := range b.components {
		switch component {
		case "config":
			managerSet.ConfigManager = config.NewManager(b.config)
		case "port":
			portManager := port.NewManager(b.config)
			if sshClient != nil {
				portManager.SetSSHClient(sshClient)
			}
			managerSet.PortManager = portManager
		case "docker":
			if sshClient != nil {
				managerSet.DockerManager = docker.NewManager(b.config, sshClient)
			}
		case "fileshare":
			if sshClient != nil {
				fileShareManager, err := fileshare.CreateFileShareManager(b.config, sshClient)
				if err != nil {
					return nil, fmt.Errorf("创建文件共享管理器失败: %v", err)
				}
				managerSet.FileShareManager = fileShareManager
			}
		case "diagnosis":
			managerSet.DiagnosisManager = diagnosis.NewManager(b.config)
		case "cleanup":
			cleanupManager := cleanup.NewManager(b.config)
			if sshClient != nil {
				cleanupManager.SetManagers(
					sshClient,
					managerSet.FileShareManager,
					managerSet.PortManager,
					managerSet.ConfigManager,
					managerSet.DockerManager,
				)
			}
			managerSet.CleanupManager = cleanupManager
		default:
			return nil, fmt.Errorf("未知组件: %s", component)
		}
	}
	
	return managerSet, nil
}

// Cleanup 清理管理器集合
func (ms *ManagerSet) Cleanup() {
	if ms.CleanupManager != nil {
		ms.CleanupManager.ExecuteStandardCleanup()
	}
	
	if ms.ConnectionManager != nil {
		ms.ConnectionManager.Disconnect()
	}
}

// GetConnectionInfo 获取连接信息
func (ms *ManagerSet) GetConnectionInfo() map[string]interface{} {
	if ms.ConnectionManager != nil {
		return ms.ConnectionManager.GetConnectionInfo()
	}
	return map[string]interface{}{}
}

// IsConnected 检查是否已连接
func (ms *ManagerSet) IsConnected() bool {
	if ms.ConnectionManager != nil {
		return ms.ConnectionManager.IsConnected()
	}
	return false
}

// GetSSHClient 获取SSH客户端
func (ms *ManagerSet) GetSSHClient() *ssh.Client {
	if ms.ConnectionManager != nil {
		return ms.ConnectionManager.GetClient()
	}
	return nil
}

// 预定义的管理器集合构建器

// CreateFullManagerSet 创建完整的管理器集合
func CreateFullManagerSet(config *types.Config) (*ManagerSet, error) {
	factory := NewManagerFactory(config)
	return factory.CreateConnectedManagerSet()
}

// CreateDiagnosisManagerSet 创建诊断管理器集合
func CreateDiagnosisManagerSet(config *types.Config) (*ManagerSet, error) {
	factory := NewManagerFactory(config)
	return factory.CreateDiagnosisOnlyManagerSet()
}

// CreateCleanupManagerSet 创建清理管理器集合
func CreateCleanupManagerSet(config *types.Config) (*ManagerSet, error) {
	factory := NewManagerFactory(config)
	return factory.CreateCleanupOnlyManagerSet()
}

// CreateCustomManagerSet 创建自定义管理器集合
func CreateCustomManagerSet(config *types.Config, components ...string) (*ManagerSet, error) {
	builder := NewManagerBuilder(config)
	builder.WithConnection().WithSSH().WithComponents(components...)
	return builder.Build()
} 