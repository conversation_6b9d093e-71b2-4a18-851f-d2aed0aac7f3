package logger

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// 日志级别名称映射
var levelNames = map[LogLevel]string{
	DEBUG: "DEBUG",
	INFO:  "INFO",
	WARN:  "WARN",
	ERROR: "ERROR",
}

// 颜色代码 (ANSI)
var levelColors = map[LogLevel]string{
	DEBUG: "\033[36m", // 青色
	INFO:  "\033[32m", // 绿色
	WARN:  "\033[33m", // 黄色
	ERROR: "\033[31m", // 红色
}

const (
	colorReset = "\033[0m"
	colorBold  = "\033[1m"
	colorGray  = "\033[90m"
)

// Logger 日志管理器
type Logger struct {
	level      LogLevel
	timeFormat string
	useColors  bool
	prefix     string
}

// defaultLogger 默认日志实例
var defaultLogger *Logger

func init() {
	defaultLogger = NewLogger()
}

// NewLogger 创建新的日志管理器
func NewLogger() *Logger {
	return &Logger{
		level:      INFO,
		timeFormat: "2006-01-02 15:04:05",
		useColors:  supportsColor(),
		prefix:     "",
	}
}

// supportsColor 检测终端是否支持颜色
func supportsColor() bool {
	if runtime.GOOS == "windows" {
		// Windows 10+ 支持 ANSI 颜色
		return os.Getenv("TERM") != "dumb"
	}
	// Unix 系统通常支持颜色
	return os.Getenv("TERM") != "dumb" && os.Getenv("NO_COLOR") == ""
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.level = level
}

// SetPrefix 设置日志前缀
func (l *Logger) SetPrefix(prefix string) {
	l.prefix = prefix
}

// SetTimeFormat 设置时间格式
func (l *Logger) SetTimeFormat(format string) {
	l.timeFormat = format
}

// SetUseColors 设置是否使用颜色
func (l *Logger) SetUseColors(useColors bool) {
	l.useColors = useColors
}

// formatMessage 格式化日志消息
func (l *Logger) formatMessage(level LogLevel, message string) string {
	timestamp := time.Now().Format(l.timeFormat)
	levelName := levelNames[level]
	
	var formattedMessage string
	
	if l.useColors {
		levelColor := levelColors[level]
		timeColor := colorGray
		
		// 格式: [时间] [级别] 消息
		formattedMessage = fmt.Sprintf("%s[%s]%s %s[%s]%s %s",
			timeColor, timestamp, colorReset,
			levelColor+colorBold, levelName, colorReset,
			message)
	} else {
		// 无颜色格式: [时间] [级别] 消息
		formattedMessage = fmt.Sprintf("[%s] [%s] %s",
			timestamp, levelName, message)
	}
	
	if l.prefix != "" {
		if l.useColors {
			formattedMessage = fmt.Sprintf("%s[%s]%s %s",
				colorGray, l.prefix, colorReset, formattedMessage)
		} else {
			formattedMessage = fmt.Sprintf("[%s] %s", l.prefix, formattedMessage)
		}
	}
	
	return formattedMessage
}

// log 通用日志方法
func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}
	
	message := fmt.Sprintf(format, args...)
	formattedMessage := l.formatMessage(level, message)
	
	if level >= ERROR {
		fmt.Fprintln(os.Stderr, formattedMessage)
	} else {
		fmt.Println(formattedMessage)
	}
}

// Debug 调试日志
func (l *Logger) Debug(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

// Info 信息日志
func (l *Logger) Info(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

// Warn 警告日志
func (l *Logger) Warn(format string, args ...interface{}) {
	l.log(WARN, format, args...)
}

// Error 错误日志
func (l *Logger) Error(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Success 成功日志（使用INFO级别）
func (l *Logger) Success(format string, args ...interface{}) {
	message := fmt.Sprintf("✅ %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// Progress 进度日志（使用INFO级别）
func (l *Logger) Progress(format string, args ...interface{}) {
	message := fmt.Sprintf("🔄 %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// Config 配置日志（使用INFO级别）
func (l *Logger) Config(format string, args ...interface{}) {
	message := fmt.Sprintf("⚙️ %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// Network 网络日志（使用INFO级别）
func (l *Logger) Network(format string, args ...interface{}) {
	message := fmt.Sprintf("📡 %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// File 文件操作日志（使用INFO级别）
func (l *Logger) File(format string, args ...interface{}) {
	message := fmt.Sprintf("📁 %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// Command 命令执行日志（使用DEBUG级别）
func (l *Logger) Command(format string, args ...interface{}) {
	message := fmt.Sprintf("🔧 %s", fmt.Sprintf(format, args...))
	l.log(DEBUG, message)
}

// Cleanup 清理日志（使用INFO级别）
func (l *Logger) Cleanup(format string, args ...interface{}) {
	message := fmt.Sprintf("🧹 %s", fmt.Sprintf(format, args...))
	l.log(INFO, message)
}

// 全局日志方法
func SetLevel(level LogLevel) {
	defaultLogger.SetLevel(level)
}

func SetPrefix(prefix string) {
	defaultLogger.SetPrefix(prefix)
}

func SetTimeFormat(format string) {
	defaultLogger.SetTimeFormat(format)
}

func SetUseColors(useColors bool) {
	defaultLogger.SetUseColors(useColors)
}

func Debug(format string, args ...interface{}) {
	defaultLogger.Debug(format, args...)
}

func Info(format string, args ...interface{}) {
	defaultLogger.Info(format, args...)
}

func Warn(format string, args ...interface{}) {
	defaultLogger.Warn(format, args...)
}

func Error(format string, args ...interface{}) {
	defaultLogger.Error(format, args...)
}

func Success(format string, args ...interface{}) {
	defaultLogger.Success(format, args...)
}

func Progress(format string, args ...interface{}) {
	defaultLogger.Progress(format, args...)
}

func Config(format string, args ...interface{}) {
	defaultLogger.Config(format, args...)
}

func Network(format string, args ...interface{}) {
	defaultLogger.Network(format, args...)
}

func File(format string, args ...interface{}) {
	defaultLogger.File(format, args...)
}

func Command(format string, args ...interface{}) {
	defaultLogger.Command(format, args...)
}

func Cleanup(format string, args ...interface{}) {
	defaultLogger.Cleanup(format, args...)
}

// StartupBanner 启动横幅
func StartupBanner(appName, version string) {
	border := strings.Repeat("=", 60)
	if defaultLogger.useColors {
		fmt.Printf("%s%s%s\n", colorBold, border, colorReset)
		fmt.Printf("%s🚀 %s v%s%s\n", colorBold, appName, version, colorReset)
		fmt.Printf("%s%s%s\n", colorBold, border, colorReset)
	} else {
		fmt.Println(border)
		fmt.Printf("🚀 %s v%s\n", appName, version)
		fmt.Println(border)
	}
}

// Section 创建日志段落
func Section(title string) {
	if defaultLogger.useColors {
		fmt.Printf("\n%s📋 %s%s\n", colorBold, title, colorReset)
		fmt.Printf("%s%s%s\n", colorGray, strings.Repeat("-", len(title)+5), colorReset)
	} else {
		fmt.Printf("\n📋 %s\n", title)
		fmt.Printf("%s\n", strings.Repeat("-", len(title)+5))
	}
} 