package ssh

import (
	"fmt"
	"io/ioutil"
	"net"
	"time"

	"golang.org/x/crypto/ssh"

	"github.com/addx/iot-debug-setup/internal/logger"
	"github.com/addx/iot-debug-setup/internal/service"
	"github.com/addx/iot-debug-setup/internal/types"
)

// Client SSH客户端结构体
type Client struct {
	config *types.Config
	client *ssh.Client
}

// NewClient 创建新的SSH客户端
func NewClient(config *types.Config) *Client {
	return &Client{
		config: config,
	}
}

// Connect 连接到SSH服务器
func (c *Client) Connect() error {
	// 创建SSH配置
	sshConfig := &ssh.ClientConfig{
		User:            c.config.TargetDevice.SSH.Username,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境中应该验证主机密钥
		Timeout:         30 * time.Second,
	}

	// 添加密码认证
	if c.config.TargetDevice.SSH.Password != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.Password(c.config.TargetDevice.SSH.Password))
	}

	// 添加密钥认证
	if c.config.TargetDevice.SSH.KeyFile != "" {
		key, err := ioutil.ReadFile(c.config.TargetDevice.SSH.KeyFile)
		if err != nil {
			return fmt.Errorf("读取密钥文件失败: %v", err)
		}

		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return fmt.Errorf("解析私钥失败: %v", err)
		}

		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeys(signer))
	}

	// 如果没有配置任何认证方式，返回错误
	if len(sshConfig.Auth) == 0 {
		return fmt.Errorf("没有配置SSH认证方式")
	}

	// 建立SSH连接
	addr := fmt.Sprintf("%s:%d", c.config.TargetDevice.IP, c.config.TargetDevice.SSH.Port)
	client, err := ssh.Dial("tcp", addr, sshConfig)
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}

	c.client = client
	logger.Success("SSH连接成功: %s", addr)
	return nil
}

// ExecuteCommand 执行远程命令
func (c *Client) ExecuteCommand(command string) (string, error) {
	if c.client == nil {
		return "", fmt.Errorf("SSH客户端未连接")
	}

	// 创建新的会话
	session, err := c.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 执行命令
	logger.Command("执行命令: %s", command)
	output, err := session.CombinedOutput(command)
	if err != nil {
		return string(output), fmt.Errorf("命令执行失败: %v", err)
	}

	return string(output), nil
}

// CommandExists 检查命令是否存在
func (c *Client) CommandExists(command string) bool {
	_, err := c.ExecuteCommand(fmt.Sprintf("command -v %s", command))
	return err == nil
}

// FileExists 检查文件是否存在
func (c *Client) FileExists(path string) bool {
	_, err := c.ExecuteCommand(fmt.Sprintf("test -f %s", path))
	return err == nil
}

// DirectoryExists 检查目录是否存在
func (c *Client) DirectoryExists(path string) bool {
	_, err := c.ExecuteCommand(fmt.Sprintf("test -d %s", path))
	return err == nil
}

// CreateDirectory 创建目录
func (c *Client) CreateDirectory(path string) error {
	_, err := c.ExecuteCommand(fmt.Sprintf("mkdir -p %s", path))
	return err
}

// IsServiceRunning 检查服务是否运行
func (c *Client) IsServiceRunning(serviceName string) bool {
	_, err := c.ExecuteCommand(fmt.Sprintf("systemctl is-active %s", serviceName))
	return err == nil
}

// StartService 启动服务（使用抽象服务管理器）
func (c *Client) StartService(serviceName string) error {
	serviceManager := service.NewLinuxServiceManager(c)
	return serviceManager.StartService(serviceName)
}

// StopService 停止服务
func (c *Client) StopService(serviceName string) error {
	serviceManager := service.NewLinuxServiceManager(c)
	return serviceManager.StopService(serviceName)
}

// EnableService 启用服务（使用抽象服务管理器）
func (c *Client) EnableService(serviceName string) error {
	serviceManager := service.NewLinuxServiceManager(c)
	return serviceManager.EnableService(serviceName)
}

// RestartService 重启服务
func (c *Client) RestartService(serviceName string) error {
	_, err := c.ExecuteCommand(fmt.Sprintf("systemctl restart %s", serviceName))
	return err
}

// IsPortInUse 检查端口是否被占用
func (c *Client) IsPortInUse(port int) bool {
	_, err := c.ExecuteCommand(fmt.Sprintf("netstat -tuln | grep ':%d '", port))
	return err == nil
}

// TestConnection 测试网络连接
func (c *Client) TestConnection() error {
	addr := fmt.Sprintf("%s:%d", c.config.TargetDevice.IP, c.config.TargetDevice.SSH.Port)
	conn, err := net.DialTimeout("tcp", addr, 5*time.Second)
	if err != nil {
		return fmt.Errorf("无法连接到 %s: %v", addr, err)
	}
	conn.Close()
	return nil
}

// IsConnected 检查SSH客户端是否连接
func (c *Client) IsConnected() bool {
	if c.client == nil {
		return false
	}
	
	// 尝试执行一个简单的命令来测试连接
	session, err := c.client.NewSession()
	if err != nil {
		return false
	}
	defer session.Close()
	
	err = session.Run("echo")
	return err == nil
}

// TryReconnect 尝试重新连接SSH
func (c *Client) TryReconnect() error {
	if c.client != nil {
		c.client.Close()
		c.client = nil
	}
	
	logger.Progress("尝试重新连接SSH")
	return c.Connect()
}

// Close 关闭SSH连接
func (c *Client) Close() error {
	if c.client != nil {
		err := c.client.Close()
		c.client = nil
		return err
	}
	return nil
} 