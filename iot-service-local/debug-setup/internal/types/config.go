package types

// Config 主配置结构体
type Config struct {
	TargetDevice       TargetDevice       `yaml:"target_device"`
	LocalMachine       LocalMachine       `yaml:"local_machine"`
	FileSharing        FileSharing        `yaml:"file_sharing"`
	NFSExports         NFSExports         `yaml:"nfs_exports,omitempty"`        // 向后兼容
	PortForwarding     PortForwarding     `yaml:"port_forwarding"`
	ConfigReplacements ConfigReplacements `yaml:"config_replacements"`
	DockerControl      DockerControl      `yaml:"docker_control"`
	ExecutionSteps     ExecutionSteps     `yaml:"execution_steps"`
}

// TargetDevice 目标设备配置
type TargetDevice struct {
	IP  string `yaml:"ip"`
	SSH struct {
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Port     int    `yaml:"port"`
		KeyFile  string `yaml:"key_file"`
	} `yaml:"ssh"`
}

// LocalMachine 本地机器配置
type LocalMachine struct {
	IP            string `yaml:"ip"`
	MountPoint    string `yaml:"mount_point"`
	NFSMountPoint string `yaml:"nfs_mount_point,omitempty"` // 向后兼容
}

// FileSharing 文件共享配置
type FileSharing struct {
	Type        string            `yaml:"type"`        // "samba", "nfs", "sshfs"
	Directories []ShareDirectory  `yaml:"directories"`
	Options     map[string]string `yaml:"options"`     // 类型特定的选项
}

// ShareDirectory 共享目录配置（通用）
type ShareDirectory struct {
	Source      string `yaml:"source"`       // 源目录路径
	ShareName   string `yaml:"share_name"`   // 共享名称（SAMBA用）或导出路径（NFS用）
	Options     string `yaml:"options"`      // 目录特定选项
	Description string `yaml:"description"`  // 描述
}

// PortForwarding 端口转发配置
type PortForwarding struct {
	ExposePorts    []ExposePort    `yaml:"expose_ports"`
	ForwardToLocal []ForwardToLocal `yaml:"forward_to_local"`
}

// ExposePort 暴露端口配置
type ExposePort struct {
	Port        int    `yaml:"port"`
	Description string `yaml:"description"`
}

// ForwardToLocal 转发到本地的端口配置
type ForwardToLocal struct {
	RemotePort  int    `yaml:"remote_port"`
	LocalPort   int    `yaml:"local_port"`
	Description string `yaml:"description"`
}

// ConfigReplacements 配置替换规则
type ConfigReplacements struct {
	ProjectRoot   string        `yaml:"project_root"`   // 项目根目录（绝对路径）
	ActiveProfile string        `yaml:"active_profile"` // 当前激活的profile（如"test"对应application-test.yml）
	Replacements  []Replacement `yaml:"replacements"`
}

// Replacement 单个替换规则
type Replacement struct {
	Key         string `yaml:"key"`
	Value       string `yaml:"value"`
	Description string `yaml:"description"`
}

// ExecutionSteps 执行步骤配置
type ExecutionSteps struct {
	SetupFileSharing    bool `yaml:"setup_file_sharing"`
	SetupNFS            bool `yaml:"setup_nfs,omitempty"`            // 向后兼容
	SetupPortForwarding bool `yaml:"setup_port_forwarding"`
	UpdateConfigFile    bool `yaml:"update_config_file"`
	SetupDockerControl  bool `yaml:"setup_docker_control"`
	CleanupOnExit       bool `yaml:"cleanup_on_exit"`
}

// 保留旧的NFS类型以确保向后兼容
type NFSExports struct {
	Directories []NFSDirectory `yaml:"directories"`
}

type NFSDirectory struct {
	Source     string `yaml:"source"`
	ExportPath string `yaml:"export_path"`
	Options    string `yaml:"options"`
}

// DockerControl Docker容器控制配置
type DockerControl struct {
	Enabled    bool              `yaml:"enabled"`
	Containers []DockerContainer `yaml:"containers"`
}

// DockerContainer Docker容器配置
type DockerContainer struct {
	Name            string `yaml:"name"`
	ActionOnStart   string `yaml:"action_on_start"`   // stop, restart, none
	ActionOnCleanup string `yaml:"action_on_cleanup"` // start, stop, restart, none
	Description     string `yaml:"description"`
} 