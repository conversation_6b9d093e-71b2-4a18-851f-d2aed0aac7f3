package cleanup

import (
	"fmt"
	"log"

	"github.com/addx/iot-debug-setup/internal/config"
	"github.com/addx/iot-debug-setup/internal/docker"
	"github.com/addx/iot-debug-setup/internal/fileshare"
	"github.com/addx/iot-debug-setup/internal/port"
	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// CleanupTask 清理任务接口
type CleanupTask interface {
	Execute() error
	GetName() string
	GetType() string
}

// CleanupResult 清理结果
type CleanupResult struct {
	TaskName string
	TaskType string
	Success  bool
	Error    error
}

// Manager 清理管理器
type Manager struct {
	config        *types.Config
	sshClient     *ssh.Client
	fileManager   fileshare.FileShareManager
	portManager   *port.Manager
	configManager *config.Manager
	dockerManager *docker.Manager
	tasks         []CleanupTask
}

// NewManager 创建清理管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config: config,
		tasks:  make([]CleanupTask, 0),
	}
}

// SetManagers 设置各种管理器
func (m *Manager) SetManagers(sshClient *ssh.Client, fileManager fileshare.FileShareManager, 
	portManager *port.Manager, configManager *config.Manager, dockerManager *docker.Manager) {
	m.sshClient = sshClient
	m.fileManager = fileManager
	m.portManager = portManager
	m.configManager = configManager
	m.dockerManager = dockerManager
}

// AddTask 添加清理任务
func (m *Manager) AddTask(task CleanupTask) {
	m.tasks = append(m.tasks, task)
}

// ExecuteAll 执行所有清理任务
func (m *Manager) ExecuteAll() []CleanupResult {
	var results []CleanupResult
	
	for _, task := range m.tasks {
		fmt.Printf("🧹 %s...\n", task.GetName())
		
		err := task.Execute()
		result := CleanupResult{
			TaskName: task.GetName(),
			TaskType: task.GetType(),
			Success:  err == nil,
			Error:    err,
		}
		
		if err != nil {
			fmt.Printf("❌ %s失败: %v\n", task.GetName(), err)
		} else {
			fmt.Printf("✅ %s完成\n", task.GetName())
		}
		
		results = append(results, result)
	}
	
	return results
}

// ExecuteStandardCleanup 执行标准清理流程
func (m *Manager) ExecuteStandardCleanup() bool {
	fmt.Println("🧹 开始清理...")
	
	// 添加标准清理任务
	m.AddTask(&portCleanupTask{m.portManager})
	m.AddTask(&fileShareCleanupTask{m.fileManager})
	m.AddTask(&configCleanupTask{m.configManager})
	m.AddTask(&dockerCleanupTask{m.dockerManager, m.config})
	m.AddTask(&sshCloseTask{m.sshClient})
	
	// 执行所有清理任务
	results := m.ExecuteAll()
	
	// 统计结果
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	
	// 输出结果总结
	allSuccess := successCount == len(results)
	if allSuccess {
		fmt.Println("\n🎉 资源清理完成！")
		m.printCleanupSummary()
	} else {
		fmt.Println("\n⚠️  部分资源清理失败，请检查上述错误信息")
		fmt.Println("💡 您可以手动清理剩余的配置，或重新运行清理命令")
	}
	
	return allSuccess
}

// printCleanupSummary 打印清理总结
func (m *Manager) printCleanupSummary() {
	fmt.Println("📋 已清理的内容:")
	fmt.Println("  • iptables端口转发规则")
	
	if m.fileManager != nil {
		fmt.Printf("  • %s文件共享配置\n", m.fileManager.GetShareType())
	}
	
	fmt.Println("  • application.yml配置文件")
	fmt.Println("  • Docker容器状态恢复")
	fmt.Println()
	fmt.Println("💡 注意: 已安装的软件包（如samba、nfs-utils等）未被删除")
}

// 各种清理任务的具体实现

// portCleanupTask 端口清理任务
type portCleanupTask struct {
	portManager *port.Manager
}

func (t *portCleanupTask) Execute() error {
	if t.portManager == nil {
		return fmt.Errorf("端口管理器未设置")
	}
	return t.portManager.Cleanup()
}

func (t *portCleanupTask) GetName() string {
	return "清理端口转发"
}

func (t *portCleanupTask) GetType() string {
	return "port"
}

// fileShareCleanupTask 文件共享清理任务
type fileShareCleanupTask struct {
	fileManager fileshare.FileShareManager
}

func (t *fileShareCleanupTask) Execute() error {
	if t.fileManager == nil {
		return fmt.Errorf("文件共享管理器未设置")
	}
	return t.fileManager.Cleanup()
}

func (t *fileShareCleanupTask) GetName() string {
	if t.fileManager == nil {
		return "清理文件共享"
	}
	return fmt.Sprintf("清理%s文件共享", t.fileManager.GetShareType())
}

func (t *fileShareCleanupTask) GetType() string {
	return "fileshare"
}

// configCleanupTask 配置清理任务
type configCleanupTask struct {
	configManager *config.Manager
}

func (t *configCleanupTask) Execute() error {
	if t.configManager == nil {
		return fmt.Errorf("配置管理器未设置")
	}
	return t.configManager.Cleanup(true) // true表示恢复原始配置
}

func (t *configCleanupTask) GetName() string {
	return "恢复配置文件"
}

func (t *configCleanupTask) GetType() string {
	return "config"
}

// dockerCleanupTask Docker清理任务
type dockerCleanupTask struct {
	dockerManager *docker.Manager
	config        *types.Config
}

func (t *dockerCleanupTask) Execute() error {
	if t.dockerManager == nil {
		return fmt.Errorf("Docker管理器未设置")
	}
	if !t.config.DockerControl.Enabled {
		log.Println("Docker控制未启用，跳过清理")
		return nil
	}
	return t.dockerManager.CleanupDockerControl()
}

func (t *dockerCleanupTask) GetName() string {
	return "恢复Docker容器状态"
}

func (t *dockerCleanupTask) GetType() string {
	return "docker"
}

// sshCloseTask SSH关闭任务
type sshCloseTask struct {
	sshClient *ssh.Client
}

func (t *sshCloseTask) Execute() error {
	if t.sshClient == nil {
		return fmt.Errorf("SSH客户端未设置")
	}
	t.sshClient.Close()
	return nil
}

func (t *sshCloseTask) GetName() string {
	return "关闭SSH连接"
}

func (t *sshCloseTask) GetType() string {
	return "ssh"
}

// TaskBuilder 任务构建器
type TaskBuilder struct {
	manager *Manager
}

// NewTaskBuilder 创建任务构建器
func NewTaskBuilder(manager *Manager) *TaskBuilder {
	return &TaskBuilder{
		manager: manager,
	}
}

// BuildStandardTasks 构建标准清理任务
func (b *TaskBuilder) BuildStandardTasks() *TaskBuilder {
	if b.manager.portManager != nil {
		b.manager.AddTask(&portCleanupTask{b.manager.portManager})
	}
	
	if b.manager.fileManager != nil {
		b.manager.AddTask(&fileShareCleanupTask{b.manager.fileManager})
	}
	
	if b.manager.configManager != nil {
		b.manager.AddTask(&configCleanupTask{b.manager.configManager})
	}
	
	if b.manager.dockerManager != nil {
		b.manager.AddTask(&dockerCleanupTask{b.manager.dockerManager, b.manager.config})
	}
	
	if b.manager.sshClient != nil {
		b.manager.AddTask(&sshCloseTask{b.manager.sshClient})
	}
	
	return b
}

// BuildPortCleanupOnly 仅构建端口清理任务
func (b *TaskBuilder) BuildPortCleanupOnly() *TaskBuilder {
	if b.manager.portManager != nil {
		b.manager.AddTask(&portCleanupTask{b.manager.portManager})
	}
	return b
}

// BuildFileShareCleanupOnly 仅构建文件共享清理任务
func (b *TaskBuilder) BuildFileShareCleanupOnly() *TaskBuilder {
	if b.manager.fileManager != nil {
		b.manager.AddTask(&fileShareCleanupTask{b.manager.fileManager})
	}
	return b
}

// Execute 执行构建的任务
func (b *TaskBuilder) Execute() []CleanupResult {
	return b.manager.ExecuteAll()
} 