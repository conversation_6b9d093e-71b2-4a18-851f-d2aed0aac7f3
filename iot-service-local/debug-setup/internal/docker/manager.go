package docker

import (
	"fmt"
	"log"
	"strings"

	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
)

// Manager Docker管理器结构体
type Manager struct {
	config           *types.Config
	sshClient        *ssh.Client
	originalStatuses map[string]string // 记录容器的原始状态
}

// NewManager 创建新的Docker管理器
func NewManager(config *types.Config, sshClient *ssh.Client) *Manager {
	return &Manager{
		config:           config,
		sshClient:        sshClient,
		originalStatuses: make(map[string]string),
	}
}

// SetupDockerControl 执行Docker容器启动控制
func (m *Manager) SetupDockerControl() error {
	if !m.config.DockerControl.Enabled {
		log.Println("Docker控制功能已禁用，跳过...")
		return nil
	}

	if len(m.config.DockerControl.Containers) == 0 {
		log.Println("没有配置需要控制的Docker容器")
		return nil
	}

	fmt.Println("🐳 设置Docker容器控制...")
	
	for _, container := range m.config.DockerControl.Containers {
		if container.ActionOnStart == "none" {
			log.Printf("容器 %s 设置为不操作，跳过...", container.Name)
			continue
		}

		// 首先记录容器的原始状态
		originalStatus, err := m.getContainerStatus(container.Name)
		if err != nil {
			log.Printf("⚠️  获取容器 %s 状态失败: %v", container.Name, err)
			continue
		}
		m.originalStatuses[container.Name] = originalStatus

		// 根据配置执行相应的操作
		if err := m.executeAction(container.Name, container.ActionOnStart, container.Description); err != nil {
			return fmt.Errorf("执行容器 %s 启动操作失败: %v", container.Name, err)
		}
	}

	return nil
}

// CleanupDockerControl 清理Docker容器控制
func (m *Manager) CleanupDockerControl() error {
	if !m.config.DockerControl.Enabled {
		return nil
	}

	fmt.Println("🐳 恢复Docker容器状态...")
	
	for _, container := range m.config.DockerControl.Containers {
		if container.ActionOnCleanup == "none" {
			log.Printf("容器 %s 设置为不操作，跳过...", container.Name)
			continue
		}

		// 根据配置执行相应的操作
		if err := m.executeAction(container.Name, container.ActionOnCleanup, container.Description); err != nil {
			log.Printf("⚠️  恢复容器 %s 失败: %v", container.Name, err)
			continue
		}
	}

	return nil
}

// executeAction 执行Docker容器操作
func (m *Manager) executeAction(containerName, action, description string) error {
	log.Printf("对容器 %s (%s) 执行操作: %s", containerName, description, action)

	switch action {
	case "start":
		return m.startContainer(containerName)
	case "stop":
		return m.stopContainer(containerName)
	case "restart":
		return m.restartContainer(containerName)
	case "none":
		return nil
	default:
		return fmt.Errorf("不支持的操作: %s", action)
	}
}

// startContainer 启动容器
func (m *Manager) startContainer(containerName string) error {
	// 首先检查容器是否存在
	exists, err := m.containerExists(containerName)
	if err != nil {
		return fmt.Errorf("检查容器是否存在失败: %v", err)
	}

	if !exists {
		log.Printf("⚠️  容器 %s 不存在，跳过启动", containerName)
		return nil
	}

	// 检查容器是否已经在运行
	running, err := m.isContainerRunning(containerName)
	if err != nil {
		return fmt.Errorf("检查容器运行状态失败: %v", err)
	}

	if running {
		log.Printf("✅ 容器 %s 已经在运行", containerName)
		return nil
	}

	// 启动容器
	cmd := fmt.Sprintf("docker start %s", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return fmt.Errorf("启动容器失败: %v, 输出: %s", err, output)
	}

	log.Printf("✅ 容器 %s 启动成功", containerName)
	return nil
}

// stopContainer 停止容器
func (m *Manager) stopContainer(containerName string) error {
	// 首先检查容器是否存在
	exists, err := m.containerExists(containerName)
	if err != nil {
		return fmt.Errorf("检查容器是否存在失败: %v", err)
	}

	if !exists {
		log.Printf("⚠️  容器 %s 不存在，跳过停止", containerName)
		return nil
	}

	// 检查容器是否在运行
	running, err := m.isContainerRunning(containerName)
	if err != nil {
		return fmt.Errorf("检查容器运行状态失败: %v", err)
	}

	if !running {
		log.Printf("✅ 容器 %s 已经停止", containerName)
		return nil
	}

	// 停止容器
	cmd := fmt.Sprintf("docker stop %s", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return fmt.Errorf("停止容器失败: %v, 输出: %s", err, output)
	}

	log.Printf("✅ 容器 %s 停止成功", containerName)
	return nil
}

// restartContainer 重启容器
func (m *Manager) restartContainer(containerName string) error {
	// 首先检查容器是否存在
	exists, err := m.containerExists(containerName)
	if err != nil {
		return fmt.Errorf("检查容器是否存在失败: %v", err)
	}

	if !exists {
		log.Printf("⚠️  容器 %s 不存在，跳过重启", containerName)
		return nil
	}

	// 重启容器
	cmd := fmt.Sprintf("docker restart %s", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return fmt.Errorf("重启容器失败: %v, 输出: %s", err, output)
	}

	log.Printf("✅ 容器 %s 重启成功", containerName)
	return nil
}

// containerExists 检查容器是否存在
func (m *Manager) containerExists(containerName string) (bool, error) {
	cmd := fmt.Sprintf("docker ps -a --filter name=^%s$ --format '{{.Names}}'", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return false, fmt.Errorf("执行docker ps命令失败: %v", err)
	}

	return strings.TrimSpace(output) == containerName, nil
}

// isContainerRunning 检查容器是否正在运行
func (m *Manager) isContainerRunning(containerName string) (bool, error) {
	cmd := fmt.Sprintf("docker ps --filter name=^%s$ --format '{{.Names}}'", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return false, fmt.Errorf("执行docker ps命令失败: %v", err)
	}

	return strings.TrimSpace(output) == containerName, nil
}

// getContainerStatus 获取容器状态
func (m *Manager) getContainerStatus(containerName string) (string, error) {
	cmd := fmt.Sprintf("docker ps -a --filter name=^%s$ --format '{{.Status}}'", containerName)
	output, err := m.sshClient.ExecuteCommand(cmd)
	if err != nil {
		return "", fmt.Errorf("获取容器状态失败: %v", err)
	}

	status := strings.TrimSpace(output)
	if status == "" {
		return "not_found", nil
	}

	// 解析状态
	if strings.Contains(status, "Up") {
		return "running", nil
	} else if strings.Contains(status, "Exited") {
		return "stopped", nil
	} else {
		return status, nil
	}
}

// DiagnoseDockerIssues 诊断Docker相关问题
func (m *Manager) DiagnoseDockerIssues() error {
	if !m.config.DockerControl.Enabled {
		fmt.Println("Docker控制功能已禁用")
		return nil
	}

	fmt.Println("🐳 诊断Docker容器状态...")

	// 检查Docker是否可用
	_, err := m.sshClient.ExecuteCommand("docker version")
	if err != nil {
		fmt.Printf("❌ Docker服务不可用: %v\n", err)
		return err
	}

	fmt.Println("✅ Docker服务正常")

	// 检查每个配置的容器
	for _, container := range m.config.DockerControl.Containers {
		fmt.Printf("\n检查容器: %s (%s)\n", container.Name, container.Description)
		
		exists, err := m.containerExists(container.Name)
		if err != nil {
			fmt.Printf("❌ 检查容器存在性失败: %v\n", err)
			continue
		}

		if !exists {
			fmt.Printf("❌ 容器不存在\n")
			continue
		}

		fmt.Printf("✅ 容器存在\n")

		running, err := m.isContainerRunning(container.Name)
		if err != nil {
			fmt.Printf("❌ 检查容器运行状态失败: %v\n", err)
			continue
		}

		if running {
			fmt.Printf("✅ 容器正在运行\n")
		} else {
			fmt.Printf("⚠️  容器未运行\n")
		}

		// 显示容器详细状态
		status, err := m.getContainerStatus(container.Name)
		if err != nil {
			fmt.Printf("❌ 获取容器状态失败: %v\n", err)
		} else {
			fmt.Printf("状态: %s\n", status)
		}
	}

	return nil
}

// GetOriginalStatus 获取容器的原始状态
func (m *Manager) GetOriginalStatus(containerName string) string {
	return m.originalStatuses[containerName]
}

// DiagnoseDockerStatus 静默诊断Docker服务状态
func (m *Manager) DiagnoseDockerStatus() (bool, string, error) {
	// 检查Docker是否可用
	_, err := m.sshClient.ExecuteCommand("docker version")
	if err != nil {
		return false, "Docker服务不可用", err
	}
	
	return true, "Docker服务正常", nil
}

// DiagnoseContainerStatus 静默诊断容器状态
func (m *Manager) DiagnoseContainerStatus(containerName string) (bool, string, error) {
	// 检查容器是否存在
	exists, err := m.containerExists(containerName)
	if err != nil {
		return false, "检查容器失败", err
	}
	
	if !exists {
		return false, "容器不存在", nil
	}
	
	// 检查容器是否在运行
	running, err := m.isContainerRunning(containerName)
	if err != nil {
		return false, "检查运行状态失败", err
	}
	
	if running {
		return true, "容器运行中", nil
	} else {
		return false, "容器已停止", nil
	}
} 