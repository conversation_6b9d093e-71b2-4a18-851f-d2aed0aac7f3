package port

import (
	"fmt"
	"strings"

	"github.com/addx/iot-debug-setup/internal/ssh"
	"github.com/addx/iot-debug-setup/internal/types"
	"github.com/addx/iot-debug-setup/internal/utils"
)

// Manager 端口管理器结构体
type Manager struct {
	config        *types.Config
	sshClient     *ssh.Client
	appliedRules  []string // 记录已应用的规则，用于清理
	backupManager *utils.BackupManager
}

// NewManager 创建新的端口管理器
func NewManager(config *types.Config) *Manager {
	return &Manager{
		config:       config,
		appliedRules: make([]string, 0),
	}
}

// SetSSHClient 设置SSH客户端
func (m *Manager) SetSSHClient(client *ssh.Client) {
	m.sshClient = client
	m.backupManager = utils.NewBackupManager(client)
}

// SetupPortForwarding 设置端口转发
func (m *Manager) SetupPortForwarding() error {
	fmt.Println("🌐 开始设置端口转发...")

	// 只处理远程端口暴露
	if err := m.setupRemotePortExposure(); err != nil {
		return fmt.Errorf("设置远程端口暴露失败: %v", err)
	}

	fmt.Println("✅ 端口转发设置完成")
	return nil
}

// setupRemotePortExposure 设置远程端口暴露
func (m *Manager) setupRemotePortExposure() error {
	fmt.Println("🔗 设置远程端口暴露...")

	// 确保SSH客户端已连接
	if m.sshClient == nil {
		return fmt.Errorf("SSH客户端未设置")
	}

	// 检查iptables是否可用
	if !m.sshClient.CommandExists("iptables") {
		return fmt.Errorf("iptables不可用")
	}

	// 保存当前iptables规则
	if err := m.backupIptablesRules(); err != nil {
		fmt.Printf("⚠️  备份iptables规则失败: %v\n", err)
	}

	// 为每个需要暴露的端口创建规则
	for _, portConfig := range m.config.PortForwarding.ExposePorts {
		if err := m.exposePort(portConfig.Port, portConfig.Description); err != nil {
			return fmt.Errorf("暴露端口失败 %d: %v", portConfig.Port, err)
		}
	}

	return nil
}

// backupIptablesRules 备份iptables规则
func (m *Manager) backupIptablesRules() error {
	// 首先将当前iptables规则保存到文件
	backupCmd := "iptables-save > /tmp/iptables_rules"
	if _, err := m.sshClient.ExecuteCommand(backupCmd); err != nil {
		return fmt.Errorf("保存iptables规则失败: %v", err)
	}
	
	// 使用统一的备份管理器备份
	if err := m.backupManager.BackupFile("/tmp/iptables_rules"); err != nil {
		return fmt.Errorf("备份iptables规则失败: %v", err)
	}
	
	return nil
}

// exposePort 暴露端口（从127.0.0.1映射到0.0.0.0）
func (m *Manager) exposePort(port int, description string) error {
	fmt.Printf("🔌 暴露端口 %d (%s)...\n", port, description)

	// 检查端口是否已经在127.0.0.1上监听
	if !m.sshClient.IsPortInUse(port) {
		fmt.Printf("⚠️  端口 %d 未在使用中\n", port)
	}

	// 创建DNAT规则，将外部访问转发到本地
	dnatRule := fmt.Sprintf(
		"iptables -t nat -A PREROUTING -p tcp --dport %d -j DNAT --to-destination 127.0.0.1:%d",
		port, port)

	if _, err := m.sshClient.ExecuteCommand(dnatRule); err != nil {
		return fmt.Errorf("创建DNAT规则失败: %v", err)
	}
	// 立即记录成功的规则
	m.appliedRules = append(m.appliedRules, fmt.Sprintf("nat:PREROUTING:%d", port))

	// 创建FORWARD规则，允许转发
	forwardRule := fmt.Sprintf(
		"iptables -A FORWARD -p tcp --dport %d -j ACCEPT",
		port)

	if _, err := m.sshClient.ExecuteCommand(forwardRule); err != nil {
		return fmt.Errorf("创建FORWARD规则失败: %v", err)
	}
	// 立即记录成功的规则
	m.appliedRules = append(m.appliedRules, fmt.Sprintf("filter:FORWARD:%d", port))

	// 创建MASQUERADE规则，用于源地址转换
	masqueradeRule := fmt.Sprintf(
		"iptables -t nat -A POSTROUTING -p tcp --dport %d -j MASQUERADE",
		port)

	if _, err := m.sshClient.ExecuteCommand(masqueradeRule); err != nil {
		return fmt.Errorf("创建MASQUERADE规则失败: %v", err)
	}
	// 立即记录成功的规则
	m.appliedRules = append(m.appliedRules, fmt.Sprintf("nat:POSTROUTING:%d", port))

	fmt.Printf("✅ 端口 %d 暴露成功\n", port)
	return nil
}

// Cleanup 清理端口转发规则
func (m *Manager) Cleanup() error {
	fmt.Println("🧹 清理端口转发...")

	var cleanupErrors []string
	
	// 清理远程iptables规则
	if err := m.cleanupRemoteRules(); err != nil {
		fmt.Printf("⚠️  清理远程规则失败: %v\n", err)
		cleanupErrors = append(cleanupErrors, fmt.Sprintf("远程规则: %v", err))
	}

	// 如果有清理错误，返回错误
	if len(cleanupErrors) > 0 {
		return fmt.Errorf("清理失败 - %s", strings.Join(cleanupErrors, "; "))
	}

	return nil
}

// cleanupRemoteRules 清理远程规则
func (m *Manager) cleanupRemoteRules() error {
	if m.sshClient == nil {
		fmt.Println("  ⚠️  SSH客户端未初始化，跳过远程规则清理")
		return nil
	}

	if len(m.appliedRules) == 0 {
		fmt.Println("  ℹ️  没有需要清理的远程规则")
		return nil
	}

	// 检查SSH连接状态
	if !m.sshClient.IsConnected() {
		fmt.Println("  ⚠️  SSH连接已断开，尝试重新连接...")
		if err := m.sshClient.TryReconnect(); err != nil {
			fmt.Printf("  ❌ SSH重连失败: %v\n", err)
			fmt.Printf("  ⚠️  跳过远程规则清理 (连接不可用)\n")
			fmt.Printf("  💡 建议: 请手动清理设备上的iptables规则\n")
			
			// 清理本地记录，避免重复尝试
			rulesList := make([]string, len(m.appliedRules))
			copy(rulesList, m.appliedRules)
			m.appliedRules = m.appliedRules[:0]
			
			return fmt.Errorf("SSH连接不可用，跳过 %d 个远程规则清理: %v", len(rulesList), strings.Join(rulesList, ", "))
		}
		fmt.Println("  ✅ SSH重连成功")
	}

	var errors []string
	successCount := 0

	for _, rule := range m.appliedRules {
		parts := strings.Split(rule, ":")
		if len(parts) != 3 {
			continue
		}

		table := parts[0]
		chain := parts[1]
		port := parts[2]

		var deleteCmd string
		switch table {
		case "nat":
			if chain == "PREROUTING" {
				deleteCmd = fmt.Sprintf("iptables -t nat -D PREROUTING -p tcp --dport %s -j DNAT --to-destination 127.0.0.1:%s", port, port)
			} else if chain == "POSTROUTING" {
				deleteCmd = fmt.Sprintf("iptables -t nat -D POSTROUTING -p tcp --dport %s -j MASQUERADE", port)
			}
		case "filter":
			if chain == "FORWARD" {
				deleteCmd = fmt.Sprintf("iptables -D FORWARD -p tcp --dport %s -j ACCEPT", port)
			}
		}

		if deleteCmd != "" {
			fmt.Printf("🔧 执行远程命令: %s\n", deleteCmd)
			if _, err := m.sshClient.ExecuteCommand(deleteCmd); err != nil {
				fmt.Printf("⚠️  删除规则失败: %s\n", deleteCmd)
				errors = append(errors, fmt.Sprintf("规则 %s: %v", rule, err))
			} else {
				fmt.Printf("✅ 删除规则成功: %s\n", rule)
				successCount++
			}
		}
	}

	// 清理规则记录
	m.appliedRules = m.appliedRules[:0]
	
	// 恢复原始的iptables规则
	if m.backupManager != nil {
		if err := m.backupManager.RestoreFile("/tmp/iptables_rules"); err != nil {
			fmt.Printf("⚠️  恢复iptables规则失败: %v\n", err)
		} else {
			// 应用恢复的规则
			if _, err := m.sshClient.ExecuteCommand("iptables-restore < /tmp/iptables_rules"); err != nil {
				fmt.Printf("⚠️  应用恢复的iptables规则失败: %v\n", err)
			}
		}
	}
	
	fmt.Printf("  📊 远程规则清理: 成功 %d, 失败 %d\n", successCount, len(errors))
	
	if len(errors) > 0 {
		return fmt.Errorf("删除 %d 个规则失败: %s", len(errors), strings.Join(errors, "; "))
	}

	return nil
} 