package service

import (
	"fmt"
	"strings"

	"github.com/addx/iot-debug-setup/internal/interfaces"
)

// ServiceManager 服务管理器接口
type ServiceManager interface {
	StartService(serviceName string) error
	EnableService(serviceName string) error
	StartAndEnableService(serviceName string) error
	StartAndEnableServices(serviceNames []string) error
	IsServiceActive(serviceName string) (bool, error)
	IsServiceEnabled(serviceName string) (bool, error)
	RestartService(serviceName string) error
	StopService(serviceName string) error
}

// LinuxServiceManager Linux系统服务管理器
type LinuxServiceManager struct {
	sshExecutor interfaces.SSHExecutor
}

// NewLinuxServiceManager 创建Linux服务管理器
func NewLinuxServiceManager(sshExecutor interfaces.SSHExecutor) ServiceManager {
	return &LinuxServiceManager{
		sshExecutor: sshExecutor,
	}
}

// IsServiceActive 检查服务是否活跃
func (sm *LinuxServiceManager) IsServiceActive(serviceName string) (bool, error) {
	output, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl is-active %s", serviceName))
	if err != nil {
		return false, err
	}
	return strings.TrimSpace(output) == "active", nil
}

// IsServiceEnabled 检查服务是否启用
func (sm *LinuxServiceManager) IsServiceEnabled(serviceName string) (bool, error) {
	output, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl is-enabled %s", serviceName))
	if err != nil {
		return false, err
	}
	return strings.TrimSpace(output) == "enabled", nil
}

// StartService 智能启动服务
func (sm *LinuxServiceManager) StartService(serviceName string) error {
	// 检查服务是否已经在运行
	if active, err := sm.IsServiceActive(serviceName); err == nil && active {
		fmt.Printf("✅ %s 服务已在运行\n", serviceName)
		return nil
	}

	// 服务未运行，启动服务
	fmt.Printf("🔧 执行命令: systemctl start %s\n", serviceName)
	if _, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl start %s", serviceName)); err != nil {
		return fmt.Errorf("启动服务失败 %s: %v", serviceName, err)
	}

	fmt.Printf("✅ %s 服务启动成功\n", serviceName)
	return nil
}

// EnableService 智能启用服务
func (sm *LinuxServiceManager) EnableService(serviceName string) error {
	// 检查服务是否已经启用
	if enabled, err := sm.IsServiceEnabled(serviceName); err == nil && enabled {
		fmt.Printf("✅ %s 服务已启用开机自启\n", serviceName)
		return nil
	}

	// 服务未启用，启用开机自启
	fmt.Printf("🔧 执行命令: systemctl enable %s\n", serviceName)
	if _, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl enable %s", serviceName)); err != nil {
		return fmt.Errorf("启用服务失败 %s: %v", serviceName, err)
	}

	fmt.Printf("✅ %s 服务启用成功\n", serviceName)
	return nil
}

// StartAndEnableService 智能启动并启用服务
func (sm *LinuxServiceManager) StartAndEnableService(serviceName string) error {
	// 启动服务
	if err := sm.StartService(serviceName); err != nil {
		return err
	}

	// 启用服务
	if err := sm.EnableService(serviceName); err != nil {
		return err
	}

	return nil
}

// StartAndEnableServices 批量启动并启用服务
func (sm *LinuxServiceManager) StartAndEnableServices(serviceNames []string) error {
	for _, serviceName := range serviceNames {
		if err := sm.StartAndEnableService(serviceName); err != nil {
			fmt.Printf("⚠️  处理服务失败 %s: %v\n", serviceName, err)
			// 继续处理其他服务，不中断整个流程
		}
	}
	return nil
}

// RestartService 重启服务
func (sm *LinuxServiceManager) RestartService(serviceName string) error {
	fmt.Printf("🔄 重启服务: %s\n", serviceName)
	if _, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl restart %s", serviceName)); err != nil {
		return fmt.Errorf("重启服务失败 %s: %v", serviceName, err)
	}
	fmt.Printf("✅ %s 服务重启成功\n", serviceName)
	return nil
}

// StopService 停止服务
func (sm *LinuxServiceManager) StopService(serviceName string) error {
	fmt.Printf("🛑 停止服务: %s\n", serviceName)
	if _, err := sm.sshExecutor.ExecuteCommand(fmt.Sprintf("systemctl stop %s", serviceName)); err != nil {
		return fmt.Errorf("停止服务失败 %s: %v", serviceName, err)
	}
	fmt.Printf("✅ %s 服务停止成功\n", serviceName)
	return nil
}

// ServiceGroup 服务组管理
type ServiceGroup struct {
	Name     string
	Services []string
	Manager  ServiceManager
}

// NewServiceGroup 创建服务组
func NewServiceGroup(name string, services []string, manager ServiceManager) *ServiceGroup {
	return &ServiceGroup{
		Name:     name,
		Services: services,
		Manager:  manager,
	}
}

// StartAll 启动组内所有服务
func (sg *ServiceGroup) StartAll() error {
	fmt.Printf("🚀 启动%s服务组...\n", sg.Name)
	return sg.Manager.StartAndEnableServices(sg.Services)
}

// StopAll 停止组内所有服务
func (sg *ServiceGroup) StopAll() error {
	fmt.Printf("🛑 停止%s服务组...\n", sg.Name)
	for _, serviceName := range sg.Services {
		if err := sg.Manager.StopService(serviceName); err != nil {
			fmt.Printf("⚠️  停止服务失败 %s: %v\n", serviceName, err)
		}
	}
	return nil
}

// RestartAll 重启组内所有服务
func (sg *ServiceGroup) RestartAll() error {
	fmt.Printf("🔄 重启%s服务组...\n", sg.Name)
	for _, serviceName := range sg.Services {
		if err := sg.Manager.RestartService(serviceName); err != nil {
			fmt.Printf("⚠️  重启服务失败 %s: %v\n", serviceName, err)
		}
	}
	return nil
}

// CheckStatus 检查组内所有服务状态
func (sg *ServiceGroup) CheckStatus() error {
	fmt.Printf("🔍 检查%s服务组状态...\n", sg.Name)
	for _, serviceName := range sg.Services {
		if active, err := sg.Manager.IsServiceActive(serviceName); err == nil {
			if active {
				fmt.Printf("✅ %s: 运行中\n", serviceName)
			} else {
				fmt.Printf("❌ %s: 未运行\n", serviceName)
			}
		} else {
			fmt.Printf("⚠️  %s: 状态检查失败 - %v\n", serviceName, err)
		}
	}
	return nil
}

// 预定义的服务组
var (
	// SAMBA服务组
	SambaServices = []string{"smbd", "nmbd"}
	
	// NFS服务组
	NFSServices = []string{"rpcbind", "nfs-server", "nfs-kernel-server"}
	
	// 常用系统服务
	SystemServices = []string{"sshd", "systemd-resolved", "dbus"}
)

// CreateSambaServiceGroup 创建SAMBA服务组
func CreateSambaServiceGroup(manager ServiceManager) *ServiceGroup {
	return NewServiceGroup("SAMBA", SambaServices, manager)
}

// CreateNFSServiceGroup 创建NFS服务组
func CreateNFSServiceGroup(manager ServiceManager) *ServiceGroup {
	return NewServiceGroup("NFS", NFSServices, manager)
}

// CreateSystemServiceGroup 创建系统服务组
func CreateSystemServiceGroup(manager ServiceManager) *ServiceGroup {
	return NewServiceGroup("系统", SystemServices, manager)
} 