package main

import (
	"time"
	
	"github.com/addx/iot-debug-setup/internal/logger"
)

func main() {
	// 展示新的日志系统
	logger.StartupBanner("IoT调试环境设置工具", "1.0.0")
	
	// 基本日志级别
	logger.Debug("这是调试信息")
	logger.Info("这是一般信息")
	logger.Warn("这是警告信息")
	logger.Error("这是错误信息")
	
	// 功能性日志
	logger.Section("文件共享设置")
	logger.File("创建目录: /mnt/camera_data")
	logger.Progress("正在挂载SAMBA共享")
	logger.Network("连接到设备: *************")
	logger.Success("SAMBA设置完成")
	
	logger.Section("端口转发设置")
	logger.Config("配置端口转发规则")
	logger.Command("执行命令: iptables -t nat -A OUTPUT ...")
	logger.Success("端口转发设置完成")
	
	logger.Section("配置文件更新")
	logger.Config("开始更新配置文件")
	logger.Progress("处理 application.yml")
	logger.Progress("处理 application-test.yml")
	logger.Success("配置文件更新完成")
	
	logger.Section("清理环境")
	logger.Cleanup("清理端口转发规则")
	logger.Cleanup("恢复配置文件")
	logger.Success("环境清理完成")
	
	// 设置不同的日志级别
	logger.Info("\n--- 设置日志级别为 WARN ---")
	logger.SetLevel(logger.WARN)
	
	logger.Debug("这条调试信息不会显示")
	logger.Info("这条信息不会显示")
	logger.Warn("这条警告会显示")
	logger.Error("这条错误会显示")
	
	// 恢复INFO级别
	logger.SetLevel(logger.INFO)
	logger.Success("\n日志系统演示完成！")
} 