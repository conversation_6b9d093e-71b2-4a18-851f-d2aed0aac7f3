package main

import (
	"fmt"
	"runtime"

	"github.com/addx/iot-debug-setup/internal/platform"
	"github.com/addx/iot-debug-setup/internal/types"
)

// 这是一个演示如何使用新的CommandProvider设计的示例
// 展示了重构后的系统如何工作

func main() {
	// 模拟配置
	config := &types.Config{
		TargetDevice: types.TargetDevice{
			IP: "*************",
			SSH: struct {
				Username string `yaml:"username"`
				Password string `yaml:"password"`
				Port     int    `yaml:"port"`
				KeyFile  string `yaml:"key_file"`
			}{
				Username: "root",
				Password: "password",
				Port:     22,
			},
		},
		LocalMachine: types.LocalMachine{
			IP:         "**************",
			MountPoint: "/mnt/camera_data",
		},
		FileSharing: types.FileSharing{
			Type: "samba",
			Directories: []types.ShareDirectory{
				{
					Source:      "/data/sqlite",
					ShareName:   "sqlite_data",
					Description: "SQLite数据库目录",
				},
			},
		},
	}

	// 创建命令提供者
	provider := platform.NewCommandProvider(config)

	// 演示使用方式
	fmt.Printf("🖥️  当前操作系统: %s\n", runtime.GOOS)
	fmt.Printf("🔧 推荐的共享类型: %s\n", provider.GetRecommendedShareType())
	
	// 获取支持的共享类型
	supported := provider.GetSupportedShareTypes()
	fmt.Printf("✅ 支持的共享类型: %v\n", supported)

	// 演示获取挂载命令
	params := platform.CommandParams{
		TargetDeviceIP: config.TargetDevice.IP,
		ShareName:      "sqlite_data",
		MountPoint:     "/mnt/camera_data/sqlite_data",
		Username:       config.TargetDevice.SSH.Username,
		Port:           config.TargetDevice.SSH.Port,
	}

	// 获取SAMBA挂载命令
	if provider.IsSupported(platform.ShareTypeSamba) {
		mountCmd, err := provider.GetCommand(
			platform.ShareTypeSamba,
			platform.CommandTypeMount,
			params,
		)
		if err != nil {
			fmt.Printf("❌ 获取SAMBA挂载命令失败: %v\n", err)
		} else {
			fmt.Printf("📁 SAMBA挂载命令: %s\n", mountCmd)
		}

		// 获取卸载命令
		unmountCmd, err := provider.GetCommand(
			platform.ShareTypeSamba,
			platform.CommandTypeUnmount,
			params,
		)
		if err != nil {
			fmt.Printf("❌ 获取SAMBA卸载命令失败: %v\n", err)
		} else {
			fmt.Printf("🗑️  SAMBA卸载命令: %s\n", unmountCmd)
		}
	}

	// 演示NFS命令
	if provider.IsSupported(platform.ShareTypeNFS) {
		params.ExportPath = "/data/sqlite"
		nfsMount, err := provider.GetCommand(
			platform.ShareTypeNFS,
			platform.CommandTypeMount,
			params,
		)
		if err != nil {
			fmt.Printf("❌ 获取NFS挂载命令失败: %v\n", err)
		} else {
			fmt.Printf("🔗 NFS挂载命令: %s\n", nfsMount)
		}
	}

	// 演示SSHFS命令
	if provider.IsSupported(platform.ShareTypeSSHFS) {
		params.RemotePath = "/data/sqlite"
		sshfsMount, err := provider.GetCommand(
			platform.ShareTypeSSHFS,
			platform.CommandTypeMount,
			params,
		)
		if err != nil {
			fmt.Printf("❌ 获取SSHFS挂载命令失败: %v\n", err)
		} else {
			fmt.Printf("🔐 SSHFS挂载命令: %s\n", sshfsMount)
		}
	} else {
		fmt.Printf("⚠️  当前平台不支持SSHFS\n")
	}

	// 演示如何在实际应用中使用
	fmt.Println("\n📋 实际应用中的使用方式:")
	fmt.Println("1. 创建CommandProvider实例")
	fmt.Println("2. 检查平台支持情况")
	fmt.Println("3. 根据配置获取对应的命令")
	fmt.Println("4. 执行命令并处理结果")
	fmt.Println("5. 清理时使用卸载命令")

	// 演示错误处理
	fmt.Println("\n🚨 错误处理示例:")
	_, err := provider.GetCommand(
		platform.ShareTypeSSHFS,
		platform.CommandTypeMount,
		params,
	)
	if err != nil {
		fmt.Printf("✅ 正确处理不支持的情况: %v\n", err)
	}
}

// 演示如何在文件共享管理器中使用
func demonstrateManagerUsage() {
	fmt.Println("\n📂 文件共享管理器使用示例:")
	fmt.Println("```go")
	fmt.Println("// 在Manager中使用CommandProvider")
	fmt.Println("type SambaManager struct {")
	fmt.Println("    config          *types.Config")
	fmt.Println("    commandProvider *platform.CommandProvider")
	fmt.Println("}")
	fmt.Println("")
	fmt.Println("func (s *SambaManager) Mount(shareName, localPath string) error {")
	fmt.Println("    // 检查平台支持")
	fmt.Println("    if !s.commandProvider.IsSupported(platform.ShareTypeSamba) {")
	fmt.Println("        return fmt.Errorf(\"不支持SAMBA\")")
	fmt.Println("    }")
	fmt.Println("")
	fmt.Println("    // 获取命令")
	fmt.Println("    params := platform.CommandParams{...}")
	fmt.Println("    cmd, err := s.commandProvider.GetCommand(")
	fmt.Println("        platform.ShareTypeSamba,")
	fmt.Println("        platform.CommandTypeMount,")
	fmt.Println("        params,")
	fmt.Println("    )")
	fmt.Println("    if err != nil {")
	fmt.Println("        return err")
	fmt.Println("    }")
	fmt.Println("")
	fmt.Println("    // 执行命令")
	fmt.Println("    return s.executeCommand(cmd)")
	fmt.Println("}")
	fmt.Println("```")
} 