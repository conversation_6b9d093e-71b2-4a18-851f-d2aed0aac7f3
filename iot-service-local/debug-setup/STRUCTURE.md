# Debug Setup 项目结构

## 目录结构说明

```
debug-setup/
├── cmd/                    # 主要的应用程序入口点
├── internal/              # 内部包和库
│   ├── service/           # 抽象服务管理层
│   │   └── manager.go     # 服务管理器接口和实现
│   ├── fileshare/         # 文件共享管理器
│   ├── nfs/               # NFS管理器
│   ├── ssh/               # SSH客户端
│   └── utils/             # 工具类
├── configs/               # 配置文件
├── examples/              # 示例代码和用法演示
│   └── example_usage.go   # CommandProvider使用示例
├── tests/                 # 测试文件
│   ├── test_install_check.go  # 安装检查测试
│   ├── test_permission_solutions.go  # 权限解决方案测试
│   ├── test_service_smart_start.go   # 服务智能启动测试
│   └── test_refactored_service_management.go  # 重构后的服务管理测试
├── scripts/               # 脚本文件
│   ├── build.sh          # 构建脚本
│   ├── test_mount_creation.sh  # 挂载点创建测试脚本
│   ├── fix_permission_issues.sh   # 权限问题修复脚本 (Linux/macOS)
│   └── fix_permission_issues.ps1  # 权限问题修复脚本 (Windows)
├── prompt/                # 提示和文档
├── dist/                  # 构建输出目录
├── go.mod                 # Go模块文件
├── go.sum                 # Go依赖校验文件
├── Makefile              # 构建和任务管理
├── README.md             # 项目说明文档
├── STRUCTURE.md          # 项目结构说明
├── SOLUTIONS.md          # 问题解决方案总结
└── REFACTORING_SUMMARY.md # 重构总结文档
```

## 文件分类说明

### 📁 examples/
- **用途**: 存放示例代码和用法演示
- **内容**: 展示如何使用项目的各个组件
- **目标用户**: 新用户学习和参考

### 📁 tests/
- **用途**: 存放测试文件和验证脚本
- **内容**: 单元测试、集成测试、功能验证
- **目标用户**: 开发者和CI/CD系统

### 📁 scripts/
- **用途**: 存放构建、部署和维护脚本
- **内容**: shell脚本、构建脚本、自动化工具
- **目标用户**: 开发者和运维人员

### 📁 internal/
- **用途**: 内部包和库，不对外暴露
- **内容**: 核心业务逻辑、工具函数
- **目标用户**: 项目内部使用

### 📁 cmd/
- **用途**: 应用程序入口点
- **内容**: main函数、CLI工具
- **目标用户**: 最终用户

## 使用建议

1. **新用户**: 先查看 `examples/` 目录了解用法
2. **开发者**: 运行 `tests/` 目录中的测试验证功能
3. **构建**: 使用 `scripts/build.sh` 或 `make` 命令构建项目
4. **配置**: 修改 `configs/` 目录中的配置文件
5. **重构了解**: 查看 `REFACTORING_SUMMARY.md` 了解架构改进
6. **服务管理**: 使用 `internal/service/` 包进行服务管理

## 重构改进

### 🎯 服务管理重构
项目进行了大规模的服务管理重构，从面向过程转向面向对象：

- **抽象层**: 创建了 `internal/service/` 包，提供统一的服务管理接口
- **消除重复**: 移除了各个管理器中的重复服务启动代码
- **服务组管理**: 支持批量管理相关服务（如SAMBA、NFS服务组）
- **智能判断**: 保持原有的智能启动功能，避免重复操作

### 🔧 架构改进
- **接口抽象**: `ServiceManager` 接口提供统一的服务管理方法
- **依赖注入**: 各管理器通过接口依赖服务管理器
- **组合模式**: `ServiceGroup` 支持批量服务管理
- **策略模式**: 支持不同平台的服务管理策略

详细信息请参阅 `REFACTORING_SUMMARY.md`。

## 最佳实践

- 保持目录结构清晰
- 新增示例代码放入 `examples/`
- 新增测试文件放入 `tests/`
- 脚本文件统一放入 `scripts/`
- 遵循Go项目标准结构
- 使用抽象接口而非具体实现
- 优先使用组合而非继承 