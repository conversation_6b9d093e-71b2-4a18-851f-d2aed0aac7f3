需求背景：iot-service-local是运行在linux设备里的一个springboot程序，主要是作为摄像机服务的server端，使用docker打包并运行，但是想要调试就会变得麻烦，试过了开启5005调试端口，
但是由于linux设备（录像机）性能比较差，光光启动就花了十几分钟的时间，而且如果使用远程调试，没有办法做到代码热部署
提出改进想法：使用本地的idea，把iot-service-local运行起来，但是由于有一些依赖关系，下面我列几点出来，一一处理
1. sqlite数据库，录像文件在linux设备（录像机）上，所以可以把相关的文件或者路径使用nfs暴露到局域网中，本地电脑再挂载这个nfs路径即可。可以在配置文件中列出来哪些文件/文件夹需要暴露到nfs
2. iot-service-local需要与其他进程进行网络通讯，录像机中的其他进程的端口号都是绑定在127.0.0.1上，所以如果我在本机上启动iot-service-local，那么无法与其他进程进行网络通讯，
我能想到的解决方案是，使用iptables，将这些绑定在127.0.0.1的ip地址上的端口都重新映射到0.0.0.0上，并且让原先访问iot-service-local的端口号，
比如127.0.0.1:7777转发到我本机的电脑上，这个也可以作为一个配置文件，配置哪些录像机上的端口是要暴露到0.0.0.0上，哪些访问127.0.0.1的流量又需要转发到我本机电脑上
3. 对于iot-service-local这个程序的application.yml需要手动维护的问题。在基于1，2两点，如果都能使用脚本实现自动化，那么我想让配置application.yml也实现自动化，我的想法是，
以配置文件的方式事先给出需要自动化配置的属性，比如spring.redis.host，原先这个属性填写的127.0.0.1，那么在运行自动化脚本后，这里被替换为入参中的目标地址ip，也就是录像机的局域网ip地址，比如*************，
再比如，spring.datasource.url，原先这里的值打个比方为*************************************************************************************************************************************挂载到本机上的路径
完成这些脚本的功能后，我就可以在idea->Run/Debug Configurations->Modify options->add before launch task，将脚本与参数添加上，在点击debug图标，将可以实现
1. 启动deubg后，开始自动远程登录录像机，根据配置文件提供的内容，暴露nfs
2. ssh远程登录录像机，将绑定在127.0.0.1上的端口号全部重新映射到0.0.0.0上
3. 根据配置文件提供的内容，修改application.yml，使其能够设置正确的ip地址或者文件路径

以上需求要编写的脚本 windows/linux/mac都需要实现，比如windows自动挂载nfs和linux/mac肯定是不一样的，所以我在想是不是不能用shell脚本去实现，shell可读性又很差，不便于维护，
最好是能用面向对象的语言去实现？比如go语言？我在这里只是提个建议而已，你有什么好的建议吗？