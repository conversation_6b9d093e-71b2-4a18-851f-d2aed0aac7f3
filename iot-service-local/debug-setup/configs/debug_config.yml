# 调试环境自动化配置文件
# 支持Windows/Linux/Mac跨平台使用

# 目标录像机配置
target_device:
  # 录像机IP地址
  ip: "*************"
  # SSH连接配置
  ssh:
    username: "root"
    password: "addx-beijing~2018"
    port: 22
    # 或者使用密钥文件
    # key_file: "/path/to/private/key"

# 本地开发机配置
local_machine:
  # 本地IP地址（用于端口转发）
  ip: "**************"
  # 文件共享挂载点
  mount_point: "/mnt/camera_data"  # Linux/Mac/WSL
  # Windows: "Z:\\"

# 文件共享配置 - 需要从录像机暴露的文件和目录
file_sharing:
  # 共享类型: samba(默认,推荐), nfs, sshfs
  type: "samba"
  
  # 共享目录列表
  directories:
    - source: "/data/sqlite"           # 录像机上的SQLite数据库目录
      share_name: "sqlite"             # 共享名称（修正：与本地期望路径匹配）
      description: "SQLite数据库目录"
      options: "valid users = guest"
    - source: "/data/iot-service"      # 录像机上的IoT服务数据目录
      share_name: "iot-service"        # 共享名称（修正：与本地期望路径匹配）
      description: "IoT服务数据目录"
      options: "valid users = guest"
    - source: "/tmp/ssd"               # 录像机上的SSD存储目录
      share_name: "ssd"                # 共享名称（修正：与本地期望路径匹配）
      description: "SSD存储目录"
      options: "valid users = guest"
    - source: "/addx/temp"             # 录像机上的临时目录
      share_name: "temp"               # 共享名称（修正：与本地期望路径匹配）
      description: "临时文件目录"
      options: "valid users = guest"
    - source: "/addx/firmware"         # 录像机上的固件目录
      share_name: "firmware"           # 共享名称（保持不变，已匹配）
      description: "固件目录"
      options: "valid users = guest"
  
  # 全局选项（可选）
  options:
    workgroup: "WORKGROUP"
    security: "user"
    guest_ok: "yes"

# 端口转发配置
port_forwarding:
  # 录像机上需要从127.0.0.1映射到0.0.0.0的端口
  expose_ports:
    - port: 6379
      description: "Redis服务"
    - port: 50031
      description: "AI服务gRPC端口"
    - port: 51003
      description: "基站服务gRPC端口"
    - port: 51004
      description: "Alexa gRPC服务端口"

  # 需要从录像机转发到本地的端口
  forward_to_local:
    - remote_port: 7777
      local_port: 7777
      description: "IoT服务HTTP端口"

# 配置文件替换规则
config_replacements:
  # 项目根目录（绝对路径）
  # Windows格式: "C:/Users/<USER>/Project/java/iot-service-unified/iot-service-local"
  # WSL中会自动转换为: "/mnt/c/Users/<USER>/Project/java/iot-service-unified/iot-service-local"
  project_root: "C:/Users/<USER>/Project/java/iot-service-unified/iot-service-local"
  
  # 当前激活的profile（可选）
  # 如果设置了此字段，会优先使用对应的profile文件
  # 例如: "test" 对应 application-test.yml
  #      "dev" 对应 application-dev.yml
  #      如果为空或不设置，则自动检测可用的profile文件
  active_profile: "test"
  
  # 程序会自动根据project_root构建以下路径：
  # - {project_root}/src/main/resources/application.yml
  # - {project_root}/src/main/resources/application-{active_profile}.yml
  
  # 替换规则列表
  replacements:
    # Redis配置
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
    
    # 数据库配置
    - key: "spring.datasource.url"
      value: "jdbc:sqlite:{{.local_mount_path}}/sqlite/camera.db?journal_mode=WAL&busy_timeout=30000"
      description: "主数据库连接"
    
    - key: "spring.datasource.dynamic.datasource.camera.url"
      value: "jdbc:sqlite:{{.local_mount_path}}/sqlite/camera.db?journal_mode=WAL&busy_timeout=30000"
      description: "摄像头数据库连接"
    
    - key: "spring.datasource.dynamic.datasource.library.url"
      value: "jdbc:sqlite:{{.local_mount_path}}/sqlite/library.db?journal_mode=WAL&busy_timeout=30000"
      description: "录像库数据库连接"
    
    # gRPC服务配置
    - key: "grpc.saas-ai-service"
      value: "{{.target_device.ip}}:50031"
      description: "AI服务地址"
    
    - key: "grpc.bstationd-service"
      value: "{{.target_device.ip}}:51003"
      description: "基站服务地址"
      
    - key: "grpc.client.GLOBAL.address"
      value: "static://{{.target_device.ip}}:51003"
      description: "全局gRPC客户端地址"
    
    - key: "grpc.client.alexa-grpc-server.address"
      value: "static://{{.target_device.ip}}:51004"
      description: "Alexa gRPC服务地址"
    
    - key: "grpc.client.ai-station-grpc-server.address"
      value: "static://{{.target_device.ip}}:50031"
      description: "AI站点gRPC服务地址"
    
    # 文件存储路径配置
    - key: "video-file.storage.rootPath"
      value: "{{.local_mount_path}}/iot-service/"
      description: "视频文件存储根路径"
    
    - key: "video-file.extStorage.rootPath"
      value: "{{.local_mount_path}}/ssd/"
      description: "外部存储路径"
    
    - key: "video-config.storage.rootPath"
      value: "{{.local_mount_path}}/iot-service/"
      description: "视频配置存储路径"
    
    - key: "tempDir"
      value: "{{.local_mount_path}}/temp/"
      description: "临时目录"
    
    - key: "storage.firmwareDir"
      value: "{{.local_mount_path}}/firmware"
      description: "固件目录"
    
    - key: "video-file.uploadAIImage.repository"
      value: "{{.local_mount_path}}/iot-service/temp/uploadAIImage"
      description: "AI图片上传仓库"
    
    # 服务地址配置
    - key: "iot-service.rootPath"
      value: "http://{{.target_device.ip}}:7777"
      description: "IoT服务根路径"
    
    - key: "ai-service.rootPath"
      value: "http://{{.target_device.ip}}:8888"
      description: "AI服务根路径"
    
    # 服务器地址配置
    - key: "server.address"
      value: "0.0.0.0"
      description: "服务器绑定地址"

# Docker容器控制配置
docker_control:
  # 是否启用Docker容器控制
  enabled: false
  
  # 容器配置列表
  containers:
    - name: "iot-service"
      action_on_start: "stop"     # 启动时停止容器
      action_on_cleanup: "start"  # 清理时启动容器
      description: "IoT服务容器"
    
    - name: "ai-service"
      action_on_start: "stop"
      action_on_cleanup: "start"
      description: "AI服务容器"

# 执行步骤控制
execution_steps:
  # 设置文件共享（NFS/SAMBA/SSHFS）
  setup_file_sharing: true
  
  # 设置端口转发
  setup_port_forwarding: true
  
  # 更新配置文件
  update_config_file: true
  
  # 设置Docker容器控制
  setup_docker_control: false
  
  # 退出时自动清理
  cleanup_on_exit: true 