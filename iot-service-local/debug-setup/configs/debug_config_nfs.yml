# IoT调试环境自动化设置工具配置文件 - NFS版本
# 当目标设备支持NFS时可以使用此配置

# 目标设备配置
target_device:
  # 录像机IP地址
  ip: "*************"
  # SSH连接配置
  ssh:
    username: "root"
    password: "addx-beijing~2018"
    port: 22
    # 或者使用密钥文件
    # key_file: "/path/to/private/key"

# 本地开发机配置
local_machine:
  # 本地IP地址（用于端口转发）
  ip: "**************"
  # 文件共享挂载点
  mount_point: "/mnt/camera_data"  # Linux/Mac
  # Windows: "Z:\\"

# 文件共享配置 - 使用NFS
file_sharing:
  # 共享类型: nfs
  type: "nfs"
  
  # 共享目录列表
  directories:
    - source: "/data/sqlite"           # 录像机上的SQLite数据库目录
      share_name: "/data/sqlite"       # NFS导出路径
      description: "SQLite数据库目录"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/data/iot-service"      # 录像机上的IoT服务数据目录
      share_name: "/data/iot-service"
      description: "IoT服务数据目录"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/tmp/ssd"               # 录像机上的SSD存储目录
      share_name: "/tmp/ssd"
      description: "SSD存储目录"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/addx/temp"             # 录像机上的临时目录
      share_name: "/addx/temp"
      description: "临时文件目录"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/addx/firmware"         # 录像机上的固件目录
      share_name: "/addx/firmware"
      description: "固件目录"
      options: "rw,sync,no_subtree_check,no_root_squash"

# 端口转发配置
port_forwarding:
  # 录像机上需要从127.0.0.1映射到0.0.0.0的端口
  expose_ports:
    - port: 6379
      description: "Redis服务"
    - port: 50031
      description: "AI服务gRPC端口"
    - port: 51003
      description: "基站服务gRPC端口"
    - port: 51004
      description: "Alexa gRPC服务端口"

  # 需要从录像机转发到本地的端口
  forward_to_local:
    - remote_port: 7777
      local_port: 7777
      description: "应用服务HTTP端口"

# 配置文件替换规则
config_replacements:
  # 支持多个配置文件，Spring Boot会按优先级加载
  file_paths:
    - "iot-service-local/src/main/resources/application.yml"
    - "iot-service-local/src/main/resources/application-test.yml"
  
  # 向后兼容单个文件路径（如果没有配置file_paths，则使用此字段）
  file_path: "iot-service-local/src/main/resources/application.yml"
  
  # 替换规则列表
  replacements:
    # Redis配置
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
    
    # 数据库配置
    - key: "spring.datasource.url"
      value: "**********************************************************************"
      description: "主数据库连接"
    
    - key: "spring.datasource.dynamic.datasource.library.url"
      value: "***********************************************************************************"
      description: "Library数据库连接"
    
    # gRPC服务地址
    - key: "grpc.saas-ai-service"
      value: "{{.target_device.ip}}:50031"
      description: "AI服务gRPC地址"
    
    - key: "grpc.client.GLOBAL.address"
      value: "static://{{.target_device.ip}}:51003"
      description: "全局gRPC客户端地址"
    
    - key: "grpc.client.alexa-grpc-server.address"
      value: "static://{{.target_device.ip}}:51004"
      description: "Alexa gRPC服务地址"

# 平台特定配置
platform_config:
  windows:
    # NFS命令
    nfs_mount_command: "echo 'Windows NFS不推荐，建议使用SAMBA'"
    nfs_unmount_command: "echo 'Windows NFS不推荐，建议使用SAMBA'"
    
    # SAMBA命令（备用）
    samba_mount_command: 'net use "{{.mount_point}}" "\\\\{{.target_device.ip}}\\{{.share_name}}" /persistent:no'
    samba_unmount_command: 'net use "{{.mount_point}}" /delete'
    
  linux:
    # NFS命令
    nfs_mount_command: "sudo mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "sudo umount {{.mount_point}}"
    
    # SAMBA命令（备用）
    samba_mount_command: "sudo mount -t cifs //{{.target_device.ip}}/{{.share_name}} {{.mount_point}} -o guest,uid=$(id -u),gid=$(id -g),iocharset=utf8"
    samba_unmount_command: "sudo umount {{.mount_point}}"
    
  darwin:
    # NFS命令
    nfs_mount_command: "sudo mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "sudo umount {{.mount_point}}"
    
    # SAMBA命令（备用）
    samba_mount_command: "mount -t smbfs //guest@{{.target_device.ip}}/{{.share_name}} {{.mount_point}}"
    samba_unmount_command: "umount {{.mount_point}}"

# 执行步骤配置
execution_steps:
  # 是否启用各个步骤
  setup_file_sharing: true
  setup_port_forwarding: true
  update_config_file: true
  
  # 清理步骤（在程序结束时执行）
  cleanup_on_exit: true 