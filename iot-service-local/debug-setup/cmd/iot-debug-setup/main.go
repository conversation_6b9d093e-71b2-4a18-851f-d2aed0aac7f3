package main

import (
	"fmt"
	"os"
	"runtime"

	"github.com/addx/iot-debug-setup/internal/config"
	"github.com/addx/iot-debug-setup/internal/diagnosis"
	"github.com/addx/iot-debug-setup/internal/errors"
	"github.com/addx/iot-debug-setup/internal/factory"
	"github.com/addx/iot-debug-setup/internal/logger"
	"github.com/addx/iot-debug-setup/internal/types"
)

// 版本信息
var (
	Version   = "1.0.0"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	// 处理命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "--version", "-v":
			showVersion()
			return
		case "--help", "-h":
			showHelp()
			return
		case "--diagnose", "-d":
			if len(os.Args) < 3 {
				logger.Error("诊断模式需要配置文件参数")
				logger.Info("使用方法: iot-debug-setup --diagnose <config_file>")
				os.Exit(1)
			}
			runDiagnose(os.Args[2])
			return
		case "--cleanup", "-c":
			if len(os.Args) < 3 {
				logger.Error("清理模式需要配置文件参数")
				logger.Info("使用方法: iot-debug-setup --cleanup <config_file>")
				os.Exit(1)
			}
			runCleanup(os.Args[2])
			return
		}
	}

	// 检查配置文件参数
	if len(os.Args) < 2 {
		fmt.Println("❌ 错误: 缺少配置文件参数")
		fmt.Println()
		showHelp()
		os.Exit(1)
	}

	// 运行主程序
	if err := runMain(os.Args[1]); err != nil {
		setupErr := errors.AnalyzeError(err, "环境设置")
		errors.PrintDetailedError(setupErr)
		os.Exit(1)
	}
}

// runMain 运行主程序
func runMain(configPath string) error {
	// 加载配置
	cfg, err := config.LoadConfigFromPath(configPath)
	if err != nil {
		return fmt.Errorf("配置加载失败: %v", err)
	}

	// 创建管理器集合
	managerSet, err := factory.CreateFullManagerSet(cfg)
	if err != nil {
		return fmt.Errorf("创建管理器集合失败: %v", err)
	}
	defer managerSet.Cleanup()

	// 显示启动信息
	logger.StartupBanner("IoT调试环境设置工具", Version)
	logger.Info("目标设备: %s", cfg.TargetDevice.IP)
	logger.Info("本地机器: %s", cfg.LocalMachine.IP)
	logger.Info("操作系统: %s", runtime.GOOS)
	logger.Network("已连接到录像机: %s", cfg.TargetDevice.IP)

	// 执行设置步骤
	if err := executeSetupSteps(managerSet, cfg); err != nil {
		return err
	}

	logger.Success("IoT调试环境设置完成！")
	logger.Info("您现在可以开始调试了")
	return nil
}

// executeSetupSteps 执行设置步骤
func executeSetupSteps(managerSet *factory.ManagerSet, cfg *types.Config) error {
	// 1. 设置文件共享
	if cfg.ExecutionSteps.SetupFileSharing {
		if err := setupFileSharing(managerSet, cfg); err != nil {
			return err
		}
	}

	// 2. 设置端口转发
	if cfg.ExecutionSteps.SetupPortForwarding {
		if err := setupPortForwarding(managerSet); err != nil {
			return err
		}
	}

	// 3. 更新配置文件
	if cfg.ExecutionSteps.UpdateConfigFile {
		if err := updateConfigFile(managerSet); err != nil {
			return err
		}
	}

	// 4. 设置Docker控制
	if cfg.ExecutionSteps.SetupDockerControl {
		if err := setupDockerControl(managerSet); err != nil {
			return err
		}
	}

	return nil
}

// setupFileSharing 设置文件共享
func setupFileSharing(managerSet *factory.ManagerSet, cfg *types.Config) error {
	logger.Section(fmt.Sprintf("设置%s文件共享", managerSet.FileShareManager.GetShareType()))
	
	// 设置远程服务器
	if err := managerSet.FileShareManager.SetupServer(); err != nil {
		shareType := managerSet.FileShareManager.GetShareType()
		return errors.NewSetupError(
			getFileShareErrorType(shareType),
			fmt.Sprintf("%s文件共享服务器设置", shareType),
			"远程服务器配置失败",
			err,
		)
	}
	
	// 在本地挂载
	if err := managerSet.FileShareManager.MountOnLocal(); err != nil {
		shareType := managerSet.FileShareManager.GetShareType()
		return errors.NewSetupError(
			getFileShareErrorType(shareType),
			fmt.Sprintf("%s文件共享本地挂载", shareType),
			"本地挂载失败",
			err,
		)
	}
	
	return nil
}

// setupPortForwarding 设置端口转发
func setupPortForwarding(managerSet *factory.ManagerSet) error {
	logger.Section("设置端口转发")
	if err := managerSet.PortManager.SetupPortForwarding(); err != nil {
		return errors.NewSetupError(
			errors.ErrorTypePortForwarding,
			"端口转发设置",
			"端口转发配置失败",
			err,
		)
	}
	return nil
}

// updateConfigFile 更新配置文件
func updateConfigFile(managerSet *factory.ManagerSet) error {
	logger.Section("更新配置文件")
	if err := managerSet.ConfigManager.UpdateConfigFile(); err != nil {
		return errors.NewSetupError(
			errors.ErrorTypeConfig,
			"配置文件更新",
			"配置文件修改失败",
			err,
		)
	}
	return nil
}

// setupDockerControl 设置Docker控制
func setupDockerControl(managerSet *factory.ManagerSet) error {
	fmt.Println("\n🐳 设置Docker容器控制...")
	if err := managerSet.DockerManager.SetupDockerControl(); err != nil {
		return errors.NewSetupError(
			errors.ErrorTypeDocker,
			"Docker控制设置",
			"Docker容器控制失败",
			err,
		)
	}
	return nil
}

// runDiagnose 运行诊断模式
func runDiagnose(configPath string) {
	fmt.Println("🔍 启动IoT调试环境诊断模式...")

	// 加载配置
	cfg, err := config.LoadConfigFromPath(configPath)
	if err != nil {
		fmt.Printf("❌ 错误: %v\n", err)
		return
	}

	// 创建诊断管理器集合
	managerSet, err := factory.CreateDiagnosisManagerSet(cfg)
	if err != nil {
		fmt.Printf("❌ 错误: %v\n", err)
		return
	}
	defer managerSet.Cleanup()

	// 执行诊断
	runDiagnosisWithManagers(managerSet, cfg)
}

// runDiagnosisWithManagers 使用管理器运行诊断
func runDiagnosisWithManagers(managerSet *factory.ManagerSet, cfg *types.Config) {
	fmt.Println()
	fmt.Println("📊 系统诊断报告")
	fmt.Println("================================================================================")
	
	// 基础连接信息
	fmt.Printf("🎯 目标设备: %s\n", cfg.TargetDevice.IP)
	fmt.Printf("📁 文件共享类型: %s\n", managerSet.FileShareManager.GetShareType())
	fmt.Printf("🐳 Docker控制: %s\n", managerSet.DiagnosisManager.GetEnabledStatus(cfg.DockerControl.Enabled))
	fmt.Println()
	
	// 收集诊断结果
	var results []diagnosis.DiagnosisTableRow
	
	// SSH连接状态
	results = append(results, diagnosis.DiagnosisTableRow{
		Component: "SSH连接",
		Status:    "✅ 正常",
		Detail:    fmt.Sprintf("%s:%d", cfg.TargetDevice.IP, cfg.TargetDevice.SSH.Port),
	})
	
	// 文件共享诊断
	shareStatus := managerSet.DiagnosisManager.DiagnoseFileShare(managerSet.FileShareManager)
	results = append(results, diagnosis.DiagnosisTableRow{
		Component: fmt.Sprintf("%s文件共享", managerSet.FileShareManager.GetShareType()),
		Status:    shareStatus.Status,
		Detail:    shareStatus.Detail,
	})
	
	// Docker诊断
	dockerStatus := managerSet.DiagnosisManager.DiagnoseDockerService(managerSet.DockerManager)
	results = append(results, diagnosis.DiagnosisTableRow{
		Component: "Docker服务",
		Status:    dockerStatus.Status,
		Detail:    dockerStatus.Detail,
	})
	
	// 容器状态诊断
	for _, container := range cfg.DockerControl.Containers {
		containerStatus := managerSet.DiagnosisManager.DiagnoseContainer(managerSet.DockerManager, container.Name)
		results = append(results, diagnosis.DiagnosisTableRow{
			Component: fmt.Sprintf("容器: %s", truncateString(container.Name, 15)),
			Status:    containerStatus.Status,
			Detail:    containerStatus.Detail,
		})
	}
	
	// 生成诊断表格
	managerSet.DiagnosisManager.GenerateDiagnosisTable(results)
	fmt.Println()
	
	// 打印建议
	managerSet.DiagnosisManager.PrintRecommendations(shareStatus, dockerStatus)
}

// runCleanup 运行清理模式
func runCleanup(configPath string) {
	fmt.Println("🧹 启动清理模式...")

	// 加载配置
	cfg, err := config.LoadConfigFromPath(configPath)
	if err != nil {
		fmt.Printf("❌ 错误: %v\n", err)
		return
	}

	// 创建清理管理器集合
	managerSet, err := factory.CreateCleanupManagerSet(cfg)
	if err != nil {
		fmt.Printf("❌ 错误: %v\n", err)
		return
	}
	defer managerSet.Cleanup()

	// 执行清理
	managerSet.CleanupManager.ExecuteStandardCleanup()
}

// 工具函数

// showVersion 显示版本信息
func showVersion() {
	fmt.Printf("IoT Debug Setup v%s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Printf("Git Commit: %s\n", GitCommit)
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("IoT调试环境自动化设置工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Printf("  %s <配置文件路径>\n", os.Args[0])
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -h, --help      显示此帮助信息")
	fmt.Println("  -v, --version   显示版本信息")
	fmt.Println("  -d, --diagnose  运行文件共享诊断模式")
	fmt.Println("  -c, --cleanup   清理设置（端口转发、文件共享、配置文件）")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Printf("  %s configs/debug_config.yml\n", os.Args[0])
	fmt.Printf("  %s my_config.yml\n", os.Args[0])
	fmt.Printf("  %s --cleanup configs/debug_config.yml\n", os.Args[0])
	fmt.Printf("  %s --diagnose configs/debug_config.yml\n", os.Args[0])
	fmt.Println()
	fmt.Println("功能:")
	fmt.Println("  • 自动设置文件共享（SAMBA/NFS/SSHFS）")
	fmt.Println("  • 配置端口转发和iptables规则")
	fmt.Println("  • 自动修改application.yml配置文件")
	fmt.Println("  • 支持Windows、Linux、macOS跨平台使用")
	fmt.Println("  • 独立的清理功能，不删除已安装的软件")
	fmt.Println()
	fmt.Println("更多信息请查看: https://github.com/addx/iot-debug-setup")
}

// getFileShareErrorType 获取文件共享错误类型
func getFileShareErrorType(shareType string) errors.ErrorType {
	switch shareType {
	case "samba":
		return errors.ErrorTypeSamba
	case "nfs":
		return errors.ErrorTypeNFS
	case "sshfs":
		return errors.ErrorTypeSSHFS
	default:
		return errors.ErrorTypeGeneric
	}
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
} 