# IoT Debug Setup Tool Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build parameters
BINARY_NAME=iot-debug-setup
BINARY_UNIX=$(BINARY_NAME)_unix
BINARY_WINDOWS=$(BINARY_NAME).exe
MAIN_PATH=./cmd/iot-debug-setup

# Version info
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT ?= $(shell git rev-parse HEAD 2>/dev/null || echo "unknown")

# Linker flags
LDFLAGS=-ldflags "-s -w -X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# Build output directory
BUILD_DIR=dist

.PHONY: all build build-all clean test deps help install uninstall fmt vet run

# Default target
all: clean test build

# Help
help:
	@echo "IoT Debug Setup Tool - 构建命令"
	@echo ""
	@echo "使用方法:"
	@echo "  make <target>"
	@echo ""
	@echo "目标:"
	@echo "  build       构建当前平台的可执行文件"
	@echo "  build-all   构建所有平台的可执行文件"
	@echo "  test        运行测试"
	@echo "  clean       清理构建文件"
	@echo "  deps        下载依赖"
	@echo "  fmt         格式化代码"
	@echo "  vet         检查代码"
	@echo "  run         运行程序"
	@echo "  install     安装到系统PATH"
	@echo "  uninstall   从系统PATH卸载"
	@echo "  help        显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build                    # 构建当前平台"
	@echo "  make build-all               # 构建所有平台"
	@echo "  make run ARGS=config.yml     # 运行程序"

# Build for current platform
build: deps
	@echo "🔨 构建 $(VERSION) for $(shell go env GOOS)/$(shell go env GOARCH)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ 构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# Build for all platforms
build-all: deps
	@echo "🔨 构建所有平台..."
	@chmod +x scripts/build.sh
	@./scripts/build.sh --all

# Build for Linux
build-linux: deps
	@echo "🔨 构建 Linux 版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_UNIX) $(MAIN_PATH)

# Build for Windows
build-windows: deps
	@echo "🔨 构建 Windows 版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_WINDOWS) $(MAIN_PATH)

# Build for macOS
build-darwin: deps
	@echo "🔨 构建 macOS 版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)_darwin $(MAIN_PATH)

# Run tests
test:
	@echo "🧪 运行测试..."
	$(GOTEST) -v ./...

# Clean build files
clean:
	@echo "🧹 清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@rm -f iot-debug-setup-release*

# Download dependencies
deps:
	@echo "📦 下载依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
fmt:
	@echo "📝 格式化代码..."
	@go fmt ./...

# Vet code
vet:
	@echo "🔍 检查代码..."
	@go vet ./...

# Run the application
run: build
	@if [ "$(ARGS)" = "" ]; then \
		echo "❌ 请提供配置文件参数: make run ARGS=config.yml"; \
		exit 1; \
	fi
	@echo "🚀 运行程序..."
	@./$(BUILD_DIR)/$(BINARY_NAME) $(ARGS)

# Install to system PATH
install: build
	@echo "📦 安装到系统PATH..."
	@if [ "$(shell uname)" = "Darwin" ] || [ "$(shell uname)" = "Linux" ]; then \
		sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/; \
		echo "✅ 已安装到 /usr/local/bin/$(BINARY_NAME)"; \
	else \
		echo "❌ 不支持的操作系统"; \
		exit 1; \
	fi

# Uninstall from system PATH
uninstall:
	@echo "🗑️  从系统PATH卸载..."
	@if [ "$(shell uname)" = "Darwin" ] || [ "$(shell uname)" = "Linux" ]; then \
		sudo rm -f /usr/local/bin/$(BINARY_NAME); \
		echo "✅ 已从 /usr/local/bin 卸载"; \
	else \
		echo "❌ 不支持的操作系统"; \
		exit 1; \
	fi

# Development workflow
dev: clean fmt vet test build

# Release workflow
release: clean test build-all
	@echo "📦 创建发布包..."
	@chmod +x scripts/build.sh
	@./scripts/build.sh --release

# Check dependencies
check-deps:
	@echo "🔍 检查依赖..."
	@which go > /dev/null || (echo "❌ Go未安装" && exit 1)
	@echo "✅ Go版本: $$(go version)"

# Show build info
info:
	@echo "📊 构建信息:"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  Go版本: $$(go version)"
	@echo "  目标平台: $$(go env GOOS)/$$(go env GOARCH)"

# Docker build (if needed)
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker build -t iot-debug-setup:$(VERSION) .

# Generate Go mod graph
mod-graph:
	@echo "📊 生成依赖图..."
	$(GOMOD) graph

# Update dependencies
update-deps:
	@echo "⬆️  更新依赖..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# Benchmark tests
bench:
	@echo "⚡ 运行性能测试..."
	$(GOTEST) -bench=. -benchmem ./...

# Generate coverage report
coverage:
	@echo "📊 生成覆盖率报告..."
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆盖率报告: coverage.html"

# Lint code (requires golangci-lint)
lint:
	@echo "🔍 代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint 未安装，跳过检查"; \
	fi 