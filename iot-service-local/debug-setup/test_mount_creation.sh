#!/bin/bash

# 测试脚本：验证挂载点创建是否正常工作

echo "🧪 测试挂载点创建功能"
echo "====================="

# 定义测试目录
TEST_DIR="/tmp/test_mount_creation"
MOUNT_POINT="/tmp/test_camera_data"

# 清理旧的测试目录
echo "🧹 清理旧的测试目录..."
rm -rf "$TEST_DIR" "$MOUNT_POINT"

# 创建测试目录
echo "📁 创建测试目录..."
mkdir -p "$TEST_DIR"

# 测试1：验证 mkdir -p 命令是否正常工作
echo ""
echo "1️⃣  测试基本的 mkdir -p 功能："
echo "   执行命令: mkdir -p $MOUNT_POINT"
mkdir -p "$MOUNT_POINT"
if [ -d "$MOUNT_POINT" ]; then
    echo "   ✅ 成功创建目录: $MOUNT_POINT"
else
    echo "   ❌ 创建目录失败: $MOUNT_POINT"
    exit 1
fi

# 测试2：验证子目录创建
echo ""
echo "2️⃣  测试子目录创建："
SUB_DIRS=("sqlite" "iot-service" "ssd" "temp" "firmware")
for dir in "${SUB_DIRS[@]}"; do
    sub_path="$MOUNT_POINT/$dir"
    echo "   执行命令: mkdir -p $sub_path"
    mkdir -p "$sub_path"
    if [ -d "$sub_path" ]; then
        echo "   ✅ 成功创建子目录: $sub_path"
    else
        echo "   ❌ 创建子目录失败: $sub_path"
        exit 1
    fi
done

# 测试3：验证权限
echo ""
echo "3️⃣  测试目录权限："
for dir in "${SUB_DIRS[@]}"; do
    sub_path="$MOUNT_POINT/$dir"
    perm=$(ls -ld "$sub_path" | cut -d' ' -f1)
    echo "   目录权限: $sub_path -> $perm"
done

# 测试4：创建测试文件
echo ""
echo "4️⃣  测试文件创建："
echo "test content" > "$MOUNT_POINT/sqlite/test.db"
if [ -f "$MOUNT_POINT/sqlite/test.db" ]; then
    echo "   ✅ 成功创建测试文件: $MOUNT_POINT/sqlite/test.db"
else
    echo "   ❌ 创建测试文件失败"
    exit 1
fi

# 测试5：模拟JDBC连接字符串路径
echo ""
echo "5️⃣  测试JDBC连接字符串路径："
JDBC_PATH="${MOUNT_POINT}/sqlite/camera.db"
echo "   模拟JDBC路径: jdbc:sqlite:$JDBC_PATH"
echo "   检查父目录是否存在..."
if [ -d "$(dirname "$JDBC_PATH")" ]; then
    echo "   ✅ JDBC路径的父目录存在"
    # 创建模拟数据库文件
    touch "$JDBC_PATH"
    if [ -f "$JDBC_PATH" ]; then
        echo "   ✅ 成功创建模拟数据库文件: $JDBC_PATH"
    else
        echo "   ❌ 创建模拟数据库文件失败"
    fi
else
    echo "   ❌ JDBC路径的父目录不存在"
    exit 1
fi

# 测试6：检查在WSL环境中的表现
echo ""
echo "6️⃣  WSL环境检查："
if grep -q Microsoft /proc/version 2>/dev/null; then
    echo "   🔍 检测到WSL环境"
    echo "   当前挂载点: $MOUNT_POINT"
    echo "   目录结构："
    tree "$MOUNT_POINT" 2>/dev/null || find "$MOUNT_POINT" -type d | sort
else
    echo "   ℹ️  非WSL环境"
fi

# 列出最终的目录结构
echo ""
echo "📋 最终目录结构："
echo "ls -la $MOUNT_POINT:"
ls -la "$MOUNT_POINT"

echo ""
echo "🎉 测试完成！"
echo "💡 如果所有测试都通过，说明挂载点创建功能正常工作。"
echo "💡 如果仍然看不到 /mnt/camera_data 目录，请检查程序是否真正调用了 MountOnLocal() 方法。"

# 清理测试文件
echo ""
echo "🧹 清理测试文件..."
rm -rf "$TEST_DIR" "$MOUNT_POINT"
echo "✅ 清理完成" 