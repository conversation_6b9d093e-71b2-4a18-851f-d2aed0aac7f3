# 权限问题修复脚本 - Windows版本
# 用于解决常见的文件共享权限问题

Write-Host "🔧 权限问题修复工具 (Windows版本)" -ForegroundColor Green
Write-Host "=================================="

# 检查是否以管理员身份运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Host "⚠️  检测到管理员权限，建议以普通用户身份运行" -ForegroundColor Yellow
} else {
    Write-Host "✅ 以普通用户身份运行" -ForegroundColor Green
}

# 默认挂载路径
$DefaultMountPath = "C:\camera_data"
$MountPath = if ($args.Length -gt 0) { $args[0] } else { $DefaultMountPath }

Write-Host "🎯 目标路径: $MountPath"
Write-Host ""

# 检查路径是否存在
if (Test-Path $MountPath) {
    Write-Host "✅ 目录已存在: $MountPath" -ForegroundColor Green
    Write-Host "🔍 检查目录权限..."
    Get-Acl $MountPath | Format-List
    Write-Host ""
} else {
    Write-Host "❌ 目录不存在: $MountPath" -ForegroundColor Red
    Write-Host ""
}

# 解决方案1：直接创建目录
Write-Host "🔧 解决方案1: 直接创建目录" -ForegroundColor Cyan
Write-Host "----------------------------"
Write-Host "尝试创建目录: $MountPath"

try {
    New-Item -ItemType Directory -Path $MountPath -Force -ErrorAction Stop | Out-Null
    Write-Host "✅ 成功创建目录" -ForegroundColor Green
    
    # 测试写入权限
    $testFile = Join-Path $MountPath ".permission_test"
    try {
        New-Item -ItemType File -Path $testFile -Force -ErrorAction Stop | Out-Null
        Write-Host "✅ 写入权限正常" -ForegroundColor Green
        Remove-Item $testFile -ErrorAction SilentlyContinue
    } catch {
        Write-Host "❌ 写入权限异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "📋 目录权限:"
    Get-Acl $MountPath | Format-List
    
} catch {
    Write-Host "❌ 权限不足，无法创建目录: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 这是正常现象，继续尝试其他方案" -ForegroundColor Yellow
}
Write-Host ""

# 解决方案2：使用用户目录
Write-Host "🔧 解决方案2: 使用用户目录" -ForegroundColor Cyan
Write-Host "----------------------------"
$UserMountPath = Join-Path $env:USERPROFILE "debug-setup\mount\camera_data"
Write-Host "📁 用户目录路径: $UserMountPath"

try {
    New-Item -ItemType Directory -Path $UserMountPath -Force -ErrorAction Stop | Out-Null
    Write-Host "✅ 用户目录创建成功" -ForegroundColor Green
    
    # 测试写入权限
    $testFile = Join-Path $UserMountPath ".permission_test"
    try {
        New-Item -ItemType File -Path $testFile -Force -ErrorAction Stop | Out-Null
        Write-Host "✅ 写入权限正常" -ForegroundColor Green
        Remove-Item $testFile -ErrorAction SilentlyContinue
    } catch {
        Write-Host "❌ 写入权限异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "📋 目录权限:"
    Get-Acl $UserMountPath | Format-List
    
    Write-Host "💡 建议在配置文件中使用此路径:" -ForegroundColor Yellow
    Write-Host "   mount_point: $UserMountPath"
    
} catch {
    Write-Host "❌ 用户目录创建失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 解决方案3：使用临时目录
Write-Host "🔧 解决方案3: 使用临时目录" -ForegroundColor Cyan
Write-Host "----------------------------"
$TempMountPath = Join-Path $env:TEMP "debug-setup\mount\camera_data"
Write-Host "📁 临时目录路径: $TempMountPath"

try {
    New-Item -ItemType Directory -Path $TempMountPath -Force -ErrorAction Stop | Out-Null
    Write-Host "✅ 临时目录创建成功" -ForegroundColor Green
    
    # 测试写入权限
    $testFile = Join-Path $TempMountPath ".permission_test"
    try {
        New-Item -ItemType File -Path $testFile -Force -ErrorAction Stop | Out-Null
        Write-Host "✅ 写入权限正常" -ForegroundColor Green
        Remove-Item $testFile -ErrorAction SilentlyContinue
    } catch {
        Write-Host "❌ 写入权限异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "📋 目录权限:"
    Get-Acl $TempMountPath | Format-List
    
    Write-Host "⚠️  注意：临时目录在重启后可能被清理" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ 临时目录创建失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 解决方案4：WSL环境检查
Write-Host "🔧 解决方案4: WSL环境检查" -ForegroundColor Cyan
Write-Host "----------------------------"
$wslAvailable = Get-Command wsl -ErrorAction SilentlyContinue
if ($wslAvailable) {
    Write-Host "🔍 检测到WSL可用" -ForegroundColor Green
    
    # 检查WSL发行版
    try {
        $wslDistributions = wsl --list --quiet
        Write-Host "📋 可用的WSL发行版:"
        $wslDistributions | ForEach-Object { Write-Host "   - $_" }
        
        # WSL推荐路径
        $WSLMountPath = "/mnt/c/Users/<USER>/camera_data"
        Write-Host "💡 WSL推荐路径: $WSLMountPath"
        
        # 在WSL中创建目录
        Write-Host "🔧 在WSL中创建目录..."
        $wslResult = wsl -- mkdir -p $WSLMountPath 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ WSL路径创建成功" -ForegroundColor Green
            
            # 测试写入权限
            $wslTestResult = wsl -- touch "$WSLMountPath/.permission_test" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 写入权限正常" -ForegroundColor Green
                wsl -- rm -f "$WSLMountPath/.permission_test"
            } else {
                Write-Host "❌ 写入权限异常: $wslTestResult" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ WSL路径创建失败: $wslResult" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ WSL操作失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️  未检测到WSL，跳过WSL路径检查" -ForegroundColor Gray
}
Write-Host ""

# 总结和建议
Write-Host "📋 总结和建议" -ForegroundColor Cyan
Write-Host "============"
Write-Host "根据上述测试结果，建议按以下优先级解决权限问题:"
Write-Host ""
Write-Host "1. 如果解决方案1成功，使用原始路径: $MountPath" -ForegroundColor Green
Write-Host "2. 如果解决方案2成功，使用用户目录: $UserMountPath" -ForegroundColor Green
Write-Host "3. 如果解决方案3成功，使用临时目录: $TempMountPath" -ForegroundColor Green
Write-Host "4. 如果有WSL环境，使用WSL路径: /mnt/c/Users/<USER>/camera_data" -ForegroundColor Green
Write-Host ""
Write-Host "📝 配置文件修改建议:" -ForegroundColor Yellow
Write-Host "在 debug_config.yml 中修改:"
Write-Host "local_machine:"
Write-Host "  mount_point: [选择上述可用路径]"
Write-Host ""
Write-Host "🎉 脚本执行完成！" -ForegroundColor Green

# 暂停以便用户查看结果
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 