#!/bin/bash

# 权限问题修复脚本
# 用于解决常见的文件共享权限问题

echo "🔧 权限问题修复工具"
echo "=================="

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  请不要以root用户运行此脚本"
   echo "💡 请使用普通用户运行，脚本会在需要时提示输入sudo密码"
   exit 1
fi

# 默认挂载路径
DEFAULT_MOUNT_PATH="/mnt/camera_data"
MOUNT_PATH=${1:-$DEFAULT_MOUNT_PATH}

echo "🎯 目标路径: $MOUNT_PATH"
echo ""

# 检查路径是否存在
if [ -d "$MOUNT_PATH" ]; then
    echo "✅ 目录已存在: $MOUNT_PATH"
    echo "🔍 检查目录权限..."
    ls -ld "$MOUNT_PATH"
    echo ""
else
    echo "❌ 目录不存在: $MOUNT_PATH"
    echo ""
fi

# 解决方案1：直接创建目录
echo "🔧 解决方案1: 直接创建目录"
echo "----------------------------"
echo "尝试创建目录: $MOUNT_PATH"
if mkdir -p "$MOUNT_PATH" 2>/dev/null; then
    echo "✅ 成功创建目录"
    echo "🗑️  清理测试目录..."
    rmdir "$MOUNT_PATH" 2>/dev/null
else
    echo "❌ 权限不足，无法创建目录"
    echo "💡 这是正常现象，继续尝试其他方案"
fi
echo ""

# 解决方案2：使用sudo创建目录
echo "🔧 解决方案2: 使用sudo创建目录"
echo "--------------------------------"
echo "这将需要输入sudo密码"
read -p "是否继续？(y/N): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔐 使用sudo创建目录..."
    if sudo mkdir -p "$MOUNT_PATH"; then
        echo "✅ 目录创建成功"
        
        # 设置目录所有者
        echo "🔧 设置目录所有者为当前用户..."
        if sudo chown -R "$USER:$USER" "$MOUNT_PATH"; then
            echo "✅ 所有者设置成功"
        else
            echo "⚠️  设置所有者失败"
        fi
        
        # 设置权限
        echo "🔧 设置目录权限..."
        if sudo chmod -R 755 "$MOUNT_PATH"; then
            echo "✅ 权限设置成功"
        else
            echo "⚠️  设置权限失败"
        fi
        
        # 检查最终权限
        echo "📋 最终权限:"
        ls -ld "$MOUNT_PATH"
        
        # 测试写入权限
        echo "🧪 测试写入权限..."
        test_file="$MOUNT_PATH/.permission_test"
        if touch "$test_file" 2>/dev/null; then
            echo "✅ 写入权限正常"
            rm -f "$test_file"
        else
            echo "❌ 写入权限异常"
        fi
        
    else
        echo "❌ 使用sudo创建目录失败"
    fi
else
    echo "⏭️  跳过sudo方案"
fi
echo ""

# 解决方案3：使用用户目录
echo "🔧 解决方案3: 使用用户目录"
echo "----------------------------"
USER_MOUNT_PATH="$HOME/debug-setup/mount/camera_data"
echo "📁 用户目录路径: $USER_MOUNT_PATH"

if mkdir -p "$USER_MOUNT_PATH"; then
    echo "✅ 用户目录创建成功"
    
    # 测试写入权限
    test_file="$USER_MOUNT_PATH/.permission_test"
    if touch "$test_file"; then
        echo "✅ 写入权限正常"
        rm -f "$test_file"
    else
        echo "❌ 写入权限异常"
    fi
    
    echo "📋 目录权限:"
    ls -ld "$USER_MOUNT_PATH"
    
    echo "💡 建议在配置文件中使用此路径:"
    echo "   mount_point: $USER_MOUNT_PATH"
    
else
    echo "❌ 用户目录创建失败"
fi
echo ""

# 解决方案4：WSL环境检查
echo "🔧 解决方案4: WSL环境检查"
echo "----------------------------"
if grep -q Microsoft /proc/version 2>/dev/null; then
    echo "🔍 检测到WSL环境"
    
    # WSL推荐路径
    WSL_MOUNT_PATH="/mnt/c/Users/<USER>/camera_data"
    echo "💡 WSL推荐路径: $WSL_MOUNT_PATH"
    
    if mkdir -p "$WSL_MOUNT_PATH"; then
        echo "✅ WSL路径创建成功"
        
        # 测试写入权限
        test_file="$WSL_MOUNT_PATH/.permission_test"
        if touch "$test_file"; then
            echo "✅ 写入权限正常"
            rm -f "$test_file"
        else
            echo "❌ 写入权限异常"
        fi
        
        echo "📋 目录权限:"
        ls -ld "$WSL_MOUNT_PATH"
        
    else
        echo "❌ WSL路径创建失败"
    fi
else
    echo "ℹ️  非WSL环境，跳过WSL路径检查"
fi
echo ""

# 总结和建议
echo "📋 总结和建议"
echo "============"
echo "根据上述测试结果，建议按以下优先级解决权限问题:"
echo ""
echo "1. 如果解决方案2成功，使用原始路径: $MOUNT_PATH"
echo "2. 如果解决方案3成功，使用用户目录: $USER_MOUNT_PATH"
echo "3. 如果在WSL环境，使用WSL路径: /mnt/c/Users/<USER>/camera_data"
echo ""
echo "📝 配置文件修改建议:"
echo "在 debug_config.yml 中修改:"
echo "local_machine:"
echo "  mount_point: [选择上述可用路径]"
echo ""
echo "🎉 脚本执行完成！" 