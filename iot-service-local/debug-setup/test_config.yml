target_device:
  ip: "*************"
  ssh:
    username: "test"
    password: "test123"
    port: 22
    key_file: ""

local_machine:
  ip: "************"
  nfs_mount_point: "/mnt/nfs"

nfs_exports:
  directories:
    - source: "/data/sqlite"
      export_path: "/data/sqlite" 
      options: "rw,sync,no_subtree_check,no_root_squash"

port_forwarding:
  expose_ports:
    - port: 6379
      description: "Redis服务"
  forward_to_local:
    - remote_port: 7777
      local_port: 7777
      description: "应用服务"

config_replacements:
  # 测试多配置文件支持
  file_paths:
    - "test_app.yml"
    - "test_app-test.yml"
  
  replacements:
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
    - key: "test.value"
      value: "updated_value"
      description: "测试值"

platform_config:
  windows:
    nfs_mount_command: "echo 'Windows NFS mount'"
    nfs_unmount_command: "echo 'Windows NFS unmount'"
  linux:
    nfs_mount_command: "mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "umount {{.mount_point}}"
  darwin:
    nfs_mount_command: "mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "umount {{.mount_point}}"

execution_steps:
  setup_nfs: false
  setup_port_forwarding: false
  update_config_file: true
  cleanup_on_exit: false 