version: '3'

services:

  nginx:
    image: bx-nginx
    container_name: nginx
    restart: always
    network_mode: host
    volumes:
          - /data/resolv.conf:/etc/resolv.conf:ro
    environment:
      - NGINX_ENTRYPOINT_QUIET_LOGS=1
    depends_on:
    - iot-service
    - redis
    ulimits:
      core: 0

  iot-service:
    image: bx-iot-service
    container_name: iot-service
    restart: always
    network_mode: host
    volumes:  # sqlite, video, etc
          - /userdata:/data/iot-service
          - /data/sqlite:/data/sqlite
          - /tmp/ssd:/tmp/ssd
          - /data/resolv.conf:/etc/resolv.conf:ro
          - /tmp:/tmp
          - /data/debug_version:/data/debug_version:ro
    environment:
      - appName=iot-service-local
    depends_on:
    - redis
    ulimits:
      core: 0

  redis:
    image: bx-redis
    container_name: redis
    network_mode: host
    volumes: # rdb
          - /data/sqlite:/data
          - /data/resolv.conf:/etc/resolv.conf:ro
    restart: always
    ulimits:
      core: 0

  coturn:
    image: bx-coturn
    container_name: coturn
    restart: always
    network_mode: host
    volumes:
          - /data/resolv.conf:/etc/resolv.conf:ro
    ulimits:
      core: 0
