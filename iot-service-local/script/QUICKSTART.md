# 快速开始指南

这是一个5分钟的快速指南，帮助您快速设置和使用IoT调试环境自动化工具。

## ⚡ 快速设置

### 1. 准备工作

确保您的环境满足以下要求：

- **开发机**：安装了Go 1.21+
- **录像机**：Linux系统，可SSH访问，有root权限
- **网络**：开发机和录像机在同一局域网

### 2. 下载并构建工具

```bash
# 进入项目目录
cd iot-service-local/script

# 构建工具
go build -o iot-debug-setup .

# 或者使用构建脚本
./build.sh
```

### 3. 配置环境

复制配置文件模板并根据您的环境修改：

```bash
cp debug_config.yml my_config.yml
```

**必须修改的配置项：**

```yaml
# 录像机配置
target_device:
  ip: "*************"        # 改为您的录像机IP
  ssh:
    username: "root"
    password: "your_password"  # 改为您的SSH密码

# 本地机器配置
local_machine:
  ip: "**************"      # 改为您的本地IP
  nfs_mount_point: "/mnt/camera_data"  # Linux/Mac
  # Windows用户改为: "Z:\\"
```

### 4. 运行工具

```bash
./iot-debug-setup my_config.yml
```

如果一切正常，您会看到：

```
🚀 开始设置IoT调试环境...
目标设备: *************
本地机器: **************
操作系统: linux

📡 连接到录像机...
✅ SSH连接成功: *************:22

📁 设置NFS导出...
✅ NFS设置完成

🔗 设置端口转发...
✅ 端口转发设置完成

⚙️ 更新配置文件...
✅ 配置文件更新完成

✅ 调试环境设置完成！
现在可以在IDE中启动调试模式。
按 Ctrl+C 退出并清理环境。
```

### 5. 开始调试

保持工具运行，在您的IDE中启动调试：

- **IntelliJ IDEA**：点击Debug按钮
- **VS Code**：按F5启动调试
- **命令行**：`mvn spring-boot:run`

## 🔧 IDE集成（可选）

### IntelliJ IDEA集成

1. 打开 `Run/Debug Configurations`
2. 选择您的Spring Boot配置
3. 点击 `Modify options` → `Add before launch task`
4. 添加 `Run External tool`：
   - **Program**: `/path/to/iot-debug-setup`
   - **Arguments**: `my_config.yml`
   - **Working directory**: `项目根目录/iot-service-local/script`

现在每次点击Debug按钮时，工具会自动启动！

## 📋 验证步骤

### 1. 验证SSH连接

```bash
ssh root@*************
```

### 2. 验证NFS挂载

```bash
# Linux/Mac
mount | grep nfs

# Windows
mount
```

### 3. 验证端口转发

```bash
# 测试Redis连接
telnet ************* 6379

# 测试gRPC端口
telnet ************* 50031
```

### 4. 验证配置文件

检查 `application.yml` 是否已正确更新：

```bash
grep "spring.redis.host" iot-service-local/src/main/resources/application.yml
```

## 🚫 常见问题快速解决

### 问题1：SSH连接失败

```bash
# 测试SSH连接
ssh root@*************

# 如果失败，检查：
# 1. IP地址是否正确
# 2. SSH服务是否运行
# 3. 用户名密码是否正确
```

### 问题2：NFS挂载失败

```bash
# 检查NFS客户端是否安装
# Ubuntu/Debian
sudo apt-get install nfs-common

# CentOS/RHEL
sudo yum install nfs-utils

# macOS - 通常已内置
```

### 问题3：端口转发不工作

```bash
# 在录像机上检查端口是否监听
netstat -tuln | grep 6379

# 检查iptables规则
iptables -t nat -L
```

### 问题4：配置文件路径错误

确认配置文件路径：

```yaml
config_replacements:
  file_path: "iot-service-local/src/main/resources/application.yml"
```

## 🔄 重启和清理

### 重启工具

如果需要重新运行：

1. 按 `Ctrl+C` 停止工具
2. 等待清理完成
3. 重新运行 `./iot-debug-setup my_config.yml`

### 手动清理（如果自动清理失败）

```bash
# 卸载NFS
sudo umount /mnt/camera_data

# 清理端口转发（Linux）
sudo pkill socat

# 清理端口转发（Windows）
netsh interface portproxy reset

# 恢复配置文件
cp application.yml.backup application.yml
```

## 📝 下一步

1. **阅读完整文档**：查看 [README.md](README.md) 了解更多功能
2. **自定义配置**：根据需要修改NFS路径、端口转发等
3. **集成CI/CD**：将工具集成到您的开发流程中
4. **安全加固**：使用SSH密钥认证，设置合适的NFS权限

## 🆘 获取帮助

如果遇到问题：

1. 查看工具输出的错误信息
2. 检查 [故障排除部分](README.md#故障排除)
3. 启用调试模式：修改配置文件中的 `execution_steps` 来逐步调试
4. 提交Issue或寻求技术支持

祝您调试愉快！🎉 