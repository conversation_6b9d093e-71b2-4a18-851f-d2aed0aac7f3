package main

import (
	"fmt"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
)

// 端口管理器结构体
type PortManager struct {
	config         *Config
	sshClient      *SSHClient
	appliedRules   []string // 记录已应用的规则，用于清理
	localTunnels   []string // 记录本地隧道，用于清理
}

// 创建新的端口管理器
func NewPortManager(config *Config) *PortManager {
	return &PortManager{
		config:       config,
		appliedRules: make([]string, 0),
		localTunnels: make([]string, 0),
	}
}

// 设置SSH客户端
func (p *PortManager) SetSSHClient(client *SSHClient) {
	p.sshClient = client
}

// 设置端口转发
func (p *PortManager) SetupPortForwarding() error {
	// 确保SSH客户端已连接
	if p.sshClient == nil {
		return fmt.Errorf("SSH客户端未设置")
	}

	// 设置远程端口暴露（从127.0.0.1映射到0.0.0.0）
	if err := p.setupRemotePortExposure(); err != nil {
		return fmt.Errorf("设置远程端口暴露失败: %v", err)
	}

	// 设置本地端口转发
	if err := p.setupLocalPortForwarding(); err != nil {
		return fmt.Errorf("设置本地端口转发失败: %v", err)
	}

	fmt.Println("✅ 端口转发设置完成")
	return nil
}

// 设置远程端口暴露
func (p *PortManager) setupRemotePortExposure() error {
	fmt.Println("🔗 设置远程端口暴露...")

	// 检查iptables是否可用
	if !p.sshClient.CommandExists("iptables") {
		return fmt.Errorf("iptables不可用")
	}

	// 保存当前iptables规则
	if err := p.backupIptablesRules(); err != nil {
		fmt.Printf("⚠️  备份iptables规则失败: %v\n", err)
	}

	// 为每个需要暴露的端口创建规则
	for _, portConfig := range p.config.PortForwarding.ExposePorts {
		if err := p.exposePort(portConfig.Port, portConfig.Description); err != nil {
			return fmt.Errorf("暴露端口失败 %d: %v", portConfig.Port, err)
		}
	}

	return nil
}

// 备份iptables规则
func (p *PortManager) backupIptablesRules() error {
	backupCmd := "iptables-save > /tmp/iptables_backup_$(date +%Y%m%d_%H%M%S).rules"
	_, err := p.sshClient.ExecuteCommand(backupCmd)
	if err != nil {
		return fmt.Errorf("备份iptables规则失败: %v", err)
	}
	fmt.Println("✅ iptables规则备份完成")
	return nil
}

// 暴露端口（从127.0.0.1映射到0.0.0.0）
func (p *PortManager) exposePort(port int, description string) error {
	fmt.Printf("🔌 暴露端口 %d (%s)...\n", port, description)

	// 检查端口是否已经在127.0.0.1上监听
	if !p.sshClient.IsPortInUse(port) {
		fmt.Printf("⚠️  端口 %d 未在使用中\n", port)
	}

	// 创建DNAT规则，将外部访问转发到本地
	dnatRule := fmt.Sprintf(
		"iptables -t nat -A PREROUTING -p tcp --dport %d -j DNAT --to-destination 127.0.0.1:%d",
		port, port)

	if _, err := p.sshClient.ExecuteCommand(dnatRule); err != nil {
		return fmt.Errorf("创建DNAT规则失败: %v", err)
	}

	// 创建FORWARD规则，允许转发
	forwardRule := fmt.Sprintf(
		"iptables -A FORWARD -p tcp --dport %d -j ACCEPT",
		port)

	if _, err := p.sshClient.ExecuteCommand(forwardRule); err != nil {
		return fmt.Errorf("创建FORWARD规则失败: %v", err)
	}

	// 创建MASQUERADE规则，用于源地址转换
	masqueradeRule := fmt.Sprintf(
		"iptables -t nat -A POSTROUTING -p tcp --dport %d -j MASQUERADE",
		port)

	if _, err := p.sshClient.ExecuteCommand(masqueradeRule); err != nil {
		return fmt.Errorf("创建MASQUERADE规则失败: %v", err)
	}

	// 记录应用的规则（用于清理）
	p.appliedRules = append(p.appliedRules, 
		fmt.Sprintf("nat:PREROUTING:%d", port),
		fmt.Sprintf("filter:FORWARD:%d", port),
		fmt.Sprintf("nat:POSTROUTING:%d", port))

	fmt.Printf("✅ 端口 %d 暴露成功\n", port)
	return nil
}

// 设置本地端口转发
func (p *PortManager) setupLocalPortForwarding() error {
	fmt.Println("🔗 设置本地端口转发...")

	for _, forwardConfig := range p.config.PortForwarding.ForwardToLocal {
		if err := p.setupLocalForward(forwardConfig.RemotePort, forwardConfig.LocalPort, forwardConfig.Description); err != nil {
			return fmt.Errorf("设置本地转发失败 %d->%d: %v", forwardConfig.RemotePort, forwardConfig.LocalPort, err)
		}
	}

	return nil
}

// 设置单个本地端口转发
func (p *PortManager) setupLocalForward(remotePort, localPort int, description string) error {
	fmt.Printf("🔗 设置端口转发 %d->%d (%s)...\n", remotePort, localPort, description)

	// 检查本地端口是否已被占用
	if p.isLocalPortInUse(localPort) {
		fmt.Printf("⚠️  本地端口 %d 已被占用\n", localPort)
	}

	// 根据不同平台创建端口转发
	switch runtime.GOOS {
	case "linux":
		return p.setupLinuxPortForward(remotePort, localPort)
	case "darwin":
		return p.setupMacPortForward(remotePort, localPort)
	case "windows":
		return p.setupWindowsPortForward(remotePort, localPort)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// Linux端口转发
func (p *PortManager) setupLinuxPortForward(remotePort, localPort int) error {
	// 使用socat创建端口转发
	socatCmd := fmt.Sprintf("socat TCP-LISTEN:%d,fork TCP:%s:%d",
		localPort, p.config.TargetDevice.IP, remotePort)
	
	// 在后台运行socat
	if err := p.executeLocalCommandInBackground(socatCmd); err != nil {
		return fmt.Errorf("启动socat失败: %v", err)
	}

	// 记录端口转发（用于清理）
	p.localTunnels = append(p.localTunnels, fmt.Sprintf("socat:%d", localPort))

	fmt.Printf("✅ Linux端口转发设置成功: %d->%d\n", remotePort, localPort)
	return nil
}

// macOS端口转发
func (p *PortManager) setupMacPortForward(remotePort, localPort int) error {
	// 使用nc (netcat)创建端口转发
	// 注意：这是一个简化的实现，生产环境中可能需要更复杂的解决方案
	ncCmd := fmt.Sprintf("while true; do nc -l %d -c 'nc %s %d'; done",
		localPort, p.config.TargetDevice.IP, remotePort)
	
	if err := p.executeLocalCommandInBackground(ncCmd); err != nil {
		return fmt.Errorf("启动nc失败: %v", err)
	}

	p.localTunnels = append(p.localTunnels, fmt.Sprintf("nc:%d", localPort))

	fmt.Printf("✅ macOS端口转发设置成功: %d->%d\n", remotePort, localPort)
	return nil
}

// Windows端口转发
func (p *PortManager) setupWindowsPortForward(remotePort, localPort int) error {
	// 使用netsh设置端口转发
	netshCmd := fmt.Sprintf("netsh interface portproxy add v4tov4 listenport=%d listenaddress=127.0.0.1 connectport=%d connectaddress=%s",
		localPort, remotePort, p.config.TargetDevice.IP)
	
	if err := p.executeLocalCommand(netshCmd); err != nil {
		return fmt.Errorf("设置Windows端口转发失败: %v", err)
	}

	p.localTunnels = append(p.localTunnels, fmt.Sprintf("netsh:%d", localPort))

	fmt.Printf("✅ Windows端口转发设置成功: %d->%d\n", remotePort, localPort)
	return nil
}

// 检查本地端口是否被占用
func (p *PortManager) isLocalPortInUse(port int) bool {
	var checkCmd string
	
	switch runtime.GOOS {
	case "linux":
		checkCmd = fmt.Sprintf("netstat -tuln | grep ':%d '", port)
	case "darwin":
		checkCmd = fmt.Sprintf("netstat -an | grep ':%d '", port)
	case "windows":
		checkCmd = fmt.Sprintf("netstat -an | findstr :%d", port)
	default:
		return false
	}

	err := p.executeLocalCommand(checkCmd)
	return err == nil
}

// 在后台执行本地命令
func (p *PortManager) executeLocalCommandInBackground(command string) error {
	fmt.Printf("🔧 后台执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", "start", "/b", command)
	} else {
		cmd = exec.Command("sh", "-c", command+" &")
	}

	return cmd.Start()
}

// 执行本地命令
func (p *PortManager) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

// 清理端口转发规则
func (p *PortManager) Cleanup() error {
	fmt.Println("🧹 清理端口转发...")

	// 清理远程iptables规则
	if err := p.cleanupRemoteRules(); err != nil {
		fmt.Printf("⚠️  清理远程规则失败: %v\n", err)
	}

	// 清理本地端口转发
	if err := p.cleanupLocalTunnels(); err != nil {
		fmt.Printf("⚠️  清理本地隧道失败: %v\n", err)
	}

	return nil
}

// 清理远程iptables规则
func (p *PortManager) cleanupRemoteRules() error {
	if p.sshClient == nil {
		return nil
	}

	for _, rule := range p.appliedRules {
		parts := strings.Split(rule, ":")
		if len(parts) != 3 {
			continue
		}

		table := parts[0]
		chain := parts[1]
		port := parts[2]

		var deleteCmd string
		switch table {
		case "nat":
			if chain == "PREROUTING" {
				deleteCmd = fmt.Sprintf("iptables -t nat -D PREROUTING -p tcp --dport %s -j DNAT --to-destination 127.0.0.1:%s", port, port)
			} else if chain == "POSTROUTING" {
				deleteCmd = fmt.Sprintf("iptables -t nat -D POSTROUTING -p tcp --dport %s -j MASQUERADE", port)
			}
		case "filter":
			if chain == "FORWARD" {
				deleteCmd = fmt.Sprintf("iptables -D FORWARD -p tcp --dport %s -j ACCEPT", port)
			}
		}

		if deleteCmd != "" {
			if _, err := p.sshClient.ExecuteCommand(deleteCmd); err != nil {
				fmt.Printf("⚠️  删除规则失败: %s\n", deleteCmd)
			} else {
				fmt.Printf("✅ 删除规则成功: %s\n", rule)
			}
		}
	}

	// 清理规则记录
	p.appliedRules = p.appliedRules[:0]
	return nil
}

// 清理本地隧道
func (p *PortManager) cleanupLocalTunnels() error {
	for _, tunnel := range p.localTunnels {
		parts := strings.Split(tunnel, ":")
		if len(parts) != 2 {
			continue
		}

		tunnelType := parts[0]
		port := parts[1]

		var cleanupCmd string
		switch tunnelType {
		case "socat":
			cleanupCmd = fmt.Sprintf("pkill -f 'socat.*:%s'", port)
		case "nc":
			cleanupCmd = fmt.Sprintf("pkill -f 'nc.*%s'", port)
		case "netsh":
			portNum, _ := strconv.Atoi(port)
			cleanupCmd = fmt.Sprintf("netsh interface portproxy delete v4tov4 listenport=%d listenaddress=127.0.0.1", portNum)
		}

		if cleanupCmd != "" {
			if err := p.executeLocalCommand(cleanupCmd); err != nil {
				fmt.Printf("⚠️  清理隧道失败: %s\n", tunnel)
			} else {
				fmt.Printf("✅ 清理隧道成功: %s\n", tunnel)
			}
		}
	}

	// 清理隧道记录
	p.localTunnels = p.localTunnels[:0]
	return nil
} 