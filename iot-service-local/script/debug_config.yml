# 调试环境自动化配置文件
# 支持Windows/Linux/Mac跨平台使用

# 目标录像机配置
target_device:
  # 录像机IP地址
  ip: "*************"
  # SSH连接配置
  ssh:
    username: "root"
    password: "your_password"
    port: 22
    # 或者使用密钥文件
    # key_file: "/path/to/private/key"

# 本地开发机配置
local_machine:
  # 本地IP地址（用于端口转发）
  ip: "**************"
  # 本地NFS挂载点
  nfs_mount_point: "/mnt/camera_data"  # Linux/Mac
  # Windows: "Z:\\"

# NFS配置 - 需要从录像机暴露的文件和目录
nfs_exports:
  # 要暴露的目录列表
  directories:
    - source: "/data/sqlite"           # 录像机上的SQLite数据库目录
      export_path: "/data/sqlite"      # NFS导出路径
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/data/iot-service"      # 录像机上的IoT服务数据目录
      export_path: "/data/iot-service"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/tmp/ssd"               # 录像机上的SSD存储目录
      export_path: "/tmp/ssd"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/addx/temp"             # 录像机上的临时目录
      export_path: "/addx/temp"
      options: "rw,sync,no_subtree_check,no_root_squash"
    - source: "/addx/firmware"         # 录像机上的固件目录
      export_path: "/addx/firmware"
      options: "rw,sync,no_subtree_check,no_root_squash"

# 端口转发配置
port_forwarding:
  # 录像机上需要从127.0.0.1映射到0.0.0.0的端口
  expose_ports:
    - port: 6379
      description: "Redis服务"
    - port: 50031
      description: "AI服务gRPC端口"
    - port: 51003
      description: "基站服务gRPC端口"
    - port: 51004
      description: "Alexa gRPC服务端口"

  # 需要从录像机转发到本地的端口
  forward_to_local:
    - remote_port: 7777
      local_port: 7777
      description: "IoT服务HTTP端口"

# 配置文件替换规则
config_replacements:
  # 要修改的配置文件路径
  file_path: "iot-service-local/src/main/resources/application.yml"
  
  # 替换规则列表
  replacements:
    # Redis配置
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
    
    # 数据库配置
    - key: "spring.datasource.url"
      value: "jdbc:sqlite:{{.local_nfs_mount}}/sqlite/camera.db?journal_mode=WAL&busy_timeout=30000"
      description: "主数据库连接"
    
    - key: "spring.datasource.dynamic.datasource.camera.url"
      value: "jdbc:sqlite:{{.local_nfs_mount}}/sqlite/camera.db?journal_mode=WAL&busy_timeout=30000"
      description: "摄像头数据库连接"
    
    - key: "spring.datasource.dynamic.datasource.library.url"
      value: "jdbc:sqlite:{{.local_nfs_mount}}/sqlite/library.db?journal_mode=WAL&busy_timeout=30000"
      description: "录像库数据库连接"
    
    # gRPC服务配置
    - key: "grpc.saas-ai-service"
      value: "{{.target_device.ip}}:50031"
      description: "AI服务地址"
    
    - key: "grpc.bstationd-service"
      value: "{{.target_device.ip}}:51003"
      description: "基站服务地址"
      
    - key: "grpc.client.GLOBAL.address"
      value: "static://{{.target_device.ip}}:51003"
      description: "全局gRPC客户端地址"
    
    - key: "grpc.client.alexa-grpc-server.address"
      value: "static://{{.target_device.ip}}:51004"
      description: "Alexa gRPC服务地址"
    
    - key: "grpc.client.ai-station-grpc-server.address"
      value: "static://{{.target_device.ip}}:50031"
      description: "AI站点gRPC服务地址"
    
    # 文件存储路径配置
    - key: "video-file.storage.rootPath"
      value: "{{.local_nfs_mount}}/iot-service/"
      description: "视频文件存储根路径"
    
    - key: "video-file.extStorage.rootPath"
      value: "{{.local_nfs_mount}}/ssd/"
      description: "外部存储路径"
    
    - key: "video-config.storage.rootPath"
      value: "{{.local_nfs_mount}}/iot-service/"
      description: "视频配置存储路径"
    
    - key: "tempDir"
      value: "{{.local_nfs_mount}}/temp/"
      description: "临时目录"
    
    - key: "storage.firmwareDir"
      value: "{{.local_nfs_mount}}/firmware"
      description: "固件目录"
    
    - key: "video-file.uploadAIImage.repository"
      value: "{{.local_nfs_mount}}/iot-service/temp/uploadAIImage"
      description: "AI图片上传仓库"
    
    # 服务地址配置
    - key: "iot-service.rootPath"
      value: "http://{{.target_device.ip}}:7777"
      description: "IoT服务根路径"
    
    - key: "ai-service.rootPath"
      value: "http://{{.target_device.ip}}:8888"
      description: "AI服务根路径"
    
    # 服务器地址配置
    - key: "server.address"
      value: "0.0.0.0"
      description: "服务器绑定地址"

# 平台特定配置
platform_config:
  windows:
    nfs_mount_command: "mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "umount {{.mount_point}}"
    
  linux:
    nfs_mount_command: "sudo mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "sudo umount {{.mount_point}}"
    
  darwin:  # macOS
    nfs_mount_command: "sudo mount -t nfs {{.target_device.ip}}:{{.export_path}} {{.mount_point}}"
    nfs_unmount_command: "sudo umount {{.mount_point}}"

# 执行步骤配置
execution_steps:
  # 是否启用各个步骤
  setup_nfs: true
  setup_port_forwarding: true
  update_config_file: true
  
  # 清理步骤（在程序结束时执行）
  cleanup_on_exit: true 