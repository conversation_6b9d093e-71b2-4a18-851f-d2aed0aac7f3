package main

import (
	"fmt"
	"io/ioutil"
	"net"
	"time"

	"golang.org/x/crypto/ssh"
)

// SSH客户端结构体
type SSHClient struct {
	config *Config
	client *ssh.Client
}

// 创建新的SSH客户端
func NewSSHClient(config *Config) *SSHClient {
	return &SSHClient{
		config: config,
	}
}

// 连接到SSH服务器
func (s *SSHClient) Connect() error {
	// 创建SSH配置
	sshConfig := &ssh.ClientConfig{
		User: s.config.TargetDevice.SSH.Username,
		Auth: []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境中应该验证主机密钥
		Timeout:         30 * time.Second,
	}

	// 添加密码认证
	if s.config.TargetDevice.SSH.Password != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.Password(s.config.TargetDevice.SSH.Password))
	}

	// 添加密钥认证
	if s.config.TargetDevice.SSH.KeyFile != "" {
		key, err := ioutil.ReadFile(s.config.TargetDevice.SSH.KeyFile)
		if err != nil {
			return fmt.Errorf("读取密钥文件失败: %v", err)
		}

		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return fmt.Errorf("解析私钥失败: %v", err)
		}

		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeys(signer))
	}

	// 如果没有配置任何认证方式，使用默认密码认证
	if len(sshConfig.Auth) == 0 {
		return fmt.Errorf("没有配置SSH认证方式")
	}

	// 建立SSH连接
	addr := fmt.Sprintf("%s:%d", s.config.TargetDevice.IP, s.config.TargetDevice.SSH.Port)
	client, err := ssh.Dial("tcp", addr, sshConfig)
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}

	s.client = client
	fmt.Printf("✅ SSH连接成功: %s\n", addr)
	return nil
}

// 执行远程命令
func (s *SSHClient) ExecuteCommand(command string) (string, error) {
	if s.client == nil {
		return "", fmt.Errorf("SSH客户端未连接")
	}

	// 创建新的会话
	session, err := s.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 执行命令
	fmt.Printf("🔧 执行命令: %s\n", command)
	output, err := session.CombinedOutput(command)
	if err != nil {
		return string(output), fmt.Errorf("命令执行失败: %v", err)
	}

	return string(output), nil
}

// 检查命令是否存在
func (s *SSHClient) CommandExists(command string) bool {
	_, err := s.ExecuteCommand(fmt.Sprintf("command -v %s", command))
	return err == nil
}

// 检查文件是否存在
func (s *SSHClient) FileExists(path string) bool {
	_, err := s.ExecuteCommand(fmt.Sprintf("test -f %s", path))
	return err == nil
}

// 检查目录是否存在
func (s *SSHClient) DirectoryExists(path string) bool {
	_, err := s.ExecuteCommand(fmt.Sprintf("test -d %s", path))
	return err == nil
}

// 创建目录
func (s *SSHClient) CreateDirectory(path string) error {
	_, err := s.ExecuteCommand(fmt.Sprintf("mkdir -p %s", path))
	return err
}

// 检查服务是否运行
func (s *SSHClient) IsServiceRunning(serviceName string) bool {
	_, err := s.ExecuteCommand(fmt.Sprintf("systemctl is-active %s", serviceName))
	return err == nil
}

// 启动服务
func (s *SSHClient) StartService(serviceName string) error {
	_, err := s.ExecuteCommand(fmt.Sprintf("systemctl start %s", serviceName))
	return err
}

// 停止服务
func (s *SSHClient) StopService(serviceName string) error {
	_, err := s.ExecuteCommand(fmt.Sprintf("systemctl stop %s", serviceName))
	return err
}

// 重启服务
func (s *SSHClient) RestartService(serviceName string) error {
	_, err := s.ExecuteCommand(fmt.Sprintf("systemctl restart %s", serviceName))
	return err
}

// 检查端口是否被占用
func (s *SSHClient) IsPortInUse(port int) bool {
	_, err := s.ExecuteCommand(fmt.Sprintf("netstat -tuln | grep ':%d '", port))
	return err == nil
}

// 关闭SSH连接
func (s *SSHClient) Close() error {
	if s.client != nil {
		err := s.client.Close()
		s.client = nil
		return err
	}
	return nil
}

// 测试本地到远程的网络连接
func (s *SSHClient) TestConnection() error {
	addr := fmt.Sprintf("%s:%d", s.config.TargetDevice.IP, s.config.TargetDevice.SSH.Port)
	conn, err := net.DialTimeout("tcp", addr, 5*time.Second)
	if err != nil {
		return fmt.Errorf("无法连接到 %s: %v", addr, err)
	}
	conn.Close()
	return nil
} 