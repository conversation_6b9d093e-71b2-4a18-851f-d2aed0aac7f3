package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"text/template"
	"time"

	"gopkg.in/yaml.v3"
)

// 配置管理器结构体
type ConfigManager struct {
	config           *Config
	backupPath       string
	originalContent  string
	templateVars     map[string]interface{}
}

// 创建新的配置管理器
func NewConfigManager(config *Config) *ConfigManager {
	return &ConfigManager{
		config:       config,
		templateVars: make(map[string]interface{}),
	}
}

// 更新配置文件
func (c *ConfigManager) UpdateConfigFile() error {
	// 准备模板变量
	c.prepareTemplateVars()

	// 读取原始配置文件
	if err := c.readOriginalConfig(); err != nil {
		return fmt.Errorf("读取原始配置失败: %v", err)
	}

	// 备份原始配置文件
	if err := c.backupOriginalConfig(); err != nil {
		return fmt.Errorf("备份原始配置失败: %v", err)
	}

	// 应用配置替换
	if err := c.applyConfigReplacements(); err != nil {
		return fmt.Errorf("应用配置替换失败: %v", err)
	}

	fmt.Println("✅ 配置文件更新完成")
	return nil
}

// 准备模板变量
func (c *ConfigManager) prepareTemplateVars() {
	// 设置基本变量
	c.templateVars["target_device"] = map[string]interface{}{
		"ip": c.config.TargetDevice.IP,
	}
	
	c.templateVars["local_machine"] = map[string]interface{}{
		"ip": c.config.LocalMachine.IP,
	}

	// 设置NFS挂载点路径
	var nfsMountPath string
	switch runtime.GOOS {
	case "windows":
		nfsMountPath = strings.ReplaceAll(c.config.LocalMachine.NFSMountPoint, "/", "\\")
	default:
		nfsMountPath = c.config.LocalMachine.NFSMountPoint
	}
	c.templateVars["local_nfs_mount"] = nfsMountPath

	fmt.Printf("📋 模板变量准备完成:\n")
	fmt.Printf("  - target_device.ip: %s\n", c.config.TargetDevice.IP)
	fmt.Printf("  - local_machine.ip: %s\n", c.config.LocalMachine.IP)
	fmt.Printf("  - local_nfs_mount: %s\n", nfsMountPath)
}

// 读取原始配置文件
func (c *ConfigManager) readOriginalConfig() error {
	configPath := c.config.ConfigReplacements.FilePath
	
	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取文件内容
	content, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	c.originalContent = string(content)
	fmt.Printf("📖 读取配置文件: %s (%d bytes)\n", configPath, len(content))
	return nil
}

// 备份原始配置文件
func (c *ConfigManager) backupOriginalConfig() error {
	configPath := c.config.ConfigReplacements.FilePath
	
	// 创建备份文件名
	timestamp := time.Now().Format("20060102_150405")
	backupFileName := fmt.Sprintf("%s.backup_%s", filepath.Base(configPath), timestamp)
	backupDir := filepath.Join(filepath.Dir(configPath), "backups")
	
	// 创建备份目录
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	c.backupPath = filepath.Join(backupDir, backupFileName)
	
	// 写入备份文件
	if err := ioutil.WriteFile(c.backupPath, []byte(c.originalContent), 0644); err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	fmt.Printf("📁 配置文件备份: %s\n", c.backupPath)
	return nil
}

// 应用配置替换
func (c *ConfigManager) applyConfigReplacements() error {
	fmt.Println("🔄 应用配置替换...")
	
	modifiedContent := c.originalContent
	
	// 应用每个替换规则
	for i, replacement := range c.config.ConfigReplacements.Replacements {
		fmt.Printf("  %d. %s\n", i+1, replacement.Description)
		
		// 处理模板变量
		processedValue, err := c.processTemplate(replacement.Value)
		if err != nil {
			return fmt.Errorf("处理模板变量失败 %s: %v", replacement.Key, err)
		}

		// 应用替换
		newContent, err := c.replaceConfigValue(modifiedContent, replacement.Key, processedValue)
		if err != nil {
			return fmt.Errorf("替换配置值失败 %s: %v", replacement.Key, err)
		}
		
		modifiedContent = newContent
		fmt.Printf("     %s: %s\n", replacement.Key, processedValue)
	}

	// 写入修改后的配置文件
	configPath := c.config.ConfigReplacements.FilePath
	if err := ioutil.WriteFile(configPath, []byte(modifiedContent), 0644); err != nil {
		return fmt.Errorf("写入修改后的配置文件失败: %v", err)
	}

	fmt.Printf("✅ 配置替换完成，共应用 %d 个替换规则\n", len(c.config.ConfigReplacements.Replacements))
	return nil
}

// 处理模板变量
func (c *ConfigManager) processTemplate(templateStr string) (string, error) {
	// 如果不包含模板语法，直接返回
	if !strings.Contains(templateStr, "{{") {
		return templateStr, nil
	}

	// 创建模板
	tmpl, err := template.New("config").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %v", err)
	}

	// 执行模板
	var result strings.Builder
	if err := tmpl.Execute(&result, c.templateVars); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	return result.String(), nil
}

// 替换配置值
func (c *ConfigManager) replaceConfigValue(content, key, value string) (string, error) {
	// 尝试不同的YAML格式匹配
	patterns := []string{
		// 标准格式: key: value
		fmt.Sprintf(`^(\s*)%s:\s*(.*)$`, regexp.QuoteMeta(key)),
		// 带引号的格式: key: "value"
		fmt.Sprintf(`^(\s*)%s:\s*"[^"]*"(.*)$`, regexp.QuoteMeta(key)),
		// 带单引号的格式: key: 'value'
		fmt.Sprintf(`^(\s*)%s:\s*'[^']*'(.*)$`, regexp.QuoteMeta(key)),
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		if regex.MatchString(content) {
			// 确定值的格式（是否需要引号）
			formattedValue := c.formatValue(value)
			replacement := fmt.Sprintf("${1}%s: %s${2}", key, formattedValue)
			
			result := regex.ReplaceAllStringFunc(content, func(match string) string {
				return regex.ReplaceAllString(match, replacement)
			})
			
			if result != content {
				return result, nil
			}
		}
	}

	// 如果没有找到匹配，尝试多行搜索
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		if strings.HasPrefix(trimmed, key+":") {
			// 提取缩进
			indent := strings.TrimSuffix(line, trimmed)
			formattedValue := c.formatValue(value)
			lines[i] = fmt.Sprintf("%s%s: %s", indent, key, formattedValue)
			return strings.Join(lines, "\n"), nil
		}
	}

	return content, fmt.Errorf("未找到配置键: %s", key)
}

// 格式化值（确定是否需要引号）
func (c *ConfigManager) formatValue(value string) string {
	// 如果值包含特殊字符，加引号
	if strings.Contains(value, " ") || 
	   strings.Contains(value, ":") || 
	   strings.Contains(value, "/") ||
	   strings.Contains(value, "\\") {
		// 转义内部引号
		escaped := strings.ReplaceAll(value, "\"", "\\\"")
		return fmt.Sprintf("\"%s\"", escaped)
	}
	return value
}

// 验证配置文件语法
func (c *ConfigManager) validateConfig() error {
	configPath := c.config.ConfigReplacements.FilePath
	
	// 读取修改后的配置文件
	content, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 尝试解析YAML
	var yamlData interface{}
	if err := yaml.Unmarshal(content, &yamlData); err != nil {
		return fmt.Errorf("YAML语法错误: %v", err)
	}

	fmt.Println("✅ 配置文件语法验证通过")
	return nil
}

// 恢复原始配置
func (c *ConfigManager) RestoreOriginalConfig() error {
	if c.backupPath == "" {
		return fmt.Errorf("没有备份文件可恢复")
	}

	configPath := c.config.ConfigReplacements.FilePath
	
	// 从备份文件恢复
	if err := copyFile(c.backupPath, configPath); err != nil {
		return fmt.Errorf("恢复配置文件失败: %v", err)
	}

	fmt.Printf("✅ 配置文件已恢复: %s\n", configPath)
	return nil
}

// 获取配置差异
func (c *ConfigManager) GetConfigDiff() (string, error) {
	if c.originalContent == "" {
		return "", fmt.Errorf("没有原始配置内容")
	}

	configPath := c.config.ConfigReplacements.FilePath
	currentContent, err := ioutil.ReadFile(configPath)
	if err != nil {
		return "", fmt.Errorf("读取当前配置失败: %v", err)
	}

	// 简单的差异对比
	originalLines := strings.Split(c.originalContent, "\n")
	currentLines := strings.Split(string(currentContent), "\n")

	var diff strings.Builder
	diff.WriteString("配置文件差异:\n")
	diff.WriteString("================\n")

	for i, originalLine := range originalLines {
		if i < len(currentLines) && originalLine != currentLines[i] {
			diff.WriteString(fmt.Sprintf("- %s\n", originalLine))
			diff.WriteString(fmt.Sprintf("+ %s\n", currentLines[i]))
		}
	}

	return diff.String(), nil
}

// 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// 清理（可选择恢复原始配置）
func (c *ConfigManager) Cleanup(restore bool) error {
	if restore {
		return c.RestoreOriginalConfig()
	}
	return nil
} 