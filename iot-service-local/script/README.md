# IoT调试环境自动化设置工具

本工具用于自动化设置IoT设备调试环境，解决在本地开发环境中调试运行在Linux设备上的Spring Boot应用程序的问题。

## 🎯 功能特性

- **跨平台支持**：支持Windows、Linux、macOS
- **NFS自动配置**：自动在录像机上设置NFS导出，并在本地挂载
- **端口转发**：自动设置iptables规则和本地端口转发
- **配置文件自动化**：自动修改application.yml配置文件
- **一键清理**：程序退出时自动清理所有设置

## 🔧 环境要求

### 开发机要求
- Go 1.21+
- NFS客户端支持
- SSH客户端
- 管理员权限（用于挂载NFS和设置端口转发）

### 录像机要求
- Linux系统
- SSH服务器
- root权限
- iptables支持
- NFS服务器支持

## 📦 安装

### 方式1：从源码构建

```bash
# 克隆项目
git clone <项目地址>
cd iot-service-local/script

# 构建
chmod +x build.sh
./build.sh

# 或者直接使用Go构建
go build -o iot-debug-setup .
```

### 方式2：下载预编译版本

从发布页面下载适合您系统的预编译版本。

## ⚙️ 配置

### 1. 复制配置文件模板

```bash
cp debug_config.yml my_config.yml
```

### 2. 修改配置文件

编辑 `my_config.yml` 文件，配置您的环境：

```yaml
# 目标录像机配置
target_device:
  ip: "*************"  # 修改为您的录像机IP
  ssh:
    username: "root"
    password: "your_password"  # 修改为您的SSH密码
    port: 22

# 本地开发机配置
local_machine:
  ip: "**************"  # 修改为您的本地IP
  nfs_mount_point: "/mnt/camera_data"  # Linux/Mac
  # Windows: "Z:\\"
```

### 3. 自定义配置项

根据需要修改以下配置：

- **NFS导出目录**：修改 `nfs_exports.directories`
- **端口转发规则**：修改 `port_forwarding`
- **配置文件替换**：修改 `config_replacements.replacements`

## 🚀 使用方法

### 基本用法

```bash
# 使用默认配置文件
./iot-debug-setup debug_config.yml

# 使用自定义配置文件
./iot-debug-setup my_config.yml
```

### 运行流程

1. **连接录像机**：通过SSH连接到录像机
2. **设置NFS**：在录像机上安装并配置NFS服务器
3. **端口转发**：设置iptables规则和本地端口转发
4. **更新配置**：修改application.yml文件
5. **等待调试**：工具保持运行状态，等待您在IDE中启动调试

### 退出和清理

按 `Ctrl+C` 退出程序，工具会自动：
- 清理端口转发规则
- 卸载NFS挂载
- 恢复原始配置文件
- 关闭SSH连接

## 🛠️ 与IDE集成

### IntelliJ IDEA

1. 打开 `Run/Debug Configurations`
2. 选择您的Spring Boot配置
3. 点击 `Modify options` → `Add before launch task`
4. 添加 `Run External tool`
5. 配置外部工具：
   - **Program**: `iot-debug-setup`的完整路径
   - **Arguments**: 配置文件路径
   - **Working directory**: 项目根目录

### VS Code

在 `launch.json` 中添加预启动任务：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug IoT Service",
      "type": "java",
      "request": "launch",
      "mainClass": "com.addx.IotCameraApplication",
      "preLaunchTask": "setup-debug-env"
    }
  ]
}
```

在 `tasks.json` 中定义任务：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "setup-debug-env",
      "type": "shell",
      "command": "./iot-debug-setup",
      "args": ["debug_config.yml"],
      "options": {
        "cwd": "${workspaceFolder}/iot-service-local/script"
      }
    }
  ]
}
```

## 📋 配置说明

### NFS配置

```yaml
nfs_exports:
  directories:
    - source: "/data/sqlite"           # 录像机上的源目录
      export_path: "/data/sqlite"      # NFS导出路径
      options: "rw,sync,no_subtree_check,no_root_squash"
```

### 端口转发配置

```yaml
port_forwarding:
  expose_ports:
    - port: 6379
      description: "Redis服务"
  forward_to_local:
    - remote_port: 7777
      local_port: 7777
      description: "IoT服务HTTP端口"
```

### 配置文件替换

```yaml
config_replacements:
  replacements:
    - key: "spring.redis.host"
      value: "{{.target_device.ip}}"
      description: "Redis服务器地址"
```

## 🔍 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查录像机IP地址和SSH配置
   - 确认SSH服务正在运行
   - 验证用户名和密码

2. **NFS挂载失败**
   - 检查NFS服务器是否正在运行
   - 验证网络连接
   - 确认本地NFS客户端已安装

3. **端口转发不工作**
   - 检查iptables规则
   - 验证端口是否被占用
   - 确认防火墙设置

4. **配置文件更新失败**
   - 检查配置文件路径
   - 验证YAML语法
   - 确认文件权限

### 调试选项

在配置文件中禁用某些步骤来调试问题：

```yaml
execution_steps:
  setup_nfs: false            # 禁用NFS设置
  setup_port_forwarding: false  # 禁用端口转发
  update_config_file: false   # 禁用配置文件更新
```

## 🔒 安全注意事项

1. **SSH密码**：避免在配置文件中明文存储密码，建议使用SSH密钥认证
2. **NFS权限**：合理设置NFS导出选项，避免过度开放权限
3. **iptables规则**：程序会自动备份和恢复iptables规则
4. **网络安全**：确保在安全的网络环境中使用

## 📝 开发和贡献

### 项目结构

```
iot-service-local/script/
├── main.go              # 主程序
├── ssh_client.go        # SSH客户端
├── nfs_manager.go       # NFS管理器
├── port_manager.go      # 端口管理器
├── config_manager.go    # 配置管理器
├── debug_config.yml     # 配置文件模板
├── build.sh            # 构建脚本
└── README.md           # 使用文档
```

### 构建选项

```bash
# 构建当前平台
./build.sh

# 构建所有平台
./build.sh --all

# 运行测试
./build.sh --test

# 创建发布包
./build.sh --release

# 清理构建文件
./build.sh --clean
```

## 📄 许可证

MIT License

## 🤝 支持

如果您遇到问题或有建议，请：

1. 查看故障排除部分
2. 检查日志输出
3. 提交Issue或PR

## 📚 相关资源

- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [NFS配置指南](https://nfs.sourceforge.net/)
- [iptables使用手册](https://netfilter.org/documentation/) 