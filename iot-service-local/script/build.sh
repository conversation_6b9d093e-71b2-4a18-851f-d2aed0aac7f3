#!/bin/bash

# IoT调试环境设置工具构建脚本
# 支持跨平台编译 (Windows, Linux, macOS)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_environment() {
    print_info "检查Go环境..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go未安装，请先安装Go语言环境"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    print_success "Go版本: $GO_VERSION"
}

# 初始化Go模块
init_go_module() {
    print_info "初始化Go模块..."
    
    if [ ! -f "go.mod" ]; then
        print_error "go.mod文件不存在"
        exit 1
    fi
    
    # 下载依赖
    print_info "下载依赖..."
    go mod download
    go mod tidy
    
    print_success "Go模块初始化完成"
}

# 构建函数
build_for_platform() {
    local GOOS=$1
    local GOARCH=$2
    local EXT=$3
    
    OUTPUT_NAME="iot-debug-setup-${GOOS}-${GOARCH}${EXT}"
    
    print_info "构建 ${GOOS}/${GOARCH}..."
    
    GOOS=$GOOS GOARCH=$GOARCH go build -o "dist/${OUTPUT_NAME}" -ldflags="-s -w" .
    
    if [ $? -eq 0 ]; then
        print_success "构建成功: dist/${OUTPUT_NAME}"
        
        # 获取文件大小
        if [ "$GOOS" = "windows" ]; then
            SIZE=$(stat -c%s "dist/${OUTPUT_NAME}" 2>/dev/null || echo "Unknown")
        else
            SIZE=$(stat -f%z "dist/${OUTPUT_NAME}" 2>/dev/null || stat -c%s "dist/${OUTPUT_NAME}" 2>/dev/null || echo "Unknown")
        fi
        
        echo "  文件大小: $SIZE bytes"
    else
        print_error "构建失败: ${GOOS}/${GOARCH}"
        return 1
    fi
}

# 创建输出目录
create_output_dir() {
    print_info "创建输出目录..."
    
    if [ -d "dist" ]; then
        rm -rf dist
    fi
    
    mkdir -p dist
    print_success "输出目录创建完成"
}

# 构建所有平台
build_all_platforms() {
    print_info "开始构建所有平台..."
    
    # Windows 64位
    build_for_platform "windows" "amd64" ".exe"
    
    # Windows 32位
    build_for_platform "windows" "386" ".exe"
    
    # Linux 64位
    build_for_platform "linux" "amd64" ""
    
    # Linux 32位
    build_for_platform "linux" "386" ""
    
    # Linux ARM64
    build_for_platform "linux" "arm64" ""
    
    # macOS 64位 (Intel)
    build_for_platform "darwin" "amd64" ""
    
    # macOS ARM64 (Apple Silicon)
    build_for_platform "darwin" "arm64" ""
    
    print_success "所有平台构建完成"
}

# 构建当前平台
build_current_platform() {
    print_info "构建当前平台..."
    
    CURRENT_OS=$(go env GOOS)
    CURRENT_ARCH=$(go env GOARCH)
    
    EXT=""
    if [ "$CURRENT_OS" = "windows" ]; then
        EXT=".exe"
    fi
    
    build_for_platform "$CURRENT_OS" "$CURRENT_ARCH" "$EXT"
    
    # 创建符号链接或复制文件（便于使用）
    OUTPUT_NAME="iot-debug-setup-${CURRENT_OS}-${CURRENT_ARCH}${EXT}"
    SIMPLE_NAME="iot-debug-setup${EXT}"
    
    cp "dist/${OUTPUT_NAME}" "dist/${SIMPLE_NAME}"
    print_success "创建便捷可执行文件: dist/${SIMPLE_NAME}"
}

# 运行测试
run_tests() {
    print_info "运行测试..."
    
    if go test -v ./...; then
        print_success "所有测试通过"
    else
        print_error "测试失败"
        return 1
    fi
}

# 生成校验和
generate_checksums() {
    print_info "生成校验和..."
    
    cd dist
    
    # 生成SHA256校验和
    if command -v sha256sum &> /dev/null; then
        sha256sum * > checksums.sha256
    elif command -v shasum &> /dev/null; then
        shasum -a 256 * > checksums.sha256
    else
        print_warning "无法生成校验和，sha256sum或shasum未找到"
        cd ..
        return 1
    fi
    
    print_success "校验和生成完成: dist/checksums.sha256"
    cd ..
}

# 创建发布包
create_release_package() {
    print_info "创建发布包..."
    
    RELEASE_DIR="iot-debug-setup-release"
    
    if [ -d "$RELEASE_DIR" ]; then
        rm -rf "$RELEASE_DIR"
    fi
    
    mkdir -p "$RELEASE_DIR"
    
    # 复制可执行文件
    cp dist/* "$RELEASE_DIR/"
    
    # 复制配置文件和文档
    cp debug_config.yml "$RELEASE_DIR/debug_config.example.yml"
    cp README.md "$RELEASE_DIR/" 2>/dev/null || echo "README.md not found"
    
    # 创建压缩包
    if command -v tar &> /dev/null; then
        tar -czf "${RELEASE_DIR}.tar.gz" "$RELEASE_DIR"
        print_success "发布包创建完成: ${RELEASE_DIR}.tar.gz"
    else
        print_warning "tar命令未找到，无法创建压缩包"
    fi
}

# 显示帮助信息
show_help() {
    echo "IoT调试环境设置工具构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -a, --all       构建所有平台"
    echo "  -c, --current   构建当前平台 (默认)"
    echo "  -t, --test      运行测试"
    echo "  -r, --release   创建发布包"
    echo "  --clean         清理构建文件"
    echo ""
    echo "示例:"
    echo "  $0                # 构建当前平台"
    echo "  $0 -a             # 构建所有平台"
    echo "  $0 -t             # 运行测试"
    echo "  $0 -r             # 创建发布包"
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."
    
    rm -rf dist/
    rm -rf iot-debug-setup-release/
    rm -f iot-debug-setup-release.tar.gz
    
    print_success "清理完成"
}

# 主函数
main() {
    local BUILD_ALL=false
    local BUILD_CURRENT=true
    local RUN_TESTS=false
    local CREATE_RELEASE=false
    local CLEAN=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                BUILD_ALL=true
                BUILD_CURRENT=false
                shift
                ;;
            -c|--current)
                BUILD_CURRENT=true
                BUILD_ALL=false
                shift
                ;;
            -t|--test)
                RUN_TESTS=true
                shift
                ;;
            -r|--release)
                CREATE_RELEASE=true
                shift
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行清理
    if [ "$CLEAN" = true ]; then
        clean_build
        exit 0
    fi
    
    print_info "开始构建IoT调试环境设置工具..."
    
    # 检查环境
    check_go_environment
    init_go_module
    
    # 运行测试
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi
    
    # 创建输出目录
    create_output_dir
    
    # 构建
    if [ "$BUILD_ALL" = true ]; then
        build_all_platforms
    elif [ "$BUILD_CURRENT" = true ]; then
        build_current_platform
    fi
    
    # 生成校验和
    generate_checksums
    
    # 创建发布包
    if [ "$CREATE_RELEASE" = true ]; then
        create_release_package
    fi
    
    print_success "构建完成!"
    print_info "可执行文件位于 dist/ 目录中"
    
    # 显示构建结果
    if [ -d "dist" ]; then
        print_info "构建结果:"
        ls -la dist/
    fi
}

# 运行主函数
main "$@" 