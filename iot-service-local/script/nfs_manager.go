package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
)

// NFS管理器结构体
type NFSManager struct {
	config    *Config
	sshClient *SSHClient
	mounted   []string // 记录已挂载的路径，用于清理
}

// 创建新的NFS管理器
func NewNFSManager(config *Config) *NFSManager {
	return &NFSManager{
		config:  config,
		mounted: make([]string, 0),
	}
}

// 设置SSH客户端
func (n *NFSManager) SetSSHClient(client *SSHClient) {
	n.sshClient = client
}

// 设置NFS
func (n *NFSManager) SetupNFS() error {
	// 确保SSH客户端已连接
	if n.sshClient == nil {
		return fmt.Errorf("SSH客户端未设置")
	}

	// 检查并安装NFS服务
	if err := n.ensureNFSServerInstalled(); err != nil {
		return fmt.Errorf("安装NFS服务失败: %v", err)
	}

	// 配置NFS导出
	if err := n.configureNFSExports(); err != nil {
		return fmt.Errorf("配置NFS导出失败: %v", err)
	}

	// 启动NFS服务
	if err := n.startNFSService(); err != nil {
		return fmt.Errorf("启动NFS服务失败: %v", err)
	}

	// 在本地挂载NFS
	if err := n.mountNFSOnLocal(); err != nil {
		return fmt.Errorf("挂载NFS失败: %v", err)
	}

	fmt.Println("✅ NFS设置完成")
	return nil
}

// 确保NFS服务器已安装
func (n *NFSManager) ensureNFSServerInstalled() error {
	fmt.Println("🔍 检查NFS服务器...")

	// 检查是否已安装NFS服务器
	if n.sshClient.CommandExists("exportfs") {
		fmt.Println("✅ NFS服务器已安装")
		return nil
	}

	fmt.Println("📦 安装NFS服务器...")

	// 根据不同的发行版安装NFS服务器
	commands := []string{
		"apt-get update && apt-get install -y nfs-kernel-server",          // Ubuntu/Debian
		"yum install -y nfs-utils",                                        // CentOS/RHEL
		"dnf install -y nfs-utils",                                        // Fedora
		"apk add nfs-utils",                                               // Alpine
	}

	var lastErr error
	for _, cmd := range commands {
		if _, err := n.sshClient.ExecuteCommand(cmd); err == nil {
			fmt.Println("✅ NFS服务器安装成功")
			return nil
		} else {
			lastErr = err
		}
	}

	return fmt.Errorf("无法安装NFS服务器: %v", lastErr)
}

// 配置NFS导出
func (n *NFSManager) configureNFSExports() error {
	fmt.Println("📝 配置NFS导出...")

	// 创建导出配置
	var exportLines []string
	for _, dir := range n.config.NFSExports.Directories {
		// 确保目录存在
		if !n.sshClient.DirectoryExists(dir.Source) {
			fmt.Printf("📁 创建目录: %s\n", dir.Source)
			if err := n.sshClient.CreateDirectory(dir.Source); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", dir.Source, err)
			}
		}

		// 添加导出行
		exportLine := fmt.Sprintf("%s %s(%s)",
			dir.Source,
			n.config.LocalMachine.IP,
			dir.Options)
		exportLines = append(exportLines, exportLine)
	}

	// 备份原有的exports文件
	_, _ = n.sshClient.ExecuteCommand("cp /etc/exports /etc/exports.bak.$(date +%Y%m%d_%H%M%S)")

	// 写入新的exports配置
	exportsContent := strings.Join(exportLines, "\n")
	writeCmd := fmt.Sprintf("echo '%s' > /etc/exports", exportsContent)
	if _, err := n.sshClient.ExecuteCommand(writeCmd); err != nil {
		return fmt.Errorf("写入exports配置失败: %v", err)
	}

	fmt.Println("✅ NFS导出配置完成")
	return nil
}

// 启动NFS服务
func (n *NFSManager) startNFSService() error {
	fmt.Println("🚀 启动NFS服务...")

	// 启动相关服务
	services := []string{
		"rpcbind",
		"nfs-server",
		"nfs-kernel-server",
	}

	for _, service := range services {
		if _, err := n.sshClient.ExecuteCommand(fmt.Sprintf("systemctl start %s", service)); err != nil {
			fmt.Printf("⚠️  启动服务失败 %s: %v\n", service, err)
		}
	}

	// 重新加载exports
	if _, err := n.sshClient.ExecuteCommand("exportfs -ra"); err != nil {
		return fmt.Errorf("重新加载exports失败: %v", err)
	}

	// 验证导出状态
	output, err := n.sshClient.ExecuteCommand("exportfs -v")
	if err != nil {
		return fmt.Errorf("验证导出状态失败: %v", err)
	}

	fmt.Printf("📋 当前NFS导出状态:\n%s\n", output)
	fmt.Println("✅ NFS服务启动成功")
	return nil
}

// 在本地挂载NFS
func (n *NFSManager) mountNFSOnLocal() error {
	fmt.Println("🔗 在本地挂载NFS...")

	// 创建挂载点
	mountPoint := n.config.LocalMachine.NFSMountPoint
	if err := n.createMountPoint(mountPoint); err != nil {
		return fmt.Errorf("创建挂载点失败: %v", err)
	}

	// 检查本地NFS客户端
	if err := n.ensureNFSClientInstalled(); err != nil {
		return fmt.Errorf("安装NFS客户端失败: %v", err)
	}

	// 挂载各个目录
	for _, dir := range n.config.NFSExports.Directories {
		localPath := filepath.Join(mountPoint, strings.TrimPrefix(dir.ExportPath, "/"))
		
		// 创建本地目录
		if err := n.createMountPoint(localPath); err != nil {
			return fmt.Errorf("创建本地目录失败 %s: %v", localPath, err)
		}

		// 挂载NFS
		if err := n.mountSingleNFS(dir.ExportPath, localPath); err != nil {
			return fmt.Errorf("挂载NFS失败 %s: %v", localPath, err)
		}

		n.mounted = append(n.mounted, localPath)
		fmt.Printf("✅ 挂载成功: %s -> %s\n", dir.ExportPath, localPath)
	}

	return nil
}

// 创建挂载点
func (n *NFSManager) createMountPoint(path string) error {
	if err := os.MkdirAll(path, 0755); err != nil {
		return fmt.Errorf("创建目录失败 %s: %v", path, err)
	}
	return nil
}

// 确保NFS客户端已安装
func (n *NFSManager) ensureNFSClientInstalled() error {
	var installCmd string
	
	switch runtime.GOOS {
	case "linux":
		// 检查是否已安装
		if _, err := exec.LookPath("mount.nfs"); err == nil {
			return nil
		}
		
		// 尝试安装
		installCmd = "sudo apt-get install -y nfs-common || sudo yum install -y nfs-utils || sudo dnf install -y nfs-utils"
	case "darwin":
		// macOS通常已经内置NFS支持
		return nil
	case "windows":
		// Windows需要启用NFS客户端功能
		fmt.Println("⚠️  Windows需要手动启用NFS客户端功能")
		return nil
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	if installCmd != "" {
		fmt.Println("📦 安装NFS客户端...")
		if err := n.executeLocalCommand(installCmd); err != nil {
			return fmt.Errorf("安装NFS客户端失败: %v", err)
		}
	}

	return nil
}

// 挂载单个NFS
func (n *NFSManager) mountSingleNFS(remotePath, localPath string) error {
	// 首先检查是否已经挂载
	if n.isMounted(localPath) {
		fmt.Printf("📌 %s 已经挂载\n", localPath)
		return nil
	}

	// 构建挂载命令
	var mountCmd string
	nfsServer := fmt.Sprintf("%s:%s", n.config.TargetDevice.IP, remotePath)
	
	switch runtime.GOOS {
	case "linux":
		mountCmd = fmt.Sprintf("sudo mount -t nfs %s %s", nfsServer, localPath)
	case "darwin":
		mountCmd = fmt.Sprintf("sudo mount -t nfs %s %s", nfsServer, localPath)
	case "windows":
		// Windows NFS挂载命令
		mountCmd = fmt.Sprintf("mount -t nfs %s %s", nfsServer, localPath)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	// 执行挂载命令
	if err := n.executeLocalCommand(mountCmd); err != nil {
		return fmt.Errorf("挂载命令执行失败: %v", err)
	}

	return nil
}

// 检查是否已挂载
func (n *NFSManager) isMounted(path string) bool {
	var checkCmd string
	
	switch runtime.GOOS {
	case "linux", "darwin":
		checkCmd = fmt.Sprintf("mount | grep %s", path)
	case "windows":
		checkCmd = fmt.Sprintf("mount | findstr %s", path)
	default:
		return false
	}

	err := n.executeLocalCommand(checkCmd)
	return err == nil
}

// 执行本地命令
func (n *NFSManager) executeLocalCommand(command string) error {
	fmt.Printf("🔧 执行本地命令: %s\n", command)
	
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", command)
	} else {
		cmd = exec.Command("sh", "-c", command)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("命令执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

// 清理NFS挂载
func (n *NFSManager) Cleanup() error {
	fmt.Println("🧹 清理NFS挂载...")

	// 卸载所有挂载的NFS
	for _, mountPath := range n.mounted {
		if err := n.unmountNFS(mountPath); err != nil {
			fmt.Printf("⚠️  卸载失败 %s: %v\n", mountPath, err)
		} else {
			fmt.Printf("✅ 卸载成功: %s\n", mountPath)
		}
	}

	// 清理挂载记录
	n.mounted = n.mounted[:0]

	return nil
}

// 卸载NFS
func (n *NFSManager) unmountNFS(path string) error {
	var unmountCmd string
	
	switch runtime.GOOS {
	case "linux", "darwin":
		unmountCmd = fmt.Sprintf("sudo umount %s", path)
	case "windows":
		unmountCmd = fmt.Sprintf("umount %s", path)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	return n.executeLocalCommand(unmountCmd)
} 