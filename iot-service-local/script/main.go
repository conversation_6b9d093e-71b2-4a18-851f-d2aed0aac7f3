package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"

	"gopkg.in/yaml.v3"
)

// 配置结构体定义
type Config struct {
	TargetDevice struct {
		IP  string `yaml:"ip"`
		SSH struct {
			Username string `yaml:"username"`
			Password string `yaml:"password"`
			Port     int    `yaml:"port"`
			KeyFile  string `yaml:"key_file"`
		} `yaml:"ssh"`
	} `yaml:"target_device"`

	LocalMachine struct {
		IP             string `yaml:"ip"`
		NFSMountPoint  string `yaml:"nfs_mount_point"`
	} `yaml:"local_machine"`

	NFSExports struct {
		Directories []struct {
			Source      string `yaml:"source"`
			ExportPath  string `yaml:"export_path"`
			Options     string `yaml:"options"`
		} `yaml:"directories"`
	} `yaml:"nfs_exports"`

	PortForwarding struct {
		ExposePorts []struct {
			Port        int    `yaml:"port"`
			Description string `yaml:"description"`
		} `yaml:"expose_ports"`
		ForwardToLocal []struct {
			RemotePort  int    `yaml:"remote_port"`
			LocalPort   int    `yaml:"local_port"`
			Description string `yaml:"description"`
		} `yaml:"forward_to_local"`
	} `yaml:"port_forwarding"`

	ConfigReplacements struct {
		FilePath     string `yaml:"file_path"`
		Replacements []struct {
			Key         string `yaml:"key"`
			Value       string `yaml:"value"`
			Description string `yaml:"description"`
		} `yaml:"replacements"`
	} `yaml:"config_replacements"`

	PlatformConfig struct {
		Windows struct {
			NFSMountCommand   string `yaml:"nfs_mount_command"`
			NFSUnmountCommand string `yaml:"nfs_unmount_command"`
		} `yaml:"windows"`
		Linux struct {
			NFSMountCommand   string `yaml:"nfs_mount_command"`
			NFSUnmountCommand string `yaml:"nfs_unmount_command"`
		} `yaml:"linux"`
		Darwin struct {
			NFSMountCommand   string `yaml:"nfs_mount_command"`
			NFSUnmountCommand string `yaml:"nfs_unmount_command"`
		} `yaml:"darwin"`
	} `yaml:"platform_config"`

	ExecutionSteps struct {
		SetupNFS            bool `yaml:"setup_nfs"`
		SetupPortForwarding bool `yaml:"setup_port_forwarding"`
		UpdateConfigFile    bool `yaml:"update_config_file"`
		CleanupOnExit       bool `yaml:"cleanup_on_exit"`
	} `yaml:"execution_steps"`
}

// 主应用程序结构体
type DebugSetupApp struct {
	config     *Config
	sshClient  *SSHClient
	nfsManager *NFSManager
	portManager *PortManager
	configManager *ConfigManager
}

// 创建新的应用程序实例
func NewDebugSetupApp(configPath string) (*DebugSetupApp, error) {
	app := &DebugSetupApp{}
	
	// 加载配置文件
	if err := app.loadConfig(configPath); err != nil {
		return nil, fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 初始化各个管理器
	app.sshClient = NewSSHClient(app.config)
	app.nfsManager = NewNFSManager(app.config)
	app.portManager = NewPortManager(app.config)
	app.configManager = NewConfigManager(app.config)

	return app, nil
}

// 加载配置文件
func (app *DebugSetupApp) loadConfig(configPath string) error {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	app.config = &Config{}
	if err := yaml.Unmarshal(data, app.config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	return nil
}

// 运行主程序
func (app *DebugSetupApp) Run() error {
	fmt.Println("🚀 开始设置IoT调试环境...")
	fmt.Printf("目标设备: %s\n", app.config.TargetDevice.IP)
	fmt.Printf("本地机器: %s\n", app.config.LocalMachine.IP)
	fmt.Printf("操作系统: %s\n", runtime.GOOS)

	// 设置信号处理，用于优雅退出
	if app.config.ExecutionSteps.CleanupOnExit {
		app.setupSignalHandling()
	}

	// 连接SSH
	fmt.Println("\n📡 连接到录像机...")
	if err := app.sshClient.Connect(); err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer app.sshClient.Close()

	// 设置NFS
	if app.config.ExecutionSteps.SetupNFS {
		fmt.Println("\n📁 设置NFS导出...")
		app.nfsManager.SetSSHClient(app.sshClient)
		if err := app.nfsManager.SetupNFS(); err != nil {
			return fmt.Errorf("NFS设置失败: %v", err)
		}
	}

	// 设置端口转发
	if app.config.ExecutionSteps.SetupPortForwarding {
		fmt.Println("\n🔗 设置端口转发...")
		app.portManager.SetSSHClient(app.sshClient)
		if err := app.portManager.SetupPortForwarding(); err != nil {
			return fmt.Errorf("端口转发设置失败: %v", err)
		}
	}

	// 更新配置文件
	if app.config.ExecutionSteps.UpdateConfigFile {
		fmt.Println("\n⚙️  更新配置文件...")
		if err := app.configManager.UpdateConfigFile(); err != nil {
			return fmt.Errorf("配置文件更新失败: %v", err)
		}
	}

	fmt.Println("\n✅ 调试环境设置完成！")
	fmt.Println("现在可以在IDE中启动调试模式。")
	fmt.Println("按 Ctrl+C 退出并清理环境。")

	// 等待用户中断
	select {}
}

// 设置信号处理
func (app *DebugSetupApp) setupSignalHandling() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\n🧹 收到退出信号，开始清理...")
		app.cleanup()
		os.Exit(0)
	}()
}

// 清理资源
func (app *DebugSetupApp) cleanup() {
	fmt.Println("清理端口转发...")
	if app.portManager != nil {
		app.portManager.Cleanup()
	}

	fmt.Println("清理NFS...")
	if app.nfsManager != nil {
		app.nfsManager.Cleanup()
	}

	fmt.Println("恢复配置文件...")
	if app.configManager != nil {
		app.configManager.Cleanup(true) // true表示恢复原始配置
	}

	fmt.Println("关闭SSH连接...")
	if app.sshClient != nil {
		app.sshClient.Close()
	}

	fmt.Println("✅ 清理完成")
}

// 主函数
func main() {
	// 检查命令行参数
	if len(os.Args) < 2 {
		fmt.Println("使用方法: ./iot-debug-setup <配置文件路径>")
		fmt.Println("示例: ./iot-debug-setup debug_config.yml")
		os.Exit(1)
	}

	configPath := os.Args[1]
	
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("配置文件不存在: %s\n", configPath)
		os.Exit(1)
	}

	// 获取配置文件的绝对路径
	absConfigPath, err := filepath.Abs(configPath)
	if err != nil {
		log.Fatalf("获取配置文件绝对路径失败: %v", err)
	}

	// 创建应用程序实例
	app, err := NewDebugSetupApp(absConfigPath)
	if err != nil {
		log.Fatalf("创建应用程序实例失败: %v", err)
	}

	// 运行应用程序
	if err := app.Run(); err != nil {
		log.Fatalf("运行应用程序失败: %v", err)
	}
} 