#!/bin/bash

# 停止docker容器
CONTAINER_NAME="iot-service"

if docker ps --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo "[recover]: Container $CONTAINER_NAME is running, stopping it..."
    docker stop $CONTAINER_NAME
else
    echo "[recover]: Container $CONTAINER_NAME is not running, no action taken"
fi


FILE_PATH=/tmp/iot-local
CAMERA_DB_FILE=/data/sqlite/camera.db # camera.db文件路径
LIBRARY_DB_FILE=/userdata/sqlite/library.db # library.db文件路径


fix_sqlite() {
  local CAMERA_DB_FILE="$1"
  echo "[recover]: fix $CAMERA_DB_FILE "
  lines=`python3 ${FILE_PATH}/sqlite/sqlite3_cli_for_shell.py $CAMERA_DB_FILE "PRAGMA integrity_check;" |grep index|awk -F ' index ' '{print $2}'|tr -d ' '|sort|uniq`
  echo $lines
  for line in $lines; do
    tbl_name=`python3 ${FILE_PATH}/sqlite/sqlite3_cli_for_shell.py $CAMERA_DB_FILE " SELECT tbl_name FROM sqlite_master WHERE sql LIKE '% $line %';"`
           echo $tbl_name
    echo "[recover]: fix index $line in $tbl_name\n"
    python3 ${FILE_PATH}/sqlite/sqlite3_cli_for_shell.py $CAMERA_DB_FILE  "REINDEX $tbl_name"
  done
  lines=`python3 ${FILE_PATH}/sqlite/sqlite3_cli_for_shell.py $CAMERA_DB_FILE "PRAGMA integrity_check;" |grep index|awk -F ' index ' '{print $2}'|tr -d ' '|sort|uniq`
  echo "[recover]: after fix\n"
  echo $lines

  python3 ${FILE_PATH}/sqlite/sqlite3_cli_for_shell.py $CAMERA_DB_FILE  "VACUUM;"
}

fix_sqlite $CAMERA_DB_FILE
fix_sqlite $LIBRARY_DB_FILE

# 启动iot-local
sh /tmp/iot-local/run.sh
