#!/bin/sh

FILE_PATH=/tmp/iot-local
YML_FILE="bx-compose.yml"
LOAD_FILE="iot-local.tar"
IMAGE_NAME="iot-service"
CAMERA_DB_FILE=/data/sqlite/camera.db # camera.db文件路径
LIBRARY_DB_FILE=/userdata/sqlite/library.db # library.db文件路径

DEST_DB_VERSION=56 # 升级sqlite表结构的目标版本号
DEST_LIBRARY_DB_VERSION=57 # 升级sqlite library.db表结构的目标版本号

sqlite_upload() {
  # iot-local项目下src/main/resource/sqlite目录会被复制到/tmp/iot-local/sqlite
  sql_dir="${FILE_PATH}/sqlite/sqls" # sql目录

  python3 ${FILE_PATH}/sqlite/upgrade.py -s ${sql_dir} -c ${CAMERA_DB_FILE} -d ${DEST_DB_VERSION};
  result=$?
  if [ $result -ne 0 ]; then
      echo "sqlite升级失败，脚本将退出"
      exit $result
  fi
}

sqlite_library_db_load() {
  python3 ${FILE_PATH}/sqlite/create_library_db.py -c ${CAMERA_DB_FILE} -l ${LIBRARY_DB_FILE};
  result=$?
  if [ $result -ne 0 ]; then
      echo "sqlite[library.db]创建失败，脚本将退出"
      exit $result
  fi
  # iot-local项目下src/main/resource/sqlite目录会被复制到/tmp/iot-local/sqlite
  sql_dir="${FILE_PATH}/sqlite/library_db_sqls" # sql目录
  python3 ${FILE_PATH}/sqlite/upgrade.py -s ${sql_dir} -c ${LIBRARY_DB_FILE} -d ${DEST_LIBRARY_DB_VERSION};
  result=$?
  if [ $result -ne 0 ]; then
      echo "sqlite[library.db]升级失败，脚本将退出"
      exit $result
  fi
}

docker_load() {
  docker load -i ${LOAD_FILE}
}

docker_run() {
  docker compose -f ${YML_FILE} up -d
  docker system prune -f
}

do_main() {
  cd ${FILE_PATH}

  if [ -e ${YML_FILE} ] && [ -e ${LOAD_FILE} ]; then
    sqlite_upload
    sqlite_library_db_load
    docker_load
    docker_run
  fi

  cd -
}

do_main
